{"recommendations": ["ms-dotnettools.csharp", "ms-dotnettools.csdevkit", "ms-dotnettools.vscode-dotnet-runtime", "ms-dotnettools.dotnet-interactive-vscode", "ms-vscode.vscode-json", "ms-vscode.powershell", "formulahendry.code-runner", "jchannon.csharpextensions", "k--kato.docomment", "adrianwilczynski.namespace", "patcx.vscode-nuget-gallery", "alefragnani.bookmarks", "ms-vscode.hexeditor", "ionide.ionide-fsharp", "ms-vscode.test-adapter-converter", "ms-vscode.vscode-eslint", "streetsidesoftware.code-spell-checker", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "ms-dotnettools.bla<PERSON><PERSON>m-companion", "ms-vscode.vscode-json", "redhat.vscode-xml", "ms-vscode.vscode-yaml", "ms-vscode.remote-repositories", "github.copilot", "github.copilot-chat"]}