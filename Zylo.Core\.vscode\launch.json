{"version": "0.2.0", "configurations": [{"name": "⚡ 快速调试 (当前文件)", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["run", "--project", "${fileDirname}"], "cwd": "${fileDirname}", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false, "preLaunchTask": "🔨 构建当前项目"}, {"name": "🚀 调试 Zylo.YIo", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/Zylo.YIo/bin/Debug/net8.0/Zylo.YIo.dll", "args": [], "cwd": "${workspaceFolder}/Zylo.YIo", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false}, {"name": "🔧 调试 Zylo.Core", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/Zylo.Core/bin/Debug/net8.0/Zylo.Core.dll", "args": [], "cwd": "${workspaceFolder}/Zylo.Core", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false}, {"name": "⚙️ 调试 Zylo.AutoRegister", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/Zylo.AutoRegister/bin/Debug/net8.0/Zylo.AutoRegister.dll", "args": [], "cwd": "${workspaceFolder}/Zylo.AutoRegister", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false}, {"name": "🧪 调试测试项目", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["test", "--logger", "console;verbosity=detailed"], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}, {"name": "🔍 附加到进程", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}, {"name": "🧪 快速测试 (当前文件)", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["test", "${fileDirname}", "--logger", "console;verbosity=detailed"], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false}, {"name": "🏃 快速运行 (无调试)", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["run", "--project", "${fileDirname}"], "cwd": "${fileDirname}", "console": "externalTerminal", "stopAtEntry": false, "requireExactSource": false, "justMyCode": false}, {"name": "🔥 热重载调试", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["watch", "run", "--project", "${fileDirname}"], "cwd": "${fileDirname}", "console": "internalConsole", "stopAtEntry": false, "requireExactSource": false, "env": {"DOTNET_WATCH_RESTART_ON_RUDE_EDIT": "true"}}]}