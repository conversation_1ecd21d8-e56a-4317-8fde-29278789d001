{
    "dotnet.defaultSolution": "Zylo.Core.sln",
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    "dotnet.codeLens.enableReferencesCodeLens": true,
    "dotnet.projects.enableAutomaticRestore": true,
    "dotnet.server.useOmnisharp": false,
    "omnisharp.enableEditorConfigSupport": true,
    "omnisharp.enableRoslynAnalyzers": true,
    "omnisharp.analyzeOpenDocumentsOnly": false,
    "omnisharp.enableAsyncCompletion": true,
    "omnisharp.enableDecompilationSupport": true,
    "omnisharp.enableMsBuildLoadProjectsOnDemand": false,
    "csharp.semanticHighlighting.enabled": true,
    "csharp.format.enable": true,
    "csharp.inlayHints.enableInlayHintsForParameters": true,
    "csharp.inlayHints.enableInlayHintsForLiteralParameters": true,
    "csharp.inlayHints.enableInlayHintsForIndexerParameters": true,
    "csharp.inlayHints.enableInlayHintsForObjectCreationParameters": true,
    "csharp.inlayHints.enableInlayHintsForOtherParameters": true,
    "csharp.inlayHints.enableInlayHintsForTypes": true,
    "csharp.inlayHints.enableInlayHintsForImplicitVariableTypes": true,
    "csharp.inlayHints.enableInlayHintsForLambdaParameterTypes": true,
    "csharp.inlayHints.enableInlayHintsForImplicitObjectCreation": true,
    // ========== 超强重构和重命名设置 ==========
    "editor.rename.enablePreview": true,
    "editor.linkedEditing": true,
    "editor.suggest.insertMode": "replace",
    "editor.suggest.filterGraceful": true,
    "editor.suggest.localityBonus": true,
    "editor.suggest.shareSuggestSelections": true,
    "editor.codeActionsOnSaveTimeout": 3000,
    "editor.quickSuggestions": {
        "other": "on",
        "comments": "on",
        "strings": "on"
    },
    "editor.quickSuggestionsDelay": 10,
    "editor.parameterHints.cycle": true,
    "editor.hover.enabled": true,
    "editor.hover.delay": 300,
    "editor.hover.sticky": true,
    // ========== 右键菜单增强 ==========
    "editor.lightbulb.enabled": "onCode",
    "editor.codeActionWidget.includeNearbyQuickFixes": true,
    "workbench.editor.enablePreview": false,
    "workbench.editor.enablePreviewFromQuickOpen": false,
    // ========== 符号搜索和导航 ==========
    "workbench.editor.revealIfOpen": true,
    "editor.gotoLocation.multipleReferences": "peek",
    "editor.gotoLocation.multipleDefinitions": "peek",
    "editor.gotoLocation.multipleDeclarations": "peek",
    "editor.gotoLocation.multipleImplementations": "peek",
    "editor.gotoLocation.multipleTypeDefinitions": "peek",
    "debug.allowBreakpointsEverywhere": true,
    "debug.showBreakpointsInOverviewRuler": true,
    "debug.showInlineBreakpointCandidates": true,
    "debug.toolBarLocation": "floating",
    "debug.console.fontSize": 14,
    "debug.console.lineHeight": 20,
    "debug.openDebug": "openOnDebugBreak",
    "debug.focusWindowOnBreak": true,
    "debug.showSubSessionsInToolBar": true,
    "code-runner.enableAppInsights": false,
    "code-runner.runInTerminal": true,
    "code-runner.saveFileBeforeRun": true,
    "code-runner.clearPreviousOutput": true,
    "code-runner.showExecutionMessage": true,
    "code-runner.executorMap": {
        "csharp": "cd $dir && dotnet run"
    },
    "files.exclude": {
        "**/bin": true,
        "**/obj": false,
        "**/.vs": true
    },
    "search.exclude": {
        "**/bin": true,
        "**/obj": true,
        "**/.vs": true,
        "**/node_modules": true
    },
    "files.watcherExclude": {
        "**/bin/**": true,
        "**/obj/**": true,
        "**/.vs/**": true
    },
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit"
    },
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "*.csproj": "*.user",
        "*.cs": "$(capture).*.cs"
    }
}