﻿{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "馃敤 Build Current Project",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "${fileDirname}",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "group": "build",
            "presentation": {
                "reveal": "silent",
                "panel": "shared",
                "showReuseMessage": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "馃敤 Build Solution",
            "command": "dotnet",
            "type": "process",
            "args": [
                "build",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "silent",
                "panel": "shared",
                "showReuseMessage": false
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "馃Ч Clean and Rebuild",
            "command": "dotnet",
            "type": "process",
            "args": [
                "clean"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": "$msCompile",
            "dependsOrder": "sequence",
            "dependsOn": ["馃敤 Build Solution"]
        },
        {
            "label": "馃殌 Run Current Project",
            "command": "dotnet",
            "type": "process",
            "args": [
                "run",
                "--project",
                "${fileDirname}"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "馃И Run Tests",
            "command": "dotnet",
            "type": "process",
            "args": [
                "test",
                "--logger",
                "console;verbosity=detailed"
            ],
            "group": "test",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "馃摝 Pack NuGet",
            "command": "dotnet",
            "type": "process",
            "args": [
                "pack",
                "--configuration",
                "Release",
                "--output",
                "${workspaceFolder}/nupkgs"
            ],
            "group": "build",
            "presentation": {
                "reveal": "always",
                "panel": "new"
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "馃攳 Open Generated Files",
            "command": "explorer",
            "type": "process",
            "args": [
                "${fileDirname}/obj/Generated"
            ],
            "group": "build",
            "presentation": {
                "reveal": "never"
            },
            "windows": {
                "command": "explorer"
            },
            "linux": {
                "command": "xdg-open"
            },
            "osx": {
                "command": "open"
            }
        }
    ]
}