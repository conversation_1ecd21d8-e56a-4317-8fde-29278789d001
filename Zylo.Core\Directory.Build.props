<Project>

  <!-- ========== Zylo 框架统一版本管理 ========== -->
  <PropertyGroup>
    <!-- 🔥 统一版本号 - 所有项目自动继承 -->
    <ZyloFrameworkVersion>1.3.4</ZyloFrameworkVersion>
    <PackageVersion>$(ZyloFrameworkVersion)</PackageVersion>
    <AssemblyVersion>$(ZyloFrameworkVersion).0</AssemblyVersion>
    <FileVersion>$(ZyloFrameworkVersion).0</FileVersion>

    <!-- 🔥 统一包信息 - 所有项目自动继承 -->
    <Authors>Zylo Development Team</Authors>
    <Company>Zylo</Company>
    <Copyright>Copyright © Zylo $(Year)</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/zylo/zylo-framework</RepositoryUrl>

    <!-- 🔥 统一构建配置 -->
    <TargetFrameworks>net6.0;net8.0;net8.0-windows</TargetFrameworks>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <PackageOutputPath>D:\NuGet</PackageOutputPath>

    <!-- 🔥 统一文档配置 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <PackageReadmeFile>README.md</PackageReadmeFile>

    <!-- 🔥 统一警告抑制 -->
    <NoWarn>$(NoWarn);CS1591</NoWarn>
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <!-- ========== 动态年份计算 ========== -->
  <PropertyGroup>
    <Year>$([System.DateTime]::Now.Year)</Year>
  </PropertyGroup>

  <!-- ========== 包含 README 文件 ========== -->
  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" Condition="Exists('README.md')" />
  </ItemGroup>

  <!-- ========== 版本信息显示 ========== -->
  <Target Name="ShowZyloVersion" BeforeTargets="Build">
    <Message Text="🚀 构建 $(MSBuildProjectName) v$(ZyloFrameworkVersion)" Importance="high" />
  </Target>

</Project>
