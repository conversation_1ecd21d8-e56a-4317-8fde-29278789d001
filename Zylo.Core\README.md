# YConfigGenerator

[![.NET](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/your-repo/YConfigGenerator)

**YConfigGenerator** 是一个强大的 C# 源代码生成器，专为企业级应用程序设计，提供完整的配置管理解决方案。通过简单的特性标注，自动生成类型安全、功能丰富的配置类，支持 JSON 文件操作、依赖注入集成、异步操作等企业级功能。

## ✨ 核心特性

### 🏗️ **企业级配置管理**
- **类型安全的配置绑定** - 自动生成强类型配置类
- **JSON 文件支持** - 完整的读写、序列化/反序列化功能
- **数据验证** - 集成 DataAnnotations 验证
- **配置热更新** - 运行时配置变更和事件通知
- **错误处理** - 完善的异常处理机制

### ⚡ **异步非阻塞操作**
- **异步文件操作** - `SaveToJsonFileAsync()`, `LoadFromJsonFileAsync()`
- **后台保存** - `SaveToJsonFileBackground()` 不阻塞主线程
- **取消令牌支持** - 完整的 `CancellationToken` 支持

### 🔧 **依赖注入集成**
- **Microsoft.Extensions.DependencyInjection** 完整支持
- **IOptions<T> 模式** - 标准 .NET 配置模式
- **多种注册方式** - 基础注册、JSON文件注册、异步注册、延迟注册
- **配置管理器服务** - 高级配置管理功能

### 💾 **DI 注入后保存功能**
- **运行时配置修改** - 修改 DI 注入的配置并保存到 JSON
- **配置同步** - DI 实例和静态实例自动同步
- **热更新支持** - 实时配置变更和持久化

## 🚀 快速开始

### 1. 安装

```xml
<PackageReference Include="Zylo.AutoG" Version="1.0.0" />
```

### 2. 定义配置类

```csharp
using Zylo.AutoG.Attributes;

[YConfig]
public partial class DatabaseConfig
{
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 5432;
    public string DatabaseName { get; set; } = "myapp";
    public string Username { get; set; } = "admin";
    public bool UseSsl { get; set; } = true;
}
```

### 3. 使用生成的配置

```csharp
// 基础使用
var config = new ConfigurationBuilder()
    .AddJsonFile("appsettings.json")
    .Build();

DatabaseConfig.Load(config);
Console.WriteLine($"数据库主机: {DatabaseConfig.Current.Host}");

// JSON 文件操作
DatabaseConfig.SaveToJsonFile("database.json");
DatabaseConfig.LoadFromJsonFile("database.json");

// 异步操作
await DatabaseConfig.SaveToJsonFileAsync("database.json");
await DatabaseConfig.LoadFromJsonFileAsync("database.json");
```

## 🏗️ 依赖注入集成

### 基础 DI 注册

```csharp
var services = new ServiceCollection();

// 基础注册
services.AddDatabaseConfig(configuration);

// JSON 文件注册
services.AddDatabaseConfigFromJsonFile("database.json");

// 异步注册
await services.AddDatabaseConfigFromJsonFileAsync("database.json");

// 延迟注册
services.AddDatabaseConfigFromJsonFileLazy("database.json");

var provider = services.BuildServiceProvider();

// 使用配置
var dbConfig = provider.GetService<DatabaseConfig>();
var optionsConfig = provider.GetService<IOptions<DatabaseConfig>>();
```

### 配置管理器服务

```csharp
// 注册配置管理器
services.AddDatabaseConfigManager(configuration, "database.json");

var provider = services.BuildServiceProvider();
var manager = provider.GetService<IDatabaseConfigManager>();

// 高级功能
Console.WriteLine($"当前版本: {manager.Version}");
Console.WriteLine($"当前配置: {manager.Current.Host}");

// 更新配置
var newConfig = new DatabaseConfig { Host = "new-server.com" };
manager.UpdateConfiguration(newConfig);

// 保存配置
await manager.SaveToJsonFileAsync();

// 创建备份
string backupPath = manager.CreateBackup();

// 从备份恢复
manager.RestoreFromBackup(backupPath);
```

## 💾 DI 注入后保存 JSON

```csharp
// 场景1: 运行时配置修改
var dbConfig = provider.GetService<DatabaseConfig>();
dbConfig.Host = "updated-server.com";
dbConfig.Port = 5433;

// 保存修改后的配置
await DatabaseConfig.SaveToJsonFileAsync("updated-config.json", dbConfig);

// 场景2: 配置热更新
var manager = provider.GetService<IDatabaseConfigManager>();
var updatedConfig = new DatabaseConfig 
{ 
    Host = "hot-updated-server.com",
    Port = 6543 
};

manager.UpdateConfiguration(updatedConfig);
await manager.SaveToJsonFileAsync(); // 自动保存到原文件

// 场景3: 配置同步
// DI 实例修改会自动同步到静态 Current 属性
Console.WriteLine($"DI配置: {dbConfig.Host}");
Console.WriteLine($"静态配置: {DatabaseConfig.Current.Host}"); // 自动同步
```

## 🔧 高级功能

### 数据验证

```csharp
[YConfig(EnableValidation = true)]
public partial class UserConfig
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = "";
    
    [EmailAddress]
    public string Email { get; set; } = "";
    
    [Range(18, 120)]
    public int Age { get; set; } = 18;
}
```

### 环境变量支持

```csharp
[YConfig]
public partial class EnvironmentConfig
{
    public string Environment { get; set; } = "Development";
    public string ApiUrl { get; set; } = "https://api.example.com";
    public string LogLevel { get; set; } = "Information";
}

// 自动读取环境变量
EnvironmentConfig.Load(configuration);
```

### 配置变更事件

```csharp
DatabaseConfig.ConfigurationChanged += (oldConfig, newConfig) =>
{
    Console.WriteLine($"配置已更新: {oldConfig.Host} -> {newConfig.Host}");
};

// 触发事件
DatabaseConfig.Load(newConfiguration);
```

### 复杂嵌套配置

```csharp
[YConfig]
public partial class ComplexConfig
{
    public string ServiceName { get; set; } = "MyService";
    public int Port { get; set; } = 8080;
    
    public RedisSettings Redis { get; set; } = new();
    public LoggingSettings Logging { get; set; } = new();
    public FeatureFlags Features { get; set; } = new();
}

public class RedisSettings
{
    public string Host { get; set; } = "localhost";
    public int Port { get; set; } = 6379;
}

public class LoggingSettings
{
    public string Level { get; set; } = "Information";
    public string FilePath { get; set; } = "logs/app.log";
}

public class FeatureFlags
{
    public bool EnableCaching { get; set; } = true;
    public bool EnableRateLimiting { get; set; } = false;
}
```

## 📊 功能对比

| 功能 | YConfigGenerator | 传统方式 |
|------|------------------|----------|
| **类型安全** | ✅ 编译时检查 | ❌ 运行时错误 |
| **JSON 支持** | ✅ 完整支持 | ⚠️ 手动实现 |
| **异步操作** | ✅ 原生支持 | ❌ 需要手动实现 |
| **DI 集成** | ✅ 开箱即用 | ⚠️ 复杂配置 |
| **配置管理器** | ✅ 企业级功能 | ❌ 不支持 |
| **热更新** | ✅ 事件驱动 | ❌ 需要重启 |
| **数据验证** | ✅ 自动验证 | ⚠️ 手动验证 |
| **错误处理** | ✅ 完善机制 | ⚠️ 自行处理 |

## 🎯 使用场景

### 企业级应用
- **微服务配置管理** - 统一的配置管理方案
- **多环境部署** - Development、Staging、Production
- **配置中心集成** - 与配置中心无缝集成

### 开发效率
- **快速原型开发** - 几分钟内完成配置系统
- **类型安全** - 编译时发现配置错误
- **智能提示** - IDE 完整支持

### 运维友好
- **配置热更新** - 无需重启应用
- **配置备份恢复** - 安全的配置管理
- **版本跟踪** - 配置变更历史

## 📈 性能特性

- **零运行时开销** - 编译时代码生成
- **异步非阻塞** - 不影响主线程性能
- **内存友好** - 单例模式，最小内存占用
- **高并发支持** - 线程安全设计

## 🔍 示例项目

查看 `YConfigFullTest` 项目获取完整的使用示例，包含：
- 12 种不同的配置场景
- 完整的测试用例
- 最佳实践演示

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 📞 支持

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/YConfigGenerator/issues)
- 📖 文档: [Wiki](https://github.com/your-repo/YConfigGenerator/wiki)

---

**YConfigGenerator - 让配置管理变得简单而强大！** 🚀
