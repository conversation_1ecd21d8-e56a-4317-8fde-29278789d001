using System;
using System.Text;
using System.Collections.Generic;

public static class EmojiWidthTest
{
    public static void RunTest()
    {
        Console.WriteLine("🔍 超精确对齐调试工具 v2.0");
        Console.WriteLine("===============================");
        Console.WriteLine();

        Console.WriteLine("📏 使用标尺进行精确测量：");
        Console.WriteLine("0123456789012345678901234567890123456789012345678901234567890");
        Console.WriteLine("          1         2         3         4         5         6");
        Console.WriteLine();

        // 测试当前YLogger的实际输出格式
        Console.WriteLine("🎯 当前YLogger格式测试（5个空格）：");
        var currentFormats = new[]
        {
            "🐛 DBG     ",
            "📋 IN+     ",
            "ℹ️ INF     ",
            "💡 IN-     ",
            "⚠️ WRN     ",
            "❌ ERR     "
        };

        foreach (var format in currentFormats)
        {
            Console.WriteLine($"{format}🧵 [TestClass.TestMethod] 测试消息");
        }
        Console.WriteLine();

        // 精确测试不同空格数
        Console.WriteLine("🔬 精确空格数测试（3-7个空格）：");
        for (int spaces = 3; spaces <= 7; spaces++)
        {
            Console.WriteLine($"--- {spaces}个空格 ---");
            Console.WriteLine($"🐛 DBG{new string(' ', spaces)}🧵 [TestClass] 调试消息");
            Console.WriteLine($"📋 IN+{new string(' ', spaces)}🧵 [TestClass] 详细消息");
            Console.WriteLine($"ℹ️ INF{new string(' ', spaces)}🧵 [TestClass] 信息消息");
            Console.WriteLine($"💡 IN-{new string(' ', spaces)}🧵 [TestClass] 简化消息");
            Console.WriteLine($"⚠️ WRN{new string(' ', spaces)}🧵 [TestClass] 警告消息");
            Console.WriteLine($"❌ ERR{new string(' ', spaces)}🧵 [TestClass] 错误消息");
            Console.WriteLine();
        }

        // 微调测试 - 为每个emoji单独调整
        Console.WriteLine("🎛️ 微调测试 - 单独调整每个emoji：");

        var microAdjustments = new[]
        {
            ("方案A", new[] { 4, 5, 5, 5, 5, 6 }), // 🐛少1个，❌多1个
            ("方案B", new[] { 5, 4, 5, 5, 5, 5 }), // 📋少1个
            ("方案C", new[] { 5, 5, 4, 5, 5, 5 }), // ℹ️少1个
            ("方案D", new[] { 5, 5, 5, 4, 5, 5 }), // 💡少1个
            ("方案E", new[] { 5, 5, 5, 5, 4, 5 }), // ⚠️少1个
            ("方案F", new[] { 6, 5, 5, 5, 5, 4 }), // 🐛多1个，❌少1个
        };

        var emojis = new[] { "🐛 DBG", "📋 IN+", "ℹ️ INF", "💡 IN-", "⚠️ WRN", "❌ ERR" };

        foreach (var (name, spaces) in microAdjustments)
        {
            Console.WriteLine($"--- {name}: [{string.Join(",", spaces)}] ---");
            for (int i = 0; i < emojis.Length; i++)
            {
                Console.WriteLine($"{emojis[i]}{new string(' ', spaces[i])}🧵 [TestClass] 测试消息");
            }
            Console.WriteLine();
        }

        Console.WriteLine("📊 请仔细观察上面的输出，找出🧵图标最对齐的方案！");
    }
}
