using System;
using System.Collections.Generic;

/// <summary>
/// 级别对齐专用测试工具
/// 🎯 专门用于调试和优化日志级别文字的对齐效果
/// </summary>
public static class LevelAlignmentTest
{
    public static void RunTest()
    {
        Console.WriteLine("🎯 级别对齐专用测试工具");
        Console.WriteLine("========================");
        Console.WriteLine();

        // 测试所有日志级别的基础文字
        var levels = new[]
        {
            ("DEBUG", "🐛 DBG"),
            ("INFORMATIONDETAILED", "📋 IN+"),
            ("INFORMATION", "ℹ️ INF"),
            ("INFORMATIONSIMPLE", "💡 IN-"),
            ("WARNING", "⚠️ WRN"),
            ("ERROR", "❌ ERR")
        };

        Console.WriteLine("1️⃣ 基础级别文字测试（无填充）：");
        Console.WriteLine("格式: [级别文字]|<-- 观察对齐情况");
        foreach (var (name, text) in levels)
        {
            Console.WriteLine($"{text}|<-- {name}");
        }
        Console.WriteLine();

        Console.WriteLine("2️⃣ 不同目标宽度的填充测试：");
        for (int targetWidth = 8; targetWidth <= 15; targetWidth++)
        {
            Console.WriteLine($"--- 目标宽度: {targetWidth} ---");
            foreach (var (name, text) in levels)
            {
                var padded = PadToTargetWidth(text, targetWidth);
                Console.WriteLine($"{padded}|<-- {name} (长度:{padded.Length})");
            }
            Console.WriteLine();
        }

        Console.WriteLine("3️⃣ 最佳宽度推荐测试：");
        var recommendedWidth = FindOptimalWidth(levels);
        Console.WriteLine($"推荐的最佳目标宽度: {recommendedWidth}");
        Console.WriteLine();
        
        Console.WriteLine("使用推荐宽度的效果：");
        foreach (var (name, text) in levels)
        {
            var padded = PadToTargetWidth(text, recommendedWidth);
            Console.WriteLine($"{padded}|<-- {name}");
        }
        Console.WriteLine();

        Console.WriteLine("4️⃣ 模拟完整日志格式测试：");
        Console.WriteLine("格式: [时间戳] [级别文字] 🧵 [类名.方法] 消息内容");
        foreach (var (name, text) in levels)
        {
            var padded = PadToTargetWidth(text, recommendedWidth);
            var timestamp = "[2025-07-13 11:30:00.123]";
            var threadInfo = "🧵";
            var classMethod = "[TestClass.TestMethod]";
            var message = "这是一条测试消息";
            
            Console.WriteLine($"{timestamp} {padded} {threadInfo} {classMethod} {message}");
        }
        Console.WriteLine();

        Console.WriteLine("5️⃣ 字符宽度分析：");
        foreach (var (name, text) in levels)
        {
            var displayWidth = CalculateDisplayWidth(text);
            var stringLength = text.Length;
            Console.WriteLine($"{text} -> 字符串长度:{stringLength}, 显示宽度:{displayWidth}, 差值:{displayWidth - stringLength}");
        }
    }

    /// <summary>
    /// 将文字填充到目标宽度
    /// </summary>
    private static string PadToTargetWidth(string text, int targetWidth)
    {
        var currentWidth = CalculateDisplayWidth(text);
        var spacesNeeded = Math.Max(0, targetWidth - currentWidth);
        return text + new string(' ', spacesNeeded);
    }

    /// <summary>
    /// 计算文字的实际显示宽度
    /// </summary>
    private static int CalculateDisplayWidth(string text)
    {
        int width = 0;
        foreach (char c in text)
        {
            if (IsEmoji(c))
            {
                width += 2; // emoji占用2个字符宽度
            }
            else
            {
                width += 1; // 普通字符占用1个字符宽度
            }
        }
        return width;
    }

    /// <summary>
    /// 判断字符是否为emoji
    /// </summary>
    private static bool IsEmoji(char c)
    {
        return c >= 0x1F000 && c <= 0x1F9FF || // 各种符号和象形文字
               c >= 0x2600 && c <= 0x26FF ||   // 杂项符号
               c >= 0x2700 && c <= 0x27BF;     // 装饰符号
    }

    /// <summary>
    /// 找到最佳的目标宽度
    /// </summary>
    private static int FindOptimalWidth((string name, string text)[] levels)
    {
        // 计算所有级别文字的最大显示宽度
        int maxWidth = 0;
        foreach (var (_, text) in levels)
        {
            var width = CalculateDisplayWidth(text);
            if (width > maxWidth)
            {
                maxWidth = width;
            }
        }
        
        // 推荐宽度 = 最大宽度 + 2个空格的缓冲
        return maxWidth + 2;
    }
}
