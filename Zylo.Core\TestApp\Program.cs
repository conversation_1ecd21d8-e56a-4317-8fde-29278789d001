using Zylo.YLog.Runtime;

class Program
{
    public class UserService
    {
        // 🔥 正在重点调试的类 - 使用详细模式
        public readonly YLoggerInstance _logger = YLogger.ForDetailed<UserService>();

        public void ProcessUser(int userId)
        {
            _logger.Debug($"🔍 调试信息: 开始处理用户 {userId}");
            _logger.InfoDetailed($"开始处理用户: {userId}");
            _logger.InfoDetailed("验证用户权限");
            _logger.Info("执行用户业务逻辑");
            _logger.InfoSimple("用户处理完成");
            _logger.Warning("用户处理耗时较长");
            _logger.Debug($"🔍 调试信息: 用户 {userId} 处理完毕");
        }
    }

    public class CacheService
    {
        // 🔥 已测试稳定的类 - 使用简化模式
        private static readonly YLoggerInstance _logger = YLogger.ForSimple<CacheService>();

        public void SetCache(string key)
        {
            _logger.Debug($"🔍 调试: 准备设置缓存 {key}");
            _logger.InfoSimple($"缓存 {key} 设置成功");
            _logger.Warning("缓存空间不足");
        }

        public void GetCache(string key)
        {
            _logger.Debug($"🔍 调试: 准备获取缓存 {key}");
            _logger.InfoSimple($"缓存 {key} 获取成功");
            _logger.Warning("缓存已过期");
        }
    }

    public class DatabaseService
    {
        // 🔥 生产环境稳定类 - 使用静默模式
        private static readonly YLoggerInstance _logger = YLogger.ForSilent<DatabaseService>();

        public void ExecuteQuery(string sql)
        {
            _logger.Debug($"🔍 调试: 准备执行SQL查询");
            _logger.Warning($"执行SQL: {sql}");
            if (sql.Contains("DROP"))
            {
                _logger.Error("危险的SQL操作");
                throw new InvalidOperationException("不允许DROP操作");
            }
        }

        // 🔥 测试：不同的方法名会自动显示
        public void CreateTable(string tableName)
        {
            _logger.Debug($"🔍 调试: 开始创建表 {tableName}");
            _logger.Warning($"创建表: {tableName}");
        }

        public void DropTable(string tableName)
        {
            _logger.Debug($"🔍 调试: 尝试删除表 {tableName}");
            _logger.Error($"删除表: {tableName}");
        }
    }

    public class EmailService
    {
        // 🔥 一般业务类 - 使用一般模式
        private static readonly YLoggerInstance _logger = YLogger.ForInfo<EmailService>();

        public void SendEmail(string to, string subject)
        {
            _logger.Debug($"🔍 调试: 准备发送邮件到 {to}");
            _logger.InfoDetailed($"开始发送邮件: {subject}");
            _logger.Info($"发送邮件到: {to}");
            _logger.InfoSimple("邮件发送完成");
            _logger.Warning("邮件服务器响应较慢");
            _logger.Error("邮件发送失败");
        }
    }

    public class PaymentService
    {
        // 🔥 正在开发调试的新功能 - 使用调试模式
        private static readonly YLoggerInstance _logger = YLogger.ForDebug<PaymentService>();

        public void ProcessPayment(decimal amount, string method)
        {
            // 🔥 新功能：性能监控
            using (_logger.Monitor($"处理支付 {amount:C}"))
            {
                _logger.Debug($"🔍 调试: 开始处理支付 {amount:C} via {method}");
                _logger.InfoDetailed($"验证支付参数: 金额={amount}, 方式={method}");

                // 模拟处理时间
                Thread.Sleep(150); // 模拟较慢的操作

                _logger.Info($"处理支付: {amount:C}");
                _logger.InfoSimple("支付处理完成");
                _logger.Warning("支付处理耗时较长");
                _logger.Error("支付处理失败");
            }
        }

        public void ProcessBatchPayments(List<decimal> amounts)
        {
            // 🔥 新功能：批量操作监控
            using (YLogger.BatchOperation("批量支付处理", amounts.Count))
            {
                foreach (var amount in amounts)
                {
                    ProcessPayment(amount, "批量");
                }
            }
        }
    }

    static void Main(string[] args)
    {
        // 🎯 第一步：运行级别对齐专用测试
        Console.WriteLine("🎯 第一步：级别对齐专用测试");
        Console.WriteLine();
        LevelAlignmentTest.RunTest();

        Console.WriteLine();
        Console.WriteLine("按任意键继续Emoji宽度测试...");
        Console.ReadKey();
        Console.Clear();

        // 🔍 第二步：运行emoji宽度测试
        Console.WriteLine("🔍 第二步：Emoji宽度测试");
        Console.WriteLine();
        EmojiWidthTest.RunTest();

        Console.WriteLine();
        Console.WriteLine("按任意键继续YLogger测试...");
        Console.ReadKey();
        Console.Clear();

        Console.WriteLine("🎨 YLogger 多级别INFO灵活控制展示");
        Console.WriteLine(new string('=', 50));

        try
        {
            YLogger.ConfigureForDevelopment();

            var userService = new UserService();        // ForDetailed - 重点调试
            var cacheService = new CacheService();      // ForSimple - 已测试稳定
            var dbService = new DatabaseService();      // ForSilent - 生产稳定
            var emailService = new EmailService();      // ForInfo - 一般业务
            var paymentService = new PaymentService();  // ForDebug - 新功能开发

            Console.WriteLine("\n🧪 开始测试不同级别的效果：");

            Console.WriteLine("\n📋 展示：不同类使用不同的日志级别");
            Console.WriteLine("UserService(详细) | CacheService(简化) | DatabaseService(静默) | EmailService(一般) | PaymentService(调试)");
            Console.WriteLine(new string('-', 100));

            userService.ProcessUser(123);
            cacheService.SetCache("user:123");
            cacheService.GetCache("user:123");
            dbService.ExecuteQuery("SELECT * FROM Users");
            emailService.SendEmail("<EMAIL>", "测试邮件");
            paymentService.ProcessPayment(99.99m, "信用卡");

            Console.WriteLine("\n📋 第二轮：全局强制Warning级别");
            YLogger.ForceProductionMode();  // 强制所有类只输出Warning及以上
            userService.ProcessUser(456);
            cacheService.SetCache("user:456");
            cacheService.GetCache("user:456");
            dbService.ExecuteQuery("SELECT * FROM Orders");

            Console.WriteLine("\n📋 第三轮：全局强制Debug级别");
            YLogger.ForceDebugMode();  // 强制所有类输出Debug及以上
            userService.ProcessUser(789);
            cacheService.SetCache("user:789");
            dbService.ExecuteQuery("SELECT * FROM Products");

            Console.WriteLine("\n📋 第四轮：恢复独立控制");
            YLogger.RestoreIndependentMode();  // 恢复各类独立控制
            userService.ProcessUser(999);
            cacheService.SetCache("user:999");
            dbService.ExecuteQuery("SELECT * FROM Categories");

            // 🔥 测试不同方法名的自动获取
            dbService.CreateTable("NewTable");
            dbService.DropTable("OldTable");

            try
            {
                dbService.ExecuteQuery("DROP TABLE Users");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✅ 异常被正确处理: {ex.Message}");
            }

            Console.WriteLine("\n🔥 测试新功能：临时日志级别控制");
            Console.WriteLine("--------------------------------------------------");

            // 临时详细模式测试
            Console.WriteLine("📢 临时启用详细模式：");
            using (YLogger.TemporaryVerbose())
            {
                userService.ProcessUser(888);
                cacheService.SetCache("temp:888");
            }

            Console.WriteLine("\n🔇 临时启用静默模式：");
            using (YLogger.TemporarySilent())
            {
                userService.ProcessUser(777);
                cacheService.SetCache("temp:777");
                emailService.SendEmail("<EMAIL>", "临时邮件");
            }

            Console.WriteLine("\n🔄 恢复正常模式：");
            userService.ProcessUser(666);

            Console.WriteLine("\n🔥 测试新功能：性能监控");
            Console.WriteLine("--------------------------------------------------");

            // 测试单个操作性能监控
            Console.WriteLine("📊 单个操作性能监控：");
            paymentService.ProcessPayment(199.99m, "信用卡");

            Console.WriteLine("\n📊 批量操作性能监控：");
            var amounts = new List<decimal> { 50.0m, 75.5m, 120.0m };
            paymentService.ProcessBatchPayments(amounts);

            Console.WriteLine("\n📊 全局性能监控：");
            using (YLogger.MonitorPerformance("整体业务流程"))
            {
                userService.ProcessUser(777);
                emailService.SendEmail("<EMAIL>", "性能测试");
                Thread.Sleep(200); // 模拟一些处理时间
            }

            Console.WriteLine("\n🔥 测试高级功能：条件日志和统计");
            Console.WriteLine("--------------------------------------------------");

            // 测试条件日志
            Console.WriteLine("📊 条件日志测试：");
            var isDebugMode = true;
            var userCount = 150;

            userService._logger.InfoIf(isDebugMode, "调试模式已启用");
            userService._logger.InfoIf(userCount > 100, $"用户数量较多: {userCount}");
            userService._logger.WarningIf(userCount > 200, "用户数量超出警告阈值");

            // 测试计数器日志
            Console.WriteLine("\n📊 计数器日志测试（每3次记录一次）：");
            for (int i = 1; i <= 10; i++)
            {
                userService._logger.InfoEvery(3, $"处理第{i}个用户");
            }

            // 测试带上下文的日志
            Console.WriteLine("\n📊 上下文日志测试：");
            var context = new { UserId = 123, SessionId = "abc-123", IP = "***********" };
            userService._logger.InfoWithContext("用户登录成功", context);

            // 显示日志统计
            Console.WriteLine("\n📊 日志系统统计：");
            var stats = YLogger.GetStatistics();
            Console.WriteLine($"总日志数: {stats.TotalLogs}");
            Console.WriteLine($"调试: {stats.DebugCount}, 信息: {stats.InfoCount}, 警告: {stats.WarningCount}, 错误: {stats.ErrorCount}");
            Console.WriteLine($"运行时间: {(DateTime.Now - stats.StartTime).TotalMinutes:F1} 分钟");

            // 显示健康状态
            var health = YLogger.CheckHealth();
            Console.WriteLine($"\n🏥 系统健康状态: {health.GetHealthReport()}");

            Console.WriteLine("\n✅ 测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 错误: {ex.Message}");
        }
        YLogger.Info("程序结束");

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}