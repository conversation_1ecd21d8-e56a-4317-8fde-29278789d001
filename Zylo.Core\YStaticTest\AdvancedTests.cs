using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#region 🎯 高级功能测试

/// <summary>
/// 高级功能测试 - 测试各种边界情况和复杂场景
/// </summary>

// 🚀 测试1：泛型约束的静态方法
[YStatic]
public partial class GenericConstraintTests
{
    /// <summary>
    /// 泛型约束测试 - IComparable
    /// </summary>
    public T Max<T>(T a, T b) where T : IComparable<T>
    {
        return a.CompareTo(b) > 0 ? a : b;
    }

    /// <summary>
    /// 多重泛型约束测试
    /// </summary>
    public void ProcessItem<T>(T item) where T : class, IDisposable, new()
    {
        using (item)
        {
            // 处理逻辑
        }
    }
}

// 🚀 测试2：复杂参数的扩展方法
[YStaticExtension]
public partial class ComplexParameterTests
{
    /// <summary>
    /// 复杂参数扩展方法 - 委托参数
    /// </summary>
    public TResult Transform<T, TResult>(T input, Func<T, TResult> transformer)
    {
        return transformer(input);
    }

    /// <summary>
    /// 简单字符串处理扩展方法
    /// </summary>
    public string AddPrefix(string text, string prefix)
    {
        return $"{prefix}{text}";
    }
}

// 🚀 测试3：异步方法测试
[YStatic]
public partial class AsyncTests
{
    /// <summary>
    /// 异步方法测试
    /// </summary>
    public async Task<string> FetchDataAsync(string url)
    {
        await Task.Delay(100); // 模拟网络请求
        return $"Data from {url}";
    }

    /// <summary>
    /// 异步泛型方法测试
    /// </summary>
    public async Task<T> ProcessAsync<T>(T data, Func<T, Task<T>> processor)
    {
        await Task.Delay(50);
        return await processor(data);
    }
}

// 🚀 测试4：混合属性测试（方法级）
public partial class MixedAttributeTests
{
    /// <summary>
    /// 普通方法 - 不生成
    /// </summary>
    public string NormalMethod(string input) => $"Normal: {input}";

    /// <summary>
    /// 静态方法 - 生成普通静态方法
    /// </summary>
    [YStatic]
    public int Calculate(int a, int b) => a * b + 10;

    /// <summary>
    /// 扩展方法 - 生成扩展方法
    /// </summary>
    [YStaticExtension]
    public string FormatText(string text, string prefix = ">>") => $"{prefix} {text}";

    /// <summary>
    /// 忽略的方法 - 不生成
    /// </summary>
    [YStatic]
    [YStaticIgnore]
    public string IgnoredMethod(string input) => $"Ignored: {input}";
}

// 🚀 测试5：空值处理测试
[YStaticExtension]
public partial class NullHandlingTests
{
    /// <summary>
    /// 空值安全处理
    /// </summary>
    public string SafeToString(object? obj)
    {
        return obj?.ToString() ?? "NULL";
    }

    /// <summary>
    /// 可空引用类型处理
    /// </summary>
    public int GetLengthOrZero(string? text)
    {
        return text?.Length ?? 0;
    }
}

// 🚀 测试6：性能测试类
[YStatic]
public partial class PerformanceTests
{
    /// <summary>
    /// 简单计算 - 性能基准测试
    /// </summary>
    public long Fibonacci(int n)
    {
        if (n <= 1) return n;
        return Fibonacci(n - 1) + Fibonacci(n - 2);
    }

    /// <summary>
    /// 数组处理 - 内存分配测试
    /// </summary>
    public int[] CreateRange(int start, int count)
    {
        var result = new int[count];
        for (int i = 0; i < count; i++)
        {
            result[i] = start + i;
        }
        return result;
    }
}

#endregion
