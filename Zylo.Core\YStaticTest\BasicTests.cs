using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#if true



#region v1.3 升级 - YStatic 基础测试类

/// <summary>
/// YStatic v1.3 基础功能测试
/// 
/// 🎯 测试目标：
/// 1. 验证 [YStatic] 类级属性的静态方法生成
/// 2. 验证 [YStaticExtension] 类级属性的扩展方法生成
/// 3. 验证基本的方法调用和参数传递
/// 4. 验证生成代码的编译正确性
/// 
/// 💡 设计理念：
/// - 简单明了：专注于核心功能验证
/// - 易于调试：每个测试类功能单一
/// - 渐进式：从简单到复杂逐步验证
/// </summary>

// 🚀 测试1：YStatic 基础静态方法生成
[YStatic]
public partial class SimpleMath
{
    /// <summary>
    /// 加法运算
    /// </summary>
    /// <param name="a">第一个数</param>
    /// <param name="b">第二个数</param>
    /// <returns>两数之和</returns>
    public int Add(int a, int b)
    {
        return a + b;
    }

    /// <summary>
    /// 减法运算
    /// </summary>
    /// <param name="a">被减数</param>
    /// <param name="b">减数</param>
    /// <returns>差值</returns>
    public int Subtract(int a, int b)
    {
        return a - b;
    }

    /// <summary>
    /// 乘法运算
    /// </summary>
    /// <param name="x">第一个因数</param>
    /// <param name="y">第二个因数</param>
    /// <returns>乘积</returns>
    public double Multiply(double x, double y)
    {
        return x * y;
    }
}

// 🚀 测试2：YStaticExtension 基础扩展方法生成
[YStaticExtension]
public partial class StringUtils
{
    /// <summary>
    /// 反转字符串
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>反转后的字符串</returns>
    public string Reverse(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        char[] chars = input.ToCharArray();
        Array.Reverse(chars);
        return new string(chars);
    }

    /// <summary>
    /// 转换为大写
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>大写字符串</returns>
    public string ToUpperCase(string input)
    {
        return input?.ToUpper() ?? string.Empty;
    }

    /// <summary>
    /// 获取字符串长度（安全版本）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>字符串长度</returns>
    public int SafeLength(string input)
    {
        return input?.Length ?? 0;
    }
}

// 🚀 测试3：带泛型的静态方法
[YStatic]
public partial class GenericHelper
{
    /// <summary>
    /// 泛型相等比较
    /// </summary>
    /// <typeparam name="T">比较类型</typeparam>
    /// <param name="a">第一个值</param>
    /// <param name="b">第二个值</param>
    /// <returns>是否相等</returns>
    public bool AreEqual<T>(T a, T b) where T : IEquatable<T>
    {
        if (a == null && b == null) return true;
        if (a == null || b == null) return false;
        return a.Equals(b);
    }

    /// <summary>
    /// 获取默认值
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <returns>默认值</returns>
    public T? GetDefault<T>()
    {
        return default(T);
    }
}

// 🚀 测试4：带泛型约束的扩展方法
[YStaticExtension]
public partial class CollectionExtensions
{
    /// <summary>
    /// 安全获取第一个元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">集合</param>
    /// <returns>第一个元素或默认值</returns>
    public T? SafeFirst<T>(IEnumerable<T> collection)
    {
        if (collection == null) return default(T);

        foreach (var item in collection)
        {
            return item;
        }

        return default(T);
    }

    /// <summary>
    /// 安全计数
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">集合</param>
    /// <returns>元素数量</returns>
    public int SafeCount<T>(IEnumerable<T> collection)
    {
        if (collection == null) return 0;

        if (collection is ICollection<T> coll)
            return coll.Count;

        return collection.Count();
    }
}

// 🚀 测试5：异步方法支持
[YStatic]
public partial class AsyncHelper
{
    /// <summary>
    /// 异步延迟
    /// </summary>
    /// <param name="milliseconds">延迟毫秒数</param>
    /// <returns>异步任务</returns>
    public async Task DelayAsync(int milliseconds)
    {
        await Task.Delay(milliseconds);
    }

    /// <summary>
    /// 异步获取字符串长度
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>字符串长度</returns>
    public async Task<int> GetLengthAsync(string input)
    {
        await Task.Delay(1); // 模拟异步操作
        return input?.Length ?? 0;
    }

    /// <summary>
    /// 异步处理数据
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="data">输入数据</param>
    /// <param name="processor">处理函数</param>
    /// <returns>处理结果</returns>
    public async Task<TResult> ProcessAsync<T, TResult>(T data, Func<T, Task<TResult>> processor)
    {
        if (processor == null) throw new ArgumentNullException(nameof(processor));
        return await processor(data);
    }
}

#endregion

#region 🎯 方法级属性测试

/// <summary>
/// 方法级 YStatic 测试类 - 测试方法级属性
///
/// 🎯 测试目标：
/// - 方法级 [YStatic] 属性
/// - 方法级 [YStaticExtension] 属性
/// - 混合使用不同的方法级属性
/// - 选择性生成（只有标记的方法才生成）
///
/// 💡 注意：这个类没有类级属性，只有方法级属性
/// </summary>
public partial class MethodLevelTests
{
    /// <summary>
    /// 普通方法 - 没有属性，不应该生成
    /// </summary>
    public string NormalMethod(string input)
    {
        return $"Normal: {input}";
    }

    /// <summary>
    /// 方法级静态标签 - 生成普通静态方法
    /// </summary>
    /// <param name="a">第一个数字</param>
    /// <param name="b">第二个数字</param>
    /// <returns>两数之和</returns>
    [YStatic]
    public int AddNumbers(int a, int b)
    {
        return a + b;
    }

    /// <summary>
    /// 方法级扩展标签 - 生成扩展方法
    /// </summary>
    /// <param name="text">要处理的文本</param>
    /// <returns>处理后的文本</returns>
    [YStaticExtension]
    public string ProcessText(string text)
    {
        return $"Processed: {text?.ToUpper() ?? "NULL"}";
    }

    /// <summary>
    /// 另一个静态方法 - 测试多个方法级属性
    /// </summary>
    /// <param name="value">输入值</param>
    /// <returns>平方值</returns>
    [YStatic]
    public double Square(double value)
    {
        return value * value;
    }

    /// <summary>
    /// 被忽略的方法 - 有属性但被 YStaticIgnore 排除
    /// </summary>
    /// <param name="input">输入</param>
    /// <returns>输出</returns>
    [YStatic]
    [YStaticIgnore]
    public string IgnoredMethod(string input)
    {
        return $"This should not be generated: {input}";
    }

    /// <summary>
    /// 私有方法 - 即使有属性也不应该生成
    /// </summary>
    /// <param name="value">值</param>
    /// <returns>结果</returns>
    [YStatic]
    private string PrivateMethod(string value)
    {
        return $"Private: {value}";
    }
}

#endregion

#endif