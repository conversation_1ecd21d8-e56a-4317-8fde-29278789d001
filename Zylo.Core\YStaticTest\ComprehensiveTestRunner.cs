using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace YStaticTest;

/// <summary>
/// 全面的测试运行器 - 验证 YStatic v1.3 的所有功能
/// </summary>
public static class ComprehensiveTestRunner
{
    public static async Task RunAllTestsAsync()
    {
        Console.WriteLine("🚀 YStatic v1.3 全面功能测试开始");
        Console.WriteLine(new string('=', 80));
        Console.WriteLine();

        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 基础功能测试
            TestBasicFunctionality();

            // 扩展方法测试
            TestExtensionMethods();

            // 泛型功能测试
            TestGenericFunctionality();

            // 异步功能测试
            await TestAsyncFunctionality();

            // 边界情况测试
            TestEdgeCases();

            // 性能测试
            TestPerformance();

            stopwatch.Stop();

            Console.WriteLine();
            Console.WriteLine("🎉 所有测试完成！");
            Console.WriteLine($"⏱️ 总耗时: {stopwatch.ElapsedMilliseconds}ms");
            Console.WriteLine("✅ YStatic v1.3 全面功能验证成功");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"📍 错误位置: {ex.StackTrace}");
        }
    }

    private static void TestBasicFunctionality()
    {
        Console.WriteLine("📋 1. 基础功能测试");
        Console.WriteLine("   测试类级和方法级属性的基本生成功能...");

        // 类级 [YStatic] 测试
        var sum = SimpleCalculatorEs.Add(15, 25);
        Console.WriteLine($"   ✅ SimpleCalculator.Add(15, 25) = {sum}");

        // 方法级 [YStatic] 测试
        var product = MixedAttributeTestsEs.Calculate(6, 7);
        Console.WriteLine($"   ✅ MixedAttributeTests.Calculate(6, 7) = {product}");

        Console.WriteLine();
    }

    private static void TestExtensionMethods()
    {
        Console.WriteLine("📋 2. 扩展方法测试");
        Console.WriteLine("   测试类级和方法级扩展方法生成...");

        // 方法级 [YStaticExtension] 测试
        var processed = "test string".ProcessText();
        Console.WriteLine($"   ✅ 'test string'.ProcessText() = '{processed}'");

        // 类级 [YStaticExtension] 测试
        var items = new[] { 10, 20, 30, 40, 50 };
        var first = items.SafeFirst();
        var count = items.SafeCount();
        Console.WriteLine($"   ✅ [10,20,30,40,50].SafeFirst() = {first}");
        Console.WriteLine($"   ✅ [10,20,30,40,50].SafeCount() = {count}");

        // 复杂参数扩展方法测试
        var transformed = "hello".Transform(s => s.ToUpper());
        Console.WriteLine($"   ✅ 'hello'.Transform(ToUpper) = '{transformed}'");

        Console.WriteLine();
    }

    private static void TestGenericFunctionality()
    {
        Console.WriteLine("📋 3. 泛型功能测试");
        Console.WriteLine("   测试泛型方法和约束的生成...");

        // 泛型约束测试
        var maxInt = GenericConstraintTestsEs.Max(10, 20);
        var maxString = GenericConstraintTestsEs.Max("apple", "banana");
        Console.WriteLine($"   ✅ GenericConstraintTests.Max(10, 20) = {maxInt}");
        Console.WriteLine($"   ✅ GenericConstraintTests.Max('apple', 'banana') = '{maxString}'");

        // 泛型扩展方法测试
        var defaultInt = GenericHelperEs.GetDefault<int>();
        var defaultString = GenericHelperEs.GetDefault<string>();
        Console.WriteLine($"   ✅ GenericHelper.GetDefault<int>() = {defaultInt}");
        Console.WriteLine($"   ✅ GenericHelper.GetDefault<string>() = {defaultString ?? "null"}");

        Console.WriteLine();
    }

    private static async Task TestAsyncFunctionality()
    {
        Console.WriteLine("📋 4. 异步功能测试");
        Console.WriteLine("   测试异步方法的生成和执行...");

        // 异步方法测试
        var data = await AsyncTestsEs.FetchDataAsync("https://api.example.com");
        Console.WriteLine($"   ✅ AsyncTests.FetchDataAsync() = '{data}'");

        // 异步泛型方法测试
        var processedData = await AsyncTestsEs.ProcessAsync("test", async s =>
        {
            await Task.Delay(10);
            return s.ToUpper();
        });
        Console.WriteLine($"   ✅ AsyncTests.ProcessAsync() = '{processedData}'");

        Console.WriteLine();
    }

    private static void TestEdgeCases()
    {
        Console.WriteLine("📋 5. 边界情况测试");
        Console.WriteLine("   测试空值处理、特殊参数等边界情况...");

        // 空值处理测试
        var nullStr = ((object?)null).SafeToString();
        var emptyStr = "".GetLengthOrZero();
        var nullLength = ((string?)null).GetLengthOrZero();

        Console.WriteLine($"   ✅ null.SafeToString() = '{nullStr}'");
        Console.WriteLine($"   ✅ ''.GetLengthOrZero() = {emptyStr}");
        Console.WriteLine($"   ✅ ((string?)null).GetLengthOrZero() = {nullLength}");

        // 简单扩展方法测试
        var prefixed = "world".AddPrefix("Hello ");
        Console.WriteLine($"   ✅ 'world'.AddPrefix('Hello ') = '{prefixed}'");

        Console.WriteLine();
    }

    private static void TestPerformance()
    {
        Console.WriteLine("📋 6. 性能测试");
        Console.WriteLine("   测试生成代码的性能表现...");

        var sw = Stopwatch.StartNew();

        // 简单计算性能测试
        var fib = PerformanceTestsEs.Fibonacci(10);
        sw.Stop();
        Console.WriteLine($"   ✅ PerformanceTests.Fibonacci(10) = {fib} (耗时: {sw.ElapsedMilliseconds}ms)");

        sw.Restart();
        // 数组创建性能测试
        var range = PerformanceTestsEs.CreateRange(1, 1000);
        sw.Stop();
        Console.WriteLine($"   ✅ PerformanceTests.CreateRange(1, 1000) 创建了 {range.Length} 个元素 (耗时: {sw.ElapsedMilliseconds}ms)");

        Console.WriteLine();
    }
}
