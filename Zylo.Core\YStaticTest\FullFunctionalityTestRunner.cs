using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace YStaticTest;

/// <summary>
/// YStatic v1.3 全功能测试运行器
/// 
/// 🎯 测试目标：
/// 验证 YStatic v1.3 的所有核心功能和特性
/// 
/// 📋 测试覆盖：
/// 1. 类级属性测试（YStatic/YStaticExtension）
/// 2. 方法级属性测试（YStatic/YStaticExtension）
/// 3. 混合属性测试
/// 4. 泛型方法测试
/// 5. 异步方法测试
/// 6. 扩展方法测试
/// 7. 边界情况测试
/// 8. 性能测试
/// 9. 错误处理测试
/// </summary>
public static class FullFunctionalityTestRunner
{
    private static int _testCount = 0;
    private static int _passedCount = 0;
    private static int _failedCount = 0;

    public static async Task RunAllTestsAsync()
    {
        Console.WriteLine("🚀 YStatic v1.3 全功能测试开始");
        Console.WriteLine(new string('=', 80));
        Console.WriteLine();

        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 🎯 测试组1：基础功能
            await TestGroup("基础功能测试", async () =>
            {
                TestClassLevelYStatic();
                TestClassLevelYStaticExtension();
                TestMethodLevelYStatic();
                TestMethodLevelYStaticExtension();
            });

            // 🎯 测试组2：泛型功能
            await TestGroup("泛型功能测试", async () =>
            {
                TestGenericMethods();
                TestGenericConstraints();
                TestGenericExtensionMethods();
            });

            // 🎯 测试组3：异步功能
            await TestGroup("异步功能测试", async () =>
            {
                await TestAsyncMethods();
                await TestAsyncGenericMethods();
            });

            // 🎯 测试组4：扩展方法
            await TestGroup("扩展方法测试", async () =>
            {
                TestExtensionMethodGeneration();
                TestExtensionMethodUsage();
            });

            // 🎯 测试组5：边界情况
            await TestGroup("边界情况测试", async () =>
            {
                TestNullHandling();
                TestComplexParameters();
                TestIgnoredMethods();
            });

            // 🎯 测试组6：性能测试
            await TestGroup("性能测试", async () =>
            {
                TestPerformance();
            });

            stopwatch.Stop();
            
            Console.WriteLine();
            Console.WriteLine("📊 测试结果统计");
            Console.WriteLine(new string('-', 40));
            Console.WriteLine($"总测试数: {_testCount}");
            Console.WriteLine($"通过: {_passedCount} ✅");
            Console.WriteLine($"失败: {_failedCount} ❌");
            Console.WriteLine($"成功率: {(_passedCount * 100.0 / _testCount):F1}%");
            Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            if (_failedCount == 0)
            {
                Console.WriteLine();
                Console.WriteLine("🎉 所有测试通过！YStatic v1.3 功能完整！");
            }
            else
            {
                Console.WriteLine();
                Console.WriteLine($"⚠️ 有 {_failedCount} 个测试失败，请检查相关功能");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试运行失败: {ex.Message}");
            Console.WriteLine($"📍 错误位置: {ex.StackTrace}");
        }
    }

    private static async Task TestGroup(string groupName, Func<Task> testAction)
    {
        Console.WriteLine($"📋 {groupName}");
        Console.WriteLine(new string('-', 40));
        
        try
        {
            await testAction();
        }
        catch (Exception ex)
        {
            TestFail($"测试组 '{groupName}' 执行失败", ex.Message);
        }
        
        Console.WriteLine();
    }

    #region 🎯 基础功能测试

    private static void TestClassLevelYStatic()
    {
        TestCase("类级 [YStatic] - SimpleCalculator.Add", () =>
        {
            var result = SimpleCalculatorEs.Add(10, 20);
            return result == 30;
        });

        TestCase("类级 [YStatic] - StringUtils.Reverse", () =>
        {
            var result = StringUtilsEs.Reverse("Hello");
            return result == "olleH";
        });
    }

    private static void TestClassLevelYStaticExtension()
    {
        TestCase("类级 [YStaticExtension] - 集合扩展方法", () =>
        {
            var items = new[] { 1, 2, 3, 4, 5 };
            var first = items.SafeFirst();
            var count = items.SafeCount();
            return first == 1 && count == 5;
        });
    }

    private static void TestMethodLevelYStatic()
    {
        TestCase("方法级 [YStatic] - AddNumbers", () =>
        {
            var result = MethodLevelTestsEs.AddNumbers(5, 3);
            return result == 8;
        });

        TestCase("方法级 [YStatic] - Square", () =>
        {
            var result = MethodLevelTestsEs.Square(4.0);
            return Math.Abs(result - 16.0) < 0.001;
        });
    }

    private static void TestMethodLevelYStaticExtension()
    {
        TestCase("方法级 [YStaticExtension] - ProcessText", () =>
        {
            var result = "hello world".ProcessText();
            return result.Contains("HELLO WORLD");
        });
    }

    #endregion

    #region 🎯 泛型功能测试

    private static void TestGenericMethods()
    {
        TestCase("泛型方法 - GetDefault<int>", () =>
        {
            var result = GenericHelperEs.GetDefault<int>();
            return result == 0;
        });

        TestCase("泛型方法 - GetDefault<string>", () =>
        {
            var result = GenericHelperEs.GetDefault<string>();
            return result == null;
        });
    }

    private static void TestGenericConstraints()
    {
        TestCase("泛型约束 - Max<int>", () =>
        {
            var result = GenericConstraintTestsEs.Max(10, 20);
            return result == 20;
        });

        TestCase("泛型约束 - Max<string>", () =>
        {
            var result = GenericConstraintTestsEs.Max("apple", "banana");
            return result == "banana"; // 字典序比较
        });
    }

    private static void TestGenericExtensionMethods()
    {
        TestCase("泛型扩展方法 - Transform", () =>
        {
            var result = "hello".Transform(s => s.ToUpper());
            return result == "HELLO";
        });
    }

    #endregion

    #region 🎯 异步功能测试

    private static async Task TestAsyncMethods()
    {
        await TestCaseAsync("异步方法 - FetchDataAsync", async () =>
        {
            var result = await AsyncTestsEs.FetchDataAsync("test-url");
            return result.Contains("test-url");
        });
    }

    private static async Task TestAsyncGenericMethods()
    {
        await TestCaseAsync("异步泛型方法 - ProcessAsync", async () =>
        {
            var result = await AsyncTestsEs.ProcessAsync("test", async s =>
            {
                await Task.Delay(1);
                return s.ToUpper();
            });
            return result == "TEST";
        });
    }

    #endregion

    #region 🎯 扩展方法测试

    private static void TestExtensionMethodGeneration()
    {
        TestCase("扩展方法生成 - 类级扩展", () =>
        {
            // 验证扩展方法可以直接在对象上调用
            var items = new[] { 1, 2, 3 };
            var result = items.SafeFirst();
            return result == 1;
        });
    }

    private static void TestExtensionMethodUsage()
    {
        TestCase("扩展方法使用 - 方法级扩展", () =>
        {
            var result = "test".ProcessText();
            return !string.IsNullOrEmpty(result);
        });
    }

    #endregion

    #region 🎯 边界情况测试

    private static void TestNullHandling()
    {
        TestCase("空值处理 - SafeToString", () =>
        {
            var result = ((object?)null).SafeToString();
            return result == "NULL";
        });

        TestCase("空值处理 - GetLengthOrZero", () =>
        {
            var result = ((string?)null).GetLengthOrZero();
            return result == 0;
        });
    }

    private static void TestComplexParameters()
    {
        TestCase("复杂参数 - AddPrefix", () =>
        {
            var result = "world".AddPrefix("Hello ");
            return result == "Hello world";
        });
    }

    private static void TestIgnoredMethods()
    {
        TestCase("忽略方法验证", () =>
        {
            // 验证被 [YStaticIgnore] 标记的方法没有生成
            // 这里只是验证其他方法正常工作
            var result = MixedAttributeTestsEs.Calculate(2, 3);
            return result == 16; // 2 * 3 + 10
        });
    }

    #endregion

    #region 🎯 性能测试

    private static void TestPerformance()
    {
        TestCase("性能测试 - Fibonacci", () =>
        {
            var sw = Stopwatch.StartNew();
            var result = PerformanceTestsEs.Fibonacci(10);
            sw.Stop();
            
            Console.WriteLine($"      Fibonacci(10) = {result}, 耗时: {sw.ElapsedMilliseconds}ms");
            return result == 55 && sw.ElapsedMilliseconds < 1000; // 应该很快
        });

        TestCase("性能测试 - CreateRange", () =>
        {
            var sw = Stopwatch.StartNew();
            var result = PerformanceTestsEs.CreateRange(1, 100);
            sw.Stop();
            
            Console.WriteLine($"      CreateRange(1, 100) 创建了 {result.Length} 个元素, 耗时: {sw.ElapsedMilliseconds}ms");
            return result.Length == 100 && result[0] == 1 && result[99] == 100;
        });
    }

    #endregion

    #region 🔧 测试辅助方法

    private static void TestCase(string testName, Func<bool> testAction)
    {
        _testCount++;
        try
        {
            var result = testAction();
            if (result)
            {
                _passedCount++;
                Console.WriteLine($"   ✅ {testName}");
            }
            else
            {
                _failedCount++;
                Console.WriteLine($"   ❌ {testName} - 断言失败");
            }
        }
        catch (Exception ex)
        {
            _failedCount++;
            Console.WriteLine($"   ❌ {testName} - 异常: {ex.Message}");
        }
    }

    private static async Task TestCaseAsync(string testName, Func<Task<bool>> testAction)
    {
        _testCount++;
        try
        {
            var result = await testAction();
            if (result)
            {
                _passedCount++;
                Console.WriteLine($"   ✅ {testName}");
            }
            else
            {
                _failedCount++;
                Console.WriteLine($"   ❌ {testName} - 断言失败");
            }
        }
        catch (Exception ex)
        {
            _failedCount++;
            Console.WriteLine($"   ❌ {testName} - 异常: {ex.Message}");
        }
    }

    private static void TestFail(string testName, string reason)
    {
        _testCount++;
        _failedCount++;
        Console.WriteLine($"   ❌ {testName} - {reason}");
    }

    #endregion
}
