using System;
using System.Collections.Generic;
using System.Text;

namespace YStaticTest;

/// <summary>
/// 参数处理调试工具
/// 用于测试和调试 YStatic v1.4 的参数处理逻辑
/// </summary>
public static class ParameterDebugger
{
    public static void DebugParameterProcessing()
    {
        Console.WriteLine("🔧 YStatic v1.4 参数处理调试");
        Console.WriteLine(new string('=', 50));

        // 重点测试参数分割问题
        var fullParams = "Dictionary<TKey,  TValue> dict,  string separator = \", \"";
        Console.WriteLine($"完整参数字符串: {fullParams}");

        // 测试参数分割
        var splitParams = SplitParametersSmartly(fullParams);
        Console.WriteLine($"分割结果数量: {splitParams.Count}");
        for (int i = 0; i < splitParams.Count; i++)
        {
            Console.WriteLine($"  参数 {i + 1}: '{splitParams[i]}'");
        }

        Console.WriteLine(new string('-', 30));

        // 测试每个参数的处理
        foreach (var param in splitParams)
        {
            var trimmed = param.Trim();
            Console.WriteLine($"处理参数: '{trimmed}'");

            var result = ExtractParameterCallSyntax(trimmed);
            Console.WriteLine($"提取结果: '{result}'");
            Console.WriteLine();
        }

        // 测试各种复杂参数情况
        TestParameterExtraction("in (double x, double y) point1", "应该提取: point1");
        TestParameterExtraction("in (double x, double y) point2", "应该提取: point2");
        TestParameterExtraction("string separator = \", \"", "应该提取: separator");
        TestParameterExtraction("params string[] values", "应该提取: values");
        TestParameterExtraction("ref int counter", "应该提取: counter");
        TestParameterExtraction("out string result", "应该提取: result");
        TestParameterExtraction("Func<T, TResult> transformer", "应该提取: transformer");
        TestParameterExtraction("Dictionary<string, int> dict", "应该提取: dict");
    }

    // 模拟 YStatic 的参数分割方法
    private static List<string> SplitParametersSmartly(string parametersString)
    {
        var parameters = new List<string>();
        var current = new StringBuilder();
        var bracketDepth = 0;
        var angleDepth = 0;
        var parenDepth = 0;
        var inString = false;
        var stringChar = '\0';
        var escapeNext = false;

        for (int i = 0; i < parametersString.Length; i++)
        {
            char c = parametersString[i];

            if (escapeNext)
            {
                escapeNext = false;
                current.Append(c);
                continue;
            }

            if (!inString)
            {
                switch (c)
                {
                    case '"':
                    case '\'':
                        inString = true;
                        stringChar = c;
                        current.Append(c);
                        break;
                    case '<':
                        angleDepth++;
                        current.Append(c);
                        break;
                    case '>':
                        angleDepth--;
                        current.Append(c);
                        break;
                    case '[':
                        bracketDepth++;
                        current.Append(c);
                        break;
                    case ']':
                        bracketDepth--;
                        current.Append(c);
                        break;
                    case '(':
                        parenDepth++;
                        current.Append(c);
                        break;
                    case ')':
                        parenDepth--;
                        current.Append(c);
                        break;
                    case ',':
                        if (bracketDepth == 0 && angleDepth == 0 && parenDepth == 0)
                        {
                            parameters.Add(current.ToString());
                            current.Clear();
                        }
                        else
                        {
                            current.Append(c);
                        }
                        break;
                    default:
                        current.Append(c);
                        break;
                }
            }
            else
            {
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
                current.Append(c);
            }
        }

        if (current.Length > 0)
        {
            parameters.Add(current.ToString());
        }

        return parameters;
    }

    // 模拟 YStatic 的参数调用语法提取
    private static string ExtractParameterCallSyntax(string parameterDefinition)
    {
        // 简化版本，只处理基本情况
        var trimmed = parameterDefinition.Trim();

        // 移除默认值
        var equalIndex = trimmed.IndexOf('=');
        if (equalIndex > 0)
        {
            trimmed = trimmed.Substring(0, equalIndex).Trim();
        }

        // 提取参数名
        var parts = trimmed.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length > 0)
        {
            return parts[parts.Length - 1];
        }

        return "";
    }

    // 模拟 YStatic 的方法（简化版本）
    private static string RemoveDefaultValueAdvanced(string parameterWithDefault)
    {
        if (string.IsNullOrEmpty(parameterWithDefault) || !parameterWithDefault.Contains('='))
        {
            return parameterWithDefault;
        }

        var inString = false;
        var stringChar = '\0';
        var bracketDepth = 0;
        var parenDepth = 0;
        var angleDepth = 0;
        var escapeNext = false;

        for (int i = 0; i < parameterWithDefault.Length; i++)
        {
            char c = parameterWithDefault[i];

            if (escapeNext)
            {
                escapeNext = false;
                continue;
            }

            if (!inString)
            {
                switch (c)
                {
                    case '"':
                    case '\'':
                        inString = true;
                        stringChar = c;
                        break;
                    case '[':
                        bracketDepth++;
                        break;
                    case ']':
                        bracketDepth--;
                        break;
                    case '(':
                        parenDepth++;
                        break;
                    case ')':
                        parenDepth--;
                        break;
                    case '<':
                        angleDepth++;
                        break;
                    case '>':
                        angleDepth--;
                        break;
                    case '=':
                        // 只有在不在字符串内，且不在任何括号内时，才认为是默认值分隔符
                        if (bracketDepth == 0 && parenDepth == 0 && angleDepth == 0)
                        {
                            return parameterWithDefault.Substring(0, i).Trim();
                        }
                        break;
                }
            }
            else
            {
                // 在字符串内
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
            }
        }

        // 如果没有找到有效的 = 分隔符，返回原字符串
        return parameterWithDefault;
    }

    private static string ExtractParameterNameFromSimpleDefinition(string definition)
    {
        var parts = definition.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // 从后往前找第一个有效的参数名
        for (int i = parts.Length - 1; i >= 0; i--)
        {
            var part = parts[i];

            // 跳过明显的类型符号
            if (part.Contains('<') || part.Contains('>') ||
                part.Contains('[') || part.Contains(']') ||
                part.Contains(','))
                continue;

            // 这应该是参数名
            return part;
        }

        return parts.Length > 0 ? parts[parts.Length - 1] : "";
    }

    private static void TestParameterExtraction(string input, string expected)
    {
        Console.WriteLine($"输入: {input}");
        Console.WriteLine($"期望: {expected}");

        // 这里我们需要调用 YStatic 的参数处理方法
        // 但由于这些方法是 private 的，我们需要通过反射或者创建测试版本

        Console.WriteLine($"实际: [需要实现测试逻辑]");
        Console.WriteLine(new string('-', 30));
    }
}
