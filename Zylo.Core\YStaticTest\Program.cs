using YStaticTest;

// 先运行参数处理调试
ParameterDebugger.DebugParameterProcessing();

Console.WriteLine("\n" + new string('=', 50));

await RunTestsAsync();

static async Task RunTestsAsync()
{
    Console.WriteLine("🚀 YStatic v1.3 功能测试开始");
    Console.WriteLine(new string('=', 60));
    Console.WriteLine();

    try
    {
        // 🎯 阶段1：编译验证
        Console.WriteLine("📋 阶段1：编译验证");
        Console.WriteLine("检查 YStatic 生成的代码是否能够正常编译...");

        // 如果程序能运行到这里，说明编译成功
        Console.WriteLine("✅ 编译验证通过！YStatic 生成的代码编译正常");
        Console.WriteLine();

        // 🎯 阶段2：基础功能测试
        Console.WriteLine("📋 阶段2：基础功能测试");
        Console.WriteLine("测试基础的类和方法定义...");


        // 测试原始类的实例化
        // var simpleMath = new SimpleMath();
        // var result1 = simpleMath.Add(10, 5);
        // Console.WriteLine($"✅ SimpleMath.Add(10, 5) = {result1}");
        //
        // var stringUtils = new StringUtils();
        // var result2 = stringUtils.Reverse("Hello");
        // Console.WriteLine($"✅ StringUtils.Reverse(\"Hello\") = \"{result2}\"");
        //
        // var genericHelper = new GenericHelper();
        // var result3 = genericHelper.AreEqual<int>(5, 5);
        // Console.WriteLine($"✅ GenericHelper.AreEqual<int>(5, 5) = {result3}");

        Console.WriteLine();

        // 🎯 阶段3：生成代码验证
        Console.WriteLine("📋 阶段3：生成代码验证");
        Console.WriteLine("运行全面的功能测试...");
        Console.WriteLine();

        // 运行 v1.4 专项测试
        Console.WriteLine("🆕 YStatic v1.4 专项测试:");
        V14TestRunner.RunV14Tests();

        Console.WriteLine();
        Console.WriteLine(new string('-', 60));
        Console.WriteLine();

        // 运行基础功能测试
        Console.WriteLine("🔧 基础功能快速验证:");
        TestRunner.RunAllTests();

        Console.WriteLine();
        Console.WriteLine(new string('=', 80));
        Console.WriteLine();

        // 运行全功能测试
        await FullFunctionalityTestRunner.RunAllTestsAsync();



    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ 测试过程中发生错误：{ex.Message}");
        Console.WriteLine($"📍 错误位置：{ex.StackTrace}");
    }

    Console.WriteLine();
    Console.WriteLine("按任意键退出...");
    Console.ReadKey();
}
