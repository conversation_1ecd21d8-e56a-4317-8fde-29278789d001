# Zylo.Service

> 🚀 **企业级 C# 源代码生成器** - 自动生成接口和依赖注入代码的强大工具

[![NuGet](https://img.shields.io/nuget/v/Zylo.Service.svg)](https://www.nuget.org/packages/Zylo.Service/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![.NET](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)](https://dotnet.microsoft.com/)

## 📋 目录

- [概述](#概述)
- [核心特性](#核心特性)
- [快速开始](#快速开始)
- [使用模式](#使用模式)
- [项目架构](#项目架构)
- [开发指南](#开发指南)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🎯 概述

`Zylo.Service` 是一个基于 Roslyn 的现代化 C# 源代码生成器，专为企业级应用设计。它采用先进的分层架构，提供高度可扩展的代码生成能力，自动为您的服务类生成接口定义和依赖注入配置代码。

### 🌟 核心价值

- **🔥 零样板代码**：自动生成接口和 DI 配置，告别重复劳动
- **⚡ 编译时生成**：无运行时开销，性能最优
- **🛡️ 类型安全**：完全类型安全的代码生成
- **🔧 智能感知**：生成的代码完全支持 IntelliSense
- **📝 文档保留**：自动保留和转换 XML 文档注释
- **🏗️ 可扩展架构**：为 Zylo 框架的其他功能提供基础设施

### 🎨 设计理念

- **分层架构**：Helper(通用) → Processors(专用) → Generators(协调) → Temple(生成)
- **职责分离**：每个组件都有明确的职责边界
- **高度可复用**：通用工具可被任何功能使用
- **易于扩展**：新功能只需按模板创建对应的处理器

## ✨ 核心特性

### 🎯 双模式代码生成

#### 📋 类级模式

- ✅ 使用 `[YService]` 标记整个类
- ✅ 所有公共方法自动包含在接口中
- ✅ 统一的生命周期管理
- ✅ 适合传统的服务类设计

#### 🔧 方法级模式

- ✅ 使用 `[YServiceScoped]`、`[YServiceSingleton]`、`[YServiceTransient]` 标记特定方法
- ✅ 细粒度控制，只有标记的方法包含在接口中
- ✅ 支持混合生命周期
- ✅ 适合渐进式迁移和复杂场景

### 🏗️ 自动接口生成

- ✅ 从服务类自动生成对应的接口
- ✅ 保留完整的 XML 文档注释
- ✅ 支持泛型方法和复杂类型约束
- ✅ 支持默认参数和可选参数
- ✅ 支持异步方法 (Task/ValueTask)
- ✅ 智能排除不适合的方法（构造函数、私有方法等）

### 🔌 依赖注入集成

- ✅ 自动生成服务注册扩展方法
- ✅ 支持 Singleton、Scoped、Transient 生命周期
- ✅ 批量注册和单个注册方法
- ✅ 与 Microsoft.Extensions.DependencyInjection 完美集成
- ✅ 支持静态类的包装器生成

### 🎨 高级功能

- ✅ 为静态类生成包装器，支持依赖注入
- ✅ 保持原有方法签名和文档
- ✅ 无缝集成到 DI 容器

### 🎨 智能代码生成

- ✅ 智能识别公共方法和属性
- ✅ 支持方法忽略 (`[YServiceIgnore]`)
- ✅ 自动处理复杂类型和泛型
- ✅ 生成高质量、可读性强的代码

## 🚀 快速开始

### 1. 安装包

```xml
<PackageReference Include="Zylo.Service" Version="1.0.0" />
```

或使用 .NET CLI：

```bash
dotnet add package Zylo.Service
```

### 2. 标记您的服务类

```csharp
using Zylo.Service.Attributes;

/// <summary>
/// 用户服务 - 处理用户相关的业务逻辑
/// </summary>
[YService]
public class UserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    public User GetUser(int userId)
    {
        return new User { Id = userId, Name = $"User_{userId}" };
    }

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="name">用户名</param>
    /// <param name="email">邮箱地址</param>
    /// <returns>创建的用户</returns>
    public User CreateUser(string name, string email)
    {
        return new User { Name = name, Email = email };
    }
}
```

### 3. 自动生成的代码

编译后，Zylo.Service 会自动生成：

**IUserService.yg.cs**

```csharp
/// <summary>
/// 用户服务 - 处理用户相关的业务逻辑 的接口定义
/// </summary>
public partial interface IUserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    User GetUser(int userId);

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="name">用户名</param>
    /// <param name="email">邮箱地址</param>
    /// <returns>创建的用户</returns>
    User CreateUser(string name, string email);
}

/// <summary>
/// UserService 的接口实现部分
/// </summary>
public partial class UserService : IUserService
{
    // 🔧 具体实现将通过用户定义的 partial 类提供
}
```

**ServiceRegistration.YourAssembly.yg.cs**

```csharp
public static class ServiceRegistrationExtensions
{
    /// <summary>
    /// 注册所有 YService 标记的服务
    /// </summary>
    public static IServiceCollection AddAllYourAssemblyServices(this IServiceCollection services)
    {
        services.AddScoped<IUserService, UserService>();
        // ... 其他服务
        return services;
    }

    /// <summary>
    /// 注册 UserService
    /// </summary>
    public static IServiceCollection AddUserService(this IServiceCollection services)
    {
        return services.AddScoped<IUserService, UserService>();
    }
}
```

### 4. 在 Startup 中注册服务

```csharp
public void ConfigureServices(IServiceCollection services)
{
    // 一键注册所有服务
    services.AddAllYourAssemblyServices();

    // 或者单独注册
    // services.AddUserService();
}
```

### 5. 使用依赖注入

```csharp
public class UserController : ControllerBase
{
    private readonly IUserService _userService;

    public UserController(IUserService userService)
    {
        _userService = userService;
    }

    [HttpGet("{id}")]
    public User GetUser(int id)
    {
        return _userService.GetUser(id);
    }
}
```

## 🎨 使用模式

### 📋 类级模式 - 传统服务设计

适合传统的服务类设计，整个类作为一个服务单元：

```csharp
[YService(ServiceLifetime.Scoped)]
public partial class OrderService
{
    /// <summary>
    /// 创建订单
    /// </summary>
    public async Task<Order> CreateOrderAsync(CreateOrderRequest request)
    {
        // 业务逻辑
    }

    /// <summary>
    /// 获取订单详情
    /// </summary>
    public async Task<Order> GetOrderAsync(int orderId)
    {
        // 业务逻辑
    }

    /// <summary>
    /// 内部辅助方法，不会包含在接口中
    /// </summary>
    [YServiceIgnore]
    private void LogOperation(string operation)
    {
        // 日志记录
    }
}
```

### 🔧 方法级模式 - 细粒度控制

适合复杂场景和渐进式迁移：

```csharp
public partial class DataProcessor
{
    /// <summary>
    /// 处理用户数据 - Scoped 生命周期
    /// </summary>
    [YServiceScoped]
    public async Task<UserData> ProcessUserDataAsync(int userId)
    {
        // 业务逻辑
    }

    /// <summary>
    /// 获取配置信息 - Singleton 生命周期
    /// </summary>
    [YServiceSingleton]
    public Configuration GetConfiguration()
    {
        // 配置逻辑
    }

    /// <summary>
    /// 生成临时令牌 - Transient 生命周期
    /// </summary>
    [YServiceTransient]
    public string GenerateToken()
    {
        // 令牌生成
    }

    /// <summary>
    /// 普通方法，不会包含在接口中
    /// </summary>
    public void RegularMethod()
    {
        // 普通逻辑
    }
}
```

### 🏗️ 静态类包装

为静态类生成包装器，支持依赖注入：

```csharp
[YService]
public static class MathUtilities
{
    /// <summary>
    /// 计算两个数的和
    /// </summary>
    public static int Add(int a, int b) => a + b;

    /// <summary>
    /// 计算两个数的乘积
    /// </summary>
    public static int Multiply(int a, int b) => a * b;
}
```

生成的包装器：

```csharp
public interface IMathUtilities
{
    int Add(int a, int b);
    int Multiply(int a, int b);
}

public class MathUtilitiesWrapper : IMathUtilities
{
    public int Add(int a, int b) => MathUtilities.Add(a, b);
    public int Multiply(int a, int b) => MathUtilities.Multiply(a, b);
}
```

## 📚 详细使用指南

### 🏷️ 属性标记

#### `[YService]` - 服务标记

标记一个类为服务，启用自动代码生成：

```csharp
[YService]                          // 默认 Scoped 生命周期
[YService(ServiceLifetime.Singleton)]  // 指定生命周期
[YService(ServiceLifetime.Transient)]  // 瞬态生命周期
public class MyService { }
```

#### `[YServiceIgnore]` - 方法忽略

排除特定方法不生成到接口中：

```csharp
[YService]
public class UserService
{
    public User GetUser(int id) { } // ✅ 会生成到接口

    [YServiceIgnore]
    private void InternalMethod() { } // ❌ 不会生成到接口
}
```

### 🔧 静态类支持

对于静态类，Zylo.Service 会生成包装器类：

```csharp
/// <summary>
/// 数学计算工具类
/// </summary>
[YService]
public static class MathService
{
    /// <summary>
    /// 计算两个数的和
    /// </summary>
    public static int Add(int a, int b) => a + b;
}
```

生成的代码：

```csharp
public partial interface IMathService
{
    int Add(int a, int b);
}

public class MathServiceWrapper : IMathService
{
    public int Add(int a, int b) => MathService.Add(a, b);
}
```

### 🎯 支持的生命周期

- `[YService]` - 默认 Scoped
- `[YService(ServiceLifetime.Scoped)]` - 作用域
- `[YService(ServiceLifetime.Singleton)]` - 单例
- `[YService(ServiceLifetime.Transient)]` - 瞬态

## 🚀 高级功能

### 泛型方法支持

```csharp
[YService]
public class GenericService
{
    public T Process<T>(T input) where T : class
    {
        return input;
    }
}
```

### 默认参数支持

```csharp
[YService]
public class ConfigService
{
    public string GetConfig(string key, string defaultValue = "default")
    {
        return defaultValue;
    }
}
```

### 异步方法支持

```csharp
[YService]
public class AsyncService
{
    public async Task<User> GetUserAsync(int id, CancellationToken cancellationToken = default)
    {
        // 异步逻辑
        return await SomeAsyncOperation(id, cancellationToken);
    }
}
```

## 🏗️ 项目架构

```text
📁 Zylo.Service/ (现代化分层架构)
├── 📁 Helper/ (通用工具层)
│   ├── SyntaxAnalysisHelper.cs      # 通用语法分析 + 属性检测
│   ├── MethodSignatureHelper.cs     # 通用方法签名处理
│   ├── XmlDocumentationExtractor.cs # 通用文档提取
│   ├── DocumentationProcessor.cs    # 通用文档格式化
│   ├── DocumentationTemplateGenerator.cs # 文档模板生成
│   ├── CodeIndentFormatter.cs       # 通用代码格式化
│   └── README.md                    # 通用工具文档
├── 📁 Processors/ (业务处理层)
│   ├── YServiceClassProcessor.cs    # 类级处理器
│   ├── YServiceMethodProcessor.cs   # 方法级处理器
│   └── README.md                    # 处理器架构文档
├── 📁 Models/ (数据模型层)
│   ├── YServiceModels.cs            # 统一数据模型
│   └── README.md                    # 数据模型文档
├── 📁 Generators/ (生成器层)
│   ├── YServiceGenerator.cs         # 协调者
│   └── README.md                    # 生成器文档
├── 📁 Temple/ (代码生成层)
│   ├── YServiceCodeGenerator.cs     # 代码生成器
│   └── README.md                    # 代码生成文档
├── 📁 Attributes/ (属性定义)
│   ├── YServiceAttribute.cs
│   ├── YServiceScopedAttribute.cs
│   ├── YServiceSingletonAttribute.cs
│   ├── YServiceTransientAttribute.cs
│   └── YServiceIgnoreAttribute.cs
├── 📁 Documentation/ (架构文档)
│   └── YService架构设计文档.md      # 完整架构设计
└── 📁 build/ (构建配置)
    ├── Zylo.Service.props
    └── Zylo.Service.targets
```

### 🎯 架构设计原则

#### 1. **分层架构**

- **Helper 层**：真正通用的工具，可被任何功能复用
- **Processors 层**：业务专用处理器，包含特定功能的业务逻辑
- **Models 层**：统一的数据模型，确保类型安全
- **Generators 层**：协调者，管理整个生成流程
- **Temple 层**：代码生成器，专注于代码输出

#### 2. **职责分离**

```text
🎯 单一职责：每个组件只负责一个明确的功能
🔧 开放封闭：对扩展开放，对修改封闭
🏗️ 依赖倒置：高层模块不依赖低层模块
📋 接口隔离：使用小而专用的接口
```

### 🔧 核心组件详解

#### 🎯 YServiceGenerator (协调者)

- **职责**：协调整个代码生成流程
- **特点**：不包含业务逻辑，只负责分发和协调
- **模式**：协调者模式，管道管理

#### 🔧 处理器架构

- **YServiceClassProcessor**：处理类级 YService 属性
- **YServiceMethodProcessor**：处理方法级 YService 属性
- **特点**：业务专用，包含 YService 特有逻辑

#### 🛠️ 通用工具集

- **SyntaxAnalysisHelper**：通用语法分析和属性检测
- **MethodSignatureHelper**：通用方法签名处理
- **XmlDocumentationExtractor**：专业文档注释提取
- **CodeIndentFormatter**：代码格式化完整解决方案

## 🔗 开发指南

### 📚 详细文档

- **[Helper/README.md](Helper/README.md)** - 通用工具使用指南
- **[Processors/README.md](Processors/README.md)** - 处理器架构文档
- **[Models/README.md](Models/README.md)** - 数据模型文档
- **[Generators/README.md](Generators/README.md)** - 生成器文档
- **[Documentation/YService架构设计文档.md](Documentation/YService架构设计文档.md)** - 完整架构设计

### 🚀 扩展指南

为新功能创建处理器（以 YController 为例）：

```csharp
// 1. 复用通用工具
using Zylo.Service.Helper;

// 2. 实现专用处理器
public static class YControllerClassProcessor
{
    public static YControllerInfo ProcessClassLevel(...)
    {
        // 使用通用工具
        var parameters = MethodSignatureHelper.GetParametersString(methodSymbol);
        var isIgnored = SyntaxAnalysisHelper.HasMethodAttribute(method, "YControllerIgnore");
        // ...
    }
}

// 3. 实现协调者
public class YControllerGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 委托给处理器
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: YControllerMethodProcessor.IsYControllerRelatedCandidate,
            transform: GetYControllerInfo);
    }
}
```

## 💡 最佳实践

### 1. 命名约定

```csharp
// ✅ 推荐的命名方式
[YService]
public partial class UserService { }        // 服务类使用 Service 后缀
// 生成：IUserService                        // 接口自动添加 I 前缀

[YService]
public partial class OrderProcessor { }     // 处理器类使用 Processor 后缀
// 生成：IOrderProcessor

// ✅ 自定义接口前缀
[YService(InterfacePrefix = "ICustom")]
public partial class DataService { }
// 生成：ICustomDataService
```

### 2. 文档注释最佳实践

```csharp
/// <summary>
/// 用户管理服务 - 提供用户相关的业务操作
/// </summary>
/// <remarks>
/// 这个服务负责处理用户的创建、查询、更新和删除操作
/// </remarks>
[YService(ServiceLifetime.Scoped)]
public partial class UserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="userId">用户的唯一标识符</param>
    /// <param name="includeDeleted">是否包含已删除的用户，默认为 false</param>
    /// <returns>
    /// 返回用户信息，如果用户不存在则返回 null
    /// </returns>
    /// <exception cref="ArgumentException">当 userId 小于等于 0 时抛出</exception>
    public async Task<User?> GetUserAsync(int userId, bool includeDeleted = false)
    {
        // 实现逻辑
    }
}
```

### 3. 生命周期选择指南

```csharp
// ✅ Singleton - 无状态服务、配置服务、工具类
[YServiceSingleton]
public partial class ConfigurationService
{
    public string GetConnectionString() => _config["ConnectionString"];
}

// ✅ Scoped - 业务服务、数据访问层（推荐默认）
[YServiceScoped]
public partial class UserService
{
    public async Task<User> CreateUserAsync(User user) { }
}

// ✅ Transient - 轻量级计算服务、无状态处理器
[YServiceTransient]
public partial class PasswordHasher
{
    public string HashPassword(string password) { }
}
```

### 4. 方法设计原则

```csharp
// ✅ 好的方法设计
public async Task<User?> GetUserByEmailAsync(
    string email,                           // 明确的参数名
    bool includeInactive = false,          // 合理的默认值
    CancellationToken cancellationToken = default)  // 支持取消
{
    // 实现逻辑
}

// ✅ 泛型方法支持
public async Task<T> ProcessAsync<T>(T input, ProcessOptions options = null)
    where T : class, IProcessable
{
    // 实现逻辑
}

// ✅ 排除不需要的方法
[YServiceIgnore]
private void InternalHelper() { }  // 私有方法自动排除

[YServiceIgnore]
public void UtilityMethod() { }    // 手动排除公共方法
```

### 5. 错误处理和验证

```csharp
[YService]
public partial class UserService
{
    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="request">用户创建请求</param>
    /// <returns>创建的用户信息</returns>
    /// <exception cref="ArgumentNullException">当 request 为 null 时抛出</exception>
    /// <exception cref="ValidationException">当用户数据验证失败时抛出</exception>
    /// <exception cref="DuplicateEmailException">当邮箱已存在时抛出</exception>
    public async Task<User> CreateUserAsync(CreateUserRequest request)
    {
        // 参数验证
        ArgumentNullException.ThrowIfNull(request);

        // 业务验证
        if (await _repository.ExistsByEmailAsync(request.Email))
            throw new DuplicateEmailException(request.Email);

        // 业务逻辑
        var user = new User(request.Email, request.Name);
        return await _repository.CreateAsync(user);
    }
}
```

## 🔧 故障排除

### 常见问题

#### Q: 生成器没有运行，没有生成代码？

**A: 检查以下几点：**

1. **确保类是 partial 的**：

```csharp
// ❌ 错误：缺少 partial 关键字
[YService]
public class UserService { }

// ✅ 正确：包含 partial 关键字
[YService]
public partial class UserService { }
```

2. **检查 NuGet 包引用**：

```xml
<PackageReference Include="Zylo.Service" Version="1.0.0">
  <PrivateAssets>all</PrivateAssets>
  <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
</PackageReference>
```

3. **清理和重新构建**：

```bash
dotnet clean
dotnet build
```

#### Q: 生成的接口缺少某些方法？

**A: 检查方法是否符合条件：**

```csharp
// ✅ 会包含在接口中
public async Task<User> GetUserAsync(int id) { }

// ❌ 不会包含：非公共方法
private void HelperMethod() { }

// ❌ 不会包含：被忽略的方法
[YServiceIgnore]
public void UtilityMethod() { }

// ❌ 不会包含：构造函数
public UserService() { }
```

#### Q: 编译错误：找不到生成的接口？

**A: 检查命名空间和引用：**

```csharp
// 确保在正确的命名空间中
namespace MyApp.Services
{
    [YService]
    public partial class UserService { }

    // 生成的接口在同一命名空间：MyApp.Services.IUserService
}

// 在其他地方使用时确保引用正确的命名空间
using MyApp.Services;

public class Controller
{
    private readonly IUserService _userService;  // 确保能找到接口
}
```

#### Q: 方法级属性不生效？

**A: 检查冲突和条件：**

```csharp
// ❌ 冲突：类级和方法级属性同时存在
[YService]  // 类级属性会覆盖方法级属性
public partial class UserService
{
    [YServiceScoped]  // 这个不会生效
    public void Method() { }
}

// ✅ 正确：只使用方法级属性
public partial class UserService  // 没有类级属性
{
    [YServiceScoped]  // 这个会生效
    public void Method() { }
}
```

### 调试技巧

#### 1. 查看生成的代码

在 Visual Studio 中：

1. 右键项目 → "显示所有文件"
2. 展开 "Dependencies" → "Analyzers" → "Zylo.Service"
3. 查看生成的 `.yg.cs` 文件

#### 2. 启用详细构建日志

```bash
dotnet build --verbosity detailed
```

#### 3. 检查生成器诊断

```csharp
// 在生成器中添加诊断信息（开发时）
#if DEBUG
    Console.WriteLine($"Processing class: {className}");
#endif
```

### 性能问题

#### Q: 编译速度变慢？

**A: 优化建议：**

1. **减少候选类数量**：

```csharp
// ✅ 只在需要的类上添加属性
[YService]
public partial class UserService { }  // 只有这个类会被处理

// ❌ 避免在不需要的类上添加属性
```

2. **使用增量编译**：

```xml
<!-- 在项目文件中启用 -->
<PropertyGroup>
  <UseIncrementalCompilation>true</UseIncrementalCompilation>
</PropertyGroup>
```

3. **合理使用方法级属性**：

```csharp
// ✅ 优先使用类级属性（性能更好）
[YService]
public partial class UserService { }

// 🔧 只在需要细粒度控制时使用方法级属性
public partial class MixedService
{
    [YServiceScoped] public void Method1() { }
    [YServiceSingleton] public void Method2() { }
}
```

## 📞 支持与反馈

### 获取帮助

- 📖 **详细文档**：查看各个目录下的 README.md 文件
- 🏗️ **架构设计**：[Documentation/YService架构设计文档.md](Documentation/YService架构设计文档.md)
- 🛠️ **通用工具**：[Helper/README.md](Helper/README.md)

### 报告问题

如果遇到问题，请提供以下信息：

1. **环境信息**：
   - .NET 版本
   - Zylo.Service 版本
   - IDE 版本

2. **重现步骤**：
   - 最小化的代码示例
   - 期望的行为
   - 实际的行为

3. **错误信息**：
   - 编译错误信息
   - 生成器诊断信息

### 贡献指南

欢迎贡献代码和改进建议！请遵循以下原则：

- 保持架构的分层设计
- 遵循现有的代码风格
- 添加完整的文档注释
- 提供单元测试

---

## 🎯 总结

Zylo.Service 是一个现代化、高性能的 C# 源代码生成器，采用先进的分层架构设计：

- **🏗️ 分层清晰**：Helper(通用) → Processors(专用) → Generators(协调) → Temple(生成)
- **🔧 高度可复用**：通用工具可被任何功能使用
- **⚡ 性能优异**：增量编译，只处理变化的代码
- **📝 文档完整**：自动保留和转换 XML 文档注释
- **🚀 易于扩展**：为 Zylo 框架的其他功能提供基础设施

通过使用 Zylo.Service，您可以显著减少样板代码，提高开发效率，同时保持代码的类型安全和可维护性。

> 💡 **开始使用**：只需添加 `[YService]` 属性到您的 partial 类，Zylo.Service 就会自动为您生成接口和依赖注入代码！

#### Q: 生成的代码没有出现？

A: 确保：

1. 项目已成功编译
2. 类标记了 `[YService]` 属性
3. 类是 `public` 的
4. 重新构建项目

#### Q: 某些方法没有生成到接口？

A: 检查：

1. 方法是否为 `public`
2. 方法是否标记了 `[YServiceIgnore]`
3. 方法是否为构造函数或析构函数

#### Q: 静态类包装器不工作？

A: 确保：

1. 静态类标记了 `[YService]`
2. 静态方法是 `public` 的
3. 在 DI 容器中注册了包装器类

### 调试技巧

1. **查看生成的文件**：在 `obj/Generated` 目录下查看生成的代码
2. **检查编译输出**：查看编译器是否有相关警告或错误
3. **清理重建**：使用 `dotnet clean` 和 `dotnet build` 重新生成

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

## 📞 支持

- 🐛 [报告问题](https://github.com/your-repo/Zylo.Service/issues)
- 💡 [功能请求](https://github.com/your-repo/Zylo.Service/issues)
- 📖 [文档](https://github.com/your-repo/Zylo.Service/wiki)

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
