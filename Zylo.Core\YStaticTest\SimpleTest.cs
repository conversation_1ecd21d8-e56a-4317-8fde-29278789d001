using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#region v1.3 升级 - 简单测试类

/// <summary>
/// 最简单的 YStatic 测试类
/// 用于验证代码生成器是否正常工作
/// </summary>
[YStatic]
public partial class SimpleCalculator
{
    /// <summary>
    /// 简单加法方法
    /// </summary>
    /// <param name="a"></param>
    /// <param name="b"></param>
    /// <returns></returns>
    public int Add(int a, int b)
    {
        return a + b;
    }
}

/// <summary>
/// 最简单的 YStatic 测试类
/// </summary>
[YStaticExtension]

public partial class SimpleStringHelper
{
    /// <summary>
    /// 反转字符串
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public string Reverse(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        char[] chars = input.ToCharArray();
        Array.Reverse(chars);
        return new string(chars);
    }
}

#endregion