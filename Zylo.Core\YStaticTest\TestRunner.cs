using System;

namespace YStaticTest;

/// <summary>
/// 测试运行器 - 验证生成的静态标签是否正常工作
/// </summary>
public static class TestRunner
{
    public static void RunAllTests()
    {
        Console.WriteLine("🚀 开始测试 YStatic v1.3 生成的代码...\n");

        // 测试类级属性生成的静态方法
        TestClassLevelStatic();

        // 测试方法级属性生成的静态方法
        TestMethodLevelStatic();

        // 测试扩展方法
        TestExtensionMethods();

        Console.WriteLine("\n✅ 所有测试完成！");
    }

    private static void TestClassLevelStatic()
    {
        Console.WriteLine("📋 测试类级属性生成的静态方法:");

        // 测试 SimpleCalculator
        var sum = SimpleCalculatorEs.Add(10, 20);
        Console.WriteLine($"  SimpleCalculator.Add(10, 20) = {sum}");

        // 测试 StringUtils
        var reversed = StringUtilsEs.Reverse("Hello");
        Console.WriteLine($"  StringUtils.Reverse('Hello') = '{reversed}'");

        var upper = StringUtilsEs.ToUpperCase("world");
        Console.WriteLine($"  StringUtils.ToUpperCase('world') = '{upper}'");

        var length = StringUtilsEs.SafeLength("test");
        Console.WriteLine($"  StringUtils.SafeLength('test') = {length}");

        Console.WriteLine();
    }

    private static void TestMethodLevelStatic()
    {
        Console.WriteLine("🎯 测试方法级属性生成的静态方法:");

        // 测试方法级 [YStatic] 属性
        var sum = MethodLevelTestsEs.AddNumbers(5, 3);
        Console.WriteLine($"  MethodLevelTests.AddNumbers(5, 3) = {sum}");

        var square = MethodLevelTestsEs.Square(4.5);
        Console.WriteLine($"  MethodLevelTests.Square(4.5) = {square}");

        Console.WriteLine();
    }

    private static void TestExtensionMethods()
    {
        Console.WriteLine("🔧 测试扩展方法:");

        // 测试方法级 [YStaticExtension] 属性生成的扩展方法
        var processed = "hello world".ProcessText();
        Console.WriteLine($"  'hello world'.ProcessText() = '{processed}'");

        // 测试类级 [YStaticExtension] 属性生成的扩展方法
        var items = new[] { 1, 2, 3, 4, 5 };
        var first = items.SafeFirst();
        Console.WriteLine($"  [1,2,3,4,5].SafeFirst() = {first}");

        var count = items.SafeCount();
        Console.WriteLine($"  [1,2,3,4,5].SafeCount() = {count}");

        Console.WriteLine();
    }
}
