using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#region v1.4 升级 - 全面参数测试

/// <summary>
/// YStatic v1.4 全面参数处理测试
/// 测试各种边界情况和复杂参数组合
/// </summary>

// 测试1：in 参数（只读引用）
[YStatic]
public partial class InParameterTests
{
    /// <summary>
    /// in 参数测试 - 只读引用传递
    /// </summary>
    public double CalculateDistance(in (double x, double y) point1, in (double x, double y) point2)
    {
        double dx = point1.x - point2.x;
        double dy = point1.y - point2.y;
        return Math.Sqrt(dx * dx + dy * dy);
    }

    /// <summary>
    /// in 参数测试 - 大结构体只读传递
    /// </summary>
    public string ProcessLargeStruct(in DateTime timestamp, in TimeSpan duration)
    {
        return $"Event at {timestamp:yyyy-MM-dd HH:mm:ss}, duration: {duration.TotalMinutes:F1} minutes";
    }
}

// 测试2：默认参数值
[YStaticExtension]
public partial class DefaultParameterTests
{
    /// <summary>
    /// 复杂默认参数测试
    /// </summary>
    public string CreateMessage(string text, string prefix = "Info", int priority = 1, bool includeTimestamp = true, string? suffix = null)
    {
        var timestamp = includeTimestamp ? $"[{DateTime.Now:HH:mm:ss}] " : "";
        var result = $"{timestamp}[{prefix}:{priority}] {text}";
        return suffix != null ? result + suffix : result;
    }

    /// <summary>
    /// 可空类型默认参数
    /// </summary>
    public string ProcessText(string input, string? prefix = null, int? maxLength = null)
    {
        var result = prefix != null ? prefix + input : input;
        return maxLength.HasValue && result.Length > maxLength.Value
            ? result.Substring(0, maxLength.Value)
            : result;
    }
}

// 测试3：复杂泛型组合
[YStatic]
public partial class ComplexGenericTests
{
    /// <summary>
    /// 多重泛型约束测试
    /// </summary>
    public TResult ProcessWithConstraints<T, TResult>(T input, Func<T, TResult> processor)
        where T : class, IComparable<T>
        where TResult : IComparable<TResult>
    {
        return processor(input);
    }

    /// <summary>
    /// 嵌套泛型集合测试
    /// </summary>
    public Dictionary<TKey, List<TValue>> GroupItems<TKey, TValue>(
        IEnumerable<TValue> items,
        Func<TValue, TKey> keySelector,
        IEqualityComparer<TKey>? comparer = null)
        where TKey : notnull
    {
        var result = new Dictionary<TKey, List<TValue>>(comparer ?? EqualityComparer<TKey>.Default);
        foreach (var item in items)
        {
            var key = keySelector(item);
            if (!result.ContainsKey(key))
                result[key] = new List<TValue>();
            result[key].Add(item);
        }
        return result;
    }
}

// 测试4：数组和集合参数
[YStaticExtension]
public partial class ArrayCollectionTests
{
    /// <summary>
    /// 多维数组参数测试
    /// </summary>
    public int SumMatrix(int[,] matrix)
    {
        int sum = 0;
        for (int i = 0; i < matrix.GetLength(0); i++)
        {
            for (int j = 0; j < matrix.GetLength(1); j++)
            {
                sum += matrix[i, j];
            }
        }
        return sum;
    }

    /// <summary>
    /// 锯齿数组参数测试
    /// </summary>
    public int CountElements(int[][] jaggedArray)
    {
        int count = 0;
        foreach (var array in jaggedArray)
        {
            count += array.Length;
        }
        return count;
    }

    /// <summary>
    /// 复杂集合参数测试
    /// </summary>
    public string JoinDictionary<TKey, TValue>(Dictionary<TKey, TValue> dict, string separator = ", ")
        where TKey : notnull
    {
        return string.Join(separator, dict.Select(kvp => $"{kvp.Key}={kvp.Value}"));
    }
}

#region v1.4 升级 - 复杂字符串默认值测试

/// <summary>
/// 测试包含各种特殊字符的字符串默认值
/// </summary>
[YStatic]
public static class ComplexStringTests
{
    /// <summary>
    /// 测试包含逗号的字符串
    /// </summary>
    public static string FormatWithComma(string text, string separator = ", ")
    {
        return $"Result: {text}{separator}End";
    }

    /// <summary>
    /// 测试包含分号的字符串
    /// </summary>
    public static string FormatWithSemicolon(string text, string delimiter = "; ")
    {
        return $"Items: {text}{delimiter}Done";
    }

    /// <summary>
    /// 测试包含转义字符的字符串
    /// </summary>
    public static string FormatPath(string filename, string basePath = "C:\\temp\\")
    {
        return basePath + filename;
    }

    /// <summary>
    /// 测试包含引号的字符串
    /// </summary>
    public static string FormatQuoted(string text, string wrapper = "\"")
    {
        return wrapper + text + wrapper;
    }

    /// <summary>
    /// 测试包含多种特殊字符的字符串
    /// </summary>
    public static string FormatComplex(string text, string template = "Result: \"{0}\", Status: OK; ")
    {
        return string.Format(template, text);
    }

    /// <summary>
    /// 测试单引号字符串
    /// </summary>
    public static string FormatWithChar(string text, char delimiter = ',')
    {
        return text + delimiter;
    }
}

#endregion

// 测试5：委托和事件参数
[YStatic]
public partial class DelegateTests
{
    /// <summary>
    /// 复杂委托参数测试
    /// </summary>
    public TResult ProcessWithMultipleDelegates<T, TResult>(
        T input,
        Func<T, bool> validator,
        Func<T, T> transformer,
        Func<T, TResult> converter,
        Action<TResult>? onComplete = null)
    {
        if (!validator(input))
            throw new ArgumentException("Input validation failed");

        var transformed = transformer(input);
        var result = converter(transformed);
        onComplete?.Invoke(result);
        return result;
    }

    /// <summary>
    /// 异步委托参数测试
    /// </summary>
    public async Task<TResult> ProcessAsync<T, TResult>(
        T input,
        Func<T, Task<TResult>> asyncProcessor,
        CancellationToken cancellationToken = default)
    {
        return await asyncProcessor(input);
    }
}

#endregion
