using System;
using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#region v1.4 升级 - params 参数测试

/// <summary>
/// YStatic v1.4 params 参数专项测试
/// </summary>
[YStaticExtension]
public partial class ParamsTests
{
    /// <summary>
    /// 简单的 params 参数测试
    /// </summary>
    public string JoinStrings(string separator, params string[] values)
    {
        return string.Join(separator, values);
    }

    /// <summary>
    /// params 对象数组测试
    /// </summary>
    public string FormatMessage(string template, params object[] args)
    {
        return string.Format(template, args);
    }
}

#endregion
