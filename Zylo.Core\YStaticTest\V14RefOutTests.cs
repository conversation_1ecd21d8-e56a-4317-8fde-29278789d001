using System;
using Zylo.Toolkit.Attributes;

namespace YStaticTest;

#region v1.4 升级 - ref/out/in 参数测试

/// <summary>
/// YStatic v1.4 ref/out/in 参数专项测试
/// </summary>
[YStatic]
public partial class RefOutTests
{
    /// <summary>
    /// ref 参数测试 - 交换两个值
    /// </summary>
    public void SwapValues(ref int a, ref int b)
    {
        int temp = a;
        a = b;
        b = temp;
    }

    /// <summary>
    /// out 参数测试 - 尝试解析整数
    /// </summary>
    public bool TryParseInt(string input, out int result)
    {
        return int.TryParse(input, out result);
    }

    /// <summary>
    /// 混合参数测试 - ref 和 out 一起使用
    /// </summary>
    public bool ProcessData(string input, ref int counter, out string result)
    {
        counter++;
        result = $"Processed: {input} (#{counter})";
        return true;
    }
}

#endregion
