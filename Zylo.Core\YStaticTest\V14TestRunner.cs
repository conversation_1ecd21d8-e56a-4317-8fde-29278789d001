using System;

namespace YStaticTest;

/// <summary>
/// YStatic v1.4 专项测试运行器
/// 
/// 🎯 专门测试 v1.4 的新修复功能
/// </summary>
public static class V14TestRunner
{
    public static void RunV14Tests()
    {
        Console.WriteLine("🚀 YStatic v1.4 专项测试开始");
        Console.WriteLine(new string('=', 50));
        Console.WriteLine();

        // 测试复杂参数处理修复
        TestComplexParameterHandling();

        Console.WriteLine();
        Console.WriteLine("✅ YStatic v1.4 专项测试完成");
    }

    private static void TestComplexParameterHandling()
    {
        Console.WriteLine("📋 测试复杂参数处理修复:");

        // 基础测试
        TestBasicParameters();

        // v1.4 新增：params 参数测试
        TestParamsParameters();

        // v1.4 新增：ref/out 参数测试
        TestRefOutParameters();
    }

    private static void TestBasicParameters()
    {
        try
        {
            // 测试委托参数
            var result = "hello".Transform(s => s.ToUpper());
            Console.WriteLine($"   ✅ 委托参数测试: 'hello'.Transform(ToUpper) = '{result}'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 委托参数测试失败: {ex.Message}");
        }

        try
        {
            // 测试简单扩展方法
            var result = "world".AddPrefix("Hello ");
            Console.WriteLine($"   ✅ 简单扩展方法测试: 'world'.AddPrefix('Hello ') = '{result}'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 简单扩展方法测试失败: {ex.Message}");
        }
    }

    private static void TestParamsParameters()
    {
        try
        {
            // 测试 params 参数
            var result = ",".JoinStrings("apple", "banana", "cherry");
            Console.WriteLine($"   ✅ params 参数测试: JoinStrings = '{result}'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ params 参数测试失败: {ex.Message}");
        }

        try
        {
            // 测试 params 对象数组
            var result = "Hello {0}, you have {1} messages".FormatMessage("John", 5);
            Console.WriteLine($"   ✅ params 对象数组测试: FormatMessage = '{result}'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ params 对象数组测试失败: {ex.Message}");
        }
    }

    private static void TestRefOutParameters()
    {
        try
        {
            // 测试 ref 参数
            int a = 10, b = 20;
            RefOutTestsEs.SwapValues(ref a, ref b);
            Console.WriteLine($"   ✅ ref 参数测试: SwapValues(10, 20) = ({a}, {b})");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ ref 参数测试失败: {ex.Message}");
        }

        try
        {
            // 测试 out 参数
            bool success = RefOutTestsEs.TryParseInt("123", out int result);
            Console.WriteLine($"   ✅ out 参数测试: TryParseInt('123') = {success}, result = {result}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ out 参数测试失败: {ex.Message}");
        }

        try
        {
            // 测试混合参数
            int counter = 0;
            bool success = RefOutTestsEs.ProcessData("test", ref counter, out string result);
            Console.WriteLine($"   ✅ 混合参数测试: ProcessData = {success}, counter = {counter}, result = '{result}'");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 混合参数测试失败: {ex.Message}");
        }
    }
}
