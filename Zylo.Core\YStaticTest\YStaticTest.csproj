﻿<Project Sdk="Microsoft.NET.Sdk">





  <PropertyGroup>
      <OutputType>Exe</OutputType>
      <ImplicitUsings>enable</ImplicitUsings>
      <Nullable>enable</Nullable>
      <LangVersion>13</LangVersion>
      <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
      <!-- 启用生成器调试 -->
      <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
      <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
      <IsPackable>false</IsPackable>
  </PropertyGroup>





  <ItemGroup>
      <ProjectReference Include="..\Zylo.Toolkit\Zylo.Toolkit.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="true" />
  </ItemGroup>





  <ItemGroup>
    <None Update="README.md">
      <Pack>true</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

</Project>
