# Zylo.All - 完整框架包

Zylo.All 是 Zylo 框架的完整包，包含所有核心组件，为 .NET 开发者提供一站式解决方案。
20250707
## 🚀 包含组件

### 🔧 Zylo.AutoG - 源代码生成器
- 依赖注入自动化
- 静态方法扩展
- 智能日志系统
- 配置管理

### 🛠️ Zylo.Core - 核心工具库
- 安全的类型转换
- 丰富的集合操作
- 全面的文本处理
- LINQ 风格扩展方法

### 💾 Zylo.YData - 数据访问层
- 现代化 ORM 框架
- 多数据库支持
- 容器注入
- 高性能数据操作

### 📁 Zylo.YIO - 文件操作
- 企业级文件处理
- 文件监控
- 安全操作
- 批量处理

### 📝 Zylo.YLog - 日志系统
- 智能日志记录
- 多种输出格式
- 性能优化
- 配置灵活

### 🔍 Zylo.YRegex - 正则表达式
- 强大的正则表达式工具
- 模式构建器
- 验证器
- 性能优化

### 🔤 Zylo.YString - 字符串工具
- 200+ 字符串处理方法
- 位置操作
- 查找替换
- 统计分析

## 📦 安装

```bash
dotnet add package Zylo.All
```

## 🎯 快速开始

```csharp
using Zylo.All;

// 使用所有 Zylo 组件功能
// 详细使用方法请参考各组件文档
```

## 📄 许可证

MIT License

## 🔗 相关链接

- [GitHub 仓库](https://github.com/zylo/zylo-framework)
- [文档](https://docs.zylo.dev)
- [问题反馈](https://github.com/zylo/zylo-framework/issues)
