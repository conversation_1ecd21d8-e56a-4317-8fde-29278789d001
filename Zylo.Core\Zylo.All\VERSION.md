# Zylo 框架版本管理

## 🎯 统一版本号策略

Zylo.All 作为元包，负责管理整个 Zylo 框架的版本号统一性。

### 📋 版本号变量

在 `Zylo.All.csproj` 中定义：

```xml
<PropertyGroup>
    <!-- 🎯 统一版本号管理 -->
    <ZyloFrameworkVersion>1.0.0</ZyloFrameworkVersion>
    <ZyloServiceVersion>1.0.2</ZyloServiceVersion>
</PropertyGroup>
```

### 🔧 版本号使用

```xml
<!-- 使用框架统一版本 -->
<PackageReference Include="Zylo.Core" Version="$(ZyloFrameworkVersion)" />
<PackageReference Include="Zylo.AutoG" Version="$(ZyloFrameworkVersion)" />

<!-- 使用独立版本（快速迭代的包） -->
<PackageReference Include="Zylo.Service" Version="$(ZyloServiceVersion)" />
```

## 📦 包版本策略

### 🎯 主要包（使用 ZyloFrameworkVersion）
- Zylo.All - 元包主版本
- Zylo.Core - 核心工具库
- Zylo.AutoG - 源代码生成器
- Zylo.YData - 数据访问
- Zylo.YIO - 文件操作
- Zylo.YRegex - 正则表达式
- Zylo.YString - 字符串工具

### 🚀 快速迭代包（使用独立版本）
- Zylo.Service - 依赖注入服务（当前 1.0.2）

## 🔄 版本更新流程

1. **更新子包版本**：在各自项目中更新版本号
2. **更新 Zylo.All**：在 Zylo.All.csproj 中更新对应的版本变量
3. **重新打包**：构建 Zylo.All 元包
4. **发布**：发布到 NuGet

## 📝 版本历史

### v1.0.0 (当前)
- 初始版本
- 包含 Zylo.Service 1.0.2
- 包含其他核心组件 1.0.0

### 未来版本
- 根据各组件的更新情况调整版本号
