﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
        <PackageId>Zylo.All</PackageId>
        <Product>Zylo Complete Framework</Product>

        <!-- NuGet 包信息 -->
        <Description>🚀 Zylo.All - Zylo 完整框架包。包含所有 Zylo
            组件：Toolkit源代码生成器、Core工具库、YData数据访问、YIO文件操作、YLog日志、YRegex正则表达式、YString字符串工具等，一站式解决方案。</Description>
        <PackageTags>
            zylo;framework;complete;toolkit;core;data;io;log;regex;string;source-generator;dependency-injection;orm;file-operations</PackageTags>
        <PackageProjectUrl>https://github.com/zylo/zylo-framework</PackageProjectUrl>
        <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

        <!-- 发布说明 -->
        <PackageReleaseNotes>
            🎉 v1.3.2 - Zylo 框架完整版：
            - 🔧 Zylo.Toolkit - 企业级代码生成工具包
            - 🛠️ Zylo.Core - 现代化 C# 运行时工具库
            - 🗄️ Zylo.YData - 现代化数据访问层框架
            - 📁 Zylo.YIO - 企业级文件操作工具库
            - 📝 Zylo.YLog.Runtime - 统一日志记录引擎
            - 🔍 Zylo.YRegex - 企业级正则表达式构建器
            - 🔤 Zylo.YString - 超强字符串操作工具箱
        </PackageReleaseNotes>

        <!-- 🔥 元包特殊配置 - 不包含自己的程序集 -->
        <IncludeBuildOutput>false</IncludeBuildOutput>
        <IncludeSymbols>false</IncludeSymbols>
        <SuppressDependenciesWhenPacking>false</SuppressDependenciesWhenPacking>

        <!-- 🔧 元包调试配置 -->
        <NoDefaultExcludes>true</NoDefaultExcludes>
        <NoPackageAnalysis>true</NoPackageAnalysis>
        <DevelopmentDependency>false</DevelopmentDependency>

        <!-- 🔧 版本冲突解决 -->
        <RestorePackagesWithLockFile>false</RestorePackagesWithLockFile>
        <DisableImplicitFrameworkReferences>false</DisableImplicitFrameworkReferences>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    </PropertyGroup>

    <!-- 包含 README.md 到 NuGet 包 -->
    <ItemGroup>
        <None Include="README.md" Pack="true" PackagePath="README.md" />
    </ItemGroup>


    <!-- 🔥 本地开发：使用项目引用 -->
    <ItemGroup>
        <!-- 源代码生成器包 -->
        <ProjectReference Include="..\Zylo.Toolkit\Zylo.Toolkit.csproj" />

        <!-- 核心组件包 -->
        <ProjectReference Include="..\Zylo.Core\Zylo.Core.csproj" />
        <ProjectReference Include="..\Zylo.YData\Zylo.YData.csproj" />
        <ProjectReference Include="..\Zylo.YIO\Zylo.YIO.csproj" />
        <ProjectReference Include="..\Zylo.YRegex\Zylo.YRegex.csproj" />
        <ProjectReference Include="..\Zylo.YString\Zylo.YString.csproj" />

        <!-- 运行时组件包 -->
        <ProjectReference Include="..\Zylo.YLog.Runtime\Zylo.YLog.Runtime.csproj" />
    </ItemGroup>

    <!-- 🔥 发布时：使用 NuGet 包引用（注释掉，发布前取消注释） -->
    <!--
    <ItemGroup>
        <PackageReference Include="Zylo.Toolkit" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.Core" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.YData" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.YIO" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.YRegex" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.YString" Version="$(ZyloFrameworkVersion)" />
        <PackageReference Include="Zylo.YLog.Runtime" Version="$(ZyloFrameworkVersion)" />
    </ItemGroup>
    -->

    <!-- 🔧 版本冲突解决 -->
    <ItemGroup>
        <!-- 强制使用最新版本的 System.Text.Json -->
        <PackageReference Include="System.Text.Json" Version="8.0.4" />
    </ItemGroup>


</Project>