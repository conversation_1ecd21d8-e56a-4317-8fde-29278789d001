# 🚀 Zylo.All 用户端配置指南

## 🎯 概述

本文档说明用户项目如何正确配置以使用 `Zyl<PERSON>.All` 包，以及在不同场景下需要的特殊配置。

## 📦 基础使用（零配置）

### 1. 最简单的使用方式

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <!-- 🔥 只需要这一行！ -->
    <PackageReference Include="Zylo.All" Version="1.0.0" />
  </ItemGroup>
</Project>
```

### 2. 立即可用的功能

```csharp
using Zylo.Core;           // 核心工具
using Zylo.YData;          // 数据访问
using Zylo.YIO;            // 文件操作
using Zylo.YString;        // 字符串工具
using Zylo.YRegex;         // 正则表达式

class Program
{
    static void Main()
    {
        // 🔥 直接使用，无需额外配置
        var result = YConverter.ToInt("123");
        var text = "hello world".ToPascalCase();
        
        Console.WriteLine($"转换结果: {result}");
        Console.WriteLine($"处理后文本: {text}");
    }
}
```

## 🔧 高级配置场景

### 1. 使用源代码生成器功能

如果您想使用 Zylo.AutoG 的源代码生成器功能，需要额外配置：

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    
    <!-- 🔥 启用 XML 文档生成 - 源代码生成器需要 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    
    <!-- 🔧 可选：启用生成器调试 -->
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Zylo.All" Version="1.0.0" />
  </ItemGroup>
</Project>
```

### 2. 使用依赖注入功能

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Zylo.All" Version="1.0.0" />
    
    <!-- 🔥 如果需要使用 Microsoft.Extensions.Hosting -->
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
  </ItemGroup>
</Project>
```

### 3. Web 应用程序配置

```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Zylo.All" Version="1.0.0" />
  </ItemGroup>
</Project>
```

## 🎨 代码示例

### 1. 基础功能使用

```csharp
using Zylo.Core;
using Zylo.YString;

// 类型转换
var number = YConverter.ToInt("123", 0);
var date = YConverter.ToDateTime("2024-01-01");

// 字符串处理
var text = "hello world"
    .ToPascalCase()
    .RemoveSpaces()
    .Truncate(10);

Console.WriteLine($"处理结果: {text}");
```

### 2. 使用源代码生成器

```csharp
using Microsoft.Extensions.DependencyInjection;

// 🔥 使用 YService 属性自动注册服务
[YService.Scoped]
public partial class UserService
{
    public string GetUserName(int id) => $"User_{id}";
}

// 🔥 使用 YStatic 属性生成静态扩展
[YStatic]
public partial class MathHelper
{
    public int Add(int a, int b) => a + b;
    public int Multiply(int a, int b) => a * b;
}

class Program
{
    static void Main()
    {
        // 依赖注入使用
        var services = new ServiceCollection();
        services.AddYServices(); // 自动生成的扩展方法
        
        var provider = services.BuildServiceProvider();
        var userService = provider.GetRequiredService<UserService>();
        
        // 静态方法使用
        var result = 5.Add(3); // 自动生成的扩展方法
        
        Console.WriteLine($"用户: {userService.GetUserName(1)}");
        Console.WriteLine($"计算结果: {result}");
    }
}
```

### 3. 文件操作示例

```csharp
using Zylo.YIO;

class Program
{
    static async Task Main()
    {
        // 🔥 使用 YIO 的文件操作功能
        await "test.txt".WriteTextAsync("Hello Zylo!");
        var content = await "test.txt".ReadTextAsync();
        
        // 批量文件处理
        var files = Directory.GetFiles(".", "*.txt");
        foreach (var file in files)
        {
            var size = file.GetFileSize();
            Console.WriteLine($"{file}: {size} bytes");
        }
    }
}
```

## ⚠️ 常见问题和解决方案

### 1. 源代码生成器不工作

**问题**: 使用 `[YService]` 或 `[YStatic]` 属性后没有生成代码

**解决方案**:
```xml
<!-- 确保启用 XML 文档生成 -->
<GenerateDocumentationFile>true</GenerateDocumentationFile>

<!-- 可选：启用调试查看生成的代码 -->
<EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
```

### 2. 依赖注入服务找不到

**问题**: `services.AddYServices()` 方法不存在

**解决方案**:
- 确保项目启用了 `GenerateDocumentationFile`
- 重新构建项目
- 检查是否有编译错误

### 3. 扩展方法不可用

**问题**: 如 `"text".ToPascalCase()` 方法找不到

**解决方案**:
```csharp
// 确保引用了正确的命名空间
using Zylo.YString;
using Zylo.Core;
```

## 🔄 版本升级指南

### 升级到新版本

```xml
<!-- 只需要更新一个包的版本 -->
<PackageReference Include="Zylo.All" Version="1.1.0" />
```

### 检查兼容性

```bash
# 清理并重新构建
dotnet clean
dotnet build

# 运行测试确保兼容性
dotnet test
```

## 📊 配置检查清单

### ✅ 基础使用检查
- [ ] 引用了 `Zylo.All` 包
- [ ] 目标框架为 .NET 6.0 或更高版本
- [ ] 能够正常编译和运行

### ✅ 源代码生成器使用检查
- [ ] 启用了 `GenerateDocumentationFile`
- [ ] 使用了正确的属性语法（如 `[YService.Scoped]`）
- [ ] 类声明为 `partial`
- [ ] 重新构建后生成了代码

### ✅ 依赖注入使用检查
- [ ] 引用了必要的 Microsoft.Extensions 包
- [ ] 正确配置了服务容器
- [ ] 使用了自动生成的注册方法

## 🎯 总结

使用 `Zylo.All` 的核心优势：

1. **零配置开始** - 只需一个包引用即可使用所有功能
2. **渐进式增强** - 根据需要添加高级配置
3. **统一版本管理** - 一次升级获得所有组件的最新版本
4. **完整功能集** - 涵盖从基础工具到高级代码生成的所有功能

通过合理的配置，您可以充分利用 Zylo 框架的强大功能，提升开发效率。
