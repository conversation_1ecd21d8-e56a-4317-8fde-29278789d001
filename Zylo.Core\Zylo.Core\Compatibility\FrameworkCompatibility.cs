using System;
using System.Collections.Generic;
using System.Linq;

#if NET48 || NETFRAMEWORK
using System.ComponentModel;
// 现代语言特性支持
using System.Runtime.CompilerServices;
#endif

namespace Zylo.Core.Compatibility;

/// <summary>
/// 框架兼容性工具类
/// 为不同的 .NET 框架提供统一的 API 接口
/// </summary>
public static class FrameworkCompatibility
{
    /// <summary>
    /// 获取当前运行的框架信息
    /// </summary>
    public static string GetFrameworkDescription()
    {
#if NET48
        return ".NET Framework 4.8";
#elif NETFRAMEWORK
        return ".NET Framework";
#elif NET6_0
        return ".NET 6.0";
#elif NET8_0
        return ".NET 8.0";
#elif NET9_0
        return ".NET 9.0";
#else
        return "Unknown Framework";
#endif
    }

    /// <summary>
    /// 检查是否支持 System.Range 和 System.Index
    /// </summary>
    public static bool SupportsRangeAndIndex =>
#if NET48 || NETFRAMEWORK
        true; // .NET Framework 4.8 通过 PolySharp 支持
#else
        true; // .NET Core 3.0+ 原生支持
#endif

    /// <summary>
    /// 检查是否支持 Span&lt;T&gt;
    /// </summary>
    public static bool SupportsSpan =>
#if NET48 || NETFRAMEWORK
        true; // 通过 NuGet 包支持
#else
        true; // 原生支持
#endif

    /// <summary>
    /// 检查是否支持新的 LINQ 方法
    /// </summary>
    public static bool SupportsModernLinq =>
#if NET48 || NETFRAMEWORK
        true; // .NET Framework 4.8 通过兼容性层和 PolySharp 支持
#else
        true; // .NET 6+ 原生支持
#endif

    /// <summary>
    /// 检查是否支持 HttpUtility
    /// </summary>
    public static bool SupportsHttpUtility =>
#if NET48 || NETFRAMEWORK
        true; // .NET Framework 内置支持
#else
        false; // .NET Core 需要额外包
#endif
}

/// <summary>
/// 字符串兼容性扩展方法
/// 为 .NET Framework 4.8 提供现代字符串操作的兼容实现
/// </summary>
public static class StringCompatibilityExtensions
{
    /// <summary>
    /// 兼容的字符串包含检查（忽略大小写）
    /// </summary>
    public static bool ContainsIgnoreCase(this string source, string value)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (value == null) throw new ArgumentNullException(nameof(value));

#if NET48 || NETFRAMEWORK
        return source.IndexOf(value, StringComparison.OrdinalIgnoreCase) >= 0;
#elif NET6_0_OR_GREATER
        return source.Contains(value, StringComparison.OrdinalIgnoreCase);
#else
        return source.IndexOf(value, StringComparison.OrdinalIgnoreCase) >= 0;
#endif
    }

    /// <summary>
    /// 兼容的字符串包含检查（带比较选项）
    /// </summary>
    public static bool ContainsCompat(this string source, string value, StringComparison comparisonType)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (value == null) throw new ArgumentNullException(nameof(value));

#if NET48 || NETFRAMEWORK
        return source.IndexOf(value, comparisonType) >= 0;
#elif NET6_0_OR_GREATER
        return source.Contains(value, comparisonType);
#else
        return source.IndexOf(value, comparisonType) >= 0;
#endif
    }

    /// <summary>
    /// 兼容的字符串分割（支持 StringSplitOptions.TrimEntries）
    /// </summary>
    public static string[] SplitAndTrim(this string source, char separator)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

#if NET48 || NETFRAMEWORK
        return source.Split(separator)
            .Select(s => s.Trim())
            .Where(s => !string.IsNullOrEmpty(s))
            .ToArray();
#elif NET6_0_OR_GREATER
        return source.Split(separator, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
#else
        return source.Split(separator)
            .Select(s => s.Trim())
            .Where(s => !string.IsNullOrEmpty(s))
            .ToArray();
#endif
    }

    /// <summary>
    /// 兼容的字符串分割（支持多个分隔符和 TrimEntries）
    /// </summary>
    public static string[] SplitAndTrimCompat(this string source, char[] separators, StringSplitOptions options = StringSplitOptions.RemoveEmptyEntries)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

#if NET48 || NETFRAMEWORK
        var result = source.Split(separators, options);
        // 在 .NET Framework 中手动实现 TrimEntries 效果
        return result.Select(s => s.Trim()).ToArray();
#elif NET6_0_OR_GREATER
        // 在 .NET 6+ 中，如果需要 TrimEntries，添加该选项
        var enhancedOptions = options;
        if (options.HasFlag(StringSplitOptions.RemoveEmptyEntries))
        {
            enhancedOptions |= StringSplitOptions.TrimEntries;
        }
        return source.Split(separators, enhancedOptions);
#else
        var result = source.Split(separators, options);
        // 在其他版本中手动实现 TrimEntries 效果
        return result.Select(s => s.Trim()).ToArray();
#endif
    }

    /// <summary>
    /// 兼容的子字符串提取（模拟 Range 语法）
    /// </summary>
    public static string SubstringCompat(this string source, int start, int? length = null)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        
        if (start < 0) start = source.Length + start;
        if (start < 0) start = 0;
        if (start >= source.Length) return string.Empty;

        if (length.HasValue)
        {
            var actualLength = Math.Min(length.Value, source.Length - start);
            return actualLength <= 0 ? string.Empty : source.Substring(start, actualLength);
        }
        
        return source.Substring(start);
    }

    /// <summary>
    /// 兼容的字符串结尾提取
    /// </summary>
    public static string TakeLastCompat(this string source, int count)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (count <= 0) return string.Empty;
        if (count >= source.Length) return source;
        
        return source.Substring(source.Length - count);
    }
}

/// <summary>
/// 集合兼容性扩展方法
/// 为 .NET Framework 4.8 提供现代 LINQ 方法的兼容实现
/// </summary>
public static class CollectionCompatibilityExtensions
{
    /// <summary>
    /// 兼容的 DistinctBy 实现
    /// </summary>
    public static IEnumerable<TSource> DistinctByCompat<TSource, TKey>(
        this IEnumerable<TSource> source, 
        Func<TSource, TKey> keySelector)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET48 || NETFRAMEWORK
        var seenKeys = new HashSet<TKey>();
        foreach (var element in source)
        {
            var key = keySelector(element);
            if (seenKeys.Add(key))
            {
                yield return element;
            }
        }
#else
        return source.DistinctBy(keySelector);
#endif
    }

    /// <summary>
    /// 兼容的 Chunk 实现
    /// </summary>
    public static IEnumerable<TSource[]> ChunkCompat<TSource>(
        this IEnumerable<TSource> source, 
        int size)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (size <= 0) throw new ArgumentOutOfRangeException(nameof(size));

#if NET48 || NETFRAMEWORK
        var chunk = new List<TSource>(size);
        foreach (var item in source)
        {
            chunk.Add(item);
            if (chunk.Count == size)
            {
                yield return chunk.ToArray();
                chunk.Clear();
            }
        }
        if (chunk.Count > 0)
        {
            yield return chunk.ToArray();
        }
#else
        return source.Chunk(size);
#endif
    }

    /// <summary>
    /// 兼容的 TryGetValue 实现（为 Dictionary 添加默认值支持）
    /// </summary>
    public static TValue GetValueOrDefaultCompat<TKey, TValue>(
        this Dictionary<TKey, TValue> dictionary,
        TKey key,
        TValue defaultValue = default!)
        where TKey : notnull
    {
        if (dictionary == null) throw new ArgumentNullException(nameof(dictionary));

#if NET48 || NETFRAMEWORK
        return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
#else
        return dictionary.GetValueOrDefault(key, defaultValue);
#endif
    }
}

/// <summary>
/// 枚举兼容性工具
/// </summary>
public static class EnumCompatibility
{
    /// <summary>
    /// 兼容的 Enum.GetValues&lt;T&gt; 实现
    /// </summary>
    public static T[] GetValuesCompat<T>() where T : struct, Enum
    {
#if NET48 || NETFRAMEWORK
        return (T[])Enum.GetValues(typeof(T));
#elif NET6_0_OR_GREATER
        return Enum.GetValues<T>();
#else
        return (T[])Enum.GetValues(typeof(T));
#endif
    }

    /// <summary>
    /// 兼容的 Enum.GetNames&lt;T&gt; 实现
    /// </summary>
    public static string[] GetNamesCompat<T>() where T : struct, Enum
    {
#if NET48 || NETFRAMEWORK
        return Enum.GetNames(typeof(T));
#elif NET6_0_OR_GREATER
        return Enum.GetNames<T>();
#else
        return Enum.GetNames(typeof(T));
#endif
    }
}

/// <summary>
/// HTTP 工具兼容性
/// </summary>
public static class HttpCompatibility
{
    /// <summary>
    /// URL 编码
    /// </summary>
    public static string UrlEncode(string value)
    {
        if (string.IsNullOrEmpty(value)) return value;

#if NET48 || NETFRAMEWORK
        return System.Web.HttpUtility.UrlEncode(value);
#else
        return System.Net.WebUtility.UrlEncode(value);
#endif
    }

    /// <summary>
    /// URL 解码
    /// </summary>
    public static string UrlDecode(string value)
    {
        if (string.IsNullOrEmpty(value)) return value;

#if NET48 || NETFRAMEWORK
        return System.Web.HttpUtility.UrlDecode(value);
#else
        return System.Net.WebUtility.UrlDecode(value);
#endif
    }
}

/// <summary>
/// 现代语言特性兼容性支持
/// </summary>
public static class ModernLanguageFeatures
{
    /// <summary>
    /// 可空引用类型支持 - 为 .NET Framework 4.8 提供注解
    /// </summary>
#if NET48 || NETFRAMEWORK
    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.Property | AttributeTargets.ReturnValue)]
    public sealed class NotNullAttribute : Attribute { }

    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.Property)]
    public sealed class AllowNullAttribute : Attribute { }

    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.Property | AttributeTargets.ReturnValue)]
    public sealed class MaybeNullAttribute : Attribute { }

    [AttributeUsage(AttributeTargets.Parameter)]
    public sealed class NotNullWhenAttribute : Attribute
    {
        public bool ReturnValue { get; }
        public NotNullWhenAttribute(bool returnValue) => ReturnValue = returnValue;
    }
#endif

    /// <summary>
    /// 元组解构支持
    /// </summary>
    public static void Deconstruct<T1, T2>(this KeyValuePair<T1, T2> kvp, out T1 key, out T2 value)
    {
        key = kvp.Key;
        value = kvp.Value;
    }

    // 注意：Range 和 Index 支持现在由 PolySharp 提供，无需自定义实现
}

/// <summary>
/// 现代集合方法兼容性
/// </summary>
public static class ModernCollectionMethods
{
    /// <summary>
    /// 兼容的 DistinctBy 实现（增强版）
    /// </summary>
    public static IEnumerable<TSource> DistinctByCompat<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        IEqualityComparer<TKey>? comparer = null)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET48 || NETFRAMEWORK
        var seenKeys = new HashSet<TKey>(comparer ?? EqualityComparer<TKey>.Default);
        foreach (var element in source)
        {
            var key = keySelector(element);
            if (seenKeys.Add(key))
            {
                yield return element;
            }
        }
#else
        return source.DistinctBy(keySelector, comparer);
#endif
    }

    /// <summary>
    /// 兼容的 MinBy 实现
    /// </summary>
    public static TSource? MinByCompat<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        IComparer<TKey>? comparer = null)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET48 || NETFRAMEWORK
        comparer ??= Comparer<TKey>.Default;
        using var enumerator = source.GetEnumerator();

        if (!enumerator.MoveNext())
            return default;

        var min = enumerator.Current;
        var minKey = keySelector(min);

        while (enumerator.MoveNext())
        {
            var current = enumerator.Current;
            var currentKey = keySelector(current);

            if (comparer.Compare(currentKey, minKey) < 0)
            {
                min = current;
                minKey = currentKey;
            }
        }

        return min;
#else
        return source.MinBy(keySelector, comparer);
#endif
    }

    /// <summary>
    /// 兼容的 MaxBy 实现
    /// </summary>
    public static TSource? MaxByCompat<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        IComparer<TKey>? comparer = null)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET48 || NETFRAMEWORK
        comparer ??= Comparer<TKey>.Default;
        using var enumerator = source.GetEnumerator();

        if (!enumerator.MoveNext())
            return default;

        var max = enumerator.Current;
        var maxKey = keySelector(max);

        while (enumerator.MoveNext())
        {
            var current = enumerator.Current;
            var currentKey = keySelector(current);

            if (comparer.Compare(currentKey, maxKey) > 0)
            {
                max = current;
                maxKey = currentKey;
            }
        }

        return max;
#else
        return source.MaxBy(keySelector, comparer);
#endif
    }
}
