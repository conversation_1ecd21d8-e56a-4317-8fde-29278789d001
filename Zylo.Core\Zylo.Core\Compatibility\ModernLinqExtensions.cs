using System;
using System.Collections.Generic;
using System.Linq;

namespace Zylo.Core.Compatibility;

/// <summary>
/// 现代 LINQ 方法的兼容性扩展
/// 为 .NET Framework 4.8 提供现代 LINQ 方法支持
/// </summary>
public static class ModernLinqExtensions
{
    /// <summary>
    /// 将序列分割成指定大小的块
    /// 兼容 .NET 6+ 的 Chunk 方法
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="size">块大小</param>
    /// <returns>分块后的序列</returns>
    public static IEnumerable<T[]> Chunk<T>(this IEnumerable<T> source, int size)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (size <= 0) throw new ArgumentOutOfRangeException(nameof(size), "Size must be greater than 0");

#if NET6_0_OR_GREATER
        return source.Chunk(size);
#else
        return ChunkIterator(source, size);
#endif
    }

    /// <summary>
    /// Chunk 方法的内部实现
    /// </summary>
    private static IEnumerable<T[]> ChunkIterator<T>(IEnumerable<T> source, int size)
    {
        using var enumerator = source.GetEnumerator();
        
        while (enumerator.MoveNext())
        {
            var chunk = new T[size];
            chunk[0] = enumerator.Current;
            int count = 1;
            
            while (count < size && enumerator.MoveNext())
            {
                chunk[count] = enumerator.Current;
                count++;
            }
            
            if (count == size)
            {
                yield return chunk;
            }
            else
            {
                // 最后一个块可能不满
                var lastChunk = new T[count];
                Array.Copy(chunk, lastChunk, count);
                yield return lastChunk;
            }
        }
    }

    /// <summary>
    /// 兼容的 DistinctBy 实现
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>去重后的序列</returns>
    public static IEnumerable<TSource> DistinctBy<TSource, TKey>(
        this IEnumerable<TSource> source, 
        Func<TSource, TKey> keySelector)
    {
        return DistinctBy(source, keySelector, null);
    }

    /// <summary>
    /// 兼容的 DistinctBy 实现（带比较器）
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <param name="comparer">比较器</param>
    /// <returns>去重后的序列</returns>
    public static IEnumerable<TSource> DistinctBy<TSource, TKey>(
        this IEnumerable<TSource> source, 
        Func<TSource, TKey> keySelector,
        IEqualityComparer<TKey>? comparer)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET6_0_OR_GREATER
        return source.DistinctBy(keySelector, comparer);
#else
        return DistinctByIterator(source, keySelector, comparer ?? EqualityComparer<TKey>.Default);
#endif
    }

    /// <summary>
    /// DistinctBy 方法的内部实现
    /// </summary>
    private static IEnumerable<TSource> DistinctByIterator<TSource, TKey>(
        IEnumerable<TSource> source, 
        Func<TSource, TKey> keySelector,
        IEqualityComparer<TKey> comparer)
    {
        var seenKeys = new HashSet<TKey>(comparer);
        
        foreach (var element in source)
        {
            var key = keySelector(element);
            if (seenKeys.Add(key))
            {
                yield return element;
            }
        }
    }

    /// <summary>
    /// 兼容的 MinBy 实现
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>最小值元素</returns>
    public static TSource? MinBy<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector)
    {
        return MinBy(source, keySelector, null);
    }

    /// <summary>
    /// 兼容的 MinBy 实现（带比较器）
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <param name="comparer">比较器</param>
    /// <returns>最小值元素</returns>
    public static TSource? MinBy<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        IComparer<TKey>? comparer)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET6_0_OR_GREATER
        return source.MinBy(keySelector, comparer);
#else
        comparer ??= Comparer<TKey>.Default;
        using var enumerator = source.GetEnumerator();
        
        if (!enumerator.MoveNext())
            return default;
            
        var min = enumerator.Current;
        var minKey = keySelector(min);
        
        while (enumerator.MoveNext())
        {
            var current = enumerator.Current;
            var currentKey = keySelector(current);
            
            if (comparer.Compare(currentKey, minKey) < 0)
            {
                min = current;
                minKey = currentKey;
            }
        }
        
        return min;
#endif
    }

    /// <summary>
    /// 兼容的 MaxBy 实现
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>最大值元素</returns>
    public static TSource? MaxBy<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector)
    {
        return MaxBy(source, keySelector, null);
    }

    /// <summary>
    /// 兼容的 MaxBy 实现（带比较器）
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="keySelector">键选择器</param>
    /// <param name="comparer">比较器</param>
    /// <returns>最大值元素</returns>
    public static TSource? MaxBy<TSource, TKey>(
        this IEnumerable<TSource> source,
        Func<TSource, TKey> keySelector,
        IComparer<TKey>? comparer)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (keySelector == null) throw new ArgumentNullException(nameof(keySelector));

#if NET6_0_OR_GREATER
        return source.MaxBy(keySelector, comparer);
#else
        comparer ??= Comparer<TKey>.Default;
        using var enumerator = source.GetEnumerator();
        
        if (!enumerator.MoveNext())
            return default;
            
        var max = enumerator.Current;
        var maxKey = keySelector(max);
        
        while (enumerator.MoveNext())
        {
            var current = enumerator.Current;
            var currentKey = keySelector(current);
            
            if (comparer.Compare(currentKey, maxKey) > 0)
            {
                max = current;
                maxKey = currentKey;
            }
        }
        
        return max;
#endif
    }

    /// <summary>
    /// 兼容的 TryGetNonEnumeratedCount 实现
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="count">计数结果</param>
    /// <returns>是否成功获取计数</returns>
    public static bool TryGetNonEnumeratedCount<TSource>(
        this IEnumerable<TSource> source,
        out int count)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

#if NET6_0_OR_GREATER
        return source.TryGetNonEnumeratedCount(out count);
#else
        switch (source)
        {
            case ICollection<TSource> collection:
                count = collection.Count;
                return true;
            case System.Collections.ICollection legacyCollection:
                count = legacyCollection.Count;
                return true;
            case string str:
                count = str.Length;
                return true;
            default:
                count = 0;
                return false;
        }
#endif
    }

    /// <summary>
    /// 兼容的 ToHashSet 实现
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <param name="source">源序列</param>
    /// <returns>HashSet</returns>
    public static HashSet<TSource> ToHashSetCompat<TSource>(this IEnumerable<TSource> source)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

#if NET6_0_OR_GREATER
        return source.ToHashSet();
#else
        return new HashSet<TSource>(source);
#endif
    }

    /// <summary>
    /// 兼容的 ToHashSet 实现（带比较器）
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <param name="source">源序列</param>
    /// <param name="comparer">比较器</param>
    /// <returns>HashSet</returns>
    public static HashSet<TSource> ToHashSetCompat<TSource>(this IEnumerable<TSource> source, IEqualityComparer<TSource>? comparer)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

#if NET6_0_OR_GREATER
        return source.ToHashSet(comparer);
#else
        return new HashSet<TSource>(source, comparer);
#endif
    }
}

/// <summary>
/// 字典扩展方法
/// </summary>
public static class DictionaryExtensions
{
    /// <summary>
    /// 兼容的 GetValueOrDefault 实现
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键</param>
    /// <returns>值或默认值</returns>
    public static TValue? GetValueOrDefault<TKey, TValue>(
        this Dictionary<TKey, TValue> dictionary, 
        TKey key) where TKey : notnull
    {
        return GetValueOrDefault(dictionary, key, default(TValue));
    }

    /// <summary>
    /// 兼容的 GetValueOrDefault 实现（带默认值）
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>值或默认值</returns>
    public static TValue GetValueOrDefault<TKey, TValue>(
        this Dictionary<TKey, TValue> dictionary, 
        TKey key, 
        TValue defaultValue) where TKey : notnull
    {
        if (dictionary == null) throw new ArgumentNullException(nameof(dictionary));

#if NET6_0_OR_GREATER
        return dictionary.GetValueOrDefault(key, defaultValue);
#else
        return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
#endif
    }
}
