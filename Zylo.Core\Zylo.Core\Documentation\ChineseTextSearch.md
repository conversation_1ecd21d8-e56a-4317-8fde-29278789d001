# Zylo.Core 中文文本搜索功能

## 概述

Zylo.Core 框架提供了强大的中文文本搜索和处理功能，专门针对中文文本的特点进行了优化。支持单个和多个中文字符/词汇的查找，能够处理重复文本，并提供详细的位置信息。

## 主要功能

### 1. 基本中文文本搜索

#### `YFindChinesePositions` - 查找中文文本位置
```csharp
string text = "你好世界，你好中国";
int[] positions = text.YFindChinesePositions("你好");
// 返回: [0, 5] - "你好"在位置0和5出现
```

#### `YFindFirstChinesePosition` - 查找第一个位置
```csharp
string text = "Hello你好World世界";
int position = text.YFindFirstChinesePosition("你好");
// 返回: 5
```

#### `YFindLastChinesePosition` - 查找最后一个位置
```csharp
string text = "你好世界，你好朋友";
int position = text.YFindLastChinesePosition("你好");
// 返回: 5 (最后一个"你好"的位置)
```

### 2. 多词搜索功能

#### `YFindMultipleChinesePositions` - 同时搜索多个词汇
```csharp
string text = "欢迎来到中国北京，这里是首都";
string[] searchWords = { "中国", "北京", "首都", "不存在" };
YChineseSearchResult[] results = text.YFindMultipleChinesePositions(searchWords);

foreach (var result in results)
{
    Console.WriteLine($"'{result.SearchText}': {(result.Found ? $"找到{result.Count}次" : "未找到")}");
    if (result.Found)
    {
        Console.WriteLine($"位置: [{string.Join(", ", result.Positions)}]");
    }
}
```

### 3. 详细搜索结果

#### `YSearchChinese` - 获取详细搜索信息
```csharp
string text = "这是测试文本，包含测试内容";
YChineseSearchResult result = text.YSearchChinese("测试");

Console.WriteLine($"搜索词: {result.SearchText}");
Console.WriteLine($"是否找到: {result.Found}");
Console.WriteLine($"匹配次数: {result.Count}");
Console.WriteLine($"第一个位置: {result.FirstPosition}");
Console.WriteLine($"最后位置: {result.LastPosition}");
Console.WriteLine($"所有位置: [{string.Join(", ", result.Positions)}]");
```

### 4. 位置详细信息

#### 获取行号、列号和上下文
```csharp
string text = @"第一行文本
第二行包含测试
第三行也有测试内容";

var result = text.YSearchChinese("测试");
var details = result.GetPositionDetails(text);

foreach (var detail in details)
{
    Console.WriteLine($"位置 {detail.Position}:");
    Console.WriteLine($"  行号: {detail.LineNumber}");
    Console.WriteLine($"  列号: {detail.ColumnNumber}");
    Console.WriteLine($"  匹配文本: '{detail.MatchedText}'");
    Console.WriteLine($"  上下文: {detail.Context}");
}
```

### 5. 中文字符分析

#### 中文字符检测和统计
```csharp
string text = "Hello你好World世界123";

// 检查是否包含中文
bool containsChinese = text.YContainsChinese(); // true

// 统计中文字符数量
int chineseCount = text.YCountChineseChars(); // 4

// 获取中文字符比例
double ratio = text.YGetChineseRatio(); // 0.21 (4/19)

// 检查是否为纯中文
bool isPure = text.YIsPureChinese(); // false

// 检查单个字符
bool isChineseChar = '你'.YIsChineseChar(); // true
```

#### 中文字符提取
```csharp
string text = "Hello你好World世界123";

// 提取所有中文字符
char[] chineseChars = text.YExtractChineseChars();
// 返回: ['你', '好', '世', '界']

// 获取中文字符位置
int[] positions = text.YFindAllChineseCharPositions();
// 返回: [5, 6, 12, 13]

// 提取中文词汇
string text2 = "你好世界 欢迎来到 中国北京";
string[] words = text2.YExtractChineseWords();
// 返回: ["你好世界", "欢迎来到", "中国北京"]
```

### 6. 纯中文文本验证

#### `YIsPureChinese` - 检查是否为纯中文
```csharp
// 基本检查
"你好世界".YIsPureChinese(); // true
"你好World".YIsPureChinese(); // false

// 允许标点符号
"你好，世界！".YIsPureChinese(allowPunctuation: true); // true
"你好，世界！".YIsPureChinese(allowPunctuation: false); // false

// 允许空格
"你好 世界".YIsPureChinese(allowSpaces: true); // true
"你好 世界".YIsPureChinese(allowSpaces: false); // false
```

## 处理重复文本

框架能够很好地处理重复的中文文本：

```csharp
string text = "测试测试测试，这是测试文本。测试功能很重要。";
var result = text.YSearchChinese("测试");

Console.WriteLine($"找到 {result.Count} 个匹配");
Console.WriteLine($"位置: [{string.Join(", ", result.Positions)}]");
// 输出: 找到 5 个匹配
// 位置: [0, 2, 4, 9, 15]
```

## 搜索结果类

### `YChineseSearchResult` 类

```csharp
public class YChineseSearchResult
{
    public string SearchText { get; set; }        // 搜索的文本
    public int[] Positions { get; set; }          // 所有匹配位置
    public int Count { get; set; }                // 匹配次数
    public bool Found { get; set; }               // 是否找到
    public int FirstPosition { get; }             // 第一个位置
    public int LastPosition { get; }              // 最后位置
    
    // 获取位置详细信息
    public YChinesePositionInfo[] GetPositionDetails(string sourceText);
    
    // 获取详细报告
    public string GetDetailedReport(string sourceText);
}
```

### `YChinesePositionInfo` 类

```csharp
public class YChinesePositionInfo
{
    public int Position { get; set; }             // 字符位置
    public int LineNumber { get; set; }           // 行号
    public int ColumnNumber { get; set; }         // 列号
    public string MatchedText { get; set; }       // 匹配的文本
    public string Context { get; set; }           // 上下文
}
```

## 使用场景

1. **文档搜索**: 在中文文档中快速定位关键词
2. **内容分析**: 分析文本中特定词汇的分布
3. **数据清洗**: 提取和验证中文内容
4. **文本处理**: 处理混合语言文本
5. **关键词统计**: 统计中文关键词出现频率

## 性能特点

- **高效搜索**: 使用优化的字符串搜索算法
- **内存友好**: 避免不必要的字符串复制
- **支持大文本**: 能够处理大型文本文件
- **准确定位**: 精确的位置信息和上下文

## 注意事项

1. 搜索是**区分大小写**的（对中文无影响，但对混合文本有效）
2. 支持**重叠匹配**，即同一位置可能匹配多个搜索词
3. 位置索引从**0开始**
4. 行号和列号从**1开始**
5. 中文字符范围为 Unicode `0x4e00-0x9fff`

## 示例代码

完整的示例代码请参考：
- `Zylo.Core/Examples/ChineseTextSearchExample.cs`
- `Zylo.Core/Tests/YTextExtensionsChineseTests.cs`
