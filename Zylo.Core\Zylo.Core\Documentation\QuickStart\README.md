# 🚀 快速开始指南

> **5分钟上手 Zylo.Core** - 从安装到实际应用的完整指南

## 📦 **安装**

### **NuGet 包管理器**
```bash
dotnet add package Zylo.Core
```

### **Package Manager Console**
```powershell
Install-Package Zylo.Core
```

### **PackageReference**
```xml
<PackageReference Include="Zylo.Core" Version="2.2.0" />
```

## ⚡ **30秒快速体验**

### **第一行代码**
```csharp
using Zylo.Core;

// 类型转换 - 永不抛异常
var age = "25".YToInt(0);           // 25，失败返回0
var price = "99.99".YToDecimal();   // 99.99m
var isValid = "true".YToBool();     // true

// 集合操作 - 越界安全
var list = new List<string> { "apple", "banana" };
var item = list.YSafeGet(0, "默认");  // "apple"，越界返回"默认"
var isEmpty = list.YIsNullOrEmpty(); // false

// 文本处理 - 全面安全 (v2.2.0 新增)
var cleaned = "  Hello,   World!  ".YCleanText();     // "Hello, World!"
var isEmail = "<EMAIL>".YIsValidEmail();     // true
var slug = "Hello World!".YToSlug();                  // "hello-world"
```

## 🎯 **核心理念：Y = 安全**

所有以 `Y` 开头的方法都是**安全的**，不会抛出异常：

```csharp
// ❌ 传统方式：可能抛异常
var number = int.Parse("abc");        // 💥 异常！
var item = list[10];                  // 💥 越界异常！

// ✅ Zylo 方式：永远安全
var number = "abc".YToInt(0);         // 0 (默认值)
var item = list.YSafeGet(10, "默认"); // "默认"
```

## 📋 **5分钟功能导览**

### **🔄 类型转换 - 1分钟**
```csharp
// 基础转换
"123".YToInt()                      // 123
"99.9".YToDouble()                  // 99.9
"true".YToBool()                    // true
"2024-01-01".YToDateTime()          // DateTime

// 带默认值
"abc".YToInt(999)                   // 999 (转换失败返回默认值)
"".YToDouble(1.0)                   // 1.0
"maybe".YToBool(false)              // false

// 中文支持
"是".YToBool()                       // true
"否".YToBool()                       // false

// 集合转换
"a,b,c".YToStringArray()            // ["a", "b", "c"]
"1,2,3".YToIntArray()               // [1, 2, 3]
```

### **📋 集合操作 - 2分钟**
```csharp
var list = new List<string> { "apple", "banana", "cherry" };

// 安全访问
list.YSafeGet(0, "默认")             // "apple"
list.YSafeGet(10, "默认")            // "默认" (越界返回默认值)
list.YSafeFirst("默认")              // "apple"
list.YSafeLast("默认")               // "cherry"

// LINQ 风格安全操作
var numbers = new[] { 1, 2, 3, 4, 5 };
var result = numbers
    .Where(x => x > 2)              // 标准 LINQ
    .YTakeWhile(x => x < 5)         // Zylo 安全方法
    .YSum();                        // Zylo 安全求和 = 9

// List 增强操作
list.YClearAndAddRange(new[] { "new", "items" });  // 一键替换
var count = list.YReplaceWhere(x => x == "old", "new"); // 条件替换

// 算法支持
var items = new[] { "A", "B", "C" };
var combinations = items.YCombinations(2);         // 组合算法
var permutations = items.YPermutations(2);         // 排列算法
```

### **📝 文本处理 - 2分钟 (v2.2.0 新增)**
```csharp
// 文本清理
"  Hello,   World!  \n\t".YCleanText()          // "Hello, World!"
"Very long text".YTruncate(10)                  // "Very long "
"Very long text".YEllipsis(10)                  // "Very lo..."

// 文本格式化
"hello world".YToTitleCase()                    // "Hello World"
"hello world".YToCamelCase()                    // "helloWorld"
"Hello World!".YToSlug()                       // "hello-world"
"Hello".YToBase64()                             // "SGVsbG8="

// 文本验证
"<EMAIL>".YIsValidEmail()              // true
"13812345678".YIsValidChinesePhone()            // true
"Hello 世界".YContainsChinese()                  // true
"https://example.com".YIsValidUrl()             // true

// 文本分析
"Hello world from Zylo".YGetWordCount()         // 4
var analysis = "这是测试文本。".YAnalyzeText();
Console.WriteLine($"语言: {analysis.DetectedLanguage}"); // "zh"
Console.WriteLine($"情感: {analysis.SentimentScore}");   // 情感评分

// 高级处理
"hello world".YSimilarity("hello earth")        // 0.5 (50%相似)
"hello".YLevenshteinDistance("hallo")           // 1 (编辑距离)
```

## 🔌 **依赖注入 - 企业级应用**

### **服务注册**
```csharp
// Program.cs
using Zylo.Core.Extensions;

var builder = WebApplication.CreateBuilder(args);

// 注册所有 Zylo.Core 服务
builder.Services.AddYCore();

// 或单独注册
// builder.Services.AddYConverter();
// builder.Services.AddYCollection();
// builder.Services.AddYText();

var app = builder.Build();
```

### **服务使用**
```csharp
public class UserService
{
    private readonly IYConverter _converter;
    private readonly IYCollection _collection;
    private readonly IYText _textService;
    
    public UserService(IYConverter converter, IYCollection collection, IYText textService)
    {
        _converter = converter;
        _collection = collection;
        _textService = textService;
    }
    
    public User ProcessUser(Dictionary<string, string> formData)
    {
        // 验证输入
        if (!_textService.IsValidEmail(formData["email"]))
            throw new ArgumentException("Invalid email");
            
        // 处理数据
        return new User
        {
            Name = _textService.ToTitleCase(_textService.CleanText(formData["name"])),
            Age = _converter.SafeConvert<int>(formData["age"], 18),
            Email = formData["email"],
            Bio = _textService.Truncate(_textService.CleanText(formData["bio"]), 500)
        };
    }
    
    public StatisticsResult AnalyzeScores(List<double> scores)
    {
        return new StatisticsResult
        {
            Count = scores.Count,
            Sum = _collection.SumSafe(scores),
            Average = _collection.AverageSafe(scores, 0.0),
            Max = _collection.MaxSafe(scores, 0.0),
            HasHighScores = _collection.AnySafe(scores, s => s > 90)
        };
    }
}
```

## 🎨 **实战场景示例**

### **场景1：Web API 输入处理**
```csharp
[HttpPost]
public IActionResult CreateUser(CreateUserRequest request)
{
    // 输入验证
    if (!request.Email.YIsValidEmail())
        return BadRequest("Invalid email format");
        
    if (!request.Phone.YIsValidChinesePhone())
        return BadRequest("Invalid phone number");
    
    // 数据处理
    var user = new User
    {
        Name = request.Name.YCleanText().YToTitleCase(),
        Age = request.Age.YToInt(18),
        Bio = request.Bio.YCleanText().YTruncate(500),
        Slug = request.Name.YToSlug()
    };
    
    // 保存用户...
    return Ok(user);
}
```

### **场景2：配置文件读取**
```csharp
public class AppConfig
{
    private readonly IConfiguration _config;
    
    public AppConfig(IConfiguration config)
    {
        _config = config;
    }
    
    public int Timeout => _config["Timeout"].YToInt(30);
    public bool EnableCache => _config["EnableCache"].YToBool(true);
    public double MaxSize => _config["MaxSize"].YToDouble(1024.0);
    public string[] AllowedHosts => _config["AllowedHosts"].YToStringArray();
}
```

### **场景3：数据分析**
```csharp
public class SalesAnalyzer
{
    public SalesReport Analyze(List<Sale> sales)
    {
        var amounts = sales.Select(s => s.Amount).ToArray();
        
        return new SalesReport
        {
            TotalSales = amounts.YSum(),
            AverageAmount = amounts.YAverage(0.0),
            MaxSale = amounts.YMax(0.0),
            MinSale = amounts.YMin(0.0),
            HighValueCount = amounts.YCount(x => x > 1000),
            TopProducts = sales
                .GroupBy(s => s.Product)
                .YOrderByDescending(g => g.Sum(s => s.Amount))
                .YTake(5)
                .Select(g => g.Key)
                .ToArray()
        };
    }
}
```

### **场景4：内容管理**
```csharp
public class ArticleProcessor
{
    public ProcessedArticle ProcessArticle(string title, string content)
    {
        var analysis = content.YAnalyzeText();
        
        return new ProcessedArticle
        {
            Title = title.YCleanText().YToTitleCase(),
            Content = content.YCleanText(),
            Summary = content.YEllipsis(200),
            Slug = title.YToSlug(),
            WordCount = analysis.WordCount,
            ReadingTime = analysis.EstimatedReadingTime,
            Language = analysis.DetectedLanguage,
            SeoScore = CalculateSeoScore(analysis)
        };
    }
    
    private double CalculateSeoScore(YTextAnalysis analysis)
    {
        var score = 0.0;
        if (analysis.WordCount >= 300) score += 30;
        if (analysis.ReadabilityScore > 60) score += 40;
        if (analysis.SentimentScore > 0) score += 30;
        return score;
    }
}
```

## 💡 **最佳实践**

### **1. 选择合适的使用方式**
```csharp
// ✅ 简单场景：直接使用扩展方法
var result = "123".YToInt(0);

// ✅ 企业应用：使用依赖注入
public class Service
{
    private readonly IYConverter _converter;
    // ...
}
```

### **2. 充分利用默认值**
```csharp
// ✅ 提供有意义的默认值
var age = input.YToInt(18);        // 默认成年
var score = input.YToDouble(0.0);  // 默认0分
var name = list.YSafeGet(0, "匿名"); // 默认匿名
```

### **3. 链式操作的威力**
```csharp
// ✅ 强大的链式操作
var result = data
    .Where(x => x.IsActive)         // 标准 LINQ
    .YOrderByDescending(x => x.Score) // Zylo 安全排序
    .YTakeWhile(x => x.Score > 80)   // Zylo 安全分区
    .YFirstOrDefault(new Item());    // Zylo 安全获取
```

### **4. 错误处理**
```csharp
// ✅ 使用 Y 前缀方法避免异常
try
{
    var data = ProcessData();
    var result = data.YSafeGet(0, "默认");  // 不会抛异常
}
catch (Exception ex)
{
    // 只处理业务逻辑异常，不用担心 Zylo 方法异常
}
```

## 🔧 **常见问题**

### **Q: 忘记添加 using 怎么办？**
```csharp
// 添加这一行即可
using Zylo.Core;
```

### **Q: 依赖注入不工作？**
```csharp
// 确保注册了服务
builder.Services.AddYCore();
```

### **Q: 扩展方法不显示？**
```csharp
// 确保引用了正确的命名空间
using Zylo.Core;

// 确保安装了 NuGet 包
// dotnet add package Zylo.Core
```

### **Q: 性能如何？**
- ✅ **高性能**: 优化的算法实现
- ✅ **低开销**: 最小的性能损失
- ✅ **内存友好**: 避免不必要的分配

## 🎯 **学习路径**

### **第1天：基础转换**
- 掌握 YToInt, YToBool 等基础转换
- 理解默认值的使用
- 练习处理用户输入

### **第2天：集合操作**
- 学习 YSafeGet, YIsNullOrEmpty 等安全访问
- 体验 LINQ 风格的链式操作
- 尝试算法功能

### **第3天：文本处理**
- 掌握文本清理和格式化
- 学习文本验证功能
- 体验文本分析能力

### **第4天：依赖注入**
- 理解企业级架构模式
- 学习服务注册和使用
- 编写单元测试

### **第5天：实战应用**
- 在实际项目中应用
- 结合具体业务场景
- 优化代码质量

## 🚀 **下一步**

现在您已经掌握了 Zylo.Core 的基础用法，可以：

1. **深入学习**: 查看详细的功能文档
   - [YConverter - 类型转换](../YConverter/README.md)
   - [YCollection - 集合操作](../YCollection/README.md)
   - [YText - 文本处理](../YText/README.md)

2. **企业应用**: 了解依赖注入模式
   - [依赖注入指南](../DependencyInjection/README.md)

3. **实际项目**: 在您的项目中开始使用 Zylo.Core

**立即开始使用 Zylo.Core，让您的 C# 开发更加安全、高效！** 🎉
