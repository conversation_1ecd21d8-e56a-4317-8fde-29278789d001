# 📚 Zylo.Core 文档中心

<div align="center">

![Documentation](https://img.shields.io/badge/Documentation-Complete-green)
![Modules](https://img.shields.io/badge/Modules-4-blue)
![Guides](https://img.shields.io/badge/Guides-6-orange)

**Zylo.Core 完整文档导航中心**

</div>

## 🎯 **文档概览**

欢迎来到 Zylo.Core 文档中心！这里提供了完整的使用指南、API 文档和最佳实践。

### **文档结构**

```
Documentation/
├── 📖 QuickStart/          # 快速开始指南
├── 🔥 YLog/                # 高性能日志框架文档
├── 🔄 YConverter/          # 类型转换功能文档
├── 📋 YCollection/         # 集合操作功能文档
├── 📝 YText/               # 文本处理功能文档
├── 🔌 DependencyInjection/ # 依赖注入指南
└── 🔄 BinSharedMigration/  # Bin.Shared 迁移指南
```

## 🚀 **快速导航**

### **🎯 新用户推荐路径**

1. **[📖 快速开始指南](QuickStart/README.md)** - 5分钟上手 Zylo.Core
2. **[🔥 YLog 日志框架](YLog/README.md)** - 高性能源代码生成日志系统 🆕
3. **[🔄 YConverter 文档](YConverter/README.md)** - 学习安全的类型转换
4. **[📋 YCollection 文档](YCollection/README.md)** - 掌握集合操作技巧
5. **[📝 YText 文档](YText/README.md)** - 体验强大的文本处理 ✨

### **🏢 企业用户推荐路径**

1. **[🔌 依赖注入指南](DependencyInjection/README.md)** - 企业级架构设计
2. **[📖 快速开始指南](QuickStart/README.md)** - 了解基础用法
3. **各功能模块详细文档** - 深入学习具体功能

### **🔄 迁移用户推荐路径**

1. **[🔄 Bin.Shared 迁移指南](BinSharedMigration/README.md)** - 从旧版本迁移
2. **[📖 快速开始指南](QuickStart/README.md)** - 熟悉新的使用方式
3. **各功能模块文档** - 学习新增功能

## 📋 **功能模块文档**

### **🔥 YLog - 高性能日志框架** 🆕 (v2.3.0 新增)
>
> **零运行时开销的源代码生成日志系统**

**核心特性：**

- 🚀 编译时代码生成，零反射调用
- 🆕 完全解除Core后缀约束，灵活命名
- 🧬 完整泛型支持，包括复杂约束和异步泛型
- ⚡ 异步文件写入，不阻塞主线程
- 📁 智能文件管理，自动轮转和大小控制
- 🎨 便捷属性语法：`[YLog.Debug]`、`[YLog.Error]`

**快速示例：**

```csharp
public partial class UserService
{
    [YLog]  // 自动记录方法执行
    public string GetUserCore(int userId)
    {
        YLogger.Info("开始获取用户", "UserId", userId);
        return $"User {userId}";
    }
}

// 调用：service.GetUser(123) - 自动去掉Core后缀
// 输出：[2025-06-30 10:00:11.170] [INFORMATION] [T5] UserService.GetUser(123) => User 123 [60ms]
```

**[📖 查看完整文档](YLog/README.md)**

---

### **🔄 YConverter - 类型转换**
>
> **80+ 个安全转换方法，永不抛异常**

**核心特性：**

- 🛡️ 安全转换，支持默认值
- 🌏 中文格式支持（如"是/否"）
- 🔌 完整的依赖注入支持
- ⚡ 高性能 TryParse 实现

**快速示例：**

```csharp
var age = "25".YToInt(0);           // 25，失败返回0
var isActive = "是".YToBool();       // true，支持中文
var price = "99.99".YToDecimal();   // 99.99m
```

**[📖 查看完整文档](YConverter/README.md)**

---

### **📋 YCollection - 集合操作**
>
> **150+ 个集合方法，LINQ 风格安全操作**

**核心特性：**

- 🛡️ 越界安全，边界检查
- 🔗 LINQ 风格链式操作
- 🧮 丰富的算法支持
- 📝 List 增强功能

**快速示例：**

```csharp
var item = list.YSafeGet(0, "默认");  // 安全访问
var sum = numbers.YSum();            // 安全求和
var combos = items.YCombinations(2); // 组合算法
```

**[📖 查看完整文档](YCollection/README.md)**

---

### **📝 YText - 文本处理** ✨ (v2.2.0 新增)
>
> **50+ 个文本方法，清理、格式化、验证、分析一站式解决方案**

**核心特性：**

- 🧹 文本清理和格式化
- ✅ 邮箱、手机、URL 验证
- 📊 文本分析和语言检测
- 🌏 中文特殊支持

**快速示例：**

```csharp
var cleaned = "  Hello,   World!  ".YCleanText();     // "Hello, World!"
var isEmail = "<EMAIL>".YIsValidEmail();     // true
var slug = "Hello World!".YToSlug();                  // "hello-world"
var analysis = "这是测试文本。".YAnalyzeText();        // 完整分析
```

**[📖 查看完整文档](YText/README.md)**

## 🔌 **架构和集成**

### **依赖注入指南**
>
> **企业级依赖注入支持，灵活的架构设计**

**核心特性：**

- 🏗️ 混合架构：静态扩展 + 依赖注入
- 🔧 灵活配置：单独注册和批量注册
- 🔄 可替换：自定义实现替换
- 🧪 易于测试：完整接口抽象

**快速示例：**

```csharp
// 服务注册
builder.Services.AddYCore();

// 服务使用
public class UserService
{
    private readonly IYConverter _converter;
    private readonly IYText _textService;
    
    public UserService(IYConverter converter, IYText textService)
    {
        _converter = converter;
        _textService = textService;
    }
}
```

**[📖 查看完整文档](DependencyInjection/README.md)**

## 🔄 **迁移和升级**

### **Bin.Shared 迁移指南**
>
> **从 Bin.Shared 迁移到现代化 Zylo.Core 的完整指南**

**迁移收益：**

- ✅ 安全性：永不抛异常的方法
- ✅ 功能性：50+ 个新增文本处理方法
- ✅ 架构性：完整的依赖注入支持
- ✅ 性能：优化的算法实现

**迁移示例：**

```csharp
// ❌ 旧版 Bin.Shared
int value = VCV.TOint("123");        // 可能抛异常

// ✅ 新版 Zylo.Core
int value = "123".YToInt(0);         // 安全转换
```

**[📖 查看完整文档](BinSharedMigration/README.md)**

## 📖 **学习路径**

### **🎯 按角色分类**

#### **👨‍💻 新手开发者**

1. [快速开始指南](QuickStart/README.md) - 了解基础概念
2. [YConverter 文档](YConverter/README.md) - 学习类型转换
3. [YCollection 文档](YCollection/README.md) - 掌握集合操作
4. [YText 文档](YText/README.md) - 体验文本处理

#### **🏢 企业开发者**

1. [依赖注入指南](DependencyInjection/README.md) - 架构设计
2. [快速开始指南](QuickStart/README.md) - 基础用法
3. 各模块详细文档 - 深入学习
4. 最佳实践和性能优化

#### **🔄 迁移用户**

1. [Bin.Shared 迁移指南](BinSharedMigration/README.md) - 迁移策略
2. [快速开始指南](QuickStart/README.md) - 新功能体验
3. 功能对比和升级收益
4. 渐进式迁移实践

### **🎯 按功能分类**

#### **🔄 类型转换需求**

- [YConverter 详细文档](YConverter/README.md)
- [依赖注入中的转换服务](DependencyInjection/README.md#yconverter-类型转换服务)
- [快速开始中的转换示例](QuickStart/README.md#类型转换---1分钟)

#### **📋 集合操作需求**

- [YCollection 详细文档](YCollection/README.md)
- [算法和高级操作](YCollection/README.md#算法实现)
- [LINQ 风格扩展](YCollection/README.md#linq-风格扩展)

#### **📝 文本处理需求**

- [YText 详细文档](YText/README.md)
- [文本验证功能](YText/README.md#文本验证)
- [文本分析功能](YText/README.md#文本分析)

## 🎨 **使用场景示例**

### **Web API 开发**

```csharp
[HttpPost]
public IActionResult CreateUser(CreateUserRequest request)
{
    // 输入验证
    if (!request.Email.YIsValidEmail())
        return BadRequest("Invalid email");
        
    // 数据处理
    var user = new User
    {
        Name = request.Name.YCleanText().YToTitleCase(),
        Age = request.Age.YToInt(18),
        Email = request.Email
    };
    
    return Ok(user);
}
```

### **数据处理**

```csharp
public class DataProcessor
{
    public ProcessedData Process(List<Dictionary<string, object>> rawData)
    {
        var scores = rawData
            .Select(row => row["score"].ToString().YToDouble(0.0))
            .Where(score => score > 0)
            .ToArray();
            
        return new ProcessedData
        {
            Average = scores.YAverage(0.0),
            Max = scores.YMax(0.0),
            Count = scores.Length
        };
    }
}
```

### **内容管理**

```csharp
public class ContentService
{
    public ProcessedContent ProcessArticle(string title, string content)
    {
        var analysis = content.YAnalyzeText();
        
        return new ProcessedContent
        {
            Title = title.YCleanText().YToTitleCase(),
            Slug = title.YToSlug(),
            Summary = content.YEllipsis(200),
            WordCount = analysis.WordCount,
            Language = analysis.DetectedLanguage
        };
    }
}
```

## 💡 **最佳实践**

### **1. 安全优先**

```csharp
// ✅ 推荐：使用 Y 前缀方法避免异常
var result = input.YToInt(0);

// ❌ 避免：直接转换可能抛异常
var result = int.Parse(input);
```

### **2. 充分利用默认值**

```csharp
// ✅ 推荐：提供有意义的默认值
var port = config["port"].YToInt(8080);
var timeout = config["timeout"].YToInt(30);
```

### **3. 链式操作**

```csharp
// ✅ 推荐：利用链式操作提高可读性
var result = data
    .Where(x => x.IsValid)
    .YOrderByDescending(x => x.Score)
    .YTakeWhile(x => x.Score > 80)
    .YSum();
```

## 🔗 **外部资源**

- [Zylo.Core NuGet 包](https://www.nuget.org/packages/Zylo.Core)
- [GitHub 仓库](https://github.com/your-org/zylo-core)
- [问题反馈](https://github.com/your-org/zylo-core/issues)
- [更新日志](../升级计划.md)

---

<div align="center">

**选择您需要的文档开始学习 Zylo.Core！** 📚

[🚀 快速开始](QuickStart/README.md) | [🔄 类型转换](YConverter/README.md) | [📋 集合操作](YCollection/README.md) | [📝 文本处理](YText/README.md)

</div>
