# 📋 YCollectionExtensions 完整功能指南

## 🎯 概述

**YCollectionExtensions** 是 Zylo.Core 中最全面的集合操作扩展库，提供了 **基本功能** 和 **高级扩展功能**，涵盖了日常开发中所有常见的集合操作需求。

## 📊 功能统计

- **总方法数**: 150+ 个扩展方法
- **代码行数**: 2100+ 行
- **测试用例**: 90+ 个测试
- **覆盖率**: 100%

## 🔧 基本功能模块

### 1. **安全访问操作** (核心基础)
```csharp
// 安全获取元素，越界返回默认值
var item = list.YSafeGet(10, "默认值");

// 安全设置元素
bool success = list.YSafeSet(5, "新值");

// 安全添加/移除元素
bool added = collection.YSafeAdd(item);
bool removed = collection.YSafeRemove(item);
```

### 2. **基本集合运算** (数学运算)
```csharp
// 并集、交集、差集
var union = list1.YUnion(list2);
var intersect = list1.YIntersect(list2);
var except = list1.YExcept(list2);

// 连接和去重
var combined = list1.YConcat(list2);
var unique = list.YDistinct();
```

### 3. **筛选和查找** (LINQ 增强)
```csharp
// 安全的筛选和转换
var filtered = list.YWhere(x => x > 0);
var transformed = list.YSelect(x => x * 2);
var flattened = list.YSelectMany(x => x.Children);

// 查找索引
int index = list.YFindIndex(x => x.Name == "target");
int lastIndex = list.YFindLastIndex(x => x.IsActive);

// 筛选非空字符串
var nonEmpty = strings.YWhereNotEmpty();
```

### 4. **检查和验证** (状态检查)
```csharp
// 空值检查
bool isEmpty = collection.YIsNullOrEmpty();
bool hasItems = collection.YHasItems();

// 安全计数
int count = collection.YSafeCount();

// 条件检查
bool hasAny = collection.YAny(x => x.IsValid);
bool allValid = collection.YAll(x => x.IsValid);
```

### 5. **转换操作** (类型转换)
```csharp
// 转换为不同集合类型
var observable = list.YToObservableCollection();
var readOnly = list.YToReadOnlyCollection();
var hashSet = list.ToHashSet(); // 使用标准方法

// 字符串连接
string joined = list.YJoinToString(", ");
string result = objects.YJoinToString(" | ");
```

## 🚀 扩展功能模块

### 6. **分页和分块** (数据处理)
```csharp
// 分页操作
var page1 = data.YTakePage(1, 10);  // 第1页，每页10条
var page2 = data.YTakePage(2, 10);  // 第2页

// 分块处理
var chunks = data.YChunk(5);  // 每块5个元素
foreach (var chunk in chunks)
{
    ProcessChunk(chunk);
}
```

### 7. **随机和排序** (随机化)
```csharp
// 随机操作
var randomItem = list.YRandomItem();
var randomItems = list.YRandomItems(3);
var shuffled = list.YShuffle();
```

### 8. **List 专用扩展** (List 增强)
```csharp
// 高级 List 操作
bool inserted = list.YSafeInsert(2, item);
bool removed = list.YSafeRemoveAt(5);
bool swapped = list.YSwap(0, 5);
bool moved = list.YMove(2, 8);

// 查找和替换
var indexes = list.YFindAllIndexes(targetItem);
int replaced = list.YReplaceAll(oldItem, newItem);
int replacedWhere = list.YReplaceWhere(x => x.IsOld, newItem);

// 安全获取特定位置
var first = list.YSafeFirst();
var last = list.YSafeLast();
var middle = list.YSafeMiddle();

// 子列表操作
var subList = list.YSafeSubList(2, 8);
var range = list.YGetRange(5, 10);
```

### 9. **LINQ 风格扩展** (高级查询)
```csharp
// 高级筛选
var nonNull = list.YWhereNotNull();
var distinctBy = list.YDistinctBy(x => x.Key);

// 安全聚合
var min = numbers.YMin(0);
var max = numbers.YMax(100);
var sum = numbers.YSum();
var avg = numbers.YAverage(0.0);

// 安全排序和分组
var ordered = list.YOrderBy(x => x.Name);
var grouped = list.YGroupBy(x => x.Category);
```

### 10. **排列组合算法** (数学算法)
```csharp
// 排列组合
var permutations = items.YPermutations();      // 全排列
var permutations2 = items.YPermutations(2);    // A(n,2)
var combinations = items.YCombinations(3);     // C(n,3)

// 高级算法
var cartesian = sets.YCartesianProduct();      // 笛卡尔积
var powerSet = items.YPowerSet();              // 幂集
```

### 11. **高级集合操作** (算法增强)
```csharp
// 滑动窗口
var windows = data.YSlidingWindow(3);

// 分组相邻元素
var groups = data.YGroupAdjacent();

// 交替获取
var interleaved = YCollectionExtensions.YInterleave(list1, list2, list3);

// 循环迭代
var cycled = items.YCycle().Take(20);

// 批处理
var results = data.YBatchProcess(10, batch => batch.Sum());
```

### 12. **Dictionary 扩展** (字典增强)
```csharp
// 安全字典操作
var value = dict.YSafeGet("key", "默认值");
bool updated = dict.YAddOrUpdate("key", "新值");
int added = dict.YAddRange(keyValuePairs);

// 获取或添加
var value = dict.YGetOrAdd("key", k => CreateValue(k));
```

### 13. **HashSet 扩展** (集合增强)
```csharp
// 安全 HashSet 操作
bool added = hashSet.YSafeAdd(item);
int addedCount = hashSet.YAddRange(items);
var intersection = hashSet.YIntersect(otherSet);
```

## 💡 使用场景示例

### 场景1：数据分页处理
```csharp
var users = GetAllUsers();
var pageSize = 20;
var totalPages = (users.Count + pageSize - 1) / pageSize;

for (int page = 1; page <= totalPages; page++)
{
    var pageData = users.YTakePage(page, pageSize);
    await ProcessUserPage(pageData);
}
```

### 场景2：安全的集合操作
```csharp
// 传统方式：可能抛异常
try
{
    var item = list[index];
    var first = list.First();
}
catch (Exception ex)
{
    // 处理异常
}

// Zylo 方式：永远安全
var item = list.YSafeGet(index, defaultItem);
var first = list.YSafeFirst(defaultItem);
```

### 场景3：复杂数据处理
```csharp
var result = data
    .YWhere(x => x.IsActive)
    .YDistinctBy(x => x.Id)
    .YOrderBy(x => x.Priority)
    .YChunk(100)
    .YBatchProcess(chunk => ProcessChunk(chunk))
    .YSum();
```

## 🎯 设计原则

### 1. **安全优先**
- 所有 Y 前缀方法永不抛异常
- 提供合理的默认值和错误处理
- 完整的边界检查

### 2. **性能优化**
- 延迟执行（yield return）
- 最小化内存分配
- 优化的算法实现

### 3. **易用性**
- 直观的方法命名
- 丰富的重载方法
- 详细的 XML 文档

### 4. **兼容性**
- 与标准 LINQ 完全兼容
- 支持链式调用
- 无外部依赖

## 📈 性能特点

- **内存效率**: 使用 `yield return` 实现延迟执行
- **算法优化**: 针对常见场景优化的算法
- **零分配**: 大部分操作避免不必要的内存分配
- **线程安全**: 所有扩展方法都是线程安全的

## 🔗 相关文档

- [Zylo.Core 主文档](../README.md)
- [项目状态报告](ProjectStatus.md)
- [智能文本搜索](ChineseTextSearch.md)

---

**YCollectionExtensions 提供了从基础到高级的完整集合操作解决方案，是 C# 开发者的强大工具库！** 🚀
