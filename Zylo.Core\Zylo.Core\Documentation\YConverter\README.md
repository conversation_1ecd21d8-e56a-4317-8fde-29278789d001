# 🔄 YConverter - 类型转换功能

> **安全、高效的类型转换工具** - 永不抛异常的转换方法

## 📋 **功能概览**

YConverter 提供 80+ 个安全的类型转换方法，支持所有常用数据类型的转换，并提供默认值和异常处理机制。

### **核心特性**
- 🛡️ **安全转换**: 所有方法都有异常保护，永不抛出异常
- 🎯 **默认值支持**: 转换失败时返回指定的默认值
- 🌏 **中文本土化**: 支持中文格式（如"是/否"转换为布尔值）
- 🔌 **依赖注入**: 完整的企业级 DI 支持
- ⚡ **高性能**: 优化的转换算法，最小的性能开销

## 🚀 **快速开始**

### **基础使用**
```csharp
using Zylo.Core;

// 基础类型转换
var age = "25".YToInt();                    // 25
var price = "99.99".YToDecimal();           // 99.99m
var isActive = "true".YToBool();            // true
var birthday = "2024-01-01".YToDateTime();  // DateTime

// 带默认值的转换
var age = "abc".YToInt(18);                 // 18 (转换失败返回默认值)
var score = "invalid".YToDouble(0.0);       // 0.0
var name = "".YToString("匿名");             // "匿名"
```

### **依赖注入使用**
```csharp
// 服务注册
builder.Services.AddYConverter();

// 服务使用
public class UserService
{
    private readonly IYConverter _converter;
    
    public UserService(IYConverter converter)
    {
        _converter = converter;
    }
    
    public User CreateUser(Dictionary<string, string> formData)
    {
        return new User
        {
            Age = _converter.SafeConvert<int>(formData["age"], 18),
            Salary = _converter.SafeConvert<decimal>(formData["salary"], 0m),
            IsActive = _converter.SafeConvert<bool>(formData["active"], true)
        };
    }
}
```

## 📚 **详细功能**

### **🔢 基础类型转换**

#### **数值类型转换**
```csharp
// 整数转换
"123".YToInt()                              // 123
"123".YToInt(0)                             // 123，失败返回0
"abc".YToInt(999)                           // 999 (转换失败)

// 浮点数转换
"123.45".YToDouble()                        // 123.45
"123.45".YToFloat()                         // 123.45f
"123.45".YToDecimal()                       // 123.45m

// 长整型转换
"9223372036854775807".YToLong()             // long.MaxValue
"123".YToShort()                            // (short)123
"255".YToByte()                             // (byte)255
```

#### **布尔类型转换**
```csharp
// 标准布尔转换
"true".YToBool()                            // true
"false".YToBool()                           // false
"1".YToBool()                               // true
"0".YToBool()                               // false

// 中文布尔转换
"是".YToBool()                               // true
"否".YToBool()                               // false
"对".YToBool()                               // true
"错".YToBool()                               // false

// 英文单词转换
"yes".YToBool()                             // true
"no".YToBool()                              // false
"on".YToBool()                              // true
"off".YToBool()                             // false
```

#### **日期时间转换**
```csharp
// 标准日期格式
"2024-01-01".YToDateTime()                  // DateTime(2024,1,1)
"2024/01/01 12:30:45".YToDateTime()         // 完整日期时间
"12:30:45".YToTimeSpan()                    // TimeSpan

// 带默认值的日期转换
"invalid".YToDateTime(DateTime.Now)         // 当前时间
"".YToTimeSpan(TimeSpan.Zero)               // TimeSpan.Zero

// 日期偏移转换
"2024-01-01T12:00:00+08:00".YToDateTimeOffset() // DateTimeOffset
```

### **🔤 字符串转换**

#### **字符串处理**
```csharp
// 基础字符串转换
123.YToString()                             // "123"
true.YToString()                            // "True"
DateTime.Now.YToString("yyyy-MM-dd")        // "2024-01-01"

// 安全字符串转换
((string?)null).YToString("默认值")          // "默认值"
"".YToString("空字符串")                      // "空字符串"

// 字符转换
"A".YToChar()                               // 'A'
"".YToChar('X')                             // 'X' (默认值)
```

### **📋 集合转换**

#### **数组转换**
```csharp
// 字符串数组转换
"apple,banana,cherry".YToStringArray()      // ["apple", "banana", "cherry"]
"apple|banana|cherry".YToStringArray('|')  // 自定义分隔符

// 数值数组转换
"1,2,3,4,5".YToIntArray()                   // [1, 2, 3, 4, 5]
"1.1,2.2,3.3".YToDoubleArray()              // [1.1, 2.2, 3.3]

// 布尔数组转换
"true,false,1,0".YToBoolArray()             // [true, false, true, false]
```

#### **集合转换**
```csharp
// List 转换
"a,b,c".YToStringList()                     // List<string>
"1,2,3".YToIntList()                        // List<int>

// HashSet 转换
"a,b,c,a".YToStringHashSet()                // HashSet<string> (去重)
"1,2,3,1".YToIntHashSet()                   // HashSet<int>

// Dictionary 转换
"key1=value1,key2=value2".YToStringDictionary() // Dictionary<string, string>
```

### **🔧 高级转换**

#### **可空类型转换**
```csharp
// 可空数值转换
"123".YToNullableInt()                      // (int?)123
"".YToNullableInt()                         // null
"invalid".YToNullableDouble()               // null

// 可空日期转换
"2024-01-01".YToNullableDateTime()          // (DateTime?)DateTime(2024,1,1)
"invalid".YToNullableDateTime()             // null
```

#### **枚举转换**
```csharp
public enum Status { Active, Inactive, Pending }

// 枚举转换
"Active".YToEnum<Status>()                  // Status.Active
"1".YToEnum<Status>()                       // Status.Inactive (按值)
"invalid".YToEnum(Status.Pending)           // Status.Pending (默认值)
```

#### **GUID 转换**
```csharp
// GUID 转换
"12345678-1234-1234-1234-123456789012".YToGuid()  // Guid
"invalid".YToGuid(Guid.Empty)               // Guid.Empty (默认值)
```

## 🔌 **依赖注入详解**

### **服务接口**
```csharp
public interface IYConverter
{
    T SafeConvert<T>(object? value, T defaultValue = default);
    T? SafeConvertNullable<T>(object? value) where T : struct;
    bool TryConvert<T>(object? value, out T result);
}
```

### **服务注册**
```csharp
// 注册转换服务
builder.Services.AddYConverter();

// 或注册所有 Zylo.Core 服务
builder.Services.AddYCore();

// 替换默认实现
builder.Services.ReplaceYConverter<CustomYConverter>();
```

### **服务使用示例**
```csharp
public class DataProcessor
{
    private readonly IYConverter _converter;
    
    public DataProcessor(IYConverter converter)
    {
        _converter = converter;
    }
    
    public ProcessedData ProcessRawData(Dictionary<string, object> rawData)
    {
        return new ProcessedData
        {
            Id = _converter.SafeConvert<int>(rawData["id"], 0),
            Name = _converter.SafeConvert<string>(rawData["name"], "Unknown"),
            Price = _converter.SafeConvert<decimal>(rawData["price"], 0m),
            IsActive = _converter.SafeConvert<bool>(rawData["active"], true),
            CreatedAt = _converter.SafeConvert<DateTime>(rawData["created"], DateTime.Now)
        };
    }
}
```

## 💡 **最佳实践**

### **1. 选择合适的转换方法**
```csharp
// ✅ 推荐：使用带默认值的转换
var age = userInput.YToInt(18);

// ❌ 避免：不提供默认值可能导致意外结果
var age = userInput.YToInt(); // 失败时返回 0
```

### **2. 处理用户输入**
```csharp
// ✅ 好的做法：提供有意义的默认值
public class UserRegistration
{
    public int Age { get; set; } = form["age"].YToInt(18);
    public bool IsSubscribed { get; set; } = form["subscribe"].YToBool(false);
    public decimal Salary { get; set; } = form["salary"].YToDecimal(0m);
}
```

### **3. 企业应用中使用依赖注入**
```csharp
// ✅ 推荐：在企业应用中使用依赖注入
public class ConfigurationService
{
    private readonly IYConverter _converter;
    private readonly IConfiguration _config;
    
    public ConfigurationService(IYConverter converter, IConfiguration config)
    {
        _converter = converter;
        _config = config;
    }
    
    public AppSettings GetSettings()
    {
        return new AppSettings
        {
            Timeout = _converter.SafeConvert<int>(_config["Timeout"], 30),
            MaxRetries = _converter.SafeConvert<int>(_config["MaxRetries"], 3),
            EnableLogging = _converter.SafeConvert<bool>(_config["EnableLogging"], true)
        };
    }
}
```

## 🧪 **测试覆盖**

YConverter 拥有完整的测试覆盖：
- **基础转换测试**: 80个测试用例
- **边界条件测试**: 25个测试用例  
- **异常处理测试**: 20个测试用例
- **依赖注入测试**: 15个测试用例
- **性能测试**: 10个测试用例

**总计**: 150个测试用例，100% 通过率

## 🔗 **相关文档**

- [YCollection - 集合操作](../YCollection/README.md)
- [YText - 文本处理](../YText/README.md)
- [依赖注入指南](../DependencyInjection/README.md)
- [快速开始](../QuickStart/README.md)
