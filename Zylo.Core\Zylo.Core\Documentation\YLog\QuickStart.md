# 🚀 YLog 快速开始指南

## 📋 5分钟上手YLog

YLog是一个基于源代码生成器的高性能日志框架，让您只需添加一个属性就能获得完整的日志记录功能。

## 🎯 第一步：项目配置

### 1. 添加项目引用

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
  </PropertyGroup>
  
  <ItemGroup>
    <ProjectReference Include="Zylo.AutoG.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="true" />
    <ProjectReference Include="Zylo.YLog.Runtime.csproj" />
  </ItemGroup>
</Project>
```

### 2. 程序启动配置

```csharp
using Zylo.YLog.Runtime;

static void Main()
{
    // 🔥 必须在程序启动时配置
    YLogger.ConfigureForDevelopment();
    
    // 你的应用程序代码...
}
```

## 🎨 第二步：编写代码

### 1. 创建服务类

```csharp
using Zylo.AutoG.Attributes.Logging;
using Zylo.YLog.Runtime;

// 🔥 类必须是 partial
public partial class UserService
{
    // 🆕 可以使用任意方法名，只需添加Core后缀
    [YLog]
    public string GetUserCore(int userId)
    {
        // 可以添加手动日志
        YLogger.Info("开始获取用户", "UserId", userId);
        
        if (userId <= 0)
        {
            YLogger.Warning("无效的用户ID", "UserId", userId);
            return "Invalid User";
        }
        
        return $"User {userId}";
    }
    
    // 🧬 完整泛型支持
    [YLog.Debug]
    public T ProcessDataCore<T>(T data) where T : class
    {
        YLogger.Info("处理数据", "Type", typeof(T).Name);
        return data;
    }
    
    // 🎯 性能监控
    [YLog.Performance(100)] // 超过100ms记录警告
    public async Task<string> SlowOperationCore()
    {
        await Task.Delay(150); // 故意超过阈值
        return "完成";
    }
}
```

### 2. 使用服务

```csharp
var service = new UserService();

// 🆕 调用时使用自然的方法名（自动去掉Core后缀）
var user = service.GetUser(123);
var data = service.ProcessData("Hello");
var result = await service.SlowOperation();
```

## 📊 第三步：查看日志输出

运行程序后，您将看到：

### 控制台输出
```
[2025-06-30 10:00:11.109] [INFORMATION] [T1] 开始获取用户 [UserId=123]
[2025-06-30 10:00:11.170] [INFORMATION] [T1] UserService.GetUser(123) => User 123 [61ms]
[2025-06-30 10:00:11.171] [INFORMATION] [T1] 处理数据 [Type=String]
[2025-06-30 10:00:11.172] [DEBUG] [T1] UserService.ProcessData(Hello) => Hello [1ms]
[2025-06-30 10:00:11.323] [WARNING] [T5] UserService.SlowOperation() => 完成 [151ms] (超过阈值100ms)
```

### 文件输出
日志文件位于：`bin/Debug/net8.0/Logs/YourApp_20250630.log`

## 🎨 第四步：高级功能

### 1. 便捷属性语法

```csharp
[YLog.Debug]        // Debug 级别
[YLog.Information]  // Information 级别
[YLog.Warning]      // Warning 级别
[YLog.Error]        // Error 级别
```

### 2. 敏感数据保护

```csharp
[YLog(logParameters: false)]  // 不记录参数
public bool ValidatePasswordCore(string password)
{
    // 密码不会出现在日志中
    return password?.Length >= 8;
}
```

### 3. 手动日志API

```csharp
// 基础日志
YLogger.Debug("调试信息");
YLogger.Info("操作成功");
YLogger.Warning("警告信息");
YLogger.Error("错误信息");

// 结构化日志
YLogger.Info("用户登录", "UserId", 123, "UserName", "张三");

// 异常日志
try 
{
    // 业务代码
}
catch (Exception ex)
{
    YLogger.Error("操作失败", ex);
}
```

## 🔧 第五步：配置优化

### 开发环境
```csharp
YLogger.ConfigureForDevelopment();
YLogger.SetFileNamingMode(LogFileNamingMode.Hourly); // 按小时分割
```

### 生产环境
```csharp
YLogger.ConfigureForProduction();
YLogger.SetConsoleOutput(false); // 关闭控制台输出
YLogger.SetMinimumLevel(LogLevel.Warning); // 只记录警告和错误
```

## 🎉 完成！

恭喜！您已经成功配置了YLog。现在您的应用程序拥有：

- ✅ 自动方法日志记录
- ✅ 性能监控
- ✅ 异常捕获
- ✅ 文件管理
- ✅ 泛型支持
- ✅ 灵活命名

## 📚 下一步

- [📖 查看完整文档](README.md) - 了解所有功能
- [🧪 查看测试示例](../../../YLogTest/) - 学习最佳实践
- [🔧 配置指南](README.md#配置管理) - 优化性能设置

---

**YLog - 让日志记录变得简单而强大！** 🔥
