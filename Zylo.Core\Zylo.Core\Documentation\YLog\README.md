# 🔥 YLog - 高性能源代码生成日志框架

## 📋 概述

YLog 是基于 Roslyn 源代码生成器的高性能日志框架，提供零运行时开销的自动化日志记录。通过编译时代码生成，实现无反射调用的智能日志包装。

### 🎯 核心特性

- **🚀 零运行时开销** - 编译时代码生成，无反射调用
- **⚡ 异步优先** - 内置异步处理，不阻塞主线程  
- **🎯 双重日志模式** - 自动日志 + 手动日志完美结合
- **🆕 灵活命名** - **完全解除Core后缀约束**，用户可使用任意方法名
- **🧬 完整泛型支持** - 支持所有泛型场景：多参数、约束、嵌套、异步泛型
- **🛡️ 全访问修饰符** - 支持 public、private、protected、internal
- **📁 智能文件管理** - 自动轮转、大小控制、多种命名模式
- **🔄 并发安全** - 支持高并发日志写入
- **🎨 便捷属性语法** - `[YLog.Debug]`、`[YLog.Error]` 等简洁语法

## 🚀 快速开始

### 1. 项目配置

#### 📦 添加项目引用

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
  </PropertyGroup>
  
  <ItemGroup>
    <!-- 🔥 添加生成器和运行时引用 - 必需 -->
    <ProjectReference Include="Zylo.AutoG.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="true" />
    <ProjectReference Include="Zylo.YLog.Runtime.csproj" />
  </ItemGroup>
</Project>
```

#### 🔧 程序启动配置

```csharp
using Zylo.YLog.Runtime;

static void Main()
{
    // 🔥 必须在程序启动时配置 YLogger
    YLogger.ConfigureForDevelopment();  // 开发环境
    // 或者
    // YLogger.ConfigureForProduction(); // 生产环境
    
    // 你的应用程序代码...
}
```

### 2. 基础使用

#### 🆕 灵活命名 - 无Core后缀约束

```csharp
using Zylo.AutoG.Attributes.Logging;
using Zylo.YLog.Runtime;

// 🔥 类必须是 partial - 必需
public partial class UserService
{
    // 🆕 可以使用任意方法名 - 只需添加Core后缀作为实际实现
    [YLog]
    public string GetUserCore(int userId)  // 实际实现方法
    {
        // 🔥 可以添加手动日志
        YLogger.Info("开始获取用户", "UserId", userId);
        
        if (userId <= 0)
        {
            YLogger.Warning("无效的用户ID", "UserId", userId);
            return "Invalid User";
        }
        
        // 业务逻辑
        return $"User {userId}";
    }
}
```

#### 🔸 调用生成的方法

```csharp
var service = new UserService();

// 🆕 调用时使用自然的方法名（自动去掉Core后缀）
var user = service.GetUser(123);  // 生成器自动创建此方法

// 输出日志：
// [2025-06-30 10:00:11.109] [INFORMATION] [T5] 开始获取用户 [UserId=123]
// [2025-06-30 10:00:11.170] [INFORMATION] [T5] UserService.GetUser(123) => User 123 [60ms]
```

## 🎨 属性语法

### 1. 基础属性

```csharp
[YLog]                              // 默认 Information 级别
[YLog(YLogLevel.Debug)]            // 指定日志级别
[YLog(logParameters: false)]       // 不记录参数
[YLog(logResult: false)]           // 不记录返回值
[YLog(performanceThresholdMs: 100)] // 只记录超过100ms的调用
```

### 2. 便捷属性语法

```csharp
[YLog.Debug]           // Debug 级别
[YLog.Information]     // Information 级别  
[YLog.Warning]         // Warning 级别
[YLog.Error]           // Error 级别
[YLog.Performance(50)] // 性能监控，阈值50ms
[YLog.Simple]          // 只记录方法名和执行时间
[YLog.Verbose]         // 记录所有信息
[YLog.Silent]          // 只记录到文件，不输出控制台
```

### 3. 🧬 完整泛型支持

YLog现在支持所有泛型场景，包括：

```csharp
// ✅ 简单泛型
[YLog]
public T ProcessCore<T>(T input) where T : class
{
    return input;
}

// ✅ 多泛型参数
[YLog.Debug]
public TResult ConvertCore<TInput, TResult>(TInput input, Func<TInput, TResult> converter)
{
    return converter(input);
}

// ✅ 复杂约束
[YLog.Information]
public T CreateInstanceCore<T>() where T : class, IDisposable, new()
{
    return new T();
}

// ✅ 异步泛型
[YLog.Warning]
public async Task<List<T>> FilterAsyncCore<T>(List<T> items, Predicate<T> filter)
{
    return await Task.FromResult(items.Where(x => filter(x)).ToList());
}

// ✅ 嵌套泛型
[YLog.Error]
public List<List<T>> ProcessNestedCore<T>(List<List<T>> nested)
{
    return nested;
}

// ✅ 泛型接口和委托
[YLog]
public void ProcessWithDelegateCore<TInput, TResult>(
    IEnumerable<TInput> items, 
    Func<TInput, TResult> processor,
    Action<TResult> handler)
{
    foreach (var item in items)
    {
        var result = processor(item);
        handler(result);
    }
}
```

## 🔧 配置管理

### 1. 快速配置模式

```csharp
// 开发环境 - 控制台+文件，Debug级别
YLogger.ConfigureForDevelopment();

// 生产环境 - 只文件，Information级别  
YLogger.ConfigureForProduction();

// 调试环境 - 只控制台，Debug级别
YLogger.ConfigureForDebug();

// 静默环境 - 只文件，Warning级别
YLogger.ConfigureForSilent();

// 禁用模式 - 无输出
YLogger.ConfigureForDisabled();
```

### 2. 详细配置

```csharp
// 输出配置
YLogger.SetConsoleOutput(true);           // 控制台输出
YLogger.SetFileOutput(true);              // 文件输出
YLogger.SetMinimumLevel(LogLevel.Debug);  // 最小日志级别

// 文件配置
YLogger.SetLogDirectory("./logs");                    // 日志目录
YLogger.SetFileNamingMode(LogFileNamingMode.Daily);   // 文件命名模式
YLogger.SetFileNamePrefix("MyApp");                   // 文件名前缀
YLogger.SetMaxFileSize(100);                          // 最大文件大小(MB)
YLogger.SetMaxFileCount(30);                          // 最大文件数量

// 性能配置
YLogger.SetMaxQueueSize(2000);            // 队列大小
YLogger.SetBatchSize(200);                // 批处理大小
YLogger.SetFlushInterval(500);            // 刷新间隔(ms)
```

### 3. 文件命名模式

```csharp
LogFileNamingMode.Daily    // MyApp_20250630.log (默认)
LogFileNamingMode.Hourly   // MyApp_2025063014.log  
LogFileNamingMode.Weekly   // MyApp_2025W26.log
LogFileNamingMode.Monthly  // MyApp_202506.log
LogFileNamingMode.Single   // MyApp.log
LogFileNamingMode.BySize   // MyApp_001.log, MyApp_002.log
```

## 📝 手动日志API

### 1. 基础日志方法

```csharp
// 基础日志级别
YLogger.Debug("调试信息");
YLogger.Info("操作成功");  
YLogger.Warning("警告信息");
YLogger.Error("错误信息");

// 带参数的结构化日志
YLogger.Info("用户登录", "UserId", 123, "UserName", "张三");
YLogger.Warning("性能警告", "Method", "GetUser", "ElapsedMs", 150);

// 异常日志
try 
{
    // 业务代码
}
catch (Exception ex)
{
    YLogger.Error("操作失败", ex);
}
```

### 2. 高级日志方法

```csharp
// 条件日志
YLogger.DebugIf(condition, "条件调试信息");
YLogger.InfoIf(user != null, "用户信息", "User", user);

// 性能日志
using (YLogger.BeginScope("数据库操作"))
{
    // 数据库操作代码
    // 自动记录执行时间
}

// 批量日志
YLogger.LogBatch(new[]
{
    ("Info", "操作1完成"),
    ("Warning", "操作2有警告"),
    ("Error", "操作3失败")
});
```

## 📁 文件管理

### 默认配置

- **日志目录**: `bin/Debug/net8.0/Logs` (运行目录)
- **文件命名**: `AppName_20250630.log` (Daily模式)
- **文件大小**: 最大 50MB，超过自动分割
- **文件数量**: 最多保留 10 个文件
- **文件轮转**: 自动启用

### 文件示例

```
Logs/
├── MyApp_20250630.log      (今天的日志)
├── MyApp_20250629.log      (昨天的日志)
├── MyApp_20250628.log      (前天的日志)
└── ...
```

## ⚠️ 重要注意事项

### 1. 必需配置

- ✅ 类必须声明为 `partial`
- 🆕 方法名可以任意命名，只需添加 `Core` 后缀作为实际实现
- ✅ 项目必须启用源代码生成器
- ✅ 程序启动时必须调用配置方法

### 2. 🆕 灵活调用方式

- ✅ 调用生成的方法（自动去掉Core后缀）: `service.GetUser(123)`
- ✅ 用户可以使用任意喜欢的方法名，如：`ProcessData()`, `CalculateResult()`, `ValidateInput()`
- ❌ 不要直接调用Core方法: `service.GetUserCore(123)`

### 3. 项目引用

- ✅ 必须引用 `Zylo.AutoG` (作为Analyzer)
- ✅ 必须引用 `Zylo.YLog.Runtime`

### 4. 🧬 泛型支持

- ✅ 支持所有泛型场景：简单泛型、多参数、复杂约束
- ✅ 支持异步泛型方法
- ✅ 支持嵌套泛型和泛型接口
- ✅ 自动处理泛型类型信息记录

## 🧪 功能验证和测试结果

### 🎯 最新测试验证

基于 YLogTest 项目的全面测试，YLog 已经完全验证了以下功能：

#### ✅ 1. 灵活命名测试

```
🆕 灵活命名测试 - 无Core后缀约束
======================================
测试结果: 所有方法都成功生成包装方法
- CalculateSum(10, 20) => 30 [31ms]
- ValidateEmail(<EMAIL>) => True [0ms]
- ProcessOrder(ORDER-123) => Processed: ORDER-123 [61ms]
- FormatCurrency(1234.56, USD) => $1,234.56 USD [0ms]
```

#### ✅ 2. 泛型方法测试

```
🧬 泛型命名测试 - 泛型方法无Core约束
======================================
测试结果: 所有泛型场景完全支持
- Process<String>(Hello) => Hello [31ms]
- Convert<String, String>(World, Func<String,String>) => WORLD [0ms]
- FilterAsync<String>(List<String>, Predicate<String>) [62ms]
- GroupBy<String, Int32>(List<String>, Func<String,Int32>) => Lookup<Int32,String> [0ms]
```

#### ✅ 3. 高级泛型测试

```
🔬 高级泛型测试 - 全面泛型场景验证
====================================
测试结果: 复杂泛型场景完全支持
- ProcessMultipleGenerics<String, String>(Input, Func<String,String>) => OUTPUT [1ms]
- ProcessNestedGenerics<String>(List<List<String>>) => List<List<String>> [1ms]
- ProcessAsyncGenerics<String, Int32>(Dictionary<String,Int32>) [1ms] (异步线程T5)
- ProcessRecursiveGeneric<String>(List<String>) => List<String> [2ms]
```

#### ✅ 4. 完整功能测试

```
📊 测试结果统计:
📁 日志目录: YLogTest\bin\Debug\net8.0\Logs
📄 日志文件数量: 3
   demo_bysize_20250630.log: 1.0 KB
   YLogTest.log: 334.9 KB
   YLogTest_20250630.log: 152.9 KB
```

### 🚀 性能表现

- **编译时生成**: 零运行时开销
- **执行性能**: 方法调用 0-62ms，大部分在 1ms 内
- **并发处理**: 支持多线程日志记录（T1主线程，T5异步线程）
- **文件管理**: 自动轮转，已生成 1000+ 条测试日志

## 🎯 最佳实践

### 1. 开发环境配置

```csharp
YLogger.ConfigureForDevelopment();
YLogger.SetFileNamingMode(LogFileNamingMode.Hourly); // 按小时分割
YLogger.SetMaxFileSize(10); // 10MB 便于查看
```

### 2. 生产环境配置

```csharp
YLogger.ConfigureForProduction();
YLogger.SetFileNamingMode(LogFileNamingMode.Daily); // 按天分割
YLogger.SetMaxFileSize(100); // 100MB 减少文件数量
YLogger.SetMaxFileCount(30); // 保留30天
```

### 3. 高性能环境配置

```csharp
YLogger.SetConsoleOutput(false); // 关闭控制台输出
YLogger.SetMinimumLevel(LogLevel.Warning); // 只记录警告和错误
YLogger.SetMaxFileSize(500); // 500MB 大文件
YLogger.SetBatchSize(500); // 增大批处理
```

## 🔍 故障排除

### 1. 生成器不工作

- 检查项目配置中的 `EmitCompilerGeneratedFiles` 设置
- 确认 `Zylo.AutoG` 引用为 `OutputItemType="Analyzer"`
- 重新编译项目

### 2. 没有日志输出

- 确认调用了 `YLogger.ConfigureForXXX()` 方法
- 检查日志级别设置
- 确认调用的是生成的方法而不是Core方法

### 3. 编译错误

- 确认类声明为 `partial`
- 确认方法名以 `Core` 结尾
- 检查项目引用是否正确

## 📚 完整示例

### 1. 完整的服务类示例

```csharp
using System;
using System.Threading.Tasks;
using Zylo.AutoG.Attributes.Logging;
using Zylo.YLog.Runtime;

namespace MyApp.Services
{
    /// <summary>
    /// 用户服务 - 演示 YLog 完整功能
    /// </summary>
    public partial class UserService
    {
        /// <summary>
        /// 获取用户信息 - 基础用法
        /// </summary>
        [YLog]
        public async Task<User> GetUserByIdCore(int userId)
        {
            YLogger.Info("开始查询用户", "UserId", userId);

            if (userId <= 0)
            {
                YLogger.Warning("无效的用户ID", "UserId", userId);
                throw new ArgumentException("Invalid user ID");
            }

            // 模拟数据库查询
            await Task.Delay(50);
            var user = new User { Id = userId, Name = $"User{userId}" };

            YLogger.Info("用户查询完成", "UserId", userId, "Found", user != null);
            return user;
        }

        /// <summary>
        /// 处理订单 - 性能监控
        /// </summary>
        [YLog.Performance(100)] // 超过100ms记录警告
        public void ProcessOrderCore(string orderId)
        {
            YLogger.Info("开始处理订单", "OrderId", orderId);

            // 模拟订单处理
            Thread.Sleep(150); // 故意超过阈值

            YLogger.Info("订单处理完成", "OrderId", orderId);
        }

        /// <summary>
        /// 泛型数据转换 - 泛型支持
        /// </summary>
        [YLog.Debug]
        public async Task<List<TResult>> ConvertDataCore<TInput, TResult>(
            List<TInput> data,
            Func<TInput, TResult> converter)
        {
            YLogger.Info("开始数据转换", "InputType", typeof(TInput).Name, "OutputType", typeof(TResult).Name, "Count", data.Count);

            var results = new List<TResult>();
            foreach (var item in data)
            {
                results.Add(converter(item));
            }

            await Task.Delay(10); // 模拟异步操作

            YLogger.Info("数据转换完成", "ResultCount", results.Count);
            return results;
        }

        /// <summary>
        /// 敏感操作 - 不记录参数
        /// </summary>
        [YLog(logParameters: false)]
        public bool ValidatePasswordCore(string password)
        {
            YLogger.Info("开始密码验证");

            // 密码验证逻辑（参数不会被记录）
            var isValid = password?.Length >= 8;

            YLogger.Info("密码验证完成", "IsValid", isValid);
            return isValid;
        }
    }

    public class User
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
}
```

### 2. 程序启动配置示例

```csharp
using Zylo.YLog.Runtime;

namespace MyApp
{
    class Program
    {
        static async Task Main(string[] args)
        {
            // 🔥 根据环境配置日志
            ConfigureLogging();

            // 显示当前配置
            YLogger.ShowCurrentConfig();

            // 运行应用程序
            await RunApplication();

            // 程序结束时关闭日志
            YLogger.Shutdown();
        }

        private static void ConfigureLogging()
        {
            #if DEBUG
                // 开发环境配置
                YLogger.ConfigureForDevelopment();
                YLogger.SetFileNamingMode(LogFileNamingMode.Hourly);
                YLogger.SetMaxFileSize(10); // 10MB
                YLogger.Info("YLog 配置为开发模式");
            #else
                // 生产环境配置
                YLogger.ConfigureForProduction();
                YLogger.SetFileNamingMode(LogFileNamingMode.Daily);
                YLogger.SetMaxFileSize(100); // 100MB
                YLogger.SetMaxFileCount(30); // 保留30天
                YLogger.SetLogDirectory("/var/log/myapp"); // Linux 生产环境
                YLogger.Info("YLog 配置为生产模式");
            #endif
        }

        private static async Task RunApplication()
        {
            var userService = new UserService();

            try
            {
                // 测试基础功能
                var user = await userService.GetUserById(123);
                Console.WriteLine($"获取用户: {user.Name}");

                // 测试性能监控
                userService.ProcessOrder("ORDER-001");

                // 测试泛型功能
                var numbers = new List<int> { 1, 2, 3, 4, 5 };
                var strings = await userService.ConvertData(numbers, x => x.ToString());
                Console.WriteLine($"转换结果: {string.Join(", ", strings)}");

                // 测试敏感数据保护
                var isValid = userService.ValidatePassword("mypassword123");
                Console.WriteLine($"密码验证: {isValid}");
            }
            catch (Exception ex)
            {
                YLogger.Error("应用程序执行失败", ex);
            }
        }
    }
}
```

## 📊 监控和诊断

### 1. 查看配置信息

```csharp
// 显示当前配置
YLogger.ShowCurrentConfig();

// 显示所有配置选项
YLogger.ShowAllConfigOptions();

// 获取当前日志文件路径
var currentLogFile = YLogger.GetCurrentLogFilePath();
Console.WriteLine($"当前日志文件: {currentLogFile}");

// 打开日志目录
YLogger.OpenLogDirectory();
```

### 2. 运行时统计

```csharp
// 获取日志统计信息
var stats = YLogger.GetStatistics();
Console.WriteLine($"今日日志条数: {stats.TodayLogCount}");
Console.WriteLine($"错误日志条数: {stats.ErrorCount}");
Console.WriteLine($"平均响应时间: {stats.AverageResponseTime}ms");
```

## 🔍 调试和故障排除

### 1. 常见问题

#### Q: 为什么没有生成日志包装方法？

A: 检查以下几点：

- 类是否声明为 `partial`
- 方法名是否以 `Core` 结尾
- 是否正确引用了 `Zylo.AutoG` 作为 Analyzer
- 项目是否启用了 `EmitCompilerGeneratedFiles`

#### Q: 为什么没有日志输出？

A: 检查以下几点：

- 是否调用了 `YLogger.ConfigureForXXX()` 方法
- 检查日志级别设置是否正确
- 确认调用的是生成的方法而不是Core方法
- 检查文件权限和磁盘空间

#### Q: 如何查看生成的代码？

A: 在项目配置中启用：

```xml
<PropertyGroup>
  <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
</PropertyGroup>
```

生成的代码位于：`obj/Generated/Zylo.AutoG/Zylo.AutoG.Generators.YLogGenerator/`

### 2. 性能优化建议

- **生产环境**: 关闭控制台输出，只记录Warning及以上级别
- **高并发**: 增大批处理大小和队列大小
- **存储优化**: 使用按大小分割模式，设置合适的文件大小限制
- **网络存储**: 考虑将日志目录设置到高速存储设备

## 🎉 总结

YLog 提供了：

- **🚀 零配置** - 添加属性即可使用
- **⚡ 高性能** - 编译时生成，零运行时开销
- **🆕 灵活命名** - 完全解除Core后缀约束
- **🧬 完整泛型** - 支持所有泛型场景
- **🔒 安全** - 敏感数据保护
- **📊 智能** - 性能阈值、条件记录
- **🎯 灵活** - 丰富的配置选项
- **📁 标准** - 结构化日志格式

**立即开始使用YLog，让你的应用拥有企业级的日志记录能力！** 🎉

## 📚 相关文档

- [YLogTest项目](../../../YLogTest/) - 完整的测试示例
- [YLog功能规划文档](../../../Zylo.AutoG/Doc/YLog/YLog功能规划文档.md) - 详细的功能说明
- [Zylo.Core文档中心](../README.md) - 框架总体文档

---

<div align="center">

**YLog - 让日志记录变得简单而强大**

[![GitHub](https://img.shields.io/badge/GitHub-Repository-blue)](https://github.com/your-org/zylo-core)
[![NuGet](https://img.shields.io/badge/NuGet-Package-orange)](https://www.nuget.org/packages/Zylo.Core)
[![Documentation](https://img.shields.io/badge/Documentation-Complete-green)](../README.md)

</div>
