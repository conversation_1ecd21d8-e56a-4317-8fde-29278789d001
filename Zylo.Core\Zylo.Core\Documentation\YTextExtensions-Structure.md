# YTextExtensions 文本处理扩展方法 - 结构说明

## 📋 概述

`YTextExtensions` 是 Zylo.Core 框架的核心文本处理类，提供了全面的文本操作、验证、格式化和分析功能。经过重新整理，现在具有清晰的模块化结构。

## 🏗️ 模块结构

### 1. 基础文本操作
**功能**：文本清理、标准化、基本操作
- `YTrimAll()` - 移除所有空白字符
- `YRemoveExtraSpaces()` - 移除多余空格
- `YCleanText()` - 综合文本清理
- `YRemoveSpecialChars()` - 移除特殊字符
- `YNormalizeWhitespace()` - 标准化空白字符

### 2. 文本截取和提取操作
**功能**：各种截取、提取、分割功能
- `YTruncate()` - 安全截取文本
- `YEllipsis()` - 截取并添加省略号
- `YLeft()` / `YRight()` / `YMid()` - 左右中截取
- `YSubstringAfter()` / `YSubstringBefore()` - 分隔符截取
- `YChunk()` - 文本分块
- `YSmartTruncate()` - 智能截取（单词边界）
- `YTruncateByBytes()` - 按字节截取（支持中文）

### 3. 智能文本搜索和查找操作 ⭐
**功能**：统一的智能搜索功能，支持中文、英文、符号
- `YFindAllPositions()` - 查找所有位置
- `YFindMultiplePositions()` - 多词同时搜索
- `YSearch()` - 详细搜索结果
- `YFindFirstPosition()` / `YFindLastPosition()` - 首末位置
- `YGetLeftContent()` / `YGetRightContent()` - 左右内容提取 ⭐
- `YGetSurroundingContent()` - 周围内容提取 ⭐
- `YFindWithContext()` - 带上下文的搜索 ⭐

### 4. 文本替换和转换操作
**功能**：文本替换、转换、模式匹配
- `YRegexReplace()` - 正则表达式替换
- `YReplaceMultiple()` - 多重替换
- `YRemovePattern()` - 移除匹配模式

### 5. 文本格式化操作
**功能**：各种格式化和大小写转换
- `YToTitleCase()` - 标题格式
- `YToCamelCase()` - 驼峰格式
- `YToPascalCase()` - 帕斯卡格式
- `YToKebabCase()` - 短横线格式
- `YToSnakeCase()` - 下划线格式
- `YCapitalize()` - 首字母大写
- `YReverse()` - 反转文本
- `YPadCenter()` - 居中填充

### 6. 文本验证和检查操作
**功能**：各种文本验证和检查
- `YIsNullOrWhiteSpace()` - 空白检查
- `YIsNumeric()` - 数字检查
- `YIsAlpha()` - 字母检查
- `YIsAlphaNumeric()` - 字母数字检查
- `YIsEmail()` - 邮箱验证
- `YIsUrl()` - URL验证
- `YIsPhoneNumber()` - 电话号码验证
- `YIsChineseChar()` - 中文字符检查 ⭐
- `YContainsChinese()` - 包含中文检查 ⭐
- `YIsPureChinese()` - 纯中文检查 ⭐
- `YGetChineseRatio()` - 中文字符比例 ⭐

### 7. 文本分析和统计操作
**功能**：文本分析、统计、评估
- `YWordCount()` - 单词计数
- `YCharacterCount()` - 字符计数
- `YLineCount()` - 行数统计
- `YSentenceCount()` - 句子计数
- `YCountChineseChars()` - 中文字符计数 ⭐
- `YExtractChineseChars()` - 提取中文字符 ⭐
- `YExtractChineseWords()` - 提取中文词汇 ⭐
- `YAnalyze()` - 综合文本分析
- `YCalculateSimilarity()` - 相似度计算
- `YExtractKeywords()` - 关键词提取

### 8. 高级文本处理操作
**功能**：复杂的文本处理功能
- `YToSlug()` - URL友好格式
- `YGenerateRandomString()` - 随机字符串生成
- `YLevenshteinDistance()` - 编辑距离计算
- `YSoundex()` - 语音相似度

### 9. 文本安全和编码操作
**功能**：安全、加密、编码相关
- `YMd5Hash()` - MD5哈希
- `YSha256Hash()` - SHA256哈希
- `YBase64Encode()` / `YBase64Decode()` - Base64编码
- `YUrlEncode()` / `YUrlDecode()` - URL编码
- `YHtmlEncode()` / `YHtmlDecode()` - HTML编码
- `YMask()` - 文本掩码
- `YMaskEmail()` / `YMaskPhone()` - 邮箱/电话掩码

### 10. 私有辅助方法
**功能**：内部使用的辅助方法
- 可读性评分计算
- 关键词提取算法
- 重音符号移除
- 中文标点符号检测

## 🌟 核心特性

### 智能搜索功能 ⭐
- **统一接口**：不区分中文、英文、符号，一套函数处理所有字符
- **精确定位**：字符级别的精确位置信息
- **上下文提取**：提供左右内容和完整上下文
- **重复处理**：正确处理重复和重叠匹配

### 中文支持 ⭐
- **全面支持**：完整的中文字符处理能力
- **智能识别**：自动识别中文字符和标点符号
- **统计分析**：中文字符统计和比例分析
- **词汇提取**：简单的中文分词功能

### 安全性
- **输入验证**：所有方法都进行 null 检查
- **边界保护**：避免数组越界和异常
- **编码安全**：提供多种编码和掩码功能

### 性能优化
- **高效算法**：使用优化的字符串处理算法
- **内存友好**：避免不必要的字符串复制
- **延迟执行**：支持 IEnumerable 的延迟执行

## 📊 使用统计

- **总方法数**：100+ 个扩展方法
- **功能模块**：9 个主要功能区域
- **中文支持**：20+ 个中文相关方法
- **搜索功能**：15+ 个智能搜索方法

## 🚀 使用示例

```csharp
// 基础操作
var cleaned = text.YCleanText();
var truncated = text.YSmartTruncate(100);

// 智能搜索 ⭐
var positions = text.YFindAllPositions("测试");
var results = text.YFindMultiplePositions(new[] { "框架", "C#", "！" });
var leftContent = text.YGetLeftContent(position, 5);
var contextResults = text.YFindWithContext("框架", 8, 8);

// 中文处理 ⭐
var chineseRatio = text.YGetChineseRatio();
var isPureChinese = text.YIsPureChinese();
var chineseWords = text.YExtractChineseWords();

// 格式化
var camelCase = text.YToCamelCase();
var slug = text.YToSlug();

// 验证
var isEmail = text.YIsEmail();
var containsChinese = text.YContainsChinese();

// 分析
var analysis = text.YAnalyze();
var similarity = text1.YCalculateSimilarity(text2);

// 安全
var masked = email.YMaskEmail();
var hash = text.YSha256Hash();
```

## 📝 版本历史

- **v2.0** - 重新整理模块结构，统一智能搜索功能
- **v1.5** - 新增中文文本处理功能
- **v1.0** - 初始版本，基础文本处理功能

## 🔗 相关文档

- [智能文本搜索功能详解](./ChineseTextSearch.md)
- [API 参考文档](./YTextExtensions-API.md)
- [使用示例集合](./YTextExtensions-Examples.md)
