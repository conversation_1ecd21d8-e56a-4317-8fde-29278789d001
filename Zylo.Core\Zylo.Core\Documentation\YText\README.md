# 📝 YText - 文本处理功能

> **全面的文本处理工具** - 清理、格式化、验证、分析一站式解决方案

## 📋 **功能概览**

YText 提供 50+ 个文本处理方法，涵盖文本操作、验证、格式化和分析的各个方面。

### **核心特性**
- 🧹 **文本清理**: 15个方法，处理空白、特殊字符、截取等
- 🎨 **文本格式化**: 10个方法，大小写转换、编码处理等
- ✅ **文本验证**: 12个方法，邮箱、手机、URL、中文验证
- 📊 **文本分析**: 8个方法，统计、语言检测、情感分析
- 🚀 **高级处理**: 5个方法，相似度、编辑距离、slug等
- 🔌 **依赖注入**: 完整的企业级 DI 支持

## 🚀 **快速开始**

### **基础使用**
```csharp
using Zylo.Core;

// 文本清理
var cleaned = "  Hello,   World!  \n\t".YCleanText();     // "Hello, World!"
var truncated = "Very long text".YTruncate(10);           // "Very long "
var ellipsis = "Very long text".YEllipsis(10);            // "Very lo..."

// 文本格式化
var title = "hello world".YToTitleCase();                 // "Hello World"
var camel = "hello world".YToCamelCase();                 // "helloWorld"
var slug = "Hello World!".YToSlug();                      // "hello-world"

// 文本验证
var isEmail = "<EMAIL>".YIsValidEmail();         // true
var isPhone = "13812345678".YIsValidChinesePhone();       // true
var hasChinese = "Hello 世界".YContainsChinese();          // true

// 文本分析
var wordCount = "Hello world from Zylo".YGetWordCount();  // 4
var analysis = "这是测试文本。".YAnalyzeText();
Console.WriteLine($"语言: {analysis.DetectedLanguage}");   // "zh"
```

### **依赖注入使用**
```csharp
// 服务注册
builder.Services.AddYText();

// 服务使用
public class ContentService
{
    private readonly IYText _textService;
    
    public ContentService(IYText textService)
    {
        _textService = textService;
    }
    
    public string ProcessContent(string input)
    {
        // 清理文本
        var cleaned = _textService.CleanText(input);
        
        // 截取长度
        var truncated = _textService.Truncate(cleaned, 200);
        
        // 格式化标题
        return _textService.ToTitleCase(truncated);
    }
}
```

## 📚 **详细功能**

### **🧹 文本基础操作**

#### **文本清理**
```csharp
// 移除空白字符
"  Hello   World  \t\n".YTrimAll()              // "HelloWorld"
"  Hello    World  ".YRemoveExtraSpaces()       // "Hello World"
"hello\t\nworld".YNormalizeWhitespace()         // "hello world"

// 清理特殊字符
"Hello@#World".YRemoveSpecialChars()            // "HelloWorld"
"Hello@#World".YRemoveSpecialChars("@")         // "Hello@World" (保留@)

// 综合清理
"  Hello,   World!  \n\t".YCleanText()          // "Hello, World!"
"  Hello,   World!  \n\t".YCleanText(false)     // "Hello World" (不保留标点)
```

#### **文本截取**
```csharp
// 基础截取
"Hello World".YTruncate(5)                      // "Hello"
"Hello World".YLeft(5)                          // "Hello"
"Hello World".YRight(5)                         // "World"

// 按单词截取
"Hello World Test".YTruncateWords(2)            // "Hello World"

// 省略号截取
"Hello World".YEllipsis(8)                      // "Hello..."
"Hello World".YEllipsis(8, "…")                 // "Hello…"
```

#### **文本查找**
```csharp
// 忽略大小写查找
"Hello".YContainsIgnoreCase("HELLO")            // true
"Hello".YIndexOfIgnoreCase("LLO")               // 2

// 统计和提取
"hello hello world".YCountOccurrences("hello")  // 2
"[Hello] [World]".YFindBetween("[", "]")        // "Hello"
"abc123def456".YExtractNumbers()                // ["123", "456"]
```

### **🎨 文本格式化**

#### **大小写转换**
```csharp
// 标准大小写转换
"hello world".YToTitleCase()                    // "Hello World"
"hello world".YToCamelCase()                    // "helloWorld"
"hello world".YToPascalCase()                   // "HelloWorld"
"Hello World".YToKebabCase()                    // "hello-world"

// 复杂文本处理
"user_name_field".YToCamelCase()                // "userNameField"
"UserNameField".YToKebabCase()                  // "user-name-field"
```

#### **文本格式化**
```csharp
// 模板格式化
"Hello {0}, welcome to {1}!".YFormatTemplate("John", "Zylo")  // "Hello John, welcome to Zylo!"

// 填充和重复
"Hi".YPadCenter(6)                              // "  Hi  "
"Hi".YPadCenter(6, '*')                         // "**Hi**"
"Hi".YRepeat(3)                                 // "HiHiHi"
```

#### **编码处理**
```csharp
// Base64 编码
"Hello World".YToBase64()                       // "SGVsbG8gV29ybGQ="
"SGVsbG8gV29ybGQ=".YFromBase64()                // "Hello World"

// URL 编码
"Hello World".YUrlEncode()                      // "Hello%20World"
"Hello%20World".YUrlDecode()                    // "Hello World"
```

### **✅ 文本验证**

#### **基础验证**
```csharp
// 空白检查
"   ".YIsNullOrWhiteSpace()                     // true
"Hello".YIsNullOrWhiteSpace()                   // false

// 字符类型检查
"123".YIsNumeric()                              // true
"Hello".YIsAlpha()                              // true
"Hello123".YIsAlphaNumeric()                    // true
```

#### **格式验证**
```csharp
// 邮箱验证
"<EMAIL>".YIsValidEmail()              // true
"invalid-email".YIsValidEmail()                 // false

// URL 验证
"https://example.com".YIsValidUrl()             // true
"invalid-url".YIsValidUrl()                     // false

// IP 地址验证
"***********".YIsValidIPAddress()               // true
"999.999.999.999".YIsValidIPAddress()           // false
```

#### **手机号验证**
```csharp
// 通用手机号验证
"13812345678".YIsValidPhone()                   // true (中国手机号)
"******-123-4567".YIsValidPhone()               // true (美国格式)

// 中国手机号专用验证
"13812345678".YIsValidChinesePhone()            // true
"12812345678".YIsValidChinesePhone()            // false (不是1开头的有效号段)
```

#### **中文验证**
```csharp
// 中文字符检查
"中文".YIsChineseChar()                          // true
"Hello".YIsChineseChar()                        // false
"Hello世界".YContainsChinese()                   // true

// 中国身份证验证
"110101199001011234".YIsValidChineseIdCard()    // true (示例，实际需要有效号码)
"123456789012345678".YIsValidChineseIdCard()    // false
```

### **📊 文本分析**

#### **统计分析**
```csharp
// 基础统计
"Hello world from Zylo".YGetWordCount()         // 4
"Hello World".YGetCharacterCount()              // 11
"Hello World".YGetCharacterCount(false)         // 10 (不包含空格)
"Line1\nLine2\nLine3".YGetLineCount()           // 3

// 阅读时间估算
"Long article content...".YGetReadingTime()     // TimeSpan (基于200词/分钟)
"Long article content...".YGetReadingTime(150)  // TimeSpan (自定义150词/分钟)
```

#### **综合分析**
```csharp
// 完整文本分析
var analysis = "这是一个中文测试文档。包含多个句子和段落。".YAnalyzeText();

Console.WriteLine($"字符数: {analysis.CharacterCount}");           // 总字符数
Console.WriteLine($"单词数: {analysis.WordCount}");               // 单词数量
Console.WriteLine($"行数: {analysis.LineCount}");                 // 行数
Console.WriteLine($"段落数: {analysis.ParagraphCount}");          // 段落数
Console.WriteLine($"句子数: {analysis.SentenceCount}");           // 句子数
Console.WriteLine($"包含中文: {analysis.ContainsChinese}");        // true
Console.WriteLine($"检测语言: {analysis.DetectedLanguage}");       // "zh"
Console.WriteLine($"可读性评分: {analysis.ReadabilityScore}");     // 0-100分
Console.WriteLine($"复杂度等级: {analysis.ComplexityLevel}");      // 1-5级
Console.WriteLine($"情感倾向: {analysis.SentimentScore}");        // -1到1
Console.WriteLine($"预计阅读时间: {analysis.EstimatedReadingTime}"); // TimeSpan
Console.WriteLine($"平均单词长度: {analysis.AverageWordLength}");   // 字符数
Console.WriteLine($"平均句子长度: {analysis.AverageSentenceLength}"); // 单词数

// 高频词汇和关键词
foreach (var word in analysis.TopWords.Take(5))
{
    Console.WriteLine($"高频词: {word.Key} ({word.Value}次)");
}

Console.WriteLine($"关键词: {string.Join(", ", analysis.Keywords.Take(10))}");
```

#### **语言和情感分析**
```csharp
// 语言检测
"Hello World".YGetLanguage()                    // "en"
"这是中文".YGetLanguage()                        // "zh"
"Bonjour le monde".YGetLanguage()               // "unknown" (简化检测)

// 情感分析
"Great job! Excellent work!".YGetSentiment()    // 正数 (积极)
"Terrible experience, very bad".YGetSentiment() // 负数 (消极)
"This is a neutral statement".YGetSentiment()   // 接近0 (中性)

// 关键词提取
"Hello world test example".YExtractKeywords(3)  // ["hello", "world", "test"]
"人工智能技术发展".YExtractKeywords(2)            // ["人工智能", "技术"]
```

### **🚀 高级文本处理**

#### **URL 友好处理**
```csharp
// Slug 生成
"Hello World!".YToSlug()                        // "hello-world"
"Café & Restaurant".YToSlug()                   // "cafe-restaurant"
"文章标题 - 副标题".YToSlug()                     // "文章标题-副标题"

// ASCII 转换
"Café".YToAscii()                               // "Cafe"
"Naïve résumé".YToAscii()                       // "Naive resume"
```

#### **文本简化**
```csharp
// 复杂文本简化
var complex = "Complex\ttext\nwith\r\nspecial chars!!!";
var simplified = complex.YSimplifyText();       // "Complex text with special chars!"
```

#### **相似度和距离**
```csharp
// 文本相似度 (Jaccard 相似度)
"hello world".YSimilarity("hello world")        // 1.0 (完全相同)
"hello world".YSimilarity("hello earth")        // 0.5 (50%相似)
"apple".YSimilarity("orange")                   // 0.0 (完全不同)

// 编辑距离 (Levenshtein 距离)
"hello".YLevenshteinDistance("hallo")           // 1 (需要1次编辑)
"kitten".YLevenshteinDistance("sitting")        // 3 (需要3次编辑)
"same".YLevenshteinDistance("same")             // 0 (相同)
```

## 🔌 **依赖注入详解**

### **服务接口**
```csharp
public interface IYText
{
    // 文本基础操作
    string TrimAll(string? text);
    string CleanText(string? text, bool keepBasicPunctuation = true);
    string Truncate(string? text, int maxLength);
    string Ellipsis(string? text, int maxLength, string ellipsis = "...");
    
    // 文本格式化
    string ToTitleCase(string? text);
    string ToCamelCase(string? text);
    string ToPascalCase(string? text);
    string ToKebabCase(string? text);
    
    // 文本验证
    bool IsValidEmail(string? text);
    bool IsValidPhone(string? text);
    bool ContainsChinese(string? text);
    
    // 文本分析
    int GetWordCount(string? text);
    YTextAnalysis AnalyzeText(string? text);
    string GetLanguage(string? text);
    
    // 高级处理
    string ToSlug(string? text);
    double CalculateSimilarity(string? text1, string? text2);
}
```

### **实际应用示例**
```csharp
// Web API 输入验证
[HttpPost]
public IActionResult CreateUser(CreateUserRequest request)
{
    if (!_textService.IsValidEmail(request.Email))
        return BadRequest("无效的邮箱格式");
    
    if (!_textService.IsValidChinesePhone(request.Phone))
        return BadRequest("无效的手机号格式");
    
    // 清理和格式化数据
    var user = new User
    {
        Name = _textService.ToTitleCase(_textService.CleanText(request.Name)),
        Bio = _textService.Truncate(_textService.CleanText(request.Bio), 500),
        Slug = _textService.ToSlug(request.Name)
    };
    
    return Ok(user);
}

// 内容管理系统
public class ArticleProcessor
{
    private readonly IYText _textService;
    
    public ProcessedArticle ProcessArticle(string content)
    {
        var analysis = _textService.AnalyzeText(content);
        
        return new ProcessedArticle
        {
            Content = _textService.CleanText(content),
            Summary = _textService.Ellipsis(content, 200),
            WordCount = analysis.WordCount,
            ReadingTime = analysis.EstimatedReadingTime,
            Language = analysis.DetectedLanguage,
            Sentiment = analysis.SentimentScore,
            Slug = _textService.ToSlug(content.Split('.')[0]) // 用第一句话生成slug
        };
    }
}
```

## 💡 **最佳实践**

### **1. 选择合适的方法**
```csharp
// ✅ 推荐：根据场景选择合适的清理方法
var userInput = _textService.CleanText(input, true);  // 保留标点
var searchTerm = _textService.CleanText(input, false); // 移除标点

// ✅ 推荐：使用专门的验证方法
var isValidPhone = _textService.IsValidChinesePhone(phone); // 中国手机号
var isValidEmail = _textService.IsValidEmail(email);        // 邮箱
```

### **2. 性能优化**
```csharp
// ✅ 推荐：对于大量文本，先进行基础检查
if (!string.IsNullOrWhiteSpace(content))
{
    var analysis = _textService.AnalyzeText(content);
    // 处理分析结果
}

// ✅ 推荐：缓存分析结果
private readonly MemoryCache _analysisCache = new();

public YTextAnalysis GetCachedAnalysis(string content)
{
    return _analysisCache.GetOrCreate(content.GetHashCode(), 
        _ => _textService.AnalyzeText(content));
}
```

### **3. 国际化支持**
```csharp
// ✅ 推荐：根据语言选择合适的处理方法
var language = _textService.GetLanguage(content);
if (language == "zh")
{
    // 中文特殊处理
    var isValidId = _textService.IsValidChineseIdCard(idCard);
}
else
{
    // 英文或其他语言处理
    var slug = _textService.ToSlug(content);
}
```

## 🧪 **测试覆盖**

YText 拥有完整的测试覆盖：
- **文本基础操作测试**: 30个测试用例
- **文本格式化测试**: 25个测试用例
- **文本验证测试**: 25个测试用例
- **文本分析测试**: 20个测试用例
- **高级处理测试**: 10个测试用例
- **依赖注入测试**: 6个测试用例

**总计**: 116个测试用例，100% 通过率

## 🔗 **相关文档**

- [YConverter - 类型转换](../YConverter/README.md)
- [YCollection - 集合操作](../YCollection/README.md)
- [依赖注入指南](../DependencyInjection/README.md)
- [快速开始](../QuickStart/README.md)
