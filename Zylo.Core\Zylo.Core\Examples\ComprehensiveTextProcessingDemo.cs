using System;
using System.Linq;
using System.Text;

namespace Zylo.Core.Examples;

/// <summary>
/// YTextExtensions 综合文本处理应用示例
/// 展示各种实际使用场景和功能
/// </summary>
public static class ComprehensiveTextProcessingDemo
{
    /// <summary>
    /// 运行综合文本处理应用示例演示
    /// </summary>
    public static void RunDemo()
    {
        Console.WriteLine("=== Zylo.Core 文本处理综合应用示例 ===\n");

        // 1. 用户输入数据清理场景
        DemoDataCleaning();

        // 2. 文档内容分析场景
        DemoDocumentAnalysis();

        // 3. 用户信息处理场景
        DemoUserDataProcessing();

        // 4. 搜索和高亮场景
        DemoSearchAndHighlight();

        // 5. 文本格式转换场景
        DemoTextFormatting();

        // 6. 内容验证场景
        DemoContentValidation();

        // 7. 敏感信息处理场景
        DemoSensitiveDataHandling();

        // 8. 多语言文本处理场景
        DemoMultiLanguageProcessing();

        // 9. 实际业务应用示例
        DemoBusinessApplication();

        Console.WriteLine("\n=== 综合示例演示完成 ===");
    }

    /// <summary>
    /// 场景1: 用户输入数据清理
    /// </summary>
    private static void DemoDataCleaning()
    {
        Console.WriteLine("📋 场景1: 用户输入数据清理");
        Console.WriteLine(new string('-', 40));

        // 模拟用户输入的脏数据
        var userInput = "  Hello   World!!! \t\n  这是一个测试@#$%^&*()文本   \r\n  ";
        Console.WriteLine($"原始输入: '{userInput}'");

        // 基础清理
        var cleaned = userInput.YCleanText();
        Console.WriteLine($"基础清理: '{cleaned}'");

        // 移除特殊字符，保留基本标点
        var noSpecial = userInput.YRemoveSpecialChars("!.,");
        Console.WriteLine($"移除特殊字符: '{noSpecial}'");

        // 标准化空白
        var normalized = userInput.YNormalizeWhitespace();
        Console.WriteLine($"标准化空白: '{normalized}'");

        // 智能截取
        var truncated = cleaned.YSmartTruncate(20, "...");
        Console.WriteLine($"智能截取(20字符): '{truncated}'");

        Console.WriteLine();
    }

    /// <summary>
    /// 场景2: 文档内容分析
    /// </summary>
    private static void DemoDocumentAnalysis()
    {
        Console.WriteLine("📊 场景2: 文档内容分析");
        Console.WriteLine(new string('-', 40));

        var document = @"人工智能技术正在快速发展。机器学习和深度学习是人工智能的核心技术。
        在医疗、教育、金融等领域，人工智能都有广泛的应用。
        未来，人工智能将会改变我们的生活方式。
        The future of AI is bright. Machine learning will continue to evolve.";

        Console.WriteLine("文档内容:");
        Console.WriteLine(document);
        Console.WriteLine();

        // 基础统计
        Console.WriteLine("📈 基础统计:");
        Console.WriteLine($"  总字符数: {document.Length}");
        Console.WriteLine($"  单词数: {document.YGetWordCount()}");
        Console.WriteLine($"  行数: {document.YGetLineCount()}");
        Console.WriteLine($"  中文字符数: {document.YCountChineseChars()}");
        Console.WriteLine($"  中文字符比例: {document.YGetChineseRatio():P1}");

        // 关键词提取 (简化版本)
        var words = document.YExtractChineseWords();
        Console.WriteLine($"  主要词汇: [{string.Join(", ", words.Take(5))}]");

        // 中文词汇提取
        var chineseWords = document.YExtractChineseWords();
        Console.WriteLine($"  中文词汇: [{string.Join(", ", chineseWords.Take(5))}...]");

        // 数字提取
        var numbers = document.YExtractNumbers();
        if (numbers.Length > 0)
            Console.WriteLine($"  提取的数字: [{string.Join(", ", numbers)}]");

        Console.WriteLine();
    }

    /// <summary>
    /// 场景3: 用户信息处理
    /// </summary>
    private static void DemoUserDataProcessing()
    {
        Console.WriteLine("👤 场景3: 用户信息处理");
        Console.WriteLine(new string('-', 40));

        // 模拟用户数据
        var userData = new
        {
            Name = "张三丰",
            Email = "<EMAIL>",
            Phone = "13812345678",
            Address = "北京市朝阳区某某街道123号",
            Bio = "我是一名软件工程师，专注于C#开发。喜欢学习新技术，目前在研究人工智能。"
        };

        Console.WriteLine("原始用户数据:");
        Console.WriteLine($"  姓名: {userData.Name}");
        Console.WriteLine($"  邮箱: {userData.Email}");
        Console.WriteLine($"  电话: {userData.Phone}");
        Console.WriteLine($"  地址: {userData.Address}");
        Console.WriteLine($"  简介: {userData.Bio}");
        Console.WriteLine();

        // 数据验证
        Console.WriteLine("🔍 数据验证:");
        Console.WriteLine($"  姓名是否为中文: {userData.Name.YIsPureChinese()}");
        Console.WriteLine($"  邮箱包含@符号: {userData.Email.Contains("@")}");
        Console.WriteLine($"  电话是否为数字: {userData.Phone.YIsNumeric()}");

        // 敏感信息掩码
        Console.WriteLine("\n🔒 敏感信息掩码:");
        Console.WriteLine($"  邮箱掩码: {userData.Email.YMask(3, 3)}");
        Console.WriteLine($"  电话掩码: {userData.Phone.YMask(3, 4)}");

        // 文本分析
        Console.WriteLine("\n📝 简介分析:");
        Console.WriteLine($"  字符数: {userData.Bio.Length}");
        Console.WriteLine($"  单词数: {userData.Bio.YGetWordCount()}");
        Console.WriteLine($"  中文字符比例: {userData.Bio.YGetChineseRatio():P1}");
        Console.WriteLine($"  包含中文: {userData.Bio.YContainsChinese()}");

        Console.WriteLine();
    }

    /// <summary>
    /// 场景4: 搜索和高亮
    /// </summary>
    private static void DemoSearchAndHighlight()
    {
        Console.WriteLine("🔍 场景4: 搜索和高亮");
        Console.WriteLine(new string('-', 40));

        var content = @"在现代软件开发中，C#是一种非常流行的编程语言。
        C#具有强类型、面向对象的特性，广泛应用于Web开发、桌面应用和游戏开发。
        学习C#可以帮助开发者构建高质量的应用程序。";

        Console.WriteLine("原始内容:");
        Console.WriteLine(content);
        Console.WriteLine();

        // 搜索关键词
        var searchTerms = new[] { "C#", "开发", "应用" };
        Console.WriteLine($"搜索关键词: [{string.Join(", ", searchTerms)}]");
        Console.WriteLine();

        // 多词搜索
        var searchResults = content.YFindMultiplePositions(searchTerms);
        foreach (var result in searchResults)
        {
            if (result.Found)
            {
                Console.WriteLine($"'{result.SearchText}' - 找到{result.Count}次，位置: [{string.Join(", ", result.Positions)}]");
                
                // 显示上下文
                var contexts = content.YFindWithContext(result.SearchText, 8, 8);
                foreach (var context in contexts.Take(2))
                {
                    Console.WriteLine($"  上下文: {context.GetFormattedContext("【", "】")}");
                }
            }
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 场景5: 文本格式转换
    /// </summary>
    private static void DemoTextFormatting()
    {
        Console.WriteLine("🎨 场景5: 文本格式转换");
        Console.WriteLine(new string('-', 40));

        var originalText = "user management system";
        Console.WriteLine($"原始文本: '{originalText}'");
        Console.WriteLine();

        // 各种格式转换
        Console.WriteLine("格式转换结果:");
        Console.WriteLine($"  驼峰格式: {originalText.YToCamelCase()}");
        Console.WriteLine($"  帕斯卡格式: {originalText.YToPascalCase()}");
        Console.WriteLine($"  短横线格式: {originalText.YToKebabCase()}");
        Console.WriteLine($"  标题格式: {originalText.YToTitleCase()}");

        // URL友好格式 (简化版本)
        var title = "这是一个中文标题 - 测试URL转换";
        Console.WriteLine($"\n标题: '{title}'");
        var urlSlug = title.YRemoveSpecialChars("-").YToKebabCase();
        Console.WriteLine($"URL Slug: {urlSlug}");

        // 编码转换
        var sensitiveData = "敏感信息123";
        Console.WriteLine($"\n原始数据: '{sensitiveData}'");
        Console.WriteLine($"Base64编码: {sensitiveData.YToBase64()}");
        Console.WriteLine($"URL编码: {sensitiveData.YUrlEncode()}");

        Console.WriteLine();
    }

    /// <summary>
    /// 场景6: 内容验证
    /// </summary>
    private static void DemoContentValidation()
    {
        Console.WriteLine("✅ 场景6: 内容验证");
        Console.WriteLine(new string('-', 40));

        // 测试各种输入
        var testInputs = new[]
        {
            ("", "空字符串"),
            ("   ", "空白字符串"),
            ("123456", "纯数字"),
            ("abcdef", "纯字母"),
            ("abc123", "字母数字混合"),
            ("张三", "纯中文"),
            ("Hello世界", "中英混合"),
            ("<EMAIL>", "邮箱格式"),
            ("https://www.example.com", "URL格式"),
            ("13812345678", "电话号码")
        };

        Console.WriteLine("输入验证结果:");
        foreach (var (input, description) in testInputs)
        {
            Console.WriteLine($"\n  {description}: '{input}'");
            Console.WriteLine($"    是否为空: {input.YIsNullOrWhiteSpace()}");
            Console.WriteLine($"    是否数字: {input.YIsNumeric()}");
            Console.WriteLine($"    是否字母: {input.YIsAlpha()}");
            Console.WriteLine($"    是否字母数字: {input.YIsAlphaNumeric()}");
            Console.WriteLine($"    包含中文: {input.YContainsChinese()}");
            Console.WriteLine($"    包含@符号: {input.Contains("@")}");
            Console.WriteLine($"    包含http: {input.YContainsIgnoreCase("http")}");
            Console.WriteLine($"    长度合理: {input.Length >= 8 && input.Length <= 15}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 场景7: 敏感信息处理
    /// </summary>
    private static void DemoSensitiveDataHandling()
    {
        Console.WriteLine("🔐 场景7: 敏感信息处理");
        Console.WriteLine(new string('-', 40));

        // 模拟包含敏感信息的文本
        var sensitiveText = @"用户张三的邮箱是********************，电话号码是13812345678。
        身份证号码是110101199001011234，银行卡号是6222021234567890123。
        请联系李四，邮箱****************，手机18987654321。";

        Console.WriteLine("原始文本:");
        Console.WriteLine(sensitiveText);
        Console.WriteLine();

        // 查找邮箱
        var emailPattern = @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b";
        var emails = sensitiveText.YMatchRegex(emailPattern);
        Console.WriteLine($"发现的邮箱: [{string.Join(", ", emails)}]");

        // 查找电话号码
        var phonePattern = @"1[3-9]\d{9}";
        var phones = sensitiveText.YMatchRegex(phonePattern);
        Console.WriteLine($"发现的电话: [{string.Join(", ", phones)}]");

        // 敏感信息掩码处理
        var maskedText = sensitiveText;
        foreach (var email in emails)
        {
            maskedText = maskedText.Replace(email, email.YMask(3, 3));
        }
        foreach (var phone in phones)
        {
            maskedText = maskedText.Replace(phone, phone.YMask(3, 4));
        }

        Console.WriteLine("\n掩码处理后:");
        Console.WriteLine(maskedText);

        Console.WriteLine();
    }

    /// <summary>
    /// 场景8: 多语言文本处理
    /// </summary>
    private static void DemoMultiLanguageProcessing()
    {
        Console.WriteLine("🌍 场景8: 多语言文本处理");
        Console.WriteLine(new string('-', 40));

        var multiLangText = @"Hello世界! Bonjour monde! 你好世界!
        This is a multilingual text example. 这是一个多语言文本示例。
        Programming编程 is fun有趣! Let's code一起编程吧!";

        Console.WriteLine("多语言文本:");
        Console.WriteLine(multiLangText);
        Console.WriteLine();

        // 语言分析
        Console.WriteLine("🔍 语言分析:");
        Console.WriteLine($"  总字符数: {multiLangText.Length}");
        Console.WriteLine($"  中文字符数: {multiLangText.YCountChineseChars()}");
        Console.WriteLine($"  中文字符比例: {multiLangText.YGetChineseRatio():P1}");
        Console.WriteLine($"  包含中文: {multiLangText.YContainsChinese()}");

        // 提取不同语言内容
        var chineseChars = multiLangText.YExtractChineseChars();
        var chineseWords = multiLangText.YExtractChineseWords();
        var letters = multiLangText.YExtractLetters();
        var numbers = multiLangText.YExtractNumbers();

        Console.WriteLine($"\n📝 内容提取:");
        Console.WriteLine($"  中文字符: [{string.Join("", chineseChars.Take(20))}...]");
        Console.WriteLine($"  中文词汇: [{string.Join(", ", chineseWords)}]");
        Console.WriteLine($"  字母内容: {letters.YLeft(50)}...");
        Console.WriteLine($"  数字内容: [{string.Join(", ", numbers)}]");

        // 智能搜索测试
        var searchTerms = new[] { "Hello", "世界", "编程", "fun", "!" };
        Console.WriteLine($"\n🔍 多语言搜索测试:");
        Console.WriteLine($"搜索词: [{string.Join(", ", searchTerms)}]");

        var searchResults = multiLangText.YFindMultiplePositions(searchTerms);
        foreach (var result in searchResults.Where(r => r.Found))
        {
            Console.WriteLine($"  '{result.SearchText}': 找到{result.Count}次");
            var contexts = multiLangText.YFindWithContext(result.SearchText, 6, 6);
            foreach (var context in contexts.Take(1))
            {
                Console.WriteLine($"    上下文: {context.GetFormattedContext("【", "】")}");
            }
        }

        // 文本长度比较
        var text1 = "Hello世界";
        var text2 = "Hello world";
        Console.WriteLine($"\n📊 文本比较:");
        Console.WriteLine($"  '{text1}' 长度: {text1.Length}, 中文字符: {text1.YCountChineseChars()}");
        Console.WriteLine($"  '{text2}' 长度: {text2.Length}, 中文字符: {text2.YCountChineseChars()}");

        Console.WriteLine();
    }

    /// <summary>
    /// 场景9: 实际业务应用示例
    /// </summary>
    private static void DemoBusinessApplication()
    {
        Console.WriteLine("💼 场景9: 实际业务应用");
        Console.WriteLine(new string('-', 40));

        // 模拟用户评论处理
        var userComments = new[]
        {
            "这个产品真的很好用！！！推荐给大家   ",
            "   质量一般般，价格有点贵。。。",
            "customer service is excellent! 客服态度很好 😊",
            "发货速度快，包装完整，满意！",
            "   "
        };

        Console.WriteLine("用户评论处理:");
        for (int i = 0; i < userComments.Length; i++)
        {
            var comment = userComments[i];
            Console.WriteLine($"\n评论 {i + 1}: '{comment}'");

            // 数据清理和验证
            if (comment.YIsNullOrWhiteSpace())
            {
                Console.WriteLine("  状态: 空评论，已过滤");
                continue;
            }

            var cleaned = comment.YCleanText();
            var truncated = cleaned.YEllipsis(50);

            Console.WriteLine($"  清理后: '{cleaned}'");
            Console.WriteLine($"  截取显示: '{truncated}'");
            Console.WriteLine($"  字符数: {cleaned.Length}");
            Console.WriteLine($"  包含中文: {cleaned.YContainsChinese()}");
            Console.WriteLine($"  情感词汇: {(cleaned.YContainsIgnoreCase("好") || cleaned.YContainsIgnoreCase("excellent") ? "正面" : cleaned.YContainsIgnoreCase("一般") || cleaned.YContainsIgnoreCase("贵") ? "负面" : "中性")}");
        }

        Console.WriteLine();
    }
}
