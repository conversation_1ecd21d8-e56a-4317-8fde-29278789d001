// Zylo.Core GlobalUsings.cs
// 核心基础工具的全局引用 - 无外部依赖

// .NET 核心命名空间
global using System;
global using System.Collections;
global using System.Collections.Generic;
global using System.Collections.Concurrent;
global using System.Linq;
global using System.Text;
global using System.Text.RegularExpressions;
global using System.Threading;
global using System.Threading.Tasks;
global using System.Globalization;
global using System.Runtime.CompilerServices;

// 异步和性能相关
global using System.Buffers;

// 数学和转换
global using System.Numerics;

// 验证和格式化
global using System.ComponentModel.DataAnnotations;

// Zylo.Core 内部命名空间 - 暂时注释，等模块创建后再启用
// global using Zylo.Core.YValidation;
// global using Zylo.Core.YDateTime;
// global using Zylo.Core.YCollection;
// global using Zylo.Core.YText;
// global using Zylo.Core.YAsync;

// 注意：Attributes 已移动到 Zylo.AutoRegister 项目
// 如需使用源代码生成功能，请安装 Zylo.AutoRegister 包
