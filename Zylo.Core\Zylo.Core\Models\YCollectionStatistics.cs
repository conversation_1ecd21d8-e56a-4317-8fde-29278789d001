namespace Zylo.Core.Models;

/// <summary>
/// 集合统计信息
/// </summary>
public class YCollectionStatistics
{
    /// <summary>
    /// 总元素数量
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 唯一元素数量
    /// </summary>
    public int UniqueCount { get; set; }

    /// <summary>
    /// 重复元素种类数量
    /// </summary>
    public int DuplicateCount { get; set; }

    /// <summary>
    /// 重复元素实例数量（总重复次数）
    /// </summary>
    public int DuplicateItemCount { get; set; }

    /// <summary>
    /// 是否为空集合
    /// </summary>
    public bool IsEmpty { get; set; }

    /// <summary>
    /// 是否包含重复元素
    /// </summary>
    public bool HasDuplicates { get; set; }

    /// <summary>
    /// 重复率（重复元素数量 / 总元素数量）
    /// </summary>
    public double DuplicateRate => TotalCount > 0 ? (double)DuplicateItemCount / TotalCount : 0.0;

    /// <summary>
    /// 唯一率（唯一元素数量 / 总元素数量）
    /// </summary>
    public double UniqueRate => TotalCount > 0 ? (double)UniqueCount / TotalCount : 0.0;

    /// <summary>
    /// 返回统计信息的字符串表示
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public override string ToString()
    {
        return $"总数: {TotalCount}, 唯一: {UniqueCount}, 重复: {DuplicateCount}, 重复率: {DuplicateRate:P2}";
    }
}

/// <summary>
/// 数学统计信息
/// </summary>
public class YMathStatistics
{
    /// <summary>
    /// 数据数量
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 总和
    /// </summary>
    public double Sum { get; set; }

    /// <summary>
    /// 平均值
    /// </summary>
    public double Mean { get; set; }

    /// <summary>
    /// 中位数
    /// </summary>
    public double Median { get; set; }

    /// <summary>
    /// 众数
    /// </summary>
    public double Mode { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public double Min { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public double Max { get; set; }

    /// <summary>
    /// 范围（最大值 - 最小值）
    /// </summary>
    public double Range { get; set; }

    /// <summary>
    /// 方差
    /// </summary>
    public double Variance { get; set; }

    /// <summary>
    /// 标准差
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// 返回统计信息的字符串表示
    /// </summary>
    /// <returns>统计信息字符串</returns>
    public override string ToString()
    {
        return $"数量: {Count}, 平均: {Mean:F2}, 中位: {Median:F2}, 标准差: {StandardDeviation:F2}";
    }
}
