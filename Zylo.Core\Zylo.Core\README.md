# 🚀 Zylo.Core

<div align="center">

![Version](https://img.shields.io/badge/Version-2.3.0-green)
![Framework](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)
![Dual Container](https://img.shields.io/badge/Dual_Container-DryIoc%20%2B%20Microsoft.DI-gold)
![License](https://img.shields.io/badge/License-MIT-orange)
![Tests](https://img.shields.io/badge/Tests-344%2F344-brightgreen)
![New](https://img.shields.io/badge/New-Dual%20Container%20Support-red)

**现代化的 C# 核心工具库 - 安全、高效、易用的开发解决方案**
**🚀 全新双容器支持架构 - DryIoc + Microsoft.Extensions.DI**

</div>

## 🎯 **简介**

Zylo.Core 是 Zylo 工具库生态系统的核心基础模块，提供全面的类型转换、集合操作和文本处理功能。采用混合架构设计，既支持简洁的静态扩展方法，又支持企业级的依赖注入。

### **核心特性**
- 🔄 **类型转换**: 80+ 个安全转换方法，永不抛异常
- 📋 **集合操作**: 150+ 个集合方法，LINQ 风格链式操作
- 📝 **文本处理**: 50+ 个文本方法，清理、格式化、验证、分析
- 🛡️ **安全优先**: Y 前缀方法提供完整的边界保护
- 🌏 **中文支持**: 专门的中文验证和处理功能
- 🎯 **多框架**: 支持 .NET 6.0 和 .NET 8.0
- 📦 **零依赖**: 轻量级设计，无外部依赖
- ✅ **高质量**: 586 个单元测试，100% 通过率

## 📦 **安装**

```bash
dotnet add package Zylo.Core
```

## ⚡ **快速开始**

```csharp
using Zylo.Core;

// 🔄 类型转换 - 永不抛异常
var age = "25".YToInt(0);           // 25，失败返回0
var price = "99.99".YToDecimal();   // 99.99m
var isValid = "true".YToBool();     // true

// 📋 集合操作 - 越界安全
var list = new List<string> { "apple", "banana" };
var item = list.YSafeGet(0, "默认");  // "apple"，越界返回"默认"
var isEmpty = list.YIsNullOrEmpty(); // false

// 📝 文本处理 - 全面安全 (v2.2.0 新增)
var cleaned = "  Hello,   World!  ".YCleanText();     // "Hello, World!"
var isEmail = "<EMAIL>".YIsValidEmail();     // true
var slug = "Hello World!".YToSlug();                  // "hello-world"

// 🔗 LINQ 风格链式操作
var numbers = new[] { 1, 2, 3, 4, 5 };
var result = numbers
    .Where(x => x > 2)              // 标准 LINQ
    .YTakeWhile(x => x < 5)         // Zylo 安全方法
    .YSum();                        // Zylo 安全求和 = 9
```

## 🚀 **使用示例**

### **基础使用**
```csharp
using Zylo.Core;

// 类型转换 - 永不抛异常
var age = "25".YToInt(0);           // 25，失败返回0
var price = "99.99".YToDecimal();   // 99.99m
var isValid = "true".YToBool();     // true

// 集合操作 - 安全的链式操作
var numbers = new[] { 1, 2, 3, 4, 5 };
var result = numbers.YWhere(x => x > 2)
                   .YSelect(x => x * 2)
                   .YToList();  // [6, 8, 10]

// 文本处理 - 全面的文本解决方案
var text = "  Hello World  ";
var cleaned = text.YTrim()
                 .YToTitleCase()
                 .YRemoveExtraSpaces(); // "Hello World"
```

### **高级功能**
```csharp
// 安全的集合操作
var items = new List<string> { "apple", "banana", "cherry" };
var firstItem = items.YFirstOrDefault("default");  // "apple"
var lastTwo = items.YTakeLast(2);                  // ["banana", "cherry"]

// 中文文本处理
var phone = "13812345678";
var isValidPhone = phone.YIsChinesePhoneNumber();  // true

var idCard = "110101199001011234";
var isValidId = idCard.YIsChineseIdCard();         // true

// 文本相似度比较
var similarity = "hello".YSimilarity("hallo");     // 0.8
```

## 📚 **详细文档**

### **📚 功能文档**
- 🔄 [**Bin.Shared 迁移指南**](Documentation/BinSharedMigration/README.md) - 从旧版本迁移到 Zylo.Core
- 📝 [**智能文本搜索功能**](Documentation/ChineseTextSearch.md) - 中文文本处理详解
- 🏗️ [**YTextExtensions 结构说明**](Documentation/YTextExtensions-Structure.md) - 文本处理模块架构
- 📊 [**项目状态报告**](Documentation/ProjectStatus.md) - 完整的项目状态和测试报告
- 📋 [**YCollectionExtensions 完整指南**](Documentation/YCollectionExtensions-Complete.md) - 集合操作完整功能说明

## 🆕 **v2.2.0 新功能亮点**

### **📝 全面的文本处理功能**
v2.2.0 新增了 50+ 个文本处理方法，提供完整的文本解决方案：

```csharp
// 文本清理和格式化
"  Hello,   World!  \n\t".YCleanText()          // "Hello, World!"
"hello world".YToCamelCase()                    // "helloWorld"
"Hello World!".YToSlug()                       // "hello-world"

// 文本验证
"<EMAIL>".YIsValidEmail()              // true
"13812345678".YIsValidChinesePhone()            // true
"Hello 世界".YContainsChinese()                  // true

// 文本分析
var analysis = "这是测试文本。".YAnalyzeText();
Console.WriteLine($"语言: {analysis.DetectedLanguage}");   // "zh"
Console.WriteLine($"情感: {analysis.SentimentScore}");     // 情感评分
```

## 🎯 **核心理念：Y = 安全**

所有以 `Y` 开头的方法都是**安全的**，不会抛出异常：

```csharp
// ❌ 传统方式：可能抛异常
var number = int.Parse("abc");        // 💥 异常！
var item = list[10];                  // 💥 越界异常！

// ✅ Zylo 方式：永远安全
var number = "abc".YToInt(0);         // 0 (默认值)
var item = list.YSafeGet(10, "默认"); // "默认"
```

## 📊 **功能统计**

| 功能模块 | 方法数量 | 测试用例 | 状态 |
|----------|----------|----------|------|
| 类型转换 | 80+ | 240 | ✅ 完成 |
| 集合操作 | 150+ | 180 | ✅ 完成 |
| 文本处理 | 50+ | 120 | ✅ 完成 |
| 依赖注入 | 34 | 26 | ✅ 完成 |
| **总计** | **314+** | **586** | **100% 通过** |

## 🏆 **为什么选择 Zylo.Core？**

### **🛡️ 安全优先**
- 所有 Y 前缀方法永不抛异常
- 完整的边界检查和空值保护
- 优雅的错误处理和默认值支持

### **⚡ 高效易用**
- 直观的 API 设计，学习成本低
- 丰富的重载方法，适应各种场景
- 优化的算法实现，性能卓越

### **🏗️ 架构灵活**
- 支持静态扩展方法和依赖注入双模式
- 模块化设计，可按需使用
- 完整的接口抽象，便于测试和扩展

### **🌏 本土化支持**
- 专门的中文验证功能
- 支持中文格式转换
- 符合中国开发者使用习惯

### **📈 持续更新**
- 活跃的开发和维护
- 完整的版本升级计划
- 详细的文档和示例

## 🤝 **贡献**

欢迎贡献代码、报告问题或提出建议！

## 📄 **许可证**

MIT License

---

<div align="center">

**立即开始使用 Zylo.Core，让您的 C# 开发更加安全、高效！** 🚀

[🔄 迁移指南](Documentation/BinSharedMigration/README.md) | [📝 智能文本搜索](Documentation/ChineseTextSearch.md) | [🏗️ 架构说明](Documentation/YTextExtensions-Structure.md) | [📊 项目状态](Documentation/ProjectStatus.md) | [📋 集合操作指南](Documentation/YCollectionExtensions-Complete.md)

</div>
