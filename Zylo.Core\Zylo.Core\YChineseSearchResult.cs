namespace Zylo.Core;

/// <summary>
/// 智能文本搜索结果（支持中文、英文、符号等所有字符）
/// </summary>
public class YSearchResult
{
    /// <summary>
    /// 搜索的文本
    /// </summary>
    public string SearchText { get; set; } = string.Empty;

    /// <summary>
    /// 找到的所有位置索引
    /// </summary>
    public int[] Positions { get; set; } = Array.Empty<int>();

    /// <summary>
    /// 匹配次数
    /// </summary>
    public int Count { get; set; }

    /// <summary>
    /// 是否找到匹配项
    /// </summary>
    public bool Found { get; set; }

    /// <summary>
    /// 第一个匹配位置（如果存在）
    /// </summary>
    public int FirstPosition => Positions.Length > 0 ? Positions[0] : -1;

    /// <summary>
    /// 最后一个匹配位置（如果存在）
    /// </summary>
    public int LastPosition => Positions.Length > 0 ? Positions[Positions.Length - 1] : -1;

    /// <summary>
    /// 获取匹配位置的详细信息
    /// </summary>
    /// <param name="sourceText">源文本</param>
    /// <returns>位置详细信息数组</returns>
    public YPositionInfo[] GetPositionDetails(string sourceText)
    {
        if (string.IsNullOrEmpty(sourceText) || Positions.Length == 0)
            return Array.Empty<YPositionInfo>();

        var details = new List<YPositionInfo>();

        foreach (var position in Positions)
        {
            if (position >= 0 && position < sourceText.Length)
            {
                // 计算行号和列号
                var lineNumber = sourceText.Take(position).Count(c => c == '\n') + 1;
                var lastNewLineIndex = position > 0 ? sourceText.LastIndexOf('\n', position - 1) : -1;
                var columnNumber = position - lastNewLineIndex;

                // 获取上下文
                var contextStart = Math.Max(0, position - 20);
                var contextEnd = Math.Min(sourceText.Length, position + SearchText.Length + 20);
                var context = sourceText.Substring(contextStart, contextEnd - contextStart);

                details.Add(new YPositionInfo
                {
                    Position = position,
                    LineNumber = lineNumber,
                    ColumnNumber = columnNumber,
                    Context = context,
                    MatchedText = sourceText.Substring(position, Math.Min(SearchText.Length, sourceText.Length - position))
                });
            }
        }

        return details.ToArray();
    }

    /// <summary>
    /// 返回搜索结果的摘要字符串
    /// </summary>
    /// <returns>搜索结果摘要</returns>
    public override string ToString()
    {
        if (!Found)
            return $"'{SearchText}' - 未找到";

        return $"'{SearchText}' - 找到 {Count} 次，位置: [{string.Join(", ", Positions.Take(5))}{(Positions.Length > 5 ? "..." : "")}]";
    }

    /// <summary>
    /// 获取详细的搜索报告
    /// </summary>
    /// <param name="sourceText">源文本</param>
    /// <returns>详细搜索报告</returns>
    public string GetDetailedReport(string sourceText)
    {
        var report = new System.Text.StringBuilder();
        
        report.AppendLine($"=== 搜索结果报告 ===");
        report.AppendLine($"搜索词: '{SearchText}'");
        report.AppendLine($"匹配次数: {Count}");
        report.AppendLine($"是否找到: {(Found ? "是" : "否")}");

        if (Found && !string.IsNullOrEmpty(sourceText))
        {
            report.AppendLine($"第一个位置: {FirstPosition}");
            report.AppendLine($"最后位置: {LastPosition}");
            report.AppendLine();

            var details = GetPositionDetails(sourceText);
            if (details.Length > 0)
            {
                report.AppendLine("详细位置信息:");
                foreach (var detail in details.Take(10)) // 最多显示10个位置
                {
                    report.AppendLine($"  位置 {detail.Position}: 第{detail.LineNumber}行, 第{detail.ColumnNumber}列");
                    report.AppendLine($"    上下文: {detail.Context.Trim()}");
                    report.AppendLine();
                }

                if (details.Length > 10)
                {
                    report.AppendLine($"  ... 还有 {details.Length - 10} 个匹配位置");
                }
            }
        }

        return report.ToString();
    }
}

/// <summary>
/// 文本位置详细信息
/// </summary>
public class YPositionInfo
{
    /// <summary>
    /// 字符位置索引
    /// </summary>
    public int Position { get; set; }

    /// <summary>
    /// 行号（从1开始）
    /// </summary>
    public int LineNumber { get; set; }

    /// <summary>
    /// 列号（从1开始）
    /// </summary>
    public int ColumnNumber { get; set; }

    /// <summary>
    /// 匹配的文本
    /// </summary>
    public string MatchedText { get; set; } = string.Empty;

    /// <summary>
    /// 上下文文本
    /// </summary>
    public string Context { get; set; } = string.Empty;

    /// <summary>
    /// 返回位置信息的字符串表示
    /// </summary>
    /// <returns>位置信息字符串</returns>
    public override string ToString()
    {
        return $"位置 {Position} (第{LineNumber}行, 第{ColumnNumber}列): '{MatchedText}'";
    }
}

/// <summary>
/// 位置内容信息（包含左右内容）
/// </summary>
public class YPositionContent
{
    /// <summary>
    /// 字符位置索引
    /// </summary>
    public int Position { get; set; }

    /// <summary>
    /// 匹配的文本
    /// </summary>
    public string MatchedText { get; set; } = string.Empty;

    /// <summary>
    /// 左侧内容
    /// </summary>
    public string LeftContent { get; set; } = string.Empty;

    /// <summary>
    /// 右侧内容
    /// </summary>
    public string RightContent { get; set; } = string.Empty;

    /// <summary>
    /// 完整上下文（左侧 + 匹配文本 + 右侧）
    /// </summary>
    public string FullContext { get; set; } = string.Empty;

    /// <summary>
    /// 左侧内容长度
    /// </summary>
    public int LeftLength => LeftContent.Length;

    /// <summary>
    /// 右侧内容长度
    /// </summary>
    public int RightLength => RightContent.Length;

    /// <summary>
    /// 匹配文本长度
    /// </summary>
    public int MatchedLength => MatchedText.Length;

    /// <summary>
    /// 返回位置内容的字符串表示
    /// </summary>
    /// <returns>位置内容字符串</returns>
    public override string ToString()
    {
        return $"位置 {Position}: '{LeftContent}[{MatchedText}]{RightContent}'";
    }

    /// <summary>
    /// 获取格式化的上下文显示
    /// </summary>
    /// <param name="highlightStart">高亮开始标记</param>
    /// <param name="highlightEnd">高亮结束标记</param>
    /// <returns>格式化的上下文</returns>
    public string GetFormattedContext(string highlightStart = "[", string highlightEnd = "]")
    {
        return $"{LeftContent}{highlightStart}{MatchedText}{highlightEnd}{RightContent}";
    }
}
