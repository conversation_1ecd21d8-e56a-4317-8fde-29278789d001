using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Zylo.Core.Models;
using System.Linq;
using Zylo.Core.Compatibility;

namespace Zylo.Core;

/// <summary>
/// 集合操作扩展方法
/// 提供安全、便捷的集合操作功能
/// </summary>
public static class YCollectionExtensions
{
    #region 安全访问操作

    /// <summary>
    /// 安全获取列表中指定索引的元素，索引超出范围时返回默认值
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index">索引</param>
    /// <param name="defaultValue">索引超出范围时的默认值</param>
    /// <returns>元素值或默认值</returns>
    /// <example>
    /// <code>
    /// var list = new List&lt;string&gt; { "a", "b", "c" };
    /// var item1 = list.YSafeGet(1);        // "b"
    /// var item2 = list.YSafeGet(10, "x");  // "x" (索引超出范围)
    /// </code>
    /// </example>
    public static T YSafeGet<T>(this IList<T> list, int index, T defaultValue = default!)
    {
        if (list == null || index < 0 || index >= list.Count)
            return defaultValue;

        return list[index];
    }

    /// <summary>
    /// 安全设置列表中指定索引的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index">索引</param>
    /// <param name="value">要设置的值</param>
    /// <returns>是否设置成功</returns>
    public static bool YSafeSet<T>(this IList<T> list, int index, T value)
    {
        if (list == null || index < 0 || index >= list.Count)
            return false;

        try
        {
            list[index] = value;
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 安全添加元素到集合
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">集合</param>
    /// <param name="item">要添加的元素</param>
    /// <returns>是否添加成功</returns>
    public static bool YSafeAdd<T>(this ICollection<T> collection, T item)
    {
        if (collection == null)
            return false;

        try
        {
            collection.Add(item);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 安全移除集合中的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">集合</param>
    /// <param name="item">要移除的元素</param>
    /// <returns>是否移除成功</returns>
    public static bool YSafeRemove<T>(this ICollection<T> collection, T item)
    {
        if (collection == null)
            return false;

        try
        {
            return collection.Remove(item);
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 批量操作

    /// <summary>
    /// 批量添加元素到集合
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">目标集合</param>
    /// <param name="items">要添加的元素</param>
    /// <returns>成功添加的元素数量</returns>
    public static int YAddRange<T>(this ICollection<T> collection, IEnumerable<T> items)
    {
        if (collection == null || items == null)
            return 0;

        int count = 0;
        foreach (var item in items)
        {
            if (collection.YSafeAdd(item))
                count++;
        }

        return count;
    }

    /// <summary>
    /// 批量移除集合中的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collection">目标集合</param>
    /// <param name="items">要移除的元素</param>
    /// <returns>成功移除的元素数量</returns>
    public static int YRemoveRange<T>(this ICollection<T> collection, IEnumerable<T> items)
    {
        if (collection == null || items == null)
            return 0;

        int count = 0;
        foreach (var item in items)
        {
            if (collection.YSafeRemove(item))
                count++;
        }

        return count;
    }

    #endregion

    #region 筛选和查找

    /// <summary>
    /// 筛选非空字符串
    /// </summary>
    /// <param name="source">字符串集合</param>
    /// <returns>非空字符串集合</returns>
    public static IEnumerable<string> YWhereNotEmpty(this IEnumerable<string> source)
    {
        if (source == null)
            yield break;

        foreach (var item in source)
        {
            if (!string.IsNullOrWhiteSpace(item))
                yield return item;
        }
    }

    /// <summary>
    /// 安全的Where操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">筛选条件</param>
    /// <returns>筛选后的集合</returns>
    public static IEnumerable<T> YWhere<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            yield break;

        foreach (var item in source)
        {
            if (predicate(item))
                yield return item;
        }
    }

    /// <summary>
    /// 安全的Select操作
    /// </summary>
    /// <typeparam name="T">源元素类型</typeparam>
    /// <typeparam name="TResult">结果元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="selector">选择器</param>
    /// <returns>转换后的集合</returns>
    public static IEnumerable<TResult> YSelect<T, TResult>(this IEnumerable<T> source, Func<T, TResult> selector)
    {
        if (source == null || selector == null)
            yield break;

        foreach (var item in source)
        {
            yield return selector(item);
        }
    }

    /// <summary>
    /// 安全的SelectMany操作
    /// </summary>
    /// <typeparam name="T">源元素类型</typeparam>
    /// <typeparam name="TResult">结果元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="selector">选择器</param>
    /// <returns>展平后的集合</returns>
    public static IEnumerable<TResult> YSelectMany<T, TResult>(this IEnumerable<T> source, Func<T, IEnumerable<TResult>> selector)
    {
        if (source == null || selector == null)
            yield break;

        foreach (var item in source)
        {
            var subItems = selector(item);
            if (subItems != null)
            {
                foreach (var subItem in subItems)
                {
                    yield return subItem;
                }
            }
        }
    }

    /// <summary>
    /// 查找第一个满足条件的元素索引
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">查找条件</param>
    /// <returns>元素索引，未找到返回-1</returns>
    public static int YFindIndex<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            return -1;

        int index = 0;
        foreach (var item in source)
        {
            if (predicate(item))
                return index;
            index++;
        }

        return -1;
    }

    /// <summary>
    /// 查找最后一个满足条件的元素索引
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">查找条件</param>
    /// <returns>元素索引，未找到返回-1</returns>
    public static int YFindLastIndex<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            return -1;

        int lastIndex = -1;
        int currentIndex = 0;

        foreach (var item in source)
        {
            if (predicate(item))
                lastIndex = currentIndex;
            currentIndex++;
        }

        return lastIndex;
    }

    #endregion

    #region 统计和分析功能

    /// <summary>
    /// 获取集合的详细统计信息
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>统计信息</returns>
    public static YCollectionStatistics YGetStatistics<T>(this IEnumerable<T> source) where T : notnull
    {
        if (source == null)
            return new YCollectionStatistics();

        var list = source.ToList();
        var uniqueItems = list.Distinct().ToList();
        var duplicateCounts = list.YCountDuplicates();
        var duplicates = duplicateCounts.Where(kvp => kvp.Value > 1).ToList();

        return new YCollectionStatistics
        {
            TotalCount = list.Count,
            UniqueCount = uniqueItems.Count,
            DuplicateCount = duplicates.Count,
            DuplicateItemCount = duplicates.Sum(kvp => kvp.Value - 1),
            IsEmpty = list.Count == 0,
            HasDuplicates = duplicates.Any()
        };
    }

    /// <summary>
    /// 获取数值集合的数学统计
    /// </summary>
    /// <param name="source">数值数据源</param>
    /// <returns>数学统计信息</returns>
    public static YMathStatistics YGetMathStatistics(this IEnumerable<double> source)
    {
        if (source == null)
            return new YMathStatistics();

        var list = source.ToList();
        if (list.Count == 0)
            return new YMathStatistics();

        var sorted = list.OrderBy(x => x).ToList();
        var sum = list.Sum();
        var mean = sum / list.Count;
        var variance = list.Sum(x => Math.Pow(x - mean, 2)) / list.Count;

        return new YMathStatistics
        {
            Count = list.Count,
            Sum = sum,
            Mean = mean,
            Median = GetMedian(sorted),
            Mode = GetMode(list),
            Min = sorted.First(),
            Max = sorted.Last(),
            Range = sorted.Last() - sorted.First(),
            Variance = variance,
            StandardDeviation = Math.Sqrt(variance)
        };
    }

    private static double GetMedian(List<double> sorted)
    {
        int count = sorted.Count;
        if (count % 2 == 0)
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        else
            return sorted[count / 2];
    }

    private static double GetMode(List<double> values)
    {
        var groups = values.GroupBy(x => x).ToList();
        var maxCount = groups.Max(g => g.Count());
        return groups.First(g => g.Count() == maxCount).Key;
    }

    /// <summary>
    /// 按频率排序元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="descending">是否降序</param>
    /// <returns>按频率排序的元素</returns>
    public static IEnumerable<(T Item, int Count)> YOrderByFrequency<T>(this IEnumerable<T> source, bool descending = true) where T : notnull
    {
        if (source == null)
            yield break;

        var frequencies = source.YCountDuplicates();
        var ordered = descending
            ? frequencies.OrderByDescending(kvp => kvp.Value)
            : frequencies.OrderBy(kvp => kvp.Value);

        foreach (var kvp in ordered)
        {
            yield return (kvp.Key, kvp.Value);
        }
    }

    /// <summary>
    /// 获取最常见的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="count">返回数量</param>
    /// <returns>最常见的元素</returns>
    public static IEnumerable<T> YGetMostCommon<T>(this IEnumerable<T> source, int count = 1)
    {
        if (source == null || count <= 0)
            yield break;

        var frequencies = source.YOrderByFrequency(true);
        foreach (var (item, _) in frequencies.Take(count))
        {
            yield return item;
        }
    }

    /// <summary>
    /// 获取最少见的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="count">返回数量</param>
    /// <returns>最少见的元素</returns>
    public static IEnumerable<T> YGetLeastCommon<T>(this IEnumerable<T> source, int count = 1)
    {
        if (source == null || count <= 0)
            yield break;

        var frequencies = source.YOrderByFrequency(false);
        foreach (var (item, _) in frequencies.Take(count))
        {
            yield return item;
        }
    }

    #endregion

    #region 分页和分块

    /// <summary>
    /// 分页获取数据
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="page">页码（从1开始）</param>
    /// <param name="size">页大小</param>
    /// <returns>分页后的数据</returns>
    /// <example>
    /// <code>
    /// var numbers = Enumerable.Range(1, 100);
    /// var page1 = numbers.YTakePage(1, 10);  // [1, 2, 3, ..., 10]
    /// var page2 = numbers.YTakePage(2, 10);  // [11, 12, 13, ..., 20]
    /// </code>
    /// </example>
    public static IEnumerable<T> YTakePage<T>(this IEnumerable<T> source, int page, int size)
    {
        if (source == null || page <= 0 || size <= 0)
            return Enumerable.Empty<T>();

        return source.Skip((page - 1) * size).Take(size);
    }

    /// <summary>
    /// 跳过指定页数的数据
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="page">要跳过的页数</param>
    /// <param name="size">页大小</param>
    /// <returns>跳过后的数据</returns>
    public static IEnumerable<T> YSkipPage<T>(this IEnumerable<T> source, int page, int size)
    {
        if (source == null || page <= 0 || size <= 0)
            return source ?? Enumerable.Empty<T>();

        return source.Skip(page * size);
    }

    /// <summary>
    /// 将序列分块处理
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="chunkSize">块大小</param>
    /// <returns>分块后的数据</returns>
    /// <example>
    /// <code>
    /// var numbers = Enumerable.Range(1, 10);
    /// var chunks = numbers.YChunk(3);  // [[1,2,3], [4,5,6], [7,8,9], [10]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YChunk<T>(this IEnumerable<T> source, int chunkSize)
    {
        if (source == null || chunkSize <= 0)
            yield break;

        var chunk = new List<T>(chunkSize);
        foreach (var item in source)
        {
            chunk.Add(item);
            if (chunk.Count == chunkSize)
            {
                yield return chunk;
                chunk = new List<T>(chunkSize);
            }
        }

        if (chunk.Count > 0)
            yield return chunk;
    }

    #endregion

    #region 随机和排序

    private static readonly Random _random = new Random();

    /// <summary>
    /// 随机获取一个元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">数据源为空时的默认值</param>
    /// <returns>随机元素或默认值</returns>
    public static T YRandomItem<T>(this IEnumerable<T> source, T defaultValue = default!)
    {
        if (source == null)
            return defaultValue;

        var list = source as IList<T> ?? source.ToList();
        if (list.Count == 0)
            return defaultValue;

        var index = _random.Next(list.Count);
        return list[index];
    }

    /// <summary>
    /// 随机获取多个元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="count">要获取的元素数量</param>
    /// <returns>随机元素集合</returns>
    public static IEnumerable<T> YRandomItems<T>(this IEnumerable<T> source, int count)
    {
        if (source == null || count <= 0)
            return Enumerable.Empty<T>();

        return source.YShuffle().Take(count);
    }

    /// <summary>
    /// 随机打乱序列
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>打乱后的序列</returns>
    public static IEnumerable<T> YShuffle<T>(this IEnumerable<T> source)
    {
        if (source == null)
            return Enumerable.Empty<T>();

        var list = source.ToList();
        for (int i = list.Count - 1; i > 0; i--)
        {
            int j = _random.Next(i + 1);
            (list[i], list[j]) = (list[j], list[i]);
        }

        return list;
    }

    #endregion

    #region 检查和验证

    /// <summary>
    /// 检查集合是否为空或null
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">要检查的集合</param>
    /// <returns>如果集合为空或null返回true</returns>
    public static bool YIsNullOrEmpty<T>(this IEnumerable<T>? source)
    {
        return source == null || !source.Any();
    }

    /// <summary>
    /// 检查集合是否有元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">要检查的集合</param>
    /// <returns>如果集合有元素返回true</returns>
    public static bool YHasItems<T>(this IEnumerable<T>? source)
    {
        return source != null && source.Any();
    }

    /// <summary>
    /// 安全获取集合元素数量
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">要计数的集合</param>
    /// <returns>元素数量，null时返回0</returns>
    public static int YSafeCount<T>(this IEnumerable<T>? source)
    {
        return source?.Count() ?? 0;
    }

    #endregion

    #region 转换操作

    /// <summary>
    /// 转换为可观察集合
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>可观察集合</returns>
    public static ObservableCollection<T> YToObservableCollection<T>(this IEnumerable<T> source)
    {
        return source == null ? new ObservableCollection<T>() : new ObservableCollection<T>(source);
    }

    /// <summary>
    /// 转换为只读集合
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>只读集合</returns>
    public static ReadOnlyCollection<T> YToReadOnlyCollection<T>(this IEnumerable<T> source)
    {
        var list = source?.ToList() ?? new List<T>();
        return new ReadOnlyCollection<T>(list);
    }

    #endregion

    #region 基本集合运算

    /// <summary>
    /// 安全的集合并集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="first">第一个集合</param>
    /// <param name="second">第二个集合</param>
    /// <returns>并集结果</returns>
    public static IEnumerable<T> YUnion<T>(this IEnumerable<T> first, IEnumerable<T> second)
    {
        if (first == null && second == null)
            return Enumerable.Empty<T>();
        if (first == null)
            return second;
        if (second == null)
            return first;

        return first.Union(second);
    }

    /// <summary>
    /// 安全的集合交集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="first">第一个集合</param>
    /// <param name="second">第二个集合</param>
    /// <returns>交集结果</returns>
    public static IEnumerable<T> YIntersect<T>(this IEnumerable<T> first, IEnumerable<T> second)
    {
        if (first == null || second == null)
            return Enumerable.Empty<T>();

        return first.Intersect(second);
    }

    /// <summary>
    /// 安全的集合差集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="first">第一个集合</param>
    /// <param name="second">第二个集合</param>
    /// <returns>差集结果</returns>
    public static IEnumerable<T> YExcept<T>(this IEnumerable<T> first, IEnumerable<T> second)
    {
        if (first == null)
            return Enumerable.Empty<T>();
        if (second == null)
            return first;

        return first.Except(second);
    }

    /// <summary>
    /// 安全的集合连接操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="first">第一个集合</param>
    /// <param name="second">第二个集合</param>
    /// <returns>连接后的集合</returns>
    public static IEnumerable<T> YConcat<T>(this IEnumerable<T> first, IEnumerable<T> second)
    {
        if (first == null && second == null)
            return Enumerable.Empty<T>();
        if (first == null)
            return second;
        if (second == null)
            return first;

        return first.Concat(second);
    }

    /// <summary>
    /// 安全的去重操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>去重后的集合</returns>
    public static IEnumerable<T> YDistinct<T>(this IEnumerable<T> source)
    {
        if (source == null)
            return Enumerable.Empty<T>();

        return source.Distinct();
    }

    #endregion

    #region 基本 CRUD 操作

    /// <summary>
    /// 安全的包含检查
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="item">要检查的元素</param>
    /// <returns>是否包含该元素</returns>
    public static bool YContains<T>(this IEnumerable<T> source, T item)
    {
        if (source == null)
            return false;

        return source.Contains(item);
    }

    /// <summary>
    /// 安全的包含检查（使用比较器）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="item">要检查的元素</param>
    /// <param name="comparer">比较器</param>
    /// <returns>是否包含该元素</returns>
    public static bool YContains<T>(this IEnumerable<T> source, T item, IEqualityComparer<T> comparer)
    {
        if (source == null)
            return false;

        return source.Contains(item, comparer);
    }

    /// <summary>
    /// 查找所有匹配的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">查找条件</param>
    /// <returns>所有匹配的元素</returns>
    public static IEnumerable<T> YFindAll<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            yield break;

        foreach (var item in source)
        {
            if (predicate(item))
                yield return item;
        }
    }

    /// <summary>
    /// 查找所有匹配元素的索引
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">查找条件</param>
    /// <returns>所有匹配元素的索引</returns>
    public static IEnumerable<int> YFindAllIndexes<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            yield break;

        int index = 0;
        foreach (var item in source)
        {
            if (predicate(item))
                yield return index;
            index++;
        }
    }

    /// <summary>
    /// 统计重复元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>元素及其出现次数的字典</returns>
    public static Dictionary<T, int> YCountDuplicates<T>(this IEnumerable<T> source) where T : notnull
    {
        var result = new Dictionary<T, int>();

        if (source == null)
            return result;

        foreach (var item in source)
        {
            if (result.ContainsKey(item))
                result[item]++;
            else
                result[item] = 1;
        }

        return result;
    }

    /// <summary>
    /// 获取重复的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>重复的元素</returns>
    public static IEnumerable<T> YGetDuplicates<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        var seen = new HashSet<T>();
        var duplicates = new HashSet<T>();

        foreach (var item in source)
        {
            if (!seen.Add(item))
            {
                duplicates.Add(item);
            }
        }

        foreach (var duplicate in duplicates)
        {
            yield return duplicate;
        }
    }

    /// <summary>
    /// 移除重复元素（保留第一个）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>去重后的集合</returns>
    public static IEnumerable<T> YRemoveDuplicates<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        var seen = new HashSet<T>();
        foreach (var item in source)
        {
            if (seen.Add(item))
                yield return item;
        }
    }

    /// <summary>
    /// 批量更新元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="predicate">更新条件</param>
    /// <param name="updater">更新函数</param>
    /// <returns>更新的元素数量</returns>
    public static int YUpdateWhere<T>(this IList<T> list, Func<T, bool> predicate, Func<T, T> updater)
    {
        if (list == null || predicate == null || updater == null)
            return 0;

        int count = 0;
        for (int i = 0; i < list.Count; i++)
        {
            if (predicate(list[i]))
            {
                list[i] = updater(list[i]);
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// 批量更新元素（按索引）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="indexes">要更新的索引</param>
    /// <param name="updater">更新函数</param>
    /// <returns>更新的元素数量</returns>
    public static int YUpdateAt<T>(this IList<T> list, IEnumerable<int> indexes, Func<T, T> updater)
    {
        if (list == null || indexes == null || updater == null)
            return 0;

        int count = 0;
        foreach (var index in indexes)
        {
            if (index >= 0 && index < list.Count)
            {
                list[index] = updater(list[index]);
                count++;
            }
        }

        return count;
    }

    #endregion

    #region 字符串连接

    /// <summary>
    /// 将字符串集合连接为单个字符串
    /// </summary>
    /// <param name="source">字符串集合</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YJoinToString(this IEnumerable<string> source, string separator = ",")
    {
        return source == null ? string.Empty : string.Join(separator, source);
    }

    /// <summary>
    /// 将任意类型集合转换为字符串并连接
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YJoinToString<T>(this IEnumerable<T> source, string separator = ",")
    {
        return source == null ? string.Empty : string.Join(separator, source);
    }

    #endregion

    #region List 专用扩展方法

    /// <summary>
    /// List 安全插入元素到指定位置
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index">插入位置</param>
    /// <param name="item">要插入的元素</param>
    /// <returns>是否插入成功</returns>
    public static bool YSafeInsert<T>(this List<T> list, int index, T item)
    {
        if (list == null || index < 0 || index > list.Count)
            return false;

        try
        {
            list.Insert(index, item);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 安全移除指定位置的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index">要移除的位置</param>
    /// <returns>是否移除成功</returns>
    public static bool YSafeRemoveAt<T>(this List<T> list, int index)
    {
        if (list == null || index < 0 || index >= list.Count)
            return false;

        try
        {
            list.RemoveAt(index);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 查找元素的所有索引位置
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="item">要查找的元素</param>
    /// <returns>所有匹配元素的索引列表</returns>
    public static List<int> YFindAllIndexes<T>(this List<T> list, T item)
    {
        var indexes = new List<int>();

        if (list == null)
            return indexes;

        for (int i = 0; i < list.Count; i++)
        {
            if (EqualityComparer<T>.Default.Equals(list[i], item))
            {
                indexes.Add(i);
            }
        }

        return indexes;
    }

    /// <summary>
    /// List 交换两个位置的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index1">第一个位置</param>
    /// <param name="index2">第二个位置</param>
    /// <returns>是否交换成功</returns>
    public static bool YSwap<T>(this List<T> list, int index1, int index2)
    {
        if (list == null || index1 < 0 || index1 >= list.Count || index2 < 0 || index2 >= list.Count)
            return false;

        if (index1 == index2)
            return true;

        try
        {
            (list[index1], list[index2]) = (list[index2], list[index1]);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 移动元素到新位置
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="fromIndex">原位置</param>
    /// <param name="toIndex">目标位置</param>
    /// <returns>是否移动成功</returns>
    public static bool YMove<T>(this List<T> list, int fromIndex, int toIndex)
    {
        if (list == null || fromIndex < 0 || fromIndex >= list.Count || toIndex < 0 || toIndex >= list.Count)
            return false;

        if (fromIndex == toIndex)
            return true;

        try
        {
            var item = list[fromIndex];
            list.RemoveAt(fromIndex);
            list.Insert(toIndex, item);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 批量替换元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="oldItem">要替换的元素</param>
    /// <param name="newItem">新元素</param>
    /// <returns>替换的元素数量</returns>
    public static int YReplaceAll<T>(this List<T> list, T oldItem, T newItem)
    {
        if (list == null)
            return 0;

        int count = 0;
        for (int i = 0; i < list.Count; i++)
        {
            if (EqualityComparer<T>.Default.Equals(list[i], oldItem))
            {
                list[i] = newItem;
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// List 清空并添加新元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="items">要添加的新元素</param>
    /// <returns>是否操作成功</returns>
    public static bool YClearAndAddRange<T>(this List<T> list, IEnumerable<T> items)
    {
        if (list == null)
            return false;

        try
        {
            list.Clear();
            if (items != null)
            {
                list.AddRange(items);
            }
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 安全获取第一个元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>第一个元素或默认值</returns>
    public static T YSafeFirst<T>(this List<T> list, T defaultValue = default!)
    {
        return list != null && list.Count > 0 ? list[0] : defaultValue;
    }

    /// <summary>
    /// List 安全获取最后一个元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>最后一个元素或默认值</returns>
    public static T YSafeLast<T>(this List<T> list, T defaultValue = default!)
    {
        return list != null && list.Count > 0 ? list[list.Count - 1] : defaultValue;
    }

    /// <summary>
    /// List 安全获取中间元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>中间元素或默认值</returns>
    public static T YSafeMiddle<T>(this List<T> list, T defaultValue = default!)
    {
        if (list == null || list.Count == 0)
            return defaultValue;

        int middleIndex = list.Count / 2;
        return list[middleIndex];
    }

    /// <summary>
    /// List 批量条件替换
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="predicate">替换条件</param>
    /// <param name="newValue">新值</param>
    /// <returns>替换的元素数量</returns>
    public static int YReplaceWhere<T>(this List<T> list, Func<T, bool> predicate, T newValue)
    {
        if (list == null || predicate == null)
            return 0;

        int count = 0;
        for (int i = 0; i < list.Count; i++)
        {
            if (predicate(list[i]))
            {
                list[i] = newValue;
                count++;
            }
        }

        return count;
    }

    /// <summary>
    /// List 批量条件移除
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="predicate">移除条件</param>
    /// <returns>移除的元素数量</returns>
    public static int YRemoveWhere<T>(this List<T> list, Func<T, bool> predicate)
    {
        if (list == null || predicate == null)
            return 0;

        return list.RemoveAll(new Predicate<T>(predicate));
    }

    /// <summary>
    /// List 安全截取子列表
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="startIndex">开始索引</param>
    /// <param name="endIndex">结束索引（包含）</param>
    /// <returns>子列表</returns>
    public static List<T> YSafeSubList<T>(this List<T> list, int startIndex, int endIndex)
    {
        if (list == null || list.Count == 0)
            return new List<T>();

        // 如果开始索引大于结束索引，直接返回空列表
        if (startIndex > endIndex)
            return new List<T>();

        // 处理负索引和超出范围的索引
        startIndex = Math.Max(0, Math.Min(startIndex, list.Count - 1));
        endIndex = Math.Max(0, Math.Min(endIndex, list.Count - 1));

        // 再次检查调整后的索引
        if (startIndex > endIndex)
            return new List<T>();

        var result = new List<T>();
        for (int i = startIndex; i <= endIndex; i++)
        {
            result.Add(list[i]);
        }

        return result;
    }

    /// <summary>
    /// List 填充指定值
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="value">填充值</param>
    /// <param name="count">填充数量</param>
    /// <returns>是否填充成功</returns>
    public static bool YFill<T>(this List<T> list, T value, int count)
    {
        if (list == null || count <= 0)
            return false;

        try
        {
            for (int i = 0; i < count; i++)
            {
                list.Add(value);
            }
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region LINQ 风格扩展方法

    /// <summary>
    /// 筛选非空元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>非空元素集合</returns>
    public static IEnumerable<T> YWhereNotNull<T>(this IEnumerable<T?> source) where T : class
    {
        if (source == null)
            yield break;

        foreach (var item in source)
        {
            if (item != null)
                yield return item;
        }
    }

    /// <summary>
    /// 根据键选择器去重
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>去重后的集合</returns>
    public static IEnumerable<T> YDistinctBy<T, TKey>(this IEnumerable<T> source, Func<T, TKey> keySelector)
    {
        if (source == null || keySelector == null)
            yield break;

        var seenKeys = new HashSet<TKey>();
        foreach (var item in source)
        {
            var key = keySelector(item);
            if (seenKeys.Add(key))
            {
                yield return item;
            }
        }
    }

    /// <summary>
    /// 安全获取第一个元素或指定默认值
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>第一个元素或默认值</returns>
    public static T YFirstOrDefault<T>(this IEnumerable<T> source, T defaultValue)
    {
        if (source == null)
            return defaultValue;

        foreach (var item in source)
        {
            return item;
        }

        return defaultValue;
    }

    /// <summary>
    /// 安全获取最后一个元素或指定默认值
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>最后一个元素或默认值</returns>
    public static T YLastOrDefault<T>(this IEnumerable<T> source, T defaultValue)
    {
        if (source == null)
            return defaultValue;

        var result = defaultValue;
        foreach (var item in source)
        {
            result = item;
        }

        return result;
    }

    /// <summary>
    /// 安全获取指定位置的元素或默认值
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="index">索引</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>指定位置的元素或默认值</returns>
    public static T YElementAtOrDefault<T>(this IEnumerable<T> source, int index, T defaultValue)
    {
        if (source == null || index < 0)
            return defaultValue;

        if (source is IList<T> list)
        {
            return index < list.Count ? list[index] : defaultValue;
        }

        int currentIndex = 0;
        foreach (var item in source)
        {
            if (currentIndex == index)
                return item;
            currentIndex++;
        }

        return defaultValue;
    }

    /// <summary>
    /// 检查是否有任何元素满足条件
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">条件</param>
    /// <returns>是否有元素满足条件</returns>
    public static bool YAny<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            return false;

        foreach (var item in source)
        {
            if (predicate(item))
                return true;
        }

        return false;
    }

    /// <summary>
    /// 检查是否所有元素都满足条件
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">条件</param>
    /// <returns>是否所有元素都满足条件</returns>
    public static bool YAll<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            return false;

        foreach (var item in source)
        {
            if (!predicate(item))
                return false;
        }

        return true;
    }

    /// <summary>
    /// 安全计数满足条件的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">条件</param>
    /// <returns>满足条件的元素数量</returns>
    public static int YCount<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            return 0;

        int count = 0;
        foreach (var item in source)
        {
            if (predicate(item))
                count++;
        }

        return count;
    }

    /// <summary>
    /// 跳过满足条件的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">条件</param>
    /// <returns>跳过满足条件元素后的集合</returns>
    public static IEnumerable<T> YSkipWhile<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            yield break;

        bool yielding = false;
        foreach (var item in source)
        {
            if (!yielding && !predicate(item))
                yielding = true;

            if (yielding)
                yield return item;
        }
    }

    /// <summary>
    /// 获取满足条件的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="predicate">条件</param>
    /// <returns>满足条件的元素集合</returns>
    public static IEnumerable<T> YTakeWhile<T>(this IEnumerable<T> source, Func<T, bool> predicate)
    {
        if (source == null || predicate == null)
            yield break;

        foreach (var item in source)
        {
            if (!predicate(item))
                break;

            yield return item;
        }
    }

    /// <summary>
    /// 安全的聚合操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="seed">初始值</param>
    /// <param name="func">聚合函数</param>
    /// <returns>聚合结果</returns>
    public static TResult YAggregate<T, TResult>(this IEnumerable<T> source, TResult seed, Func<TResult, T, TResult> func)
    {
        if (source == null || func == null)
            return seed;

        var result = seed;
        foreach (var item in source)
        {
            result = func(result, item);
        }

        return result;
    }

    /// <summary>
    /// 安全的最小值获取
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>最小值或默认值</returns>
    public static T YMin<T>(this IEnumerable<T> source, T defaultValue = default!) where T : IComparable<T>
    {
        if (source == null)
            return defaultValue;

        using var enumerator = source.GetEnumerator();
        if (!enumerator.MoveNext())
            return defaultValue;

        var min = enumerator.Current;
        while (enumerator.MoveNext())
        {
            if (enumerator.Current != null && (min == null || enumerator.Current.CompareTo(min) < 0))
                min = enumerator.Current;
        }

        return min ?? defaultValue;
    }

    /// <summary>
    /// 安全的最大值获取
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>最大值或默认值</returns>
    public static T YMax<T>(this IEnumerable<T> source, T defaultValue = default!) where T : IComparable<T>
    {
        if (source == null)
            return defaultValue;

        using var enumerator = source.GetEnumerator();
        if (!enumerator.MoveNext())
            return defaultValue;

        var max = enumerator.Current;
        while (enumerator.MoveNext())
        {
            if (enumerator.Current != null && (max == null || enumerator.Current.CompareTo(max) > 0))
                max = enumerator.Current;
        }

        return max ?? defaultValue;
    }

    /// <summary>
    /// 安全的求和操作
    /// </summary>
    /// <param name="source">数据源</param>
    /// <returns>求和结果</returns>
    public static int YSum(this IEnumerable<int> source)
    {
        if (source == null)
            return 0;

        int sum = 0;
        foreach (var item in source)
        {
            sum += item;
        }

        return sum;
    }

    /// <summary>
    /// 安全的求和操作（decimal）
    /// </summary>
    /// <param name="source">数据源</param>
    /// <returns>求和结果</returns>
    public static decimal YSum(this IEnumerable<decimal> source)
    {
        if (source == null)
            return 0m;

        decimal sum = 0m;
        foreach (var item in source)
        {
            sum += item;
        }

        return sum;
    }

    /// <summary>
    /// 安全的平均值计算
    /// </summary>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>平均值或默认值</returns>
    public static double YAverage(this IEnumerable<int> source, double defaultValue = 0.0)
    {
        if (source == null)
            return defaultValue;

        long sum = 0;
        int count = 0;

        foreach (var item in source)
        {
            sum += item;
            count++;
        }

        return count == 0 ? defaultValue : (double)sum / count;
    }

    /// <summary>
    /// 安全的平均值计算（decimal）
    /// </summary>
    /// <param name="source">数据源</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>平均值或默认值</returns>
    public static decimal YAverage(this IEnumerable<decimal> source, decimal defaultValue = 0m)
    {
        if (source == null)
            return defaultValue;

        decimal sum = 0m;
        int count = 0;

        foreach (var item in source)
        {
            sum += item;
            count++;
        }

        return count == 0 ? defaultValue : sum / count;
    }

    /// <summary>
    /// 安全的分组操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>分组结果</returns>
    public static IEnumerable<IGrouping<TKey, T>> YGroupBy<T, TKey>(this IEnumerable<T> source, Func<T, TKey> keySelector)
    {
        if (source == null || keySelector == null)
            return Enumerable.Empty<IGrouping<TKey, T>>();

        return source.GroupBy(keySelector);
    }

    /// <summary>
    /// 安全的排序操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>排序后的集合</returns>
    public static IOrderedEnumerable<T> YOrderBy<T, TKey>(this IEnumerable<T> source, Func<T, TKey> keySelector)
    {
        if (source == null || keySelector == null)
            return Enumerable.Empty<T>().OrderBy(x => x);

        return source.OrderBy(keySelector);
    }

    /// <summary>
    /// 安全的降序排序操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="keySelector">键选择器</param>
    /// <returns>降序排序后的集合</returns>
    public static IOrderedEnumerable<T> YOrderByDescending<T, TKey>(this IEnumerable<T> source, Func<T, TKey> keySelector)
    {
        if (source == null || keySelector == null)
            return Enumerable.Empty<T>().OrderBy(x => x);

        return source.OrderByDescending(keySelector);
    }

    #endregion

    #region 元组扩展方法

    /// <summary>
    /// 二元组转换为字符串
    /// </summary>
    /// <typeparam name="T1">第一个元素类型</typeparam>
    /// <typeparam name="T2">第二个元素类型</typeparam>
    /// <param name="tuple">二元组</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString<T1, T2>(this (T1, T2) tuple, string separator = ",")
    {
        return $"{tuple.Item1}{separator}{tuple.Item2}";
    }

    /// <summary>
    /// 三元组转换为字符串
    /// </summary>
    /// <typeparam name="T1">第一个元素类型</typeparam>
    /// <typeparam name="T2">第二个元素类型</typeparam>
    /// <typeparam name="T3">第三个元素类型</typeparam>
    /// <param name="tuple">三元组</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString<T1, T2, T3>(this (T1, T2, T3) tuple, string separator = ",")
    {
        return $"{tuple.Item1}{separator}{tuple.Item2}{separator}{tuple.Item3}";
    }

    /// <summary>
    /// 二元组转换为数组
    /// </summary>
    /// <typeparam name="T">元素类型（两个元素必须是同一类型）</typeparam>
    /// <param name="tuple">二元组</param>
    /// <returns>转换后的数组</returns>
    public static T[] YToArray<T>(this (T, T) tuple)
    {
        return new[] { tuple.Item1, tuple.Item2 };
    }

    /// <summary>
    /// 三元组转换为数组
    /// </summary>
    /// <typeparam name="T">元素类型（三个元素必须是同一类型）</typeparam>
    /// <param name="tuple">三元组</param>
    /// <returns>转换后的数组</returns>
    public static T[] YToArray<T>(this (T, T, T) tuple)
    {
        return new[] { tuple.Item1, tuple.Item2, tuple.Item3 };
    }

    /// <summary>
    /// 二元组转换为 List
    /// </summary>
    /// <typeparam name="T">元素类型（两个元素必须是同一类型）</typeparam>
    /// <param name="tuple">二元组</param>
    /// <returns>转换后的 List</returns>
    public static List<T> YToList<T>(this (T, T) tuple)
    {
        return new List<T> { tuple.Item1, tuple.Item2 };
    }

    #endregion

    #region 排列组合算法

    /// <summary>
    /// 生成集合的所有排列（全排列）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>所有排列的集合</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 2, 3 };
    /// var permutations = items.YPermutations();
    /// // 结果: [[1,2,3], [1,3,2], [2,1,3], [2,3,1], [3,1,2], [3,2,1]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YPermutations<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        var list = source.ToList();
        if (list.Count <= 1)
        {
            yield return list;
            yield break;
        }

        foreach (var permutation in GetPermutations(list, list.Count))
        {
            yield return permutation;
        }
    }

    /// <summary>
    /// 生成集合的指定长度排列（A(n,r)）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="length">排列长度</param>
    /// <returns>指定长度的排列集合</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 2, 3, 4 };
    /// var permutations = items.YPermutations(2);
    /// // 结果: [[1,2], [1,3], [1,4], [2,1], [2,3], [2,4], [3,1], [3,2], [3,4], [4,1], [4,2], [4,3]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YPermutations<T>(this IEnumerable<T> source, int length)
    {
        if (source == null || length <= 0)
            yield break;

        var list = source.ToList();
        if (length > list.Count)
            yield break;

        foreach (var permutation in GetPermutations(list, length))
        {
            yield return permutation;
        }
    }

    /// <summary>
    /// 生成集合的所有组合（C(n,r)）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="length">组合长度</param>
    /// <returns>所有组合的集合</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 2, 3, 4 };
    /// var combinations = items.YCombinations(2);
    /// // 结果: [[1,2], [1,3], [1,4], [2,3], [2,4], [3,4]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YCombinations<T>(this IEnumerable<T> source, int length)
    {
        if (source == null || length <= 0)
            yield break;

        var list = source.ToList();
        if (length > list.Count)
            yield break;

        foreach (var combination in GetCombinations(list, length))
        {
            yield return combination;
        }
    }

    /// <summary>
    /// 生成集合的笛卡尔积
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源集合</param>
    /// <returns>笛卡尔积结果</returns>
    /// <example>
    /// <code>
    /// var sets = new[] {
    ///     new[] { 1, 2 },
    ///     new[] { "a", "b" },
    ///     new[] { "x", "y" }
    /// };
    /// var cartesian = sets.YCartesianProduct();
    /// // 结果: [[1,"a","x"], [1,"a","y"], [1,"b","x"], [1,"b","y"], [2,"a","x"], [2,"a","y"], [2,"b","x"], [2,"b","y"]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YCartesianProduct<T>(this IEnumerable<IEnumerable<T>> source)
    {
        if (source == null)
            yield break;

        var sequences = source.Select(s => s?.ToList() ?? new List<T>()).ToList();
        if (sequences.Count == 0 || sequences.Any(s => s.Count == 0))
            yield break;

        var indices = new int[sequences.Count];

        do
        {
            yield return sequences.Select((seq, i) => seq[indices[i]]).ToList();
        }
        while (IncrementIndices(indices, sequences));
    }

    /// <summary>
    /// 生成集合的幂集（所有子集）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>幂集（所有子集的集合）</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 2, 3 };
    /// var powerSet = items.YPowerSet();
    /// // 结果: [[], [1], [2], [1,2], [3], [1,3], [2,3], [1,2,3]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YPowerSet<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        var list = source.ToList();
        var count = 1 << list.Count; // 2^n

        for (int i = 0; i < count; i++)
        {
            var subset = new List<T>();
            for (int j = 0; j < list.Count; j++)
            {
                if ((i & (1 << j)) != 0)
                {
                    subset.Add(list[j]);
                }
            }
            yield return subset;
        }
    }

    #endregion

    #region 高级集合操作

    /// <summary>
    /// 滑动窗口操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="windowSize">窗口大小</param>
    /// <returns>滑动窗口结果</returns>
    /// <example>
    /// <code>
    /// var numbers = new[] { 1, 2, 3, 4, 5 };
    /// var windows = numbers.YSlidingWindow(3);
    /// // 结果: [[1,2,3], [2,3,4], [3,4,5]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YSlidingWindow<T>(this IEnumerable<T> source, int windowSize)
    {
        if (source == null || windowSize <= 0)
            yield break;

        var list = source.ToList();
        if (list.Count < windowSize)
            yield break;

        for (int i = 0; i <= list.Count - windowSize; i++)
        {
            yield return list.Skip(i).Take(windowSize);
        }
    }

    /// <summary>
    /// 分组相邻相同元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>分组后的相邻相同元素</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 1, 2, 2, 2, 3, 1, 1 };
    /// var groups = items.YGroupAdjacent();
    /// // 结果: [[1,1], [2,2,2], [3], [1,1]]
    /// </code>
    /// </example>
    public static IEnumerable<IEnumerable<T>> YGroupAdjacent<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        using var enumerator = source.GetEnumerator();
        if (!enumerator.MoveNext())
            yield break;

        var currentGroup = new List<T> { enumerator.Current };
        var currentValue = enumerator.Current;

        while (enumerator.MoveNext())
        {
            if (EqualityComparer<T>.Default.Equals(enumerator.Current, currentValue))
            {
                currentGroup.Add(enumerator.Current);
            }
            else
            {
                yield return currentGroup;
                currentValue = enumerator.Current;
                currentGroup = new List<T> { enumerator.Current };
            }
        }

        if (currentGroup.Count > 0)
            yield return currentGroup;
    }

    /// <summary>
    /// 交替获取多个集合的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="sources">多个数据源</param>
    /// <returns>交替获取的元素序列</returns>
    /// <example>
    /// <code>
    /// var list1 = new[] { 1, 3, 5 };
    /// var list2 = new[] { 2, 4, 6 };
    /// var result = YCollectionExtensions.YInterleave(list1, list2);
    /// // 结果: [1, 2, 3, 4, 5, 6]
    /// </code>
    /// </example>
    public static IEnumerable<T> YInterleave<T>(params IEnumerable<T>[] sources)
    {
        if (sources == null || sources.Length == 0)
            yield break;

        var enumerators = sources.Select(s => s?.GetEnumerator()).Where(e => e != null).ToList();

        try
        {
            bool hasMore;
            do
            {
                hasMore = false;
                foreach (var enumerator in enumerators.ToList())
                {
                    if (enumerator!.MoveNext())
                    {
                        yield return enumerator.Current;
                        hasMore = true;
                    }
                    else
                    {
                        enumerators.Remove(enumerator);
                        enumerator.Dispose();
                    }
                }
            } while (hasMore);
        }
        finally
        {
            foreach (var enumerator in enumerators)
            {
                enumerator?.Dispose();
            }
        }
    }

    /// <summary>
    /// 循环迭代集合（无限循环）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>循环迭代的无限序列</returns>
    /// <example>
    /// <code>
    /// var items = new[] { 1, 2, 3 };
    /// var cycled = items.YCycle().Take(10);
    /// // 结果: [1, 2, 3, 1, 2, 3, 1, 2, 3, 1]
    /// </code>
    /// </example>
    public static IEnumerable<T> YCycle<T>(this IEnumerable<T> source)
    {
        if (source == null)
            yield break;

        var list = source.ToList();
        if (list.Count == 0)
            yield break;

        while (true)
        {
            foreach (var item in list)
            {
                yield return item;
            }
        }
    }

    /// <summary>
    /// 批处理操作（按指定大小分批处理）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="batchSize">批处理大小</param>
    /// <param name="processor">批处理函数</param>
    /// <returns>批处理结果</returns>
    /// <example>
    /// <code>
    /// var numbers = Enumerable.Range(1, 10);
    /// var sums = numbers.YBatchProcess(3, batch => batch.Sum());
    /// // 结果: [6, 15, 24, 10] (1+2+3, 4+5+6, 7+8+9, 10)
    /// </code>
    /// </example>
    public static IEnumerable<TResult> YBatchProcess<T, TResult>(
        this IEnumerable<T> source,
        int batchSize,
        Func<IEnumerable<T>, TResult> processor)
    {
        if (source == null || processor == null || batchSize <= 0)
            yield break;

        foreach (var batch in source.YChunk(batchSize))
        {
            yield return processor(batch);
        }
    }

    #endregion

    #region List 基本操作增强

    /// <summary>
    /// List 安全清空
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <returns>是否清空成功</returns>
    public static bool YSafeClear<T>(this List<T> list)
    {
        if (list == null)
            return false;

        try
        {
            list.Clear();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 安全排序
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <returns>是否排序成功</returns>
    public static bool YSafeSort<T>(this List<T> list) where T : IComparable<T>
    {
        if (list == null)
            return false;

        try
        {
            list.Sort();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 安全排序（使用比较器）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="comparer">比较器</param>
    /// <returns>是否排序成功</returns>
    public static bool YSafeSort<T>(this List<T> list, IComparer<T> comparer)
    {
        if (list == null || comparer == null)
            return false;

        try
        {
            list.Sort(comparer);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 反转指定范围的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="startIndex">开始索引</param>
    /// <param name="count">元素数量</param>
    /// <returns>是否反转成功</returns>
    public static bool YReverseRange<T>(this List<T> list, int startIndex, int count)
    {
        if (list == null || startIndex < 0 || count <= 0 || startIndex + count > list.Count)
            return false;

        try
        {
            list.Reverse(startIndex, count);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 获取指定范围的子列表
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="startIndex">开始索引</param>
    /// <param name="count">元素数量</param>
    /// <returns>子列表</returns>
    public static List<T> YGetRange<T>(this List<T> list, int startIndex, int count)
    {
        if (list == null || startIndex < 0 || count <= 0 || startIndex + count > list.Count)
            return new List<T>();

        try
        {
            return list.GetRange(startIndex, count);
        }
        catch
        {
            return new List<T>();
        }
    }

    /// <summary>
    /// List 在指定位置插入范围
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="index">插入位置</param>
    /// <param name="items">要插入的元素</param>
    /// <returns>是否插入成功</returns>
    public static bool YInsertRange<T>(this List<T> list, int index, IEnumerable<T> items)
    {
        if (list == null || items == null || index < 0 || index > list.Count)
            return false;

        try
        {
            list.InsertRange(index, items);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// List 移除指定范围的元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="list">列表</param>
    /// <param name="startIndex">开始索引</param>
    /// <param name="count">移除数量</param>
    /// <returns>是否移除成功</returns>
    public static bool YRemoveRange<T>(this List<T> list, int startIndex, int count)
    {
        if (list == null || startIndex < 0 || count <= 0 || startIndex + count > list.Count)
            return false;

        try
        {
            list.RemoveRange(startIndex, count);
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region Dictionary 扩展操作

    /// <summary>
    /// Dictionary 安全获取值
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>值或默认值</returns>
    /// <example>
    /// <code>
    /// var dict = new Dictionary&lt;string, int&gt; { {"a", 1}, {"b", 2} };
    /// var value = dict.YSafeGet("c", 0);  // 0 (键不存在)
    /// </code>
    /// </example>
    public static TValue YSafeGet<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key, TValue defaultValue = default!)
        where TKey : notnull
    {
        if (dictionary == null || key == null)
            return defaultValue;

        return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
    }

    /// <summary>
    /// Dictionary 安全添加或更新
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键</param>
    /// <param name="value">值</param>
    /// <returns>是否操作成功</returns>
    public static bool YAddOrUpdate<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key, TValue value)
        where TKey : notnull
    {
        if (dictionary == null || key == null)
            return false;

        try
        {
            dictionary[key] = value;
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Dictionary 批量添加
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="items">要添加的键值对</param>
    /// <returns>成功添加的数量</returns>
    public static int YAddRange<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, IEnumerable<KeyValuePair<TKey, TValue>> items)
        where TKey : notnull
    {
        if (dictionary == null || items == null)
            return 0;

        int count = 0;
        foreach (var item in items)
        {
            if (dictionary.YAddOrUpdate(item.Key, item.Value))
                count++;
        }

        return count;
    }

    /// <summary>
    /// Dictionary 获取或添加值
    /// </summary>
    /// <typeparam name="TKey">键类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="key">键</param>
    /// <param name="valueFactory">值工厂函数</param>
    /// <returns>获取或新创建的值</returns>
    public static TValue YGetOrAdd<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key, Func<TKey, TValue> valueFactory)
        where TKey : notnull
    {
        if (dictionary == null || key == null || valueFactory == null)
            return default!;

        if (dictionary.TryGetValue(key, out var existingValue))
            return existingValue;

        var newValue = valueFactory(key);
        dictionary[key] = newValue;
        return newValue;
    }

    #endregion

    #region HashSet 扩展操作

    /// <summary>
    /// HashSet 安全添加元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="hashSet">哈希集合</param>
    /// <param name="item">要添加的元素</param>
    /// <returns>是否添加成功（如果元素已存在返回false）</returns>
    public static bool YSafeAdd<T>(this HashSet<T> hashSet, T item)
    {
        if (hashSet == null)
            return false;

        try
        {
            return hashSet.Add(item);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// HashSet 批量添加元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="hashSet">哈希集合</param>
    /// <param name="items">要添加的元素</param>
    /// <returns>成功添加的元素数量</returns>
    public static int YAddRange<T>(this HashSet<T> hashSet, IEnumerable<T> items)
    {
        if (hashSet == null || items == null)
            return 0;

        int count = 0;
        foreach (var item in items)
        {
            if (hashSet.YSafeAdd(item))
                count++;
        }

        return count;
    }

    /// <summary>
    /// HashSet 交集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="hashSet">哈希集合</param>
    /// <param name="other">另一个集合</param>
    /// <returns>交集结果</returns>
    public static HashSet<T> YIntersect<T>(this HashSet<T> hashSet, IEnumerable<T> other)
    {
        if (hashSet == null || other == null)
            return new HashSet<T>();

        var result = new HashSet<T>(hashSet);
        result.IntersectWith(other);
        return result;
    }

    /// <summary>
    /// HashSet 并集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="hashSet">哈希集合</param>
    /// <param name="other">另一个集合</param>
    /// <returns>并集结果</returns>
    public static HashSet<T> YUnion<T>(this HashSet<T> hashSet, IEnumerable<T> other)
    {
        if (hashSet == null)
            return other?.ToHashSetCompat() ?? new HashSet<T>();

        if (other == null)
            return new HashSet<T>(hashSet);

        var result = new HashSet<T>(hashSet);
        result.UnionWith(other);
        return result;
    }

    /// <summary>
    /// HashSet 差集操作
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="hashSet">哈希集合</param>
    /// <param name="other">另一个集合</param>
    /// <returns>差集结果</returns>
    public static HashSet<T> YExcept<T>(this HashSet<T> hashSet, IEnumerable<T> other)
    {
        if (hashSet == null)
            return new HashSet<T>();

        if (other == null)
            return new HashSet<T>(hashSet);

        var result = new HashSet<T>(hashSet);
        result.ExceptWith(other);
        return result;
    }

    #endregion

    #region Queue 和 Stack 扩展操作

    /// <summary>
    /// Queue 安全入队
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="queue">队列</param>
    /// <param name="item">要入队的元素</param>
    /// <returns>是否入队成功</returns>
    public static bool YSafeEnqueue<T>(this Queue<T> queue, T item)
    {
        if (queue == null)
            return false;

        try
        {
            queue.Enqueue(item);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Queue 安全出队
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="queue">队列</param>
    /// <param name="defaultValue">队列为空时的默认值</param>
    /// <returns>出队的元素或默认值</returns>
    public static T YSafeDequeue<T>(this Queue<T> queue, T defaultValue = default!)
    {
        if (queue == null || queue.Count == 0)
            return defaultValue;

        try
        {
            return queue.Dequeue();
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Queue 安全查看队首元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="queue">队列</param>
    /// <param name="defaultValue">队列为空时的默认值</param>
    /// <returns>队首元素或默认值</returns>
    public static T YSafePeek<T>(this Queue<T> queue, T defaultValue = default!)
    {
        if (queue == null || queue.Count == 0)
            return defaultValue;

        try
        {
            return queue.Peek();
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Stack 安全入栈
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="stack">栈</param>
    /// <param name="item">要入栈的元素</param>
    /// <returns>是否入栈成功</returns>
    public static bool YSafePush<T>(this Stack<T> stack, T item)
    {
        if (stack == null)
            return false;

        try
        {
            stack.Push(item);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Stack 安全出栈
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="stack">栈</param>
    /// <param name="defaultValue">栈为空时的默认值</param>
    /// <returns>出栈的元素或默认值</returns>
    public static T YSafePop<T>(this Stack<T> stack, T defaultValue = default!)
    {
        if (stack == null || stack.Count == 0)
            return defaultValue;

        try
        {
            return stack.Pop();
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Stack 安全查看栈顶元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="stack">栈</param>
    /// <param name="defaultValue">栈为空时的默认值</param>
    /// <returns>栈顶元素或默认值</returns>
    public static T YSafePeek<T>(this Stack<T> stack, T defaultValue = default!)
    {
        if (stack == null || stack.Count == 0)
            return defaultValue;

        try
        {
            return stack.Peek();
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 私有辅助方法

    private static IEnumerable<IEnumerable<T>> GetPermutations<T>(List<T> list, int length)
    {
        if (length == 1)
        {
            foreach (var item in list)
                yield return new[] { item };
        }
        else
        {
            for (int i = 0; i < list.Count; i++)
            {
                var remaining = list.Where((_, index) => index != i).ToList();
                foreach (var permutation in GetPermutations(remaining, length - 1))
                {
                    yield return new[] { list[i] }.Concat(permutation);
                }
            }
        }
    }

    private static IEnumerable<IEnumerable<T>> GetCombinations<T>(List<T> list, int length)
    {
        if (length == 0)
        {
            yield return Enumerable.Empty<T>();
        }
        else if (length == 1)
        {
            foreach (var item in list)
                yield return new[] { item };
        }
        else
        {
            for (int i = 0; i <= list.Count - length; i++)
            {
                var head = list[i];
                var tail = list.Skip(i + 1).ToList();

                foreach (var combination in GetCombinations(tail, length - 1))
                {
                    yield return new[] { head }.Concat(combination);
                }
            }
        }
    }

    private static bool IncrementIndices<T>(int[] indices, List<List<T>> sequences)
    {
        for (int i = indices.Length - 1; i >= 0; i--)
        {
            indices[i]++;
            if (indices[i] < sequences[i].Count)
                return true;
            indices[i] = 0;
        }
        return false;
    }

    #endregion
}
