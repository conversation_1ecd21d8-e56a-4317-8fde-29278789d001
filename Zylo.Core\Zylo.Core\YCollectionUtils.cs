using System;
using System.Collections.Generic;
using System.Linq;
using Zylo.Core.Compatibility;

namespace Zylo.Core;

/// <summary>
/// 集合操作工具类
/// 提供高级的集合处理功能
/// </summary>
public static class YCollectionUtils
{
    #region 集合比较和分析

    /// <summary>
    /// 比较两个集合的差异
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">源集合</param>
    /// <param name="target">目标集合</param>
    /// <returns>差异结果</returns>
    /// <example>
    /// <code>
    /// var list1 = new[] { 1, 2, 3, 4 };
    /// var list2 = new[] { 3, 4, 5, 6 };
    /// var diff = YCollectionUtils.CompareDifference(list1, list2);
    /// // diff.OnlyInSource: [1, 2]
    /// // diff.OnlyInTarget: [5, 6]
    /// // diff.Common: [3, 4]
    /// </code>
    /// </example>
    public static CollectionDifference<T> CompareDifference<T>(IEnumerable<T> source, IEnumerable<T> target)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (target == null) throw new ArgumentNullException(nameof(target));

        var sourceSet = new HashSet<T>(source);
        var targetSet = new HashSet<T>(target);

        return new CollectionDifference<T>
        {
            OnlyInSource = sourceSet.Except(targetSet).ToList(),
            OnlyInTarget = targetSet.Except(sourceSet).ToList(),
            Common = sourceSet.Intersect(targetSet).ToList()
        };
    }

    /// <summary>
    /// 计算集合的基本统计信息
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>统计信息</returns>
    public static (int TotalCount, int UniqueCount) GetBasicStatistics<T>(IEnumerable<T> source)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

        var list = source.ToList();
        var uniqueCount = list.Distinct().Count();

        return (list.Count, uniqueCount);
    }

    #endregion

    #region 集合转换和重组

    /// <summary>
    /// 将集合按指定大小分组
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="groupSize">组大小</param>
    /// <returns>分组后的集合</returns>
    public static IEnumerable<IEnumerable<T>> GroupBySize<T>(IEnumerable<T> source, int groupSize)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (groupSize <= 0) throw new ArgumentException("Group size must be positive", nameof(groupSize));

        return source.YChunk(groupSize);
    }

    /// <summary>
    /// 交错合并多个集合
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="collections">要合并的集合</param>
    /// <returns>交错合并后的集合</returns>
    /// <example>
    /// <code>
    /// var list1 = new[] { 1, 2, 3 };
    /// var list2 = new[] { "a", "b", "c" };
    /// var result = YCollectionUtils.Interleave(list1.Cast&lt;object&gt;(), list2.Cast&lt;object&gt;());
    /// // 结果: [1, "a", 2, "b", 3, "c"]
    /// </code>
    /// </example>
    public static IEnumerable<T> Interleave<T>(params IEnumerable<T>[] collections)
    {
        if (collections == null || collections.Length == 0)
            yield break;

        var enumerators = collections.Select(c => c.GetEnumerator()).ToArray();
        
        try
        {
            bool hasMore;
            do
            {
                hasMore = false;
                foreach (var enumerator in enumerators)
                {
                    if (enumerator.MoveNext())
                    {
                        yield return enumerator.Current;
                        hasMore = true;
                    }
                }
            } while (hasMore);
        }
        finally
        {
            foreach (var enumerator in enumerators)
            {
                enumerator.Dispose();
            }
        }
    }

    /// <summary>
    /// 旋转集合（将第一个元素移到最后）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="positions">旋转位置数，正数向右旋转，负数向左旋转</param>
    /// <returns>旋转后的集合</returns>
    public static IEnumerable<T> Rotate<T>(IEnumerable<T> source, int positions)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

        var list = source.ToList();
        if (list.Count == 0) return list;

        positions = positions % list.Count;
        if (positions < 0) positions += list.Count;

        return list.Skip(positions).Concat(list.Take(positions));
    }

    #endregion

    #region 集合搜索和过滤

    /// <summary>
    /// 模糊搜索
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="searchTerm">搜索词</param>
    /// <param name="selector">字符串选择器</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>匹配的元素</returns>
    public static IEnumerable<T> FuzzySearch<T>(IEnumerable<T> source, string searchTerm, 
        Func<T, string> selector, bool ignoreCase = true)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));
        if (selector == null) throw new ArgumentNullException(nameof(selector));
        if (string.IsNullOrEmpty(searchTerm)) return source;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

        return source.Where(item =>
        {
            var text = selector(item);
            return text != null && text.ContainsCompat(searchTerm, comparison);
        });
    }

    /// <summary>
    /// 查找重复元素
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>重复的元素</returns>
    public static IEnumerable<T> FindDuplicates<T>(IEnumerable<T> source)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

        return source.GroupBy(x => x)
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key);
    }

    /// <summary>
    /// 查找唯一元素（只出现一次的元素）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <returns>唯一的元素</returns>
    public static IEnumerable<T> FindUniques<T>(IEnumerable<T> source)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

        return source.GroupBy(x => x)
                    .Where(g => g.Count() == 1)
                    .Select(g => g.Key);
    }

    #endregion

    #region 集合验证

    /// <summary>
    /// 检查集合是否已排序
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="source">数据源</param>
    /// <param name="comparer">比较器</param>
    /// <returns>是否已排序</returns>
    public static bool IsSorted<T>(IEnumerable<T> source, IComparer<T>? comparer = null)
    {
        if (source == null) throw new ArgumentNullException(nameof(source));

        comparer ??= Comparer<T>.Default;
        
        using var enumerator = source.GetEnumerator();
        if (!enumerator.MoveNext()) return true;

        var previous = enumerator.Current;
        while (enumerator.MoveNext())
        {
            if (comparer.Compare(previous, enumerator.Current) > 0)
                return false;
            previous = enumerator.Current;
        }

        return true;
    }

    /// <summary>
    /// 检查两个集合是否相等（忽略顺序）
    /// </summary>
    /// <typeparam name="T">元素类型</typeparam>
    /// <param name="first">第一个集合</param>
    /// <param name="second">第二个集合</param>
    /// <returns>是否相等</returns>
    public static bool SequenceEqualIgnoreOrder<T>(IEnumerable<T> first, IEnumerable<T> second) where T : notnull
    {
        if (first == null) throw new ArgumentNullException(nameof(first));
        if (second == null) throw new ArgumentNullException(nameof(second));

        var firstCounts = first.GroupBy(x => x).ToDictionary(g => g.Key, g => g.Count());
        var secondCounts = second.GroupBy(x => x).ToDictionary(g => g.Key, g => g.Count());

        return firstCounts.Count == secondCounts.Count &&
               firstCounts.All(kvp => secondCounts.TryGetValue(kvp.Key, out var count) && count == kvp.Value);
    }

    #endregion
}

/// <summary>
/// 集合差异结果
/// </summary>
/// <typeparam name="T">元素类型</typeparam>
public class CollectionDifference<T>
{
    /// <summary>只在源集合中的元素</summary>
    public List<T> OnlyInSource { get; set; } = new();
    
    /// <summary>只在目标集合中的元素</summary>
    public List<T> OnlyInTarget { get; set; } = new();
    
    /// <summary>两个集合共有的元素</summary>
    public List<T> Common { get; set; } = new();
}


