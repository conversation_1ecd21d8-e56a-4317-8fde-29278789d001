namespace Zylo.Core;

/// <summary>
/// Y系列类型转换扩展方法
/// 提供安全的类型转换功能，避免命名冲突，支持默认值
/// </summary>
public static class YConverter
{
    #region String 转换扩展方法

    /// <summary>
    /// 字符串转换为 int，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 int 值</returns>
    /// <example>
    /// <code>
    /// int value1 = "123".YToInt();        // 返回 123
    /// int value2 = "abc".YToInt(0);       // 返回 0
    /// int value3 = "".YToInt(-1);         // 返回 -1
    /// </code>
    /// </example>
    public static int YToInt(this string str, int defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用兼容的解析方法
        return int.TryParse(str, out int result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 long，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 long 值</returns>
    public static long YToLong(this string str, long defaultValue = 0L)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用兼容的解析方法
        return long.TryParse(str, out long result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 short，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 short 值</returns>
    public static short YToShort(this string str, short defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return short.TryParse(str, out short result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 double，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0.0</param>
    /// <returns>转换后的 double 值</returns>
    public static double YToDouble(this string str, double defaultValue = 0.0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用兼容的解析方法
        return double.TryParse(str, NumberStyles.Float, CultureInfo.InvariantCulture, out double result)
            ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 decimal，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 decimal 值</returns>
    public static decimal YToDecimal(this string str, decimal defaultValue = 0m)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用兼容的解析方法
        return decimal.TryParse(str, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal result)
            ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 float，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0.0f</param>
    /// <returns>转换后的 float 值</returns>
    public static float YToFloat(this string str, float defaultValue = 0.0f)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return float.TryParse(str, NumberStyles.Float, CultureInfo.InvariantCulture, out float result) 
            ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 bool
    /// 支持多种格式：true/false, 1/0, yes/no, on/off, 是/否
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 false</param>
    /// <returns>转换后的 bool 值</returns>
    /// <example>
    /// <code>
    /// bool result1 = "true".YToBool();     // true
    /// bool result2 = "1".YToBool();        // true
    /// bool result3 = "是".YToBool();       // true
    /// bool result4 = "yes".YToBool();      // true
    /// bool result5 = "on".YToBool();       // true
    /// bool result6 = "false".YToBool();    // false
    /// bool result7 = "0".YToBool();        // false
    /// bool result8 = "否".YToBool();       // false
    /// bool result9 = "invalid".YToBool();  // false (默认值)
    /// </code>
    /// </example>
    public static bool YToBool(this string str, bool defaultValue = false)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        var lowerStr = str.Trim().ToLowerInvariant();
        
        return lowerStr switch
        {
            "true" or "1" or "yes" or "on" or "是" or "真" or "t" or "y" => true,
            "false" or "0" or "no" or "off" or "否" or "假" or "f" or "n" => false,
            _ => bool.TryParse(str, out bool result) ? result : defaultValue
        };
    }

    /// <summary>
    /// 字符串转换为 char，取第一个字符
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 '\0'</param>
    /// <returns>转换后的 char 值</returns>
    public static char YToChar(this string str, char defaultValue = '\0')
    {
        return string.IsNullOrEmpty(str) ? defaultValue : str[0];
    }

    /// <summary>
    /// 字符串转换为 DateTime，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 DateTime.MinValue</param>
    /// <returns>转换后的 DateTime 值</returns>
    public static DateTime YToDateTime(this string str, DateTime? defaultValue = null)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue ?? DateTime.MinValue;

        return DateTime.TryParse(str, out DateTime result) ? result : (defaultValue ?? DateTime.MinValue);
    }

    /// <summary>
    /// 字符串转换为 Guid，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 Guid.Empty</param>
    /// <returns>转换后的 Guid 值</returns>
    public static Guid YToGuid(this string str, Guid? defaultValue = null)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue ?? Guid.Empty;

        return Guid.TryParse(str, out Guid result) ? result : (defaultValue ?? Guid.Empty);
    }

    /// <summary>
    /// 字符串转换为 byte，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 byte 值</returns>
    public static byte YToByte(this string str, byte defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return byte.TryParse(str, out byte result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 sbyte，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 sbyte 值</returns>
    public static sbyte YToSByte(this string str, sbyte defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return sbyte.TryParse(str, out sbyte result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 uint，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 uint 值</returns>
    public static uint YToUInt(this string str, uint defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return uint.TryParse(str, out uint result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 ulong，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 ulong 值</returns>
    public static ulong YToULong(this string str, ulong defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return ulong.TryParse(str, out ulong result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 ushort，转换失败返回默认值
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的 ushort 值</returns>
    public static ushort YToUShort(this string str, ushort defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return ushort.TryParse(str, out ushort result) ? result : defaultValue;
    }

    #endregion

    #region 新增时间相关转换 (.NET 8+ 优化)

    /// <summary>
    /// 字符串转换为 TimeSpan，转换失败返回默认值
    /// 支持格式: "1.23:45:30", "23:45:30", "45:30", "30"
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 TimeSpan.Zero</param>
    /// <returns>转换后的 TimeSpan 值</returns>
    /// <example>
    /// <code>
    /// var time1 = "1.23:45:30".YToTimeSpan();    // 1天23小时45分30秒
    /// var time2 = "23:45:30".YToTimeSpan();      // 23小时45分30秒
    /// var time3 = "invalid".YToTimeSpan();       // TimeSpan.Zero
    /// </code>
    /// </example>
    public static TimeSpan YToTimeSpan(this string str, TimeSpan defaultValue = default)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用兼容的解析方法
        return TimeSpan.TryParse(str, out TimeSpan result) ? result : defaultValue;
    }

#if NET8_0_OR_GREATER
    /// <summary>
    /// 字符串转换为 DateOnly，转换失败返回默认值
    /// 仅在 .NET 8+ 中可用
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 DateOnly.MinValue</param>
    /// <returns>转换后的 DateOnly 值</returns>
    /// <example>
    /// <code>
    /// var date1 = "2024-01-15".YToDateOnly();    // DateOnly(2024, 1, 15)
    /// var date2 = "invalid".YToDateOnly();       // DateOnly.MinValue
    /// </code>
    /// </example>
    public static DateOnly YToDateOnly(this string str, DateOnly defaultValue = default)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用 Span<char> 优化性能
        return DateOnly.TryParse(str.AsSpan(), out DateOnly result) ? result : defaultValue;
    }

    /// <summary>
    /// 字符串转换为 TimeOnly，转换失败返回默认值
    /// 仅在 .NET 8+ 中可用
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 TimeOnly.MinValue</param>
    /// <returns>转换后的 TimeOnly 值</returns>
    /// <example>
    /// <code>
    /// var time1 = "14:30:45".YToTimeOnly();      // TimeOnly(14, 30, 45)
    /// var time2 = "invalid".YToTimeOnly();       // TimeOnly.MinValue
    /// </code>
    /// </example>
    public static TimeOnly YToTimeOnly(this string str, TimeOnly defaultValue = default)
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        // 使用 Span<char> 优化性能
        return TimeOnly.TryParse(str.AsSpan(), out TimeOnly result) ? result : defaultValue;
    }
#endif

    #endregion

    #region 数值格式化转换

    /// <summary>
    /// decimal 转换为格式化的货币字符串
    /// </summary>
    /// <param name="value">要转换的 decimal 值</param>
    /// <param name="format">格式化字符串，默认为 "C"</param>
    /// <param name="culture">文化信息，默认为当前文化</param>
    /// <returns>格式化后的字符串</returns>
    /// <example>
    /// <code>
    /// var currency1 = 123.45m.YToFormattedString();           // "¥123.45" (中文环境)
    /// var currency2 = 123.45m.YToFormattedString("C2");       // "¥123.45"
    /// var number = 123.45m.YToFormattedString("N2");          // "123.45"
    /// </code>
    /// </example>
    public static string YToFormattedString(this decimal value, string format = "C", CultureInfo? culture = null)
    {
        culture ??= CultureInfo.CurrentCulture;
        return value.ToString(format, culture);
    }

    /// <summary>
    /// double 转换为百分比字符串
    /// </summary>
    /// <param name="value">要转换的 double 值 (0.0 - 1.0)</param>
    /// <param name="decimals">小数位数，默认为 2</param>
    /// <returns>百分比字符串</returns>
    /// <example>
    /// <code>
    /// var percent1 = 0.1234.YToPercentageString();    // "12.34%"
    /// var percent2 = 0.5.YToPercentageString(1);      // "50.0%"
    /// var percent3 = 1.0.YToPercentageString(0);      // "100%"
    /// </code>
    /// </example>
    public static string YToPercentageString(this double value, int decimals = 2)
    {
        var percentage = value * 100;
        return $"{percentage.ToString($"F{decimals}", CultureInfo.InvariantCulture)}%";
    }

    /// <summary>
    /// long 转换为文件大小字符串
    /// </summary>
    /// <param name="bytes">字节数</param>
    /// <param name="decimals">小数位数，默认为 2</param>
    /// <returns>文件大小字符串</returns>
    /// <example>
    /// <code>
    /// var size1 = 1024L.YToFileSizeString();          // "1.00 KB"
    /// var size2 = 1048576L.YToFileSizeString();       // "1.00 MB"
    /// var size3 = 1073741824L.YToFileSizeString(1);   // "1.0 GB"
    /// </code>
    /// </example>
    public static string YToFileSizeString(this long bytes, int decimals = 2)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB", "PB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size.ToString($"F{decimals}", CultureInfo.InvariantCulture)} {sizes[order]}";
    }

    #endregion

    #region 数值类型转换扩展方法

    /// <summary>
    /// int 转换为字符串
    /// </summary>
    /// <param name="value">要转换的 int 值</param>
    /// <param name="format">格式化字符串，默认为 null</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this int value, string? format = null)
    {
        return string.IsNullOrEmpty(format) ? value.ToString() : value.ToString(format);
    }

    /// <summary>
    /// int 转换为 bool
    /// 0 为 false，其他值为 true
    /// </summary>
    /// <param name="value">要转换的 int 值</param>
    /// <returns>转换后的 bool 值</returns>
    public static bool YToBool(this int value)
    {
        return value != 0;
    }

    /// <summary>
    /// long 转换为字符串
    /// </summary>
    /// <param name="value">要转换的 long 值</param>
    /// <param name="format">格式化字符串，默认为 null</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this long value, string? format = null)
    {
        return string.IsNullOrEmpty(format) ? value.ToString() : value.ToString(format);
    }

    /// <summary>
    /// double 转换为字符串
    /// </summary>
    /// <param name="value">要转换的 double 值</param>
    /// <param name="format">格式化字符串，默认为 null</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this double value, string? format = null)
    {
        return string.IsNullOrEmpty(format) ? value.ToString(CultureInfo.InvariantCulture) 
            : value.ToString(format, CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// decimal 转换为字符串
    /// </summary>
    /// <param name="value">要转换的 decimal 值</param>
    /// <param name="format">格式化字符串，默认为 null</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this decimal value, string? format = null)
    {
        return string.IsNullOrEmpty(format) ? value.ToString(CultureInfo.InvariantCulture) 
            : value.ToString(format, CultureInfo.InvariantCulture);
    }

    #endregion

    #region Bool 转换扩展方法

    /// <summary>
    /// bool 转换为 int
    /// true 为 1，false 为 0
    /// </summary>
    /// <param name="value">要转换的 bool 值</param>
    /// <returns>转换后的 int 值</returns>
    public static int YToInt(this bool value)
    {
        return value ? 1 : 0;
    }

    /// <summary>
    /// bool 转换为字符串
    /// </summary>
    /// <param name="value">要转换的 bool 值</param>
    /// <param name="trueValue">true 时的字符串，默认为 "True"</param>
    /// <param name="falseValue">false 时的字符串，默认为 "False"</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this bool value, string trueValue = "True", string falseValue = "False")
    {
        return value ? trueValue : falseValue;
    }

    /// <summary>
    /// bool 转换为中文字符串
    /// </summary>
    /// <param name="value">要转换的 bool 值</param>
    /// <returns>转换后的中文字符串</returns>
    public static string YToChineseString(this bool value)
    {
        return value ? "是" : "否";
    }

    #endregion

    #region 安全转换方法

    /// <summary>
    /// 安全转换，带异常处理
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="value">要转换的值</param>
    /// <param name="defaultValue">转换失败时的默认值</param>
    /// <returns>转换后的值</returns>
    /// <example>
    /// <code>
    /// var result1 = "123".YSafeConvert&lt;int&gt;(0);           // 123
    /// var result2 = "abc".YSafeConvert&lt;int&gt;(0);           // 0
    /// var result3 = DBNull.Value.YSafeConvert&lt;string&gt;(""); // ""
    /// </code>
    /// </example>
    public static T YSafeConvert<T>(this object value, T defaultValue = default!)
    {
        try
        {
            if (value == null || value == DBNull.Value)
                return defaultValue;

            if (value is T directValue)
                return directValue;

            return (T)Convert.ChangeType(value, typeof(T), CultureInfo.InvariantCulture);
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion

    #region 枚举转换扩展方法

    /// <summary>
    /// 字符串转换为枚举，转换失败返回默认值
    /// </summary>
    /// <typeparam name="TEnum">枚举类型</typeparam>
    /// <param name="str">要转换的字符串</param>
    /// <param name="defaultValue">转换失败时的默认值</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为 true</param>
    /// <returns>转换后的枚举值</returns>
    /// <example>
    /// <code>
    /// enum Color { Red, Green, Blue }
    /// var color1 = "Red".YToEnum&lt;Color&gt;();           // Color.Red
    /// var color2 = "red".YToEnum&lt;Color&gt;();           // Color.Red (忽略大小写)
    /// var color3 = "Invalid".YToEnum&lt;Color&gt;();       // 默认值
    /// </code>
    /// </example>
    public static TEnum YToEnum<TEnum>(this string str, TEnum defaultValue = default!, bool ignoreCase = true)
        where TEnum : struct, Enum
    {
        if (string.IsNullOrWhiteSpace(str))
            return defaultValue;

        return Enum.TryParse<TEnum>(str, ignoreCase, out TEnum result) ? result : defaultValue;
    }

    #endregion

    #region 集合转换扩展方法

    /// <summary>
    /// 字符串转换为字符串数组，使用指定分隔符
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="removeEmptyEntries">是否移除空项，默认为 true</param>
    /// <returns>转换后的字符串数组</returns>
    /// <example>
    /// <code>
    /// var array1 = "a,b,c".YToStringArray();        // ["a", "b", "c"]
    /// var array2 = "1|2|3".YToStringArray("|");     // ["1", "2", "3"]
    /// var array3 = "a,,b".YToStringArray();         // ["a", "b"] (移除空项)
    /// </code>
    /// </example>
    public static string[] YToStringArray(this string str, string separator = ",", bool removeEmptyEntries = true)
    {
        if (string.IsNullOrWhiteSpace(str))
            return Array.Empty<string>();

        var options = removeEmptyEntries ? StringSplitOptions.RemoveEmptyEntries : StringSplitOptions.None;
        return str.Split(new[] { separator }, options)
                  .Select(s => s.Trim())
                  .ToArray();
    }

    /// <summary>
    /// 字符串转换为整数数组，使用指定分隔符
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的整数数组</returns>
    /// <example>
    /// <code>
    /// var array1 = "1,2,3".YToIntArray();           // [1, 2, 3]
    /// var array2 = "1|2|abc".YToIntArray("|");      // [1, 2, 0] (abc转换失败用默认值)
    /// </code>
    /// </example>
    public static int[] YToIntArray(this string str, string separator = ",", int defaultValue = 0)
    {
        if (string.IsNullOrWhiteSpace(str))
            return Array.Empty<int>();

        return str.Split(new[] { separator }, StringSplitOptions.RemoveEmptyEntries)
                  .Select(s => s.Trim().YToInt(defaultValue))
                  .ToArray();
    }

    /// <summary>
    /// 字符串转换为 List&lt;string&gt;，使用指定分隔符
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="removeEmptyEntries">是否移除空项，默认为 true</param>
    /// <returns>转换后的字符串列表</returns>
    public static List<string> YToStringList(this string str, string separator = ",", bool removeEmptyEntries = true)
    {
        return str.YToStringArray(separator, removeEmptyEntries).ToList();
    }

    /// <summary>
    /// 字符串转换为 List&lt;int&gt;，使用指定分隔符
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="defaultValue">转换失败时的默认值，默认为 0</param>
    /// <returns>转换后的整数列表</returns>
    public static List<int> YToIntList(this string str, string separator = ",", int defaultValue = 0)
    {
        return str.YToIntArray(separator, defaultValue).ToList();
    }

    #endregion

    #region 可空类型转换扩展方法

    /// <summary>
    /// 字符串转换为可空 int，转换失败返回 null
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <returns>转换后的可空 int 值</returns>
    public static int? YToNullableInt(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
            return null;

        return int.TryParse(str, out int result) ? result : null;
    }

    /// <summary>
    /// 字符串转换为可空 DateTime，转换失败返回 null
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <returns>转换后的可空 DateTime 值</returns>
    public static DateTime? YToNullableDateTime(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
            return null;

        return DateTime.TryParse(str, out DateTime result) ? result : null;
    }

    /// <summary>
    /// 字符串转换为可空 double，转换失败返回 null
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <returns>转换后的可空 double 值</returns>
    public static double? YToNullableDouble(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
            return null;

        return double.TryParse(str, NumberStyles.Float, CultureInfo.InvariantCulture, out double result) ? result : null;
    }

    /// <summary>
    /// 字符串转换为可空 decimal，转换失败返回 null
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <returns>转换后的可空 decimal 值</returns>
    public static decimal? YToNullableDecimal(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
            return null;

        return decimal.TryParse(str, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal result) ? result : null;
    }

    /// <summary>
    /// 字符串转换为可空 bool，转换失败返回 null
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <returns>转换后的可空 bool 值</returns>
    public static bool? YToNullableBool(this string str)
    {
        if (string.IsNullOrWhiteSpace(str))
            return null;

        var lowerStr = str.Trim().ToLowerInvariant();

        return lowerStr switch
        {
            "true" or "1" or "yes" or "on" or "是" or "真" or "t" or "y" => true,
            "false" or "0" or "no" or "off" or "否" or "假" or "f" or "n" => false,
            _ => bool.TryParse(str, out bool result) ? result : null
        };
    }

    #endregion

    #region 集合扩展转换方法

    /// <summary>
    /// 字符串数组转换为单个字符串，使用指定分隔符
    /// </summary>
    /// <param name="array">字符串数组</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YToString(this string[] array, string separator = ",")
    {
        return array == null ? string.Empty : string.Join(separator, array);
    }

    /// <summary>
    /// 整数数组转换为字符串，使用指定分隔符
    /// </summary>
    /// <param name="array">整数数组</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YToString(this int[] array, string separator = ",")
    {
        return array == null ? string.Empty : string.Join(separator, array);
    }

    /// <summary>
    /// List&lt;string&gt; 转换为字符串，使用指定分隔符
    /// </summary>
    /// <param name="list">字符串列表</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YToString(this List<string> list, string separator = ",")
    {
        return list == null ? string.Empty : string.Join(separator, list);
    }

    /// <summary>
    /// List&lt;int&gt; 转换为字符串，使用指定分隔符
    /// </summary>
    /// <param name="list">整数列表</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>连接后的字符串</returns>
    public static string YToString(this List<int> list, string separator = ",")
    {
        return list == null ? string.Empty : string.Join(separator, list);
    }

    #endregion

    #region 元组转换扩展方法

    /// <summary>
    /// 字符串转换为二元组
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="defaultValue1">第一个元素的默认值</param>
    /// <param name="defaultValue2">第二个元素的默认值</param>
    /// <returns>转换后的二元组</returns>
    /// <example>
    /// <code>
    /// var tuple1 = "张三,25".YToTuple();              // ("张三", "25")
    /// var tuple2 = "John|30".YToTuple("|");           // ("John", "30")
    /// var tuple3 = "incomplete".YToTuple(",", "默认", "0"); // ("incomplete", "0")
    /// </code>
    /// </example>
    public static (string, string) YToTuple(this string str, string separator = ",", string defaultValue1 = "", string defaultValue2 = "")
    {
        if (string.IsNullOrWhiteSpace(str))
            return (defaultValue1, defaultValue2);

        var parts = str.Split(new[] { separator }, StringSplitOptions.None);
        var item1 = parts.Length > 0 ? parts[0].Trim() : defaultValue1;
        var item2 = parts.Length > 1 ? parts[1].Trim() : defaultValue2;

        return (item1, item2);
    }

    /// <summary>
    /// 字符串转换为三元组
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="defaultValue1">第一个元素的默认值</param>
    /// <param name="defaultValue2">第二个元素的默认值</param>
    /// <param name="defaultValue3">第三个元素的默认值</param>
    /// <returns>转换后的三元组</returns>
    /// <example>
    /// <code>
    /// var tuple1 = "张三,25,男".YToTuple3();           // ("张三", "25", "男")
    /// var tuple2 = "John|30|Male".YToTuple3("|");     // ("John", "30", "Male")
    /// </code>
    /// </example>
    public static (string, string, string) YToTuple3(this string str, string separator = ",", string defaultValue1 = "", string defaultValue2 = "", string defaultValue3 = "")
    {
        if (string.IsNullOrWhiteSpace(str))
            return (defaultValue1, defaultValue2, defaultValue3);

        var parts = str.Split(new[] { separator }, StringSplitOptions.None);
        var item1 = parts.Length > 0 ? parts[0].Trim() : defaultValue1;
        var item2 = parts.Length > 1 ? parts[1].Trim() : defaultValue2;
        var item3 = parts.Length > 2 ? parts[2].Trim() : defaultValue3;

        return (item1, item2, item3);
    }

    /// <summary>
    /// 字符串转换为类型化二元组
    /// </summary>
    /// <typeparam name="T1">第一个元素的类型</typeparam>
    /// <typeparam name="T2">第二个元素的类型</typeparam>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="defaultValue1">第一个元素的默认值</param>
    /// <param name="defaultValue2">第二个元素的默认值</param>
    /// <returns>转换后的类型化二元组</returns>
    /// <example>
    /// <code>
    /// var tuple1 = "张三,25".YToTuple&lt;string, int&gt;();    // ("张三", 25)
    /// var tuple2 = "3.14,true".YToTuple&lt;double, bool&gt;(); // (3.14, true)
    /// </code>
    /// </example>
    public static (T1, T2) YToTuple<T1, T2>(this string str, string separator = ",", T1 defaultValue1 = default!, T2 defaultValue2 = default!)
    {
        if (string.IsNullOrWhiteSpace(str))
            return (defaultValue1, defaultValue2);

        var parts = str.Split(new[] { separator }, StringSplitOptions.None);
        var item1 = parts.Length > 0 ? parts[0].Trim().YSafeConvert(defaultValue1) : defaultValue1;
        var item2 = parts.Length > 1 ? parts[1].Trim().YSafeConvert(defaultValue2) : defaultValue2;

        return (item1, item2);
    }

    #endregion

    #region 字典转换扩展方法

    /// <summary>
    /// 字符串转换为字典
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="itemSeparator">项目分隔符，默认为分号</param>
    /// <param name="keyValueSeparator">键值分隔符，默认为等号</param>
    /// <returns>转换后的字典</returns>
    /// <example>
    /// <code>
    /// var dict1 = "name=张三;age=25;city=北京".YToDictionary();
    /// var dict2 = "a:1|b:2|c:3".YToDictionary("|", ":");
    /// </code>
    /// </example>
    public static Dictionary<string, string> YToDictionary(this string str, string itemSeparator = ";", string keyValueSeparator = "=")
    {
        var result = new Dictionary<string, string>();

        if (string.IsNullOrWhiteSpace(str))
            return result;

        var items = str.Split(new[] { itemSeparator }, StringSplitOptions.RemoveEmptyEntries);

        foreach (var item in items)
        {
            var keyValue = item.Split(new[] { keyValueSeparator }, 2, StringSplitOptions.None);
            if (keyValue.Length >= 2)
            {
                var key = keyValue[0].Trim();
                var value = keyValue[1].Trim();
                result[key] = value;
            }
        }

        return result;
    }

    /// <summary>
    /// 字典转换为字符串
    /// </summary>
    /// <param name="dictionary">要转换的字典</param>
    /// <param name="itemSeparator">项目分隔符，默认为分号</param>
    /// <param name="keyValueSeparator">键值分隔符，默认为等号</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this Dictionary<string, string> dictionary, string itemSeparator = ";", string keyValueSeparator = "=")
    {
        if (dictionary == null || dictionary.Count == 0)
            return string.Empty;

        return string.Join(itemSeparator, dictionary.Select(kvp => $"{kvp.Key}{keyValueSeparator}{kvp.Value}"));
    }

    #endregion

    #region HashSet 转换扩展方法

    /// <summary>
    /// 字符串转换为 HashSet
    /// </summary>
    /// <param name="str">要转换的字符串</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <param name="removeEmptyEntries">是否移除空项，默认为 true</param>
    /// <returns>转换后的 HashSet</returns>
    /// <example>
    /// <code>
    /// var set1 = "a,b,c,a,b".YToHashSet();           // {"a", "b", "c"} (自动去重)
    /// var set2 = "1|2|3|1".YToHashSet("|");          // {"1", "2", "3"}
    /// </code>
    /// </example>
    public static HashSet<string> YToHashSet(this string str, string separator = ",", bool removeEmptyEntries = true)
    {
        if (string.IsNullOrWhiteSpace(str))
            return new HashSet<string>();

        var options = removeEmptyEntries ? StringSplitOptions.RemoveEmptyEntries : StringSplitOptions.None;
        var items = str.Split(new[] { separator }, options)
                      .Select(s => s.Trim());

        return new HashSet<string>(items);
    }

    /// <summary>
    /// HashSet 转换为字符串
    /// </summary>
    /// <param name="hashSet">要转换的 HashSet</param>
    /// <param name="separator">分隔符，默认为逗号</param>
    /// <returns>转换后的字符串</returns>
    public static string YToString(this HashSet<string> hashSet, string separator = ",")
    {
        return hashSet == null ? string.Empty : string.Join(separator, hashSet);
    }

    #endregion
}
