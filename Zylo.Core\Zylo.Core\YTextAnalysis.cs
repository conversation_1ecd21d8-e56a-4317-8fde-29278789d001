namespace Zylo.Core;

/// <summary>
/// 文本分析结果
/// </summary>
public class YTextAnalysis
{
    /// <summary>
    /// 字符总数（包含空格和标点）
    /// </summary>
    public int CharacterCount { get; set; }

    /// <summary>
    /// 字符数（不包含空格）
    /// </summary>
    public int CharacterCountNoSpaces { get; set; }

    /// <summary>
    /// 单词数量
    /// </summary>
    public int WordCount { get; set; }

    /// <summary>
    /// 行数
    /// </summary>
    public int LineCount { get; set; }

    /// <summary>
    /// 段落数量
    /// </summary>
    public int ParagraphCount { get; set; }

    /// <summary>
    /// 句子数量
    /// </summary>
    public int SentenceCount { get; set; }

    /// <summary>
    /// 预计阅读时间（基于平均阅读速度）
    /// </summary>
    public TimeSpan EstimatedReadingTime { get; set; }

    /// <summary>
    /// 是否包含中文字符
    /// </summary>
    public bool ContainsChinese { get; set; }

    /// <summary>
    /// 检测到的主要语言
    /// </summary>
    public string DetectedLanguage { get; set; } = "unknown";

    /// <summary>
    /// 可读性评分（0-100，分数越高越易读）
    /// </summary>
    public double ReadabilityScore { get; set; }

    /// <summary>
    /// 情感倾向评分（-1到1，负数表示消极，正数表示积极）
    /// </summary>
    public double SentimentScore { get; set; }

    /// <summary>
    /// 平均单词长度
    /// </summary>
    public double AverageWordLength { get; set; }

    /// <summary>
    /// 平均句子长度（单词数）
    /// </summary>
    public double AverageSentenceLength { get; set; }

    /// <summary>
    /// 最常用的单词（前10个）
    /// </summary>
    public Dictionary<string, int> TopWords { get; set; } = new();

    /// <summary>
    /// 提取的关键词
    /// </summary>
    public string[] Keywords { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 文本复杂度等级（1-5，1最简单，5最复杂）
    /// </summary>
    public int ComplexityLevel { get; set; }

    /// <summary>
    /// 是否包含特殊字符
    /// </summary>
    public bool ContainsSpecialCharacters { get; set; }

    /// <summary>
    /// 数字出现次数
    /// </summary>
    public int NumberCount { get; set; }

    /// <summary>
    /// 大写字母比例
    /// </summary>
    public double UppercaseRatio { get; set; }

    /// <summary>
    /// 标点符号数量
    /// </summary>
    public int PunctuationCount { get; set; }

    /// <summary>
    /// 返回分析结果的摘要字符串
    /// </summary>
    /// <returns>分析摘要</returns>
    public override string ToString()
    {
        return $"字符数: {CharacterCount}, 单词数: {WordCount}, 行数: {LineCount}, " +
               $"预计阅读时间: {EstimatedReadingTime:mm\\:ss}, 语言: {DetectedLanguage}, " +
               $"可读性: {ReadabilityScore:F1}, 复杂度: {ComplexityLevel}";
    }

    /// <summary>
    /// 获取详细的分析报告
    /// </summary>
    /// <returns>详细分析报告</returns>
    public string GetDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        
        report.AppendLine("=== 文本分析报告 ===");
        report.AppendLine($"字符统计:");
        report.AppendLine($"  总字符数: {CharacterCount}");
        report.AppendLine($"  字符数(无空格): {CharacterCountNoSpaces}");
        report.AppendLine($"  单词数: {WordCount}");
        report.AppendLine($"  行数: {LineCount}");
        report.AppendLine($"  段落数: {ParagraphCount}");
        report.AppendLine($"  句子数: {SentenceCount}");
        
        report.AppendLine($"\n结构分析:");
        report.AppendLine($"  平均单词长度: {AverageWordLength:F1} 字符");
        report.AppendLine($"  平均句子长度: {AverageSentenceLength:F1} 单词");
        report.AppendLine($"  预计阅读时间: {EstimatedReadingTime:mm\\:ss}");
        
        report.AppendLine($"\n语言特征:");
        report.AppendLine($"  检测语言: {DetectedLanguage}");
        report.AppendLine($"  包含中文: {(ContainsChinese ? "是" : "否")}");
        report.AppendLine($"  大写字母比例: {UppercaseRatio:P1}");
        
        report.AppendLine($"\n质量评估:");
        report.AppendLine($"  可读性评分: {ReadabilityScore:F1}/100");
        report.AppendLine($"  复杂度等级: {ComplexityLevel}/5");
        report.AppendLine($"  情感倾向: {SentimentScore:F2} ({GetSentimentDescription()})");
        
        report.AppendLine($"\n其他统计:");
        report.AppendLine($"  数字出现次数: {NumberCount}");
        report.AppendLine($"  标点符号数量: {PunctuationCount}");
        report.AppendLine($"  包含特殊字符: {(ContainsSpecialCharacters ? "是" : "否")}");
        
        if (TopWords.Any())
        {
            report.AppendLine($"\n高频词汇:");
            foreach (var word in TopWords.Take(5))
            {
                report.AppendLine($"  {word.Key}: {word.Value} 次");
            }
        }
        
        if (Keywords.Any())
        {
            report.AppendLine($"\n关键词: {string.Join(", ", Keywords.Take(10))}");
        }
        
        return report.ToString();
    }

    /// <summary>
    /// 获取情感描述
    /// </summary>
    /// <returns>情感描述文本</returns>
    private string GetSentimentDescription()
    {
        return SentimentScore switch
        {
            > 0.5 => "非常积极",
            > 0.2 => "积极",
            > -0.2 => "中性",
            > -0.5 => "消极",
            _ => "非常消极"
        };
    }
}
