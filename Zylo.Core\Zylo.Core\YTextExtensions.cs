using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using Zylo.Core.Compatibility;
using Zylo.Core.Compatibility;
#if NET48 || NETFRAMEWORK
using System.Web;
#endif

namespace Zylo.Core;

/// <summary>
/// Zylo 文本处理扩展方法
/// 提供全面的文本操作、验证、格式化和分析功能
///
/// 功能模块：
/// - 基础文本操作：清理、标准化、基本操作
/// - 文本截取和提取：各种截取、提取功能
/// - 智能文本搜索：统一的搜索和查找功能（支持中文、英文、符号）
/// - 文本替换和转换：替换、转换、格式化
/// - 文本验证和检查：各种验证功能
/// - 文本分析和统计：分析、统计、评估功能
/// - 高级文本处理：复杂的文本处理功能
/// - 文本安全和编码：安全、加密、编码相关
/// </summary>
public static class YTextExtensions
{
    #region 1. 基础文本操作

    /// <summary>
    /// 移除所有类型的空白字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>清理后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTrimAll(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return Regex.Replace(text, @"\s+", "");
    }

    /// <summary>
    /// 移除多余的空格，保留单个空格
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>标准化后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YRemoveExtraSpaces(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return Regex.Replace(text.Trim(), @"\s+", " ");
    }

    /// <summary>
    /// 综合文本清理：移除特殊字符、标准化空白
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="keepBasicPunctuation">是否保留基本标点符号</param>
    /// <returns>清理后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YCleanText(this string? text, bool keepBasicPunctuation = true)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        // 移除控制字符
        var cleaned = Regex.Replace(text, @"[\x00-\x1F\x7F]", "");
        
        // 标准化空白字符
        cleaned = cleaned.YRemoveExtraSpaces();

        // 如果不保留基本标点，则移除特殊字符
        if (!keepBasicPunctuation)
        {
            cleaned = Regex.Replace(cleaned, @"[^\w\s\u4e00-\u9fff]", "");
        }

        return cleaned.Trim();
    }

    /// <summary>
    /// 移除特殊字符，保留字母、数字和指定字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="keep">要保留的特殊字符</param>
    /// <returns>清理后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YRemoveSpecialChars(this string? text, string keep = "")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        var pattern = keep.Length > 0 
            ? $@"[^\w\s\u4e00-\u9fff{Regex.Escape(keep)}]"
            : @"[^\w\s\u4e00-\u9fff]";

        return Regex.Replace(text, pattern, "");
    }

    /// <summary>
    /// 标准化空白字符，将所有空白字符转换为单个空格
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>标准化后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YNormalizeWhitespace(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return Regex.Replace(text, @"\s+", " ").Trim();
    }

    #endregion

    #region 2. 文本截取和提取操作

    /// <summary>
    /// 安全截取文本，避免越界
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTruncate(this string? text, int maxLength)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLength <= 0)
            return string.Empty;

        return text.Length <= maxLength ? text : text.SubstringCompat(0, maxLength);
    }

    /// <summary>
    /// 按单词截取文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxWords">最大单词数</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTruncateWords(this string? text, int maxWords)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxWords <= 0)
            return string.Empty;

        var words = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
        
        if (words.Length <= maxWords)
            return text;

        return string.Join(" ", words.Take(maxWords));
    }

    /// <summary>
    /// 截取文本并添加省略号
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">最大长度（包含省略号）</param>
    /// <param name="ellipsis">省略号字符</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YEllipsis(this string? text, int maxLength, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLength <= 0)
            return string.Empty;

        if (text.Length <= maxLength)
            return text;

        var truncateLength = Math.Max(0, maxLength - ellipsis.Length);
        return text.SubstringCompat(0, truncateLength) + ellipsis;
    }

    /// <summary>
    /// 安全左截取
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="length">截取长度</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YLeft(this string? text, int length)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (length <= 0)
            return string.Empty;

        return text.Length <= length ? text : text.SubstringCompat(0, length);
    }

    /// <summary>
    /// 安全右截取
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="length">截取长度</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YRight(this string? text, int length)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (length <= 0)
            return string.Empty;

        return text.Length <= length ? text : text.TakeLastCompat(length);
    }

    /// <summary>
    /// 安全截取文本中间部分，避免越界
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="startIndex">开始索引</param>
    /// <param name="length">截取长度</param>
    /// <returns>截取的中间部分文本，如果输入为 null 则返回空字符串</returns>
    public static string YMid(this string? text, int startIndex, int length)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (startIndex < 0 || length <= 0 || startIndex >= text.Length)
            return string.Empty;

        int endIndex = Math.Min(startIndex + length, text.Length);
        return text.Substring(startIndex, endIndex - startIndex);
    }

    /// <summary>
    /// 截取指定分隔符后的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="delimiter">分隔符</param>
    /// <param name="includeDelimiter">是否包含分隔符</param>
    /// <returns>分隔符后的文本，如果分隔符不存在则返回空字符串</returns>
    public static string YSubstringAfter(this string? text, string delimiter, bool includeDelimiter = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(delimiter))
            return string.Empty;

        int delimiterIndex = text.IndexOf(delimiter, StringComparison.Ordinal);
        if (delimiterIndex < 0)
            return string.Empty;

        int startIndex = includeDelimiter ? delimiterIndex : delimiterIndex + delimiter.Length;
        return text.Substring(startIndex);
    }

    /// <summary>
    /// 截取指定分隔符前的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="delimiter">分隔符</param>
    /// <param name="includeDelimiter">是否包含分隔符</param>
    /// <returns>分隔符前的文本，如果分隔符不存在则返回原文本</returns>
    public static string YSubstringBefore(this string? text, string delimiter, bool includeDelimiter = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(delimiter))
            return text ?? string.Empty;

        int delimiterIndex = text.IndexOf(delimiter, StringComparison.Ordinal);
        if (delimiterIndex < 0)
            return text;

        int endIndex = includeDelimiter ? delimiterIndex + delimiter.Length : delimiterIndex;
        return text.SubstringCompat(0, endIndex);
    }

    /// <summary>
    /// 截取文本最后指定长度的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="length">截取长度</param>
    /// <returns>文本末尾指定长度的内容，如果输入为 null 则返回空字符串</returns>
    public static string YSubstringLast(this string? text, int length)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (length <= 0)
            return string.Empty;

        return text.Length <= length ? text : text.TakeLastCompat(length);
    }

    /// <summary>
    /// 将文本按指定大小分块
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="chunkSize">块大小</param>
    /// <returns>文本块集合，如果输入为 null 则返回空集合</returns>
    public static IEnumerable<string> YChunk(this string? text, int chunkSize)
    {
        if (string.IsNullOrEmpty(text))
            yield break;

        if (chunkSize <= 0)
        {
            yield return text;
            yield break;
        }

        for (int i = 0; i < text.Length; i += chunkSize)
        {
            int length = Math.Min(chunkSize, text.Length - i);
            yield return text.Substring(i, length);
        }
    }

    /// <summary>
    /// 智能截取文本，在单词边界处截断，避免截断单词
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="ellipsis">省略号</param>
    /// <returns>智能截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YSmartTruncate(this string? text, int maxLength, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLength <= 0)
            return string.Empty;

        if (text.Length <= maxLength)
            return text;

        // 如果省略号长度大于等于最大长度，直接返回省略号
        if (ellipsis.Length >= maxLength)
            return ellipsis.SubstringCompat(0, maxLength);

        var truncateLength = maxLength - ellipsis.Length;

        // 查找最后一个空格位置，避免截断单词
        var lastSpaceIndex = text.LastIndexOf(' ', truncateLength);

        if (lastSpaceIndex > 0 && lastSpaceIndex > truncateLength * 0.7) // 至少保留70%的长度
        {
            return text.SubstringCompat(0, lastSpaceIndex).TrimEnd() + ellipsis;
        }

        return text.SubstringCompat(0, truncateLength) + ellipsis;
    }

    /// <summary>
    /// 按句子截取文本，保持句子完整性
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxSentences">最大句子数</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTruncateSentences(this string? text, int maxSentences)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxSentences <= 0)
            return string.Empty;

        // 按句号、问号、感叹号分割句子
        var sentences = Regex.Split(text, @"[.!?]+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .Take(maxSentences)
            .ToArray();

        if (sentences.Length == 0)
            return string.Empty;

        return string.Join(". ", sentences.Select(s => s.Trim())) + ".";
    }

    /// <summary>
    /// 截取指定字符之间的所有内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="startChar">开始字符</param>
    /// <param name="endChar">结束字符</param>
    /// <returns>所有匹配的内容集合，如果输入为 null 则返回空集合</returns>
    public static IEnumerable<string> YExtractBetweenChars(this string? text, char startChar, char endChar)
    {
        if (string.IsNullOrEmpty(text))
            yield break;

        var results = new List<string>();
        var startIndex = -1;

        for (int i = 0; i < text.Length; i++)
        {
            if (text[i] == startChar && startIndex == -1)
            {
                startIndex = i + 1;
            }
            else if (text[i] == endChar && startIndex != -1)
            {
                if (i > startIndex)
                {
                    yield return text.Substring(startIndex, i - startIndex);
                }
                startIndex = -1;
            }
        }
    }

    /// <summary>
    /// 截取最后一个指定分隔符之后的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="delimiter">分隔符</param>
    /// <returns>最后一个分隔符后的内容，如果分隔符不存在则返回原文本</returns>
    public static string YSubstringAfterLast(this string? text, string delimiter)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(delimiter))
            return text ?? string.Empty;

        int lastIndex = text.LastIndexOf(delimiter, StringComparison.Ordinal);
        if (lastIndex < 0)
            return text;

        return text.Substring(lastIndex + delimiter.Length);
    }

    /// <summary>
    /// 截取最后一个指定分隔符之前的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="delimiter">分隔符</param>
    /// <returns>最后一个分隔符前的内容，如果分隔符不存在则返回原文本</returns>
    public static string YSubstringBeforeLast(this string? text, string delimiter)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(delimiter))
            return text ?? string.Empty;

        int lastIndex = text.LastIndexOf(delimiter, StringComparison.Ordinal);
        if (lastIndex < 0)
            return text;

        return text.SubstringCompat(0, lastIndex);
    }

    /// <summary>
    /// 按行截取文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLines">最大行数</param>
    /// <param name="ellipsis">省略号</param>
    /// <returns>截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTruncateLines(this string? text, int maxLines, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLines <= 0)
            return string.Empty;

        var lines = text.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

        if (lines.Length <= maxLines)
            return text;

        var truncatedLines = lines.Take(maxLines).ToArray();
        return string.Join(Environment.NewLine, truncatedLines) + Environment.NewLine + ellipsis;
    }

    /// <summary>
    /// 截取文本中的数字部分
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="includeDecimals">是否包含小数</param>
    /// <returns>提取的数字字符串，如果输入为 null 或无数字则返回空字符串</returns>
    public static string YExtractFirstNumber(this string? text, bool includeDecimals = true)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        var pattern = includeDecimals ? @"-?\d+\.?\d*" : @"-?\d+";
        var match = Regex.Match(text, pattern);

        return match.Success ? match.Value : string.Empty;
    }

    /// <summary>
    /// 截取文本中的字母部分
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>提取的字母字符串，如果输入为 null 或无字母则返回空字符串</returns>
    public static string YExtractLetters(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return new string(text.Where(char.IsLetter).ToArray());
    }

    /// <summary>
    /// 按字节长度截取文本（支持中文）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxBytes">最大字节数</param>
    /// <param name="encoding">编码方式，默认UTF-8</param>
    /// <param name="ellipsis">省略号</param>
    /// <returns>按字节截取后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YTruncateByBytes(this string? text, int maxBytes, Encoding? encoding = null, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxBytes <= 0)
            return string.Empty;

        encoding ??= Encoding.UTF8;
        var ellipsisBytes = encoding.GetByteCount(ellipsis);

        if (ellipsisBytes >= maxBytes)
            return ellipsis.SubstringCompat(0, 1); // 返回省略号的第一个字符

        var availableBytes = maxBytes - ellipsisBytes;
        var textBytes = encoding.GetBytes(text);

        if (textBytes.Length <= maxBytes)
            return text;

        // 确保不会截断多字节字符
        var truncatedText = encoding.GetString(textBytes, 0, availableBytes);

        // 检查最后一个字符是否完整
        var lastCharBytes = encoding.GetByteCount(truncatedText.TakeLastCompat(1));
        var expectedBytes = encoding.GetByteCount(truncatedText);

        if (expectedBytes > availableBytes)
        {
            // 移除最后一个可能不完整的字符
            truncatedText = truncatedText.SubstringCompat(0, truncatedText.Length - 1);
        }

        return truncatedText + ellipsis;
    }

    /// <summary>
    /// 智能分割文本，保持单词完整性
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">每段最大长度</param>
    /// <returns>分割后的文本段落集合，如果输入为 null 则返回空集合</returns>
    public static IEnumerable<string> YSmartSplit(this string? text, int maxLength)
    {
        if (string.IsNullOrEmpty(text))
            yield break;

        if (maxLength <= 0)
        {
            yield return text;
            yield break;
        }

        if (text.Length <= maxLength)
        {
            yield return text;
            yield break;
        }

        var words = text.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
        var currentSegment = new StringBuilder();

        foreach (var word in words)
        {
            // 如果单个单词就超过最大长度，强制分割
            if (word.Length > maxLength)
            {
                if (currentSegment.Length > 0)
                {
                    yield return currentSegment.ToString().Trim();
                    currentSegment.Clear();
                }

                // 分割长单词
                for (int i = 0; i < word.Length; i += maxLength)
                {
                    var chunk = word.Substring(i, Math.Min(maxLength, word.Length - i));
                    yield return chunk;
                }
                continue;
            }

            // 检查添加当前单词是否会超过长度限制
            var testLength = currentSegment.Length + (currentSegment.Length > 0 ? 1 : 0) + word.Length;

            if (testLength > maxLength && currentSegment.Length > 0)
            {
                yield return currentSegment.ToString().Trim();
                currentSegment.Clear();
            }

            if (currentSegment.Length > 0)
                currentSegment.Append(' ');
            currentSegment.Append(word);
        }

        if (currentSegment.Length > 0)
        {
            yield return currentSegment.ToString().Trim();
        }
    }

    /// <summary>
    /// 截取文本的摘要，智能选择重要句子
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">摘要最大长度</param>
    /// <param name="sentenceCount">最大句子数</param>
    /// <returns>文本摘要，如果输入为 null 则返回空字符串</returns>
    public static string YExtractSummary(this string? text, int maxLength = 200, int sentenceCount = 3)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLength <= 0 || sentenceCount <= 0)
            return string.Empty;

        // 按句子分割
        var sentences = Regex.Split(text, @"[.!?]+")
            .Where(s => !string.IsNullOrWhiteSpace(s))
            .Select(s => s.Trim())
            .Where(s => s.Length > 10) // 过滤太短的句子
            .ToArray();

        if (sentences.Length == 0)
            return text.YTruncate(maxLength);

        // 如果句子数量少于要求，返回所有句子
        if (sentences.Length <= sentenceCount)
        {
            var allSentences = string.Join(". ", sentences) + ".";
            return allSentences.Length <= maxLength ? allSentences : allSentences.YTruncate(maxLength);
        }

        // 选择前几个句子作为摘要
        var selectedSentences = sentences.Take(sentenceCount).ToArray();
        var summary = string.Join(". ", selectedSentences) + ".";

        return summary.Length <= maxLength ? summary : summary.YTruncate(maxLength);
    }

    /// <summary>
    /// 按正则表达式模式截取文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="groupIndex">捕获组索引，默认为0（整个匹配）</param>
    /// <returns>匹配的文本，如果输入为 null 或无匹配则返回空字符串</returns>
    public static string YExtractByPattern(this string? text, string pattern, int groupIndex = 0)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
            return string.Empty;

        try
        {
            var match = Regex.Match(text, pattern);
            if (match.Success && groupIndex < match.Groups.Count)
            {
                return match.Groups[groupIndex].Value;
            }
        }
        catch (ArgumentException)
        {
            // 正则表达式无效
        }

        return string.Empty;
    }

    /// <summary>
    /// 截取文本中的标签内容（如HTML标签）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="tagName">标签名称</param>
    /// <param name="includeTag">是否包含标签本身</param>
    /// <returns>标签内容集合，如果输入为 null 则返回空集合</returns>
    public static IEnumerable<string> YExtractTagContent(this string? text, string tagName, bool includeTag = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(tagName))
            return Array.Empty<string>();

        var pattern = includeTag
            ? $@"<{tagName}[^>]*>.*?</{tagName}>"
            : $@"<{tagName}[^>]*>(.*?)</{tagName}>";

        try
        {
            var matches = Regex.Matches(text, pattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);
            var results = new List<string>();

            foreach (Match match in matches)
            {
                if (match.Success)
                {
                    results.Add(includeTag ? match.Value : match.Groups[1].Value);
                }
            }

            return results;
        }
        catch (ArgumentException)
        {
            // 正则表达式无效
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 截取文本的预览，保持格式美观
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="preserveWords">是否保持单词完整</param>
    /// <param name="ellipsis">省略号</param>
    /// <returns>预览文本，如果输入为 null 则返回空字符串</returns>
    public static string YCreatePreview(this string? text, int maxLength = 150, bool preserveWords = true, string ellipsis = "...")
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (maxLength <= 0)
            return string.Empty;

        // 清理文本，移除多余空白
        var cleaned = text.YCleanText().YRemoveExtraSpaces();

        if (cleaned.Length <= maxLength)
            return cleaned;

        if (preserveWords)
        {
            return cleaned.YSmartTruncate(maxLength, ellipsis);
        }
        else
        {
            return cleaned.YEllipsis(maxLength, ellipsis);
        }
    }

    #endregion

    #region 3. 智能文本搜索和查找操作
    //
    // 本区域提供统一的智能文本搜索功能，支持：
    // - 中文、英文、符号等所有字符类型的搜索
    // - 多词同时搜索和位置定位
    // - 左右内容提取和上下文分析
    // - 重复文本和重叠匹配处理
    //

    /// <summary>
    /// 忽略大小写检查是否包含指定文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="value">要查找的文本</param>
    /// <returns>是否包含，如果任一参数为 null 则返回 false</returns>
    public static bool YContainsIgnoreCase(this string? text, string? value)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(value))
            return false;

        return text.ContainsCompat(value, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 忽略大小写查找文本位置
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="value">要查找的文本</param>
    /// <returns>位置索引，未找到或参数为 null 时返回 -1</returns>
    public static int YIndexOfIgnoreCase(this string? text, string? value)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(value))
            return -1;

        return text.IndexOf(value, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 统计指定文本的出现次数
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="value">要统计的文本</param>
    /// <returns>出现次数，如果任一参数为 null 则返回 0</returns>
    public static int YCountOccurrences(this string? text, string? value)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(value))
            return 0;

        int count = 0;
        int index = 0;

        while ((index = text.IndexOf(value, index, StringComparison.Ordinal)) != -1)
        {
            count++;
            index += value.Length;
        }

        return count;
    }

    /// <summary>
    /// 查找两个字符串之间的内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="start">开始标记</param>
    /// <param name="end">结束标记</param>
    /// <returns>两个标记之间的内容，未找到时返回空字符串</returns>
    public static string YFindBetween(this string? text, string? start, string? end)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(start) || string.IsNullOrEmpty(end))
            return string.Empty;

        var startIndex = text.IndexOf(start, StringComparison.Ordinal);
        if (startIndex == -1)
            return string.Empty;

        startIndex += start.Length;
        var endIndex = text.IndexOf(end, startIndex, StringComparison.Ordinal);
        if (endIndex == -1)
            return string.Empty;

        return text.Substring(startIndex, endIndex - startIndex);
    }

    /// <summary>
    /// 查找所有匹配位置（通用版本，支持英文和中文）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchText">要查找的文本</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>所有匹配位置的索引数组</returns>
    public static int[] YFindAllPositions(this string? text, string? searchText, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchText))
            return Array.Empty<int>();

        var positions = new List<int>();
        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        int startIndex = 0;

        while (startIndex < text.Length)
        {
            int index = text.IndexOf(searchText, startIndex, comparison);
            if (index == -1)
                break;

            positions.Add(index);
            startIndex = index + 1; // 允许重叠匹配
        }

        return positions.ToArray();
    }

    /// <summary>
    /// 根据位置提取左侧内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="position">位置索引</param>
    /// <param name="length">提取长度</param>
    /// <returns>左侧内容，如果位置无效则返回空字符串</returns>
    public static string YGetLeftContent(this string? text, int position, int length)
    {
        if (string.IsNullOrEmpty(text) || position < 0 || position >= text.Length || length <= 0)
            return string.Empty;

        var startIndex = Math.Max(0, position - length);
        var actualLength = position - startIndex;

        return text.Substring(startIndex, actualLength);
    }

    /// <summary>
    /// 根据位置提取右侧内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="position">位置索引</param>
    /// <param name="length">提取长度</param>
    /// <returns>右侧内容，如果位置无效则返回空字符串</returns>
    public static string YGetRightContent(this string? text, int position, int length)
    {
        if (string.IsNullOrEmpty(text) || position < 0 || position >= text.Length || length <= 0)
            return string.Empty;

        var startIndex = position + 1;
        if (startIndex >= text.Length)
            return string.Empty;

        var actualLength = Math.Min(length, text.Length - startIndex);

        return text.Substring(startIndex, actualLength);
    }

    /// <summary>
    /// 根据位置提取周围内容（左右各指定长度）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="position">位置索引</param>
    /// <param name="leftLength">左侧长度</param>
    /// <param name="rightLength">右侧长度</param>
    /// <returns>周围内容，如果位置无效则返回空字符串</returns>
    public static string YGetSurroundingContent(this string? text, int position, int leftLength, int rightLength)
    {
        if (string.IsNullOrEmpty(text) || position < 0 || position >= text.Length)
            return string.Empty;

        var leftContent = text.YGetLeftContent(position, leftLength);
        var rightContent = text.YGetRightContent(position, rightLength);
        var currentChar = text[position];

        return leftContent + currentChar + rightContent;
    }



    /// <summary>
    /// 智能查找多个文本在文本中的位置信息（支持中文、英文、符号等所有字符）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchTexts">要查找的文本数组</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>查找结果，包含每个搜索词的位置信息</returns>
    public static YSearchResult[] YFindMultiplePositions(this string? text, string[]? searchTexts, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(text) || searchTexts == null || searchTexts.Length == 0)
            return Array.Empty<YSearchResult>();

        var results = new List<YSearchResult>();

        foreach (var searchText in searchTexts.Where(s => !string.IsNullOrEmpty(s)))
        {
            var positions = text.YFindAllPositions(searchText, ignoreCase);
            results.Add(new YSearchResult
            {
                SearchText = searchText,
                Positions = positions,
                Count = positions.Length,
                Found = positions.Length > 0
            });
        }

        return results.ToArray();
    }

    /// <summary>
    /// 智能查找文本的第一个位置
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchText">要查找的文本</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>第一个匹配位置的索引，未找到返回 -1</returns>
    public static int YFindFirstPosition(this string? text, string? searchText, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchText))
            return -1;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        return text.IndexOf(searchText, comparison);
    }

    /// <summary>
    /// 智能查找文本的最后一个位置
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchText">要查找的文本</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>最后一个匹配位置的索引，未找到返回 -1</returns>
    public static int YFindLastPosition(this string? text, string? searchText, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchText))
            return -1;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        return text.LastIndexOf(searchText, comparison);
    }



    /// <summary>
    /// 根据搜索结果提取每个匹配位置的左右内容
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchText">搜索文本</param>
    /// <param name="leftLength">左侧提取长度</param>
    /// <param name="rightLength">右侧提取长度</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>包含位置和左右内容的结果数组</returns>
    public static YPositionContent[] YFindWithContext(this string? text, string? searchText, int leftLength = 10, int rightLength = 10, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchText))
            return Array.Empty<YPositionContent>();

        var positions = text.YFindAllPositions(searchText, ignoreCase);
        var results = new List<YPositionContent>();

        foreach (var position in positions)
        {
            var leftContent = text.YGetLeftContent(position, leftLength);
            var rightContent = text.YGetRightContent(position + searchText.Length - 1, rightLength);
            var matchedText = text.Substring(position, searchText.Length);

            results.Add(new YPositionContent
            {
                Position = position,
                MatchedText = matchedText,
                LeftContent = leftContent,
                RightContent = rightContent,
                FullContext = leftContent + matchedText + rightContent
            });
        }

        return results.ToArray();
    }

    /// <summary>
    /// 智能搜索文本并返回详细的搜索结果
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="searchText">要查找的文本</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>详细的搜索结果</returns>
    public static YSearchResult YSearch(this string? text, string? searchText, bool ignoreCase = false)
    {
        var result = new YSearchResult
        {
            SearchText = searchText ?? string.Empty
        };

        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(searchText))
            return result;

        var positions = text.YFindAllPositions(searchText, ignoreCase);
        result.Positions = positions;
        result.Count = positions.Length;
        result.Found = positions.Length > 0;

        return result;
    }

    /// <summary>
    /// 查找文本中所有的中文字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>中文字符位置数组</returns>
    public static int[] YFindAllChineseCharPositions(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<int>();

        var positions = new List<int>();
        for (int i = 0; i < text.Length; i++)
        {
            if (text[i] >= 0x4e00 && text[i] <= 0x9fff)
            {
                positions.Add(i);
            }
        }

        return positions.ToArray();
    }

    /// <summary>
    /// 提取文本中的所有中文字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>中文字符数组</returns>
    public static char[] YExtractChineseChars(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<char>();

        return text.Where(c => c >= 0x4e00 && c <= 0x9fff).ToArray();
    }

    /// <summary>
    /// 提取文本中的所有中文词汇（简单分词）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>中文词汇数组</returns>
    public static string[] YExtractChineseWords(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<string>();

        var words = new List<string>();
        var currentWord = new StringBuilder();

        foreach (char c in text)
        {
            if (c >= 0x4e00 && c <= 0x9fff) // 中文字符
            {
                currentWord.Append(c);
            }
            else
            {
                if (currentWord.Length > 0)
                {
                    words.Add(currentWord.ToString());
                    currentWord.Clear();
                }
            }
        }

        // 添加最后一个词
        if (currentWord.Length > 0)
        {
            words.Add(currentWord.ToString());
        }

        return words.ToArray();
    }

    /// <summary>
    /// 统计文本中中文字符的数量
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>中文字符数量</returns>
    public static int YCountChineseChars(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return 0;

        return text.Count(c => c >= 0x4e00 && c <= 0x9fff);
    }

    /// <summary>
    /// 提取文本中的所有数字
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>数字字符串数组，如果输入为 null 则返回空数组</returns>
    public static string[] YExtractNumbers(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<string>();

        var matches = Regex.Matches(text, @"\d+");
        return matches.Cast<Match>().Select(m => m.Value).ToArray();
    }

    #endregion

    #region 4. 文本替换和转换操作

    /// <summary>
    /// 使用正则表达式替换文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="replacement">替换内容</param>
    /// <returns>替换后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YReplaceRegex(this string? text, string pattern, string replacement)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
            return text ?? string.Empty;

        try
        {
            return Regex.Replace(text, pattern, replacement ?? string.Empty);
        }
        catch (ArgumentException)
        {
            // 正则表达式无效时返回原文本
            return text;
        }
    }

    /// <summary>
    /// 仅替换第一个匹配的子字符串
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="oldValue">要替换的子字符串</param>
    /// <param name="newValue">替换内容</param>
    /// <returns>替换后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YReplaceFirst(this string? text, string oldValue, string newValue)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(oldValue))
            return text ?? string.Empty;

        int pos = text.IndexOf(oldValue, StringComparison.Ordinal);
        if (pos < 0)
            return text;

        return text.SubstringCompat(0, pos) + (newValue ?? string.Empty) + text.Substring(pos + oldValue.Length);
    }

    /// <summary>
    /// 安全替换所有匹配的子字符串（避免null异常）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="oldValue">要替换的子字符串</param>
    /// <param name="newValue">替换内容</param>
    /// <returns>替换后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YReplaceAll(this string? text, string oldValue, string newValue)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(oldValue))
            return text ?? string.Empty;

        return text.Replace(oldValue, newValue ?? string.Empty);
    }

    /// <summary>
    /// 使用正则表达式匹配并提取文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>匹配结果集合，如果输入为 null 或无匹配则返回空集合</returns>
    public static IEnumerable<string> YMatchRegex(this string? text, string pattern)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(pattern))
            return Array.Empty<string>();

        try
        {
            var matches = Regex.Matches(text, pattern);
            return matches.Cast<Match>().Where(m => m.Success).Select(m => m.Value).ToArray();
        }
        catch (ArgumentException)
        {
            // 正则表达式无效时返回空集合
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 使用正则表达式拆分文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>拆分后的文本数组，如果输入为 null 则返回空数组</returns>
    public static string[] YSplitByRegex(this string? text, string pattern)
    {
        if (string.IsNullOrEmpty(text))
            return Array.Empty<string>();

        if (string.IsNullOrEmpty(pattern))
            return new[] { text };

        try
        {
            return Regex.Split(text, pattern);
        }
        catch (ArgumentException)
        {
            // 正则表达式无效时返回原文本数组
            return new[] { text };
        }
    }

    #endregion

    #region 5. 文本格式化操作

    /// <summary>
    /// 转换为标题格式 (Title Case)
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>标题格式文本，如果输入为 null 则返回空字符串</returns>
    public static string YToTitleCase(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        var textInfo = CultureInfo.CurrentCulture.TextInfo;
        return textInfo.ToTitleCase(text.ToLower());
    }

    /// <summary>
    /// 转换为驼峰格式 (camelCase)
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>驼峰格式文本，如果输入为 null 则返回空字符串</returns>
    public static string YToCamelCase(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        // 移除特殊字符，保留字母数字和空格
        var cleaned = Regex.Replace(text, @"[^\w\s]", " ");

        // 分割单词
        var words = cleaned.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

        if (words.Length == 0)
            return string.Empty;

        var result = new StringBuilder();

        // 第一个单词小写
        result.Append(words[0].ToLower());

        // 后续单词首字母大写
        for (int i = 1; i < words.Length; i++)
        {
            if (words[i].Length > 0)
            {
                result.Append(char.ToUpper(words[i][0]));
                if (words[i].Length > 1)
                    result.Append(words[i].Substring(1).ToLower());
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// 转换为帕斯卡格式 (PascalCase)
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>帕斯卡格式文本，如果输入为 null 则返回空字符串</returns>
    public static string YToPascalCase(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        // 移除特殊字符，保留字母数字和空格
        var cleaned = Regex.Replace(text, @"[^\w\s]", " ");

        // 分割单词
        var words = cleaned.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

        if (words.Length == 0)
            return string.Empty;

        var result = new StringBuilder();

        // 所有单词首字母大写
        foreach (var word in words)
        {
            if (word.Length > 0)
            {
                result.Append(char.ToUpper(word[0]));
                if (word.Length > 1)
                    result.Append(word.Substring(1).ToLower());
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// 转换为短横线格式 (kebab-case)
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>短横线格式文本，如果输入为 null 则返回空字符串</returns>
    public static string YToKebabCase(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        // 在大写字母前添加空格（处理 PascalCase 和 camelCase）
        var spaced = Regex.Replace(text, @"(?<!^)(?=[A-Z])", " ");

        // 移除特殊字符，保留字母数字和空格
        var cleaned = Regex.Replace(spaced, @"[^\w\s]", " ");

        // 分割单词并转换为小写，用短横线连接
        var words = cleaned.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);

        return string.Join("-", words.Select(w => w.ToLower()));
    }

    /// <summary>
    /// 模板格式化，支持 {0}, {1} 等占位符
    /// </summary>
    /// <param name="template">模板字符串</param>
    /// <param name="args">参数数组</param>
    /// <returns>格式化后的文本，如果模板为 null 则返回空字符串</returns>
    public static string YFormatTemplate(this string? template, params object[] args)
    {
        if (string.IsNullOrEmpty(template))
            return string.Empty;

        try
        {
            return string.Format(template, args);
        }
        catch
        {
            return template;
        }
    }

    /// <summary>
    /// 居中填充文本
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="totalWidth">总宽度</param>
    /// <param name="padChar">填充字符</param>
    /// <returns>居中填充后的文本，如果输入为 null 则返回填充字符组成的字符串</returns>
    public static string YPadCenter(this string? text, int totalWidth, char padChar = ' ')
    {
        if (string.IsNullOrEmpty(text))
            return new string(padChar, Math.Max(0, totalWidth));

        if (text.Length >= totalWidth)
            return text;

        var padding = totalWidth - text.Length;
        var leftPadding = padding / 2;
        var rightPadding = padding - leftPadding;

        return new string(padChar, leftPadding) + text + new string(padChar, rightPadding);
    }

    /// <summary>
    /// 重复字符串指定次数
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="count">重复次数</param>
    /// <returns>重复后的文本，如果输入为 null 或次数小于等于0则返回空字符串</returns>
    public static string YRepeat(this string? text, int count)
    {
        if (string.IsNullOrEmpty(text) || count <= 0)
            return string.Empty;

        var result = new StringBuilder(text.Length * count);
        for (int i = 0; i < count; i++)
        {
            result.Append(text);
        }

        return result.ToString();
    }

    /// <summary>
    /// Base64 编码
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>Base64 编码后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YToBase64(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        try
        {
            var bytes = Encoding.UTF8.GetBytes(text);
            return Convert.ToBase64String(bytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// Base64 解码
    /// </summary>
    /// <param name="base64">Base64 编码的文本</param>
    /// <returns>解码后的文本，如果输入为 null 或解码失败则返回空字符串</returns>
    public static string YFromBase64(this string? base64)
    {
        if (string.IsNullOrEmpty(base64))
            return string.Empty;

        try
        {
            var bytes = Convert.FromBase64String(base64);
            return Encoding.UTF8.GetString(bytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// URL 编码
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>URL 编码后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YUrlEncode(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return HttpUtility.UrlEncode(text);
    }

    /// <summary>
    /// URL 解码
    /// </summary>
    /// <param name="encodedText">URL 编码的文本</param>
    /// <returns>解码后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YUrlDecode(this string? encodedText)
    {
        if (string.IsNullOrEmpty(encodedText))
            return string.Empty;

        try
        {
            return HttpUtility.UrlDecode(encodedText);
        }
        catch
        {
            return encodedText;
        }
    }

    #endregion

    #region 6. 文本验证和检查操作

    /// <summary>
    /// 检查字符串是否为 null、空字符串或仅包含空白字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为 null、空或仅空白则返回 true</returns>
    public static bool YIsNullOrWhiteSpace(this string? text)
    {
        return string.IsNullOrWhiteSpace(text);
    }

    /// <summary>
    /// 检查字符串是否为纯数字
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为纯数字则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsNumeric(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        return text.All(char.IsDigit);
    }

    /// <summary>
    /// 检查字符串是否为纯字母
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为纯字母则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsAlpha(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        return text.All(char.IsLetter);
    }

    /// <summary>
    /// 检查字符串是否为字母和数字的组合
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为字母数字组合则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsAlphaNumeric(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        return text.All(char.IsLetterOrDigit);
    }

    /// <summary>
    /// 验证是否为有效的邮箱地址
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效邮箱则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidEmail(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        try
        {
            // 使用正则表达式验证邮箱格式
            var emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            return Regex.IsMatch(text, emailPattern, RegexOptions.IgnoreCase);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证是否为有效的手机号码（支持多种格式）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效手机号则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidPhone(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        // 移除常见的分隔符
        var cleaned = Regex.Replace(text, @"[\s\-\(\)\+]", "");

        // 检查是否为纯数字且长度合理（7-15位）
        if (!cleaned.All(char.IsDigit) || cleaned.Length < 7 || cleaned.Length > 15)
            return false;

        // 中国手机号格式检查
        if (cleaned.Length == 11 && cleaned.StartsWith("1"))
        {
            return Regex.IsMatch(cleaned, @"^1[3-9]\d{9}$");
        }

        // 其他国际格式的基本检查
        return true;
    }

    /// <summary>
    /// 验证是否为有效的 URL
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效 URL 则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidUrl(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        try
        {
            var uri = new Uri(text);
            return uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证是否为有效的 IP 地址
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效 IP 地址则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidIPAddress(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        return System.Net.IPAddress.TryParse(text, out _);
    }

    /// <summary>
    /// 检查字符是否为中文字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为中文字符则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsChineseChar(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        return text.All(c => c >= 0x4e00 && c <= 0x9fff);
    }

    /// <summary>
    /// 检查单个字符是否为中文字符
    /// </summary>
    /// <param name="c">字符</param>
    /// <returns>如果为中文字符则返回 true</returns>
    public static bool YIsChineseChar(this char c)
    {
        return c >= 0x4e00 && c <= 0x9fff;
    }

    /// <summary>
    /// 检查文本是否包含中文字符
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果包含中文字符则返回 true，null 或空字符串返回 false</returns>
    public static bool YContainsChinese(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        return text.Any(c => c >= 0x4e00 && c <= 0x9fff);
    }

    /// <summary>
    /// 检查文本是否为纯中文（只包含中文字符和常见标点）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="allowPunctuation">是否允许标点符号</param>
    /// <param name="allowSpaces">是否允许空格</param>
    /// <returns>如果为纯中文则返回 true</returns>
    public static bool YIsPureChinese(this string? text, bool allowPunctuation = true, bool allowSpaces = true)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        foreach (char c in text)
        {
            // 中文字符
            if (c >= 0x4e00 && c <= 0x9fff)
                continue;

            // 空格
            if (allowSpaces && char.IsWhiteSpace(c))
                continue;

            // 中文标点符号
            if (allowPunctuation && IsChinesePunctuation(c))
                continue;

            // 其他字符不允许
            return false;
        }

        return true;
    }

    /// <summary>
    /// 获取文本中中文字符的比例
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>中文字符比例（0-1），如果输入为 null 或空则返回 0</returns>
    public static double YGetChineseRatio(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return 0.0;

        var chineseCount = text.Count(c => c >= 0x4e00 && c <= 0x9fff);
        return (double)chineseCount / text.Length;
    }

    /// <summary>
    /// 验证是否为有效的中国手机号
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效的中国手机号则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidChinesePhone(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        // 移除常见的分隔符
        var cleaned = Regex.Replace(text, @"[\s\-\(\)]", "");

        // 中国手机号格式：1开头，第二位为3-9，总共11位
        return Regex.IsMatch(cleaned, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 验证是否为有效的中国身份证号
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>如果为有效的身份证号则返回 true，null 或空字符串返回 false</returns>
    public static bool YIsValidChineseIdCard(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;

        // 18位身份证号码格式检查
        if (text.Length != 18)
            return false;

        // 前17位必须是数字，最后一位可以是数字或X
        if (!Regex.IsMatch(text, @"^\d{17}[\dXx]$"))
            return false;

        // 简单的校验码验证
        try
        {
            var weights = new[] { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };
            var checkCodes = new[] { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };

            var sum = 0;
            for (int i = 0; i < 17; i++)
            {
                sum += (text[i] - '0') * weights[i];
            }

            var checkCode = checkCodes[sum % 11];
            return char.ToUpper(text[17]) == checkCode;
        }
        catch
        {
            return false;
        }
    }



    #endregion

    #region 7. 文本分析和统计操作

    /// <summary>
    /// 获取文本的单词数量
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>单词数量，如果输入为 null 或空则返回 0</returns>
    public static int YGetWordCount(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return 0;

        // 分割单词，移除空白项
        var words = text.Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?' },
                               StringSplitOptions.RemoveEmptyEntries);

        return words.Length;
    }

    /// <summary>
    /// 获取文本的字符数量
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="includeSpaces">是否包含空格</param>
    /// <returns>字符数量，如果输入为 null 则返回 0</returns>
    public static int YGetCharacterCount(this string? text, bool includeSpaces = true)
    {
        if (string.IsNullOrEmpty(text))
            return 0;

        return includeSpaces ? text.Length : text.Count(c => !char.IsWhiteSpace(c));
    }

    /// <summary>
    /// 获取文本的行数
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>行数，如果输入为 null 或空则返回 0</returns>
    public static int YGetLineCount(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return 0;

        return text.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }

    /// <summary>
    /// 估算文本的阅读时间
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="wordsPerMinute">每分钟阅读单词数，默认200</param>
    /// <returns>预计阅读时间，如果输入为 null 或空则返回 TimeSpan.Zero</returns>
    public static TimeSpan YGetReadingTime(this string? text, int wordsPerMinute = 200)
    {
        if (string.IsNullOrWhiteSpace(text))
            return TimeSpan.Zero;

        var wordCount = text.YGetWordCount();
        var minutes = (double)wordCount / wordsPerMinute;

        return TimeSpan.FromMinutes(minutes);
    }

    /// <summary>
    /// 综合文本分析
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>文本分析结果，如果输入为 null 则返回空的分析结果</returns>
    public static YTextAnalysis YAnalyzeText(this string? text)
    {
        var analysis = new YTextAnalysis();

        if (string.IsNullOrEmpty(text))
            return analysis;

        // 基础统计
        analysis.CharacterCount = text.Length;
        analysis.CharacterCountNoSpaces = text.YGetCharacterCount(false);
        analysis.WordCount = text.YGetWordCount();
        analysis.LineCount = text.YGetLineCount();
        analysis.EstimatedReadingTime = text.YGetReadingTime();

        // 段落统计
        analysis.ParagraphCount = text.Split(new[] { "\n\n", "\r\n\r\n" },
                                           StringSplitOptions.RemoveEmptyEntries).Length;

        // 句子统计
        analysis.SentenceCount = text.Split(new[] { '.', '!', '?', '。', '！', '？' },
                                          StringSplitOptions.RemoveEmptyEntries).Length;

        // 语言检测
        analysis.ContainsChinese = text.YContainsChinese();
        analysis.DetectedLanguage = analysis.ContainsChinese ? "zh" : "en";

        // 计算平均值
        if (analysis.WordCount > 0)
        {
            var words = text.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            analysis.AverageWordLength = words.Average(w => w.Length);
        }

        if (analysis.SentenceCount > 0)
        {
            analysis.AverageSentenceLength = (double)analysis.WordCount / analysis.SentenceCount;
        }

        // 其他统计
        analysis.NumberCount = text.YExtractNumbers().Length;
        analysis.UppercaseRatio = text.Count(char.IsUpper) / (double)text.Length;
        analysis.PunctuationCount = text.Count(char.IsPunctuation);
        analysis.ContainsSpecialCharacters = text.Any(c => !char.IsLetterOrDigit(c) && !char.IsWhiteSpace(c));

        // 可读性评分（简化版）
        analysis.ReadabilityScore = CalculateReadabilityScore(analysis);

        // 复杂度等级
        analysis.ComplexityLevel = CalculateComplexityLevel(analysis);

        // 情感分析（简化版）
        analysis.SentimentScore = CalculateBasicSentiment(text);

        // 高频词汇分析
        analysis.TopWords = GetTopWords(text);

        // 关键词提取（简化版）
        analysis.Keywords = ExtractBasicKeywords(text);

        return analysis;
    }

    /// <summary>
    /// 检测文本的主要语言
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>语言代码，如果输入为 null 或空则返回 "unknown"</returns>
    public static string YGetLanguage(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return "unknown";

        // 简单的语言检测
        if (text.YContainsChinese())
            return "zh";

        // 检测其他语言的基本特征
        var englishWords = new[] { "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by" };
        var lowerText = text.ToLower();
        var englishMatches = englishWords.Count(word => lowerText.Contains(word));

        return englishMatches > 2 ? "en" : "unknown";
    }

    /// <summary>
    /// 基础情感分析
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>情感评分（-1到1），如果输入为 null 或空则返回 0</returns>
    public static double YGetSentiment(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return 0.0;

        return CalculateBasicSentiment(text);
    }

    /// <summary>
    /// 提取关键词
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="maxCount">最大关键词数量</param>
    /// <returns>关键词数组，如果输入为 null 或空则返回空数组</returns>
    public static string[] YExtractKeywords(this string? text, int maxCount = 10)
    {
        if (string.IsNullOrWhiteSpace(text))
            return Array.Empty<string>();

        return ExtractBasicKeywords(text).Take(maxCount).ToArray();
    }

    #endregion

    #region 10. 私有辅助方法

    /// <summary>
    /// 计算可读性评分
    /// </summary>
    private static double CalculateReadabilityScore(YTextAnalysis analysis)
    {
        // 简化的可读性评分算法
        var score = 100.0;

        // 平均句子长度影响
        if (analysis.AverageSentenceLength > 20)
            score -= (analysis.AverageSentenceLength - 20) * 2;

        // 平均单词长度影响
        if (analysis.AverageWordLength > 6)
            score -= (analysis.AverageWordLength - 6) * 5;

        // 复杂字符影响
        if (analysis.ContainsSpecialCharacters)
            score -= 10;

        return Math.Max(0, Math.Min(100, score));
    }

    /// <summary>
    /// 计算复杂度等级
    /// </summary>
    private static int CalculateComplexityLevel(YTextAnalysis analysis)
    {
        var complexity = 1;

        if (analysis.AverageSentenceLength > 15) complexity++;
        if (analysis.AverageWordLength > 5) complexity++;
        if (analysis.ContainsSpecialCharacters) complexity++;
        if (analysis.PunctuationCount > analysis.WordCount * 0.2) complexity++;

        return Math.Min(5, complexity);
    }

    /// <summary>
    /// 基础情感分析
    /// </summary>
    private static double CalculateBasicSentiment(string text)
    {
        var positiveWords = new[] { "good", "great", "excellent", "amazing", "wonderful", "fantastic", "好", "棒", "优秀", "很好", "不错" };
        var negativeWords = new[] { "bad", "terrible", "awful", "horrible", "worst", "hate", "坏", "糟糕", "讨厌", "不好", "差" };

        var lowerText = text.ToLower();
        var positiveCount = positiveWords.Count(word => lowerText.Contains(word));
        var negativeCount = negativeWords.Count(word => lowerText.Contains(word));

        var totalWords = text.YGetWordCount();
        if (totalWords == 0) return 0.0;

        var sentiment = (positiveCount - negativeCount) / (double)totalWords * 10;
        return Math.Max(-1.0, Math.Min(1.0, sentiment));
    }

    /// <summary>
    /// 获取高频词汇
    /// </summary>
    private static Dictionary<string, int> GetTopWords(string text)
    {
        var words = text.Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?', '"', '\'', '(', ')', '[', ']' },
                              StringSplitOptions.RemoveEmptyEntries)
                       .Where(w => w.Length > 2)
                       .Select(w => w.ToLower())
                       .GroupBy(w => w)
                       .ToDictionary(g => g.Key, g => g.Count())
                       .OrderByDescending(kvp => kvp.Value)
                       .Take(10)
                       .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        return words;
    }

    /// <summary>
    /// 提取基础关键词
    /// </summary>
    private static string[] ExtractBasicKeywords(string text)
    {
        // 停用词列表
        var stopWords = new HashSet<string> { "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "a", "an", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "must", "can", "这", "那", "的", "了", "在", "是", "有", "和", "与", "或", "但", "如果", "因为", "所以", "然后", "现在", "已经", "还是", "就是", "可以", "应该", "能够", "需要" };

        var words = text.Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?', '"', '\'', '(', ')', '[', ']' },
                              StringSplitOptions.RemoveEmptyEntries)
                       .Where(w => w.Length > 2 && !stopWords.Contains(w.ToLower()))
                       .Select(w => w.ToLower())
                       .GroupBy(w => w)
                       .OrderByDescending(g => g.Count())
                       .Take(20)
                       .Select(g => g.Key)
                       .ToArray();

        return words;
    }

    #endregion

    #region 8. 高级文本处理操作

    /// <summary>
    /// 转换为 URL 友好的 slug 格式
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>URL 友好的字符串，如果输入为 null 则返回空字符串</returns>
    public static string YToSlug(this string? text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return string.Empty;

        // 转换为小写
        var slug = text.ToLower();

        // 移除重音符号
        slug = RemoveAccents(slug);

        // 替换空格和特殊字符为短横线
        slug = Regex.Replace(slug, @"[^a-z0-9\u4e00-\u9fff]+", "-");

        // 移除开头和结尾的短横线
        slug = slug.Trim('-');

        // 移除连续的短横线
        slug = Regex.Replace(slug, @"-+", "-");

        return slug;
    }

    /// <summary>
    /// 转换为 ASCII 字符，移除重音符号
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>ASCII 字符串，如果输入为 null 则返回空字符串</returns>
    public static string YToAscii(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        return RemoveAccents(text);
    }

    /// <summary>
    /// 简化文本，移除复杂字符和格式
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>简化后的文本，如果输入为 null 则返回空字符串</returns>
    public static string YSimplifyText(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        // 移除控制字符
        var simplified = Regex.Replace(text, @"[\x00-\x1F\x7F-\x9F]", "");

        // 标准化空白字符
        simplified = simplified.YNormalizeWhitespace();

        // 移除重复的标点符号
        simplified = Regex.Replace(simplified, @"([.!?])\1+", "$1");

        // 移除多余的引号
        simplified = Regex.Replace(simplified, @"[""''`´]", "\"");

        return simplified.Trim();
    }

    /// <summary>
    /// 计算两个文本的相似度（使用 Jaccard 相似度）
    /// </summary>
    /// <param name="text">第一个文本</param>
    /// <param name="other">第二个文本</param>
    /// <returns>相似度（0-1），如果任一参数为 null 则返回 0</returns>
    public static double YSimilarity(this string? text, string? other)
    {
        if (string.IsNullOrEmpty(text) || string.IsNullOrEmpty(other))
            return 0.0;

        // 转换为单词集合
        var words1 = GetWordSet(text);
        var words2 = GetWordSet(other);

        if (words1.Count == 0 && words2.Count == 0)
            return 1.0;

        if (words1.Count == 0 || words2.Count == 0)
            return 0.0;

        // 计算 Jaccard 相似度
        var intersection = words1.Intersect(words2).Count();
        var union = words1.Union(words2).Count();

        return (double)intersection / union;
    }

    /// <summary>
    /// 计算两个文本的编辑距离（Levenshtein 距离）
    /// </summary>
    /// <param name="text">第一个文本</param>
    /// <param name="other">第二个文本</param>
    /// <returns>编辑距离，如果任一参数为 null 则返回较长文本的长度</returns>
    public static int YLevenshteinDistance(this string? text, string? other)
    {
        if (string.IsNullOrEmpty(text))
            return other?.Length ?? 0;

        if (string.IsNullOrEmpty(other))
            return text.Length;

        var matrix = new int[text.Length + 1, other.Length + 1];

        // 初始化第一行和第一列
        for (int i = 0; i <= text.Length; i++)
            matrix[i, 0] = i;

        for (int j = 0; j <= other.Length; j++)
            matrix[0, j] = j;

        // 填充矩阵
        for (int i = 1; i <= text.Length; i++)
        {
            for (int j = 1; j <= other.Length; j++)
            {
                var cost = text[i - 1] == other[j - 1] ? 0 : 1;

                matrix[i, j] = Math.Min(
                    Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                    matrix[i - 1, j - 1] + cost);
            }
        }

        return matrix[text.Length, other.Length];
    }

    #endregion

    #region 9. 文本安全和编码操作

    /// <summary>
    /// 计算文本的MD5哈希值
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>MD5哈希值的十六进制字符串，如果输入为null则返回空字符串</returns>
    public static string YToMd5(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        using var md5 = System.Security.Cryptography.MD5.Create();
        var inputBytes = Encoding.UTF8.GetBytes(text);
        var hashBytes = md5.ComputeHash(inputBytes);

        // 将字节数组转换为十六进制字符串
        var sb = new StringBuilder();
        foreach (var b in hashBytes)
        {
            sb.Append(b.ToString("x2"));
        }
        
        return sb.ToString();
    }

    /// <summary>
    /// 计算文本的SHA256哈希值
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>SHA256哈希值的十六进制字符串，如果输入为null则返回空字符串</returns>
    public static string YToSha256(this string? text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var inputBytes = Encoding.UTF8.GetBytes(text);
        var hashBytes = sha256.ComputeHash(inputBytes);

        // 将字节数组转换为十六进制字符串
        var sb = new StringBuilder();
        foreach (var b in hashBytes)
        {
            sb.Append(b.ToString("x2"));
        }
        
        return sb.ToString();
    }

    /// <summary>
    /// 对敏感信息进行掩码处理，如邮箱地址、手机号码等
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <param name="visibleStartChars">开始可见字符数</param>
    /// <param name="visibleEndChars">结尾可见字符数</param>
    /// <param name="maskChar">掩码字符</param>
    /// <returns>掩码处理后的文本，如果输入为null则返回空字符串</returns>
    public static string YMask(this string? text, int visibleStartChars = 3, int visibleEndChars = 4, char maskChar = '*')
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;

        if (visibleStartChars < 0)
            visibleStartChars = 0;

        if (visibleEndChars < 0)
            visibleEndChars = 0;

        // 如果文本长度小于等于可见字符总数，全部掩码
        if (text.Length <= visibleStartChars + visibleEndChars)
        {
            return new string(maskChar, text.Length);
        }

        // 计算掩码部分的长度
        int maskLength = text.Length - visibleStartChars - visibleEndChars;
        
        // 构建掩码文本
        return text.Substring(0, visibleStartChars) +
               new string(maskChar, maskLength) +
               text.Substring(text.Length - visibleEndChars);
    }

    /// <summary>
    /// 对邮箱地址进行掩码处理
    /// </summary>
    /// <param name="email">邮箱地址</param>
    /// <returns>掩码处理后的邮箱地址，如：a***@example.com</returns>
    public static string YMaskEmail(this string? email)
    {
        if (string.IsNullOrEmpty(email))
            return string.Empty;

        // 检查是否为有效邮箱
        if (!YIsValidEmail(email))
            return email;

        // 分离用户名和域名部分
        var parts = email.Split('@');
        string username = parts[0];
        string domain = parts[1];

        // 对用户名进行掩码处理
        int usernameLength = username.Length;
        int visibleChars = Math.Min(2, usernameLength);
        
        string maskedUsername = username.Substring(0, visibleChars) + 
                               new string('*', usernameLength - visibleChars);
        
        return maskedUsername + "@" + domain;
    }

    /// <summary>
    /// 对手机号码进行掩码处理
    /// </summary>
    /// <param name="phoneNumber">手机号码</param>
    /// <returns>掩码处理后的手机号码，如：138****1234</returns>
    public static string YMaskPhone(this string? phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return string.Empty;

        // 通用处理
        return YMask(phoneNumber, 3, 4);
    }

    /// <summary>
    /// 移除重音符号
    /// </summary>
    private static string RemoveAccents(string text)
    {
        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark)
            {
                stringBuilder.Append(c);
            }
        }

        return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
    }

    /// <summary>
    /// 获取文本的单词集合
    /// </summary>
    private static HashSet<string> GetWordSet(string text)
    {
        var words = text.ToLower()
                       .Split(new[] { ' ', '\t', '\n', '\r', '.', ',', ';', ':', '!', '?', '"', '\'', '(', ')', '[', ']' },
                              StringSplitOptions.RemoveEmptyEntries)
                       .Where(w => w.Length > 1);

        return new HashSet<string>(words);
    }

    /// <summary>
    /// 检查字符是否为中文标点符号
    /// </summary>
    /// <param name="c">字符</param>
    /// <returns>如果是中文标点符号则返回 true</returns>
    private static bool IsChinesePunctuation(char c)
    {
        // 常见中文标点符号的Unicode值
        return c switch
        {
            '，' or '。' or '！' or '？' or '；' or '：' or
            '（' or '）' or '【' or '】' or '《' or '》' or
            '、' or '…' or '—' or '·' => true,
            _ when (c >= '\u201c' && c <= '\u201d') => true, // 中文双引号
            _ when (c >= '\u2018' && c <= '\u2019') => true, // 中文单引号
            _ => false
        };
    }

    #endregion
}

