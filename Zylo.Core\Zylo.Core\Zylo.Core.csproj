<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
    <PackageId>Zylo.Core</PackageId>
    <Product>Zylo Core Utilities</Product>
    <Description>🎯 Zylo.Core 现代化 C# 运行时工具库。提供安全的类型转换、丰富的集合操作、全面的文本处理、LINQ
      风格扩展方法。专注于核心工具功能，零依赖，轻量级设计。</Description>
    <PackageTags>
      utility;helper;tools;core;extensions;linq;collection;converter;text;safe;csharp;lightweight;zero-dependency</PackageTags>

    <!-- 项目特定的URL配置 -->
    <PackageProjectUrl>https://github.com/zylo/zylo-core</PackageProjectUrl>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

    <!-- 发布说明 -->
    <PackageReleaseNotes>
      🎉 v1.3.2 - 功能增强与稳定性提升：
      - 🎯 移除所有容器依赖，回归纯工具库本质
      - 🚀 零依赖设计，轻量级部署
      - ✨ 专注核心功能：类型转换、集合操作、文本处理
      - 🔧 保留所有核心工具方法
      - 📚 简化的 API 和文档
      - 🔄 完美支持 .NET 6.0 和 .NET 8.0 双框架
    </PackageReleaseNotes>

    <!-- 兼容性 -->
    <SupportedOSPlatform>windows</SupportedOSPlatform>
    <SupportedOSPlatform>linux</SupportedOSPlatform>
    <SupportedOSPlatform>macos</SupportedOSPlatform>
  </PropertyGroup>

  <!-- 运行时依赖 - .NET 6+ -->
  <ItemGroup>
    <!-- 移除所有容器依赖，保持 Zylo.Core 为纯工具库 -->
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Text\" />
  </ItemGroup>

</Project>