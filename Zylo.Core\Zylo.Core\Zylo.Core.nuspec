<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Zylo.Core</id>
    <version>2.2.0-dev</version>
    <authors>Zylo Team</authors>
    <owners>Zylo</owners>
    <description>Zylo.Core 现代化 C# 工具库，提供安全的类型转换、丰富的集合操作、全面的文本处理和 LINQ 风格扩展方法。支持 .NET 6.0 和 .NET 8.0，专注于简单易用。</description>
    <projectUrl>https://github.com/zylo/zylo-core</projectUrl>
    <repository type="git" url="https://github.com/zylo/zylo-core" />
    <license type="expression">MIT</license>
    <tags>utility;helper;tools;core;linq;collection;converter;text;safe;extensions;csharp</tags>
    <releaseNotes>
      v2.2.0 - 文本处理功能升级：
      - 新增 50+ 个文本处理方法，提供全面的文本解决方案
      - 支持文本清理、格式化、验证和分析
      - 增强中文支持，包括中文手机号和身份证验证
      - 提供文本相似度比较和编辑距离计算
      - 完整的测试覆盖：344 个测试用例，100% 通过率
      - 优化类型转换和集合操作性能
      - 完美支持 .NET 6.0 和 .NET 8.0 双框架
      
      v2.1.0 - 重大功能升级：
      - 新增 18 个 LINQ 风格扩展方法，支持安全的链式操作
      - 新增 10 个 List 增强方法，提供更丰富的 List 操作
      - 所有新方法都有完整的空值保护和边界检查
      - 支持自定义默认值，避免异常抛出
      - 完整的测试覆盖：228 个测试用例，100% 通过率
      - 详细的 XML 文档注释和使用示例
      - 简化文档结构，更易于使用
      - 完美支持 .NET 6.0 和 .NET 8.0 双框架
    </releaseNotes>
    <copyright>Copyright © 2025</copyright>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
  </metadata>
  <files>
    <!-- 主要程序集 -->
    <file src="bin\\Release\\**\\Zylo.Core.dll" target="lib" />
    <file src="bin\\Release\\**\\Zylo.Core.pdb" target="lib" />

    <!-- 文档文件 -->
    <file src="bin\\Release\\**\\Zylo.Core.xml" target="lib" />

    <!-- Build 文件 -->
    <file src="build\\Zylo.Core.props" target="build" />
    <file src="build\\Zylo.Core.targets" target="build" />

    <!-- 内容文件 -->
    <file src="README.md" target="" />
  </files>
</package>