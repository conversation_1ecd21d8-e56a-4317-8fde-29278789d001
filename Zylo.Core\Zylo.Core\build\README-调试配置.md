# Zylo.Core 调试配置文件

## 📅 生成时间
2025-06-18 10:16:43

## 📁 文件说明

### Zylo.Core.props
- **作用**: MSBuild 属性文件，定义项目属性和默认值
- **位置**: `build/Zylo.Core.props`
- **自动导入**: 当项目引用此 NuGet 包时自动导入

### Zylo.Core.targets
- **作用**: MSBuild 目标文件，定义自定义构建任务
- **位置**: `build/Zylo.Core.targets`
- **自动导入**: 当项目引用此 NuGet 包时自动导入

### Zylo.Core.nuspec
- **作用**: NuGet 包规范文件，定义包的元数据和文件包含规则
- **位置**: `Zylo.Core.nuspec`
- **用途**: 用于生成 NuGet 包

### 示例项目-Zylo.Core.csproj
- **作用**: 示例项目文件，展示如何配置项目以使用生成的 NuGet 包
- **位置**: `build/示例项目-Zylo.Core.csproj`
- **用途**: 参考配置，可复制到实际项目中使用

## ⚙️ 当前配置

### Build文件设置
- 自动引用 SourceGenerator: 是
- 包含构建任务: 否
- 显示构建信息: 否
- 验证项目配置: 否
- SourceGenerator版本: 

### 包文件设置
- 包图标文件: 未设置
- README文件: 未设置
- 最小客户端版本: 未设置

### 输出设置
- 构建配置: Release
- 输出类型: Default
- 包含符号包: 否
- 详细程度: Minimal

## 🚀 使用方法

1. **测试配置**: 将这些文件复制到实际的 NuGet 包项目中
2. **验证效果**: 构建项目查看 MSBuild 行为
3. **调试问题**: 根据构建输出调整配置

## 📖 参考文档

请参考项目根目录的 `Build文件说明文档.md` 获取详细的使用说明。
