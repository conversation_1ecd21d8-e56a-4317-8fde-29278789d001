<Project>
  <PropertyGroup>
    <!-- Zylo.Core 源代码生成器配置 -->
    
    <!-- 自定义模块名（覆盖从项目名推断的名称） -->
    <ZyloModuleName Condition="'$(ZyloModuleName)' == ''">$(MSBuildProjectName)</ZyloModuleName>
    
    <!-- 生成代码的文件夹 -->
    <ZyloGeneratedFolder Condition="'$(ZyloGeneratedFolder)' == ''">$(BaseIntermediateOutputPath)Generated</ZyloGeneratedFolder>
    
    <!-- 是否生成本地YAll（当没有引用Zylo.ALL时） -->
    <ZyloCreateLocalYAll Condition="'$(ZyloCreateLocalYAll)' == ''">true</ZyloCreateLocalYAll>
    
    <!-- 是否启用调试信息 -->
    <ZyloEnableDebugInfo Condition="'$(ZyloEnableDebugInfo)' == ''">false</ZyloEnableDebugInfo>
  </PropertyGroup>
</Project>
