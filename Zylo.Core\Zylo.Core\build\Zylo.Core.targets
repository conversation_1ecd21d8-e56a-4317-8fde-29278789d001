<Project>
  <!-- 🎯 Zylo.Core 集成 Zylo.AutoRegister 源代码生成器 -->

  <!-- 🔧 自动设置包引用属性 -->
  <ItemGroup>
    <Analyzer Include="$(MSBuildThisFileDirectory)..\analyzers\dotnet\cs\Zylo.AutoRegister.dll" />
  </ItemGroup>

  <!-- 🎯 确保源代码生成器正确工作 -->
  <PropertyGroup>
    <RestoreAdditionalProjectSources Condition="'$(RestoreAdditionalProjectSources)' == ''">$(RestoreAdditionalProjectSources);https://api.nuget.org/v3/index.json</RestoreAdditionalProjectSources>
  </PropertyGroup>

  <!-- 📝 显示自动配置信息（仅在详细模式下） -->
  <Target Name="ShowZyloCoreAutoRegisterInfo" BeforeTargets="Build" Condition="'$(DesignTimeBuild)' != 'true' and '$(BuildingProject)' == 'true'">
    <Message Text="🎯 Zylo.Core: 已自动配置 AutoRegister 源代码生成器" Importance="low" />
    <Message Text="   ✅ 将自动生成 AddAllSimpleAutoServices() 扩展方法" Importance="low" />
    <Message Text="   📁 生成的代码位于: obj/Generated/Zylo.AutoRegister/" Importance="low" />
  </Target>

  <!-- 🔧 开发时显示生成的代码文件（可选） -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
  </PropertyGroup>
</Project>
