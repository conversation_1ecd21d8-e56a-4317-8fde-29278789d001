using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using Zylo.Core.DI.Abstractions;
using Zylo.Core.DI.Extensions;
using Zylo.Core.DI.Factory;
using Zylo.Core.DI.Adapters;
using Zylo.Data.Interfaces;
using Zylo.Data.Services;

namespace Zylo.Data.DI;

/// <summary>
/// Zylo.Data 双容器支持扩展方法
/// 提供 DryIoc 和 Microsoft.Extensions.DI 的统一支持
/// </summary>
public static class ZyloDataContainerExtensions
{
    /// <summary>
    /// 注册所有 Zylo.Data 服务到 Zylo 容器
    /// </summary>
    /// <param name="container">Zylo 容器</param>
    /// <param name="configure">配置选项</param>
    /// <returns>容器实例</returns>
    public static IZyloContainer AddZyloData(this IZyloContainer container, Action<ZyloDataOptions>? configure = null)
    {
        // 确保 Zylo.Core 已注册
        container.AddZyloCore();

        // 注册配置选项
        var options = new ZyloDataOptions();
        configure?.Invoke(options);
        container.RegisterInstance(options);
        container.RegisterFactory<IOptions<ZyloDataOptions>>(_ => Options.Create(options));

        // 添加日志服务（如果尚未注册）
        if (!container.IsRegistered<ILoggerFactory>())
        {
            container.RegisterFactory<ILoggerFactory>(_ => new Microsoft.Extensions.Logging.LoggerFactory());
        }
        if (!container.IsRegistered<ILogger<ZyloDataService>>())
        {
            container.RegisterFactory<ILogger<ZyloDataService>>(c =>
                c.Resolve<ILoggerFactory>().CreateLogger<ZyloDataService>());
        }
        if (!container.IsRegistered<ILogger<YDatabaseService>>())
        {
            container.RegisterFactory<ILogger<YDatabaseService>>(c =>
                c.Resolve<ILoggerFactory>().CreateLogger<YDatabaseService>());
        }

        // 注册核心数据服务
        if (options.EnableDatabase && !container.IsRegistered<IYDatabase>())
        {
            // 注册一个简单的内存数据库 DbContext 用于测试
            if (!container.IsRegistered<Microsoft.EntityFrameworkCore.DbContext>())
            {
                container.RegisterFactory<Microsoft.EntityFrameworkCore.DbContext>(_ =>
                {
                    var optionsBuilder = new Microsoft.EntityFrameworkCore.DbContextOptionsBuilder();
                    optionsBuilder.UseInMemoryDatabase("TestDatabase");
                    return new TestDbContext(optionsBuilder.Options);
                });
            }

            // 注册内存缓存
            if (!container.IsRegistered<Microsoft.Extensions.Caching.Memory.IMemoryCache>())
            {
                container.RegisterFactory<Microsoft.Extensions.Caching.Memory.IMemoryCache>(_ =>
                    new Microsoft.Extensions.Caching.Memory.MemoryCache(new Microsoft.Extensions.Caching.Memory.MemoryCacheOptions()));
            }

            container.RegisterSingleton<IYDatabase, YDatabaseService>();
        }

        if (options.EnableCache && !container.IsRegistered<IYCache>())
        {
            container.RegisterSingleton<IYCache, YCacheService>();
        }

        if (options.EnableConfiguration && !container.IsRegistered<IYConfiguration>())
        {
            container.RegisterSingleton<IYConfiguration, YConfigurationService>();
        }

        if (options.EnableMapping && !container.IsRegistered<IYMapping>())
        {
            container.RegisterSingleton<IYMapping, YMappingService>();
        }

        // 注册数据管理服务
        container.RegisterSingleton<IZyloDataService, ZyloDataService>();

        return container;
    }

    /// <summary>
    /// 注册所有 Zylo.Data 服务到 Microsoft.Extensions.DI 容器
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configure">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddZyloData(this IServiceCollection services, Action<ZyloDataOptions>? configure = null)
    {
        // 添加日志服务（如果尚未注册）
        services.TryAddSingleton<ILoggerFactory, Microsoft.Extensions.Logging.LoggerFactory>();
        services.TryAddSingleton(typeof(ILogger<>), typeof(Microsoft.Extensions.Logging.Logger<>));
        
        // 创建 Zylo 容器适配器
        var container = new ZyloMicrosoftDIAdapter(services);
        container.AddZyloData(configure);
        return services;
    }

    /// <summary>
    /// 验证 Zylo.Data 容器配置
    /// </summary>
    /// <param name="container">容器实例</param>
    /// <returns>验证结果</returns>
    public static ZyloDataValidationResult ValidateZyloData(this IZyloContainer container)
    {
        var result = new ZyloDataValidationResult();

        try
        {
            // 检查核心依赖
            result.HasCore = container.IsRegistered<Zylo.Core.Interfaces.IYConverter>();

            // 检查配置选项
            result.HasOptions = container.IsRegistered<ZyloDataOptions>();

            // 检查数据管理服务
            result.HasDataManagementService = container.IsRegistered<IZyloDataService>();

            // 检查可选服务
            result.HasDatabase = container.IsRegistered<IYDatabase>();
            result.HasCache = container.IsRegistered<IYCache>();
            result.HasConfiguration = container.IsRegistered<IYConfiguration>();
            result.HasMapping = container.IsRegistered<IYMapping>();

            // 检查容器健康状态
            result.IsContainerHealthy = true; // 简化检查，实际可以添加更复杂的验证

            // 验证配置选项
            if (result.HasOptions)
            {
                var options = container.Resolve<ZyloDataOptions>();
                var (isValid, errors) = options.Validate();
                result.ConfigurationErrors.AddRange(errors);
            }

            // 计算总体验证结果
            result.IsValid = result.HasCore && 
                           result.HasOptions && 
                           result.HasDataManagementService && 
                           result.IsContainerHealthy &&
                           result.ConfigurationErrors.Count == 0;

            if (!result.IsValid)
            {
                var errors = new List<string>();
                if (!result.HasCore) errors.Add("缺少 Zylo.Core 依赖");
                if (!result.HasOptions) errors.Add("缺少配置选项");
                if (!result.HasDataManagementService) errors.Add("缺少数据管理服务");
                if (!result.IsContainerHealthy) errors.Add("容器状态不健康");
                
                result.Errors.AddRange(errors);
                result.Errors.AddRange(result.ConfigurationErrors);
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"验证过程中发生异常: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 检查容器是否包含 Zylo.Data 服务
    /// </summary>
    /// <param name="container">容器实例</param>
    /// <returns>是否包含服务</returns>
    public static bool HasZyloDataServices(this IZyloContainer container)
    {
        return container.IsRegistered<IZyloDataService>();
    }

    /// <summary>
    /// 创建预配置的 Zylo.Data 容器
    /// </summary>
    /// <param name="containerType">容器类型</param>
    /// <param name="configure">配置选项</param>
    /// <returns>配置好的容器</returns>
    public static IZyloContainer CreateZyloDataContainer(ZyloContainerType containerType = ZyloContainerType.Auto, Action<ZyloDataOptions>? configure = null)
    {
        var container = ZyloContainerFactory.Create(containerType);
        container.AddZyloData(configure);
        return container;
    }


}

/// <summary>
/// Zylo.Data 验证结果
/// </summary>
public class ZyloDataValidationResult
{
    /// <summary>
    /// 验证是否通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 是否包含 Zylo.Core 依赖
    /// </summary>
    public bool HasCore { get; set; }

    /// <summary>
    /// 是否包含配置选项
    /// </summary>
    public bool HasOptions { get; set; }

    /// <summary>
    /// 是否包含数据管理服务
    /// </summary>
    public bool HasDataManagementService { get; set; }

    /// <summary>
    /// 是否包含数据库服务
    /// </summary>
    public bool HasDatabase { get; set; }

    /// <summary>
    /// 是否包含缓存服务
    /// </summary>
    public bool HasCache { get; set; }

    /// <summary>
    /// 是否包含配置服务
    /// </summary>
    public bool HasConfiguration { get; set; }

    /// <summary>
    /// 是否包含映射服务
    /// </summary>
    public bool HasMapping { get; set; }

    /// <summary>
    /// 容器是否健康
    /// </summary>
    public bool IsContainerHealthy { get; set; }

    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 配置错误列表
    /// </summary>
    public List<string> ConfigurationErrors { get; set; } = new();
}

/// <summary>
/// 测试用的简单 DbContext
/// </summary>
internal class TestDbContext : DbContext
{
    public TestDbContext(DbContextOptions options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // 简单的模型配置，用于测试
        base.OnModelCreating(modelBuilder);
    }
}
