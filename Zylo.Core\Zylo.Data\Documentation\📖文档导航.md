# 📖 Zylo.Data 文档导航

## 🎯 你应该看哪个文档？

### 🆕 **新手开发者（推荐从这里开始）**
📚 **[新手完整开发指南.md](📚新手完整开发指南.md)**
- ✅ 如何添加新功能到Zylo.Data
- ✅ 配置为什么这么多以及如何简化使用  
- ✅ 完整的代码模板和示例
- ✅ 实际的文件修改步骤
- ✅ 7步开发流程详解

**这个文档包含了你需要的所有信息！**

---

### 📚 **其他文档说明**

#### 📖 **[使用指南.md](使用指南.md)**
- 现有功能的使用方法
- 数据库、缓存、配置等功能介绍
- 适合了解现有功能

#### 🚀 **[升级计划.md](升级计划.md)**  
- 框架的发展规划
- 未来功能预览
- 适合了解框架发展方向

#### 📋 **[README.md](README.md)**
- 项目基本介绍
- 快速开始指南
- 适合第一次接触项目

---

## 🗂️ 项目文件结构

```
Zylo.Data/
├── 📚新手完整开发指南.md          # 🎯 新手必读：完整开发指南
├── 📖文档导航.md                  # 📍 当前文件：文档导航
├── 使用指南.md                    # 📖 现有功能使用说明
├── 升级计划.md                    # 🚀 框架发展规划
├── README.md                      # 📋 项目基本介绍
├── 📁 DI/                         # 依赖注入配置
├── 📁 Interfaces/                 # 接口定义
├── 📁 Services/                   # 服务实现
├── 📁 Examples/                   # 示例代码
├── YData.cs                       # 静态API
├── ZyloDataOptions.cs             # 配置选项
└── Y*Extensions.cs                # 扩展方法
```

---

## 🎯 快速导航

### 🆕 **我是新手，想学习如何开发**
👉 直接看：**[📚新手完整开发指南.md](📚新手完整开发指南.md)**

### 🔧 **我想了解现有功能怎么用**
👉 直接看：**[使用指南.md](使用指南.md)**

### 🚀 **我想了解框架的发展方向**
👉 直接看：**[升级计划.md](升级计划.md)**

### 📋 **我想了解项目基本信息**
👉 直接看：**[README.md](README.md)**

---

## 💡 学习建议

### 🎯 **推荐学习路径**

1. **第1步**：阅读 [📚新手完整开发指南.md](📚新手完整开发指南.md)
   - 了解配置为什么这么多（实际使用很简单）
   - 学习7步开发流程
   - 掌握代码模板

2. **第2步**：实际动手练习
   - 按照指南添加一个简单功能
   - 比如文本读取功能

3. **第3步**：深入了解现有功能
   - 阅读 [使用指南.md](使用指南.md)
   - 了解数据库、缓存等功能

4. **第4步**：参与框架发展
   - 阅读 [升级计划.md](升级计划.md)
   - 贡献你的想法和代码

---

## 🎉 总结

**只需要记住一个文件**：
📚 **[新手完整开发指南.md](📚新手完整开发指南.md)**

这个文件包含了：
- ✅ 配置简化使用方法
- ✅ 完整的开发流程
- ✅ 代码模板和示例
- ✅ 实际修改步骤
- ✅ 最佳实践建议

**其他文档都是补充材料，有需要时再看！** 🚀
