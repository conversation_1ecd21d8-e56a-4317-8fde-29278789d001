# 📚 Zylo.Data 新手完整开发指南

## 🎯 这个指南包含什么？

这是一个**完整的、整理过的**指南，包含了：
- ✅ 如何添加新功能到Zylo.Data
- ✅ 配置为什么这么多以及如何简化使用
- ✅ 完整的代码模板和示例
- ✅ 实际的文件修改步骤

---

## 📁 项目文件结构

```
Zylo.Data/                                    # 项目根目录
├── 📁 DI/                                    
│   └── ZyloDataContainerExtensions.cs        # ✅ 需要修改：添加服务注册
├── 📁 Interfaces/                            
│   ├── IYCache.cs, IYDatabase.cs...         # 🔒 已存在，不要动
│   └── IYText.cs                            # 🆕 需要新建：你的接口
├── 📁 Services/                              
│   ├── YCacheService.cs, YDatabaseService.cs... # 🔒 已存在，不要动
│   └── YTextService.cs                      # 🆕 需要新建：你的服务实现
├── YData.cs                                 # ✅ 需要修改：添加GetText()方法
├── ZyloDataOptions.cs                       # ✅ 需要修改：添加配置选项
└── YTextExtensions.cs                       # 🆕 需要新建：你的扩展方法
```

---

## 🚀 配置使用：从复杂到简单

### 😰 看起来复杂（20+个配置选项）
```csharp
// ZyloDataOptions.cs 中有很多配置，看起来很复杂
public bool EnableDatabase { get; set; } = true;
public bool EnableCache { get; set; } = true;
public string ConnectionString { get; set; } = "";
// ... 还有20多个配置
```

### 😊 实际使用很简单

#### 🟢 90%的情况：零配置
```csharp
// 就这一行！什么都不用管
YData.UseAuto();

// 然后直接使用
var data = YData.GetDatabase().Query<User>("SELECT * FROM Users");
```

#### 🟡 9%的情况：只配置数据库
```csharp
// 只配置一个数据库连接，其他20多个配置都用默认值
YData.UseAuto(options =>
{
    options.ConnectionString = "你的数据库连接字符串";
});
```

#### 🔴 1%的情况：精细调优
```csharp
// 只有特殊需求才需要调整更多配置
YData.UseAuto(options =>
{
    options.ConnectionString = "连接字符串";
    options.MaxPoolSize = 200;  // 高并发才需要
    options.EnableCache = false; // 不需要缓存才禁用
});
```

---

## 🛠️ 添加新功能：7个步骤

### 📝 示例：添加文本读取功能

#### 第1步：添加配置选项（修改现有文件）

在 `ZyloDataOptions.cs` 文件的最后添加：

```csharp
    #region 文本读取配置
    
    /// <summary>
    /// 启用文本读取功能
    /// </summary>
    public bool EnableTextReading { get; set; } = true;
    
    /// <summary>
    /// 默认文本编码
    /// </summary>
    public string DefaultTextEncoding { get; set; } = "UTF-8";
    
    /// <summary>
    /// 是否自动去除空行
    /// </summary>
    public bool RemoveEmptyLines { get; set; } = false;
    
    #endregion
```

#### 第2步：创建接口（新建文件）

新建 `Interfaces/IYText.cs`：

```csharp
namespace Zylo.Data.Interfaces;

/// <summary>
/// 文本处理服务接口
/// </summary>
public interface IYText
{
    /// <summary>
    /// 按行读取文本文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码（可选）</param>
    /// <returns>文本行数组</returns>
    Task<string[]> ReadLinesAsync(string filePath, string? encoding = null);
}
```

#### 第3步：实现服务（新建文件）

新建 `Services/YTextService.cs`：

```csharp
using System.Text;
using Zylo.Data.Interfaces;

namespace Zylo.Data.Services;

/// <summary>
/// 文本处理服务实现
/// </summary>
public class YTextService : IYText
{
    private readonly ZyloDataOptions _options;
    
    public YTextService(ZyloDataOptions options)
    {
        _options = options;
    }
    
    public async Task<string[]> ReadLinesAsync(string filePath, string? encoding = null)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));
            
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"文件不存在: {filePath}");
        
        try
        {
            // 确定编码（使用配置）
            var useEncoding = !string.IsNullOrEmpty(encoding) 
                ? Encoding.GetEncoding(encoding) 
                : Encoding.GetEncoding(_options.DefaultTextEncoding);
            
            // 读取文件
            var lines = await File.ReadAllLinesAsync(filePath, useEncoding);
            
            // 根据配置处理空行
            if (_options.RemoveEmptyLines)
            {
                lines = lines.Where(line => !string.IsNullOrWhiteSpace(line)).ToArray();
            }
            
            return lines;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"读取文件失败: {ex.Message}", ex);
        }
    }
}
```

#### 第4步：配置依赖注入（修改现有文件）

在 `DI/ZyloDataContainerExtensions.cs` 的现有服务注册后面添加：

```csharp
// Microsoft.Extensions.DI 注册
if (options.EnableTextReading)
{
    services.AddSingleton<IYText, YTextService>();
}

// DryIoc 注册
if (options.EnableTextReading)
{
    container.Register<IYText, YTextService>(Reuse.Singleton);
}
```

#### 第5步：添加静态API（修改现有文件）

在 `YData.cs` 的现有Get方法后面添加：

```csharp
    /// <summary>
    /// 获取文本服务
    /// </summary>
    /// <returns>文本服务实例</returns>
    public static IYText GetText()
    {
        EnsureInitialized();
        return _container!.Resolve<IYText>();
    }
```

#### 第6步：创建扩展方法（新建文件）

新建 `YTextExtensions.cs`：

```csharp
using Zylo.Data.Interfaces;

namespace Zylo.Data;

/// <summary>
/// 文本扩展方法
/// </summary>
public static class YTextExtensions
{
    /// <summary>
    /// 按行读取文本文件（扩展方法）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码</param>
    /// <returns>文本行数组</returns>
    public static async Task<string[]> YReadLinesAsync(this string filePath, string? encoding = null)
    {
        var textService = YData.GetText();
        return await textService.ReadLinesAsync(filePath, encoding);
    }
}
```

#### 第7步：测试验证

```csharp
// 初始化
YData.UseAuto(options =>
{
    options.EnableTextReading = true;
    options.DefaultTextEncoding = "UTF-8";
    options.RemoveEmptyLines = false;
});

// 创建测试文件
await File.WriteAllTextAsync("test.txt", "第一行\n\n第二行\n第三行");

// 方式1：静态API
var textService = YData.GetText();
var lines1 = await textService.ReadLinesAsync("test.txt");
Console.WriteLine($"静态API读取: {lines1.Length} 行");

// 方式2：扩展方法（推荐）
var lines2 = await "test.txt".YReadLinesAsync();
Console.WriteLine($"扩展方法读取: {lines2.Length} 行");
```

---

## 🔗 与 Zylo.ALL 集成

**重要**：添加新功能后，需要在 **Zylo.ALL** 中启用集成，否则无法通过 `YAll` 访问新功能！

### 🎯 具体问题：Zylo.ALL需要做什么？

当你在Zylo.Data中添加了文本读取功能后：

**❌ 修改前**：
```csharp
YAll.UseAuto();
var textService = YAll.Resolve<IYText>(); // 异常！服务未注册
```

**✅ 修改后**：
```csharp
YAll.UseAuto(options =>
{
    options.EnableData = true;
    options.DataOptions = new ZyloDataOptions
    {
        EnableTextReading = true  // 启用新功能
    };
});
var textService = YAll.GetText(); // 成功！
```

### 第1步：启用集成（修改现有代码）

**文件**: `Zylo.ALL/DI/ZyloAllContainerExtensions.cs`

**找到被注释的代码**：
```csharp
if (options.EnableData)
{
    // 等待 Zylo.Data 升级完成后启用
    // container.AddZyloData();  ← 取消这行注释
}
```

**修改为**：
```csharp
if (options.EnableData)
{
    container.AddZyloData(dataOptions =>
    {
        if (options.DataOptions != null)
        {
            dataOptions.ConnectionString = options.DataOptions.ConnectionString;
            dataOptions.EnableTextReading = options.DataOptions.EnableTextReading; // 新功能
            dataOptions.DefaultTextEncoding = options.DataOptions.DefaultTextEncoding; // 新功能
            // 传递其他配置...
        }
    });
}
```

### 第2步：添加配置（修改现有代码）

**文件**: `Zylo.ALL/ZyloAllOptions.cs`

```csharp
public class ZyloDataOptions
{
    public string ConnectionString { get; set; } = "";
    public bool EnableTextReading { get; set; } = true;  // 新功能配置
    public string DefaultTextEncoding { get; set; } = "UTF-8";  // 新功能配置
    // 其他配置...
}
```

### 第3步：添加便捷方法（新增代码，可选）

**文件**: `Zylo.ALL/YAll.cs`

```csharp
/// <summary>
/// 获取文本服务
/// </summary>
/// <returns>文本服务实例</returns>
public static IYText GetText()
{
    return GetContainer().Resolve<IYText>();
}
```

### 完整使用示例

```csharp
// 通过 Zylo.ALL 统一入口使用
YAll.UseAuto(options =>
{
    options.EnableData = true;
    options.DataOptions = new ZyloDataOptions
    {
        ConnectionString = "your-connection-string",
        EnableTextReading = true,  // 启用你新添加的文本功能
        DefaultTextEncoding = "UTF-8"
    };
});

// 现在可以通过 YAll 访问新功能
var text = YAll.GetText();
var lines = await text.ReadLinesAsync("data.txt");

// 或者使用扩展方法
var lines2 = await "data.txt".YReadLinesAsync();
```

**详细说明请参考**: [Zylo.ALL集成指南.md](Zylo.ALL集成指南.md)

---

## 🔑 代码模板总结

### 🔒 固定模板（不能改）
- 命名空间格式：`namespace Zylo.Data.Interfaces;`
- 构造函数注入：`public YTextService(ZyloDataOptions options)`
- 异常处理格式：`throw new InvalidOperationException(...)`
- 依赖注入格式：`services.AddSingleton<IYText, YTextService>()`

### 🔧 可变内容（需要改）
- 功能名称：Text、Json、Xml等
- 方法名称：ReadLines、ParseJson等
- 配置选项：根据功能需要设计
- 业务逻辑：具体的功能实现

### 📝 命名规则
- 接口：`IY + 功能名` → `IYText`
- 服务：`Y功能名Service` → `YTextService`
- 扩展类：`Y功能名Extensions` → `YTextExtensions`
- 静态方法：`Get功能名` → `GetText()`
- 扩展方法：`Y + 方法名` → `YReadLines()`

---

## ✅ 检查清单

开发新功能时，按照这个清单逐项检查：

### 📊 Zylo.Data 模块开发
- [ ] **配置选项**：在ZyloDataOptions.cs中添加配置区域
- [ ] **接口定义**：在Interfaces/文件夹中创建接口
- [ ] **服务实现**：在Services/文件夹中创建实现
- [ ] **依赖注入**：在ContainerExtensions.cs中添加注册
- [ ] **静态API**：在YData.cs中添加Get方法
- [ ] **扩展方法**：在根目录创建扩展方法文件
- [ ] **模块测试**：编写测试代码验证功能

### 🔗 Zylo.ALL 集成（重要！）
- [ ] **启用集成**：在ZyloAllContainerExtensions.cs中取消注释并配置
- [ ] **添加配置**：在ZyloAllOptions.cs中添加新功能配置
- [ ] **便捷方法**：在YAll.cs中添加Get方法（可选）
- [ ] **集成测试**：验证通过YAll可以访问新功能

---

## 🎯 最佳实践

### ✅ 推荐做法
1. **从简单开始**：先用 `YData.UseAuto()`
2. **按需配置**：只配置必要的选项
3. **遵循模板**：严格按照框架模板开发
4. **测试验证**：确保功能正常工作

### ❌ 避免错误
1. **不要删除现有代码**：只添加，不删除
2. **不要配置所有选项**：大多数用默认值就好
3. **不要修改固定格式**：框架要求的格式必须遵守
4. **不要忽略异常处理**：按照模板添加异常处理

---

## 🎉 总结

**记住这三个要点**：

1. **配置看起来复杂，使用很简单**：90%的情况用 `YData.UseAuto()` 就够了
2. **开发新功能有固定模板**：按照7个步骤，严格遵循模板
3. **从简单开始，需要时再调整**：不要一开始就想配置所有选项

**这个指南包含了你需要的所有信息，不需要再看其他分散的文件了！** 🚀
