using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Zylo.Data.Extensions;
using Zylo.Data.Interfaces;

namespace Zylo.Data.Examples;

/// <summary>
/// Y数据库使用示例 - 展示如何使用现代化的YDatabase
/// 替代传统的Bin.Shared DBHelper，提供更好的开发体验
/// </summary>
public class YDatabaseUsageExample
{
    /// <summary>
    /// Y基础使用示例 - 依赖注入方式
    /// </summary>
    public static async Task Y基础使用示例()
    {
        // 1. Y配置服务容器
        var services = new ServiceCollection();
        services.AddLogging();
        
        // Y注册数据库服务 - SQLite
        services.AddYDatabase("Data Source=example.db");
        
        var provider = services.BuildServiceProvider();
        var ydb = provider.GetRequiredService<IYDatabase>();

        // 2. Y基础CRUD操作
        var user = new User { Name = "张三", Email = "<EMAIL>" };
        
        // Y插入数据
        var insertResult = await ydb.YInsertAsync(user);
        Console.WriteLine($"Y插入结果: {insertResult} 行受影响");

        // Y查找数据
        var foundUser = await ydb.YFindAsync<User>(user.Id);
        Console.WriteLine($"Y找到用户: {foundUser?.Name}");

        // Y更新数据
        if (foundUser != null)
        {
            foundUser.Email = "<EMAIL>";
            var updateResult = await ydb.YUpdateAsync(foundUser);
            Console.WriteLine($"Y更新结果: {updateResult} 行受影响");
        }

        // Y删除数据
        var deleteResult = await ydb.YDeleteByIdAsync<User>(user.Id);
        Console.WriteLine($"Y删除结果: {deleteResult} 行受影响");
    }

    /// <summary>
    /// Y高级查询示例 - 多表关联和分组
    /// </summary>
    public static async Task Y高级查询示例(IYDatabase ydb)
    {
        // Y复杂查询 - 用户订单统计
        var userOrderStats = await ydb.YTable<User>()
            .YJoin(ydb.YTable<Order>(), u => u.Id, o => o.UserId)
            .YWhere((u, o) => u.IsActive && o.Status == "已完成")
            .YSelect((u, o) => new UserOrderStat
            {
                UserName = u.Name,
                OrderCount = 1, // 这里需要实际的聚合逻辑
                TotalAmount = o.Amount
            })
            .YToListAsync();

        Console.WriteLine($"Y查询到 {userOrderStats.Count} 条用户订单统计");

        // Y分页查询
        var pagedUsers = await ydb.YTable<User>()
            .YWhere(u => u.IsActive)
            .YOrderBy(u => u.Name)
            .YToPagedListAsync(pageIndex: 1, pageSize: 10);

        Console.WriteLine($"Y分页查询: 第{pagedUsers.PageIndex}页，共{pagedUsers.TotalPages}页");

        // Y缓存查询
        var cachedUsers = await ydb.YTable<User>()
            .YWhere(u => u.IsActive)
            .YCacheFor(TimeSpan.FromMinutes(5))
            .YToListAsync();

        Console.WriteLine($"Y缓存查询: {cachedUsers.Count} 个活跃用户");
    }

    /// <summary>
    /// Y事务操作示例
    /// </summary>
    public static async Task Y事务操作示例(IYDatabase ydb)
    {
        // Y方式1: 手动事务管理
        using var transaction = await ydb.YBeginTransactionAsync();
        try
        {
            var user = new User { Name = "事务用户", Email = "<EMAIL>" };
            await ydb.YInsertAsync(user);

            var order = new Order { UserId = user.Id, Amount = 100.00m, Status = "待支付" };
            await ydb.YInsertAsync(order);

            await transaction.YCommitAsync();
            Console.WriteLine("Y事务提交成功");
        }
        catch
        {
            await transaction.YRollbackAsync();
            Console.WriteLine("Y事务回滚");
        }

        // Y方式2: 自动事务管理
        var success = await ydb.YExecuteInTransactionAsync(async db =>
        {
            var user = new User { Name = "自动事务用户", Email = "<EMAIL>" };
            await db.YInsertAsync(user);

            var order = new Order { UserId = user.Id, Amount = 200.00m, Status = "已支付" };
            await db.YInsertAsync(order);

            return true;
        });

        Console.WriteLine($"Y自动事务结果: {success}");
    }

    /// <summary>
    /// Y与Bin.Shared DBHelper对比示例
    /// </summary>
    public static void Y对比传统方式()
    {
        Console.WriteLine("=== Y数据库操作对比 ===");
        
        Console.WriteLine("传统 Bin.Shared DBHelper 方式:");
        Console.WriteLine("var users = dbHelper.QueryList<User>(u => u.IsActive);");
        Console.WriteLine("// 问题: 可能抛异常、不支持异步、功能有限");
        
        Console.WriteLine("\nY现代化 YDatabase 方式:");
        Console.WriteLine("var users = await ydb.YTable<User>()");
        Console.WriteLine("    .YWhere(u => u.IsActive)");
        Console.WriteLine("    .YOrderBy(u => u.Name)");
        Console.WriteLine("    .YCacheFor(TimeSpan.FromMinutes(5))");
        Console.WriteLine("    .YToListAsync();");
        Console.WriteLine("// 优势: 永不抛异常、全异步、强类型、缓存支持");
    }
}

/// <summary>
/// 示例实体类 - 用户
/// </summary>
public class User
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 示例实体类 - 订单
/// </summary>
public class Order
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public decimal Amount { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // 导航属性
    public User? User { get; set; }
}

/// <summary>
/// 示例统计类 - 用户订单统计
/// </summary>
public class UserOrderStat
{
    public string UserName { get; set; } = string.Empty;
    public int OrderCount { get; set; }
    public decimal TotalAmount { get; set; }
}

/// <summary>
/// Y业务服务示例 - 展示在实际业务中的使用
/// </summary>
public class YUserService
{
    private readonly IYDatabase _ydb;
    private readonly ILogger<YUserService> _logger;

    public YUserService(IYDatabase yDatabase, ILogger<YUserService> logger)
    {
        _ydb = yDatabase;
        _logger = logger;
    }

    /// <summary>
    /// Y获取活跃用户列表
    /// </summary>
    public async Task<List<User>> Y获取活跃用户列表()
    {
        return await _ydb.YTable<User>()
            .YWhere(u => u.IsActive)
            .YOrderBy(u => u.Name)
            .YCacheFor(TimeSpan.FromMinutes(10), "active_users")
            .YToListAsync();
    }

    /// <summary>
    /// Y创建用户和首单
    /// </summary>
    public async Task<bool> Y创建用户和首单(string name, string email, decimal firstOrderAmount)
    {
        return await _ydb.YExecuteInTransactionAsync(async db =>
        {
            // 创建用户
            var user = new User { Name = name, Email = email };
            var userResult = await db.YInsertAsync(user);
            if (userResult == 0) return false;

            // 创建首单
            var order = new Order 
            { 
                UserId = user.Id, 
                Amount = firstOrderAmount, 
                Status = "已创建" 
            };
            var orderResult = await db.YInsertAsync(order);
            
            _logger.LogInformation("Y成功创建用户 {UserName} 和首单 {Amount}", name, firstOrderAmount);
            return orderResult > 0;
        });
    }

    /// <summary>
    /// Y获取用户订单统计
    /// </summary>
    public async Task<UserOrderStat?> Y获取用户订单统计(int userId)
    {
        var stats = await _ydb.YTable<User>()
            .YWhere(u => u.Id == userId)
            .YJoin(_ydb.YTable<Order>(), u => u.Id, o => o.UserId)
            .YWhere((u, o) => o.Status == "已完成")
            .YSelect((u, o) => new UserOrderStat
            {
                UserName = u.Name,
                OrderCount = 1,
                TotalAmount = o.Amount
            })
            .YFirstOrDefaultAsync();

        return stats;
    }
}
