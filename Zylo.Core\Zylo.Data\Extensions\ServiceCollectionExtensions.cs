using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Zylo.Data.Interfaces;
using Zylo.Data.Services;

namespace Zylo.Data.Extensions;

/// <summary>
/// Zylo.Data 依赖注入扩展方法
/// 按照总体升级计划，本项目提供数据处理的依赖注入支持
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 注册所有 Zylo.Data 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYData(this IServiceCollection services)
    {
        // 注册数据库服务
        services.TryAddSingleton<IYDatabase, YDatabaseService>();
        
        // 注册配置服务
        services.TryAddSingleton<IYConfiguration, YConfigurationService>();
        
        // 注册缓存服务
        services.TryAddSingleton<IYCache, YCacheService>();
        
        // 注册映射服务
        services.TryAddSingleton<IYMapping, YMappingService>();
        
        return services;
    }

    /// <summary>
    /// Y注册数据库服务 - 支持SQLite
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="connectionString">SQLite连接字符串</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYDatabase(this IServiceCollection services, string connectionString)
    {
        // 注册DbContext
        services.AddDbContext<DbContext>(options =>
        {
            options.UseSqlite(connectionString);
            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
        });

        // 注册内存缓存
        services.AddMemoryCache();

        // 注册YDatabase服务
        services.TryAddScoped<IYDatabase, YDatabaseService>();

        return services;
    }

    /// <summary>
    /// Y注册数据库服务 - 使用配置委托
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">数据库配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYDatabase(this IServiceCollection services, Action<DbContextOptionsBuilder> configureOptions)
    {
        // 注册DbContext
        services.AddDbContext<DbContext>(configureOptions);

        // 注册内存缓存
        services.AddMemoryCache();

        // 注册YDatabase服务
        services.TryAddScoped<IYDatabase, YDatabaseService>();

        return services;
    }

    /// <summary>
    /// Y注册数据库服务 - 使用现有DbContext
    /// </summary>
    /// <typeparam name="TContext">DbContext类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYDatabase<TContext>(this IServiceCollection services) where TContext : DbContext
    {
        // 注册内存缓存
        services.AddMemoryCache();

        // 注册YDatabase服务，使用指定的DbContext
        services.TryAddScoped<IYDatabase>(provider =>
        {
            var context = provider.GetRequiredService<TContext>();
            var logger = provider.GetRequiredService<ILogger<YDatabaseService>>();
            var cache = provider.GetRequiredService<IMemoryCache>();
            return new YDatabaseService(context, logger, cache);
        });

        return services;
    }
    
    /// <summary>
    /// 注册 YConfiguration 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYConfiguration(this IServiceCollection services)
    {
        services.TryAddSingleton<IYConfiguration, YConfigurationService>();
        return services;
    }
    
    /// <summary>
    /// 注册 YCache 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYCache(this IServiceCollection services)
    {
        services.TryAddSingleton<IYCache, YCacheService>();
        return services;
    }
    
    /// <summary>
    /// 注册 YMapping 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYMapping(this IServiceCollection services)
    {
        services.TryAddSingleton<IYMapping, YMappingService>();
        return services;
    }

    /// <summary>
    /// 替换默认的 YDatabase 实现
    /// </summary>
    /// <typeparam name="TImplementation">自定义实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ReplaceYDatabase<TImplementation>(this IServiceCollection services)
        where TImplementation : class, IYDatabase
    {
        services.Replace(ServiceDescriptor.Singleton<IYDatabase, TImplementation>());
        return services;
    }
    
    /// <summary>
    /// 替换默认的 YConfiguration 实现
    /// </summary>
    /// <typeparam name="TImplementation">自定义实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ReplaceYConfiguration<TImplementation>(this IServiceCollection services)
        where TImplementation : class, IYConfiguration
    {
        services.Replace(ServiceDescriptor.Singleton<IYConfiguration, TImplementation>());
        return services;
    }
    
    /// <summary>
    /// 替换默认的 YCache 实现
    /// </summary>
    /// <typeparam name="TImplementation">自定义实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ReplaceYCache<TImplementation>(this IServiceCollection services)
        where TImplementation : class, IYCache
    {
        services.Replace(ServiceDescriptor.Singleton<IYCache, TImplementation>());
        return services;
    }
    
    /// <summary>
    /// 替换默认的 YMapping 实现
    /// </summary>
    /// <typeparam name="TImplementation">自定义实现类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ReplaceYMapping<TImplementation>(this IServiceCollection services)
        where TImplementation : class, IYMapping
    {
        services.Replace(ServiceDescriptor.Singleton<IYMapping, TImplementation>());
        return services;
    }
}
