using System.Linq.Expressions;

namespace Zylo.Data.Interfaces;

/// <summary>
/// Y数据库操作核心接口 - 现代化数据库访问层
/// 提供类型安全、异步优先、永不抛异常的数据库操作
/// 支持SQLite多表查询、事务管理、缓存集成
/// </summary>
public interface IYDatabase : IDisposable
{
    #region Y核心查询接口

    /// <summary>
    /// Y获取表查询器 - 支持强类型LINQ查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>返回可查询的表对象</returns>
    IYQueryable<T> YTable<T>() where T : class;

    /// <summary>
    /// Y根据ID查找单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键ID</param>
    /// <returns>找到的实体，未找到返回null</returns>
    Task<T?> YFindAsync<T>(object id) where T : class;

    /// <summary>
    /// Y根据条件查找单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件</param>
    /// <returns>找到的实体，未找到返回null</returns>
    Task<T?> YFirstOrDefaultAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Y根据条件查询实体列表 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件，null表示查询所有</param>
    /// <returns>实体列表</returns>
    Task<List<T>> YToListAsync<T>(Expression<Func<T, bool>>? predicate = null) where T : class;

    /// <summary>
    /// Y检查实体是否存在 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件</param>
    /// <returns>存在返回true，否则返回false</returns>
    Task<bool> YAnyAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    /// <summary>
    /// Y统计实体数量 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件，null表示统计所有</param>
    /// <returns>实体数量</returns>
    Task<int> YCountAsync<T>(Expression<Func<T, bool>>? predicate = null) where T : class;

    #endregion

    #region Y增删改操作接口

    /// <summary>
    /// Y插入单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <returns>插入成功返回受影响行数，失败返回0</returns>
    Task<int> YInsertAsync<T>(T entity) where T : class;

    /// <summary>
    /// Y批量插入实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体列表</param>
    /// <returns>插入成功的实体数量</returns>
    Task<int> YInsertRangeAsync<T>(IEnumerable<T> entities) where T : class;

    /// <summary>
    /// Y更新单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <returns>更新成功返回受影响行数，失败返回0</returns>
    Task<int> YUpdateAsync<T>(T entity) where T : class;

    /// <summary>
    /// Y根据条件更新实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">更新条件</param>
    /// <param name="updateExpression">更新表达式</param>
    /// <returns>更新成功返回受影响行数，失败返回0</returns>
    Task<int> YUpdateWhereAsync<T>(Expression<Func<T, bool>> predicate, Expression<Func<T, T>> updateExpression) where T : class;

    /// <summary>
    /// Y删除单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要删除的实体</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    Task<int> YDeleteAsync<T>(T entity) where T : class;

    /// <summary>
    /// Y根据ID删除实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">要删除的实体ID</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    Task<int> YDeleteByIdAsync<T>(object id) where T : class;

    /// <summary>
    /// Y根据条件删除实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">删除条件</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    Task<int> YDeleteWhereAsync<T>(Expression<Func<T, bool>> predicate) where T : class;

    #endregion

    #region Y事务管理接口

    /// <summary>
    /// Y开始数据库事务 (异步)
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务对象</returns>
    Task<IYTransaction> YBeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Y在事务中执行操作 (异步)
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    Task<T> YExecuteInTransactionAsync<T>(Func<IYDatabase, Task<T>> operation, CancellationToken cancellationToken = default);

    #endregion
}
