using System.Linq.Expressions;

namespace Zylo.Data.Interfaces;

/// <summary>
/// Y可查询接口 - 支持强类型LINQ查询和多表关联
/// 提供类似LINQ的流畅API，支持SQLite等数据库的复杂查询
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public interface IYQueryable<T> where T : class
{
    #region Y基础查询方法
    
    /// <summary>
    /// Y添加Where条件
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YWhere(Expression<Func<T, bool>> predicate);
    
    /// <summary>
    /// Y排序 (升序)
    /// </summary>
    /// <typeparam name="TKey">排序字段类型</typeparam>
    /// <param name="keySelector">排序字段选择器</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YOrderBy<TKey>(Expression<Func<T, TKey>> keySelector);
    
    /// <summary>
    /// Y排序 (降序)
    /// </summary>
    /// <typeparam name="TKey">排序字段类型</typeparam>
    /// <param name="keySelector">排序字段选择器</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YOrderByDescending<TKey>(Expression<Func<T, TKey>> keySelector);
    
    /// <summary>
    /// Y跳过指定数量的记录
    /// </summary>
    /// <param name="count">跳过的记录数</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YSkip(int count);
    
    /// <summary>
    /// Y获取指定数量的记录
    /// </summary>
    /// <param name="count">获取的记录数</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YTake(int count);
    
    #endregion
    
    #region Y多表关联方法
    
    /// <summary>
    /// Y内连接
    /// </summary>
    /// <typeparam name="TInner">关联表类型</typeparam>
    /// <param name="inner">关联表查询器</param>
    /// <param name="outerKeySelector">外表关联字段</param>
    /// <param name="innerKeySelector">内表关联字段</param>
    /// <returns>关联查询器</returns>
    IYJoinQueryable<T, TInner> YJoin<TInner>(
        IYQueryable<TInner> inner,
        Expression<Func<T, object>> outerKeySelector,
        Expression<Func<TInner, object>> innerKeySelector) where TInner : class;
    
    /// <summary>
    /// Y左连接
    /// </summary>
    /// <typeparam name="TInner">关联表类型</typeparam>
    /// <param name="inner">关联表查询器</param>
    /// <param name="outerKeySelector">外表关联字段</param>
    /// <param name="innerKeySelector">内表关联字段</param>
    /// <returns>关联查询器</returns>
    IYJoinQueryable<T, TInner> YLeftJoin<TInner>(
        IYQueryable<TInner> inner,
        Expression<Func<T, object>> outerKeySelector,
        Expression<Func<TInner, object>> innerKeySelector) where TInner : class;
    
    #endregion
    
    #region Y分组和聚合方法
    
    /// <summary>
    /// Y分组
    /// </summary>
    /// <typeparam name="TKey">分组键类型</typeparam>
    /// <param name="keySelector">分组键选择器</param>
    /// <returns>分组查询器</returns>
    IYGroupQueryable<TKey, T> YGroupBy<TKey>(Expression<Func<T, TKey>> keySelector);
    
    #endregion
    
    #region Y执行方法
    
    /// <summary>
    /// Y转换为列表 (异步)
    /// </summary>
    /// <returns>实体列表</returns>
    Task<List<T>> YToListAsync();
    
    /// <summary>
    /// Y获取第一个实体 (异步)
    /// </summary>
    /// <returns>第一个实体，未找到返回null</returns>
    Task<T?> YFirstOrDefaultAsync();
    
    /// <summary>
    /// Y检查是否存在 (异步)
    /// </summary>
    /// <returns>存在返回true，否则返回false</returns>
    Task<bool> YAnyAsync();
    
    /// <summary>
    /// Y统计数量 (异步)
    /// </summary>
    /// <returns>实体数量</returns>
    Task<int> YCountAsync();
    
    /// <summary>
    /// Y分页查询 (异步)
    /// </summary>
    /// <param name="pageIndex">页码 (从1开始)</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页结果</returns>
    Task<YPagedResult<T>> YToPagedListAsync(int pageIndex, int pageSize);
    
    #endregion
    
    #region Y缓存方法
    
    /// <summary>
    /// Y设置查询缓存
    /// </summary>
    /// <param name="duration">缓存时长</param>
    /// <param name="cacheKey">缓存键，null表示自动生成</param>
    /// <returns>查询器</returns>
    IYQueryable<T> YCacheFor(TimeSpan duration, string? cacheKey = null);
    
    #endregion
}

/// <summary>
/// Y关联查询接口
/// </summary>
/// <typeparam name="TOuter">外表类型</typeparam>
/// <typeparam name="TInner">内表类型</typeparam>
public interface IYJoinQueryable<TOuter, TInner> 
    where TOuter : class 
    where TInner : class
{
    /// <summary>
    /// Y选择结果
    /// </summary>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="selector">结果选择器</param>
    /// <returns>查询器</returns>
    IYQueryable<TResult> YSelect<TResult>(Expression<Func<TOuter, TInner, TResult>> selector) where TResult : class;
    
    /// <summary>
    /// Y添加Where条件
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>关联查询器</returns>
    IYJoinQueryable<TOuter, TInner> YWhere(Expression<Func<TOuter, TInner, bool>> predicate);
}

/// <summary>
/// Y分组查询接口
/// </summary>
/// <typeparam name="TKey">分组键类型</typeparam>
/// <typeparam name="TElement">元素类型</typeparam>
public interface IYGroupQueryable<TKey, TElement> where TElement : class
{
    /// <summary>
    /// Y选择分组结果
    /// </summary>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="selector">结果选择器</param>
    /// <returns>查询器</returns>
    IYQueryable<TResult> YSelect<TResult>(Expression<Func<IGrouping<TKey, TElement>, TResult>> selector) where TResult : class;
}

/// <summary>
/// Y分页结果
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class YPagedResult<T>
{
    /// <summary>
    /// 当前页数据
    /// </summary>
    public List<T> Items { get; set; } = new();
    
    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }
    
    /// <summary>
    /// 页码 (从1开始)
    /// </summary>
    public int PageIndex { get; set; }
    
    /// <summary>
    /// 每页大小
    /// </summary>
    public int PageSize { get; set; }
    
    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    
    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 1;
    
    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages;
}
