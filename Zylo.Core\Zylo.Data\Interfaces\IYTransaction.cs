namespace Zylo.Data.Interfaces;

/// <summary>
/// Y数据库事务接口 - 支持事务管理和嵌套事务
/// 提供安全的事务操作，确保数据一致性
/// </summary>
public interface IYTransaction : IDisposable, IAsyncDisposable
{
    /// <summary>
    /// 事务ID
    /// </summary>
    string TransactionId { get; }
    
    /// <summary>
    /// 事务状态
    /// </summary>
    YTransactionState State { get; }
    
    /// <summary>
    /// Y提交事务 (异步)
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>提交任务</returns>
    Task YCommitAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Y回滚事务 (异步)
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回滚任务</returns>
    Task YRollbackAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Y创建保存点
    /// </summary>
    /// <param name="name">保存点名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存点任务</returns>
    Task YCreateSavepointAsync(string name, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Y回滚到保存点
    /// </summary>
    /// <param name="name">保存点名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>回滚任务</returns>
    Task YRollbackToSavepointAsync(string name, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Y释放保存点
    /// </summary>
    /// <param name="name">保存点名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放任务</returns>
    Task YReleaseSavepointAsync(string name, CancellationToken cancellationToken = default);
}

/// <summary>
/// Y事务状态枚举
/// </summary>
public enum YTransactionState
{
    /// <summary>
    /// 活跃状态
    /// </summary>
    Active,
    
    /// <summary>
    /// 已提交
    /// </summary>
    Committed,
    
    /// <summary>
    /// 已回滚
    /// </summary>
    RolledBack,
    
    /// <summary>
    /// 已释放
    /// </summary>
    Disposed
}

/// <summary>
/// Y事务选项
/// </summary>
public class YTransactionOptions
{
    /// <summary>
    /// 事务隔离级别
    /// </summary>
    public YIsolationLevel IsolationLevel { get; set; } = YIsolationLevel.ReadCommitted;
    
    /// <summary>
    /// 事务超时时间
    /// </summary>
    public TimeSpan? Timeout { get; set; }
    
    /// <summary>
    /// 是否为只读事务
    /// </summary>
    public bool IsReadOnly { get; set; } = false;
    
    /// <summary>
    /// 事务名称 (用于调试)
    /// </summary>
    public string? Name { get; set; }
}

/// <summary>
/// Y事务隔离级别
/// </summary>
public enum YIsolationLevel
{
    /// <summary>
    /// 读未提交
    /// </summary>
    ReadUncommitted,
    
    /// <summary>
    /// 读已提交
    /// </summary>
    ReadCommitted,
    
    /// <summary>
    /// 可重复读
    /// </summary>
    RepeatableRead,
    
    /// <summary>
    /// 序列化
    /// </summary>
    Serializable
}
