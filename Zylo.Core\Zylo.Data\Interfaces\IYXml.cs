using System.Xml;
using System.Xml.Linq;

namespace Zylo.Data.Interfaces;

/// <summary>
/// XML增强服务接口
/// 提供XML文档的创建、读取、修改、验证等功能
/// </summary>
public interface IYXml
{
    #region 基础操作
    
    /// <summary>
    /// 从文件加载XML文档
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>XML文档</returns>
    Task<XDocument> LoadFromFileAsync(string filePath);
    
    /// <summary>
    /// 从字符串加载XML文档
    /// </summary>
    /// <param name="xmlContent">XML内容</param>
    /// <returns>XML文档</returns>
    XDocument LoadFromString(string xmlContent);
    
    /// <summary>
    /// 保存XML文档到文件
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否成功</returns>
    Task<bool> SaveToFileAsync(XDocument document, string filePath);
    
    /// <summary>
    /// 将XML文档转换为字符串
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="formatted">是否格式化</param>
    /// <returns>XML字符串</returns>
    string ToString(XDocument document, bool formatted = true);
    
    #endregion
    
    #region 查询操作
    
    /// <summary>
    /// 使用XPath查询节点
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="xpath">XPath表达式</param>
    /// <returns>查询结果</returns>
    IEnumerable<XElement> QueryByXPath(XDocument document, string xpath);
    
    /// <summary>
    /// 查询指定名称的所有元素
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="elementName">元素名称</param>
    /// <returns>元素集合</returns>
    IEnumerable<XElement> QueryByElementName(XDocument document, string elementName);
    
    /// <summary>
    /// 根据属性查询元素
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="attributeName">属性名</param>
    /// <param name="attributeValue">属性值</param>
    /// <returns>元素集合</returns>
    IEnumerable<XElement> QueryByAttribute(XDocument document, string attributeName, string attributeValue);
    
    #endregion
    
    #region 修改操作
    
    /// <summary>
    /// 添加元素
    /// </summary>
    /// <param name="parent">父元素</param>
    /// <param name="elementName">元素名称</param>
    /// <param name="value">元素值</param>
    /// <param name="attributes">属性字典</param>
    /// <returns>新添加的元素</returns>
    XElement AddElement(XElement parent, string elementName, object? value = null, 
        Dictionary<string, string>? attributes = null);
    
    /// <summary>
    /// 更新元素值
    /// </summary>
    /// <param name="element">要更新的元素</param>
    /// <param name="newValue">新值</param>
    /// <returns>是否成功</returns>
    bool UpdateElementValue(XElement element, object newValue);
    
    /// <summary>
    /// 删除元素
    /// </summary>
    /// <param name="element">要删除的元素</param>
    /// <returns>是否成功</returns>
    bool RemoveElement(XElement element);
    
    #endregion
    
    #region 验证操作
    
    /// <summary>
    /// 验证XML格式是否正确
    /// </summary>
    /// <param name="xmlContent">XML内容</param>
    /// <returns>验证结果</returns>
    (bool IsValid, string ErrorMessage) ValidateXml(string xmlContent);
    
    /// <summary>
    /// 使用XSD验证XML
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <param name="xsdPath">XSD文件路径</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, List<string> Errors)> ValidateWithXsdAsync(XDocument document, string xsdPath);
    
    #endregion
    
    #region 转换操作
    
    /// <summary>
    /// 将对象转换为XML
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="obj">要转换的对象</param>
    /// <returns>XML文档</returns>
    XDocument SerializeToXml<T>(T obj);
    
    /// <summary>
    /// 将XML转换为对象
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="document">XML文档</param>
    /// <returns>转换后的对象</returns>
    T? DeserializeFromXml<T>(XDocument document);
    
    /// <summary>
    /// XML转JSON
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <returns>JSON字符串</returns>
    string XmlToJson(XDocument document);
    
    /// <summary>
    /// JSON转XML
    /// </summary>
    /// <param name="json">JSON字符串</param>
    /// <param name="rootElementName">根元素名称</param>
    /// <returns>XML文档</returns>
    XDocument JsonToXml(string json, string rootElementName = "root");
    
    #endregion
    
    #region 高级操作
    
    /// <summary>
    /// 合并两个XML文档
    /// </summary>
    /// <param name="source">源文档</param>
    /// <param name="target">目标文档</param>
    /// <param name="mergeStrategy">合并策略</param>
    /// <returns>合并后的文档</returns>
    XDocument MergeDocuments(XDocument source, XDocument target, XmlMergeStrategy mergeStrategy = XmlMergeStrategy.Append);
    
    /// <summary>
    /// 比较两个XML文档的差异
    /// </summary>
    /// <param name="doc1">文档1</param>
    /// <param name="doc2">文档2</param>
    /// <returns>差异列表</returns>
    List<XmlDifference> CompareDocuments(XDocument doc1, XDocument doc2);
    
    /// <summary>
    /// 应用XSLT转换
    /// </summary>
    /// <param name="document">源文档</param>
    /// <param name="xsltPath">XSLT文件路径</param>
    /// <returns>转换后的文档</returns>
    Task<XDocument> TransformWithXsltAsync(XDocument document, string xsltPath);
    
    /// <summary>
    /// 压缩XML文档
    /// </summary>
    /// <param name="document">XML文档</param>
    /// <returns>压缩后的字节数组</returns>
    byte[] CompressXml(XDocument document);
    
    /// <summary>
    /// 解压缩XML文档
    /// </summary>
    /// <param name="compressedData">压缩数据</param>
    /// <returns>XML文档</returns>
    XDocument DecompressXml(byte[] compressedData);
    
    #endregion
}

/// <summary>
/// XML合并策略
/// </summary>
public enum XmlMergeStrategy
{
    /// <summary>追加</summary>
    Append,
    /// <summary>覆盖</summary>
    Overwrite,
    /// <summary>合并</summary>
    Merge
}

/// <summary>
/// XML差异信息
/// </summary>
public class XmlDifference
{
    /// <summary>差异类型</summary>
    public XmlDifferenceType Type { get; set; }
    
    /// <summary>XPath路径</summary>
    public string XPath { get; set; } = string.Empty;
    
    /// <summary>旧值</summary>
    public string? OldValue { get; set; }
    
    /// <summary>新值</summary>
    public string? NewValue { get; set; }
    
    /// <summary>描述</summary>
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// XML差异类型
/// </summary>
public enum XmlDifferenceType
{
    /// <summary>元素添加</summary>
    ElementAdded,
    /// <summary>元素删除</summary>
    ElementRemoved,
    /// <summary>元素修改</summary>
    ElementModified,
    /// <summary>属性添加</summary>
    AttributeAdded,
    /// <summary>属性删除</summary>
    AttributeRemoved,
    /// <summary>属性修改</summary>
    AttributeModified,
    /// <summary>文本修改</summary>
    TextModified
}
