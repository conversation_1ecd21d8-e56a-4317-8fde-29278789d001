namespace Zylo.Data.Interfaces;

/// <summary>
/// Zylo.Data 数据管理服务接口
/// 提供数据访问层的统一管理和监控功能
/// </summary>
public interface IZyloDataService
{
    /// <summary>
    /// 获取服务版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    string GetVersion();

    /// <summary>
    /// 获取服务状态信息
    /// </summary>
    /// <returns>状态信息</returns>
    ZyloDataStatus GetStatus();

    /// <summary>
    /// 执行健康检查
    /// </summary>
    /// <returns>健康检查结果</returns>
    ZyloDataHealthCheck PerformHealthCheck();

    /// <summary>
    /// 获取已启用的功能列表
    /// </summary>
    /// <returns>功能名称列表</returns>
    IEnumerable<string> GetEnabledFeatures();

    /// <summary>
    /// 检查指定功能是否已启用
    /// </summary>
    /// <param name="featureName">功能名称</param>
    /// <returns>是否已启用</returns>
    bool IsFeatureEnabled(string featureName);

    /// <summary>
    /// 获取数据访问统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    ZyloDataStatistics GetStatistics();

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    ZyloDataPerformanceMetrics GetPerformanceMetrics();

    /// <summary>
    /// 重置统计信息
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// 验证数据库连接
    /// </summary>
    /// <returns>连接验证结果</returns>
    Task<(bool IsValid, string Message)> ValidateDatabaseConnectionAsync();

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    /// <returns>数据库信息</returns>
    Task<ZyloDatabaseInfo> GetDatabaseInfoAsync();

    /// <summary>
    /// 清理缓存
    /// </summary>
    /// <returns>清理结果</returns>
    Task<bool> ClearCacheAsync();

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计</returns>
    ZyloCacheStatistics GetCacheStatistics();
}

/// <summary>
/// Zylo.Data 服务状态信息
/// </summary>
public class ZyloDataStatus
{
    /// <summary>
    /// 是否已初始化
    /// </summary>
    public bool IsInitialized { get; set; }

    /// <summary>
    /// 服务状态
    /// </summary>
    public string Status { get; set; } = "Unknown";

    /// <summary>
    /// 已启用功能数量
    /// </summary>
    public int EnabledFeaturesCount { get; set; }

    /// <summary>
    /// 运行时间
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// 数据库连接状态
    /// </summary>
    public string DatabaseConnectionStatus { get; set; } = "Unknown";

    /// <summary>
    /// 缓存状态
    /// </summary>
    public string CacheStatus { get; set; } = "Unknown";

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Zylo.Data 健康检查结果
/// </summary>
public class ZyloDataHealthCheck
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 健康状态
    /// </summary>
    public string Status { get; set; } = "Unknown";

    /// <summary>
    /// 检查持续时间（毫秒）
    /// </summary>
    public double CheckDurationMs { get; set; }

    /// <summary>
    /// 检查结果详情
    /// </summary>
    public Dictionary<string, object> CheckResults { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Zylo.Data 统计信息
/// </summary>
public class ZyloDataStatistics
{
    /// <summary>
    /// 数据库查询次数
    /// </summary>
    public long DatabaseQueriesCount { get; set; }

    /// <summary>
    /// 数据库插入次数
    /// </summary>
    public long DatabaseInsertsCount { get; set; }

    /// <summary>
    /// 数据库更新次数
    /// </summary>
    public long DatabaseUpdatesCount { get; set; }

    /// <summary>
    /// 数据库删除次数
    /// </summary>
    public long DatabaseDeletesCount { get; set; }

    /// <summary>
    /// 缓存命中次数
    /// </summary>
    public long CacheHitsCount { get; set; }

    /// <summary>
    /// 缓存未命中次数
    /// </summary>
    public long CacheMissesCount { get; set; }

    /// <summary>
    /// 事务次数
    /// </summary>
    public long TransactionsCount { get; set; }

    /// <summary>
    /// 错误次数
    /// </summary>
    public long ErrorCount { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; } = 100.0;

    /// <summary>
    /// 缓存命中率
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// 统计开始时间
    /// </summary>
    public DateTime StatisticsStartTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Zylo.Data 性能指标
/// </summary>
public class ZyloDataPerformanceMetrics
{
    /// <summary>
    /// 平均查询时间（毫秒）
    /// </summary>
    public double AverageQueryTimeMs { get; set; }

    /// <summary>
    /// 平均插入时间（毫秒）
    /// </summary>
    public double AverageInsertTimeMs { get; set; }

    /// <summary>
    /// 平均更新时间（毫秒）
    /// </summary>
    public double AverageUpdateTimeMs { get; set; }

    /// <summary>
    /// 平均删除时间（毫秒）
    /// </summary>
    public double AverageDeleteTimeMs { get; set; }

    /// <summary>
    /// 平均事务时间（毫秒）
    /// </summary>
    public double AverageTransactionTimeMs { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// 测量时间
    /// </summary>
    public DateTime MeasurementTime { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 数据库信息
/// </summary>
public class ZyloDatabaseInfo
{
    /// <summary>
    /// 数据库类型
    /// </summary>
    public string DatabaseType { get; set; } = "Unknown";

    /// <summary>
    /// 数据库版本
    /// </summary>
    public string DatabaseVersion { get; set; } = "Unknown";

    /// <summary>
    /// 连接字符串（脱敏）
    /// </summary>
    public string ConnectionString { get; set; } = "Unknown";

    /// <summary>
    /// 表数量
    /// </summary>
    public int TableCount { get; set; }

    /// <summary>
    /// 数据库大小（字节）
    /// </summary>
    public long DatabaseSizeBytes { get; set; }

    /// <summary>
    /// 是否支持事务
    /// </summary>
    public bool SupportsTransactions { get; set; }

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; }

    /// <summary>
    /// 当前连接数
    /// </summary>
    public int CurrentConnections { get; set; }
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class ZyloCacheStatistics
{
    /// <summary>
    /// 缓存项数量
    /// </summary>
    public int ItemCount { get; set; }

    /// <summary>
    /// 缓存大小（字节）
    /// </summary>
    public long CacheSizeBytes { get; set; }

    /// <summary>
    /// 命中次数
    /// </summary>
    public long HitCount { get; set; }

    /// <summary>
    /// 未命中次数
    /// </summary>
    public long MissCount { get; set; }

    /// <summary>
    /// 命中率
    /// </summary>
    public double HitRate { get; set; }

    /// <summary>
    /// 过期项数量
    /// </summary>
    public int ExpiredItemCount { get; set; }

    /// <summary>
    /// 最后清理时间
    /// </summary>
    public DateTime LastCleanupTime { get; set; }
}
