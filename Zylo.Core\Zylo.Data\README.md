# 🗄️ Zylo.Data

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/Tests-119%2F119%20✅-brightgreen.svg)](#)

> **数据的智慧管家** - 提供数据库操作、配置管理、缓存和对象映射的完整解决方案

## 📋 **项目概述**

Zylo.Data 是 Zylo 工具包的数据处理核心模块，按照总体升级计划设计，提供企业级的数据操作功能。

### 🎯 **核心功能**

- **🗃️ YDatabase** - 数据库操作工具
- **⚙️ YConfiguration** - 配置管理工具  
- **🚀 YCache** - 内存缓存工具
- **🔄 YMapping** - 对象映射工具

## 🚀 **快速开始**

### 安装

```bash
dotnet add package Zylo.Data
```

### 基本使用

```csharp
using Zylo.Data;

// 数据库连接字符串构建
var connStr = YDatabaseExtensions.YBuildSqlServerConnectionString(
    "localhost", "MyDB", "sa", "password");

// 配置文件读取
var dbHost = "config.ini".YReadIniValue("Database", "Host", "localhost");

// 缓存操作
"user:123".YSetCache(userObject, TimeSpan.FromMinutes(30));
var user = "user:123".YGetCache<User>();

// 对象映射
var dto = sourceObject.YMapTo<UserDto>();
```

## 📚 **详细功能**

### 🗃️ **YDatabase - 数据库工具**

```csharp
// 连接字符串构建
var sqlServer = YDatabaseExtensions.YBuildSqlServerConnectionString(
    "localhost", "MyDB", "sa", "password");
var mysql = YDatabaseExtensions.YBuildMySqlConnectionString(
    "localhost", "mydb", "root", "password");
var sqlite = YDatabaseExtensions.YBuildSQLiteConnectionString("data.db");

// 连接测试
var isValid = await connStr.YTestConnectionAsync("System.Data.SqlClient");

// 数据库值处理
var age = dbReader["Age"].YConvertDbValue<int>(0);
var isNull = dbValue.YIsDbNull();
var paramValue = userInput.YToDbValue();

// SQL构建辅助
var escaped = userInput.YEscapeSqlString();
var inClause = ids.YBuildInClause(); // (1,2,3,4,5)
```

### ⚙️ **YConfiguration - 配置管理**

```csharp
// INI文件操作
var value = "config.ini".YReadIniValue("Database", "ConnectionString", "");
var success = "config.ini".YWriteIniValue("Database", "Host", "localhost");
var section = "config.ini".YReadIniSection("Database");

// 环境变量
var path = "PATH".YGetEnvironmentVariable("");
var success = "MY_VAR".YSetEnvironmentVariable("value");

// 配置值转换
var port = "8080".YConvertConfigValue<int>(80);
var enabled = "true".YConvertConfigValue<bool>(false);
```

### 🚀 **YCache - 缓存工具**

```csharp
// 基本缓存操作
"key".YSetCache("value", TimeSpan.FromMinutes(30));
var value = "key".YGetCache<string>("default");
var exists = "key".YHasCache();
"key".YRemoveCache();

// 高级缓存操作
var user = "user:123".YGetOrSetCache(() => LoadUser(123), TimeSpan.FromMinutes(30));
var data = await "data:456".YGetOrSetCacheAsync(async () => await LoadDataAsync(456));

// 缓存管理
YCacheExtensions.YClearAllCache();
var stats = YCacheExtensions.YGetCacheStats();
var cleaned = YCacheExtensions.YCleanupExpiredCache();
```

### 🔄 **YMapping - 对象映射**

```csharp
// 基本对象映射
var target = source.YMapTo<TargetType>();
source.YMapTo(existingTarget);

// 属性复制
var clone = original.YClone();
source.YCopyPropertiesTo(target, "ExcludedProperty");

// 字典映射
var dict = new Dictionary<string, object> { ["Name"] = "张三", ["Age"] = 25 };
var person = dict.YMapToObject<Person>();
var dictionary = person.YMapToDictionary();

// 集合映射
var targetList = sourceList.YMapToList<TargetType>();
var dtoList = entities.YMapToList<Entity, EntityDto>();
```

## 🏗️ **依赖注入支持**

```csharp
// 注册服务
services.AddZyloData();

// 使用服务
public class DataService
{
    private readonly IYDatabase _database;
    private readonly IYCache _cache;
    
    public DataService(IYDatabase database, IYCache cache)
    {
        _database = database;
        _cache = cache;
    }
}
```

## 📊 **项目状态**

### 🎯 **按总体升级计划**
- **项目定位**: 第3个项目 (3/7)
- **总体进度**: 43%
- **当前状态**: 🎉 **项目开发完成** ✅

### ✅ **开发完成**
- [x] ✅ 实现 YDatabase 数据库操作功能 (45个测试)
- [x] ✅ 实现 YConfiguration 配置管理功能 (33个测试)
- [x] ✅ 实现 YCache 缓存功能 (21个测试)
- [x] ✅ 实现 YMapping 对象映射功能 (20个测试)
- [x] ✅ 编写完整测试 (119/119 测试通过 ✅)
- [x] ✅ 完善文档 (完整XML注释和使用示例)

### 📈 **测试覆盖**
- **总测试数**: 119个
- **通过率**: 100% ✅
- **覆盖模块**: 4个核心模块全覆盖

## 🔗 **相关项目**

- [Zylo.Core](../Zylo.Core) - 核心工具集
- [Zylo.IO](../Zylo.IO) - 输入输出工具集
- [总体升级进度](../总体升级进度.md) - 查看整体进度

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

<div align="center">

**🎯 Zylo.Data - 数据的智慧管家！**

</div>
