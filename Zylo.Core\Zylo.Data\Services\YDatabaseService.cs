using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using System.Linq.Expressions;
using Zylo.Data.Interfaces;
using Zylo.AutoG.Attributes;

namespace Zylo.Data.Services;

/// <summary>
/// Y数据库服务实现类 - 现代化数据库访问层
/// 基于Entity Framework Core 8.0，提供类型安全、异步优先的数据库操作
/// 支持SQLite多表查询、事务管理、缓存集成、永不抛异常设计
/// 集成 Zylo.AutoG 四大核心功能，提供现代化的数据访问体验
/// </summary>
[YDoc]                    // 自动生成API文档
[YService.Scoped]         // 自动生成接口和依赖注入，Scoped生命周期
[YStatic]                 // 生成扩展方法，支持流畅API
public partial class YDatabaseService : IYDatabase
{
    private readonly DbContext _context;
    private readonly ILogger<YDatabaseService> _logger;
    private readonly IMemoryCache _cache;
    private bool _disposed = false;

    /// <summary>
    /// Y数据库服务构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="cache">内存缓存</param>
    public YDatabaseService(DbContext context, ILogger<YDatabaseService> logger, IMemoryCache cache)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
    }

    #region Y核心查询实现

    /// <summary>
    /// Y获取表查询器 - 支持强类型LINQ查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>返回可查询的表对象</returns>
    [YLog.Debug]
    public IYQueryable<T> YTableCore<T>() where T : class
    {
        try
        {
            var dbSet = _context.Set<T>();
            return new YQueryableWrapper<T>(dbSet, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y获取表查询器失败: {EntityType}", typeof(T).Name);
            return new YQueryableWrapper<T>(Enumerable.Empty<T>().AsQueryable(), _cache, _logger);
        }
    }

    /// <summary>
    /// Y根据ID查找单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键ID</param>
    /// <returns>找到的实体，未找到返回null</returns>
    [YLog.Info(performanceThresholdMs: 50)]
    public async Task<T?> YFindAsyncCore<T>(object id) where T : class
    {
        try
        {
            if (id == null)
            {
                _logger.LogWarning("Y查找实体失败: ID不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return null;
            }

            var entity = await _context.Set<T>().FindAsync(id);
            if (entity == null)
            {
                _logger.LogDebug("Y未找到实体: ID={Id}, 实体类型: {EntityType}", id, typeof(T).Name);
            }
            return entity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y查找实体异常: ID={Id}, 实体类型: {EntityType}", id, typeof(T).Name);
            return null;
        }
    }

    /// <summary>
    /// Y根据条件查找单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件</param>
    /// <returns>找到的实体，未找到返回null</returns>
    [YLog.Info(performanceThresholdMs: 100)]
    public async Task<T?> YFirstOrDefaultAsyncCore<T>(Expression<Func<T, bool>> predicate) where T : class
    {
        try
        {
            if (predicate == null)
            {
                _logger.LogWarning("Y查找实体失败: 查询条件不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return null;
            }

            return await _context.Set<T>().FirstOrDefaultAsync(predicate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y根据条件查找实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return null;
        }
    }

    /// <summary>
    /// Y根据条件查询实体列表 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件，null表示查询所有</param>
    /// <returns>实体列表</returns>
    [YLog.Info(performanceThresholdMs: 200)]
    public async Task<List<T>> YToListAsyncCore<T>(Expression<Func<T, bool>>? predicate = null) where T : class
    {
        try
        {
            var query = _context.Set<T>().AsQueryable();
            if (predicate != null)
            {
                query = query.Where(predicate);
            }
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y查询实体列表异常: 实体类型: {EntityType}", typeof(T).Name);
            return new List<T>();
        }
    }

    /// <summary>
    /// Y检查实体是否存在 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件</param>
    /// <returns>存在返回true，否则返回false</returns>
    [YLog.Debug]
    public async Task<bool> YAnyAsyncCore<T>(Expression<Func<T, bool>> predicate) where T : class
    {
        try
        {
            if (predicate == null)
            {
                _logger.LogWarning("Y检查实体存在失败: 查询条件不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return false;
            }

            return await _context.Set<T>().AnyAsync(predicate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y检查实体存在异常: 实体类型: {EntityType}", typeof(T).Name);
            return false;
        }
    }

    /// <summary>
    /// Y统计实体数量 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">查询条件，null表示统计所有</param>
    /// <returns>实体数量</returns>
    [YLog.Info(performanceThresholdMs: 100)]
    public async Task<int> YCountAsyncCore<T>(Expression<Func<T, bool>>? predicate = null) where T : class
    {
        try
        {
            var query = _context.Set<T>().AsQueryable();
            if (predicate != null)
            {
                query = query.Where(predicate);
            }
            return await query.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y统计实体数量异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    #endregion
    #region Y增删改操作实现

    /// <summary>
    /// Y插入单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <returns>插入成功返回受影响行数，失败返回0</returns>
    public async Task<int> YInsertAsync<T>(T entity) where T : class
    {
        try
        {
            if (entity == null)
            {
                _logger.LogWarning("Y插入实体失败: 实体不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            _context.Set<T>().Add(entity);
            var result = await _context.SaveChangesAsync();
            _logger.LogDebug("Y插入实体成功: 受影响行数={RowsAffected}, 实体类型: {EntityType}", result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y插入实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y批量插入实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体列表</param>
    /// <returns>插入成功的实体数量</returns>
    public async Task<int> YInsertRangeAsync<T>(IEnumerable<T> entities) where T : class
    {
        try
        {
            if (entities == null || !entities.Any())
            {
                _logger.LogWarning("Y批量插入实体失败: 实体列表为空, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            var entityList = entities.ToList();
            _context.Set<T>().AddRange(entityList);
            var result = await _context.SaveChangesAsync();
            _logger.LogDebug("Y批量插入实体成功: 插入数量={Count}, 受影响行数={RowsAffected}, 实体类型: {EntityType}",
                entityList.Count, result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y批量插入实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y更新单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <returns>更新成功返回受影响行数，失败返回0</returns>
    public async Task<int> YUpdateAsync<T>(T entity) where T : class
    {
        try
        {
            if (entity == null)
            {
                _logger.LogWarning("Y更新实体失败: 实体不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            _context.Set<T>().Update(entity);
            var result = await _context.SaveChangesAsync();
            _logger.LogDebug("Y更新实体成功: 受影响行数={RowsAffected}, 实体类型: {EntityType}", result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y更新实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y根据条件更新实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">更新条件</param>
    /// <param name="updateExpression">更新表达式</param>
    /// <returns>更新成功返回受影响行数，失败返回0</returns>
    public async Task<int> YUpdateWhereAsync<T>(Expression<Func<T, bool>> predicate, Expression<Func<T, T>> updateExpression) where T : class
    {
        try
        {
            if (predicate == null || updateExpression == null)
            {
                _logger.LogWarning("Y条件更新实体失败: 条件或更新表达式不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            // EF Core 6.0 不支持 ExecuteUpdateAsync，使用传统方式
            var entities = await _context.Set<T>().Where(predicate).ToListAsync();
            var result = 0;

            foreach (var entity in entities)
            {
                var compiled = updateExpression.Compile();
                var updatedEntity = compiled(entity);

                // 复制属性值
                _context.Entry(entity).CurrentValues.SetValues(updatedEntity);
                result++;
            }

            if (result > 0)
            {
                await _context.SaveChangesAsync();
            }

            _logger.LogDebug("Y条件更新实体成功: 受影响行数={RowsAffected}, 实体类型: {EntityType}", result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y条件更新实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y删除单个实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要删除的实体</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    public async Task<int> YDeleteAsync<T>(T entity) where T : class
    {
        try
        {
            if (entity == null)
            {
                _logger.LogWarning("Y删除实体失败: 实体不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            _context.Set<T>().Remove(entity);
            var result = await _context.SaveChangesAsync();
            _logger.LogDebug("Y删除实体成功: 受影响行数={RowsAffected}, 实体类型: {EntityType}", result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y删除实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y根据ID删除实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">要删除的实体ID</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    public async Task<int> YDeleteByIdAsync<T>(object id) where T : class
    {
        try
        {
            if (id == null)
            {
                _logger.LogWarning("Y根据ID删除实体失败: ID不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            var entity = await _context.Set<T>().FindAsync(id);
            if (entity == null)
            {
                _logger.LogWarning("Y根据ID删除实体失败: 未找到实体, ID={Id}, 实体类型: {EntityType}", id, typeof(T).Name);
                return 0;
            }

            _context.Set<T>().Remove(entity);
            var result = await _context.SaveChangesAsync();
            _logger.LogDebug("Y根据ID删除实体成功: ID={Id}, 受影响行数={RowsAffected}, 实体类型: {EntityType}",
                id, result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y根据ID删除实体异常: ID={Id}, 实体类型: {EntityType}", id, typeof(T).Name);
            return 0;
        }
    }

    /// <summary>
    /// Y根据条件删除实体 (异步)
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">删除条件</param>
    /// <returns>删除成功返回受影响行数，失败返回0</returns>
    public async Task<int> YDeleteWhereAsync<T>(Expression<Func<T, bool>> predicate) where T : class
    {
        try
        {
            if (predicate == null)
            {
                _logger.LogWarning("Y条件删除实体失败: 删除条件不能为null, 实体类型: {EntityType}", typeof(T).Name);
                return 0;
            }

            // EF Core 6.0 不支持 ExecuteDeleteAsync，使用传统方式
            var entities = await _context.Set<T>().Where(predicate).ToListAsync();
            var result = entities.Count;

            if (result > 0)
            {
                _context.Set<T>().RemoveRange(entities);
                await _context.SaveChangesAsync();
            }

            _logger.LogDebug("Y条件删除实体成功: 受影响行数={RowsAffected}, 实体类型: {EntityType}", result, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y条件删除实体异常: 实体类型: {EntityType}", typeof(T).Name);
            return 0;
        }
    }

    #endregion
    #region Y事务管理实现

    /// <summary>
    /// Y开始数据库事务 (异步)
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>事务对象</returns>
    public async Task<IYTransaction> YBeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
            var yTransaction = new YTransactionWrapper(transaction, _logger);
            _logger.LogDebug("Y开始事务成功: TransactionId={TransactionId}", yTransaction.TransactionId);
            return yTransaction;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y开始事务异常");
            // 返回一个空事务对象，避免抛异常
            return new YNullTransaction();
        }
    }

    /// <summary>
    /// Y在事务中执行操作 (异步)
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果</returns>
    public async Task<T> YExecuteInTransactionAsync<T>(Func<IYDatabase, Task<T>> operation, CancellationToken cancellationToken = default)
    {
        try
        {
            if (operation == null)
            {
                _logger.LogWarning("Y事务执行操作失败: 操作不能为null");
                return default(T)!;
            }

            using var transaction = await YBeginTransactionAsync(cancellationToken);
            try
            {
                var result = await operation(this);
                await transaction.YCommitAsync(cancellationToken);
                _logger.LogDebug("Y事务执行操作成功: TransactionId={TransactionId}", transaction.TransactionId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Y事务执行操作异常，正在回滚: TransactionId={TransactionId}", transaction.TransactionId);
                await transaction.YRollbackAsync(cancellationToken);
                return default(T)!;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y事务执行操作外层异常");
            return default(T)!;
        }
    }

    #endregion

    #region Y资源释放

    /// <summary>
    /// Y释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Y释放资源的具体实现
    /// </summary>
    /// <param name="disposing">是否释放托管资源</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    _context?.Dispose();
                    _logger.LogDebug("Y数据库服务资源释放成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Y数据库服务资源释放异常");
                }
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// Y析构函数
    /// </summary>
    ~YDatabaseService()
    {
        Dispose(false);
    }

    #endregion
}
