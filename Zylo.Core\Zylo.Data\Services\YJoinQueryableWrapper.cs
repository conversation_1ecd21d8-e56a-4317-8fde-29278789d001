using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Zylo.Data.Interfaces;

namespace Zylo.Data.Services;

/// <summary>
/// Y关联查询包装类 - 实现IYJoinQueryable接口
/// 支持多表关联查询和结果选择
/// </summary>
/// <typeparam name="TOuter">外表类型</typeparam>
/// <typeparam name="TInner">内表类型</typeparam>
internal class YJoinQueryableWrapper<TOuter, TInner> : IYJoinQueryable<TOuter, TInner>
    where TOuter : class
    where TInner : class
{
    private readonly IQueryable<(TOuter, TInner)> _queryable;
    private readonly IMemoryCache _cache;
    private readonly ILogger _logger;

    public YJoinQueryableWrapper(IQueryable<(TOuter, TInner)> queryable, IMemoryCache cache, ILogger logger)
    {
        _queryable = queryable ?? throw new ArgumentNullException(nameof(queryable));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public IYQueryable<TResult> YSelect<TResult>(Expression<Func<TOuter, TInner, TResult>> selector) where TResult : class
    {
        try
        {
            if (selector == null)
            {
                _logger.LogWarning("YSelect: 结果选择器不能为null");
                return new YQueryableWrapper<TResult>(Enumerable.Empty<TResult>().AsQueryable(), _cache, _logger);
            }

            var resultQuery = _queryable.Select(tuple => selector.Compile()(tuple.Item1, tuple.Item2));
            return new YQueryableWrapper<TResult>(resultQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YSelect: 选择结果异常");
            return new YQueryableWrapper<TResult>(Enumerable.Empty<TResult>().AsQueryable(), _cache, _logger);
        }
    }

    public IYJoinQueryable<TOuter, TInner> YWhere(Expression<Func<TOuter, TInner, bool>> predicate)
    {
        try
        {
            if (predicate == null)
            {
                _logger.LogWarning("YWhere: 查询条件不能为null");
                return this;
            }

            var filteredQuery = _queryable.Where(tuple => predicate.Compile()(tuple.Item1, tuple.Item2));
            return new YJoinQueryableWrapper<TOuter, TInner>(filteredQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YWhere: 添加关联查询条件异常");
            return this;
        }
    }
}

/// <summary>
/// Y分组查询包装类 - 实现IYGroupQueryable接口
/// 支持分组查询和聚合操作
/// </summary>
/// <typeparam name="TKey">分组键类型</typeparam>
/// <typeparam name="TElement">元素类型</typeparam>
internal class YGroupQueryableWrapper<TKey, TElement> : IYGroupQueryable<TKey, TElement> where TElement : class
{
    private readonly IQueryable<IGrouping<TKey, TElement>> _queryable;
    private readonly IMemoryCache _cache;
    private readonly ILogger _logger;

    public YGroupQueryableWrapper(IQueryable<IGrouping<TKey, TElement>> queryable, IMemoryCache cache, ILogger logger)
    {
        _queryable = queryable ?? throw new ArgumentNullException(nameof(queryable));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public IYQueryable<TResult> YSelect<TResult>(Expression<Func<IGrouping<TKey, TElement>, TResult>> selector) where TResult : class
    {
        try
        {
            if (selector == null)
            {
                _logger.LogWarning("YSelect: 分组结果选择器不能为null");
                return new YQueryableWrapper<TResult>(Enumerable.Empty<TResult>().AsQueryable(), _cache, _logger);
            }

            var resultQuery = _queryable.Select(selector);
            return new YQueryableWrapper<TResult>(resultQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YSelect: 选择分组结果异常");
            return new YQueryableWrapper<TResult>(Enumerable.Empty<TResult>().AsQueryable(), _cache, _logger);
        }
    }
}
