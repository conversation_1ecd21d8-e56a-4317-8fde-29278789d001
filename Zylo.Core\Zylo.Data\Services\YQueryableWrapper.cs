using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using Zylo.Data.Interfaces;

namespace Zylo.Data.Services;

/// <summary>
/// Y查询器包装类 - 实现IYQueryable接口
/// 提供强类型LINQ查询和多表关联支持
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
internal class YQueryableWrapper<T> : IYQueryable<T> where T : class
{
    private readonly IQueryable<T> _queryable;
    private readonly IMemoryCache _cache;
    private readonly ILogger _logger;
    private TimeSpan? _cacheDuration;
    private string? _cacheKey;

    public YQueryableWrapper(IQueryable<T> queryable, IMemoryCache cache, ILogger logger)
    {
        _queryable = queryable ?? throw new ArgumentNullException(nameof(queryable));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #region Y基础查询方法

    public IYQueryable<T> YWhere(Expression<Func<T, bool>> predicate)
    {
        try
        {
            if (predicate == null)
            {
                _logger.LogWarning("YWhere: 查询条件不能为null");
                return this;
            }
            
            var newQueryable = _queryable.Where(predicate);
            return new YQueryableWrapper<T>(newQueryable, _cache, _logger)
            {
                _cacheDuration = _cacheDuration,
                _cacheKey = _cacheKey
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YWhere: 添加查询条件异常");
            return this;
        }
    }

    public IYQueryable<T> YOrderBy<TKey>(Expression<Func<T, TKey>> keySelector)
    {
        try
        {
            if (keySelector == null)
            {
                _logger.LogWarning("YOrderBy: 排序字段选择器不能为null");
                return this;
            }
            
            var newQueryable = _queryable.OrderBy(keySelector);
            return new YQueryableWrapper<T>(newQueryable, _cache, _logger)
            {
                _cacheDuration = _cacheDuration,
                _cacheKey = _cacheKey
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOrderBy: 排序异常");
            return this;
        }
    }

    public IYQueryable<T> YOrderByDescending<TKey>(Expression<Func<T, TKey>> keySelector)
    {
        try
        {
            if (keySelector == null)
            {
                _logger.LogWarning("YOrderByDescending: 排序字段选择器不能为null");
                return this;
            }
            
            var newQueryable = _queryable.OrderByDescending(keySelector);
            return new YQueryableWrapper<T>(newQueryable, _cache, _logger)
            {
                _cacheDuration = _cacheDuration,
                _cacheKey = _cacheKey
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YOrderByDescending: 降序排序异常");
            return this;
        }
    }

    public IYQueryable<T> YSkip(int count)
    {
        try
        {
            if (count < 0)
            {
                _logger.LogWarning("YSkip: 跳过数量不能为负数");
                return this;
            }
            
            var newQueryable = _queryable.Skip(count);
            return new YQueryableWrapper<T>(newQueryable, _cache, _logger)
            {
                _cacheDuration = _cacheDuration,
                _cacheKey = _cacheKey
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YSkip: 跳过记录异常");
            return this;
        }
    }

    public IYQueryable<T> YTake(int count)
    {
        try
        {
            if (count <= 0)
            {
                _logger.LogWarning("YTake: 获取数量必须大于0");
                return this;
            }
            
            var newQueryable = _queryable.Take(count);
            return new YQueryableWrapper<T>(newQueryable, _cache, _logger)
            {
                _cacheDuration = _cacheDuration,
                _cacheKey = _cacheKey
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YTake: 获取记录异常");
            return this;
        }
    }

    #endregion

    #region Y多表关联方法

    public IYJoinQueryable<T, TInner> YJoin<TInner>(
        IYQueryable<TInner> inner,
        Expression<Func<T, object>> outerKeySelector,
        Expression<Func<TInner, object>> innerKeySelector) where TInner : class
    {
        try
        {
            if (inner == null || outerKeySelector == null || innerKeySelector == null)
            {
                _logger.LogWarning("YJoin: 参数不能为null");
                return new YJoinQueryableWrapper<T, TInner>(
                    Enumerable.Empty<(T, TInner)>().AsQueryable(), _cache, _logger);
            }

            // 这里需要实现实际的JOIN逻辑
            // 由于EF Core的限制，我们使用简化的实现
            var joinResult = from outer in _queryable
                           join innerItem in ((YQueryableWrapper<TInner>)inner)._queryable
                           on outerKeySelector.Compile()(outer) equals innerKeySelector.Compile()(innerItem)
                           select new { Outer = outer, Inner = innerItem };

            var tupleQuery = joinResult.Select(x => ValueTuple.Create(x.Outer, x.Inner));
            return new YJoinQueryableWrapper<T, TInner>(tupleQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YJoin: 内连接异常");
            return new YJoinQueryableWrapper<T, TInner>(
                Enumerable.Empty<(T, TInner)>().AsQueryable(), _cache, _logger);
        }
    }

    public IYJoinQueryable<T, TInner> YLeftJoin<TInner>(
        IYQueryable<TInner> inner,
        Expression<Func<T, object>> outerKeySelector,
        Expression<Func<TInner, object>> innerKeySelector) where TInner : class
    {
        try
        {
            if (inner == null || outerKeySelector == null || innerKeySelector == null)
            {
                _logger.LogWarning("YLeftJoin: 参数不能为null");
                return new YJoinQueryableWrapper<T, TInner>(
                    Enumerable.Empty<(T, TInner)>().AsQueryable(), _cache, _logger);
            }

            // 左连接实现
            var joinResult = from outer in _queryable
                           join innerItem in ((YQueryableWrapper<TInner>)inner)._queryable
                           on outerKeySelector.Compile()(outer) equals innerKeySelector.Compile()(innerItem) into temp
                           from innerItem in temp.DefaultIfEmpty()
                           select new { Outer = outer, Inner = innerItem };

            var tupleQuery = joinResult.Select(x => ValueTuple.Create(x.Outer, x.Inner));
            return new YJoinQueryableWrapper<T, TInner>(tupleQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YLeftJoin: 左连接异常");
            return new YJoinQueryableWrapper<T, TInner>(
                Enumerable.Empty<(T, TInner)>().AsQueryable(), _cache, _logger);
        }
    }

    #endregion

    #region Y分组方法

    public IYGroupQueryable<TKey, T> YGroupBy<TKey>(Expression<Func<T, TKey>> keySelector)
    {
        try
        {
            if (keySelector == null)
            {
                _logger.LogWarning("YGroupBy: 分组键选择器不能为null");
                return new YGroupQueryableWrapper<TKey, T>(
                    Enumerable.Empty<IGrouping<TKey, T>>().AsQueryable(), _cache, _logger);
            }
            
            var groupedQuery = _queryable.GroupBy(keySelector);
            return new YGroupQueryableWrapper<TKey, T>(groupedQuery, _cache, _logger);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YGroupBy: 分组异常");
            return new YGroupQueryableWrapper<TKey, T>(
                Enumerable.Empty<IGrouping<TKey, T>>().AsQueryable(), _cache, _logger);
        }
    }

    #endregion

    #region Y执行方法

    public async Task<List<T>> YToListAsync()
    {
        try
        {
            // 检查缓存
            if (_cacheDuration.HasValue && !string.IsNullOrEmpty(_cacheKey))
            {
                if (_cache.TryGetValue(_cacheKey, out List<T>? cachedResult))
                {
                    _logger.LogDebug("YToListAsync: 从缓存获取数据, CacheKey={CacheKey}", _cacheKey);
                    return cachedResult ?? new List<T>();
                }
            }

            var result = await _queryable.ToListAsync();
            
            // 设置缓存
            if (_cacheDuration.HasValue && !string.IsNullOrEmpty(_cacheKey))
            {
                _cache.Set(_cacheKey, result, _cacheDuration.Value);
                _logger.LogDebug("YToListAsync: 数据已缓存, CacheKey={CacheKey}, Duration={Duration}", 
                    _cacheKey, _cacheDuration.Value);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YToListAsync: 转换为列表异常");
            return new List<T>();
        }
    }

    public async Task<T?> YFirstOrDefaultAsync()
    {
        try
        {
            return await _queryable.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YFirstOrDefaultAsync: 获取第一个实体异常");
            return null;
        }
    }

    public async Task<bool> YAnyAsync()
    {
        try
        {
            return await _queryable.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YAnyAsync: 检查存在异常");
            return false;
        }
    }

    public async Task<int> YCountAsync()
    {
        try
        {
            return await _queryable.CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YCountAsync: 统计数量异常");
            return 0;
        }
    }

    public async Task<YPagedResult<T>> YToPagedListAsync(int pageIndex, int pageSize)
    {
        try
        {
            if (pageIndex <= 0 || pageSize <= 0)
            {
                _logger.LogWarning("YToPagedListAsync: 页码和页大小必须大于0");
                return new YPagedResult<T> { PageIndex = pageIndex, PageSize = pageSize };
            }

            var totalCount = await _queryable.CountAsync();
            var items = await _queryable
                .Skip((pageIndex - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new YPagedResult<T>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = pageIndex,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YToPagedListAsync: 分页查询异常");
            return new YPagedResult<T> { PageIndex = pageIndex, PageSize = pageSize };
        }
    }

    #endregion

    #region Y缓存方法

    public IYQueryable<T> YCacheFor(TimeSpan duration, string? cacheKey = null)
    {
        try
        {
            var key = cacheKey ?? $"YQuery_{typeof(T).Name}_{Guid.NewGuid():N}";
            return new YQueryableWrapper<T>(_queryable, _cache, _logger)
            {
                _cacheDuration = duration,
                _cacheKey = key
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "YCacheFor: 设置缓存异常");
            return this;
        }
    }

    #endregion
}
