using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;
using Zylo.Data.Interfaces;

namespace Zylo.Data.Services;

/// <summary>
/// Y事务包装类 - 实现IYTransaction接口
/// 提供安全的事务操作，确保数据一致性
/// </summary>
internal class YTransactionWrapper : IYTransaction
{
    private readonly IDbContextTransaction _transaction;
    private readonly ILogger _logger;
    private bool _disposed = false;

    public string TransactionId { get; }
    public YTransactionState State { get; private set; }

    public YTransactionWrapper(IDbContextTransaction transaction, ILogger logger)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        TransactionId = Guid.NewGuid().ToString("N");
        State = YTransactionState.Active;
    }

    public async Task YCommitAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (State != YTransactionState.Active)
            {
                _logger.LogWarning("Y提交事务失败: 事务状态不是活跃状态, TransactionId={TransactionId}, State={State}", 
                    TransactionId, State);
                return;
            }

            await _transaction.CommitAsync(cancellationToken);
            State = YTransactionState.Committed;
            _logger.LogDebug("Y提交事务成功: TransactionId={TransactionId}", TransactionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y提交事务异常: TransactionId={TransactionId}", TransactionId);
            State = YTransactionState.RolledBack;
            try
            {
                await _transaction.RollbackAsync(cancellationToken);
            }
            catch (Exception rollbackEx)
            {
                _logger.LogError(rollbackEx, "Y事务回滚异常: TransactionId={TransactionId}", TransactionId);
            }
        }
    }

    public async Task YRollbackAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (State != YTransactionState.Active)
            {
                _logger.LogWarning("Y回滚事务失败: 事务状态不是活跃状态, TransactionId={TransactionId}, State={State}", 
                    TransactionId, State);
                return;
            }

            await _transaction.RollbackAsync(cancellationToken);
            State = YTransactionState.RolledBack;
            _logger.LogDebug("Y回滚事务成功: TransactionId={TransactionId}", TransactionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y回滚事务异常: TransactionId={TransactionId}", TransactionId);
            State = YTransactionState.RolledBack;
        }
    }

    public async Task YCreateSavepointAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                _logger.LogWarning("Y创建保存点失败: 保存点名称不能为空, TransactionId={TransactionId}", TransactionId);
                return;
            }

            if (State != YTransactionState.Active)
            {
                _logger.LogWarning("Y创建保存点失败: 事务状态不是活跃状态, TransactionId={TransactionId}, State={State}", 
                    TransactionId, State);
                return;
            }

            await _transaction.CreateSavepointAsync(name, cancellationToken);
            _logger.LogDebug("Y创建保存点成功: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y创建保存点异常: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
    }

    public async Task YRollbackToSavepointAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                _logger.LogWarning("Y回滚到保存点失败: 保存点名称不能为空, TransactionId={TransactionId}", TransactionId);
                return;
            }

            if (State != YTransactionState.Active)
            {
                _logger.LogWarning("Y回滚到保存点失败: 事务状态不是活跃状态, TransactionId={TransactionId}, State={State}", 
                    TransactionId, State);
                return;
            }

            await _transaction.RollbackToSavepointAsync(name, cancellationToken);
            _logger.LogDebug("Y回滚到保存点成功: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y回滚到保存点异常: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
    }

    public async Task YReleaseSavepointAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                _logger.LogWarning("Y释放保存点失败: 保存点名称不能为空, TransactionId={TransactionId}", TransactionId);
                return;
            }

            if (State != YTransactionState.Active)
            {
                _logger.LogWarning("Y释放保存点失败: 事务状态不是活跃状态, TransactionId={TransactionId}, State={State}", 
                    TransactionId, State);
                return;
            }

            await _transaction.ReleaseSavepointAsync(name, cancellationToken);
            _logger.LogDebug("Y释放保存点成功: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y释放保存点异常: TransactionId={TransactionId}, SavepointName={SavepointName}", 
                TransactionId, name);
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    public async ValueTask DisposeAsync()
    {
        await DisposeAsyncCore();
        Dispose(false);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    if (State == YTransactionState.Active)
                    {
                        _transaction.Rollback();
                        State = YTransactionState.RolledBack;
                        _logger.LogDebug("Y事务自动回滚: TransactionId={TransactionId}", TransactionId);
                    }
                    _transaction.Dispose();
                    State = YTransactionState.Disposed;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Y事务释放异常: TransactionId={TransactionId}", TransactionId);
                }
            }
            _disposed = true;
        }
    }

    protected virtual async ValueTask DisposeAsyncCore()
    {
        try
        {
            if (State == YTransactionState.Active)
            {
                await _transaction.RollbackAsync();
                State = YTransactionState.RolledBack;
                _logger.LogDebug("Y事务异步自动回滚: TransactionId={TransactionId}", TransactionId);
            }
            await _transaction.DisposeAsync();
            State = YTransactionState.Disposed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Y事务异步释放异常: TransactionId={TransactionId}", TransactionId);
        }
    }

    ~YTransactionWrapper()
    {
        Dispose(false);
    }
}

/// <summary>
/// Y空事务类 - 用于异常情况下的安全返回
/// </summary>
internal class YNullTransaction : IYTransaction
{
    public string TransactionId { get; } = "NULL_TRANSACTION";
    public YTransactionState State { get; } = YTransactionState.RolledBack;

    public Task YCommitAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task YRollbackAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task YCreateSavepointAsync(string name, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task YRollbackToSavepointAsync(string name, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public Task YReleaseSavepointAsync(string name, CancellationToken cancellationToken = default) => Task.CompletedTask;
    public void Dispose() { }
    public ValueTask DisposeAsync() => ValueTask.CompletedTask;
}
