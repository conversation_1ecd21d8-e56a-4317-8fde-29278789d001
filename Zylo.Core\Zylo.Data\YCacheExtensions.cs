using System.Collections.Concurrent;

namespace Zylo.Data;

/// <summary>
/// 缓存操作扩展方法
/// 按照总体升级计划，提供内存缓存、缓存策略等功能
/// </summary>
public static class YCacheExtensions
{
    private static readonly ConcurrentDictionary<string, CacheItem> _cache = new();
    private static readonly object _cleanupLock = new();
    private static DateTime _lastCleanup = DateTime.UtcNow;
    private static readonly TimeSpan _cleanupInterval = TimeSpan.FromMinutes(5);

    #region 缓存项定义

    /// <summary>
    /// 缓存项
    /// </summary>
    private class CacheItem
    {
        public object Value { get; set; } = null!;
        public DateTime ExpiryTime { get; set; }
        public TimeSpan? SlidingExpiration { get; set; }
        public DateTime LastAccessed { get; set; }

        public bool IsExpired => DateTime.UtcNow > ExpiryTime;

        public void UpdateLastAccessed()
        {
            LastAccessed = DateTime.UtcNow;
            if (SlidingExpiration.HasValue)
            {
                ExpiryTime = DateTime.UtcNow.Add(SlidingExpiration.Value);
            }
        }
    }

    #endregion

    #region 基本缓存操作

    /// <summary>
    /// 设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiration">过期时间</param>
    /// <param name="slidingExpiration">滑动过期时间</param>
    /// <returns>是否设置成功</returns>
    /// <example>
    /// <code>
    /// var success = "user:123".YSetCache("John Doe", TimeSpan.FromMinutes(30));
    /// Console.WriteLine($"缓存设置成功: {success}");
    /// </code>
    /// </example>
    public static bool YSetCache<T>(this string key, T value, TimeSpan? expiration = null, TimeSpan? slidingExpiration = null)
    {
        if (string.IsNullOrWhiteSpace(key) || value == null)
            return false;

        try
        {
            var expiryTime = DateTime.UtcNow.Add(expiration ?? TimeSpan.FromHours(1));
            
            var cacheItem = new CacheItem
            {
                Value = value,
                ExpiryTime = expiryTime,
                SlidingExpiration = slidingExpiration,
                LastAccessed = DateTime.UtcNow
            };

            _cache.AddOrUpdate(key, cacheItem, (k, v) => cacheItem);
            
            // 定期清理过期项
            TryCleanupExpiredItems();
            
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>缓存值或默认值</returns>
    /// <example>
    /// <code>
    /// var user = "user:123".YGetCache&lt;string&gt;("Unknown");
    /// Console.WriteLine($"用户: {user}");
    /// </code>
    /// </example>
    public static T YGetCache<T>(this string key, T defaultValue = default!)
    {
        if (string.IsNullOrWhiteSpace(key))
            return defaultValue;

        try
        {
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    return defaultValue;
                }

                cacheItem.UpdateLastAccessed();
                
                if (cacheItem.Value is T typedValue)
                    return typedValue;
            }

            return defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 检查缓存是否存在且未过期
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否存在</returns>
    /// <example>
    /// <code>
    /// if ("user:123".YHasCache())
    /// {
    ///     Console.WriteLine("缓存存在");
    /// }
    /// </code>
    /// </example>
    public static bool YHasCache(this string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            return false;

        try
        {
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (cacheItem.IsExpired)
                {
                    _cache.TryRemove(key, out _);
                    return false;
                }
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 移除缓存项
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否移除成功</returns>
    /// <example>
    /// <code>
    /// var removed = "user:123".YRemoveCache();
    /// Console.WriteLine($"缓存移除成功: {removed}");
    /// </code>
    /// </example>
    public static bool YRemoveCache(this string key)
    {
        if (string.IsNullOrWhiteSpace(key))
            return false;

        try
        {
            return _cache.TryRemove(key, out _);
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 高级缓存操作

    /// <summary>
    /// 获取或设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">值工厂函数</param>
    /// <param name="expiration">过期时间</param>
    /// <param name="slidingExpiration">滑动过期时间</param>
    /// <returns>缓存值</returns>
    /// <example>
    /// <code>
    /// var user = "user:123".YGetOrSetCache(() => LoadUserFromDatabase(123), TimeSpan.FromMinutes(30));
    /// Console.WriteLine($"用户: {user}");
    /// </code>
    /// </example>
    public static T YGetOrSetCache<T>(this string key, Func<T> factory, TimeSpan? expiration = null, TimeSpan? slidingExpiration = null)
    {
        if (string.IsNullOrWhiteSpace(key) || factory == null)
            return default!;

        try
        {
            // 先尝试获取缓存
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (!cacheItem.IsExpired)
                {
                    cacheItem.UpdateLastAccessed();
                    if (cacheItem.Value is T typedValue)
                        return typedValue;
                }
                else
                {
                    _cache.TryRemove(key, out _);
                }
            }

            // 缓存不存在或已过期，创建新值
            var newValue = factory();
            if (newValue != null)
            {
                key.YSetCache(newValue, expiration, slidingExpiration);
            }
            
            return newValue;
        }
        catch
        {
            return default!;
        }
    }

    /// <summary>
    /// 异步获取或设置缓存值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">异步值工厂函数</param>
    /// <param name="expiration">过期时间</param>
    /// <param name="slidingExpiration">滑动过期时间</param>
    /// <returns>缓存值</returns>
    /// <example>
    /// <code>
    /// var user = await "user:123".YGetOrSetCacheAsync(async () => await LoadUserFromDatabaseAsync(123));
    /// Console.WriteLine($"用户: {user}");
    /// </code>
    /// </example>
    public static async Task<T> YGetOrSetCacheAsync<T>(this string key, Func<Task<T>> factory, TimeSpan? expiration = null, TimeSpan? slidingExpiration = null)
    {
        if (string.IsNullOrWhiteSpace(key) || factory == null)
            return default!;

        try
        {
            // 先尝试获取缓存
            if (_cache.TryGetValue(key, out var cacheItem))
            {
                if (!cacheItem.IsExpired)
                {
                    cacheItem.UpdateLastAccessed();
                    if (cacheItem.Value is T typedValue)
                        return typedValue;
                }
                else
                {
                    _cache.TryRemove(key, out _);
                }
            }

            // 缓存不存在或已过期，创建新值
            var newValue = await factory();
            if (newValue != null)
            {
                key.YSetCache(newValue, expiration, slidingExpiration);
            }
            
            return newValue;
        }
        catch
        {
            return default!;
        }
    }

    #endregion

    #region 缓存管理

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    /// <example>
    /// <code>
    /// YCacheExtensions.YClearAllCache();
    /// Console.WriteLine("所有缓存已清空");
    /// </code>
    /// </example>
    public static void YClearAllCache()
    {
        try
        {
            _cache.Clear();
        }
        catch
        {
            // 忽略清理错误
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计信息</returns>
    /// <example>
    /// <code>
    /// var stats = YCacheExtensions.YGetCacheStats();
    /// Console.WriteLine($"缓存项数量: {stats.Count}, 过期项数量: {stats.ExpiredCount}");
    /// </code>
    /// </example>
    public static (int Count, int ExpiredCount) YGetCacheStats()
    {
        try
        {
            var now = DateTime.UtcNow;
            var expiredCount = _cache.Values.Count(item => item.IsExpired);
            return (_cache.Count, expiredCount);
        }
        catch
        {
            return (0, 0);
        }
    }

    /// <summary>
    /// 清理过期的缓存项
    /// </summary>
    /// <returns>清理的项数</returns>
    /// <example>
    /// <code>
    /// var cleaned = YCacheExtensions.YCleanupExpiredCache();
    /// Console.WriteLine($"清理了 {cleaned} 个过期缓存项");
    /// </code>
    /// </example>
    public static int YCleanupExpiredCache()
    {
        try
        {
            var expiredKeys = _cache
                .Where(kvp => kvp.Value.IsExpired)
                .Select(kvp => kvp.Key)
                .ToList();

            var cleanedCount = 0;
            foreach (var key in expiredKeys)
            {
                if (_cache.TryRemove(key, out _))
                    cleanedCount++;
            }

            return cleanedCount;
        }
        catch
        {
            return 0;
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 尝试清理过期项（定期执行）
    /// </summary>
    private static void TryCleanupExpiredItems()
    {
        if (DateTime.UtcNow - _lastCleanup < _cleanupInterval)
            return;

        lock (_cleanupLock)
        {
            if (DateTime.UtcNow - _lastCleanup < _cleanupInterval)
                return;

            Task.Run(() =>
            {
                try
                {
                    YCleanupExpiredCache();
                    _lastCleanup = DateTime.UtcNow;
                }
                catch
                {
                    // 忽略清理错误
                }
            });
        }
    }

    #endregion
}
