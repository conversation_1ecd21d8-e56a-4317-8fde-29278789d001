namespace Zylo.Data;

/// <summary>
/// 配置管理扩展方法
/// 按照总体升级计划，提供配置文件读写、环境变量处理等功能
/// </summary>
public static class YConfigurationExtensions
{
    #region INI文件操作

    /// <summary>
    /// 读取INI文件的值
    /// </summary>
    /// <param name="filePath">INI文件路径</param>
    /// <param name="section">节名</param>
    /// <param name="key">键名</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    /// <example>
    /// <code>
    /// var value = "config.ini".YReadIniValue("Database", "ConnectionString", "");
    /// Console.WriteLine($"连接字符串: {value}");
    /// </code>
    /// </example>
    public static string YReadIniValue(this string filePath, string section, string key, string defaultValue = "")
    {
        if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
            return defaultValue;

        if (string.IsNullOrWhiteSpace(section) || string.IsNullOrWhiteSpace(key))
            return defaultValue;

        try
        {
            var lines = File.ReadAllLines(filePath);
            var currentSection = "";
            var inTargetSection = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                // 跳过空行和注释
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                    continue;

                // 检查是否是节标题
                if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    currentSection = trimmedLine[1..^1].Trim();
                    inTargetSection = string.Equals(currentSection, section, StringComparison.OrdinalIgnoreCase);
                    continue;
                }

                // 如果在目标节中，查找键值对
                if (inTargetSection && trimmedLine.Contains('='))
                {
                    var parts = trimmedLine.Split('=', 2);
                    if (parts.Length == 2)
                    {
                        var lineKey = parts[0].Trim();
                        var lineValue = parts[1].Trim();
                        
                        if (string.Equals(lineKey, key, StringComparison.OrdinalIgnoreCase))
                        {
                            return lineValue;
                        }
                    }
                }
            }

            return defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 写入INI文件的值
    /// </summary>
    /// <param name="filePath">INI文件路径</param>
    /// <param name="section">节名</param>
    /// <param name="key">键名</param>
    /// <param name="value">值</param>
    /// <returns>是否写入成功</returns>
    /// <example>
    /// <code>
    /// var success = "config.ini".YWriteIniValue("Database", "ConnectionString", "Server=localhost;");
    /// Console.WriteLine($"写入成功: {success}");
    /// </code>
    /// </example>
    public static bool YWriteIniValue(this string filePath, string section, string key, string value)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            return false;

        if (string.IsNullOrWhiteSpace(section) || string.IsNullOrWhiteSpace(key))
            return false;

        try
        {
            var lines = new List<string>();
            var fileExists = File.Exists(filePath);
            
            if (fileExists)
            {
                lines.AddRange(File.ReadAllLines(filePath));
            }

            var sectionFound = false;
            var keyFound = false;
            var sectionIndex = -1;

            // 查找节和键
            for (int i = 0; i < lines.Count; i++)
            {
                var trimmedLine = lines[i].Trim();
                
                // 检查是否是节标题
                if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    var currentSection = trimmedLine[1..^1].Trim();
                    if (string.Equals(currentSection, section, StringComparison.OrdinalIgnoreCase))
                    {
                        sectionFound = true;
                        sectionIndex = i;
                    }
                    else if (sectionFound && !keyFound)
                    {
                        // 到了下一个节，插入新键值对
                        lines.Insert(i, $"{key}={value}");
                        keyFound = true;
                        break;
                    }
                }
                // 如果在目标节中，查找键值对
                else if (sectionFound && trimmedLine.Contains('='))
                {
                    var parts = trimmedLine.Split('=', 2);
                    if (parts.Length == 2)
                    {
                        var lineKey = parts[0].Trim();
                        if (string.Equals(lineKey, key, StringComparison.OrdinalIgnoreCase))
                        {
                            lines[i] = $"{key}={value}";
                            keyFound = true;
                            break;
                        }
                    }
                }
            }

            // 如果没有找到节，添加新节
            if (!sectionFound)
            {
                if (lines.Count > 0 && !string.IsNullOrEmpty(lines[^1]))
                {
                    lines.Add("");
                }
                lines.Add($"[{section}]");
                lines.Add($"{key}={value}");
            }
            // 如果找到节但没有找到键，在节的末尾添加
            else if (!keyFound)
            {
                // 在节的末尾添加键值对
                var insertIndex = sectionIndex + 1;
                while (insertIndex < lines.Count)
                {
                    var line = lines[insertIndex].Trim();
                    if (line.StartsWith("[") && line.EndsWith("]"))
                        break;
                    insertIndex++;
                }
                lines.Insert(insertIndex, $"{key}={value}");
            }

            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllLines(filePath, lines);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 读取INI文件的所有键值对
    /// </summary>
    /// <param name="filePath">INI文件路径</param>
    /// <param name="section">节名</param>
    /// <returns>键值对字典</returns>
    /// <example>
    /// <code>
    /// var config = "config.ini".YReadIniSection("Database");
    /// foreach (var kvp in config)
    /// {
    ///     Console.WriteLine($"{kvp.Key} = {kvp.Value}");
    /// }
    /// </code>
    /// </example>
    public static Dictionary<string, string> YReadIniSection(this string filePath, string section)
    {
        var result = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
            return result;

        if (string.IsNullOrWhiteSpace(section))
            return result;

        try
        {
            var lines = File.ReadAllLines(filePath);
            var inTargetSection = false;

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();
                
                // 跳过空行和注释
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                    continue;

                // 检查是否是节标题
                if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    var currentSection = trimmedLine[1..^1].Trim();
                    inTargetSection = string.Equals(currentSection, section, StringComparison.OrdinalIgnoreCase);
                    continue;
                }

                // 如果在目标节中，读取键值对
                if (inTargetSection && trimmedLine.Contains('='))
                {
                    var parts = trimmedLine.Split('=', 2);
                    if (parts.Length == 2)
                    {
                        var key = parts[0].Trim();
                        var value = parts[1].Trim();
                        result[key] = value;
                    }
                }
                // 如果遇到下一个节，停止读取
                else if (inTargetSection && trimmedLine.StartsWith("["))
                {
                    break;
                }
            }

            return result;
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 环境变量操作

    /// <summary>
    /// 安全获取环境变量值
    /// </summary>
    /// <param name="variableName">环境变量名</param>
    /// <param name="defaultValue">默认值</param>
    /// <param name="target">环境变量目标</param>
    /// <returns>环境变量值</returns>
    /// <example>
    /// <code>
    /// var path = "PATH".YGetEnvironmentVariable("");
    /// Console.WriteLine($"PATH环境变量: {path}");
    /// </code>
    /// </example>
    public static string YGetEnvironmentVariable(this string variableName, string defaultValue = "", EnvironmentVariableTarget target = EnvironmentVariableTarget.Process)
    {
        if (string.IsNullOrWhiteSpace(variableName))
            return defaultValue;

        try
        {
            return Environment.GetEnvironmentVariable(variableName, target) ?? defaultValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 安全设置环境变量值
    /// </summary>
    /// <param name="variableName">环境变量名</param>
    /// <param name="value">值</param>
    /// <param name="target">环境变量目标</param>
    /// <returns>是否设置成功</returns>
    /// <example>
    /// <code>
    /// var success = "MY_APP_CONFIG".YSetEnvironmentVariable("production");
    /// Console.WriteLine($"设置成功: {success}");
    /// </code>
    /// </example>
    public static bool YSetEnvironmentVariable(this string variableName, string? value, EnvironmentVariableTarget target = EnvironmentVariableTarget.Process)
    {
        if (string.IsNullOrWhiteSpace(variableName))
            return false;

        try
        {
            Environment.SetEnvironmentVariable(variableName, value, target);
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 配置值转换

    /// <summary>
    /// 将配置值转换为指定类型
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="configValue">配置值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的值</returns>
    /// <example>
    /// <code>
    /// var port = "8080".YConvertConfigValue&lt;int&gt;(80);
    /// Console.WriteLine($"端口: {port}");
    /// </code>
    /// </example>
    public static T YConvertConfigValue<T>(this string? configValue, T defaultValue = default!)
    {
        if (string.IsNullOrWhiteSpace(configValue))
            return defaultValue;

        try
        {
            // 处理布尔值的特殊情况
            if (typeof(T) == typeof(bool))
            {
                var lowerValue = configValue.ToLowerInvariant();
                var boolResult = lowerValue is "true" or "yes" or "1" or "on" or "enabled";
                return (T)(object)boolResult;
            }

            return (T)Convert.ChangeType(configValue, typeof(T));
        }
        catch
        {
            return defaultValue;
        }
    }

    #endregion
}
