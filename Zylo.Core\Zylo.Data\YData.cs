using Zylo.Core.DI.Abstractions;
using Zylo.Core.DI.Factory;
using Zylo.Data.DI;
using Zylo.Data.Interfaces;

namespace Zylo.Data;

/// <summary>
/// YData 静态 API - 提供简化的全局数据访问接口
/// 基于 Zylo.Core 的双容器支持，自动选择最佳容器
/// </summary>
public static class YData
{
    private static IZyloContainer? _container;
    private static readonly object _lock = new();

    /// <summary>
    /// 是否已初始化
    /// </summary>
    public static bool IsInitialized => _container != null;

    /// <summary>
    /// 使用自动选择的容器初始化 YData
    /// </summary>
    /// <param name="configure">配置选项</param>
    public static void UseAuto(Action<ZyloDataOptions>? configure = null)
    {
        Use(ZyloContainerType.Auto, configure);
    }

    /// <summary>
    /// 使用 DryIoc 容器初始化 YData
    /// </summary>
    /// <param name="configure">配置选项</param>
    public static void UseDryIoc(Action<ZyloDataOptions>? configure = null)
    {
        Use(ZyloContainerType.DryIoc, configure);
    }

    /// <summary>
    /// 使用 Microsoft.Extensions.DI 容器初始化 YData
    /// </summary>
    /// <param name="configure">配置选项</param>
    public static void UseMicrosoftDI(Action<ZyloDataOptions>? configure = null)
    {
        Use(ZyloContainerType.MicrosoftDI, configure);
    }

    /// <summary>
    /// 使用指定容器类型初始化 YData
    /// </summary>
    /// <param name="containerType">容器类型</param>
    /// <param name="configure">配置选项</param>
    public static void Use(ZyloContainerType containerType, Action<ZyloDataOptions>? configure = null)
    {
        lock (_lock)
        {
            _container?.Dispose();
            _container = ZyloDataContainerExtensions.CreateZyloDataContainer(containerType, configure);
        }
    }

    /// <summary>
    /// 使用现有容器初始化 YData
    /// </summary>
    /// <param name="container">现有容器</param>
    /// <param name="configure">配置选项</param>
    public static void UseContainer(IZyloContainer container, Action<ZyloDataOptions>? configure = null)
    {
        lock (_lock)
        {
            _container?.Dispose();
            container.AddZyloData(configure);
            _container = container;
        }
    }

    /// <summary>
    /// 重置 YData，释放所有资源
    /// </summary>
    public static void Reset()
    {
        lock (_lock)
        {
            _container?.Dispose();
            _container = null;
        }
    }

    /// <summary>
    /// 获取版本信息
    /// </summary>
    /// <returns>版本字符串</returns>
    public static string GetVersion()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetVersion();
    }

    /// <summary>
    /// 验证设置是否正确
    /// </summary>
    /// <returns>是否有效</returns>
    public static bool ValidateSetup()
    {
        if (!IsInitialized) return false;
        
        var result = _container!.ValidateZyloData();
        return result.IsValid;
    }

    /// <summary>
    /// 获取服务状态
    /// </summary>
    /// <returns>状态信息</returns>
    public static ZyloDataStatus GetStatus()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetStatus();
    }

    /// <summary>
    /// 获取已启用的功能列表
    /// </summary>
    /// <returns>功能名称列表</returns>
    public static IEnumerable<string> GetEnabledFeatures()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetEnabledFeatures();
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    /// <returns>健康检查结果</returns>
    public static ZyloDataHealthCheck PerformHealthCheck()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.PerformHealthCheck();
    }

    /// <summary>
    /// 获取数据访问统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public static ZyloDataStatistics GetStatistics()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetStatistics();
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    public static ZyloDataPerformanceMetrics GetPerformanceMetrics()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetPerformanceMetrics();
    }

    /// <summary>
    /// 验证数据库连接
    /// </summary>
    /// <returns>连接验证结果</returns>
    public static async Task<(bool IsValid, string Message)> ValidateDatabaseConnectionAsync()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return await service.ValidateDatabaseConnectionAsync();
    }

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    /// <returns>数据库信息</returns>
    public static async Task<ZyloDatabaseInfo> GetDatabaseInfoAsync()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return await service.GetDatabaseInfoAsync();
    }

    /// <summary>
    /// 清理缓存
    /// </summary>
    /// <returns>清理结果</returns>
    public static async Task<bool> ClearCacheAsync()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return await service.ClearCacheAsync();
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计</returns>
    public static ZyloCacheStatistics GetCacheStatistics()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.GetCacheStatistics();
    }

    /// <summary>
    /// 检查指定功能是否已启用
    /// </summary>
    /// <param name="featureName">功能名称</param>
    /// <returns>是否已启用</returns>
    public static bool IsFeatureEnabled(string featureName)
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        return service.IsFeatureEnabled(featureName);
    }

    /// <summary>
    /// 重置统计信息
    /// </summary>
    public static void ResetStatistics()
    {
        EnsureInitialized();
        var service = _container!.Resolve<IZyloDataService>();
        service.ResetStatistics();
    }

    /// <summary>
    /// 获取数据库服务
    /// </summary>
    /// <returns>数据库服务实例</returns>
    public static IYDatabase GetDatabase()
    {
        EnsureInitialized();
        return _container!.Resolve<IYDatabase>();
    }

    /// <summary>
    /// 获取缓存服务
    /// </summary>
    /// <returns>缓存服务实例</returns>
    public static IYCache GetCache()
    {
        EnsureInitialized();
        return _container!.Resolve<IYCache>();
    }

    /// <summary>
    /// 获取配置服务
    /// </summary>
    /// <returns>配置服务实例</returns>
    public static IYConfiguration GetConfiguration()
    {
        EnsureInitialized();
        return _container!.Resolve<IYConfiguration>();
    }

    /// <summary>
    /// 获取映射服务
    /// </summary>
    /// <returns>映射服务实例</returns>
    public static IYMapping GetMapping()
    {
        EnsureInitialized();
        return _container!.Resolve<IYMapping>();
    }

    /// <summary>
    /// 获取底层容器实例
    /// </summary>
    /// <returns>容器实例</returns>
    public static IZyloContainer GetContainer()
    {
        EnsureInitialized();
        return _container!;
    }

    /// <summary>
    /// 确保已初始化
    /// </summary>
    /// <exception cref="InvalidOperationException">未初始化时抛出</exception>
    private static void EnsureInitialized()
    {
        if (!IsInitialized)
        {
            throw new InvalidOperationException(
                "YData 尚未初始化。请先调用 YData.UseAuto()、YData.UseDryIoc() 或 YData.UseMicrosoftDI() 方法。");
        }
    }
}
