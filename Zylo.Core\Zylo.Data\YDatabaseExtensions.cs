using System.Data.Common;

namespace Zylo.Data;

/// <summary>
/// 数据库操作扩展方法
/// 按照总体升级计划，提供数据库连接、查询、执行等功能
/// </summary>
public static class YDatabaseExtensions
{
    #region 连接字符串工具

    /// <summary>
    /// 构建SQL Server连接字符串
    /// </summary>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="userId">用户ID</param>
    /// <param name="password">密码</param>
    /// <param name="integratedSecurity">是否使用集成安全验证，默认为false</param>
    /// <param name="timeout">连接超时时间（秒），默认为30</param>
    /// <returns>连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YDatabaseExtensions.YBuildSqlServerConnectionString("localhost", "MyDB", "sa", "password");
    /// Console.WriteLine($"连接字符串: {connStr}");
    /// </code>
    /// </example>
    public static string YBuildSqlServerConnectionString(
        string server, 
        string database, 
        string? userId = null, 
        string? password = null,
        bool integratedSecurity = false,
        int timeout = 30)
    {
        if (string.IsNullOrWhiteSpace(server))
            throw new ArgumentException("服务器地址不能为空", nameof(server));
        
        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));

        var builder = new System.Text.StringBuilder();
        builder.Append($"Server={server};");
        builder.Append($"Database={database};");
        builder.Append($"Connection Timeout={timeout};");

        if (integratedSecurity)
        {
            builder.Append("Integrated Security=true;");
        }
        else
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("用户ID不能为空", nameof(userId));
            
            builder.Append($"User Id={userId};");
            builder.Append($"Password={password ?? string.Empty};");
        }

        return builder.ToString();
    }

    /// <summary>
    /// 构建MySQL连接字符串
    /// </summary>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="userId">用户ID</param>
    /// <param name="password">密码</param>
    /// <param name="port">端口号，默认为3306</param>
    /// <param name="charset">字符集，默认为utf8mb4</param>
    /// <returns>连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YDatabaseExtensions.YBuildMySqlConnectionString("localhost", "mydb", "root", "password");
    /// Console.WriteLine($"MySQL连接字符串: {connStr}");
    /// </code>
    /// </example>
    public static string YBuildMySqlConnectionString(
        string server,
        string database,
        string userId,
        string password,
        int port = 3306,
        string charset = "utf8mb4")
    {
        if (string.IsNullOrWhiteSpace(server))
            throw new ArgumentException("服务器地址不能为空", nameof(server));
        
        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));
        
        if (string.IsNullOrWhiteSpace(userId))
            throw new ArgumentException("用户ID不能为空", nameof(userId));

        return $"Server={server};Port={port};Database={database};Uid={userId};Pwd={password};Charset={charset};";
    }

    /// <summary>
    /// 构建SQLite连接字符串
    /// </summary>
    /// <param name="dataSource">数据库文件路径</param>
    /// <param name="password">密码（可选）</param>
    /// <param name="version">版本，默认为3</param>
    /// <returns>连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YDatabaseExtensions.YBuildSQLiteConnectionString("data.db");
    /// Console.WriteLine($"SQLite连接字符串: {connStr}");
    /// </code>
    /// </example>
    public static string YBuildSQLiteConnectionString(string dataSource, string? password = null, int version = 3)
    {
        if (string.IsNullOrWhiteSpace(dataSource))
            throw new ArgumentException("数据源路径不能为空", nameof(dataSource));

        var builder = new System.Text.StringBuilder();
        builder.Append($"Data Source={dataSource};");
        builder.Append($"Version={version};");
        
        if (!string.IsNullOrWhiteSpace(password))
        {
            builder.Append($"Password={password};");
        }

        return builder.ToString();
    }

    #endregion

    #region 连接测试

    /// <summary>
    /// 测试数据库连接是否有效
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <param name="providerName">数据库提供程序名称</param>
    /// <param name="timeout">超时时间（毫秒），默认为5000</param>
    /// <returns>连接是否成功</returns>
    /// <example>
    /// <code>
    /// var connStr = "Server=localhost;Database=test;Integrated Security=true;";
    /// var isValid = await connStr.YTestConnectionAsync("System.Data.SqlClient");
    /// Console.WriteLine($"连接有效: {isValid}");
    /// </code>
    /// </example>
    public static async Task<bool> YTestConnectionAsync(
        this string connectionString, 
        string providerName, 
        int timeout = 5000)
    {
        if (string.IsNullOrWhiteSpace(connectionString))
            return false;

        if (string.IsNullOrWhiteSpace(providerName))
            return false;

        try
        {
            var factory = DbProviderFactories.GetFactory(providerName);
            using var connection = factory.CreateConnection();
            
            if (connection == null)
                return false;

            connection.ConnectionString = connectionString;
            
            using var cts = new CancellationTokenSource(timeout);
            await connection.OpenAsync(cts.Token);
            
            return connection.State == ConnectionState.Open;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 同步测试数据库连接
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <param name="providerName">数据库提供程序名称</param>
    /// <param name="timeout">超时时间（毫秒），默认为5000</param>
    /// <returns>连接是否成功</returns>
    /// <example>
    /// <code>
    /// var connStr = "Server=localhost;Database=test;Integrated Security=true;";
    /// var isValid = connStr.YTestConnection("System.Data.SqlClient");
    /// Console.WriteLine($"连接有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YTestConnection(this string connectionString, string providerName, int timeout = 5000)
    {
        if (string.IsNullOrWhiteSpace(connectionString))
            return false;

        if (string.IsNullOrWhiteSpace(providerName))
            return false;

        try
        {
            var factory = DbProviderFactories.GetFactory(providerName);
            using var connection = factory.CreateConnection();
            
            if (connection == null)
                return false;

            connection.ConnectionString = connectionString;
            connection.Open();
            
            return connection.State == ConnectionState.Open;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 查询辅助

    /// <summary>
    /// 安全地转换数据库值
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="value">数据库值</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>转换后的值</returns>
    /// <example>
    /// <code>
    /// var dbValue = reader["Age"];
    /// var age = dbValue.YConvertDbValue&lt;int&gt;(0);
    /// Console.WriteLine($"年龄: {age}");
    /// </code>
    /// </example>
    public static T YConvertDbValue<T>(this object? value, T defaultValue = default!)
    {
        if (value == null || value == DBNull.Value)
            return defaultValue;

        try
        {
            if (value is T directValue)
                return directValue;

            return (T)Convert.ChangeType(value, typeof(T));
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// 检查数据库值是否为空
    /// </summary>
    /// <param name="value">数据库值</param>
    /// <returns>是否为空</returns>
    /// <example>
    /// <code>
    /// var dbValue = reader["Description"];
    /// if (!dbValue.YIsDbNull())
    /// {
    ///     var description = dbValue.ToString();
    ///     Console.WriteLine($"描述: {description}");
    /// }
    /// </code>
    /// </example>
    public static bool YIsDbNull(this object? value)
    {
        return value == null || value == DBNull.Value;
    }

    /// <summary>
    /// 将值转换为数据库参数值
    /// </summary>
    /// <param name="value">原始值</param>
    /// <returns>数据库参数值</returns>
    /// <example>
    /// <code>
    /// string? name = null;
    /// var paramValue = name.YToDbValue();
    /// // paramValue 将是 DBNull.Value
    /// </code>
    /// </example>
    public static object YToDbValue(this object? value)
    {
        return value ?? DBNull.Value;
    }

    #endregion

    #region SQL构建辅助

    /// <summary>
    /// 安全地转义SQL字符串值
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>转义后的字符串</returns>
    /// <example>
    /// <code>
    /// var userInput = "O'Connor";
    /// var escaped = userInput.YEscapeSqlString();
    /// Console.WriteLine($"转义后: {escaped}"); // O''Connor
    /// </code>
    /// </example>
    public static string YEscapeSqlString(this string? value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        return value.Replace("'", "''");
    }

    /// <summary>
    /// 构建IN子句的参数
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="values">值列表</param>
    /// <returns>IN子句字符串</returns>
    /// <example>
    /// <code>
    /// var ids = new[] { 1, 2, 3, 4, 5 };
    /// var inClause = ids.YBuildInClause();
    /// Console.WriteLine($"IN子句: {inClause}"); // (1,2,3,4,5)
    /// </code>
    /// </example>
    public static string YBuildInClause<T>(this IEnumerable<T> values)
    {
        if (values == null)
            return "()";

        var valueList = values.ToList();
        if (valueList.Count == 0)
            return "()";

        var stringValues = valueList.Select(v => v?.ToString() ?? "NULL");
        return $"({string.Join(",", stringValues)})";
    }

    #endregion
}
