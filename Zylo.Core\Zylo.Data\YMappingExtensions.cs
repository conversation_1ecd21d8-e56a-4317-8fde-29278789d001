using System.Reflection;

namespace Zylo.Data;

/// <summary>
/// 对象映射扩展方法
/// 按照总体升级计划，提供对象映射、属性复制等功能
/// </summary>
public static class YMappingExtensions
{
    #region 基本对象映射

    /// <summary>
    /// 将源对象映射到目标类型
    /// </summary>
    /// <typeparam name="TTarget">目标类型</typeparam>
    /// <param name="source">源对象</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为true</param>
    /// <returns>映射后的目标对象</returns>
    /// <example>
    /// <code>
    /// var source = new { Name = "张三", Age = 25 };
    /// var target = source.YMapTo&lt;Person&gt;();
    /// Console.WriteLine($"映射结果: {target.Name}, {target.Age}");
    /// </code>
    /// </example>
    public static TTarget? YMapTo<TTarget>(this object? source, bool ignoreCase = true) where TTarget : new()
    {
        if (source == null)
            return default;

        try
        {
            var target = new TTarget();
            source.YMapTo(target, ignoreCase);
            return target;
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// 将源对象映射到已存在的目标对象
    /// </summary>
    /// <typeparam name="TTarget">目标类型</typeparam>
    /// <param name="source">源对象</param>
    /// <param name="target">目标对象</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为true</param>
    /// <returns>是否映射成功</returns>
    /// <example>
    /// <code>
    /// var source = new { Name = "张三", Age = 25 };
    /// var target = new Person();
    /// var success = source.YMapTo(target);
    /// Console.WriteLine($"映射成功: {success}");
    /// </code>
    /// </example>
    public static bool YMapTo<TTarget>(this object? source, TTarget target, bool ignoreCase = true)
    {
        if (source == null || target == null)
            return false;

        try
        {
            var sourceType = source.GetType();
            var targetType = typeof(TTarget);

            var sourceProperties = sourceType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead)
                .ToArray();

            var targetProperties = targetType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanWrite)
                .ToArray();

            var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

            foreach (var sourceProperty in sourceProperties)
            {
                var targetProperty = targetProperties.FirstOrDefault(p => 
                    string.Equals(p.Name, sourceProperty.Name, comparison));

                if (targetProperty != null)
                {
                    var sourceValue = sourceProperty.GetValue(source);
                    if (sourceValue != null)
                    {
                        var convertedValue = ConvertValue(sourceValue, targetProperty.PropertyType);
                        if (convertedValue != null)
                        {
                            targetProperty.SetValue(target, convertedValue);
                        }
                    }
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 属性复制

    /// <summary>
    /// 复制对象的所有属性到新对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="source">源对象</param>
    /// <returns>复制的新对象</returns>
    /// <example>
    /// <code>
    /// var original = new Person { Name = "张三", Age = 25 };
    /// var copy = original.YClone();
    /// Console.WriteLine($"复制对象: {copy?.Name}");
    /// </code>
    /// </example>
    public static T? YClone<T>(this T? source) where T : new()
    {
        if (source == null)
            return default;

        try
        {
            var target = new T();
            source.YCopyPropertiesTo(target);
            return target;
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// 将源对象的属性复制到目标对象
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="source">源对象</param>
    /// <param name="target">目标对象</param>
    /// <param name="excludeProperties">要排除的属性名列表</param>
    /// <returns>是否复制成功</returns>
    /// <example>
    /// <code>
    /// var source = new Person { Name = "张三", Age = 25, Email = "<EMAIL>" };
    /// var target = new Person();
    /// var success = source.YCopyPropertiesTo(target, "Email");
    /// Console.WriteLine($"复制成功: {success}");
    /// </code>
    /// </example>
    public static bool YCopyPropertiesTo<T>(this T? source, T target, params string[] excludeProperties)
    {
        if (source == null || target == null)
            return false;

        try
        {
            var type = typeof(T);
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite)
                .ToArray();

            var excludeSet = new HashSet<string>(excludeProperties, StringComparer.OrdinalIgnoreCase);

            foreach (var property in properties)
            {
                if (excludeSet.Contains(property.Name))
                    continue;

                var value = property.GetValue(source);
                property.SetValue(target, value);
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 字典映射

    /// <summary>
    /// 将字典映射到对象
    /// </summary>
    /// <typeparam name="T">目标类型</typeparam>
    /// <param name="dictionary">字典</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为true</param>
    /// <returns>映射后的对象</returns>
    /// <example>
    /// <code>
    /// var dict = new Dictionary&lt;string, object&gt;
    /// {
    ///     ["Name"] = "张三",
    ///     ["Age"] = 25
    /// };
    /// var person = dict.YMapToObject&lt;Person&gt;();
    /// Console.WriteLine($"姓名: {person?.Name}, 年龄: {person?.Age}");
    /// </code>
    /// </example>
    public static T? YMapToObject<T>(this IDictionary<string, object>? dictionary, bool ignoreCase = true) where T : new()
    {
        if (dictionary == null || dictionary.Count == 0)
            return default;

        try
        {
            var target = new T();
            var type = typeof(T);
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanWrite)
                .ToArray();

            var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

            foreach (var kvp in dictionary)
            {
                var property = properties.FirstOrDefault(p => 
                    string.Equals(p.Name, kvp.Key, comparison));

                if (property != null && kvp.Value != null)
                {
                    var convertedValue = ConvertValue(kvp.Value, property.PropertyType);
                    property.SetValue(target, convertedValue);
                }
            }

            return target;
        }
        catch
        {
            return default;
        }
    }

    /// <summary>
    /// 将对象映射到字典
    /// </summary>
    /// <param name="obj">源对象</param>
    /// <param name="includeNullValues">是否包含空值，默认为false</param>
    /// <returns>字典</returns>
    /// <example>
    /// <code>
    /// var person = new Person { Name = "张三", Age = 25 };
    /// var dict = person.YMapToDictionary();
    /// foreach (var kvp in dict)
    /// {
    ///     Console.WriteLine($"{kvp.Key}: {kvp.Value}");
    /// }
    /// </code>
    /// </example>
    public static Dictionary<string, object?> YMapToDictionary(this object? obj, bool includeNullValues = false)
    {
        var result = new Dictionary<string, object?>();

        if (obj == null)
            return result;

        try
        {
            var type = obj.GetType();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead)
                .ToArray();

            foreach (var property in properties)
            {
                var value = property.GetValue(obj);
                if (includeNullValues || value != null)
                {
                    result[property.Name] = value;
                }
            }

            return result;
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 集合映射

    /// <summary>
    /// 映射集合中的每个元素到目标类型
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TTarget">目标类型</typeparam>
    /// <param name="source">源集合</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为true</param>
    /// <returns>映射后的集合</returns>
    /// <example>
    /// <code>
    /// var sourceList = new[]
    /// {
    ///     new { Name = "张三", Age = 25 },
    ///     new { Name = "李四", Age = 30 }
    /// };
    /// var targetList = sourceList.YMapToList&lt;Person&gt;();
    /// Console.WriteLine($"映射了 {targetList.Count} 个对象");
    /// </code>
    /// </example>
    public static List<TTarget> YMapToList<TTarget>(this IEnumerable<object>? source, bool ignoreCase = true) where TTarget : new()
    {
        var result = new List<TTarget>();

        if (source == null)
            return result;

        try
        {
            foreach (var item in source)
            {
                var mapped = item.YMapTo<TTarget>(ignoreCase);
                if (mapped != null)
                {
                    result.Add(mapped);
                }
            }

            return result;
        }
        catch
        {
            return result;
        }
    }

    /// <summary>
    /// 映射集合中的每个元素到目标类型（泛型版本）
    /// </summary>
    /// <typeparam name="TSource">源类型</typeparam>
    /// <typeparam name="TTarget">目标类型</typeparam>
    /// <param name="source">源集合</param>
    /// <param name="ignoreCase">是否忽略大小写，默认为true</param>
    /// <returns>映射后的集合</returns>
    /// <example>
    /// <code>
    /// var sourceList = new List&lt;SourceModel&gt; { ... };
    /// var targetList = sourceList.YMapToList&lt;SourceModel, TargetModel&gt;();
    /// Console.WriteLine($"映射了 {targetList.Count} 个对象");
    /// </code>
    /// </example>
    public static List<TTarget> YMapToList<TSource, TTarget>(this IEnumerable<TSource>? source, bool ignoreCase = true) where TTarget : new()
    {
        var result = new List<TTarget>();

        if (source == null)
            return result;

        try
        {
            foreach (var item in source)
            {
                if (item != null)
                {
                    var mapped = item.YMapTo<TTarget>(ignoreCase);
                    if (mapped != null)
                    {
                        result.Add(mapped);
                    }
                }
            }

            return result;
        }
        catch
        {
            return result;
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 检查两个类型是否兼容
    /// </summary>
    private static bool IsCompatibleType(Type sourceType, Type targetType)
    {
        if (sourceType == targetType)
            return true;

        if (targetType.IsAssignableFrom(sourceType))
            return true;

        // 检查可空类型
        var targetUnderlyingType = Nullable.GetUnderlyingType(targetType);
        if (targetUnderlyingType != null)
        {
            return IsCompatibleType(sourceType, targetUnderlyingType);
        }

        var sourceUnderlyingType = Nullable.GetUnderlyingType(sourceType);
        if (sourceUnderlyingType != null)
        {
            return IsCompatibleType(sourceUnderlyingType, targetType);
        }

        // 检查基本类型转换
        try
        {
            var instance = Activator.CreateInstance(sourceType);
            if (instance != null)
            {
                Convert.ChangeType(instance, targetType);
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 转换值到目标类型
    /// </summary>
    private static object? ConvertValue(object value, Type targetType)
    {
        if (value == null)
            return null;

        var valueType = value.GetType();

        if (targetType.IsAssignableFrom(valueType))
            return value;

        // 处理可空类型
        var underlyingType = Nullable.GetUnderlyingType(targetType);
        if (underlyingType != null)
        {
            return ConvertValue(value, underlyingType);
        }

        // 特殊处理布尔值转换
        if (targetType == typeof(bool) && value is string stringValue)
        {
            var lowerValue = stringValue.ToLowerInvariant();
            return lowerValue is "true" or "yes" or "1" or "on" or "enabled";
        }

        try
        {
            return Convert.ChangeType(value, targetType);
        }
        catch
        {
            return null;
        }
    }

    #endregion
}
