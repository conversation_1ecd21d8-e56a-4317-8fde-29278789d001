namespace Zylo.Data;

/// <summary>
/// Zylo.Data 配置选项
/// 用于配置数据访问层的各种功能模块
/// </summary>
public class ZyloDataOptions
{
    /// <summary>
    /// 是否启用数据库功能
    /// </summary>
    public bool EnableDatabase { get; set; } = true;

    /// <summary>
    /// 是否启用缓存功能
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 是否启用配置管理功能
    /// </summary>
    public bool EnableConfiguration { get; set; } = true;

    /// <summary>
    /// 是否启用对象映射功能
    /// </summary>
    public bool EnableMapping { get; set; } = true;

    /// <summary>
    /// 是否启用事务支持
    /// </summary>
    public bool EnableTransactions { get; set; } = true;

    /// <summary>
    /// 是否启用查询缓存
    /// </summary>
    public bool EnableQueryCache { get; set; } = true;

    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// 数据库提供程序类型
    /// </summary>
    public string DatabaseProvider { get; set; } = "SQLite";

    /// <summary>
    /// 缓存过期时间（分钟）
    /// </summary>
    public int CacheExpirationMinutes { get; set; } = 30;

    /// <summary>
    /// 查询超时时间（秒）
    /// </summary>
    public int QueryTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// 是否启用敏感数据日志记录
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// 是否启用详细错误信息
    /// </summary>
    public bool EnableDetailedErrors { get; set; } = true;

    /// <summary>
    /// 批量操作的批次大小
    /// </summary>
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// 连接池最大大小
    /// </summary>
    public int MaxPoolSize { get; set; } = 100;

    /// <summary>
    /// 连接池最小大小
    /// </summary>
    public int MinPoolSize { get; set; } = 5;

    /// <summary>
    /// 是否启用连接重试
    /// </summary>
    public bool EnableConnectionRetry { get; set; } = true;

    /// <summary>
    /// 连接重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 重试延迟时间（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 是否启用健康检查
    /// </summary>
    public bool EnableHealthChecks { get; set; } = true;

    /// <summary>
    /// 验证配置选项
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        if (CacheExpirationMinutes <= 0)
        {
            errors.Add("缓存过期时间必须大于0");
        }

        if (QueryTimeoutSeconds <= 0)
        {
            errors.Add("查询超时时间必须大于0");
        }

        if (BatchSize <= 0)
        {
            errors.Add("批次大小必须大于0");
        }

        if (MaxPoolSize <= 0)
        {
            errors.Add("连接池最大大小必须大于0");
        }

        if (MinPoolSize < 0)
        {
            errors.Add("连接池最小大小不能小于0");
        }

        if (MinPoolSize > MaxPoolSize)
        {
            errors.Add("连接池最小大小不能大于最大大小");
        }

        if (MaxRetryCount < 0)
        {
            errors.Add("重试次数不能小于0");
        }

        if (RetryDelayMs < 0)
        {
            errors.Add("重试延迟时间不能小于0");
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 获取默认配置
    /// </summary>
    /// <returns>默认配置选项</returns>
    public static ZyloDataOptions Default => new();

    /// <summary>
    /// 获取开发环境配置
    /// </summary>
    /// <returns>开发环境配置选项</returns>
    public static ZyloDataOptions Development => new()
    {
        EnableSensitiveDataLogging = true,
        EnableDetailedErrors = true,
        EnablePerformanceMonitoring = true,
        CacheExpirationMinutes = 5,
        QueryTimeoutSeconds = 60
    };

    /// <summary>
    /// 获取生产环境配置
    /// </summary>
    /// <returns>生产环境配置选项</returns>
    public static ZyloDataOptions Production => new()
    {
        EnableSensitiveDataLogging = false,
        EnableDetailedErrors = false,
        EnablePerformanceMonitoring = true,
        CacheExpirationMinutes = 60,
        QueryTimeoutSeconds = 30,
        MaxPoolSize = 200,
        MinPoolSize = 10
    };
}
