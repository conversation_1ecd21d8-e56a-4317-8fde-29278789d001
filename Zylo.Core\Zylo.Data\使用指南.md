# 🗄️ Zylo.Data 使用指南

<div align="center">

![Data](https://img.shields.io/badge/Data-Processing-brightgreen)
![Dual Container](https://img.shields.io/badge/Dual_Container-Supported-gold)
![.NET](https://img.shields.io/badge/.NET-8.0-purple)

**企业级数据处理和数据库操作库使用完整指南**

</div>

## 🎯 **概述**

Zylo.Data 是一个功能强大的数据处理和数据库操作库，提供：
- 🗄️ **数据库操作**: 多数据库支持和类型安全操作
- 🔄 **对象映射**: 智能对象映射和数据转换
- ⚙️ **配置管理**: 灵活的配置文件支持和管理
- 📊 **数据验证**: 完整的数据验证和约束检查
- 🎯 **双容器支持**: DryIoc + Microsoft.Extensions.DI

## 🚀 **快速开始**

### 📦 **安装**

```xml
<!-- 项目文件中添加引用 -->
<ProjectReference Include="..\Zylo.Data\Zylo.Data.csproj" />
```

### 🎯 **基本使用**

#### **方式1: 静态 API (推荐新手)**
```csharp
using Zylo.Data;

// 零配置启动
YData.UseAuto(options =>
{
    options.EnableDatabaseOperations = true;
    options.EnableObjectMapping = true;
    options.EnableConfigurationManagement = true;
    options.DefaultConnectionString = "Server=localhost;Database=MyApp;";
});

// 验证配置
var isValid = YData.ValidateSetup();
Console.WriteLine($"数据配置有效: {isValid}");

// 获取数据服务
var database = YData.GetDatabase();
var mapper = YData.GetObjectMapper();
var config = YData.GetConfiguration();
```

#### **方式2: 依赖注入 (推荐企业项目)**
```csharp
using Microsoft.Extensions.DependencyInjection;
using Zylo.Data.DI;

// 注册数据服务
services.AddZyloData(options =>
{
    options.EnableDatabaseOperations = true;
    options.EnableObjectMapping = true;
    options.EnableConfigurationManagement = true;
    options.DefaultConnectionString = "Server=localhost;Database=MyApp;";
    options.EnableConnectionPooling = true;
    options.CommandTimeout = 30;
});

// 使用数据服务
public class UserRepository
{
    private readonly IYDatabase _database;
    private readonly IYMapping _mapper;

    public UserRepository(IYDatabase database, IYMapping mapper)
    {
        _database = database;
        _mapper = mapper;
    }

    public async Task<User> GetUserAsync(int id)
    {
        var sql = "SELECT * FROM Users WHERE Id = @Id";
        var result = await _database.QueryFirstOrDefaultAsync<User>(sql, new { Id = id });
        return result;
    }
}
```

#### **方式3: 高性能容器 (推荐数据密集型场景)**
```csharp
using Zylo.Core.DI.Factory;
using Zylo.Data.DI;

// 创建高性能容器
using var container = ZyloContainerFactory.Create(ZyloContainerType.DryIoc);
container.AddZyloData(options =>
{
    options.EnableDatabaseOperations = true;
    options.EnableConnectionPooling = true;
    options.MaxPoolSize = 100;
});

// 使用数据服务
var database = container.Resolve<IYDatabase>();
var users = await database.QueryAsync<User>("SELECT * FROM Users");
```

## 🔧 **配置选项**

### 📋 **完整配置示例**
```csharp
YData.UseAuto(options =>
{
    // 功能模块开关
    options.EnableDatabaseOperations = true;        // 数据库操作
    options.EnableObjectMapping = true;             // 对象映射
    options.EnableConfigurationManagement = true;   // 配置管理
    options.EnableDataValidation = true;            // 数据验证
    options.EnableCaching = true;                   // 数据缓存

    // 数据库配置
    options.DefaultConnectionString = "Server=localhost;Database=MyApp;Trusted_Connection=true;";
    options.DatabaseProvider = DatabaseProvider.SqlServer;
    options.CommandTimeout = 30;                    // 命令超时时间
    options.EnableConnectionPooling = true;         // 连接池
    options.MaxPoolSize = 100;                      // 最大连接数
    options.MinPoolSize = 5;                        // 最小连接数

    // 映射配置
    options.EnableAutoMapping = true;               // 自动映射
    options.CaseSensitiveMapping = false;           // 大小写敏感
    options.EnableNullValueMapping = true;          // 空值映射

    // 缓存配置
    options.EnableQueryCache = true;                // 查询缓存
    options.CacheExpirationMinutes = 30;            // 缓存过期时间
    options.MaxCacheSize = 1000;                    // 最大缓存条目数

    // 性能配置
    options.EnableBatchOperations = true;           // 批量操作
    options.BatchSize = 1000;                       // 批量大小
    options.EnableAsyncOperations = true;           // 异步操作
});
```

## 🗄️ **数据库操作服务**

### 📊 **基础查询操作**
```csharp
// 获取数据库服务
var database = YData.GetDatabase();

// 查询单个对象
var user = await database.QueryFirstOrDefaultAsync<User>(
    "SELECT * FROM Users WHERE Id = @Id", 
    new { Id = 1 });

// 查询多个对象
var users = await database.QueryAsync<User>(
    "SELECT * FROM Users WHERE Age > @MinAge", 
    new { MinAge = 18 });

// 查询标量值
var userCount = await database.QuerySingleAsync<int>(
    "SELECT COUNT(*) FROM Users");

// 分页查询
var pagedUsers = await database.QueryPagedAsync<User>(
    "SELECT * FROM Users ORDER BY Id", 
    pageNumber: 1, 
    pageSize: 20);

Console.WriteLine($"总记录数: {pagedUsers.TotalCount}");
Console.WriteLine($"当前页: {pagedUsers.PageNumber}");
```

### 📝 **数据修改操作**
```csharp
// 插入数据
var newUser = new User { Name = "张三", Age = 25, Email = "<EMAIL>" };
var userId = await database.InsertAsync(newUser);
Console.WriteLine($"新用户ID: {userId}");

// 更新数据
var updateResult = await database.ExecuteAsync(
    "UPDATE Users SET Age = @Age WHERE Id = @Id",
    new { Age = 26, Id = userId });
Console.WriteLine($"更新了 {updateResult} 条记录");

// 删除数据
var deleteResult = await database.ExecuteAsync(
    "DELETE FROM Users WHERE Id = @Id",
    new { Id = userId });

// 批量插入
var users = new List<User>
{
    new User { Name = "李四", Age = 30 },
    new User { Name = "王五", Age = 28 }
};
var insertedCount = await database.BulkInsertAsync(users);
Console.WriteLine($"批量插入了 {insertedCount} 条记录");
```

### 🔄 **事务操作**
```csharp
// 使用事务
using var transaction = await database.BeginTransactionAsync();
try
{
    // 执行多个操作
    await database.ExecuteAsync(
        "INSERT INTO Users (Name, Age) VALUES (@Name, @Age)",
        new { Name = "事务用户1", Age = 25 },
        transaction);

    await database.ExecuteAsync(
        "INSERT INTO Users (Name, Age) VALUES (@Name, @Age)",
        new { Name = "事务用户2", Age = 30 },
        transaction);

    // 提交事务
    await transaction.CommitAsync();
    Console.WriteLine("事务提交成功");
}
catch (Exception ex)
{
    // 回滚事务
    await transaction.RollbackAsync();
    Console.WriteLine($"事务回滚: {ex.Message}");
}
```

## 🔄 **对象映射服务**

### 📋 **基础映射操作**
```csharp
// 获取对象映射服务
var mapper = YData.GetObjectMapper();

// 简单对象映射
var userDto = mapper.Map<UserDto>(user);
var userEntity = mapper.Map<User>(userDto);

// 集合映射
var userDtos = mapper.Map<List<UserDto>>(users);

// 条件映射
var partialDto = mapper.Map<UserDto>(user, options =>
{
    options.IgnoreProperty(x => x.Password);
    options.MapProperty(x => x.FullName, src => $"{src.FirstName} {src.LastName}");
});
```

### 🎯 **高级映射配置**
```csharp
// 配置映射规则
mapper.CreateMap<User, UserDto>(config =>
{
    config.ForMember(dest => dest.FullName, 
        opt => opt.MapFrom(src => $"{src.FirstName} {src.LastName}"));
    config.ForMember(dest => dest.Age, 
        opt => opt.MapFrom(src => DateTime.Now.Year - src.BirthYear));
    config.Ignore(dest => dest.Password);
});

// 使用配置的映射
var mappedDto = mapper.Map<UserDto>(user);

// 深度映射
var orderWithDetails = mapper.Map<OrderDto>(order, options =>
{
    options.IncludeRelated(x => x.OrderItems);
    options.IncludeRelated(x => x.Customer);
    options.MaxDepth = 3;
});
```

### 📊 **数据转换**
```csharp
// 字典到对象
var userDict = new Dictionary<string, object>
{
    ["Name"] = "张三",
    ["Age"] = 25,
    ["Email"] = "<EMAIL>"
};
var userFromDict = mapper.MapFromDictionary<User>(userDict);

// 对象到字典
var dictFromUser = mapper.MapToDictionary(user);

// DataTable 到对象列表
var usersFromDataTable = mapper.MapFromDataTable<User>(dataTable);

// JSON 到对象
var userFromJson = mapper.MapFromJson<User>(jsonString);
var jsonFromUser = mapper.MapToJson(user);
```

## ⚙️ **配置管理服务**

### 📄 **配置文件操作**
```csharp
// 获取配置管理服务
var config = YData.GetConfiguration();

// 读取配置值
var dbConnectionString = config.GetValue<string>("Database:ConnectionString");
var maxRetries = config.GetValue<int>("App:MaxRetries", defaultValue: 3);
var enableLogging = config.GetValue<bool>("Logging:Enabled", defaultValue: true);

// 读取配置节
var databaseConfig = config.GetSection<DatabaseConfig>("Database");
var appSettings = config.GetSection<AppSettings>("App");

// 设置配置值
config.SetValue("App:LastUpdateTime", DateTime.Now);
config.SetValue("Database:MaxConnections", 100);

// 保存配置
await config.SaveAsync();
```

### 🔧 **配置验证**
```csharp
// 验证配置
var validationResult = config.ValidateConfiguration();
if (!validationResult.IsValid)
{
    Console.WriteLine("配置验证失败:");
    foreach (var error in validationResult.Errors)
    {
        Console.WriteLine($"- {error}");
    }
}

// 配置变更监听
config.OnConfigurationChanged += (sender, args) =>
{
    Console.WriteLine($"配置已更改: {args.Key} = {args.NewValue}");
};

// 重新加载配置
await config.ReloadAsync();
```

### 📋 **多环境配置**
```csharp
// 设置环境
config.SetEnvironment("Development");

// 加载环境特定配置
await config.LoadEnvironmentConfigAsync("appsettings.Development.json");

// 获取环境特定值
var debugMode = config.GetEnvironmentValue<bool>("Debug", defaultValue: false);

// 配置优先级: 环境变量 > 环境配置文件 > 默认配置文件
var finalValue = config.GetValueWithPriority<string>("Database:Host");
```

## 📊 **数据验证服务**

### ✅ **基础验证**
```csharp
// 获取数据验证服务
var validator = YData.GetDataValidator();

// 验证单个对象
var user = new User { Name = "", Age = -1, Email = "invalid-email" };
var validationResult = validator.Validate(user);

if (!validationResult.IsValid)
{
    Console.WriteLine("验证失败:");
    foreach (var error in validationResult.Errors)
    {
        Console.WriteLine($"- {error.PropertyName}: {error.ErrorMessage}");
    }
}

// 验证集合
var users = new List<User> { user1, user2, user3 };
var collectionResult = validator.ValidateCollection(users);
```

### 🎯 **自定义验证规则**
```csharp
// 创建验证规则
validator.AddRule<User>(user => !string.IsNullOrEmpty(user.Name), "姓名不能为空");
validator.AddRule<User>(user => user.Age >= 0 && user.Age <= 150, "年龄必须在0-150之间");
validator.AddRule<User>(user => user.Email.Contains("@"), "邮箱格式不正确");

// 条件验证
validator.AddConditionalRule<User>(
    condition: user => user.IsVip,
    rule: user => !string.IsNullOrEmpty(user.VipCode),
    message: "VIP用户必须有VIP码");

// 异步验证
validator.AddAsyncRule<User>(async user =>
{
    var exists = await database.QuerySingleAsync<bool>(
        "SELECT COUNT(*) FROM Users WHERE Email = @Email",
        new { user.Email });
    return !exists;
}, "邮箱已存在");
```

## 📈 **性能监控**

### 📊 **性能指标**
```csharp
// 获取性能指标
var metrics = YData.GetPerformanceMetrics();
Console.WriteLine($"数据库连接数: {metrics.ActiveConnections}");
Console.WriteLine($"查询缓存命中率: {metrics.CacheHitRate:P2}");
Console.WriteLine($"平均查询时间: {metrics.AverageQueryTime:F2} ms");

// 获取统计信息
var statistics = YData.GetStatistics();
Console.WriteLine($"总查询次数: {statistics.TotalQueries}");
Console.WriteLine($"成功率: {statistics.SuccessRate:P2}");
Console.WriteLine($"错误率: {statistics.ErrorRate:P2}");
```

### 🔍 **健康检查**
```csharp
// 执行健康检查
var health = YData.PerformHealthCheck();
Console.WriteLine($"健康状态: {health.Status}");
Console.WriteLine($"检查时间: {health.CheckTime}");

// 检查详细结果
foreach (var check in health.CheckResults)
{
    Console.WriteLine($"{check.Key}: {(check.Value ? "✅" : "❌")}");
}

// 数据库连接检查
var dbHealth = await YData.CheckDatabaseConnectionAsync();
Console.WriteLine($"数据库连接: {(dbHealth ? "正常" : "异常")}");
```

## 🎯 **最佳实践**

### ✅ **推荐做法**

1. **使用连接池**
```csharp
YData.UseAuto(options =>
{
    options.EnableConnectionPooling = true;
    options.MaxPoolSize = 100;
    options.MinPoolSize = 5;
});
```

2. **启用查询缓存**
```csharp
options.EnableQueryCache = true;
options.CacheExpirationMinutes = 30;
```

3. **使用事务处理关键操作**
```csharp
using var transaction = await database.BeginTransactionAsync();
try
{
    // 执行多个相关操作
    await transaction.CommitAsync();
}
catch
{
    await transaction.RollbackAsync();
    throw;
}
```

4. **配置验证和监控**
```csharp
// 定期检查配置
var isValid = YData.ValidateSetup();
if (!isValid)
{
    // 处理配置问题
}

// 监控性能
var metrics = YData.GetPerformanceMetrics();
if (metrics.AverageQueryTime > 1000) // 超过1秒
{
    // 性能警告
}
```

### ❌ **避免的做法**

1. **不要忘记释放资源**
2. **不要在循环中执行单个查询**
3. **不要忽略事务的重要性**
4. **不要在生产环境中禁用连接池**

---

<div align="center">

**🗄️ 现在您已经掌握了 Zylo.Data 的完整使用方法！**

**开始构建您的数据驱动应用吧！**

</div>
