# 📋 Zylo.Data 项目升级计划

## 🎯 **总体计划执行说明**

> **📖 本项目严格按照 [总体升级进度.md](../总体升级进度.md) 执行**
>
> **项目定位**: Zylo 工具库第3个项目 - 数据处理工具库
> **总体进度**: 3/7 项目 (43%)
> **项目职责**: 提供数据库操作、配置管理、缓存和对象映射功能
> **上一项目**: Zylo.IO (文件和IO操作)
> **下一项目**: Zylo.System (系统工具)

<div align="center">

![Data](https://img.shields.io/badge/Zylo.Data-数据处理-purple)
![Progress](https://img.shields.io/badge/Progress-100%25-brightgreen)
![Priority](https://img.shields.io/badge/Priority-3-yellow)

**数据处理项目详细升级计划**

</div>

## 🚀 **YDatabase 易用性升级计划 (2025年新增)**

> **重要决策**: 保留 YDatabase 设计，取消 FreeSql 集成，专注于易用性和简单性
> **升级目标**: 参考 FreeSql 易用性优势，让 YDatabase 更简单、更易用、更直观
> **设计原则**: 开箱即用、API 简洁、链式调用、智能推断、保持向后兼容
> **容器统一**: 全面迁移到 DryIoc 容器，与项目其他部分保持一致

### 🎯 **易用性核心目标**
- **🎪 开箱即用** - 零配置启动，自动处理常见场景
- **🎨 API 简洁** - 方法命名直观，减少学习成本
- **🔗 链式调用** - 流畅的查询体验，代码更优雅
- **🧠 智能推断** - 自动处理类型转换、参数绑定等
- **📱 一致体验** - 不同数据库使用体验完全一致

### 🏗️ **DryIoc 容器统一目标**
- **🔄 容器统一** - 全项目使用 DryIoc 容器，提升性能和一致性
- **⚡ 性能提升** - DryIoc 比 Microsoft.Extensions.DI 性能更好
- **🔧 功能增强** - DryIoc 提供更丰富的容器功能
- **🎯 架构一致** - 与 Prism.DryIoc 完美集成

## 🎯 **项目概述**

**Zylo.Data** 是 Zylo 工具库的第3个项目，根据总体升级计划，本项目专注于：

### 📋 **核心职责**（按总计划）
- **YDatabase**: 数据库操作工具
- **YConfiguration**: 配置管理工具
- **YCache**: 缓存工具
- **YMapping**: 对象映射工具

### 📊 **项目信息**
- **目标框架**: .NET 8.0
- **兼容性**: .NET 6.0+
- **依赖**: Microsoft.Extensions.Configuration, MemoryCache, AutoMapper
- **包含模块**: 4个数据模块（按总计划）
- **当前状态**: ✅ 项目框架已创建

### 🎉 **项目框架创建完成**
- [x] ✅ Zylo.Data.csproj 项目文件
- [x] ✅ GlobalUsings.cs 全局引用
- [x] ✅ Extensions/ServiceCollectionExtensions.cs 依赖注入
- [x] ✅ Interfaces/ 接口文件夹 (4个接口)
- [x] ✅ Services/ 服务文件夹 (4个服务)
- [x] ✅ 升级计划.md 已更新

### 📊 **YDatabase 当前状态分析**

#### ✅ **当前已实现功能 (基础版本)**
- [x] ✅ 基础 CRUD 操作 (YInsertAsync, YUpdateAsync, YDeleteAsync)
- [x] ✅ LINQ 查询支持 (YTable, YWhere, YOrderBy)
- [x] ✅ 事务管理 (YBeginTransactionAsync, YExecuteInTransactionAsync)
- [x] ✅ 缓存集成 (YCacheFor, 内存缓存支持)
- [x] ✅ 分页查询 (YToPagedListAsync)
- [x] ✅ 异常安全设计 (永不抛异常原则)
- [x] ✅ 多表关联 (YJoin 基础实现)
- [x] ✅ 依赖注入支持 (AddYDatabase)
- [x] ✅ 完整测试覆盖 (119/119 测试通过)

#### ⚠️ **需要改进的功能**
- [ ] ⚠️ 批量操作性能 (当前基于 EF Core，效率有限)
- [ ] ⚠️ 条件更新/删除 (先查询再操作，效率低下)
- [ ] ⚠️ 多表关联功能 (功能受限，复杂查询支持不足)
- [ ] ❌ 智能状态跟踪 (缺失变更检测功能)
- [ ] ⚠️ SQL 优化能力 (完全依赖 EF Core)
- [ ] ⚠️ 连接池管理 (基础实现，缺乏监控)

## 📋 **YDatabase 升级计划（4个阶段）**

### 1️⃣ **阶段 1: 易用性基础升级** 🎪 **计划中**
```
进度: ░░░░░░░░░░░░░░░░░░░░ 0%
状态: 准备开始 - 易用性核心功能
预计时间: 3-4天
```

#### 🎯 **核心目标 (参考 FreeSql 易用性)**
- [ ] 🎪 开箱即用配置 (零配置启动)
- [ ] 🎨 简化 API 设计 (更直观的方法命名)
- [ ] 🔗 增强链式调用 (更流畅的查询体验)
- [ ] 🧠 智能类型推断 (自动处理常见转换)

#### 📋 **具体任务 (易用性优先)**
- [ ] 创建 `YDb` 静态类 (类似 FreeSql 的简化入口)
- [ ] 实现零配置启动 (`YDb.Use("connection")`)
- [ ] 简化查询 API (`YDb.Select<User>().Where().ToList()`)
- [ ] 添加智能扩展方法 (自动类型转换)
- [ ] 创建常用操作快捷方法
- [ ] 优化错误提示和调试体验

#### 💡 **易用性示例对比**
```csharp
// 当前方式 (相对复杂)
var services = new ServiceCollection();
services.AddYDatabase("connection");
var provider = services.BuildServiceProvider();
var ydb = provider.GetRequiredService<IYDatabase>();
var users = await ydb.YTable<User>().YWhere(u => u.Age > 18).YToListAsync();

// 升级后 (简单易用)
var users = await YDb.Select<User>().Where(u => u.Age > 18).ToListAsync();
// 或者更简单
var users = await YDb.Query<User>(u => u.Age > 18);
```

### 2️⃣ **阶段 2: 智能操作升级** 🧠 **计划中**
```
进度: ░░░░░░░░░░░░░░░░░░░░ 0%
状态: 等待阶段1完成 - 智能化操作
预计时间: 3-4天
```

#### 🎯 **核心目标 (智能化易用)**
- [ ] 🧠 智能 CRUD 操作 (自动推断操作类型)
- [ ] 🔄 智能状态跟踪 (自动检测变更)
- [ ] 📝 智能 SQL 生成 (优化查询性能)
- [ ] 🎭 智能类型转换 (自动处理类型差异)

#### 📋 **具体任务 (智能化优先)**
- [ ] 实现 `YDb.Save()` 智能保存 (自动判断插入/更新)
- [ ] 创建智能状态跟踪 (无需手动 Attach)
- [ ] 实现智能批量操作 (自动选择最优策略)
- [ ] 添加智能类型转换器
- [ ] 创建智能查询优化器
- [ ] 实现智能缓存策略

#### 💡 **智能化示例**
```csharp
// 智能保存 (自动判断插入还是更新)
await YDb.Save(user); // 有 ID 就更新，没 ID 就插入

// 智能批量操作
await YDb.SaveMany(users); // 自动分组插入和更新

// 智能查询
var users = await YDb.Query<User>(u => u.Name.Contains("张")); // 自动优化查询
```

### 3️⃣ **阶段 3: 高级功能升级** 🚀 **计划中**
```
进度: ░░░░░░░░░░░░░░░░░░░░ 0%
状态: 等待阶段2完成 - 高级功能
预计时间: 3-4天
```

#### 🎯 **核心目标 (高级但易用)**
- [ ] 🚀 高级查询功能 (子查询、联表、聚合)
- [ ] 🔄 事务简化操作 (自动事务管理)
- [ ] 📊 批量操作优化 (高性能批处理)
- [ ] 🎯 原生 SQL 支持 (复杂查询场景)

#### 📋 **具体任务 (保持易用性)**
- [ ] 实现简化的多表查询 (`YDb.Join<User, Order>()`)
- [ ] 创建自动事务管理 (`YDb.Transaction()`)
- [ ] 实现高性能批量操作
- [ ] 添加原生 SQL 执行器 (`YDb.Sql()`)
- [ ] 创建聚合查询简化 API
- [ ] 实现分页查询优化

#### 💡 **高级功能示例**
```csharp
// 简化的多表查询
var result = await YDb.Join<User, Order>()
    .On((u, o) => u.Id == o.UserId)
    .Where((u, o) => u.IsActive && o.Status == "完成")
    .Select((u, o) => new { u.Name, o.Amount })
    .ToListAsync();

// 自动事务
await YDb.Transaction(async () => {
    await YDb.Insert(user);
    await YDb.Insert(order);
}); // 自动提交或回滚
```

### 4️⃣ **阶段 4: 开发体验升级** ✨ **计划中**
```
进度: ░░░░░░░░░░░░░░░░░░░░ 0%
状态: 等待阶段3完成 - 开发体验优化
预计时间: 2-3天
```

#### 🎯 **核心目标 (开发友好)**
- [ ] ✨ 智能错误提示 (友好的错误信息)
- [ ] 🔍 调试工具增强 (SQL 查看、性能分析)
- [ ] 📚 智能代码提示 (IntelliSense 优化)
- [ ] 🎨 可视化工具 (查询构建器、数据浏览)

#### 📋 **具体任务 (开发体验优先)**
- [ ] 实现友好的错误提示系统
- [ ] 创建 SQL 查看和调试工具
- [ ] 添加性能分析面板
- [ ] 实现智能代码补全
- [ ] 创建可视化查询构建器
- [ ] 添加数据库健康监控

#### 💡 **开发体验示例**
```csharp
// 友好的错误提示
try {
    await YDb.Query<User>(u => u.InvalidProperty > 0);
} catch (YDatabaseException ex) {
    // 错误: 属性 'InvalidProperty' 在 User 类中不存在
    // 建议: 可用属性有 Id, Name, Age, Email
}

// 调试信息
YDb.EnableDebug(); // 自动显示生成的 SQL
var users = await YDb.Query<User>(u => u.Age > 18);
// 调试输出: SELECT * FROM Users WHERE Age > 18 (执行时间: 15ms)
```

## 🏗️ **项目结构升级计划**

### 📁 **新增模块结构 (易用性优先)**
```
Zylo.Data/
├── Core/                    # 核心接口和基类
│   ├── IYDatabase.cs       # 保持现有接口 ✅
│   ├── YDb.cs              # 新增静态易用入口 🆕
│   └── YDatabaseBase.cs    # 抽象基类 🆕
├── Easy/                   # 易用性模块 🆕
│   ├── YEasyQuery.cs       # 简化查询 API
│   ├── YEasyCrud.cs        # 简化 CRUD 操作
│   └── YEasyConfig.cs      # 零配置启动
├── Smart/                  # 智能化模块 🆕
│   ├── YSmartSave.cs       # 智能保存
│   ├── YSmartTracker.cs    # 智能状态跟踪
│   └── YSmartConverter.cs  # 智能类型转换
├── Advanced/               # 高级功能模块 🆕
│   ├── YAdvancedQuery.cs   # 高级查询功能
│   ├── YTransaction.cs     # 事务管理
│   └── YBatchOperations.cs # 批量操作
├── Debug/                  # 调试工具模块 🆕
│   ├── YDebugger.cs        # SQL 调试工具
│   ├── YPerformance.cs     # 性能监控
│   └── YDiagnostics.cs     # 诊断工具
└── Extensions/             # 扩展方法 🆕
    ├── YDbExtensions.cs    # 数据库扩展
    └── YQueryExtensions.cs # 查询扩展
```

### 🔄 **向后兼容策略 (易用性升级)**
- ✅ **保持现有 API 不变** - 所有现有代码无需修改
- ✅ **新增简化 API** - 通过 `YDb` 静态类提供更简单的入口
- ✅ **渐进式升级** - 用户可以选择使用新的易用 API
- ✅ **智能兼容** - 新 API 内部使用现有 IYDatabase 实现
- ✅ **测试保障** - 保持 119 个测试全部通过

### 💡 **易用性对比示例**

#### 🔄 **数据库连接 (开箱即用)**
```csharp
// 当前方式 (需要依赖注入配置)
var services = new ServiceCollection();
services.AddYDatabase("Data Source=app.db");
var provider = services.BuildServiceProvider();
var ydb = provider.GetRequiredService<IYDatabase>();

// 升级后 (零配置)
YDb.Use("Data Source=app.db"); // 全局配置一次
// 或者
YDb.UseSQLite("app.db"); // 更简单的方法
```

#### 📝 **基础 CRUD 操作**
```csharp
// 当前方式
var user = new User { Name = "张三", Age = 25 };
await ydb.YInsertAsync(user);
var foundUser = await ydb.YFindAsync<User>(user.Id);
foundUser.Age = 26;
await ydb.YUpdateAsync(foundUser);

// 升级后 (更简洁)
var user = new User { Name = "张三", Age = 25 };
await YDb.Insert(user);           // 更简洁的方法名
var foundUser = await YDb.Get<User>(user.Id);  // 更直观
foundUser.Age = 26;
await YDb.Save(foundUser);        // 智能保存 (自动判断插入/更新)
```

#### 🔍 **查询操作**
```csharp
// 当前方式
var users = await ydb.YTable<User>()
    .YWhere(u => u.Age > 18)
    .YOrderBy(u => u.Name)
    .YToListAsync();

// 升级后 (更流畅)
var users = await YDb.Select<User>()
    .Where(u => u.Age > 18)
    .OrderBy(u => u.Name)
    .ToListAsync();

// 或者更简单
var users = await YDb.Query<User>(u => u.Age > 18);
```

## 📊 **预期收益分析 (易用性优先)**

### 🎪 **易用性提升**
- **学习成本**: 降低 70% 学习时间 (参考 FreeSql 易用性)
- **代码量**: 减少 50% 样板代码
- **配置复杂度**: 零配置启动，开箱即用
- **API 一致性**: 100% 一致的使用体验

### 🧠 **智能化增强**
- **自动推断**: 智能判断插入/更新操作
- **类型转换**: 自动处理常见类型转换
- **错误提示**: 友好的错误信息和建议
- **性能优化**: 自动选择最优查询策略

### 🚀 **功能完整性**
- **基础操作**: 覆盖 100% 常用数据库操作
- **高级功能**: 支持复杂查询、事务、批量操作
- **扩展性**: 保持现有架构的扩展能力
- **兼容性**: 完全向后兼容现有代码

### ✨ **开发体验**
- **智能提示**: 更好的 IntelliSense 支持
- **调试工具**: 实时 SQL 查看和性能分析
- **错误处理**: 友好的错误信息和解决建议
- **文档完整**: 丰富的示例和最佳实践

## 📅 **时间安排**

### 🗓️ **总体时间线 (易用性优先)**
- **总计**: 10-14 天 (比原计划更快，专注易用性)
- **阶段 1**: 易用性基础（3-4天）
- **阶段 2**: 智能操作（3-4天）
- **阶段 3**: 高级功能（3-4天）
- **阶段 4**: 开发体验（2-3天）

### 🎯 **里程碑 (易用性导向)**
- **里程碑 1**: `YDb` 静态类完成，零配置启动（第4天）
- **里程碑 2**: 智能 CRUD 操作完成（第8天）
- **里程碑 3**: 高级查询功能完成（第12天）
- **里程碑 4**: 完整易用性升级完成（第14天）

## 🔧 **风险控制**

### ✅ **技术风险控制**
- **完全向后兼容** - 现有代码无需任何修改
- **渐进式升级** - 可以逐步启用新功能
- **测试保障** - 保持 119 个测试全部通过
- **性能基准** - 建立性能基准测试

### 🛡️ **质量保证**
- **代码审查** - 每个功能模块独立审查
- **单元测试** - 新功能 100% 测试覆盖
- **集成测试** - 确保模块间协作正常
- **性能测试** - 验证性能提升目标

## 🎉 **最终目标**

### 🏆 **升级后的 YDatabase 将成为**
- **易用性媲美 FreeSql** 的简单 ORM
- **功能完整** 的现代化数据访问层
- **开箱即用** 的零配置数据库工具
- **开发友好** 的智能化查询系统

### 🌟 **核心优势 (易用性优先)**
- ✅ **保持现有优秀设计** - 原有 API 设计保持不变
- ✅ **获得 FreeSql 易用性** - 参考最佳实践，更简单易用
- ✅ **智能化操作** - 自动推断、智能转换、友好提示
- ✅ **零学习成本** - 现有用户无需重新学习
- ✅ **渐进式升级** - 可以逐步采用新的易用 API

### 🎯 **与 FreeSql 易用性对比**
| 特性 | FreeSql | YDatabase 升级后 | 优势 |
|------|---------|------------------|------|
| 配置复杂度 | 简单 | 零配置 | ✅ 更简单 |
| API 一致性 | 好 | 优秀 | ✅ 更一致 |
| 智能推断 | 有 | 更强 | ✅ 更智能 |
| 错误提示 | 基础 | 友好 | ✅ 更友好 |
| 向后兼容 | 一般 | 完美 | ✅ 无风险 |

---

## 🤖 **AI开发脚本指令**

### 🎯 **当前状态**
- **基础版本**: 100% 完成 ✅ (119/119 测试通过)
- **升级计划**: 已制定 ✅ (详细4阶段计划)
- **下一步**: 开始阶段1 - 性能优化升级

## 🏗️ **双容器支持升级计划**

> **重要更新**: 结合易用性升级和双容器支持，打造最强数据访问层
> **升级范围**: YDatabase 易用性 + DryIoc/Microsoft.DI 双容器支持
> **执行顺序**: 先实现双容器支持，再添加易用性功能

### 📅 **Zylo.Data 双容器升级时间线**

#### **第1步: 容器支持基础 (1天)**
- [ ] 引用 Zylo.Core.DI 容器抽象
- [ ] 更新 YDatabaseService 构造函数
- [ ] 实现 ZyloDataExtensions 容器注册
- [ ] 添加容器类型配置选项

#### **第2步: YDb 静态 API (2天)**
- [ ] 创建 YDb 静态类（支持双容器）
- [ ] 实现零配置启动方法
- [ ] 添加智能容器选择逻辑
- [ ] 创建简化的 CRUD API

#### **第3步: 易用性功能 (2天)**
- [ ] 实现智能保存（自动判断插入/更新）
- [ ] 添加智能状态跟踪
- [ ] 创建流畅查询 API
- [ ] 实现友好错误提示

#### **第4步: 集成测试 (1天)**
- [ ] 更新单元测试支持双容器
- [ ] 添加性能基准测试
- [ ] 验证 Prism 集成
- [ ] 确保向后兼容性

### 🎯 **双容器 + 易用性设计**

#### **容器配置 API**
```csharp
// 方式 1: 使用 DryIoc（推荐，性能更好）
YDb.UseDryIoc("Data Source=app.db");

// 方式 2: 使用 Microsoft.Extensions.DI
YDb.UseMicrosoftDI("Data Source=app.db");

// 方式 3: 自动选择（智能选择最佳容器）
YDb.UseAuto("Data Source=app.db");

// 方式 4: 简化的 SQLite 配置
YDb.UseSQLite("app.db", ContainerType.Auto);
```

#### **Prism 集成 API**
```csharp
// App.xaml.cs - Prism 应用
protected override void RegisterTypes(IContainerRegistry containerRegistry)
{
    // 直接注册到 Prism 容器
    containerRegistry.RegisterYDatabase("Data Source=app.db");

    // 或者注册所有 Zylo.Data 服务
    containerRegistry.RegisterZyloData(options =>
    {
        options.ConnectionString = "Data Source=app.db";
        options.EnableCaching = true;
        options.EnableTracking = true;
    });
}
```

#### **易用性 API 设计**
```csharp
// 简化的 CRUD 操作
await YDb.Insert(user);                    // 插入
var user = await YDb.Get<User>(1);         // 查询
await YDb.Save(user);                      // 智能保存
await YDb.Delete(user);                    // 删除

// 简化的查询操作
var users = await YDb.Query<User>(u => u.Age > 18);
var adults = await YDb.Select<User>()
    .Where(u => u.Age >= 18)
    .OrderBy(u => u.Name)
    .ToListAsync();

// 批量操作
await YDb.SaveMany(users);                 // 智能批量保存
await YDb.DeleteMany<User>(u => u.IsDeleted);
```

### 🚀 **执行指令 (双容器 + 易用性)**
```
开始执行 Zylo.Data 双容器支持升级计划
优先级: 双容器支持 → 易用性功能 → 集成测试
1. 等待 Zylo.Core.DI 容器抽象完成
2. 实现 ZyloDataExtensions 容器注册
3. 创建 YDb 静态类（双容器支持）
4. 添加易用性 API 和智能功能
5. 完善测试和文档
立即开始准备工作！
```

### 💡 **双容器 + 易用性核心理念**
> **"最强数据访问层 = 高性能容器 + 极简 API"**
>
> - 🏗️ **双容器支持** - DryIoc 性能 + Microsoft.DI 兼容性
> - 🎪 **开箱即用** - 零配置启动，智能容器选择
> - 🎨 **API 简洁** - 一行代码完成复杂操作
> - 🧠 **智能推断** - 自动判断插入/更新/删除
> - 🔗 **完美集成** - 无缝支持 Prism、ASP.NET Core

---

<div align="center">

**🎯 YDatabase - 易用性与功能性完美结合的数据访问层！**

**参考 FreeSql 易用性，保持 YDatabase 优秀设计，打造最好用的 .NET ORM！**

</div>
