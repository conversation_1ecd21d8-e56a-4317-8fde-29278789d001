using Zylo.StringToolbox.Core;
using Zylo.StringToolbox.Extensions;

namespace Zylo.StringToolbox.Demo;

/// <summary>
/// Zylo.StringToolbox 超强字符串操作工具箱演示程序
/// <para>展示字符串工具箱的核心功能和使用方法</para>
/// </summary>
/// <remarks>
/// <para>演示内容包括：</para>
/// <list type="bullet">
/// <item>字符串截取操作：Slice, SliceFrom, SliceTo, SliceBetween, SliceByPattern</item>
/// <item>字符串查找操作：Find, FindAll, FindByPattern, FindBetween, FindWithContext</item>
/// <item>字符串位置操作：GetAllPositions, CountOccurrences, GetLeftContent等</item>
/// <item>扩展方法：ToToolbox, SafeSubstring, 位置验证等</item>
/// <item>链式操作：展示流畅API的强大功能</item>
/// </list>
/// </remarks>
class Program
{
    /// <summary>
    /// 程序入口点 - 运行所有演示功能
    /// </summary>
    /// <param name="args">命令行参数（本演示中未使用）</param>
    static void Main(string[] args)
    {
        // 显示程序标题和分隔线
        Console.WriteLine("🚀 Zylo.StringToolbox 超强字符串操作工具箱演示");
        Console.WriteLine(new string('=', 60));

        try
        {
            // 演示基础截取功能
            DemoSliceOperations();

            // 演示查找功能
            DemoSearchOperations();

            // 演示位置操作
            DemoPositionOperations();

            // 演示扩展方法
            DemoExtensionMethods();

            // 演示完成提示
            Console.WriteLine("\n✅ 演示完成！按任意键退出...");
        }
        catch (Exception ex)
        {
            // 异常处理：显示错误信息
            Console.WriteLine($"\n❌ 演示过程中发生错误: {ex.Message}");
            Console.WriteLine("按任意键退出...");
        }
        finally
        {
            // 等待用户输入后退出
            Console.ReadKey();
        }
    }

    /// <summary>
    /// 演示字符串截取操作的各种功能
    /// </summary>
    /// <remarks>
    /// 展示StringOperationToolbox的核心截取功能：
    /// <list type="bullet">
    /// <item>Slice: 按位置和长度截取</item>
    /// <item>SliceFrom: 从指定字符串开始截取到末尾</item>
    /// <item>SliceTo: 从开始截取到指定字符串</item>
    /// <item>SliceBetween: 截取两个标记之间的内容</item>
    /// <item>SliceByPattern: 使用正则表达式截取</item>
    /// </list>
    /// </remarks>
    static void DemoSliceOperations()
    {
        Console.WriteLine("\n📋 字符串截取操作演示");
        Console.WriteLine(new string('-', 40));

        // 定义测试文本
        var text = "Hello, World! This is a test string.";
        Console.WriteLine($"原始文本: {text}");

        // 1. 基础截取：按位置和长度截取字符串
        var slice1 = StringOperationToolbox.From(text).Slice(0, 5).ToString();
        Console.WriteLine($"Slice(0, 5): {slice1}");

        // 2. 从指定字符串开始截取到末尾
        var slice2 = StringOperationToolbox.From(text).SliceFrom("World").ToString();
        Console.WriteLine($"SliceFrom('World'): {slice2}");

        // 3. 从开始截取到指定字符串（不包含该字符串）
        var slice3 = StringOperationToolbox.From(text).SliceTo("test").ToString();
        Console.WriteLine($"SliceTo('test'): {slice3}");

        // 4. 截取两个标记字符串之间的内容
        var slice4 = StringOperationToolbox.From(text).SliceBetween("Hello, ", "!").ToString();
        Console.WriteLine($"SliceBetween('Hello, ', '!'): {slice4}");

        // 5. 使用正则表达式截取第一个匹配的4字母单词
        var slice5 = StringOperationToolbox.From(text).SliceByPattern(@"\b\w{4}\b").ToString();
        Console.WriteLine($"SliceByPattern('\\b\\w{{4}}\\b'): {slice5}");
    }

    /// <summary>
    /// 演示字符串查找操作的各种功能
    /// </summary>
    /// <remarks>
    /// 展示StringOperationToolbox的强大查找功能：
    /// <list type="bullet">
    /// <item>Find: 查找第一个匹配项</item>
    /// <item>FindAll: 查找所有匹配项</item>
    /// <item>FindByPattern: 使用正则表达式查找</item>
    /// <item>FindBetween: 查找两个标记之间的内容</item>
    /// <item>FindWithContext: 带上下文的查找</item>
    /// </list>
    /// </remarks>
    static void DemoSearchOperations()
    {
        Console.WriteLine("\n🔍 字符串查找操作演示");
        Console.WriteLine(new string('-', 40));

        // 定义包含多个重复内容的测试文本
        var text = "Hello World! Hello Universe! Hello Galaxy!";
        Console.WriteLine($"原始文本: {text}");

        // 1. 查找第一个匹配项
        var search1 = StringOperationToolbox.From(text).Find("Hello");
        Console.WriteLine($"Find('Hello') - 找到: {search1.Found}, 位置: [{string.Join(", ", search1.Positions)}]");

        // 2. 查找所有匹配项
        var search2 = StringOperationToolbox.From(text).FindAll("Hello");
        Console.WriteLine($"FindAll('Hello') - 找到: {search2.Found}, 位置: [{string.Join(", ", search2.Positions)}]");

        // 3. 使用正则表达式查找所有5字母单词
        var search3 = StringOperationToolbox.From(text).FindByPattern(@"\b\w{5}\b");
        Console.WriteLine($"FindByPattern('\\b\\w{{5}}\\b') - 匹配: [{string.Join(", ", search3.Matches)}]");

        // 4. 查找两个标记字符串之间的所有内容
        var search4 = StringOperationToolbox.From(text).FindBetween("Hello ", "!");
        Console.WriteLine($"FindBetween('Hello ', '!') - 匹配: [{string.Join(", ", search4.Matches)}]");

        // 5. 带上下文的查找：查找"World"及其前后3个字符
        var search5 = StringOperationToolbox.From(text).FindWithContext("World", 3, 3);
        Console.WriteLine($"FindWithContext('World', 3, 3) - 匹配: [{string.Join(", ", search5.Matches)}]");
    }

    /// <summary>
    /// 演示字符串位置操作的各种功能
    /// </summary>
    /// <remarks>
    /// 展示StringExtensions提供的位置相关扩展方法：
    /// <list type="bullet">
    /// <item>GetAllPositions: 获取所有匹配位置</item>
    /// <item>CountOccurrences: 统计出现次数</item>
    /// <item>GetLeftContent: 获取指定字符串左侧内容</item>
    /// <item>GetRightContent: 获取指定字符串右侧内容</item>
    /// <item>GetSurroundingContent: 获取指定字符串周围内容</item>
    /// <item>GetNthPosition: 获取第N次出现的位置</item>
    /// </list>
    /// </remarks>
    static void DemoPositionOperations()
    {
        Console.WriteLine("\n📍 字符串位置操作演示");
        Console.WriteLine(new string('-', 40));

        // 使用相同的测试文本以便对比结果
        var text = "Hello World! Hello Universe! Hello Galaxy!";
        Console.WriteLine($"原始文本: {text}");

        // 1. 获取所有"Hello"出现的位置
        var positions = text.GetAllPositions("Hello");
        Console.WriteLine($"GetAllPositions('Hello'): [{string.Join(", ", positions)}]");

        // 2. 统计"Hello"出现的次数
        var count = text.CountOccurrences("Hello");
        Console.WriteLine($"CountOccurrences('Hello'): {count}");

        // 3. 获取"World"左侧6个字符的内容
        var leftContent = text.GetLeftContent("World", 6);
        Console.WriteLine($"GetLeftContent('World', 6): '{leftContent}'");

        // 4. 获取"World"右侧1个字符的内容
        var rightContent = text.GetRightContent("World", 1);
        Console.WriteLine($"GetRightContent('World', 1): '{rightContent}'");

        // 5. 获取"Universe"周围的内容（左6个字符，右1个字符）
        var surroundingContent = text.GetSurroundingContent("Universe", 6, 1);
        Console.WriteLine($"GetSurroundingContent('Universe', 6, 1): '{surroundingContent}'");

        // 6. 获取"Hello"第2次出现的位置（从1开始计数）
        var nthPosition = text.GetNthPosition("Hello", 2);
        Console.WriteLine($"GetNthPosition('Hello', 2): {nthPosition}");
    }

    /// <summary>
    /// 演示字符串扩展方法和链式操作功能
    /// </summary>
    /// <remarks>
    /// 展示StringExtensions提供的实用扩展方法和强大的链式操作：
    /// <list type="bullet">
    /// <item>ToToolbox: 转换为StringOperationToolbox实例</item>
    /// <item>SafeSubstring: 安全的子字符串截取</item>
    /// <item>IsValidPosition: 位置有效性验证</item>
    /// <item>IsPositionAtWordBoundary: 单词边界检查</item>
    /// <item>链式操作: 展示流畅API的强大功能</item>
    /// </list>
    /// </remarks>
    static void DemoExtensionMethods()
    {
        Console.WriteLine("\n🔧 扩展方法演示");
        Console.WriteLine(new string('-', 40));

        // 定义测试文本
        var text = "Hello World! This is a test.";
        Console.WriteLine($"原始文本: {text}");

        // 1. 转换为StringOperationToolbox实例
        var toolbox = text.ToToolbox();
        Console.WriteLine($"ToToolbox(): {toolbox.Value}");

        // 2. 安全的子字符串截取（不会因越界而抛出异常）
        var safeSubstring = text.SafeSubstring(6, 5);
        Console.WriteLine($"SafeSubstring(6, 5): '{safeSubstring}'");

        // 3. 验证位置5是否在字符串有效范围内
        var isValidPos = text.IsValidPosition(5);
        Console.WriteLine($"IsValidPosition(5): {isValidPos}");

        // 4. 检查位置5是否位于单词边界
        var isWordBoundary = text.IsPositionAtWordBoundary(5);
        Console.WriteLine($"IsPositionAtWordBoundary(5): {isWordBoundary}");

        // 5. 链式操作演示：展示流畅API的强大功能
        // 截取到"test" -> 转换为大写 -> 替换空格为下划线
        var chainResult = text.ToToolbox()
            .SliceTo("test")                    // 截取到"test"
            .Apply(s => s.ToUpper())            // 转换为大写
            .Apply(s => s.Replace(" ", "_"))    // 替换空格为下划线
            .ToString();                        // 获取最终结果
        Console.WriteLine($"链式操作结果: '{chainResult}'");
    }
}
