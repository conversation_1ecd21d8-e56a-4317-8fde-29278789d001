using System;

namespace Zylo.Toolkit.Attributes
{
    /// <summary>
    /// Service 属性 - 像 CommunityToolkit.Mvvm 的 ObservableProperty 一样
    /// 标记类自动生成依赖注入代码
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
    public class YServiceAttribute : Attribute
    {
        /// <summary>
        /// 服务生命周期
        /// </summary>
        public ServiceLifetime Lifetime { get; set; } = ServiceLifetime.Scoped;

        /// <summary>
        /// 是否生成接口，默认为 true
        /// </summary>
        public bool GenerateInterface { get; set; } = true;

        /// <summary>
        /// 接口名称前缀，默认为 "I"（当 InterfaceName 为空时使用）
        /// </summary>
        public string InterfacePrefix { get; set; } = "I";

        /// <summary>
        /// 自定义接口名称（优先级高于 InterfacePrefix）
        /// </summary>
        /// <remarks>
        /// 如果指定了此属性，将直接使用此名称作为接口名，忽略 InterfacePrefix。
        /// 例如：InterfaceName = "ICustomUserService"
        /// </remarks>
        public string? InterfaceName { get; set; }

        /// <summary>
        /// 服务描述（用于生成注释和文档）
        /// </summary>
        /// <remarks>
        /// 可选的服务描述，将用于生成更详细的注册代码注释。
        /// 例如：Description = "用户管理服务"
        /// </remarks>
        public string? Description { get; set; }

        /// <summary>
        /// 初始化 Service 属性
        /// </summary>
        public YServiceAttribute()
        {
        }

        /// <summary>
        /// 初始化 Service 属性并指定生命周期
        /// </summary>
        /// <param name="lifetime">服务生命周期</param>
        public YServiceAttribute(ServiceLifetime lifetime)
        {
            Lifetime = lifetime;
        }
    }

    /// <summary>
    /// 服务生命周期枚举
    /// </summary>
    public enum ServiceLifetime
    {
        /// <summary>
        /// 单例模式 - 整个应用程序生命周期内只创建一个实例
        /// </summary>
        Singleton = 0,

        /// <summary>
        /// 作用域模式 - 在同一个作用域内共享实例（默认）
        /// </summary>
        Scoped = 1,

        /// <summary>
        /// 瞬态模式 - 每次请求都创建新实例
        /// </summary>
        Transient = 2
    }

    #region 🚀 便捷属性类

    /// <summary>
    /// YService Scoped 便捷属性 - 作用域生命周期
    ///
    /// 🎯 类级使用方式：
    /// [YServiceScoped]  // 等价于 [YService(ServiceLifetime.Scoped)]
    /// public class UserService { }
    ///
    /// 🎯 方法级使用方式：
    /// public partial class ToolService  // 注意：没有类级属性
    /// {
    ///     [YServiceScoped]
    ///     public string ProcessData(string data) { }  // 只有此方法包含在接口中
    /// }
    ///
    /// 💡 优势：
    /// - 更简洁的语法
    /// - 明确的语义表达
    /// - 支持类级和方法级使用
    /// - 减少输入错误
    ///
    /// ⚠️ 重要规则：
    /// - 类级属性优先：如果类有类级属性，方法级属性会被忽略
    /// - 方法级属性只在类没有类级属性时生效
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class YServiceScopedAttribute : YServiceAttribute
    {
        /// <summary>
        /// 初始化 Scoped 生命周期的 YService 属性
        /// </summary>
        public YServiceScopedAttribute() : base(ServiceLifetime.Scoped)
        {
        }

        /// <summary>
        /// 初始化 Scoped 生命周期的 YService 属性，并指定自定义接口名称
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        public YServiceScopedAttribute(string interfaceName) : base(ServiceLifetime.Scoped)
        {
            InterfaceName = interfaceName;
        }

        /// <summary>
        /// 初始化 Scoped 生命周期的 YService 属性，并指定接口名称和描述
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        /// <param name="description">服务描述</param>
        public YServiceScopedAttribute(string interfaceName, string description) : base(ServiceLifetime.Scoped)
        {
            InterfaceName = interfaceName;
            Description = description;
        }
    }

    /// <summary>
    /// YService Singleton 便捷属性 - 单例生命周期
    ///
    /// 🎯 类级使用方式：
    /// [YServiceSingleton]  // 等价于 [YService(ServiceLifetime.Singleton)]
    /// public class CacheService { }
    ///
    /// 🎯 方法级使用方式：
    /// public partial class UtilityService  // 注意：没有类级属性
    /// {
    ///     [YServiceSingleton]
    ///     public string GetGlobalConfig(string key) { }  // 全局配置方法
    /// }
    ///
    /// 💡 适用场景：
    /// - 缓存服务
    /// - 配置服务
    /// - 日志服务
    /// - 全局状态管理
    /// - 全局配置获取方法
    ///
    /// ⚠️ 重要规则：
    /// - 类级属性优先：如果类有类级属性，方法级属性会被忽略
    /// - 方法级属性只在类没有类级属性时生效
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class YServiceSingletonAttribute : YServiceAttribute
    {
        /// <summary>
        /// 初始化 Singleton 生命周期的 YService 属性
        /// </summary>
        public YServiceSingletonAttribute() : base(ServiceLifetime.Singleton)
        {
        }

        /// <summary>
        /// 初始化 Singleton 生命周期的 YService 属性，并指定自定义接口名称
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        public YServiceSingletonAttribute(string interfaceName) : base(ServiceLifetime.Singleton)
        {
            InterfaceName = interfaceName;
        }

        /// <summary>
        /// 初始化 Singleton 生命周期的 YService 属性，并指定接口名称和描述
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        /// <param name="description">服务描述</param>
        public YServiceSingletonAttribute(string interfaceName, string description) : base(ServiceLifetime.Singleton)
        {
            InterfaceName = interfaceName;
            Description = description;
        }
    }

    /// <summary>
    /// YService Transient 便捷属性 - 瞬态生命周期
    ///
    /// 🎯 类级使用方式：
    /// [YServiceTransient]  // 等价于 [YService(ServiceLifetime.Transient)]
    /// public class EmailService { }
    ///
    /// 🎯 方法级使用方式：
    /// public partial class CalculatorService  // 注意：没有类级属性
    /// {
    ///     [YServiceTransient]
    ///     public int Calculate(int x, int y) { }  // 无状态计算方法
    /// }
    ///
    /// 💡 适用场景：
    /// - 轻量级服务
    /// - 无状态服务
    /// - 每次使用都需要新实例的服务
    /// - 临时处理服务
    /// - 无状态计算方法
    ///
    /// ⚠️ 重要规则：
    /// - 类级属性优先：如果类有类级属性，方法级属性会被忽略
    /// - 方法级属性只在类没有类级属性时生效
    /// </summary>
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class YServiceTransientAttribute : YServiceAttribute
    {
        /// <summary>
        /// 初始化 Transient 生命周期的 YService 属性
        /// </summary>
        public YServiceTransientAttribute() : base(ServiceLifetime.Transient)
        {
        }

        /// <summary>
        /// 初始化 Transient 生命周期的 YService 属性，并指定自定义接口名称
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        public YServiceTransientAttribute(string interfaceName) : base(ServiceLifetime.Transient)
        {
            InterfaceName = interfaceName;
        }

        /// <summary>
        /// 初始化 Transient 生命周期的 YService 属性，并指定接口名称和描述
        /// </summary>
        /// <param name="interfaceName">自定义接口名称</param>
        /// <param name="description">服务描述</param>
        public YServiceTransientAttribute(string interfaceName, string description) : base(ServiceLifetime.Transient)
        {
            InterfaceName = interfaceName;
            Description = description;
        }
    }

    #endregion

    #region 🚫 方法排除属性

    /// <summary>
    /// YService 方法忽略属性 - 排除特定方法
    ///
    /// 🎯 使用方式：
    /// [YService]
    /// public partial class UserService
    /// {
    ///     public string GetUser(int id) => $"User_{id}";  // ✅ 自动包含在接口中
    ///
    ///     [YServiceIgnore]
    ///     public void DebugMethod() { }                    // ❌ 不包含在接口中
    /// }
    ///
    /// 💡 作用：
    /// - 在自动生成接口时排除特定方法
    /// - 避免将调试、内部工具方法暴露到接口中
    /// - 提供精细的控制能力
    ///
    /// 🔧 默认情况下，所有公共方法都会包含在生成的接口中
    /// 使用此属性可以排除不需要的方法
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class YServiceIgnoreAttribute : Attribute
    {
        /// <summary>
        /// 初始化方法忽略属性
        /// </summary>
        public YServiceIgnoreAttribute()
        {
        }
    }

    #endregion

}
