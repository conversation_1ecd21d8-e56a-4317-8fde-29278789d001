using System;

namespace Zylo.Toolkit.Attributes;

#region v1.3 升级 - YStatic 静态标签生成属性定义

/// <summary>
/// YStatic 属性 - 为非静态类生成普通静态方法
/// 
/// 🎯 核心功能：
/// - 为普通类生成对应的静态标签版本
/// - 使用委托调用原始实例方法，保持性能
/// - 生成 {ClassName}Extensions 静态类
/// 
/// 💡 设计理念：
/// - 完全基于 YService 成熟架构
/// - 与 YService 功能完全独立且可共存
/// - 不涉及依赖注入，纯静态标签生成
/// 
/// 🔧 使用方式：
/// [YStatic] - 类级属性：为所有公共方法生成静态标签
/// [YStatic] - 方法级属性：为特定方法生成静态标签
/// 
/// 🚀 生成示例：
/// [YStatic]
/// public class MathUtils
/// {
///     public int Add(int a, int b) => a + b;
/// }
/// 
/// // 生成：
/// public static class MathUtilsExtensions
/// {
///     private static readonly MathUtils _instance = new MathUtils();
///     public static int Add(int a, int b) => _instance.Add(a, b);
/// }
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class YStaticAttribute : Attribute
{
    /// <summary>
    /// 初始化 YStatic 属性
    /// </summary>
    public YStaticAttribute()
    {
    }
}

/// <summary>
/// YStaticExtension 属性 - 为非静态类生成静态扩展方法
/// 
/// 🎯 核心功能：
/// - 将非静态类的实例方法转换为静态扩展方法
/// - 第一个参数自动变为 this 参数
/// - 提供自然的扩展方法调用体验
/// 
/// 💡 设计理念：
/// - 与 YStatic 互斥，不能同时使用
/// - 专门用于生成扩展方法
/// - 第一个参数变为 this 参数的智能转换
/// 
/// 🔧 使用方式：
/// [YStaticExtension] - 类级属性：为所有公共方法生成扩展方法
/// [YStaticExtension] - 方法级属性：为特定方法生成扩展方法
/// 
/// 🚀 生成示例：
/// [YStaticExtension]
/// public class StringHelper
/// {
///     public string ToTitleCase(string input) => ...;
/// }
/// 
/// // 生成：
/// public static class StringHelperExtensions
/// {
///     private static readonly StringHelper _instance = new StringHelper();
///     public static string ToTitleCase(this string input) => _instance.ToTitleCase(input);
/// }
/// 
/// // 使用：
/// string title = "hello world".ToTitleCase();
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
public class YStaticExtensionAttribute : Attribute
{
    /// <summary>
    /// 初始化 YStaticExtension 属性
    /// </summary>
    public YStaticExtensionAttribute()
    {
    }
}

/// <summary>
/// YStaticIgnore 属性 - 忽略静态标签生成
/// 
/// 🎯 核心功能：
/// - 覆盖类级 YStatic 或 YStaticExtension 属性
/// - 明确排除特定方法不生成静态标签
/// - 提供精细的控制能力
/// 
/// 💡 设计理念：
/// - 补充标签，用于精细控制
/// - 优先级高于类级属性
/// - 保持与 YService 一致的设计模式
/// 
/// 🔧 使用方式：
/// [YStatic]
/// public class Utils
/// {
///     public void Method1() { }           // ✅ 生成静态标签
///     [YStaticIgnore]
///     public void Method2() { }           // ❌ 忽略，不生成
/// }
/// </summary>
[AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
public class YStaticIgnoreAttribute : Attribute
{
    /// <summary>
    /// 初始化 YStaticIgnore 属性
    /// </summary>
    public YStaticIgnoreAttribute()
    {
    }
}

#endregion
