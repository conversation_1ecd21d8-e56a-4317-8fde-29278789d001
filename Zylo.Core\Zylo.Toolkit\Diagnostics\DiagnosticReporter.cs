using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Zylo.Toolkit.Helper;



namespace Zylo.Toolkit.Diagnostics;

/// <summary>
/// 诊断报告器 - 负责检查和报告 YService 相关的问题
/// </summary>
/// <remarks>
/// 这个类提供了一系列静态方法来检查常见的 YService 使用问题，
/// 并生成相应的诊断信息。所有方法都是无状态的，可以安全地并发调用。
/// </remarks>
public static class DiagnosticReporter
{
    #region 🔍 类级诊断检查

    /// <summary>
    /// 检查类是否为 partial
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="classDeclaration">类声明语法</param>
    /// <param name="className">类名</param>
    public static void CheckClassIsPartial(
        SourceProductionContext context,
        ClassDeclarationSyntax classDeclaration,
        string className)
    {
        if (!YSyntaxAnalysisHelper.IsPartialClass(classDeclaration))
        {
            var diagnostic = YServiceDiagnostics.CreateClassMustBePartialDiagnostic(
                classDeclaration.Identifier.GetLocation(),
                className);
            
            context.ReportDiagnostic(diagnostic);
        }
    }

    /// <summary>
    /// 检查类级和方法级属性冲突
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="classDeclaration">类声明语法</param>
    /// <param name="className">类名</param>
    /// <param name="hasClassAttribute">是否有类级属性</param>
    /// <param name="hasMethodAttributes">是否有方法级属性</param>
    public static void CheckAttributeConflict(
        SourceProductionContext context,
        ClassDeclarationSyntax classDeclaration,
        string className,
        bool hasClassAttribute,
        bool hasMethodAttributes)
    {
        if (hasClassAttribute && hasMethodAttributes)
        {
            var diagnostic = YServiceDiagnostics.CreateAttributeConflictDiagnostic(
                classDeclaration.Identifier.GetLocation(),
                className);
            
            context.ReportDiagnostic(diagnostic);
        }
    }

    #endregion

    #region 🔍 方法级诊断检查

    /// <summary>
    /// 检查静态方法是否使用了方法级属性
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="method">方法声明语法</param>
    /// <param name="methodName">方法名</param>
    /// <param name="yServiceAttributes">YService 方法级属性列表</param>
    public static void CheckStaticMethodWithAttributes(
        SourceProductionContext context,
        MethodDeclarationSyntax method,
        string methodName,
        string[] yServiceAttributes)
    {
        // 检查是否是静态方法
        if (!method.Modifiers.Any(Microsoft.CodeAnalysis.CSharp.SyntaxKind.StaticKeyword))
            return;

        // 检查是否有方法级 YService 属性
        foreach (var attributeName in yServiceAttributes)
        {
            if (YSyntaxAnalysisHelper.HasMethodAttribute(method, attributeName))
            {
                var diagnostic = YServiceDiagnostics.CreateStaticMethodNotSupportedDiagnostic(
                    method.Identifier.GetLocation(),
                    methodName);
                
                context.ReportDiagnostic(diagnostic);
                break; // 只报告一次
            }
        }
    }

    /// <summary>
    /// 检查方法参数数量
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="method">方法声明语法</param>
    /// <param name="methodName">方法名</param>
    /// <param name="maxParameters">最大参数数量阈值</param>
    public static void CheckMethodParameterCount(
        SourceProductionContext context,
        MethodDeclarationSyntax method,
        string methodName,
        int maxParameters = 8)
    {
        var parameterCount = method.ParameterList.Parameters.Count;
        
        if (parameterCount > maxParameters)
        {
            var diagnostic = YServiceDiagnostics.CreateTooManyParametersWarning(
                method.Identifier.GetLocation(),
                methodName,
                parameterCount);
            
            context.ReportDiagnostic(diagnostic);
        }
    }

    /// <summary>
    /// 检查方法的泛型约束复杂度
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="method">方法声明语法</param>
    /// <param name="methodName">方法名</param>
    public static void CheckGenericConstraintComplexity(
        SourceProductionContext context,
        MethodDeclarationSyntax method,
        string methodName)
    {
        // 检查是否有泛型参数
        if (method.TypeParameterList == null || method.TypeParameterList.Parameters.Count == 0)
            return;

        // 检查约束复杂度
        if (method.ConstraintClauses.Count > 0)
        {
            var totalConstraints = method.ConstraintClauses
                .SelectMany(clause => clause.Constraints)
                .Count();

            // 如果约束总数超过阈值，发出警告
            if (totalConstraints > 5)
            {
                var diagnostic = YServiceDiagnostics.CreateComplexGenericConstraintWarning(
                    method.Identifier.GetLocation(),
                    methodName);
                
                context.ReportDiagnostic(diagnostic);
            }
        }
    }

    /// <summary>
    /// 检查方法是否缺少 XML 文档注释
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="method">方法声明语法</param>
    /// <param name="methodName">方法名</param>
    /// <param name="enableDocumentationWarnings">是否启用文档警告</param>
    public static void CheckMethodDocumentation(
        SourceProductionContext context,
        MethodDeclarationSyntax method,
        string methodName,
        bool enableDocumentationWarnings = false)
    {
        if (!enableDocumentationWarnings)
            return;

        // 检查是否有 XML 文档注释
        var hasDocumentation = method.GetLeadingTrivia()
            .Any(trivia => trivia.IsKind(Microsoft.CodeAnalysis.CSharp.SyntaxKind.SingleLineDocumentationCommentTrivia) ||
                          trivia.IsKind(Microsoft.CodeAnalysis.CSharp.SyntaxKind.MultiLineDocumentationCommentTrivia));

        if (!hasDocumentation)
        {
            var diagnostic = Diagnostic.Create(
                YServiceDiagnostics.YS103_MissingDocumentation,
                method.Identifier.GetLocation(),
                methodName);
            
            context.ReportDiagnostic(diagnostic);
        }
    }

    #endregion

    #region 🔧 批量诊断方法

    /// <summary>
    /// 对类进行完整的诊断检查
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="classDeclaration">类声明语法</param>
    /// <param name="className">类名</param>
    /// <param name="hasClassAttribute">是否有类级属性</param>
    /// <param name="hasMethodAttributes">是否有方法级属性</param>
    public static void PerformClassDiagnostics(
        SourceProductionContext context,
        ClassDeclarationSyntax classDeclaration,
        string className,
        bool hasClassAttribute,
        bool hasMethodAttributes)
    {
        // 检查 partial 关键字
        CheckClassIsPartial(context, classDeclaration, className);

        // 检查属性冲突
        CheckAttributeConflict(context, classDeclaration, className, hasClassAttribute, hasMethodAttributes);
    }

    /// <summary>
    /// 对方法进行完整的诊断检查
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="method">方法声明语法</param>
    /// <param name="methodName">方法名</param>
    /// <param name="enableAllWarnings">是否启用所有警告</param>
    public static void PerformMethodDiagnostics(
        SourceProductionContext context,
        MethodDeclarationSyntax method,
        string methodName,
        bool enableAllWarnings = false)
    {
        var yServiceAttributes = new[] { "YServiceScoped", "YServiceSingleton", "YServiceTransient" };

        // 检查静态方法属性
        CheckStaticMethodWithAttributes(context, method, methodName, yServiceAttributes);

        // 检查参数数量
        CheckMethodParameterCount(context, method, methodName);

        // 检查泛型约束复杂度
        CheckGenericConstraintComplexity(context, method, methodName);

        // 检查文档注释（可选）
        if (enableAllWarnings)
        {
            CheckMethodDocumentation(context, method, methodName, true);
        }
    }

    #endregion
}
