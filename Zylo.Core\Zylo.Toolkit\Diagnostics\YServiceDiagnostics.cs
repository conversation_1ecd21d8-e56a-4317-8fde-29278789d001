using Microsoft.CodeAnalysis;

namespace Zylo.Tooolkit.Diagnostics;

/// <summary>
/// YService 诊断系统 - 提供清晰的错误代码和说明
/// </summary>
/// <remarks>
/// 这个类定义了 YService 的所有诊断信息，包括错误、警告和信息提示。
/// 每个诊断都有唯一的错误代码（YS001-YS999）和详细的说明。
/// </remarks>
public static class YServiceDiagnostics
{
    #region 🔥 错误级别诊断 (YS001-YS099)

    /// <summary>
    /// YS001: 类必须标记为 partial
    /// </summary>
    public static readonly DiagnosticDescriptor YS001_ClassMustBePartial = new(
        "YS001",
        "类必须标记为 partial",
        "类 '{0}' 必须标记为 partial 才能使用 YService 属性。请在类声明前添加 partial 关键字。",
        "YService",
        DiagnosticSeverity.Error,
        isEnabledByDefault: true,
        description: "YService 需要生成部分类代码，因此目标类必须声明为 partial。",
        helpLinkUri: "https://docs.zylo.dev/yservice/errors/YS001");

    /// <summary>
    /// YS002: 静态方法不支持方法级属性
    /// </summary>
    public static readonly DiagnosticDescriptor YS002_StaticMethodNotSupported = new(
        "YS002",
        "静态方法不支持方法级属性",
        "静态方法 '{0}' 不能使用方法级 YService 属性。请使用类级属性或改为实例方法。",
        "YService",
        DiagnosticSeverity.Error,
        isEnabledByDefault: true,
        description: "静态方法不适合依赖注入，因此不支持方法级 YService 属性。",
        helpLinkUri: "https://docs.zylo.dev/yservice/errors/YS002");

    /// <summary>
    /// YS003: 类和方法级属性冲突
    /// </summary>
    public static readonly DiagnosticDescriptor YS003_AttributeConflict = new(
        "YS003",
        "类级和方法级属性冲突",
        "类 '{0}' 同时使用了类级和方法级 YService 属性。类级属性将覆盖方法级属性。",
        "YService",
        DiagnosticSeverity.Error,
        isEnabledByDefault: true,
        description: "一个类只能使用类级或方法级属性中的一种，不能同时使用。",
        helpLinkUri: "https://docs.zylo.dev/yservice/errors/YS003");

    #endregion

    #region ⚠️ 警告级别诊断 (YS101-YS199)

    /// <summary>
    /// YS101: 复杂泛型约束可能影响性能
    /// </summary>
    public static readonly DiagnosticDescriptor YS101_ComplexGenericConstraint = new(
        "YS101",
        "复杂泛型约束可能影响性能",
        "方法 '{0}' 的泛型约束较复杂，可能影响编译性能。建议简化约束条件。",
        "YService",
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: "过于复杂的泛型约束会增加编译时间和生成代码的复杂度。",
        helpLinkUri: "https://docs.zylo.dev/yservice/warnings/YS101");

    /// <summary>
    /// YS102: 方法参数过多
    /// </summary>
    public static readonly DiagnosticDescriptor YS102_TooManyParameters = new(
        "YS102",
        "方法参数过多",
        "方法 '{0}' 有 {1} 个参数，建议重构为使用参数对象。",
        "YService",
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: "参数过多的方法不利于维护，建议使用参数对象模式。",
        helpLinkUri: "https://docs.zylo.dev/yservice/warnings/YS102");

    /// <summary>
    /// YS103: 建议添加 XML 文档注释
    /// </summary>
    public static readonly DiagnosticDescriptor YS103_MissingDocumentation = new(
        "YS103",
        "建议添加 XML 文档注释",
        "方法 '{0}' 缺少 XML 文档注释。建议添加完整的文档说明。",
        "YService",
        DiagnosticSeverity.Warning,
        isEnabledByDefault: false, // 默认关闭，可选启用
        description: "完整的 XML 文档注释有助于生成高质量的接口文档。",
        helpLinkUri: "https://docs.zylo.dev/yservice/warnings/YS103");

    #endregion

    #region ℹ️ 信息级别诊断 (YS201-YS299)

    /// <summary>
    /// YS201: 成功生成接口
    /// </summary>
    public static readonly DiagnosticDescriptor YS201_InterfaceGenerated = new(
        "YS201",
        "成功生成接口",
        "为类 '{0}' 成功生成接口 '{1}'，包含 {2} 个方法。",
        "YService",
        DiagnosticSeverity.Info,
        isEnabledByDefault: false, // 默认关闭，调试时启用
        description: "接口生成成功的确认信息。",
        helpLinkUri: "https://docs.zylo.dev/yservice/info/YS201");

    /// <summary>
    /// YS202: 方法被忽略
    /// </summary>
    public static readonly DiagnosticDescriptor YS202_MethodIgnored = new(
        "YS202",
        "方法被忽略",
        "方法 '{0}' 被 [YServiceIgnore] 属性标记，不会包含在接口中。",
        "YService",
        DiagnosticSeverity.Info,
        isEnabledByDefault: false,
        description: "被忽略方法的确认信息。",
        helpLinkUri: "https://docs.zylo.dev/yservice/info/YS202");

    #endregion

    #region 🔧 诊断工具方法

    /// <summary>
    /// 创建类必须为 partial 的诊断
    /// </summary>
    /// <param name="location">错误位置</param>
    /// <param name="className">类名</param>
    /// <returns>诊断信息</returns>
    public static Diagnostic CreateClassMustBePartialDiagnostic(Location location, string className)
    {
        return Diagnostic.Create(YS001_ClassMustBePartial, location, className);
    }

    /// <summary>
    /// 创建静态方法不支持的诊断
    /// </summary>
    /// <param name="location">错误位置</param>
    /// <param name="methodName">方法名</param>
    /// <returns>诊断信息</returns>
    public static Diagnostic CreateStaticMethodNotSupportedDiagnostic(Location location, string methodName)
    {
        return Diagnostic.Create(YS002_StaticMethodNotSupported, location, methodName);
    }

    /// <summary>
    /// 创建属性冲突的诊断
    /// </summary>
    /// <param name="location">错误位置</param>
    /// <param name="className">类名</param>
    /// <returns>诊断信息</returns>
    public static Diagnostic CreateAttributeConflictDiagnostic(Location location, string className)
    {
        return Diagnostic.Create(YS003_AttributeConflict, location, className);
    }

    /// <summary>
    /// 创建复杂泛型约束警告
    /// </summary>
    /// <param name="location">警告位置</param>
    /// <param name="methodName">方法名</param>
    /// <returns>诊断信息</returns>
    public static Diagnostic CreateComplexGenericConstraintWarning(Location location, string methodName)
    {
        return Diagnostic.Create(YS101_ComplexGenericConstraint, location, methodName);
    }

    /// <summary>
    /// 创建参数过多警告
    /// </summary>
    /// <param name="location">警告位置</param>
    /// <param name="methodName">方法名</param>
    /// <param name="parameterCount">参数数量</param>
    /// <returns>诊断信息</returns>
    public static Diagnostic CreateTooManyParametersWarning(Location location, string methodName, int parameterCount)
    {
        return Diagnostic.Create(YS102_TooManyParameters, location, methodName, parameterCount);
    }

    #endregion
}
