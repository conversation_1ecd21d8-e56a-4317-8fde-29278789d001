# YService 升级计划

> 🎯 **目标**：基于 .NET 6.0/8.0 的实用功能升级，专注于开发体验和实际需求

## 📊 **当前状态评估 (v1.2)**

### ✅ **已完成核心功能**

- 🎯 类级和方法级属性支持
- 🔧 完整的泛型和复杂类型支持（委托、元组、嵌套泛型等）
- 📝 XML 文档注释完整保留
- 🏗️ 静态类包装器生成
- 🆕 v1.2 静态方法增强（混合类支持）
- ⚡ 增量编译支持
- 🔌 Microsoft.Extensions.DependencyInjection 完美集成
- 📋 自动注册代码生成

### 🔍 **测试验证结果**

- ✅ 类级属性测试：100% 通过
- ✅ 方法级属性测试：100% 通过
- ✅ 复杂类型测试：100% 通过
- ✅ 文档注释测试：100% 通过
- ✅ 依赖注入测试：100% 通过

## 🚀 **升级路线图**

### ✅ **v1.1 - 开发体验优化版（已完成）**

**完成时间：2024年**

### ✅ **v1.2 - 静态方法增强版（已完成）**

**完成时间：2025年1月**

#### 🎯 **v1.2 核心功能**

1. **混合类静态方法支持**
   - 支持在同一个类中混合使用实例方法和静态方法
   - 自动生成包装器类处理静态方法调用
   - 统一的接口设计，透明的依赖注入

2. **包装器模式实现**
   - 实例方法通过委托调用
   - 静态方法直接调用，保持性能
   - 自动服务注册到DI容器

3. **向后兼容性**
   - 完全兼容v1.1的所有功能
   - 不影响现有代码的使用方式
   - 渐进式功能增强

### 📅 **v1.3 - 性能优化版（计划中）**

**预计时间：2-3周**

#### 🔧 **核心改进**

1. **错误诊断增强**

```csharp
// 当前：编译错误，不清楚原因
// 改进：提供清晰的错误代码和说明

YS001: 类 'UserService' 必须标记为 partial
YS002: 方法 'GetUser' 的返回类型不支持序列化
YS003: 静态方法不能使用方法级属性
YS004: 泛型约束 'where T : delegate' 不支持
```

2. **生成文件命名优化**

```
当前命名：
- IUserService.yg.cs
- ServiceRegistration.ZyloServiceTest.yg.cs

优化命名：
- IUserService.YService.g.cs
- ServiceRegistration.ZyloServiceTest.YService.g.cs
```

3. **自定义接口命名支持**

```csharp
// 支持自定义接口名称
[YService(InterfaceName = "ICustomUserService")]
public partial class UserService { }

// 支持接口前缀自定义
[YService(InterfacePrefix = "IApp")]
public partial class UserService { } // 生成 IAppUserService
```

4. **生成代码质量改进**

```csharp
// 改进生成的注册代码，添加更多信息
public static IServiceCollection AddAllZyloServiceTestServices(this IServiceCollection services)
{
    // 🔧 自动注册的服务：
    // - IUserService → UserService (Scoped) - 用户管理服务
    // - IDataProcessor → DataProcessor (Mixed) - 数据处理服务
    
    services.AddUserService();        // Scoped
    services.AddDataProcessor();      // Mixed lifetimes
    return services;
}
```

#### 📝 **实施优先级**

1. 🔥 错误诊断增强（最重要）
2. 🔥 生成文件命名优化
3. 🚀 自定义接口命名
4. 🚀 生成代码质量改进

### 📅 **v1.2 - 静态方法增强版（优先级：🚀 中）**

**预计时间：2-3周**

#### 🔧 **静态方法级属性支持**

```csharp
// 新功能：混合类中的静态方法级属性
public partial class MixedService
{
    // 实例方法 - 现有功能
    [YServiceScoped]
    public async Task<string> ProcessDataAsync(string data) { }

    // 静态方法 - 新功能
    [YServiceSingleton.Static]
    public static string GetConfig(string key) { }
    
    [YServiceTransient.Static]
    public static string GenerateId() { }
}
```

#### 🏗️ **技术实现**

- 扩展 `YServiceMethodProcessor` 支持静态方法
- 为静态方法生成特殊的注册逻辑
- 保持与现有功能的兼容性

### 📅 **v1.3 - 实用功能版（优先级：💡 低）**

**预计时间：根据需求决定**

#### 🔧 **实用功能**

1. **条件编译支持**

```csharp
[YService]
#if DEBUG
[YServiceDebugMode]  // 调试模式下的特殊行为
#endif
public partial class DebugService { }
```

2. **服务描述和元数据**

```csharp
[YService(
    Description = "用户管理服务",
    Category = "Business",
    Version = "1.0")]
public partial class UserService { }
```

3. **性能优化**

- 减少生成代码的大小
- 优化编译时间
- 改进内存使用

### 📅 **v2.0 - .NET 9 准备版（长期规划）**

**预计时间：.NET 9 发布后**

#### 🔧 **新平台支持**

- .NET 9 新特性集成
- 新的 C# 语言特性支持
- 性能进一步优化

## 📋 **实施计划**

### 🎯 **Phase 1: v1.1 立即实施**

**目标：提升开发体验**

1. **Week 1: 错误诊断系统**
   - 设计错误代码体系
   - 实现诊断信息生成
   - 添加帮助文档链接

2. **Week 2: 命名和质量优化**
   - 优化生成文件命名
   - 改进生成代码格式
   - 添加自定义接口命名

### 🎯 **Phase 2: v1.2 按需实施**

**目标：扩展功能覆盖**

- 只有在用户明确需要静态方法级属性时才实施
- 保持架构简洁性

### 🎯 **Phase 3: v1.3+ 长期规划**

**目标：企业级功能**

- 根据实际使用反馈决定
- 专注于真正有用的功能

## 💡 **功能优先级评估**

### 🔥 **立即需要（v1.1）**

1. **错误诊断增强** - 开发体验关键
2. **文件命名优化** - 避免冲突
3. **自定义接口命名** - 灵活性需求

### 🚀 **可能需要（v1.2）**

1. **静态方法级属性** - 特定场景需求
2. **性能优化** - 大型项目需求

### 💡 **暂时不需要（v1.3+）**

1. **条件编译** - 复杂度高，收益低
2. **服务元数据** - 过度设计
3. **企业级功能** - 当前阶段不需要

## 🎯 **建议实施策略**

### 📈 **渐进式升级**

1. 先完成 v1.1 的核心改进
2. 收集用户反馈
3. 根据实际需求决定后续功能

### 🔧 **保持简洁**

- 专注于解决实际问题
- 避免过度设计
- 保持 API 的简洁性

### 📊 **质量优先**

- 每个版本都要有完整的测试
- 保持向后兼容性
- 文档同步更新

## 📝 **总结**

当前 YService 已经是一个功能完整、质量优秀的代码生成器。升级计划专注于：

1. **v1.1**：提升开发体验（错误诊断、命名优化）
2. **v1.2**：扩展功能覆盖（静态方法支持）
3. **v1.3+**：根据实际需求决定

重点是保持简洁实用，避免过度设计，确保每个功能都有明确的使用场景。

---

## 📋 **v1.1 详细实施计划**

### 🔧 **1. 错误诊断系统实施**

#### 📝 **错误代码设计**

```csharp
// Zylo.Service/Diagnostics/YServiceDiagnostics.cs
public static class YServiceDiagnostics
{
    // 类相关错误
    public static readonly DiagnosticDescriptor YS001_ClassMustBePartial = new(
        "YS001",
        "类必须标记为 partial",
        "类 '{0}' 必须标记为 partial 才能使用 YService 属性",
        "YService",
        DiagnosticSeverity.Error,
        true);

    // 方法相关错误
    public static readonly DiagnosticDescriptor YS002_StaticMethodNotSupported = new(
        "YS002",
        "静态方法不支持方法级属性",
        "静态方法 '{0}' 不能使用方法级 YService 属性，请使用类级属性或改为实例方法",
        "YService",
        DiagnosticSeverity.Error,
        true);

    // 泛型相关警告
    public static readonly DiagnosticDescriptor YS003_ComplexGenericConstraint = new(
        "YS003",
        "复杂泛型约束可能影响性能",
        "方法 '{0}' 的泛型约束较复杂，建议简化以提高编译性能",
        "YService",
        DiagnosticSeverity.Warning,
        true);
}
```

#### 🔧 **集成到生成器**

```csharp
// 在 YServiceGenerator 中添加诊断
private static void ReportDiagnostics(SourceProductionContext context, ...)
{
    if (!SyntaxAnalysisHelper.IsPartialClass(classDeclaration))
    {
        context.ReportDiagnostic(Diagnostic.Create(
            YServiceDiagnostics.YS001_ClassMustBePartial,
            classDeclaration.GetLocation(),
            classDeclaration.Identifier.ValueText));
    }
}
```

### 🔧 **2. 文件命名优化实施**

#### 📝 **命名规则更新**

```csharp
// Temple/YServiceCodeGenerator.cs 中更新
private static string GetInterfaceFileName(YServiceInfo info)
{
    return $"{info.InterfaceName}.YService.g.cs";  // 新命名
}

private static string GetRegistrationFileName(string assemblyName)
{
    return $"ServiceRegistration.{assemblyName}.YService.g.cs";  // 新命名
}
```

### 🔧 **3. 自定义接口命名实施**

#### 📝 **属性扩展**

```csharp
// Attributes/YServiceAttribute.cs 更新
[AttributeUsage(AttributeTargets.Class)]
public class YServiceAttribute : Attribute
{
    public ServiceLifetime Lifetime { get; set; } = ServiceLifetime.Scoped;
    public bool GenerateInterface { get; set; } = true;
    public string InterfacePrefix { get; set; } = "I";
    public string? InterfaceName { get; set; }  // 新属性
    public string? Description { get; set; }    // 新属性
}
```

## 🧪 **测试计划**

### 📋 **v1.1 测试用例**

```csharp
// 测试错误诊断
public class NonPartialService { }  // 应该报告 YS001

// 测试自定义接口命名
[YService(InterfaceName = "ICustomUserService")]
public partial class UserService { }

// 测试新的文件命名
// 验证生成文件名为 ICustomUserService.YService.g.cs
```

## 📊 **成功指标**

### 🎯 **v1.1 成功标准**

1. ✅ 所有错误都有清晰的诊断信息
2. ✅ 生成文件命名符合新规范
3. ✅ 自定义接口命名功能正常
4. ✅ 向后兼容性 100%
5. ✅ 性能无明显下降

### 📈 **质量保证**

- 所有现有测试继续通过
- 新功能有对应的测试用例
- 文档同步更新
- 示例代码验证
