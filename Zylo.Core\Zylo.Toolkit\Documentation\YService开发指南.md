# 🚀 YService 开发指南

**新功能开发的完整指南** - 基于 YService 架构模式，为 Zylo 框架新功能提供标准化开发流程。

## 📋 目录

- [开发流程](#开发流程)
- [项目结构](#项目结构)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [最佳实践](#最佳实践)
- [🆕 v1.2 开发经验](#v12-开发经验)

## 🎯 开发流程

### **Step 1: 创建项目结构**

按照 YService 的分层架构创建新功能项目：

```text
📁 Zylo.{NewFeature}/
├── 📁 Attributes/          # 属性定义
│   └── Y{Feature}Attribute.cs
├── 📁 Helper/              # 通用工具 (复用 Zylo.Service)
├── 📁 Models/              # 数据模型
│   └── Y{Feature}Models.cs
├── 📁 Processors/          # 业务处理器
│   ├── Y{Feature}ClassProcessor.cs
│   └── Y{Feature}MethodProcessor.cs
├── 📁 Generators/          # 主协调器
│   └── Y{Feature}Generator.cs
├── 📁 Temple/              # 代码生成器
│   ├── Y{Feature}CodeGenerator.cs
│   └── 📁 Y{Feature}/      # 协调器架构 (可选)
├── 📁 Documentation/       # 文档
└── 📁 build/               # 构建配置
```

### **Step 2: 定义属性**

```csharp
// Attributes/Y{Feature}Attribute.cs
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class Y{Feature}Attribute : Attribute
{
    public string? CustomName { get; set; }
    public string? Description { get; set; }
    
    public Y{Feature}Attribute() { }
    public Y{Feature}Attribute(string customName) => CustomName = customName;
    public Y{Feature}Attribute(string customName, string description)
    {
        CustomName = customName;
        Description = description;
    }
}

// 便捷属性
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class Y{Feature}ScopedAttribute : Y{Feature}Attribute
{
    public Y{Feature}ScopedAttribute() { }
    public Y{Feature}ScopedAttribute(string customName) : base(customName) { }
    public Y{Feature}ScopedAttribute(string customName, string description) : base(customName, description) { }
}
```

### **Step 3: 定义数据模型**

```csharp
// Models/Y{Feature}Models.cs
public record Y{Feature}Info(
    string ClassName,
    string Namespace,
    string InterfaceName,
    bool IsStatic,
    List<MethodInfo> Methods,
    string? ClassDocumentation = null,
    bool GenerateInterface = true
)
{
    // 计算属性
    public string InterfaceFileName => $"{InterfaceName}.Y{Feature}.yg.cs";
    public string WrapperClassName => $"{ClassName}Wrapper";
}

public record MethodInfo(
    string Name,
    string ReturnType,
    string Parameters,
    string? Documentation = null,
    bool IsIgnored = false
);
```

### **Step 4: 实现处理器**

```csharp
// Processors/Y{Feature}ClassProcessor.cs
public static class Y{Feature}ClassProcessor
{
    #region 🚀 公共入口点
    public static Y{Feature}Info ProcessClassLevel(
        ClassDeclarationSyntax classDeclaration,
        INamedTypeSymbol classSymbol,
        AttributeData y{Feature}Attribute)
    {
        // 使用 YService 的通用工具
        var className = classSymbol.Name;
        var namespaceName = classSymbol.ContainingNamespace.ToDisplayString();
        
        // 提取配置
        var customName = GetCustomName(y{Feature}Attribute);
        var interfaceName = customName ?? $"I{className}";
        
        // 提取文档
        var classDocumentation = XmlDocumentationExtractor.ExtractFromClass(classSymbol);
        
        // 提取方法
        var methods = ExtractMethodsWithDocumentation(classSymbol);
        
        return new Y{Feature}Info(
            ClassName: className,
            Namespace: namespaceName,
            InterfaceName: interfaceName,
            IsStatic: classSymbol.IsStatic,
            Methods: methods,
            ClassDocumentation: classDocumentation
        );
    }
    #endregion
    
    #region 🔍 属性验证与识别
    public static AttributeData? GetY{Feature}Attribute(INamedTypeSymbol classSymbol)
    {
        return SyntaxAnalysisHelper.GetClassAttribute(classSymbol, "Y{Feature}Attribute");
    }
    #endregion
    
    #region ⚙️ 配置提取
    private static string? GetCustomName(AttributeData attribute)
    {
        return SyntaxAnalysisHelper.GetAttributeParameter<string>(attribute, "CustomName");
    }
    #endregion
    
    #region 🔧 方法信息提取
    private static List<MethodInfo> ExtractMethodsWithDocumentation(INamedTypeSymbol classSymbol)
    {
        return classSymbol.GetMembers()
            .OfType<IMethodSymbol>()
            .Where(method => IsValidMethod(method))
            .Select(method => new MethodInfo(
                Name: method.Name,
                ReturnType: method.ReturnType.ToDisplayString(),
                Parameters: MethodSignatureHelper.GetParametersString(method),
                Documentation: XmlDocumentationExtractor.ExtractFromMethod(method),
                IsIgnored: SyntaxAnalysisHelper.HasMethodAttribute(method, "Y{Feature}IgnoreAttribute")
            ))
            .ToList();
    }
    
    private static bool IsValidMethod(IMethodSymbol method)
    {
        return method.DeclaredAccessibility == Accessibility.Public &&
               method.MethodKind == MethodKind.Ordinary &&
               !SyntaxAnalysisHelper.HasMethodAttribute(method, "Y{Feature}IgnoreAttribute");
    }
    #endregion
}
```

### **Step 5: 实现主协调器**

```csharp
// Generators/Y{Feature}Generator.cs
[Generator]
public class Y{Feature}Generator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 1. 设置语法筛选管道
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: static (s, _) => IsY{Feature}Candidate(s),
            transform: static (ctx, _) => GetY{Feature}Info(ctx));
        
        // 2. 收集所有信息
        var {feature}s = candidates.Where(static x => x is not null)
                                  .Collect();
        
        // 3. 委托给代码生成器
        context.RegisterSourceOutput({feature}s, 
            (context, {feature}s) => Y{Feature}CodeGenerator.GenerateCode(context, {feature}s));
    }
    
    private static bool IsY{Feature}Candidate(SyntaxNode node)
    {
        return node is ClassDeclarationSyntax classDecl &&
               classDecl.AttributeLists.Count > 0 &&
               classDecl.Modifiers.Any(SyntaxKind.PartialKeyword);
    }
    
    private static Y{Feature}Info? GetY{Feature}Info(GeneratorSyntaxContext context)
    {
        if (context.Node is not ClassDeclarationSyntax classDeclaration)
            return null;
        
        var classSymbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
        if (classSymbol is null)
            return null;
        
        // 🎯 协调者模式：根据属性类型分发给对应的处理器
        var y{Feature}Attribute = Y{Feature}ClassProcessor.GetY{Feature}Attribute(classSymbol);
        if (y{Feature}Attribute != null)
            return Y{Feature}ClassProcessor.ProcessClassLevel(classDeclaration, classSymbol, y{Feature}Attribute);
        
        // 检查方法级属性
        if (HasMethodLevelAttributes(classSymbol))
            return Y{Feature}MethodProcessor.ProcessMethodLevel(classDeclaration, classSymbol);
        
        return null;
    }
    
    private static bool HasMethodLevelAttributes(INamedTypeSymbol classSymbol)
    {
        return SyntaxAnalysisHelper.HasAnyMethodWithAttribute(classSymbol, "Y{Feature}");
    }
}
```

### **Step 6: 实现代码生成器**

```csharp
// Temple/Y{Feature}CodeGenerator.cs
public static class Y{Feature}CodeGenerator
{
    public static void GenerateCode(SourceProductionContext context, ImmutableArray<Y{Feature}Info> {feature}s)
    {
        if ({feature}s.IsEmpty)
            return;
        
        foreach (var {feature} in {feature}s)
        {
            // 生成接口文件
            var interfaceCode = GenerateInterface({feature});
            context.AddSource({feature}.InterfaceFileName, interfaceCode);
            
            // 生成包装器（如果是静态类）
            if ({feature}.IsStatic)
            {
                var wrapperCode = GenerateWrapper({feature});
                context.AddSource($"{{{feature}.WrapperClassName}}.Y{Feature}.yg.cs", wrapperCode);
            }
        }
        
        // 生成注册代码
        var registrationCode = GenerateRegistration({feature}s);
        context.AddSource($"Y{Feature}Registration.yg.cs", registrationCode);
    }
    
    private static SourceText GenerateInterface(Y{Feature}Info {feature})
    {
        var sb = new StringBuilder();
        
        using static Zylo.Service.Helper.CodeIndentFormatter;
        
        // 文件头
        sb.AppendLine(I0, "// <auto-generated />")
          .AppendLine(I0, "#nullable enable")
          .AppendLine()
          .AppendLine(I0, $"namespace {{feature}.Namespace};")
          .AppendLine();
        
        // 接口文档注释
        if (!string.IsNullOrEmpty({feature}.ClassDocumentation))
        {
            sb.Append(DocumentationProcessor.AdjustIndentation({feature}.ClassDocumentation, I0));
        }
        
        // 接口声明
        sb.AppendLine(I0, $"public interface {{feature}.InterfaceName}")
          .AppendLine(I0, "{");
        
        // 方法生成
        foreach (var method in {feature}.Methods.Where(m => !m.IsIgnored))
        {
            if (!string.IsNullOrEmpty(method.Documentation))
            {
                sb.Append(DocumentationProcessor.AdjustIndentation(method.Documentation, I1));
            }
            
            sb.AppendLine(I1, $"{method.ReturnType} {method.Name}({method.Parameters});");
        }
        
        sb.AppendLine(I0, "}");
        
        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }
}
```

### **Step 7: 创建测试项目**

```csharp
// 测试项目中的示例类
[Y{Feature}]
public partial class Test{Feature}Service
{
    public string GetData(int id) => $"Data_{id}";
    
    [Y{Feature}Ignore]
    public void InternalMethod() { }
}

[Y{Feature}Scoped("ICustom{Feature}")]
public partial class Custom{Feature}Service
{
    public async Task<string> ProcessAsync(string input) => input.ToUpper();
}
```

## 🔧 代码规范

### **命名约定**

- **项目名称**: `Zylo.{Feature}`
- **属性名称**: `Y{Feature}Attribute`
- **处理器名称**: `Y{Feature}ClassProcessor`
- **生成器名称**: `Y{Feature}Generator`
- **数据模型**: `Y{Feature}Info`

### **文件组织**

- 每个处理器使用 `#region` 分组
- 公共方法在前，私有方法在后
- 按功能逻辑分组，不按访问级别

### **文档注释**

```csharp
/// <summary>
/// 方法的简短描述
/// 
/// 🎯 核心功能：详细描述
/// 💡 设计理念：设计思路
/// 🔧 处理流程：
/// 1. 第一步：具体描述
/// 2. 第二步：具体描述
/// </summary>
```

## 🧪 测试指南

### **测试覆盖**

1. **属性测试**: 所有属性的所有参数组合
2. **生成测试**: 验证生成的代码正确性
3. **边界测试**: 异常情况和边界条件
4. **集成测试**: 与 DI 容器的集成

### **测试项目结构**

```text
📁 Zylo{Feature}Test/
├── 📁 Services/            # 测试用的服务类
├── 📁 Generated/           # 查看生成的代码
└── Program.cs              # 集成测试
```

## 💡 最佳实践

### **1. 复用 YService 工具**

```csharp
// ✅ 直接使用 YService 的通用工具
using Zylo.Service.Helper;

var parameters = MethodSignatureHelper.GetParametersString(method);
var documentation = XmlDocumentationExtractor.ExtractFromClass(classSymbol);
```

### **2. 遵循协调器模式**

```csharp
// ✅ 协调器只负责分发，不处理业务逻辑
private static Y{Feature}Info? GetY{Feature}Info(GeneratorSyntaxContext context)
{
    // 根据条件分发给不同处理器
    if (hasClassAttribute)
        return ClassProcessor.Process(...);
    if (hasMethodAttribute)
        return MethodProcessor.Process(...);
    return null;
}
```

### **3. 使用统一的代码格式化**

```csharp
// ✅ 使用 CodeIndentFormatter
using static Zylo.Service.Helper.CodeIndentFormatter;

sb.AppendLine(I0, "public class Example")
  .AppendLine(I0, "{")
  .AppendLine(I1, "public void Method()")
  .AppendLine(I1, "{")
  .AppendLine(I2, "// 方法内容")
  .AppendLine(I1, "}")
  .AppendLine(I0, "}");
```

### **4. 错误处理**

```csharp
// ✅ 早期返回，避免深层嵌套
public static Y{Feature}Info? ProcessClassLevel(...)
{
    if (invalidCondition)
        return null;
    
    // 正常处理逻辑
    return new Y{Feature}Info(...);
}
```

---

## 🎯 总结

按照这个开发指南，可以快速创建符合 Zylo 框架标准的新功能：

- **🏗️ 标准化架构**: 遵循 YService 的分层架构
- **🔧 工具复用**: 最大化复用现有的通用工具
- **📝 规范统一**: 统一的命名和代码规范
- **🧪 测试完整**: 完整的测试覆盖策略
- **💡 最佳实践**: 经过验证的开发模式

这确保了 Zylo 框架各功能之间的一致性和可维护性。

## 🆕 v1.2 开发经验

### 静态方法增强功能开发总结

v1.2 版本的静态方法增强功能开发过程中积累了宝贵的经验，为后续功能开发提供参考。

#### 🎯 核心设计决策

1. **包装器模式选择**
   - 考虑过多种方案：代理模式、装饰器模式、包装器模式
   - 最终选择包装器模式：简单、高效、易于理解
   - 关键优势：静态方法直接调用，实例方法委托调用

2. **命名约定统一**
   - 初期使用 `Service` 后缀：`DataProcessorService`
   - 后期改为 `Wrapper` 后缀：`DataProcessorWrapper`
   - 原因：更准确地反映了包装器的本质

3. **生成逻辑分离**
   - 混合类生成独立的包装器类
   - 不生成 partial class 实现
   - 服务注册指向包装器而非原始类

#### 🔧 技术实现要点

1. **静态方法检测**

   ```csharp
   var hasStaticMethods = service.Methods.Any(m => m.StaticLifetime != null);
   ```

2. **包装器生成逻辑**

   ```csharp
   if (hasStaticMethods)
   {
       // 生成包装器类
       GenerateMixedWrapper(sb, service);
   }
   else
   {
       // 生成传统 partial class
       GeneratePartialClass(sb, service);
   }
   ```

3. **方法调用区分**

   ```csharp
   if (method.StaticLifetime != null)
   {
       // 静态方法：直接调用
       return $"{service.ClassName}.{method.Name}({parameters});";
   }
   else
   {
       // 实例方法：委托调用
       return $"_instance.{method.Name}({parameters});";
   }
   ```

#### 🧪 测试策略

1. **功能测试**
   - 验证接口生成正确性
   - 验证包装器类生成
   - 验证服务注册正确性
   - 验证方法调用正确性

2. **兼容性测试**
   - 确保不影响现有功能
   - 验证向后兼容性
   - 测试混合使用场景

3. **性能测试**
   - 静态方法调用性能
   - 实例方法委托性能
   - 内存使用情况

#### 🚨 遇到的挑战

1. **服务注册不匹配**
   - 问题：生成 `DataProcessorWrapper` 但注册 `DataProcessor`
   - 解决：修改服务注册生成器，检测混合类并注册包装器

2. **静态方法调用错误**
   - 问题：静态方法仍然使用 `_instance.Method()` 调用
   - 解决：在方法生成时检查 `StaticLifetime` 属性

3. **测试用例更新**
   - 问题：现有测试期望静态方法被排除
   - 解决：更新测试用例，将静态方法包含在预期结果中

#### 💡 经验教训

1. **渐进式开发**
   - 先实现核心功能，再优化细节
   - 每个步骤都要有完整的测试验证
   - 保持向后兼容性

2. **命名的重要性**
   - 好的命名能减少理解成本
   - 统一的命名约定很重要
   - 及时调整不合适的命名

3. **测试驱动开发**
   - 先写测试，再实现功能
   - 测试用例要覆盖各种边界情况
   - 回归测试确保不破坏现有功能

#### 🔮 未来改进方向

1. **性能优化**
   - 考虑缓存包装器实例
   - 优化静态方法调用路径
   - 减少不必要的对象创建

2. **功能扩展**
   - 支持静态属性
   - 支持静态事件
   - 支持更复杂的静态成员

3. **开发工具**
   - 提供更好的诊断信息
   - 增加代码生成的可视化
   - 改进错误提示

这些经验为 Zylo.Service 的持续发展提供了宝贵的参考。
