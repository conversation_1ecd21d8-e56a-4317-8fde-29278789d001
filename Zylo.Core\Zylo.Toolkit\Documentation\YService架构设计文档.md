# 🏗️ Zylo.Toolkit YService 架构设计文档

**现代化源代码生成器架构设计** - 基于协调器模式的分层架构，为 Zylo.Toolkit 框架提供高性能依赖注入代码生成。

## 📋 目录

- [架构概览](#架构概览)
- [核心设计理念](#核心设计理念)
- [分层架构详解](#分层架构详解)
- [协调器模式](#协调器模式)
- [v1.2 静态方法增强](#v12-静态方法增强)
- [技术特性](#技术特性)
- [扩展指南](#扩展指南)

## 🎯 架构概览

### **整体架构图**

```text
Zylo.Toolkit (混合架构：程序集 + 源代码生成器)
├── 📁 Attributes/          # 属性定义层
│   └── YServiceAttribute.cs        # 所有 YService 相关属性
├── 📁 Helper/              # 通用工具层 (可复用) - Y前缀命名
│   ├── YSyntaxAnalysisHelper.cs    # 语法分析和属性检测
│   ├── YMethodSignatureHelper.cs   # 方法签名处理
│   ├── YXmlDocumentationExtractor.cs # XML 文档提取
│   ├── YDocumentationProcessor.cs  # 文档格式化
│   ├── YDocumentationTemplateGenerator.cs # 文档模板生成
│   └── YCodeIndentFormatter.cs     # 代码格式化 (Y前缀扩展方法)
├── 📁 Models/              # 数据模型层
│   └── YServiceModels.cs           # 统一数据模型 (v1.2增强)
├── 📁 Processors/          # 业务处理层 (YService专用)
│   ├── YServiceClassProcessor.cs   # 类级处理器
│   └── YServiceMethodProcessor.cs  # 方法级处理器
├── 📁 Generators/          # 生成器协调层
│   └── YServiceGenerator.cs        # 主协调器
├── 📁 Temple/              # 代码生成层
│   ├── YServiceCodeGenerator.cs    # 原始生成器
│   └── 📁 Yservice/                # 新协调器架构 (v1.1+v1.2)
│       ├── YServiceCodeCoordinator.cs    # 代码生成协调器
│       ├── InterfaceFileGenerator.cs     # 接口生成器 (v1.2混合类支持)
│       ├── ServiceRegistrationGenerator.cs # 注册生成器
│       ├── StatisticsGenerator.cs        # 统计生成器
│       └── ErrorReportGenerator.cs       # 错误报告生成器
├── 📁 Diagnostics/         # 诊断和错误报告
│   └── YServiceDiagnostics.cs      # 诊断规则定义
└── 📁 build/               # 构建配置
    ├── Zylo.Toolkit.props          # 属性配置
    └── Zylo.Toolkit.targets        # 构建目标
```

### **数据流向图**

```text
🔍 语法筛选 (YServiceGenerator)
    ↓ 候选识别
📋 任务分发 (YServiceGenerator)
    ↓ 分发到处理器
🔧 业务处理 (ClassProcessor + MethodProcessor)
    ↓ 生成数据模型
🏗️ 代码协调 (YServiceCodeCoordinator)
    ↓ 协调多个生成器
📝 独立生成 (Interface + Registration + Statistics + Error)
   ├── InterfaceFileGenerator (v1.2混合类支持)
   ├── ServiceRegistrationGenerator
   ├── StatisticsGenerator
   └── ErrorReportGenerator
    ↓ 输出
📁 .yg.cs 文件
```

## 🎯 核心设计理念

### **1. 分层架构原则**

#### **Helper 层 - 通用工具**

- **设计目标**：提供真正通用的工具，可被任何功能复用
- **核心原则**：不包含业务特定逻辑，只提供技术工具
- **复用性**：YService、YController、YRepository 等都可以使用

#### **Processors 层 - 业务处理**

- **设计目标**：包含 YService 专用的业务逻辑
- **核心原则**：处理特定功能的复杂业务逻辑
- **专用性**：只为 YService 功能服务

#### **Models 层 - 数据模型**

- **设计目标**：统一的数据模型，确保类型安全
- **核心原则**：使用 record 确保数据不可变性
- **一致性**：所有组件使用相同的数据模型

#### **Generators 层 - 协调管理**

- **设计目标**：协调整个生成流程
- **核心原则**：不包含业务逻辑，专注于协调和分发
- **可扩展性**：易于添加新的处理器和生成器

#### **Temple 层 - 代码生成**

- **设计目标**：专注于代码输出
- **核心原则**：将数据模型转换为高质量的 C# 代码
- **格式化**：使用统一的代码格式化工具

### **2. 协调器模式 (v1.1)**

#### **双重协调器设计**

```text
YServiceGenerator (主协调器)
    ↓ 语法分析和任务分发
YServiceClassProcessor + YServiceMethodProcessor
    ↓ 业务处理和数据模型生成
YServiceCodeCoordinator (代码协调器)
    ↓ 协调多个专用生成器
InterfaceFileGenerator + ServiceRegistrationGenerator + StatisticsGenerator + ErrorReportGenerator
    ↓ 独立文件生成
I*.YService.yg.cs + ServiceRegistration.*.yg.cs + YServiceStatistics.*.yg.cs + YServiceErrorReport.*.yg.cs
```

#### **协调器优势**

- **模块化**：每个生成器负责单一职责
- **错误隔离**：单个生成器错误不影响其他生成器
- **独立文件**：不同类型的代码生成到独立文件
- **易于扩展**：添加新生成器只需在协调器中注册

## 🔧 分层架构详解

### **Helper 层 - 通用工具**

```text
📁 Helper/
├── SyntaxAnalysisHelper.cs      # 语法分析和属性检测
├── MethodSignatureHelper.cs     # 方法签名处理
├── XmlDocumentationExtractor.cs # XML 文档提取
├── DocumentationProcessor.cs    # 文档格式化
└── CodeIndentFormatter.cs       # 代码格式化
```

**设计原则**：

- ✅ 真正通用，任何功能都可以使用
- ✅ 无业务逻辑，只提供技术工具
- ✅ 静态方法，无状态设计
- ✅ 高度可复用

### **Processors 层 - 业务处理**

```text
📁 Processors/
├── YServiceClassProcessor.cs    # 类级业务处理器
└── YServiceMethodProcessor.cs   # 方法级业务处理器
```

**设计原则**：

- ✅ 业务专用，包含 YService 特有逻辑
- ✅ 逻辑分组，按功能模块组织代码
- ✅ 直接调用通用工具，避免中间层
- ✅ 清晰的入口点和处理流程

### **Models 层 - 数据模型**

```text
📁 Models/
└── YServiceModels.cs            # 统一数据模型定义
```

**设计原则**：

- ✅ 类型安全，使用 record 确保不可变性
- ✅ 统一管理，所有数据模型集中定义
- ✅ 详细文档，每个字段都有清晰说明
- ✅ 计算属性，提供便捷的派生属性

### **Temple/YService 层 - 协调器架构**

```text
📁 Temple/YService/
├── YServiceCodeCoordinator.cs    # 代码生成协调器
├── InterfaceFileGenerator.cs     # 接口生成器 (v1.2混合类支持)
├── ServiceRegistrationGenerator.cs # 注册生成器 (v1.2包装器注册)
├── StatisticsGenerator.cs        # 统计生成器
└── ErrorReportGenerator.cs       # 错误报告生成器
```

**设计原则**：

- ✅ 单一职责，每个生成器负责一种文件类型
- ✅ 错误隔离，单个生成器错误不影响其他生成器
- ✅ v1.2增强：InterfaceFileGenerator 支持混合类包装器生成
- ✅ v1.2增强：ServiceRegistrationGenerator 智能选择注册类型
- ✅ 独立文件，生成到独立的 .yg.cs 文件
- ✅ 统一格式，使用 CodeIndentFormatter

## 🚀 协调器模式

### **YServiceGenerator (主协调器)**

**职责**：语法分析、候选识别、任务分发

```csharp
public class YServiceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 1. 设置语法筛选管道
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: IsYServiceCandidate,
            transform: GetYServiceInfo);

        // 2. 收集所有服务信息
        var services = candidates.Where(static x => x is not null)
                                .Collect();

        // 3. 委托给代码协调器生成
        context.RegisterSourceOutput(services,
            (context, services) => YServiceCodeCoordinator.Generate(context, services));
    }
}
```

### **YServiceCodeCoordinator (代码协调器)**

**职责**：协调多个专用生成器，生成独立文件

```csharp
public static class YServiceCodeCoordinator
{
    public static void Generate(SourceProductionContext context, ImmutableArray<YServiceInfo> services)
    {
        var assemblyName = GetAssemblyName(context);

        // 协调多个生成器
        GenerateInterfaceFiles(context, services);
        GenerateServiceRegistration(context, services, assemblyName);
        GenerateStatistics(context, services, assemblyName);
        GenerateErrorReports(context, services, assemblyName);
    }
}
```

## 🆕 v1.2 静态方法增强

### **混合类架构设计**

v1.2 版本引入了革命性的静态方法增强功能，支持在同一个类中混合使用实例方法和静态方法。

#### **核心设计理念**

```text
传统模式：
├── 类级属性 → 所有方法相同生命周期
└── 方法级属性 → 每个方法独立生命周期

v1.2 混合模式：
├── 无类级属性
├── 实例方法 → 标记 YService 属性
├── 静态方法 → 标记 YService 属性
└── 普通方法 → 无属性，不包含在接口中
```

#### **包装器生成策略**

```csharp
// 检测逻辑
var hasStaticMethods = service.Methods.Any(m => m.StaticLifetime != null);

if (hasStaticMethods)
{
    // 生成包装器类
    GenerateMixedWrapper(sb, service);
}
else
{
    // 生成传统 partial class
    GeneratePartialClass(sb, service);
}
```

#### **方法调用策略**

```csharp
// 在包装器中的方法实现
if (method.StaticLifetime != null)
{
    // 静态方法：直接调用
    return $"{service.ClassName}.{method.Name}({parameters});";
}
else
{
    // 实例方法：委托调用
    return $"_instance.{method.Name}({parameters});";
}
```

#### **服务注册策略**

```csharp
// 智能选择注册类型
var hasStaticMethods = service.Methods.Any(m => m.StaticLifetime != null);
var implementationTypeName = hasStaticMethods
    ? service.FullServiceTypeName.Replace(service.ClassName, $"{service.ClassName}Wrapper")
    : service.FullServiceTypeName;

services.AddScoped<IInterface, ImplementationClass>();
```

### **v1.2 架构优势**

1. **统一接口设计**：静态方法和实例方法在同一接口中
2. **性能优化**：静态方法直接调用，无额外开销
3. **透明使用**：对使用者完全透明的依赖注入
4. **向后兼容**：完全兼容 v1.1 的所有功能

## 🔧 技术特性

### **源代码生成器特性**

- **Roslyn 组件**: 基于 Microsoft.CodeAnalysis.CSharp 4.5.0
- **增量生成**: 支持增量编译，提高构建性能
- **多目标框架**: 同时支持 .NET 6.0 和 .NET 8.0
- **C# 13.0**: 使用最新的 C# 语言特性

### **属性系统**

```csharp
// 基础属性
[YService(ServiceLifetime.Scoped)]
public partial class UserService { }

// 便捷属性
[YServiceScoped]
[YServiceSingleton]
[YServiceTransient]

// 自定义参数
[YServiceScoped("ICustomInterface", "描述信息")]

// 方法排除
[YServiceIgnore]
```

### **生成的文件类型**

- **接口文件**: `I{ClassName}.YService.yg.cs`
- **注册文件**: `ServiceRegistration.{Assembly}.yg.cs`
- **统计文件**: `YServiceStatistics.{Assembly}.yg.cs`
- **错误文件**: `YServiceErrorReport.{Assembly}.yg.cs`

### **代码格式化**

```csharp
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

// 使用预定义的缩进常量
sb.YAppendLine(I0, "public class UserService")     // 顶级，无缩进
  .YAppendLine(I0, "{")
  .YAppendLine(I1, "public void Method()")          // 类内容，4个空格
  .YAppendLine(I1, "{")
  .YAppendLine(I2, "// 方法内容，8个空格")            // 方法内容，8个空格
  .YAppendLine(I1, "}")
  .YAppendLine(I0, "}");
```

## 🚀 扩展指南

### **为新功能创建处理器**

以 YController 为例：

#### **1. 创建目录结构**

```text
📁 Zylo.Controller/
├── 📁 Helper/ (复用 Zylo.Toolkit 的通用工具)
├── 📁 Processors/
│   ├── YControllerClassProcessor.cs
│   └── YControllerMethodProcessor.cs
├── 📁 Models/
│   └── YControllerModels.cs
├── 📁 Generators/
│   └── YControllerGenerator.cs
└── 📁 Temple/
    └── YControllerCodeGenerator.cs
```

#### **2. 实现专用处理器**

```csharp
// 复用通用工具
using Zylo.Toolkit.Helper;

public static class YControllerClassProcessor
{
    public static YControllerInfo ProcessClassLevel(...)
    {
        // 使用通用工具
        var parameters = MethodSignatureHelper.GetParametersString(methodSymbol);
        var isIgnored = SyntaxAnalysisHelper.HasMethodAttribute(method, "YControllerIgnore");
        // ...
    }
}
```

#### **3. 实现协调者**

```csharp
public class YControllerGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 委托给处理器
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: YControllerMethodProcessor.IsYControllerRelatedCandidate,
            transform: GetYControllerInfo);
    }
}
```

### **架构复用原则**

1. **Helper 层完全复用**：直接使用 Zylo.Toolkit 的通用工具
2. **架构模式复用**：按照相同的分层架构组织代码
3. **命名约定复用**：使用相同的命名模式
4. **设计原则复用**：遵循相同的设计原则

---

## 🎯 总结

YService 架构设计的核心价值：

- **🏗️ 分层清晰**：Helper(通用) → Processors(专用) → Generators(协调) → Temple(生成)
- **🔧 高度可复用**：通用工具可被任何功能使用
- **⚡ 性能优异**：增量编译，协调器模式
- **📝 文档完整**：自动保留和转换 XML 文档注释
- **🚀 易于扩展**：为 Zylo 框架的其他功能提供基础设施

这个架构为 Zylo 框架的未来发展奠定了坚实的基础，同时保持了高度的可维护性和可扩展性。
{
    // 🎯 协调者模式：根据属性类型分发给对应的处理器

    // 🏷️ 检查类级属性 → 委托给类级处理器
    if (yServiceAttribute != null)
        return YServiceClassProcessor.ProcessClassLevel(...);
    
    // 🔧 检查方法级属性 → 委托给方法级处理器
    if (HasMethodLevelAttributes)
        return YServiceMethodProcessor.ProcessMethodLevel(...);
    
    return null;
}

```

### 2. YServiceClassProcessor (类级处理器)

```csharp
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 属性验证与识别   │ -> │  ⚙️ 配置提取        │
/// │  ProcessClassLevel  │    │  属性检查和获取      │    │  参数解析和提取      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘

public static YServiceInfo ProcessClassLevel(...)
{
    // ⚙️ 第一步：配置提取
    var lifetime = GetLifetime(yServiceAttribute);
    
    // 📝 第二步：类文档注释提取
    var classDocumentation = XmlDocumentationExtractor.ExtractFromClass(...);
    
    // 🔧 第三步：方法信息提取
    var methods = ExtractMethodsWithDocumentation(...);
    
    // 🏗️ 第四步：对象构建
    return new YServiceInfo(...);
}
```

### 3. YServiceMethodProcessor (方法级处理器)

```csharp
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 候选识别        │ -> │  🎯 属性检测        │
/// │  ProcessMethodLevel │    │  类和方法筛选        │    │  方法级属性识别      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘

public static YServiceInfo ProcessMethodLevel(...)
{
    // 📝 第一步：提取类文档注释
    var classDocumentation = XmlDocumentationExtractor.ExtractFromClass(...);
    
    // 🔍 第二步：提取有方法级属性的方法信息
    var methodInfos = ExtractMethodLevelServiceMethods(...);
    
    // 🏗️ 第三步：构建标准的 YServiceInfo 对象
    return new YServiceInfo(..., IsMethodLevelTriggered: true);
}
```

## 🎨 设计模式

### 1. 协调者模式 (Mediator Pattern)

**应用场景**：YServiceGenerator 作为协调者

```csharp
// ✅ 协调者：不包含业务逻辑，只负责分发
public static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    // 根据条件分发给不同的处理器
    if (hasClassAttribute)
        return ClassProcessor.Process(...);
    if (hasMethodAttribute)
        return MethodProcessor.Process(...);
    return null;
}
```

**优势**：

- 🎯 职责清晰：协调者只负责分发，不处理具体业务
- 🔧 易于扩展：新增处理器不影响协调者
- 🏗️ 松耦合：处理器之间不直接依赖

### 2. 策略模式 (Strategy Pattern)

**应用场景**：不同的处理器实现不同的处理策略

```csharp
// ✅ 策略接口（隐式）
interface IYServiceProcessor
{
    YServiceInfo Process(ClassDeclarationSyntax classDeclaration, ...);
}

// ✅ 具体策略
public static class YServiceClassProcessor    // 类级处理策略
public static class YServiceMethodProcessor  // 方法级处理策略
```

### 3. 模板方法模式 (Template Method Pattern)

**应用场景**：处理器的标准化流程

```csharp
// ✅ 模板方法：标准化的处理流程
public static YServiceInfo ProcessClassLevel(...)
{
    // 🔧 第一步：配置提取（子类实现）
    var config = ExtractConfiguration(...);
    
    // 📝 第二步：文档提取（通用逻辑）
    var docs = XmlDocumentationExtractor.ExtractFromClass(...);
    
    // 🔧 第三步：方法提取（子类实现）
    var methods = ExtractMethods(...);
    
    // 🏗️ 第四步：对象构建（通用逻辑）
    return new YServiceInfo(...);
}
```

## 🚀 扩展指南

### 为新功能创建处理器

以创建 `YController` 功能为例：

#### 1. 创建目录结构

```
📁 Zylo.Controller/
├── 📁 Helper/ (复用 Zylo.Service 的通用工具)
├── 📁 Processors/
│   ├── YControllerClassProcessor.cs
│   └── YControllerMethodProcessor.cs
├── 📁 Models/
│   └── YControllerModels.cs
├── 📁 Generators/
│   └── YControllerGenerator.cs
└── 📁 Temple/
    └── YControllerCodeGenerator.cs
```

#### 2. 复用通用工具

```csharp
// ✅ 直接使用 Zylo.Service 的通用工具
using Zylo.Service.Helper;

// 在 YControllerClassProcessor 中
var parameters = MethodSignatureHelper.GetParametersString(methodSymbol);
var isIgnored = SyntaxAnalysisHelper.HasMethodAttribute(method, "YControllerIgnore");
```

#### 3. 实现专用处理器

```csharp
public static class YControllerClassProcessor
{
    #region 🚀 公共入口点
    public static YControllerInfo ProcessClassLevel(...) { }
    #endregion
    
    #region 🔍 属性验证与识别
    public static AttributeData? GetYControllerAttribute(...) { }
    #endregion
    
    #region ⚙️ 配置提取
    public static string GetRouteTemplate(...) { }
    #endregion
    
    #region 🔧 方法信息提取
    private static List<ActionInfo> ExtractActionMethods(...) { }
    #endregion
}
```

#### 4. 实现协调者

```csharp
public class YControllerGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: static (s, _) => IsYControllerCandidate(s),
            transform: static (ctx, _) => GetYControllerInfo(ctx));
        // ...
    }
    
    private static YControllerInfo? GetYControllerInfo(GeneratorSyntaxContext context)
    {
        // 🎯 协调者模式：分发给对应处理器
        if (hasClassAttribute)
            return YControllerClassProcessor.ProcessClassLevel(...);
        if (hasMethodAttribute)
            return YControllerMethodProcessor.ProcessMethodLevel(...);
        return null;
    }
}
```

## 📋 最佳实践

### 1. 代码组织原则

```csharp
/// ✅ 推荐的类结构
public static class YServiceClassProcessor
{
    #region 🚀 公共入口点
    // 主要的公共方法，作为处理器的入口
    #endregion
    
    #region 🔍 属性验证与识别
    // 属性检查和验证相关方法
    #endregion
    
    #region ⚙️ 配置提取
    // 从属性中提取配置参数的方法
    #endregion
    
    #region 🔧 方法信息提取
    // 提取和处理方法信息的私有方法
    #endregion
    
    #region 🔍 专用验证方法
    // 业务特有的验证逻辑
    #endregion
}
```

### 2. 命名约定

```csharp
// ✅ 处理器命名
YService{Type}Processor.cs     // 如：YServiceClassProcessor.cs
YController{Type}Processor.cs  // 如：YControllerActionProcessor.cs

// ✅ 方法命名
Process{Type}Level()           // 如：ProcessClassLevel()
Extract{Info}()               // 如：ExtractMethodsWithDocumentation()
Get{Config}()                 // 如：GetLifetime()
Has{Attribute}()              // 如：HasYServiceIgnoreAttribute()
Is{Condition}()               // 如：IsValidServiceMethod()
```

### 3. 文档注释规范

```csharp
/// <summary>
/// 方法的简短描述
/// 
/// 🎯 核心功能：
/// 详细描述方法的核心功能和作用
/// 
/// 💡 设计理念：
/// 说明设计思路和理念
/// 
/// 🔧 处理流程：
/// 1. 第一步：具体步骤描述
/// 2. 第二步：具体步骤描述
/// 3. 第三步：具体步骤描述
/// 
/// 🚀 使用场景：
/// 描述什么时候使用这个方法
/// </summary>
/// <param name="parameter">参数描述</param>
/// <returns>返回值描述</returns>
```

### 4. 错误处理原则

```csharp
// ✅ 早期返回，避免深层嵌套
public static YServiceInfo? ProcessClassLevel(...)
{
    if (invalidCondition)
        return null;
    
    if (anotherInvalidCondition)
        return null;
    
    // 正常处理逻辑
    return new YServiceInfo(...);
}

// ✅ 使用 null 表示无效结果
public static AttributeData? GetYServiceAttribute(...)
{
    return classSymbol.GetAttributes()
        .FirstOrDefault(attr => IsYServiceRelatedAttribute(attr.AttributeClass));
}
```

### 5. 性能优化建议

```csharp
// ✅ 早期筛选，减少后续处理负担
predicate: static (s, _) => s is ClassDeclarationSyntax cls && 
                           cls.AttributeLists.Count > 0 &&
                           cls.Modifiers.Any(SyntaxKind.PartialKeyword)

// ✅ 使用静态方法，避免实例创建
public static class YServiceClassProcessor  // ✅ 静态类
{
    public static YServiceInfo ProcessClassLevel(...)  // ✅ 静态方法
}

// ✅ 缓存计算结果
private static readonly Dictionary<string, string> AttributeMapping = new()
{
    { "YServiceScoped", "Scoped" },
    // ...
};
```

---

## 🎯 总结

这个架构设计文档展示了 Zylo.Toolkit YService 的完整架构，可以作为 Zylo 框架其他功能的设计模板。

**核心优势**：

- 🏗️ **清晰的分层架构**：每层职责明确，易于理解和维护
- 🔧 **高度可复用性**：通用工具可被任何功能使用
- 🎯 **协调者模式**：简化复杂性，提高可扩展性
- 📋 **标准化流程**：为新功能提供清晰的开发模板
- 💡 **最佳实践**：包含详细的编码规范和设计原则
- 🆕 **v1.2 静态方法增强**：革命性的混合类支持，统一接口设计
- 🔧 **Y前缀命名**：避免命名冲突，提供清晰的工具标识

**版本演进**：

- **v1.0**：基础功能，类级和方法级属性支持
- **v1.1**：协调器架构，模块化代码生成
- **v1.2**：静态方法增强，混合类包装器支持

通过遵循这个架构设计，可以确保 Zylo.Toolkit 框架的各个功能保持一致的代码质量和架构风格。
