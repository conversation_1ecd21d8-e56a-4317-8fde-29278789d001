# 🆕 Zylo.Service v1.2 静态方法增强功能

## 🎯 概述

v1.2 版本引入了革命性的**静态方法增强**功能，支持在同一个类中混合使用实例方法和静态方法的依赖注入。这一功能解决了传统依赖注入框架无法处理静态方法的限制，为开发者提供了更灵活的服务设计选择。

## 🌟 核心特性

### 1. 统一接口设计
- 静态方法和实例方法都包含在同一个接口中
- 从使用者角度看，静态方法和实例方法没有区别
- 保持接口的一致性和简洁性

### 2. 智能包装器生成
- 自动生成包装器类处理混合方法调用
- 实例方法通过委托调用
- 静态方法直接调用，保持性能

### 3. 透明的依赖注入
- 无需修改现有的依赖注入使用方式
- 自动注册包装器类到DI容器
- 完全兼容现有的服务生命周期管理

## 📝 使用指南

### 基础用法

```csharp
public partial class DataProcessor  // 类上没有YService属性
{
    [YServiceScoped]
    public async Task<string> ProcessDataAsync(string data)
    {
        // 实例方法：需要访问实例状态
        return $"Processed: {data}";
    }
    
    [YServiceScoped] 
    public static string InternalUtility(string input)
    {
        // 静态方法：无状态的工具方法
        return $"Internal: {input}";
    }
    
    // 没有YService属性的方法不会包含在接口中
    public void LogMessage(string message)
    {
        Console.WriteLine(message);
    }
}
```

### 生成的代码

#### 接口定义
```csharp
public interface IDataProcessor
{
    Task<string> ProcessDataAsync(string data);  // 实例方法
    string InternalUtility(string input);       // 静态方法
}
```

#### 包装器实现
```csharp
public class DataProcessorWrapper : IDataProcessor
{
    private readonly DataProcessor _instance = new DataProcessor();
    
    // 实例方法：委托给实例
    public Task<string> ProcessDataAsync(string data) 
        => _instance.ProcessDataAsync(data);
    
    // 静态方法：直接调用
    public string InternalUtility(string input) 
        => DataProcessor.InternalUtility(input);
}
```

#### 服务注册
```csharp
public static IServiceCollection AddDataProcessor(this IServiceCollection services)
{
    services.AddScoped<IDataProcessor, DataProcessorWrapper>();
    return services;
}
```

## 🎨 使用场景

### 1. 工具类方法集成
```csharp
public partial class StringService
{
    [YServiceScoped]
    public string ProcessUserInput(string input)
    {
        // 实例方法：可能需要配置或状态
        return ValidateAndFormat(input);
    }
    
    [YServiceScoped]
    public static string FormatString(string input)
    {
        // 静态工具方法：无状态的字符串处理
        return input.Trim().ToUpper();
    }
}
```

### 2. 缓存操作混合
```csharp
public partial class CacheService
{
    private readonly IMemoryCache _memoryCache;
    
    [YServiceSingleton]
    public string GetFromMemory(string key)
    {
        // 实例方法：访问注入的内存缓存
        return _memoryCache.Get<string>(key);
    }
    
    [YServiceSingleton]
    public static string GetFromStaticCache(string key)
    {
        // 静态方法：访问静态缓存
        return StaticCache.Get(key);
    }
}
```

### 3. 配置管理
```csharp
public partial class ConfigService
{
    private readonly IConfiguration _config;
    
    [YServiceSingleton]
    public string GetUserConfig(string key)
    {
        // 实例方法：访问注入的配置
        return _config[key];
    }
    
    [YServiceSingleton]
    public static string GetGlobalConfig(string key)
    {
        // 静态方法：访问全局配置
        return Environment.GetEnvironmentVariable(key);
    }
}
```

## 🔧 技术实现

### 包装器模式
v1.2 使用包装器模式来解决静态方法的依赖注入问题：

1. **实例方法处理**：创建原始类的实例，委托调用实例方法
2. **静态方法处理**：直接调用原始类的静态方法
3. **统一接口**：包装器类实现统一的接口，对外提供一致的调用方式

### 性能考虑
- **静态方法**：直接调用，无额外开销
- **实例方法**：通过实例委托，开销最小
- **内存使用**：每个包装器实例只创建一个原始类实例

### 生命周期管理
- 包装器类的生命周期由DI容器管理
- 内部实例的生命周期与包装器一致
- 静态方法调用不受生命周期影响

## ⚠️ 注意事项

### 使用限制
1. **属性标记**：静态方法必须标记YService属性才会包含在接口中
2. **实例创建**：包装器会创建原始类的实例，确保原始类有无参构造函数
3. **静态构造函数**：不支持静态构造函数的特殊处理

### 最佳实践
1. **合理分离**：将有状态的操作设计为实例方法，无状态的工具方法设计为静态方法
2. **性能考虑**：频繁调用的方法优先考虑静态方法
3. **测试友好**：静态方法的测试需要特别考虑

### 兼容性
- 完全向后兼容v1.1的所有功能
- 不影响现有的类级和方法级属性使用
- 与现有的静态类包装功能并存

## 🚀 升级指南

### 从v1.1升级到v1.2
1. 更新NuGet包引用到v1.2.0
2. 现有代码无需修改，自动兼容
3. 可以开始使用新的静态方法增强功能

### 迁移建议
1. **识别候选**：找出现有代码中的静态工具方法
2. **逐步迁移**：将相关的静态方法添加到服务类中
3. **测试验证**：确保功能正常且性能符合预期

## 📊 性能对比

| 调用方式 | 性能开销 | 内存使用 | 推荐场景 |
|---------|---------|---------|---------|
| 直接静态调用 | 无 | 无 | 工具方法 |
| v1.2静态方法 | 极小 | 最小 | 需要DI的静态方法 |
| 实例方法 | 小 | 正常 | 有状态操作 |

## 🎉 总结

v1.2的静态方法增强功能为Zylo.Service带来了前所未有的灵活性，让开发者可以在同一个服务中自由混合使用实例方法和静态方法，同时保持依赖注入的所有优势。这一功能特别适合需要同时处理有状态和无状态操作的复杂业务场景。
