# 🚀 YStatic 功能规范文档 v1.3

**静态标签生成器** - 基于 YService 成熟架构的静态类标签生成功能，v1.3 版本新增功能

## 📋 目录

- [概述](#概述)
- [设计理念](#设计理念)
- [功能规范](#功能规范)
- [使用场景](#使用场景)
- [与 YService 的关系](#与-yservice-的关系)
- [架构设计](#架构设计)
- [代码示例](#代码示例)
- [技术实现](#技术实现)

## 🎯 概述

YStatic 是 Zylo.Toolkit v1.3 版本的核心新功能，专门用于为普通类生成静态标签版本。它完全基于 YService 的成熟架构，提供一模一样的开发体验和代码质量。

### 🌟 核心价值 (v1.3)

- **静态标签生成**：为普通类生成对应的静态标签版本
- **委托实现**：使用委托调用原始实例方法，保持性能
- **架构一致性**：完全复用 YService 的成熟架构和设计模式
- **独立功能**：与 YService 完全独立，不涉及依赖注入，纯静态标签生成
- **可共存使用**：与 YService 功能完全兼容，可以同时使用
- **🆕 v1.3 增强**：优化的扩展方法生成和更智能的类型推断

## 🎨 设计理念 (v1.3)

### 核心原则

1. **完全复用 YService 架构**：使用相同的分层架构、协调器模式、代码生成流程
2. **委托模式实现**：生成的静态类使用委托调用原始实例方法
3. **双标签驱动**：通过 YStatic 和 YStaticExtension 属性标记控制生成行为
4. **智能跳过**：自动跳过已经是静态的类，避免重复生成
5. **🆕 v1.3 优化**：增强的类型推断和更智能的扩展方法生成

### 设计目标 (v1.3)

- **静态标签生成**：为普通类生成对应的静态版本
- **扩展方法支持**：特殊处理扩展方法类
- **类型安全**：完全的编译时类型检查
- **独立运行**：不依赖依赖注入系统，纯静态标签生成功能
- **🆕 v1.3 增强**：优化的性能和更好的代码生成质量

## 📝 功能规范

### 1. 属性标记规则

#### YStatic - 普通类/方法静态标签生成

```csharp
// 类级属性：为整个类生成静态标签（除非方法有 YStaticIgnore）
[YStatic]
public class MathUtils
{
    public int Add(int a, int b) => a + b;              // ✅ 生成静态标签
    public int Multiply(int a, int b) => a * b;         // ✅ 生成静态标签
    [YStaticIgnore] public void Reset() { }             // ❌ 忽略，不生成

    // 私有方法不会生成静态标签
    private void InternalMethod() { }
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class MathUtilsExtensions
{
    private static readonly MathUtils _instance = new MathUtils();

    public static int Add(int a, int b) => _instance.Add(a, b);
    public static int Multiply(int a, int b) => _instance.Multiply(a, b);
    // Reset 被忽略，不生成
}
```

#### 方法级属性 (精细控制)

```csharp
public class StringUtils // 无类级属性
{
    [YStatic]
    public string FormatString(string input) => input.ToUpper();  // ✅ 有方法标签，生成

    [YStatic]
    public string GenerateId() => Guid.NewGuid().ToString();      // ✅ 有方法标签，生成

    // 无属性的方法不会生成静态标签
    public void InternalMethod() { }                              // ❌ 无标签，不生成
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class StringUtilsExtensions
{
    private static readonly StringUtils _instance = new StringUtils();

    public static string FormatString(string input) => _instance.FormatString(input);
    public static string GenerateId() => _instance.GenerateId();
}
```

#### YStaticExtension - 为非静态类生成静态扩展方法

**核心理解**：`YStaticExtension` 将非静态类的实例方法转换为静态扩展方法，第一个参数变为 `this` 参数。

```csharp
[YStaticExtension]  // 为非静态类生成扩展方法
public class StringHelper
{
    // 实例方法：处理字符串
    public string ToTitleCase(string input)
        => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.ToLower());

    public bool IsValidEmail(string email)
        => Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");

    public string Truncate(string input, int maxLength)
        => input.Length <= maxLength ? input : input.Substring(0, maxLength) + "...";
}

// 生成的静态扩展方法类：第一个参数变为 this 参数
public static class StringHelperExtensions
{
    private static readonly StringHelper _instance = new StringHelper();

    // 实例方法 → 扩展方法（第一个参数变为 this）
    public static string ToTitleCase(this string input) => _instance.ToTitleCase(input);
    public static bool IsValidEmail(this string email) => _instance.IsValidEmail(email);
    public static string Truncate(this string input, int maxLength) => _instance.Truncate(input, maxLength);
}

// 使用方式：自然的扩展方法调用
string title = "hello world".ToTitleCase();        // 而不是 StringHelper.ToTitleCase("hello world")
bool isValid = "<EMAIL>".IsValidEmail();  // 而不是 StringHelper.IsValidEmail("<EMAIL>")
string short = "long text".Truncate(10);           // 而不是 StringHelper.Truncate("long text", 10)
```

#### 混合使用示例

```csharp
public class MixedClass
{
    [YStatic] public void ProcessData(string data) { }            // ✅ 生成普通静态方法
    [YStaticExtension] public string FormatText(string input) { return input.ToUpper(); } // ✅ 生成扩展方法
    public void NoAttributeMethod() { }                           // ❌ 无标签，不生成
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class MixedClassExtensions
{
    private static readonly MixedClass _instance = new MixedClass();

    // YStatic 方法：普通静态方法
    public static void ProcessData(string data) => _instance.ProcessData(data);

    // YStaticExtension 方法：扩展方法（第一个参数变为 this）
    public static string FormatText(this string input) => _instance.FormatText(input);
}

// 使用方式
MixedClassExtensions.ProcessData("test");  // 普通静态调用
"hello".FormatText();                      // 扩展方法调用
```

### 2. 生成规则

#### 规则 0：标签互斥性

```csharp
// ❌ 错误：不能同时使用两个标签
[YStatic]
[YStaticExtension]
public class ConflictClass { }

// ❌ 错误：方法级也不能同时使用
public class TestClass
{
    [YStatic]
    [YStaticExtension]
    public static void ConflictMethod(this string s) { }
}
```

#### 规则 1：跳过已经是静态的类

```csharp
[YStatic] // ❌ 跳过：原类已经是静态的，无需生成
public static class AlreadyStaticClass { }
```

#### 规则 2：类级属性优先

```csharp
[YStatic] // ✅ 所有公共方法都生成静态标签（除非方法有 YStaticIgnore）
public class Utils
{
    public void Method1() { }                    // ✅ 实例方法 → 委托调用
    public void Method2() { }                    // ✅ 实例方法 → 委托调用
    public static void StaticMethod() { }        // ✅ 静态方法 → 直接调用
    private void Method3() { }                   // ❌ 私有方法不生成
    [YStaticIgnore] public void IgnoredMethod() { } // ❌ 明确忽略
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class UtilsExtensions
{
    private static readonly Utils _instance = new Utils();

    public static void Method1() => _instance.Method1();
    public static void Method2() => _instance.Method2();
    public static void StaticMethod() => Utils.StaticMethod(); // 保持静态功能
}
```

#### 规则 3：方法级属性精细控制

```csharp
public class MixedUtils // 无类级属性
{
    [YStatic]
    public void IncludedMethod() { } // ✅ 生成静态标签

    public void ExcludedMethod() { } // ❌ 无属性，不生成
}
```

#### 规则 4：YStaticExtension 用于非静态类

```csharp
[YStaticExtension]  // ✅ 用于非静态类，生成扩展方法
public class StringHelper
{
    // 实例方法：第一个参数将变为 this 参数
    public string Reverse(string input) => new string(input.Reverse().ToArray());
    public string Truncate(string input, int length) => input.Length <= length ? input : input.Substring(0, length);
    public bool IsValidEmail(string email) => Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
}

// 生成的扩展方法类
public static class StringHelperExtensions
{
    private static readonly StringHelper _instance = new StringHelper();

    // 第一个参数变为 this 参数
    public static string Reverse(this string input) => _instance.Reverse(input);
    public static string Truncate(this string input, int length) => _instance.Truncate(input, length);
    public static bool IsValidEmail(this string email) => _instance.IsValidEmail(email);
}

// 使用方式：自然的扩展方法调用
string reversed = "hello".Reverse();
string truncated = "long text".Truncate(5);
bool isValid = "<EMAIL>".IsValidEmail();
```

### 3. 生成策略

#### 普通类静态标签生成

```csharp
// 原始普通类
[YStatic]
public class MathUtils
{
    public int Add(int a, int b) => a + b;
    public int Multiply(int a, int b) => a * b;
    public static int Power(int a, int b) => (int)Math.Pow(a, b);  // 静态方法
    public void Reset() { /* 重置状态 */ }
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class MathUtilsExtensions
{
    // 使用静态实例委托调用
    private static readonly MathUtils _instance = new MathUtils();

    // 实例方法 → 委托调用
    public static int Add(int a, int b) => _instance.Add(a, b);
    public static int Multiply(int a, int b) => _instance.Multiply(a, b);
    public static void Reset() => _instance.Reset();

    // 静态方法 → 直接调用（保持原有功能）
    public static int Power(int a, int b) => MathUtils.Power(a, b);
}
```

#### 扩展方法生成策略

```csharp
// 原始非静态类
[YStaticExtension]
public class StringProcessor
{
    public string Reverse(string input) => new string(input.Reverse().ToArray());
    public bool IsEmpty(string input) => string.IsNullOrEmpty(input);
    public string Truncate(string input, int maxLength) =>
        input.Length <= maxLength ? input : input.Substring(0, maxLength) + "...";
}

// 生成的扩展方法类：第一个参数变为 this 参数
public static class StringProcessorExtensions
{
    private static readonly StringProcessor _instance = new StringProcessor();

    // 实例方法 → 扩展方法（第一个参数变为 this）
    public static string Reverse(this string input) => _instance.Reverse(input);
    public static bool IsEmpty(this string input) => _instance.IsEmpty(input);
    public static string Truncate(this string input, int maxLength) => _instance.Truncate(input, maxLength);
}

// 使用方式：自然的扩展方法调用
string reversed = "hello".Reverse();
bool isEmpty = "".IsEmpty();
string truncated = "long text here".Truncate(10);
```

## 🎯 使用场景

### 1. 普通类静态标签化

```csharp
[YStatic]
public class FileHelper
{
    public string ReadAllText(string path) => File.ReadAllText(path);
    public void WriteAllText(string path, string content) => File.WriteAllText(path, content);
    public bool FileExists(string path) => File.Exists(path);
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class FileHelperExtensions
{
    private static readonly FileHelper _instance = new FileHelper();

    public static string ReadAllText(string path) => _instance.ReadAllText(path);
    public static void WriteAllText(string path, string content) => _instance.WriteAllText(path, content);
    public static bool FileExists(string path) => _instance.FileExists(path);
}

// 使用静态标签
public class DocumentService
{
    public string LoadDocument(string path) => FileHelperExtensions.ReadAllText(path);
    public void SaveDocument(string path, string content) => FileHelperExtensions.WriteAllText(path, content);
}
```

### 2. 第三方库包装

```csharp
[YStatic]
public class JsonHelper
{
    public string Serialize<T>(T obj) => JsonSerializer.Serialize(obj);
    public T Deserialize<T>(string json) => JsonSerializer.Deserialize<T>(json);
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class JsonHelperExtensions
{
    private static readonly JsonHelper _instance = new JsonHelper();

    public static string Serialize<T>(T obj) => _instance.Serialize(obj);
    public static T Deserialize<T>(string json) => _instance.Deserialize<T>(json);
}
```

### 3. 配置和常量管理

```csharp
[YStatic]
public class AppConfig
{
    public string GetConnectionString() => Environment.GetEnvironmentVariable("DB_CONNECTION");
    public int GetTimeout() => int.Parse(Environment.GetEnvironmentVariable("TIMEOUT") ?? "30");
}

// 生成的扩展类（统一使用 Extensions 后缀）
public static class AppConfigExtensions
{
    private static readonly AppConfig _instance = new AppConfig();

    public static string GetConnectionString() => _instance.GetConnectionString();
    public static int GetTimeout() => _instance.GetTimeout();
}
```

### 🆕 4. 扩展方法生成

```csharp
[YStaticExtension]
public class StringValidator
{
    public string ToTitleCase(string input)
        => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input.ToLower());

    public bool IsValidEmail(string email)
        => Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");

    public string Truncate(string input, int maxLength)
        => input.Length <= maxLength ? input : input.Substring(0, maxLength) + "...";
}

// 生成的扩展方法类
public static class StringValidatorExtensions
{
    private static readonly StringValidator _instance = new StringValidator();

    public static string ToTitleCase(this string input) => _instance.ToTitleCase(input);
    public static bool IsValidEmail(this string email) => _instance.IsValidEmail(email);
    public static string Truncate(this string input, int maxLength) => _instance.Truncate(input, maxLength);
}

// 使用：自然的扩展方法调用
public class TextProcessor
{
    public string ProcessTitle(string title) => title.ToTitleCase();
    public bool ValidateEmail(string email) => email.IsValidEmail();
    public string ShortenText(string text) => text.Truncate(100);
}
```

### 🆕 5. 日期时间处理扩展方法

```csharp
[YStaticExtension]
public class DateTimeProcessor
{
    // 实例方法：将生成为扩展方法
    public string ToFriendlyString(DateTime dateTime)
        => dateTime.ToString("yyyy-MM-dd HH:mm:ss");

    public bool IsWeekend(DateTime dateTime)
        => dateTime.DayOfWeek == DayOfWeek.Saturday || dateTime.DayOfWeek == DayOfWeek.Sunday;

    public DateTime GetStartOfWeek(DateTime date)
        => date.AddDays(-(int)date.DayOfWeek);

    public DateTime GetEndOfMonth(DateTime date)
        => new DateTime(date.Year, date.Month, DateTime.DaysInMonth(date.Year, date.Month));
}

// 生成的扩展方法类
public static class DateTimeProcessorExtensions
{
    private static readonly DateTimeProcessor _instance = new DateTimeProcessor();

    // 第一个参数变为 this 参数
    public static string ToFriendlyString(this DateTime dateTime) => _instance.ToFriendlyString(dateTime);
    public static bool IsWeekend(this DateTime dateTime) => _instance.IsWeekend(dateTime);
    public static DateTime GetStartOfWeek(this DateTime date) => _instance.GetStartOfWeek(date);
    public static DateTime GetEndOfMonth(this DateTime date) => _instance.GetEndOfMonth(date);
}

// 使用：自然的扩展方法调用
DateTime now = DateTime.Now;
string friendly = now.ToFriendlyString();
bool isWeekend = now.IsWeekend();
DateTime startOfWeek = now.GetStartOfWeek();
DateTime endOfMonth = now.GetEndOfMonth();
```

## 🤝 与 YService 的关系

### 完全独立且可共存

YStatic 与 YService 是两个完全独立的功能，可以同时使用在同一个类上：

```csharp
[YServiceScoped]  // YService：生成依赖注入
[YStatic]         // YStatic：生成静态标签
public class StringProcessor
{
    public string ToUpper(string input) => input.ToUpper();
    public string ToLower(string input) => input.ToLower();

    [YStaticExtension]
    public string Reverse(string input) => new string(input.Reverse().ToArray());
}
```

### 生成的文件

```text
YService 生成：
├── IStringProcessor.YService.yg.cs     # 接口文件
├── ServiceRegistration.Assembly.yg.cs  # 服务注册
├── YServiceStatistics.Assembly.yg.cs   # 统计信息
└── YServiceErrorReport.Assembly.yg.cs  # 错误报告

YStatic 生成：
└── StringProcessorExtensions.ys.cs     # 静态扩展类
```

### YService 生成的代码

```csharp
// IStringProcessor.YService.yg.cs
public interface IStringProcessor
{
    string ToUpper(string input);
    string ToLower(string input);
    string Reverse(string input);
}

// ServiceRegistration.Assembly.yg.cs
public static IServiceCollection AddStringProcessor(this IServiceCollection services)
{
    services.AddScoped<IStringProcessor, StringProcessor>();
    return services;
}
```

### YStatic 生成的代码

```csharp
// StringProcessorExtensions.ys.cs
public static class StringProcessorExtensions
{
    private static readonly StringProcessor _instance = new StringProcessor();

    // YStatic：普通静态方法
    public static string ToUpper(string input) => _instance.ToUpper(input);
    public static string ToLower(string input) => _instance.ToLower(input);

    // YStaticExtension：扩展方法
    public static string Reverse(this string input) => _instance.Reverse(input);
}
```

### 使用场景对比

#### 1. 依赖注入场景（YService）

```csharp
public class DocumentService
{
    private readonly IStringProcessor _processor;

    public DocumentService(IStringProcessor processor)
    {
        _processor = processor;
    }

    public string ProcessTitle(string title) => _processor.ToUpper(title);
    public string ProcessContent(string content) => _processor.Reverse(content);
}
```

#### 2. 静态调用场景（YStatic）

```csharp
public class UtilityClass
{
    public void ProcessText()
    {
        // 普通静态调用
        string upper = StringProcessorExtensions.ToUpper("hello");
        string lower = StringProcessorExtensions.ToLower("WORLD");

        // 扩展方法调用
        string reversed = "text".Reverse();
    }
}
```

### 功能对比

| 特性 | YService | YStatic |
|------|----------|---------|
| **目的** | 依赖注入代码生成 | 静态标签生成 |
| **生成内容** | 接口 + 服务注册 | 静态类 + 扩展方法 |
| **使用方式** | 构造函数注入 | 静态调用/扩展方法 |
| **文件后缀** | `.yg.cs` | `.ys.cs` |
| **依赖关系** | 需要 DI 容器 | 无依赖 |
| **性能** | 接口调用 | 直接静态调用 |
| **测试友好** | 易于模拟 | 静态调用 |

### 选择指南

- **使用 YService**：需要依赖注入、单元测试、松耦合架构
- **使用 YStatic**：需要静态调用、工具方法、扩展方法
- **同时使用**：既需要依赖注入又需要静态调用的场景

## 🏗️ 架构设计

### 完全复用 YService 架构

```text
YStatic 架构 (基于 YService)
├── 📁 Attributes/
│   ├── YStaticAttribute.cs         # YStatic 属性定义
│   └── YStaticExtensionAttribute.cs # YStaticExtension 扩展方法属性定义
├── 📁 Helper/                      # 复用 YService 的所有工具
│   ├── YSyntaxAnalysisHelper.cs    # 复用：语法分析
│   ├── YMethodSignatureHelper.cs   # 复用：方法签名处理 (增强扩展方法支持)
│   └── YCodeIndentFormatter.cs     # 复用：代码格式化
├── 📁 Models/
│   └── YStaticModels.cs            # YStatic 专用数据模型 (支持扩展方法标记)
├── 📁 Processors/
│   ├── YStaticClassProcessor.cs    # YStatic 类级处理器 (支持扩展方法类)
│   └── YStaticMethodProcessor.cs   # YStatic 方法级处理器 (扩展方法特殊处理)
├── 📁 Generators/
│   └── YStaticGenerator.cs         # YStatic 主协调器
└── 📁 Temple/
    └── 📁 YStatic/                 # YStatic 代码生成层
        ├── YStaticCodeCoordinator.cs      # 代码生成协调器
        ├── StaticInterfaceGenerator.cs    # 静态接口生成器 (扩展方法this参数处理)
        ├── StaticWrapperGenerator.cs      # 静态包装器生成器 (扩展方法委托处理)
        └── StaticRegistrationGenerator.cs # 静态服务注册生成器
```

### 数据流向 (与 YService 一致)

```text
🔍 语法筛选 (YStaticGenerator)
    ↓ 候选识别
📋 任务分发 (YStaticGenerator)
    ↓ 分发到处理器
🔧 业务处理 (YStaticClassProcessor + YStaticMethodProcessor)
    ↓ 生成数据模型
🏗️ 代码协调 (YStaticCodeCoordinator)
    ↓ 协调多个生成器
📝 独立生成 (Interface + Wrapper + Registration)
    ↓ 输出
📁 .ys.cs 文件
```

## 💻 代码示例

### YStatic 基础用法

```csharp
// 1. 定义非静态类
[YStatic]
public class Calculator
{
    public int Add(int a, int b) => a + b;
    public int Subtract(int a, int b) => a - b;
    public double Divide(double a, double b) => a / b;
    public static int Multiply(int a, int b) => a * b;  // 静态方法
}

// 2. 自动生成的扩展类（统一使用 Extensions 后缀）
public static class CalculatorExtensions
{
    private static readonly Calculator _instance = new Calculator();

    // 实例方法 → 委托调用
    public static int Add(int a, int b) => _instance.Add(a, b);
    public static int Subtract(int a, int b) => _instance.Subtract(a, b);
    public static double Divide(double a, double b) => _instance.Divide(a, b);

    // 静态方法 → 直接调用
    public static int Multiply(int a, int b) => Calculator.Multiply(a, b);
}

// 3. 使用方式
int sum = CalculatorExtensions.Add(5, 3);
int product = CalculatorExtensions.Multiply(4, 6);
```

### YStaticExtension 扩展方法用法

```csharp
// 1. 定义非静态类
[YStaticExtension]
public class StringProcessor
{
    public string ToUpper(string input) => input.ToUpper();
    public string Reverse(string input) => new string(input.Reverse().ToArray());
    public bool IsValidEmail(string email) => Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
}

// 2. 自动生成的扩展方法类
public static class StringProcessorExtensions
{
    private static readonly StringProcessor _instance = new StringProcessor();

    // 第一个参数变为 this 参数
    public static string ToUpper(this string input) => _instance.ToUpper(input);
    public static string Reverse(this string input) => _instance.Reverse(input);
    public static bool IsValidEmail(this string email) => _instance.IsValidEmail(email);
}

// 3. 使用方式：自然的扩展方法调用
string upper = "hello".ToUpper();
string reversed = "world".Reverse();
bool isValid = "<EMAIL>".IsValidEmail();
```

### 方法级精细控制

```csharp
public class AdvancedUtils
{
    [YStatic]
    public string FormatDate(DateTime date) => date.ToString("yyyy-MM-dd");

    [YStaticExtension]
    public string GenerateToken(string prefix) => prefix + Guid.NewGuid().ToString("N");

    // 不包含在生成中
    public void InternalLog(string message) => Console.WriteLine(message);
}

// 生成一个扩展类（统一使用 Extensions 后缀）
public static class AdvancedUtilsExtensions
{
    private static readonly AdvancedUtils _instance = new AdvancedUtils();

    // YStatic 方法：普通静态方法
    public static string FormatDate(DateTime date) => _instance.FormatDate(date);

    // YStaticExtension 方法：扩展方法（第一个参数变为 this）
    public static string GenerateToken(this string prefix) => _instance.GenerateToken(prefix);
}
```

## 🔧 技术实现要点

### 1. 实例委托优化

- 使用静态只读实例，避免重复创建对象
- 实例方法委托调用，静态方法直接调用
- 支持泛型方法的完整保留

### 2. 类型安全保证

- 完整的编译时类型检查
- 泛型约束完全保留
- 参数和返回值类型精确匹配

### 3. 错误处理

- 编译时诊断：静态类使用 YStatic 属性（应跳过）
- 编译时诊断：同时使用 YStatic 和 YStaticExtension
- 运行时安全：实例调用的异常传播
- 详细的错误报告和建议

### 🏷️ **标签设计**

```csharp
// 主标签（互斥，不能同时使用）
[YStatic]           // 为非静态类生成普通静态方法
[YStaticExtension]  // 为非静态类生成静态扩展方法（第一个参数变为this）

// 补充标签
[YStaticIgnore]     // 忽略标签，覆盖类级标签

// 生成类命名规则：统一使用 Extensions 后缀
// {ClassName}Extensions

// ❌ 错误用法
[YStatic]
[YStaticExtension]  // 不能同时使用
public class ErrorClass { }

// ❌ 错误：不应该有 ServiceLifetime 参数
[YStaticExtension(ServiceLifetime.Singleton)]  // 错误！YStatic 功能不涉及依赖注入
```

## 🎉 总结

YStatic 功能将为 Zylo.Toolkit 带来强大的静态标签生成能力，完全基于 YService 的成熟架构，提供一致的开发体验和高质量的代码生成。通过委托模式实现，为普通类生成对应的静态标签版本。

### 🌟 核心特性总结

1. **双重属性支持（互斥）**：
   - `YStatic`：为非静态类生成普通静态方法
   - `YStaticExtension`：为非静态类生成静态扩展方法（第一个参数变为this）
   - **重要**：两个标签不能同时使用

2. **智能生成规则**：
   - 自动跳过已经是静态的类
   - 类级属性：为所有公共实例方法生成静态标签
   - 方法级属性：精细控制哪些方法生成静态标签
   - 扩展方法：自动识别并去除 this 参数

3. **与 YService 完全兼容**：
   - 可以与 YService 同时使用在同一个类上
   - 完全独立的功能，互不干扰
   - 提供依赖注入和静态调用两种方式

4. **完全架构复用**：
   - 基于 YService 成熟架构
   - 复用所有通用工具和设计模式
   - 保持一致的开发体验

5. **高性能实现**：
   - 委托模式保持方法调用性能
   - 编译时生成，运行时零开销
   - 类型安全的代码生成

### 🚀 应用价值

- **静态标签生成**：为普通类提供静态调用方式
- **扩展方法生成**：将实例方法转换为自然的扩展方法调用
- **调用方式多样化**：同一个类可以提供两种调用方式
  - `YStatic`：`ClassName.Method(param)`
  - `YStaticExtension`：`param.Method()`
- **与 YService 共存**：可以同时使用，提供依赖注入和静态调用两种方式
- **架构一致性**：与 YService 完全一致的使用体验
- **独立功能**：不依赖依赖注入，纯静态标签生成
- **灵活选择**：开发者可以根据场景选择最合适的调用方式
