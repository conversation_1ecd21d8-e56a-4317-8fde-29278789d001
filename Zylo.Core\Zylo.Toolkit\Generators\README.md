# Generators 生成器层

> 🎯 **设计目标**：提供高效的协调者，管理整个代码生成流程，不包含具体业务逻辑

这个目录包含了 YService 功能的核心协调者，负责设置增量生成器管道、候选识别和任务分发。

## 📋 生成器概览

### 🎯 YServiceGenerator.cs

**主协调者 - 管理整个代码生成流程**

#### 🎯 核心职责

- 🚀 管道协调：设置和管理增量生成器管道
- 🔍 候选识别：快速识别类级和方法级候选类
- 📋 任务分发：将处理任务分发给专门的处理器
- 🏗️ 结果整合：整合处理器结果并委托给代码生成器
- 🆕 v1.2 混合类处理：支持静态方法增强功能

#### 🔧 设计特点

- **纯协调者**：不包含任何业务逻辑
- **高性能**：使用增量生成器，只处理变化的代码
- **职责单一**：只负责协调，不处理具体业务
- **易于测试**：逻辑简单，测试容易

## 🏗️ 架构设计

### 协调者模式 (Mediator Pattern)

```csharp
public class YServiceGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 🎯 协调者：不包含业务逻辑，只负责分发
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: YServiceMethodProcessor.IsYServiceRelatedCandidate,  // 委托给处理器
            transform: GetYServiceInfo);                                     // 委托给处理器
        
        // 🏗️ 委托给代码生成器
        context.RegisterSourceOutput(candidates, YServiceCodeGenerator.GenerateCode);
    }
}
```

### 数据流向图

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  🔍 语法筛选     │ -> │  📋 任务分发     │ -> │  🔧 业务处理     │
│  快速候选识别    │    │  协调者分发      │    │  专用处理器      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ↓                       ↓                       ↓
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  🏗️ 模型构建     │ <- │  📝 信息提取     │ <- │  ⚙️ 配置解析     │
│  数据模型生成    │    │  文档和签名      │    │  属性参数提取    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ↓
┌─────────────────┐
│  🚀 代码生成     │
│  最终代码输出    │
└─────────────────┘
```

## 🔧 核心方法详解

### `Initialize()` - 生成器初始化

```csharp
public void Initialize(IncrementalGeneratorInitializationContext context)
{
    // 🔍 第一步：设置候选筛选管道
    var candidates = context.SyntaxProvider.CreateSyntaxProvider(
        predicate: static (s, _) => YServiceMethodProcessor.IsYServiceRelatedCandidate(s),
        transform: static (ctx, _) => GetYServiceInfo(ctx));

    // 🚫 第二步：过滤空结果
    var validCandidates = candidates.Where(static info => info is not null);

    // 🏗️ 第三步：注册代码生成
    context.RegisterSourceOutput(validCandidates, 
        static (spc, info) => YServiceCodeGenerator.GenerateCode(spc, info!));
}
```

#### 🎯 设计亮点

- **委托筛选**：将候选识别委托给专门的处理器
- **增量处理**：只处理变化的语法节点
- **空值过滤**：确保只处理有效的候选
- **类型安全**：使用静态方法确保类型安全

### `GetYServiceInfo()` - 信息提取协调

```csharp
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    var classDeclaration = (ClassDeclarationSyntax)context.Node;
    var classSymbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
    
    if (classSymbol == null) return null;

    // 🏷️ 第一优先级：检查类级属性 → 委托给类级处理器
    var yServiceAttribute = YServiceClassProcessor.GetYServiceAttribute(classSymbol);
    if (yServiceAttribute != null)
    {
        return YServiceClassProcessor.ProcessClassLevel(
            classDeclaration, classSymbol, context.SemanticModel, yServiceAttribute);
    }

    // 🔧 第二优先级：检查方法级属性 → 委托给方法级处理器
    if (YServiceMethodProcessor.HasMethodLevelYServiceAttributes(classDeclaration))
    {
        return YServiceMethodProcessor.ProcessMethodLevel(
            classDeclaration, classSymbol, context.SemanticModel);
    }

    return null;
}
```

#### 🎯 协调逻辑

1. **优先级处理**：类级属性优先于方法级属性
2. **委托分发**：根据属性类型分发给对应处理器
3. **结果统一**：所有处理器返回统一的 YServiceInfo 模型
4. **错误处理**：安全的空值检查和错误处理

## 🎨 设计模式

### 1. 协调者模式 (Mediator Pattern)

```csharp
// ✅ 协调者：不包含业务逻辑，只负责分发
public class YServiceGenerator : IIncrementalGenerator
{
    // 协调各个组件的交互
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 委托给处理器进行候选识别
        // 委托给处理器进行信息提取
        // 委托给代码生成器进行代码生成
    }
}
```

**优势**：

- 🎯 职责清晰：协调者只负责分发，不处理具体业务
- 🔧 易于扩展：新增处理器不影响协调者
- 🏗️ 松耦合：处理器之间不直接依赖

### 2. 管道模式 (Pipeline Pattern)

```csharp
// ✅ 管道：数据在各个阶段之间流转
语法节点 -> 候选筛选 -> 信息提取 -> 模型构建 -> 代码生成
```

**优势**：

- ⚡ 高性能：增量处理，只处理变化的部分
- 🔧 可组合：各个阶段可以独立优化
- 📊 可监控：每个阶段都可以独立监控和调试

### 3. 策略模式 (Strategy Pattern)

```csharp
// ✅ 策略：根据不同条件选择不同的处理策略
if (hasClassAttribute)
    return ClassProcessor.Process(...);  // 类级处理策略
if (hasMethodAttribute)
    return MethodProcessor.Process(...); // 方法级处理策略
```

## 🚀 性能优化

### 1. 增量生成器优势

```csharp
// ✅ 只处理变化的语法节点
context.SyntaxProvider.CreateSyntaxProvider(
    predicate: static (s, _) => IsCandidate(s),  // 快速筛选
    transform: static (ctx, _) => Transform(ctx) // 只转换候选
);
```

**性能特点**：

- **缓存机制**：未变化的节点使用缓存结果
- **并行处理**：多个节点可以并行处理
- **内存友好**：及时释放不需要的数据

### 2. 早期筛选优化

```csharp
// ✅ 在语法层面快速过滤
predicate: static (s, _) => 
    s is ClassDeclarationSyntax cls &&
    cls.AttributeLists.Count > 0 &&      // 有属性
    cls.Modifiers.Any(SyntaxKind.PartialKeyword)  // 是 partial
```

**优化效果**：

- **减少处理量**：过滤掉 90% 的无关节点
- **提高速度**：避免昂贵的语义分析
- **降低内存**：减少对象创建

## 💡 最佳实践

### 1. 保持协调者简洁

```csharp
// ✅ 好的协调者：简洁明了
public void Initialize(IncrementalGeneratorInitializationContext context)
{
    var candidates = context.SyntaxProvider.CreateSyntaxProvider(predicate, transform);
    context.RegisterSourceOutput(candidates, generate);
}

// ❌ 避免：在协调者中包含业务逻辑
public void Initialize(IncrementalGeneratorInitializationContext context)
{
    // 不要在这里处理具体的属性解析、方法提取等业务逻辑
}
```

### 2. 委托给专门的处理器

```csharp
// ✅ 委托给处理器
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    // 根据条件分发给不同的处理器
    if (condition1) return Processor1.Process(...);
    if (condition2) return Processor2.Process(...);
    return null;
}
```

### 3. 使用静态方法

```csharp
// ✅ 使用静态方法提高性能
context.SyntaxProvider.CreateSyntaxProvider(
    predicate: static (s, _) => IsCandidate(s),
    transform: static (ctx, _) => GetInfo(ctx)
);
```

### 4. 错误处理

```csharp
// ✅ 安全的错误处理
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    try
    {
        var classSymbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
        if (classSymbol == null) return null;
        
        // 处理逻辑...
    }
    catch (Exception)
    {
        // 记录错误但不中断生成过程
        return null;
    }
}
```

## 🔄 与其他组件的协作

### 输入依赖

- **Processors/YServiceMethodProcessor** - 候选识别和方法级处理
- **Processors/YServiceClassProcessor** - 类级处理
- **Models/YServiceInfo** - 数据模型定义

### 输出产物

- **调用 Temple/YServiceCodeGenerator** - 委托代码生成

### 协作流程

```
YServiceGenerator (协调者)
    ↓ 委托候选识别
YServiceMethodProcessor.IsYServiceRelatedCandidate()
    ↓ 委托信息提取
YServiceClassProcessor.ProcessClassLevel() / YServiceMethodProcessor.ProcessMethodLevel()
    ↓ 委托代码生成
YServiceCodeGenerator.GenerateCode()
```

## 🚀 扩展指南

### 添加新的处理器

```csharp
// 1. 在 GetYServiceInfo 中添加新的分发逻辑
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    // 现有逻辑...
    
    // 新的处理器
    if (NewProcessor.IsCandidate(classDeclaration))
    {
        return NewProcessor.Process(classDeclaration, classSymbol, context.SemanticModel);
    }
    
    return null;
}

// 2. 更新候选识别（如需要）
predicate: static (s, _) => 
    YServiceMethodProcessor.IsYServiceRelatedCandidate(s) ||
    NewProcessor.IsCandidate(s)
```

### 添加新的生成器

```csharp
// 创建新的生成器类
public class YControllerGenerator : IIncrementalGenerator
{
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 复用相同的架构模式
        var candidates = context.SyntaxProvider.CreateSyntaxProvider(
            predicate: YControllerProcessor.IsCandidate,
            transform: GetYControllerInfo);
        
        context.RegisterSourceOutput(candidates, YControllerCodeGenerator.Generate);
    }
}
```

---

> 💡 **提示**：YServiceGenerator 是整个架构的协调中心，保持其简洁和专注是架构成功的关键。所有的业务逻辑都应该委托给专门的处理器。
