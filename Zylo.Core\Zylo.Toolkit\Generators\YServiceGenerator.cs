using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Immutable;
using System.Linq;
using System.Text;
using Zylo.Toolkit.Temple.Yservice;


namespace Zylo.Toolkit.Generators
{
    /// <summary>
    /// YService 源代码生成器 - 协调者模式的主控制器
    ///
    /// 🎯 核心职责（重构后）：
    /// 1. 🚀 管道协调：设置和管理增量生成器管道
    /// 2. 🔍 候选识别：快速识别类级和方法级 YService 候选类
    /// 3. 📋 任务分发：将不同类型的处理任务分发给专门的处理器
    /// 4. 🏗️ 结果整合：整合处理器的结果并委托给代码生成器
    ///
    /// 🏗️ 协调者架构：
    /// ┌─────────────────────┐    ┌─────────────────────┐
    /// │  YServiceGenerator  │    │   处理器生态系统     │
    /// │     (协调者)        │ -> │ ClassLevelProcessor │
    /// │                     │    │ MethodLevelProcessor│
    /// └─────────────────────┘    └─────────────────────┘
    ///
    /// 💡 设计理念：
    /// - 协调者模式：不直接处理业务逻辑，专注于协调和分发
    /// - 职责分离：将类级和方法级处理分离到专门的处理器
    /// - 可扩展性：新的处理类型可以轻松添加新的处理器
    /// - 可测试性：每个处理器可以独立测试
    ///
    /// 🔧 处理流程：
    /// 1. 语法筛选：快速识别候选类
    /// 2. 任务分发：根据属性类型分发给对应处理器
    /// 3. 结果收集：收集处理器的结果
    /// 4. 代码生成：委托给 YServiceCodeGenerator
    /// </summary>
    [Generator]
    public class YServiceGenerator : IIncrementalGenerator
    {
        #region 🚀 生成器入口点

        /// <summary>
        /// 初始化增量生成器 - 协调者的核心配置入口
        ///
        /// 🎯 核心功能：
        /// 设置一个高效的"代码监控和分析管道"，实现：
        /// 1. 🔍 智能筛选：只关注可能包含 [YService] 的类
        /// 2. 📊 信息提取：提取类的详细信息用于代码生成
        /// 3. 🚀 代码生成：生成接口、实现和服务注册代码
        ///
        /// 💡 增量生成器的优势：
        /// - 智能缓存：只有相关代码变化时才重新分析
        /// - 内存友好：不会一次性加载所有代码到内存
        /// - 编译加速：显著减少编译时间，特别是大型项目
        /// </summary>
        /// <param name="context">生成器初始化上下文</param>
        public void Initialize(IncrementalGeneratorInitializationContext context)
        {
            // 🎯 第一阶段：语法筛选 - 快速过滤候选类
            var classDeclarations = context.SyntaxProvider
                .CreateSyntaxProvider(
                    // 🔧 协调者模式：委托给专门的处理器进行候选识别
                    predicate: static (s, _) => YServiceMethodProcessor.IsYServiceRelatedCandidate(s),
                    transform: static (ctx, _) => GetYServiceInfo(ctx))
                .Where(static m => m is not null);

            // 🎯 第二阶段：编译信息收集
            var compilationAndClasses = context.CompilationProvider.Combine(classDeclarations.Collect());

            // 🎯 第三阶段：代码生成执行
            context.RegisterSourceOutput(compilationAndClasses, static (spc, source) => Execute(source.Left, source.Right, spc));
        }

        #endregion

        #region 🔧 协调者方法

        /// <summary>
        /// 协调者方法 - 分发处理任务给专门的处理器
        ///
        /// 🎯 核心功能：
        /// 作为协调者，根据类的属性类型将处理任务分发给对应的专门处理器
        ///
        /// 🏗️ 协调流程：
        /// ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
        /// │  候选类识别      │ -> │  任务分发        │ -> │  结果收集        │
        /// │ 快速筛选候选     │    │ 委托给处理器     │    │ 返回统一结果     │
        /// └─────────────────┘    └─────────────────┘    └─────────────────┘
        ///
        /// 🔧 分发策略：
        /// - 有类级属性 → ClassLevelProcessor
        /// - 有方法级属性 → MethodLevelProcessor
        /// - 都没有 → 返回 null
        ///
        /// 💡 协调者优势：
        /// - 职责分离：每个处理器专注于自己的领域
        /// - 易于扩展：新的处理类型只需添加新的处理器
        /// - 易于测试：每个处理器可以独立测试
        /// </summary>
        /// <param name="context">生成器语法上下文</param>
        /// <returns>YService 信息对象，如果不是有效的 YService 类则返回 null</returns>
        private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
        {
            // 📥 获取基础数据
            var classDeclaration = (ClassDeclarationSyntax)context.Node;
            var semanticModel = context.SemanticModel;

            // 🔍 获取类的语义符号
            if (semanticModel.GetDeclaredSymbol(classDeclaration) is not INamedTypeSymbol classSymbol)
                return null;

            // 🎯 协调者模式：根据属性类型分发给对应的处理器

            // 🏷️ 检查类级属性
            var yServiceAttribute = YServiceClassProcessor.GetYServiceAttribute(classSymbol);

            if (yServiceAttribute != null)
            {
                // 📋 委托给类级处理器
                return YServiceClassProcessor.ProcessClassLevel(classDeclaration, classSymbol, semanticModel, yServiceAttribute);
            }

            // 🔧 检查方法级属性
            if (YServiceMethodProcessor.HasMethodLevelYServiceAttributes(classDeclaration))
            {
                // 📋 委托给方法级处理器
                return YServiceMethodProcessor.ProcessMethodLevel(classDeclaration, classSymbol, semanticModel);
            }

            // 🚫 既没有类级属性，也没有方法级属性
            return null;
        }

        #endregion

        #region 🚀 代码生成执行

        /// <summary>
        /// 执行代码生成 - 协调者的最终委托方法
        ///
        /// 🎯 核心功能：
        /// 将收集到的所有 YService 信息委托给专门的代码生成器进行代码生成
        ///
        /// 💡 协调者职责：
        /// - 数据验证：确保数据有效性
        /// - 委托生成：将实际的代码生成工作委托给 YServiceCodeGenerator
        /// - 错误处理：处理生成过程中的异常
        /// </summary>
        /// <param name="compilation">编译信息</param>
        /// <param name="yServiceInfos">YService 信息数组</param>
        /// <param name="context">源代码生成上下文</param>
        private static void Execute(Compilation compilation, ImmutableArray<YServiceInfo> yServiceInfos, SourceProductionContext context)
        {
            // 🚫 如果没有找到任何 YService 类，直接返回
            if (yServiceInfos.IsDefaultOrEmpty)
                return;

            // 🔧 过滤有效的信息
            var validInfos = yServiceInfos.Where(info => info != null).ToList();
            if (validInfos.Count == 0)
                return;

            // 🏗️ 获取程序集名称
            var assemblyName = compilation.AssemblyName ?? "UnknownAssembly";

            // 🚀 委托给新的协调器架构
            // 职责分离：YServiceGenerator 专注分析，YServiceCodeCoordinator 专注协调生成
            YServiceCodeCoordinator.ExecuteGeneration(context, validInfos, assemblyName);
        }

        #endregion

        #region 🛠️ 辅助方法

        /// <summary>
        /// 清理程序集名称 - 移除不适合用作标识符的字符
        ///
        /// 🎯 核心功能：
        /// 将程序集名称转换为适合用作 C# 标识符的格式
        ///
        /// 💡 处理规则：
        /// - 移除点号：MyProject.Core → MyProjectCore
        /// - 移除连字符：My-Project → MyProject
        /// - 移除空格：My Project → MyProject
        /// </summary>
        /// <param name="assemblyName">原始程序集名称</param>
        /// <returns>清理后的程序集名称</returns>
        private static string CleanAssemblyName(string assemblyName)
        {
            return assemblyName
                .Replace(".", "") // 移除点号：MyProject.Core → MyProjectCore
                .Replace("-", "") // 移除连字符：My-Project → MyProject
                .Replace(" ", ""); // 移除空格：My Project → MyProject
        }

        #endregion


    }
}
