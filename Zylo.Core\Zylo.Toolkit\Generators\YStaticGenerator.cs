using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System.Collections.Immutable;
using Zylo.Toolkit.Helper;
using Zylo.Toolkit.Models;
using Zylo.Toolkit.Processors;
using Zylo.Toolkit.Temple.YStatic;

namespace Zylo.Toolkit.Generators;

#region v1.3 升级 - YStatic 源代码生成器

/// <summary>
/// YStatic 源代码生成器 - 协调者模式的主控制器
///
/// 🎯 核心职责（基于 YService 架构）：
/// 1. 🚀 管道协调：设置和管理增量生成器管道
/// 2. 🔍 候选识别：快速识别类级和方法级 YStatic 候选类
/// 3. 📋 任务分发：将不同类型的处理任务分发给专门的处理器
/// 4. 🏗️ 结果整合：整合处理器的结果并委托给代码生成器
///
/// 🏗️ 协调者架构：
/// ┌─────────────────────┐    ┌─────────────────────┐
/// │  YStaticGenerator   │    │   处理器生态系统     │
/// │     (协调者)        │ -> │ ClassLevelProcessor │
/// │                     │    │ MethodLevelProcessor│
/// └─────────────────────┘    └─────────────────────┘
///
/// 💡 设计理念：
/// - 协调者模式：不直接处理业务逻辑，专注于协调和分发
/// - 职责分离：将类级和方法级处理分离到专门的处理器
/// - 可扩展性：新的处理类型可以轻松添加新的处理器
/// - 可测试性：每个处理器可以独立测试
///
/// 🔧 处理流程：
/// 1. 语法筛选：快速识别候选类
/// 2. 任务分发：根据属性类型分发给对应处理器
/// 3. 结果收集：收集处理器的结果
/// 4. 代码生成：委托给 YStaticCodeCoordinator 生成代码
/// </summary>
[Generator]
public class YStaticGenerator : IIncrementalGenerator
{
    #region 🚀 生成器初始化

    /// <summary>
    /// 初始化增量生成器 - 协调者的核心配置入口
    ///
    /// 🎯 核心功能：
    /// 设置一个高效的"代码监控和分析管道"，实现：
    /// 1. 🔍 智能筛选：只关注可能包含 [YStatic] 的类
    /// 2. 📊 信息提取：提取类的详细信息用于代码生成
    /// 3. 🚀 代码生成：生成静态扩展类和相关代码
    ///
    /// 💡 增量生成器的优势：
    /// - 智能缓存：只有相关代码变化时才重新分析
    /// - 内存友好：不会一次性加载所有代码到内存
    /// - 编译加速：显著减少编译时间，特别是大型项目
    /// </summary>
    /// <param name="context">生成器初始化上下文</param>
    public void Initialize(IncrementalGeneratorInitializationContext context)
    {
        // 🎯 第一阶段：语法筛选 - 快速过滤候选类
        var classDeclarations = context.SyntaxProvider
            .CreateSyntaxProvider(
                // 🔧 统一候选识别：检查类级和方法级属性
                predicate: static (s, _) => IsYStaticCandidate(s),
                transform: static (ctx, _) => GetYStaticInfo(ctx))
            .Where(static m => m is not null);

        // 🎯 第二阶段：收集和分组 - 将所有相关的类信息收集起来
        var allStaticInfos = classDeclarations.Collect();

        // 🎯 第三阶段：代码生成 - 委托给代码协调器生成最终代码
        context.RegisterSourceOutput(allStaticInfos,
            static (context, staticInfos) => ExecuteGeneration(context, staticInfos));
    }

    #endregion

    #region 🔍 候选识别

    /// <summary>
    /// 统一的 YStatic 候选识别
    ///
    /// 🎯 核心功能：
    /// 检查语法节点是否为 YStatic 相关的候选类（类级或方法级属性）
    ///
    /// 💡 检查条件：
    /// 1. 必须是类声明
    /// 2. 必须是 partial 类（生成代码需要）
    /// 3. 有类级 YStatic 属性 OR 有方法级 YStatic 属性
    /// </summary>
    /// <param name="node">语法节点</param>
    /// <returns>如果是候选返回 true，否则返回 false</returns>
    private static bool IsYStaticCandidate(SyntaxNode node)
    {
        // 🔍 第一步：检查是否是类声明
        if (node is not ClassDeclarationSyntax classDeclaration)
            return false;

        // 🔍 第二步：检查是否是 partial 类
        if (!YSyntaxAnalysisHelper.IsPartialClass(classDeclaration))
            return false;

        // 🔍 第三步：检查是否有类级或方法级 YStatic 属性
        // 检查类级属性
        if (HasClassLevelYStaticAttribute(classDeclaration))
            return true;

        // 检查方法级属性
        if (YStaticMethodProcessor.HasMethodLevelYStaticAttributes(classDeclaration))
            return true;

        return false;
    }

    /// <summary>
    /// 检查类是否有类级 YStatic 属性（语法层面）
    /// </summary>
    /// <param name="classDeclaration">类声明</param>
    /// <returns>如果有类级属性返回 true，否则返回 false</returns>
    private static bool HasClassLevelYStaticAttribute(ClassDeclarationSyntax classDeclaration)
    {
        return YSyntaxAnalysisHelper.HasClassAttribute(classDeclaration, "YStatic") ||
               YSyntaxAnalysisHelper.HasClassAttribute(classDeclaration, "YStaticExtension");
    }

    #endregion

    #region 🔧 信息提取和分发

    /// <summary>
    /// 获取 YStatic 信息 - 协调者的核心分发逻辑
    ///
    /// 🎯 核心功能：
    /// 作为协调者，根据不同的属性类型将处理任务分发给对应的专门处理器
    ///
    /// 💡 协调者模式的体现：
    /// - 不包含具体的业务处理逻辑
    /// - 只负责识别类型和分发任务
    /// - 让专门的处理器处理具体的业务逻辑
    ///
    /// 🔧 分发策略：
    /// 1. 🏷️ 检查类级属性 → 委托给类级处理器
    /// 2. 🔧 检查方法级属性 → 委托给方法级处理器
    /// 3. 🚫 都没有 → 返回 null
    ///
    /// 🚀 处理优先级：
    /// - 类级属性优先：如果类有类级属性，忽略方法级属性
    /// - 方法级属性：只在类没有类级属性时生效
    /// </summary>
    /// <param name="context">生成器语法上下文</param>
    /// <returns>YStatic 信息对象，如果不相关则返回 null</returns>
    private static YStaticInfo? GetYStaticInfo(GeneratorSyntaxContext context)
    {
        // 📥 获取基础数据
        var classDeclaration = (ClassDeclarationSyntax)context.Node;
        var semanticModel = context.SemanticModel;

        // 🔍 获取类的语义符号
        if (semanticModel.GetDeclaredSymbol(classDeclaration) is not INamedTypeSymbol classSymbol)
            return null;

        // 🎯 协调者模式：根据属性类型分发给对应的处理器

        // 🏷️ 检查类级属性
        var yStaticAttribute = YStaticClassProcessor.GetYStaticAttribute(classSymbol);

        if (yStaticAttribute != null)
        {
            // 📋 委托给类级处理器
            return YStaticClassProcessor.ProcessClassLevel(
                classDeclaration, classSymbol, semanticModel, yStaticAttribute);
        }

        // 🔧 检查方法级属性
        if (YStaticMethodProcessor.HasMethodLevelYStaticAttributes(classDeclaration))
        {
            // 📋 委托给方法级处理器
            return YStaticMethodProcessor.ProcessMethodLevel(classDeclaration, classSymbol, semanticModel);
        }

        // 🚫 没有找到相关属性
        return null;
    }

    #endregion

    #region 🏗️ 代码生成协调

    /// <summary>
    /// 执行代码生成 - 协调者的最终输出阶段
    ///
    /// 🎯 核心功能：
    /// 将收集到的所有 YStatic 信息委托给代码协调器进行最终的代码生成
    ///
    /// 💡 设计理念：
    /// - 职责分离：YStaticGenerator 专注分析，YStaticCodeCoordinator 专注协调生成
    /// - 协调者模式：不直接生成代码，而是委托给专门的代码协调器
    /// - 错误隔离：生成过程中的错误不会影响分析过程
    ///
    /// 🔧 处理流程：
    /// 1. 过滤有效的 YStatic 信息
    /// 2. 获取程序集名称
    /// 3. 委托给代码协调器生成代码
    ///
    /// 🚀 性能优化：
    /// - 早期过滤：只处理有效的信息
    /// - 批量处理：一次性处理所有相关的类
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="staticInfos">YStatic 信息数组</param>
    private static void ExecuteGeneration(SourceProductionContext context, ImmutableArray<YStaticInfo?> staticInfos)
    {
        // 🔍 调试信息：生成器被调用
        context.AddSource($"YStaticDebug{YStaticConstants.GeneratedFileExtension}", $@"// YStatic Generator Debug Info
// Generated at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
// Total candidates: {staticInfos.Length}
// Debug: YStatic generator was called!");

        // 🔍 第一步：过滤有效的信息
        var validInfos = staticInfos
            .Where(info => info != null)
            .Cast<YStaticInfo>()
            .Where(info => info.GenerateExtensions && info.HasMethods)
            .ToList();

        // 🛡️ 第二步：早期返回优化
        if (validInfos.Count == 0)
        {
            // 🔍 调试信息：没有有效的信息
            context.AddSource($"YStaticNoValidInfo{YStaticConstants.GeneratedFileExtension}", $@"// YStatic Generator Debug Info
// No valid YStatic info found
// Total candidates processed: {staticInfos.Length}
// Valid infos after filtering: 0");
            return;
        }

        // 🏗️ 第三步：获取程序集名称
        // 注意：这里我们使用一个简单的方法获取程序集名称
        // 在实际的 Roslyn 生成器中，可以通过 context 获取更准确的程序集信息
        var assemblyName = "GeneratedAssembly";

        // 🚀 第四步：委托给新的协调器架构
        // 职责分离：YStaticGenerator 专注分析，YStaticCodeCoordinator 专注协调生成
        YStaticCodeCoordinator.ExecuteGeneration(context, validInfos, assemblyName);
    }

    #endregion
}

#endregion
