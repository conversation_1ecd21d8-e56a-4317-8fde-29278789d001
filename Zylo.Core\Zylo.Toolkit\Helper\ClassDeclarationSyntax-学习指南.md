# ClassDeclarationSyntax 完全学习指南

## 📚 概述

`ClassDeclarationSyntax` 是 Roslyn 语法树中表示类声明的节点类型。它包含了类的所有语法信息，是源代码生成器分析类时最重要的对象。

## 🎯 基本概念

### 什么是 ClassDeclarationSyntax？

```csharp
// 这整个类声明会被解析为一个 ClassDeclarationSyntax 对象
[YService]                           // ← AttributeLists
public partial class UserService     // ← Modifiers + Identifier
    : BaseService, IUserService      // ← BaseList
    where T : class                  // ← ConstraintClauses
{                                    // ← OpenBraceToken
    public string Name { get; set; } // ← Members[0]
    public void DoWork() { }         // ← Members[1]
}                                    // ← CloseBraceToken
```

## 🔍 主要属性详解

### 1. Identifier - 类名

```csharp
// 示例代码
public class UserService { }

// 获取类名
string className = classDeclaration.Identifier.ValueText;  // "UserService"
string classNameWithTrivia = classDeclaration.Identifier.Text;  // 包含空格等
```

**🎯 小白理解**：就像人的姓名，`Identifier` 就是类的名字。

### 2. Modifiers - 修饰符

```csharp
// 示例代码
public static partial class UserService { }

// 检查修饰符
var modifiers = classDeclaration.Modifiers;
bool isPublic = modifiers.Any(SyntaxKind.PublicKeyword);     // true
bool isStatic = modifiers.Any(SyntaxKind.StaticKeyword);     // true  
bool isPartial = modifiers.Any(SyntaxKind.PartialKeyword);   // true
bool isAbstract = modifiers.Any(SyntaxKind.AbstractKeyword); // false
```

**🎯 小白理解**：修饰符就像人的标签，比如"医生"、"老师"，类的修饰符有"公共的"、"静态的"、"部分的"等。

### 3. AttributeLists - 属性列表

```csharp
// 示例代码
[YService(ServiceLifetime.Singleton)]
[Obsolete("Use NewService")]
[Serializable]
public class UserService { }

// 获取所有属性
var attributeLists = classDeclaration.AttributeLists;  // 3个属性列表
var allAttributes = attributeLists.SelectMany(list => list.Attributes);

// 检查特定属性
bool hasYService = allAttributes.Any(attr => attr.Name.ToString() == "YService");
```

**🎯 小白理解**：属性就像贴在类上的标签，告诉编译器这个类有什么特殊功能。

### 4. BaseList - 继承和接口

```csharp
// 示例代码
public class UserService : BaseService, IUserService, IDisposable { }

// 获取基类和接口
var baseList = classDeclaration.BaseList;
if (baseList != null)
{
    var types = baseList.Types;
    var baseClass = types.FirstOrDefault()?.Type.ToString();  // "BaseService"
    var interfaces = types.Skip(1).Select(t => t.Type.ToString());  // ["IUserService", "IDisposable"]
}
```

**🎯 小白理解**：就像家族关系，BaseList 告诉我们这个类继承自哪个父类，实现了哪些接口。

### 5. Members - 类成员

```csharp
// 示例代码
public class UserService 
{
    public string Name { get; set; }     // 属性成员
    private int _age;                    // 字段成员
    public void DoWork() { }             // 方法成员
    public UserService() { }             // 构造函数成员
}

// 获取不同类型的成员
var members = classDeclaration.Members;
var properties = members.OfType<PropertyDeclarationSyntax>();
var fields = members.OfType<FieldDeclarationSyntax>();
var methods = members.OfType<MethodDeclarationSyntax>();
var constructors = members.OfType<ConstructorDeclarationSyntax>();
```

**🎯 小白理解**：Members 就像类的"器官"，包括属性、方法、字段等，每个都有不同的功能。

## 🛠️ 实用方法示例

### 检查类的特征

```csharp
public static class ClassAnalyzer
{
    /// <summary>
    /// 检查类是否为抽象类
    /// </summary>
    public static bool IsAbstractClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Modifiers.Any(SyntaxKind.AbstractKeyword);
    }

    /// <summary>
    /// 检查类是否为静态类
    /// </summary>
    public static bool IsStaticClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Modifiers.Any(SyntaxKind.StaticKeyword);
    }

    /// <summary>
    /// 检查类是否为部分类
    /// </summary>
    public static bool IsPartialClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Modifiers.Any(SyntaxKind.PartialKeyword);
    }

    /// <summary>
    /// 获取类的访问级别
    /// </summary>
    public static string GetAccessLevel(ClassDeclarationSyntax classDecl)
    {
        if (classDecl.Modifiers.Any(SyntaxKind.PublicKeyword)) return "public";
        if (classDecl.Modifiers.Any(SyntaxKind.PrivateKeyword)) return "private";
        if (classDecl.Modifiers.Any(SyntaxKind.ProtectedKeyword)) return "protected";
        if (classDecl.Modifiers.Any(SyntaxKind.InternalKeyword)) return "internal";
        return "internal"; // 默认访问级别
    }
}
```

### 分析类的属性

```csharp
public static class AttributeAnalyzer
{
    /// <summary>
    /// 获取类上的所有属性名称
    /// </summary>
    public static List<string> GetAttributeNames(ClassDeclarationSyntax classDecl)
    {
        return classDecl.AttributeLists
            .SelectMany(list => list.Attributes)
            .Select(attr => attr.Name.ToString())
            .ToList();
    }

    /// <summary>
    /// 检查类是否有指定属性
    /// </summary>
    public static bool HasAttribute(ClassDeclarationSyntax classDecl, string attributeName)
    {
        return classDecl.AttributeLists
            .SelectMany(list => list.Attributes)
            .Any(attr => 
            {
                var name = attr.Name.ToString();
                return name == attributeName || name == $"{attributeName}Attribute";
            });
    }

    /// <summary>
    /// 获取指定属性的参数
    /// </summary>
    public static AttributeSyntax? GetAttribute(ClassDeclarationSyntax classDecl, string attributeName)
    {
        return classDecl.AttributeLists
            .SelectMany(list => list.Attributes)
            .FirstOrDefault(attr => 
            {
                var name = attr.Name.ToString();
                return name == attributeName || name == $"{attributeName}Attribute";
            });
    }
}
```

### 分析类的成员

```csharp
public static class MemberAnalyzer
{
    /// <summary>
    /// 获取类的所有公共方法
    /// </summary>
    public static List<MethodDeclarationSyntax> GetPublicMethods(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Members
            .OfType<MethodDeclarationSyntax>()
            .Where(method => method.Modifiers.Any(SyntaxKind.PublicKeyword))
            .ToList();
    }

    /// <summary>
    /// 获取类的所有属性
    /// </summary>
    public static List<PropertyDeclarationSyntax> GetProperties(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Members
            .OfType<PropertyDeclarationSyntax>()
            .ToList();
    }

    /// <summary>
    /// 检查类是否有构造函数
    /// </summary>
    public static bool HasConstructor(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Members
            .OfType<ConstructorDeclarationSyntax>()
            .Any();
    }

    /// <summary>
    /// 获取类的字段数量
    /// </summary>
    public static int GetFieldCount(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Members
            .OfType<FieldDeclarationSyntax>()
            .Count();
    }
}
```

## 🎯 实际应用示例

### 完整的类分析器

```csharp
public static class CompleteClassAnalyzer
{
    /// <summary>
    /// 获取类的完整分析报告
    /// </summary>
    public static string AnalyzeClass(ClassDeclarationSyntax classDecl)
    {
        var report = new StringBuilder();
        
        // 基本信息
        report.AppendLine($"类名: {classDecl.Identifier.ValueText}");
        report.AppendLine($"访问级别: {GetAccessLevel(classDecl)}");
        report.AppendLine($"是否为部分类: {IsPartialClass(classDecl)}");
        report.AppendLine($"是否为静态类: {IsStaticClass(classDecl)}");
        report.AppendLine($"是否为抽象类: {IsAbstractClass(classDecl)}");
        
        // 属性信息
        var attributes = GetAttributeNames(classDecl);
        report.AppendLine($"属性: [{string.Join(", ", attributes)}]");
        
        // 继承信息
        if (classDecl.BaseList != null)
        {
            var baseTypes = classDecl.BaseList.Types.Select(t => t.Type.ToString());
            report.AppendLine($"继承/实现: {string.Join(", ", baseTypes)}");
        }
        
        // 成员统计
        var methodCount = classDecl.Members.OfType<MethodDeclarationSyntax>().Count();
        var propertyCount = classDecl.Members.OfType<PropertyDeclarationSyntax>().Count();
        var fieldCount = classDecl.Members.OfType<FieldDeclarationSyntax>().Count();
        
        report.AppendLine($"方法数量: {methodCount}");
        report.AppendLine($"属性数量: {propertyCount}");
        report.AppendLine($"字段数量: {fieldCount}");
        
        return report.ToString();
    }
}
```

## 💡 最佳实践

### 1. 安全检查

```csharp
// ✅ 好的做法：先检查是否为 null
if (classDeclaration.BaseList != null)
{
    // 处理继承信息
}

// ❌ 不好的做法：直接访问可能为 null 的属性
var baseTypes = classDeclaration.BaseList.Types; // 可能抛出 NullReferenceException
```

### 2. 使用 LINQ 简化代码

```csharp
// ✅ 简洁的写法
var publicMethods = classDeclaration.Members
    .OfType<MethodDeclarationSyntax>()
    .Where(m => m.Modifiers.Any(SyntaxKind.PublicKeyword));

// ❌ 复杂的写法
var publicMethods = new List<MethodDeclarationSyntax>();
foreach (var member in classDeclaration.Members)
{
    if (member is MethodDeclarationSyntax method)
    {
        foreach (var modifier in method.Modifiers)
        {
            if (modifier.IsKind(SyntaxKind.PublicKeyword))
            {
                publicMethods.Add(method);
                break;
            }
        }
    }
}
```

### 3. 缓存常用结果

```csharp
// ✅ 缓存重复计算的结果
public class ClassInfo
{
    private readonly ClassDeclarationSyntax _classDecl;
    private List<string>? _attributeNames;
    
    public List<string> AttributeNames => 
        _attributeNames ??= GetAttributeNames(_classDecl);
}
```

## 🚀 总结

`ClassDeclarationSyntax` 是分析类的强大工具，掌握它的关键是：

1. **理解结构**：知道每个属性代表什么
2. **安全访问**：注意 null 检查
3. **善用 LINQ**：简化代码逻辑
4. **实践应用**：在实际项目中多练习

通过这些知识，您就能在源代码生成器中自如地分析和处理类了！

## 🔬 高级主题

### 泛型类处理

```csharp
// 示例：泛型类
public class Repository<T, TKey> where T : class where TKey : struct
{
}

// 分析泛型参数
public static class GenericAnalyzer
{
    /// <summary>
    /// 检查类是否为泛型类
    /// </summary>
    public static bool IsGenericClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.TypeParameterList != null &&
               classDecl.TypeParameterList.Parameters.Count > 0;
    }

    /// <summary>
    /// 获取泛型参数名称
    /// </summary>
    public static List<string> GetGenericParameterNames(ClassDeclarationSyntax classDecl)
    {
        if (classDecl.TypeParameterList == null)
            return new List<string>();

        return classDecl.TypeParameterList.Parameters
            .Select(p => p.Identifier.ValueText)
            .ToList();
    }

    /// <summary>
    /// 获取泛型约束
    /// </summary>
    public static Dictionary<string, List<string>> GetGenericConstraints(ClassDeclarationSyntax classDecl)
    {
        var constraints = new Dictionary<string, List<string>>();

        if (classDecl.ConstraintClauses == null)
            return constraints;

        foreach (var clause in classDecl.ConstraintClauses)
        {
            var parameterName = clause.Name.Identifier.ValueText;
            var constraintList = clause.Constraints
                .Select(c => c.ToString())
                .ToList();

            constraints[parameterName] = constraintList;
        }

        return constraints;
    }
}
```

### 嵌套类处理

```csharp
// 示例：嵌套类
public class OuterClass
{
    public class InnerClass
    {
        private class PrivateInnerClass { }
    }
}

// 分析嵌套类
public static class NestedClassAnalyzer
{
    /// <summary>
    /// 检查类是否为嵌套类
    /// </summary>
    public static bool IsNestedClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Parent is ClassDeclarationSyntax;
    }

    /// <summary>
    /// 获取外部类
    /// </summary>
    public static ClassDeclarationSyntax? GetOuterClass(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Parent as ClassDeclarationSyntax;
    }

    /// <summary>
    /// 获取所有嵌套类
    /// </summary>
    public static List<ClassDeclarationSyntax> GetNestedClasses(ClassDeclarationSyntax classDecl)
    {
        return classDecl.Members
            .OfType<ClassDeclarationSyntax>()
            .ToList();
    }

    /// <summary>
    /// 获取完整的类名（包含外部类）
    /// </summary>
    public static string GetFullClassName(ClassDeclarationSyntax classDecl)
    {
        var names = new List<string>();
        var current = classDecl;

        while (current != null)
        {
            names.Insert(0, current.Identifier.ValueText);
            current = current.Parent as ClassDeclarationSyntax;
        }

        return string.Join(".", names);
    }
}
```

### 命名空间处理

```csharp
public static class NamespaceAnalyzer
{
    /// <summary>
    /// 获取类所在的命名空间
    /// </summary>
    public static string GetNamespace(ClassDeclarationSyntax classDecl)
    {
        var namespaceDecl = classDecl.Ancestors()
            .OfType<NamespaceDeclarationSyntax>()
            .FirstOrDefault();

        if (namespaceDecl != null)
        {
            return namespaceDecl.Name.ToString();
        }

        // 检查文件作用域命名空间 (C# 10+)
        var fileScopedNamespace = classDecl.Ancestors()
            .OfType<FileScopedNamespaceDeclarationSyntax>()
            .FirstOrDefault();

        return fileScopedNamespace?.Name.ToString() ?? "";
    }

    /// <summary>
    /// 获取完整的类型名称（包含命名空间）
    /// </summary>
    public static string GetFullTypeName(ClassDeclarationSyntax classDecl)
    {
        var namespaceName = GetNamespace(classDecl);
        var className = NestedClassAnalyzer.GetFullClassName(classDecl);

        return string.IsNullOrEmpty(namespaceName)
            ? className
            : $"{namespaceName}.{className}";
    }
}
```

## 🐛 常见陷阱和解决方案

### 1. 空引用异常

```csharp
// ❌ 危险的代码
var baseClass = classDeclaration.BaseList.Types.First().Type.ToString();

// ✅ 安全的代码
var baseClass = classDeclaration.BaseList?.Types.FirstOrDefault()?.Type.ToString();
```

### 2. 属性名称匹配

```csharp
// ❌ 不完整的匹配
bool hasYService = attributes.Any(attr => attr.Name.ToString() == "YService");

// ✅ 完整的匹配（考虑 Attribute 后缀）
bool hasYService = attributes.Any(attr =>
{
    var name = attr.Name.ToString();
    return name == "YService" || name == "YServiceAttribute";
});
```

### 3. 修饰符检查

```csharp
// ❌ 错误的检查方式
bool isPublic = classDeclaration.Modifiers.ToString().Contains("public");

// ✅ 正确的检查方式
bool isPublic = classDeclaration.Modifiers.Any(SyntaxKind.PublicKeyword);
```

## 📝 调试技巧

### 1. 打印语法树结构

```csharp
public static void PrintSyntaxTree(ClassDeclarationSyntax classDecl)
{
    Console.WriteLine($"类名: {classDecl.Identifier.ValueText}");
    Console.WriteLine($"修饰符: {string.Join(" ", classDecl.Modifiers)}");
    Console.WriteLine($"属性: {classDecl.AttributeLists.Count} 个");
    Console.WriteLine($"成员: {classDecl.Members.Count} 个");
    Console.WriteLine($"语法树: {classDecl.ToFullString()}");
}
```

### 2. 验证生成的代码

```csharp
public static bool ValidateGeneratedCode(string generatedCode)
{
    try
    {
        var syntaxTree = CSharpSyntaxTree.ParseText(generatedCode);
        var diagnostics = syntaxTree.GetDiagnostics();
        return !diagnostics.Any(d => d.Severity == DiagnosticSeverity.Error);
    }
    catch
    {
        return false;
    }
}
```

## 🎓 练习题

### 初级练习

1. 编写方法检查类是否为 public partial class
2. 获取类的所有公共属性名称
3. 检查类是否实现了特定接口

### 中级练习

1. 分析类的继承层次结构
2. 提取类的所有带参数的构造函数
3. 检查类是否有循环依赖

### 高级练习

1. 实现完整的类依赖分析器
2. 生成类的 UML 类图描述
3. 创建类的代码质量评估工具

通过这些练习，您将完全掌握 ClassDeclarationSyntax 的使用！
