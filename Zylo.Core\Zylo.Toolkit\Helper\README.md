# Helper 工具类目录 - 通用工具集合

> 🎯 **设计目标**：提供高质量、可复用的通用工具，支持任何代码生成器功能

这个目录包含了 Zylo.Toolkit 框架的通用工具类，专门为源代码生成器设计。所有工具都遵循**通用性**、**可复用性**和**高性能**的设计原则。

## 📋 工具分类概览

### 🔍 语法分析工具

- **YSyntaxAnalysisHelper.cs** - 语法分析和快速筛选工具

### 🔧 方法签名处理工具

- **YMethodSignatureHelper.cs** - 通用方法签名处理工具

### 📝 文档处理工具

- **YXmlDocumentationExtractor.cs** - XML 文档注释提取器
- **YDocumentationProcessor.cs** - 通用文档格式化处理器
- **YDocumentationTemplateGenerator.cs** - 文档注释模板生成器

### 🎨 代码格式化工具

- **YCodeIndentFormatter.cs** - 代码缩进格式化器

### 📚 学习资源

- **ClassDeclarationSyntax-学习指南.md** - 语法分析学习指南

## 💡 核心设计理念

### 1. 通用性原则

- ✅ 所有工具都可被任何功能使用（YService、YController、YRepository）
- ✅ 不包含业务特定逻辑，只提供技术工具
- ✅ 接口设计考虑多种使用场景

### 2. 性能优化原则

- ✅ 预制常量避免重复字符串创建
- ✅ 早期筛选减少后续处理负担
- ✅ 静态方法避免实例创建开销

### 3. 易用性原则

- ✅ 清晰的方法命名和参数设计
- ✅ 详细的 XML 文档注释
- ✅ 支持 `using static` 简化调用

### 4. 可扩展性原则

- ✅ 模块化设计，功能独立
- ✅ 支持参数化配置
- ✅ 为未来功能预留扩展空间

## 🔧 详细功能说明

### 1. SyntaxAnalysisHelper.cs

#### 🎯 核心功能：语法分析和快速筛选的"侦探工具箱"

```csharp
/// 主要功能分类：
/// 🏗️ 类相关筛选
/// 🔧 方法相关筛选
/// 🏷️ 通用属性检测
/// 🎯 候选识别
/// 🛠️ 辅助工具
```

**核心方法**：

- `IsAttributedClass()` - 检查是否是有属性的类
- `IsPartialClass()` - 检查是否是 partial 类
- `IsClassLevelYServiceCandidate()` - 检查类级 YService 候选
- `HasMethodAttribute()` - 通用方法属性检测 ⭐
- `HasClassAttribute()` - 通用类属性检测 ⭐
- `HasAnyMethodAttribute()` - 批量属性检测 ⭐
- `HasMethodsWithAttributes()` - 检查类是否有指定属性的方法
- `GetMethodAttributeType()` - 获取方法属性类型

**🚀 适用场景**：

- YService、YController、YRepository 等任何功能
- 快速语法筛选和候选识别
- 通用属性检测和验证

### 2. MethodSignatureHelper.cs

#### 🎯 核心功能：通用的方法签名处理工具

```csharp
/// 主要功能分类：
/// 🔧 方法参数处理
/// 🔧 泛型参数处理
/// 🔧 类型约束处理
/// 🔧 完整签名生成
```

**核心方法**：

- `GetParametersString()` - 获取方法参数字符串 ⭐
- `GetTypeParametersString()` - 获取泛型参数字符串 ⭐
- `GetTypeConstraintsString()` - 获取类型约束字符串 ⭐
- `GetTypeConstraints()` - 获取类型约束列表
- `GetFullMethodSignature()` - 生成完整方法签名
- `GetFullMethodDeclaration()` - 生成带约束的完整方法声明

**🚀 适用场景**：

- 接口生成：自动生成方法签名
- 代码生成：处理复杂的泛型方法
- 文档生成：生成方法签名文档

### 3. XmlDocumentationExtractor.cs

#### 🎯 核心功能：专业的 XML 文档注释提取器

```csharp
/// 主要功能分类：
/// 🔍 公共提取接口
/// 🔧 核心处理引擎
/// 🛠️ 辅助工具方法
```

**核心方法**：

- `ExtractFromMethod()` - 从方法提取文档注释 ⭐
- `ExtractFromClass()` - 从类提取文档注释 ⭐
- `ExtractFromProperty()` - 从属性提取文档注释
- `ExtractFromSyntaxNode()` - 通用语法节点提取

**🚀 适用场景**：

- 接口生成：保留原始文档注释
- API 文档：提取完整的 XML 文档
- 代码分析：获取注释信息

### 4. DocumentationProcessor.cs

#### 🎯 核心功能：通用文档注释格式化处理器

```csharp
/// 主要功能分类：
/// 🎨 通用格式化工具
```

**核心方法**：

- `AdjustIndentation()` - 调整文档注释缩进 ⭐
- `ProcessDocumentationLines()` - 处理文档注释行

**🚀 适用场景**：

- 文档注释缩进调整
- 跨项目的通用格式化
- 标准化文档注释格式

### 5. DocumentationTemplateGenerator.cs

#### 🎯 核心功能：文档注释模板生成和智能选择

```csharp
/// 主要功能分类：
/// 🛠️ 默认模板生成
/// 🧠 智能选择逻辑
```

**核心方法**：

- `GenerateInterfaceTemplate()` - 生成接口文档模板 ⭐
- `GenerateMethodTemplate()` - 生成方法文档模板 ⭐
- `GetOptimalDocumentation()` - 智能选择最佳注释 ⭐

**🚀 适用场景**：

- 自动生成默认文档注释
- 智能选择注释策略
- 特定场景的模板生成

### 6. CodeIndentFormatter.cs

#### 🎯 核心功能：代码缩进格式化的完整解决方案

```csharp
/// 主要功能分类：
/// 📏 预制缩进常量 (I0-I7)
/// 🔧 动态缩进方法
/// 🌊 StringBuilder 扩展方法
```

**核心功能**：

- **预制常量**：`I0`(0级) 到 `I7`(28级) 覆盖常用场景 ⭐
- **动态方法**：`Indent(level)` 处理任意深度 ⭐
- **扩展方法**：`sb.AppendLine(indent, content)` 流畅调用 ⭐

**🚀 适用场景**：

- 代码生成：统一缩进管理
- 模板生成：保持代码格式一致
- 任何需要缩进的文本处理

## 🚀 使用指南

### 基本使用方式

```csharp
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;
using Zylo.Toolkit.Helper;

// 1. 语法分析
if (YSyntaxAnalysisHelper.IsAttributedClass(node))
{
    // 2. 属性检测
    if (YSyntaxAnalysisHelper.HasMethodAttribute(method, "YServiceIgnore"))
        return;

    // 3. 方法签名处理
    var parameters = YMethodSignatureHelper.GetParametersString(methodSymbol);
    var signature = YMethodSignatureHelper.GetFullMethodSignature(methodSymbol);

    // 4. 文档注释处理
    var originalDoc = YXmlDocumentationExtractor.ExtractFromMethod(method);
    var template = YDocumentationTemplateGenerator.GenerateMethodTemplate("MyMethod");
    var finalDoc = YDocumentationTemplateGenerator.GetOptimalDocumentation(originalDoc, template);

    // 5. 代码生成
    var sb = new StringBuilder();
    sb.YAppendLine(I1, finalDoc)
      .YAppendLine(I1, $"public {signature};");
}
```

### 高级使用模式

```csharp
// 批量属性检测
string[] attributes = ["YServiceIgnore", "Obsolete", "EditorBrowsable"];
if (SyntaxAnalysisHelper.HasAnyMethodAttribute(method, attributes))
    return;

// 属性类型映射
var mapping = new Dictionary<string, string>
{
    { "YServiceScoped", "Scoped" },
    { "YServiceSingleton", "Singleton" }
};
var attributeType = SyntaxAnalysisHelper.GetMethodAttributeType(method, mapping);

// 智能文档选择
var documentation = DocumentationTemplateGenerator.GetOptimalDocumentation(
    originalDoc: XmlDocumentationExtractor.ExtractFromMethod(method),
    defaultTemplate: DocumentationTemplateGenerator.GenerateMethodTemplate(methodName),
    indentLevel: I2
);
```

## 🎯 最佳实践

### 1. 推荐的引用方式

```csharp
// 静态引用常用工具
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

// 命名空间引用其他工具
using Zylo.Toolkit.Helper;
```

### 2. 性能优化建议

```csharp
// ✅ 使用预制常量（推荐）
sb.YAppendLine(I2, $"public class {className}");

// ❌ 避免动态生成（除非必要）
sb.YAppendLine(Indent(2), $"public class {className}");
```

### 3. 错误处理模式

```csharp
// ✅ 使用 null 表示无效结果
var attribute = YSyntaxAnalysisHelper.GetMethodAttributeType(method, mapping);
if (attribute == null) return;

// ✅ 早期返回避免深层嵌套
if (!YSyntaxAnalysisHelper.IsAttributedClass(node)) return;
```

## 📊 快速参考表

| 工具类 | 主要功能 | 核心方法 | 使用频率 |
|--------|----------|----------|----------|
| **YSyntaxAnalysisHelper** | 语法分析筛选 | `IsAttributedClass()`, `HasMethodAttribute()` | ⭐⭐⭐⭐⭐ |
| **YMethodSignatureHelper** | 方法签名处理 | `GetParametersString()`, `GetFullMethodSignature()` | ⭐⭐⭐⭐⭐ |
| **YCodeIndentFormatter** | 代码缩进格式化 | `I0-I7`, `Indent()`, `YAppendLine()` | ⭐⭐⭐⭐⭐ |
| **YXmlDocumentationExtractor** | XML文档提取 | `ExtractFromMethod()`, `ExtractFromClass()` | ⭐⭐⭐⭐ |
| **YDocumentationProcessor** | 文档格式化 | `AdjustIndentation()` | ⭐⭐⭐ |
| **YDocumentationTemplateGenerator** | 文档模板生成 | `GetOptimalDocumentation()` | ⭐⭐⭐ |

## 🎯 使用优先级指南

### 1. **必须掌握** (⭐⭐⭐⭐⭐)

- `YSyntaxAnalysisHelper` - 语法分析的基础
- `YMethodSignatureHelper` - 方法签名处理核心
- `YCodeIndentFormatter` - 所有代码生成都需要

### 2. **重要工具** (⭐⭐⭐⭐)

- `YXmlDocumentationExtractor` - 保留文档注释

### 3. **辅助工具** (⭐⭐⭐)

- `YDocumentationProcessor` - 文档格式化
- `YDocumentationTemplateGenerator` - 智能模板

## 📚 学习资源

- **ClassDeclarationSyntax-学习指南.md** - 深入学习语法分析的详细指南

---

## 💡 总结

这些工具类是 Zylo 框架的核心基础设施，为所有代码生成功能提供统一、高效的技术支持。

**核心优势**：

- 🏗️ **真正通用**：可被任何功能使用（YService、YController、YRepository）
- 🔧 **高度可复用**：避免重复造轮子，提高开发效率
- 🎯 **性能优化**：预制常量、早期筛选、静态方法设计
- 📋 **标准化**：统一的接口设计和编码规范

> 💡 **提示**：在开发新功能时，优先考虑复用这些通用工具，避免重复开发。好的工具让复杂的代码生成变得简单而优雅！
