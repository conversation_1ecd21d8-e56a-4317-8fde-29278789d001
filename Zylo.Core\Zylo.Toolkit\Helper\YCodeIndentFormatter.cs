using System.Text;

namespace Zylo.Toolkit.Helper;

/// <summary>
/// 代码缩进格式化器 - 预制常量 + 动态方法混合策略的静态工具类
///
/// 🎯 设计理念：
/// 预制常量覆盖 90% 的常用场景，动态方法处理特殊需求，让代码生成更优雅
///
/// 💡 核心优势：
/// 1. 🚀 性能优化：常用缩进使用预制常量，避免重复字符串创建
/// 2. 🔧 灵活应对：动态方法处理任意深度的嵌套需求
/// 3. 👀 代码对齐：让所有 $ 插值符号完美对齐，提高可读性
/// 4. 📝 易于维护：统一的缩进管理，修改缩进规则只需要改一个地方
///
/// 🏗️ 架构设计：
/// - **静态常量**：I0-I7 预制缩进常量，覆盖常用场景
/// - **静态方法**：Indent() 动态缩进生成，处理特殊需求
/// - **扩展方法**：YStringBuilderIndentExtensions 提供流畅的链式调用
///
/// 📝 使用示例：
/// ```csharp
/// using static Zylo.Toolkit.Helper.YCodeIndentFormatter;
///
/// var sb = new StringBuilder();
///
/// // 预制常量方式（推荐）
/// sb.YAppendLine(I2, $"public class {className}")
///   .YAppendLine(I2, "{")
///   .YAppendLine(I3, $"public void {methodName}()");
///
/// // 动态方法方式（特殊情况）
/// sb.YAppendLine(8, "// 深层嵌套注释");
///
/// // 混合使用
/// sb.YAppendLine(I3, "{")
///   .YAppendLine(Indent(specialLevel), specialContent)
///   .YAppendLine(I3, "}");
/// ```
///
/// 🔍 缩进级别说明：
/// - I0 (0级)：文件级别，无缩进
/// - I1 (1级)：命名空间级别，4个空格
/// - I2 (2级)：类级别，8个空格
/// - I3 (3级)：方法级别，12个空格
/// - I4 (4级)：代码块级别，16个空格
/// - I5+ (5级以上)：深层嵌套，20个空格以上
///
/// 💡 使用建议：
/// 1. 添加 `using static` 语句以直接使用常量
/// 2. 优先使用预制常量 (I0-I7) 获得最佳性能
/// 3. 只在必要时使用 Indent() 方法处理特殊缩进
/// 4. 利用扩展方法的链式调用提高代码可读性
/// </summary>
public static class YCodeIndentFormatter
{
    #region 🚀 预制缩进常量 - 覆盖常用场景

    /// <summary>0级缩进：文件级别（无缩进）</summary>
    public static readonly string I0 = "";

    /// <summary>1级缩进：命名空间级别（4个空格）</summary>
    public static readonly string I1 = "    ";

    /// <summary>2级缩进：类级别（8个空格）</summary>
    public static readonly string I2 = "        ";

    /// <summary>3级缩进：方法级别（12个空格）</summary>
    public static readonly string I3 = "            ";

    /// <summary>4级缩进：代码块级别（16个空格）</summary>
    public static readonly string I4 = "                ";

    /// <summary>5级缩进：深层嵌套（20个空格）</summary>
    public static readonly string I5 = "                    ";

    /// <summary>6级缩进：特深嵌套（24个空格）</summary>
    public static readonly string I6 = "                        ";

    /// <summary>7级缩进：极深嵌套（28个空格）</summary>
    public static readonly string I7 = "                            ";

    #endregion

    #region 🔧 缓存和动态生成

    /// <summary>
    /// 缓存数组 - 快速访问预制缩进常量的高性能查找表
    ///
    /// 🎯 设计目的：
    /// 将所有预制缩进常量组织成数组，支持通过索引快速访问
    ///
    /// 💡 性能优势：
    /// - 数组访问：O(1) 时间复杂度，比字典查找更快
    /// - 内存友好：只存储引用，不重复创建字符串
    /// - 缓存命中：95% 的缩进需求都能直接命中缓存
    ///
    /// 🔍 数组内容：
    /// [0] = I0 = ""                    (文件级别)
    /// [1] = I1 = "    "                (命名空间级别)
    /// [2] = I2 = "        "            (类级别)
    /// [3] = I3 = "            "        (方法级别)
    /// [4] = I4 = "                "    (代码块级别)
    /// [5] = I5 = "                    " (深层嵌套)
    /// [6] = I6 = "                        " (特深嵌套)
    /// [7] = I7 = "                            " (极深嵌套)
    ///
    /// 📊 使用统计（预估）：
    /// - I0-I4：占 90% 的使用场景
    /// - I5-I7：占 5% 的使用场景
    /// - 8级以上：占 5% 的使用场景（动态生成）
    /// </summary>
    private static readonly string[] IndentCache = [I0, I1, I2, I3, I4, I5, I6, I7];

    /// <summary>
    /// 动态缩进方法 - 处理任意级别的缩进需求的智能分发器
    ///
    /// 🎯 核心功能：
    /// 这是整个缩进系统的核心方法，负责根据缩进级别返回对应的缩进字符串
    /// 采用"缓存优先 + 动态后备"的混合策略，兼顾性能和灵活性
    ///
    /// 🔧 智能分发策略：
    /// ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    /// │   缓存命中检查   │ -> │   缓存直接返回   │    │   动态字符串生成 │
    /// │  (0-7级缩进)    │    │  (预制常量)     │    │  (8级以上缩进)  │
    /// │  95%的使用场景   │    │  性能最优       │    │  灵活应对       │
    /// └─────────────────┘    └─────────────────┘    └─────────────────┘
    ///
    /// 💡 性能优化原理：
    /// 1. **缓存命中**：常用级别直接返回预制字符串，避免重复创建
    ///    - 时间复杂度：O(1)
    ///    - 内存开销：零（复用现有字符串）
    ///    - 垃圾回收：无压力（不创建新对象）
    ///
    /// 2. **动态生成**：特殊级别按需创建，保证功能完整性
    ///    - 时间复杂度：O(n)，n = 缩进级别
    ///    - 内存开销：按需分配
    ///    - 使用频率：极低（&lt; 5%）
    ///
    /// 🔍 边界条件处理：
    /// - level &lt; 0：返回空字符串（防御性编程）
    /// - level = 0：返回 I0（无缩进）
    /// - level 1-7：返回对应预制常量
    /// - level &gt;= 8：动态生成（new string(' ', level * 4)）
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// Indent(0)  // 返回 I0 = ""
    /// Indent(2)  // 返回 I2 = "        "
    /// Indent(10) // 返回 "                                        " (40个空格)
    /// Indent(-1) // 返回 "" (防御性处理)
    /// ```
    ///
    /// 🚀 调用频率分析：
    /// 在典型的代码生成场景中，此方法会被频繁调用：
    /// - 每行生成的代码都需要缩进
    /// - 大型项目可能调用数万次
    /// - 缓存策略显著提升整体性能
    /// </summary>
    /// <param name="level">缩进级别（每级 = 4个空格，0表示无缩进）</param>
    /// <returns>对应级别的缩进字符串，保证非null</returns>
    public static string Indent(int level)
    {
        // 🚀 第一优先级：缓存命中检查（覆盖 95% 的使用场景）
        //
        // 🔍 条件解析：
        // level >= 0: 确保不是负数（防御性编程）
        // level < IndentCache.Length: 确保在缓存范围内（0-7级）
        //
        // 💡 为什么用 if 而不是 switch？
        // - 范围判断：需要检查一个区间，不是具体值
        // - 性能考虑：简单的比较操作，编译器优化效果好
        // - 可读性：逻辑清晰，易于理解
        if (level >= 0 && level < IndentCache.Length)
        {
            // 🎯 直接返回预制常量
            // IndentCache[level]: 数组访问，O(1) 时间复杂度
            // 返回的是预制字符串的引用，不创建新对象
            return IndentCache[level];
        }

        // 🔧 第二优先级：动态生成（处理极特殊的深度嵌套）
        //
        // 🎯 使用场景：
        // - 8级以上的深度嵌套（极少见）
        // - 负数级别的防御性处理
        //
        // 💡 实现原理：
        // level > 0: 确保是正数，负数返回空字符串
        // new string(' ', level * 4): 创建指定长度的空格字符串
        // level * 4: 每级缩进 = 4个空格（与预制常量保持一致）
        //
        // 🔍 性能考虑：
        // - 这个分支执行频率极低（< 5%）
        // - 即使创建新字符串，对整体性能影响微乎其微
        // - 保证了功能的完整性和健壮性
        return level > 0 ? new string(' ', level * 4) : "";
    }

    #endregion
}

/// <summary>
/// StringBuilder 缩进扩展方法集合
///
/// 🎯 设计理念：
/// 将所有缩进相关的方法设计为 StringBuilder 的扩展方法，使用更自然
///
/// 💡 使用优势：
/// 1. 🎯 链式调用：sb.YAppendLine(I2, "content").YAppendEmptyLine()
/// 2. 📝 自然语法：sb.AppendXmlDocComment(I2, "summary")
/// 3. 🔧 易于发现：IDE 智能提示中直接显示
/// 4. ⚡ 性能优化：直接操作 StringBuilder，无需中间对象
///
/// 📝 使用示例：
/// ```csharp
/// var sb = new StringBuilder();
/// sb.YAppendLine(I2, "/// <summary>")
///   .YAppendLine(I2, "/// 用户服务类")
///   .YAppendLine(I2, "/// </summary>")
///   .YAppendLine(I2, "public class UserService");
/// ```
/// </summary>
public static class YStringBuilderIndentExtensions
{
    #region 📝 核心缩进方法 - 高频使用的基础构建块

    /// <summary>
    /// 添加带缩进的行（使用预制常量）- 推荐的主要方法
    ///
    /// 🎯 核心功能：
    /// 这是最常用的代码生成方法，将缩进和内容组合后添加到 StringBuilder
    /// 使用预制缩进常量，性能最优，覆盖 90% 的使用场景
    ///
    /// 💡 设计优势：
    /// 1. **性能最优**：使用预制常量，避免字符串创建开销
    /// 2. **类型安全**：编译时检查缩进常量的正确性
    /// 3. **链式调用**：返回 StringBuilder，支持流畅的链式语法
    /// 4. **默认参数**：content 可选，支持只添加缩进的空行
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendLine(I2, "public class UserService")     // 类声明
    ///   .YAppendLine(I2, "{")                            // 开始大括号
    ///   .YAppendLine(I3, "public void GetUser()")        // 方法声明
    ///   .YAppendLine(I3, "{")                            // 方法开始
    ///   .YAppendLine(I4, "// 方法实现")                   // 方法内容
    ///   .YAppendLine(I3, "}")                            // 方法结束
    ///   .YAppendLine(I2, "}");                           // 类结束
    /// ```
    ///
    /// 🔍 参数说明：
    /// - indent：预制缩进常量（I0, I1, I2, I3, I4, I5, I6, I7）
    /// - content：行内容，默认为空字符串（用于生成只有缩进的空行）
    ///
    /// ⚡ 性能特性：
    /// - 字符串插值：$"{indent}{content}" 编译器优化
    /// - 无额外分配：直接使用预制常量
    /// - 链式返回：支持方法链，提高代码可读性
    /// </summary>
    /// <param name="sb">StringBuilder 实例 - 代码内容的容器</param>
    /// <param name="indent">预制缩进常量（如 I2, I3）- 推荐使用预制常量以获得最佳性能</param>
    /// <param name="content">行内容 - 要添加的代码内容，默认为空字符串</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendLine(this StringBuilder sb, string indent, string content = "")
    {
        // 🔧 核心实现：字符串插值 + YAppendLine
        //
        // 💡 为什么用字符串插值？
        // - 编译器优化：C# 编译器会优化字符串插值的性能
        // - 可读性好：代码意图清晰明了
        // - 类型安全：编译时检查参数类型
        //
        // 🎯 执行流程：
        // 1. 字符串插值：将 indent 和 content 组合
        // 2. YAppendLine：添加到 StringBuilder 并自动换行
        // 3. 返回 sb：支持链式调用
        sb.AppendLine($"{indent}{content}");
        return sb;
    }

    /// <summary>
    /// 添加带缩进的行（使用动态级别）- 灵活的备用方法
    ///
    /// 🔧 核心功能：
    /// 当预制常量无法满足需求时使用的灵活方法，支持任意级别的缩进
    /// 内部调用 YCodeIndentFormatter.Indent() 方法动态生成缩进
    ///
    /// 💡 适用场景：
    /// 1. **深度嵌套**：超过 7 级的深度嵌套代码
    /// 2. **动态缩进**：运行时计算的缩进级别
    /// 3. **特殊需求**：需要非标准缩进级别的场景
    /// 4. **算法生成**：通过算法计算缩进级别的代码生成
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// int dynamicLevel = CalculateIndentLevel(); // 运行时计算
    /// sb.YAppendLine(dynamicLevel, "// 动态缩进的注释")
    ///   .YAppendLine(8, "// 8级深度嵌套")
    ///   .YAppendLine(10, "// 10级深度嵌套");
    /// ```
    ///
    /// ⚡ 性能考虑：
    /// - 动态计算：可能涉及字符串创建（8级以上）
    /// - 缓存优化：0-7级仍使用预制常量
    /// - 使用频率：相对较低，性能影响有限
    ///
    /// 🔍 与预制常量方法的对比：
    /// | 特性 | 预制常量方法 | 动态级别方法 |
    /// |------|-------------|-------------|
    /// | 性能 | 最优 | 良好 |
    /// | 灵活性 | 有限(0-7级) | 无限制 |
    /// | 类型安全 | 编译时检查 | 运行时检查 |
    /// | 推荐度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
    /// </summary>
    /// <param name="sb">StringBuilder 实例 - 代码内容的容器</param>
    /// <param name="level">缩进级别 - 每级代表4个空格，支持任意正整数</param>
    /// <param name="content">行内容 - 要添加的代码内容，默认为空字符串</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendLine(this StringBuilder sb, int level, string content = "")
    {
        // 🔧 核心实现：动态缩进 + 字符串插值 + YAppendLine
        //
        // 💡 实现原理：
        // 1. YCodeIndentFormatter.Indent(level)：获取对应级别的缩进字符串
        //    - 0-7级：返回预制常量（性能最优）
        //    - 8级以上：动态生成（灵活应对）
        // 2. 字符串插值：组合缩进和内容
        // 3. YAppendLine：添加到 StringBuilder
        //
        // 🎯 设计考虑：
        // - 复用逻辑：利用 YCodeIndentFormatter 的智能分发
        // - 统一接口：与预制常量方法保持一致的调用方式
        // - 性能平衡：在灵活性和性能之间找到平衡点
        sb.AppendLine($"{YCodeIndentFormatter.Indent(level)}{content}");
        return sb;
    }

    /// <summary>
    /// 添加空行 - 代码格式化的基础工具
    ///
    /// 🎯 核心功能：
    /// 在生成的代码中添加空行，用于改善代码的可读性和结构化
    /// 这是代码生成中最简单但最重要的格式化工具之一
    ///
    /// 💡 使用场景：
    /// 1. **方法分隔**：在不同方法之间添加空行
    /// 2. **逻辑分组**：将相关代码块分组
    /// 3. **文件结构**：在命名空间、类、方法之间添加空行
    /// 4. **可读性提升**：避免代码过于紧凑
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendLine(I2, "public class UserService")
    ///   .YAppendLine(I2, "{")
    ///   .YAppendEmptyLine()                    // 类开始后的空行
    ///   .YAppendLine(I3, "public void Method1()")
    ///   .YAppendLine(I3, "{ }")
    ///   .YAppendEmptyLine()                    // 方法之间的空行
    ///   .YAppendLine(I3, "public void Method2()")
    ///   .YAppendLine(I3, "{ }")
    ///   .YAppendEmptyLine()                    // 类结束前的空行
    ///   .YAppendLine(I2, "}");
    /// ```
    ///
    /// ⚡ 性能特性：
    /// - 零开销：直接调用 StringBuilder.YAppendLine()
    /// - 无字符串创建：不涉及任何字符串操作
    /// - 链式支持：返回 StringBuilder 支持方法链
    ///
    /// 🎨 代码美学：
    /// 适当的空行是优秀代码的标志，它让生成的代码：
    /// - 更易阅读：视觉上的分隔效果
    /// - 更易维护：逻辑结构清晰
    /// - 更专业：符合代码规范和最佳实践
    /// </summary>
    /// <param name="sb">StringBuilder 实例 - 代码内容的容器</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendEmptyLine(this StringBuilder sb)
    {
        // 🔧 最简实现：直接调用 YAppendLine()
        //
        // 💡 为什么不直接用 sb.YAppendLine()？
        // 1. **一致性**：保持扩展方法的统一风格
        // 2. **链式调用**：返回 StringBuilder 支持方法链
        // 3. **语义清晰**：YAppendEmptyLine 比 YAppendLine() 更明确
        // 4. **未来扩展**：为可能的功能扩展预留空间
        sb.AppendLine();
        return sb;
    }

    /// <summary>
    /// 添加多个空行 - 批量空行生成工具
    ///
    /// 🎯 核心功能：
    /// 一次性添加多个空行，用于创建更大的视觉分隔效果
    /// 在代码生成中用于分隔不同的代码段落或模块
    ///
    /// 💡 使用场景：
    /// 1. **模块分隔**：在不同功能模块之间添加多个空行
    /// 2. **文件分段**：将生成的文件分成不同的逻辑段落
    /// 3. **视觉强调**：通过多个空行强调重要的代码分界
    /// 4. **格式规范**：遵循特定的代码格式规范
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendLine(I0, "// === 第一部分：属性定义 ===")
    ///   .YAppendLine(I1, "public string Name { get; set; }")
    ///   .YAppendEmptyLines(3)                  // 3个空行分隔
    ///   .YAppendLine(I0, "// === 第二部分：方法定义 ===")
    ///   .YAppendLine(I1, "public void DoSomething() { }");
    /// ```
    ///
    /// ⚡ 性能实现：
    /// - 简单循环：使用 for 循环，性能可预测
    /// - 无额外开销：每次循环只调用一次 YAppendLine()
    /// - 边界安全：自动处理 count &lt;= 0 的情况
    ///
    /// 🔍 实现细节：
    /// - 循环次数：精确控制空行数量
    /// - 异常安全：count 为负数或零时不执行任何操作
    /// - 链式返回：保持与其他扩展方法的一致性
    ///
    /// 💡 设计考虑：
    /// 虽然可以通过多次调用 YAppendEmptyLine() 实现相同效果，
    /// 但提供专门的批量方法有以下优势：
    /// - **代码简洁**：一行代码替代多行调用
    /// - **意图明确**：明确表达"添加多个空行"的意图
    /// - **性能优化**：减少方法调用开销
    /// - **参数化**：支持动态控制空行数量
    /// </summary>
    /// <param name="sb">StringBuilder 实例 - 代码内容的容器</param>
    /// <param name="count">空行数量 - 要添加的空行数，必须为非负整数</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendEmptyLines(this StringBuilder sb, int count)
    {
        // 🔧 核心实现：简单而高效的循环
        //
        // 💡 循环设计：
        // - 使用传统 for 循环：性能最优，逻辑清晰
        // - 边界处理：count <= 0 时循环不执行，自然处理边界情况
        // - 无额外检查：相信调用者传入合理参数，保持性能
        //
        // 🎯 为什么不用 LINQ 或其他方式？
        // - Enumerable.Range(0, count).ForEach：创建额外对象，性能较差
        // - StringBuilder.Append(new string('\n', count))：不符合 YAppendLine 语义
        // - 递归调用：可能导致栈溢出，且性能不如循环
        //
        // 🔍 异常处理策略：
        // 采用"宽松处理"策略：
        // - count < 0：不抛异常，不执行任何操作（防御性编程）
        // - count = 0：不抛异常，不执行任何操作（合理的边界情况）
        // - count > 0：正常执行（预期的使用场景）
        for (int i = 0; i < count; i++)
        {
            // 每次循环添加一个空行
            // 使用 YAppendLine() 而不是 YAppendEmptyLine() 避免额外的方法调用开销
            sb.AppendLine();
        }

        // 🔗 链式调用支持：返回 StringBuilder 实例
        return sb;
    }

    #endregion

    #region 🎨 高级格式化方法 - 专业代码生成工具

    /// <summary>
    /// 添加带缩进的代码块开始标记 - 代码块生成助手
    ///
    /// 🎯 核心功能：
    /// 专门用于生成代码块的开始部分，通常是左大括号 "{"
    /// 提供统一的代码块开始格式，确保生成代码的一致性
    ///
    /// 💡 设计理念：
    /// 代码块是结构化代码的基本单元，提供专门的方法可以：
    /// - 统一格式：确保所有代码块的格式一致
    /// - 语义清晰：明确表达"开始代码块"的意图
    /// - 易于维护：集中管理代码块格式规则
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendLine(I2, "public class UserService")
    ///   .AppendBlockStart(I2)               // 添加 "{"
    ///   .YAppendLine(I3, "public void GetUser()")
    ///   .AppendBlockStart(I3)               // 添加 "    {"
    ///   .YAppendLine(I4, "// 方法实现")
    ///   .YAppendBlockEnd(I3)                 // 添加 "    }"
    ///   .YAppendBlockEnd(I2);                // 添加 "}"
    /// ```
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder AppendBlockStart(this StringBuilder sb, string indent)
    {
        return sb.YAppendLine(indent, "{");
    }

    /// <summary>
    /// 添加带缩进的代码块结束标记 - 代码块生成助手
    ///
    /// 🎯 核心功能：
    /// 专门用于生成代码块的结束部分，通常是右大括号 "}"
    /// 与 AppendBlockStart 配对使用，形成完整的代码块结构
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendBlockEnd(this StringBuilder sb, string indent)
    {
        return sb.YAppendLine(indent, "}");
    }

    /// <summary>
    /// 添加带缩进的单行注释 - 注释生成工具
    ///
    /// 🎯 核心功能：
    /// 生成标准的 C# 单行注释，自动添加 "//" 前缀
    /// 支持多种注释风格和格式化选项
    ///
    /// 💡 注释的重要性：
    /// 生成的代码应该包含适当的注释，帮助用户理解：
    /// - 代码的用途和功能
    /// - 自动生成的标识
    /// - 使用说明和注意事项
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendComment(I2, "这是一个自动生成的类")
    ///   .YAppendComment(I2, "请不要手动修改此文件")
    ///   .YAppendLine(I2, "public class GeneratedClass");
    /// ```
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <param name="comment">注释内容</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendComment(this StringBuilder sb, string indent, string comment)
    {
        return sb.YAppendLine(indent, $"// {comment}");
    }

    /// <summary>
    /// 添加带缩进的分隔注释行 - 视觉分隔工具
    ///
    /// 🎯 核心功能：
    /// 生成视觉分隔效果的注释行，用于分隔代码的不同部分
    /// 提供多种分隔符样式，增强代码的可读性
    ///
    /// 💡 视觉分隔的价值：
    /// 在生成的代码中，适当的视觉分隔可以：
    /// - 提高可读性：清晰地分隔不同功能模块
    /// - 增强结构感：让代码层次更加分明
    /// - 便于维护：快速定位和理解代码结构
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendSeparator(I2)                    // "// ========================================"
    ///   .YAppendComment(I2, "属性定义部分")
    ///   .YAppendSeparator(I2)
    ///   .YAppendLine(I2, "public string Name { get; set; }");
    /// ```
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <param name="separator">分隔符字符，默认为 '='</param>
    /// <param name="length">分隔符长度，默认为 40</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendSeparator(this StringBuilder sb, string indent, char separator = '=', int length = 40)
    {
        var separatorLine = new string(separator, length);
        return sb.YAppendLine(indent, $"// {separatorLine}");
    }

    /// <summary>
    /// 添加带标题的分隔注释块 - 高级分隔工具
    ///
    /// 🎯 核心功能：
    /// 生成带标题的分隔块，用于标识重要的代码段落
    /// 结合分隔符和标题，提供最强的视觉分隔效果
    ///
    /// 📝 生成格式：
    /// ```
    /// // ========================================
    /// // 标题内容
    /// // ========================================
    /// ```
    ///
    /// 💡 使用场景：
    /// - 文件头部：标识文件的用途和生成信息
    /// - 主要模块：分隔类的不同功能模块
    /// - 重要段落：突出显示关键代码段
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <param name="title">标题内容</param>
    /// <param name="separator">分隔符字符，默认为 '='</param>
    /// <param name="length">分隔符长度，默认为 40</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder AppendTitleBlock(this StringBuilder sb, string indent, string title, char separator = '=', int length = 40)
    {
        var separatorLine = new string(separator, length);
        return sb.YAppendLine(indent, $"// {separatorLine}")
                 .YAppendLine(indent, $"// {title}")
                 .YAppendLine(indent, $"// {separatorLine}");
    }

    #endregion

    #region 🔧 实用工具方法 - 代码生成辅助功能

    /// <summary>
    /// 批量添加多行内容 - 批量内容生成工具
    ///
    /// 🎯 核心功能：
    /// 一次性添加多行内容，每行使用相同的缩进
    /// 适用于批量生成结构相似的代码行
    ///
    /// 💡 使用场景：
    /// 1. **批量属性**：生成多个属性定义
    /// 2. **批量方法**：生成多个方法声明
    /// 3. **批量注释**：添加多行注释内容
    /// 4. **模板展开**：将模板内容批量展开
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var properties = new[] {
    ///     "public string Name { get; set; }",
    ///     "public int Age { get; set; }",
    ///     "public string Email { get; set; }"
    /// };
    /// sb.YAppendLines(I3, properties);
    /// ```
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="indent">缩进字符串</param>
    /// <param name="lines">要添加的行内容数组</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendLines(this StringBuilder sb, string indent, params string[] lines)
    {
        foreach (var line in lines)
        {
            sb.YAppendLine(indent, line);
        }
        return sb;
    }

    /// <summary>
    /// 添加条件内容 - 条件代码生成工具
    ///
    /// 🎯 核心功能：
    /// 根据条件决定是否添加内容，简化条件代码生成逻辑
    /// 避免在调用代码中使用大量的 if 语句
    ///
    /// 💡 设计价值：
    /// 在代码生成中，经常需要根据条件决定是否生成某些代码
    /// 这个方法将条件判断封装在扩展方法中，让调用代码更简洁
    ///
    /// 📝 使用示例：
    /// ```csharp
    /// var sb = new StringBuilder();
    /// sb.YAppendLine(I2, "public class UserService")
    ///   .AppendBlockStart(I2)
    ///   .YAppendIf(hasConstructor, I3, "public UserService() { }")
    ///   .YAppendIf(hasGetMethod, I3, "public User GetUser(int id) { }")
    ///   .YAppendBlockEnd(I2);
    /// ```
    /// </summary>
    /// <param name="sb">StringBuilder 实例</param>
    /// <param name="condition">条件表达式</param>
    /// <param name="indent">缩进字符串</param>
    /// <param name="content">条件为真时添加的内容</param>
    /// <returns>StringBuilder 实例，支持链式调用</returns>
    public static StringBuilder YAppendIf(this StringBuilder sb, bool condition, string indent, string content)
    {
        if (condition)
        {
            sb.YAppendLine(indent, content);
        }
        return sb;
    }

    #endregion


}