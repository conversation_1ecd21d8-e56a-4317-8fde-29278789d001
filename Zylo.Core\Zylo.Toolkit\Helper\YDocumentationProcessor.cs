using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

namespace Zylo.Toolkit.Helper;

/// <summary>
/// 通用文档注释处理器 - 专注于格式化和缩进调整的通用工具
///
/// 🎯 核心功能：
/// - 调整文档注释的缩进以适应不同的代码结构
/// - 处理换行符和空行
/// - 通用的文本格式化功能
///
/// 💡 设计原理：
/// - 专注于格式化，不涉及提取和模板生成
/// - 可被其他标签和生成器重用
/// - 支持多种缩进级别和格式要求
/// - 保持功能单一，职责明确
///
/// 🔧 使用场景：
/// - 任何需要调整文档注释缩进的地方
/// - 标准化文档注释格式
/// - 跨项目的通用格式化工具
///
/// 📝 架构分工：
/// - YXmlDocumentationExtractor：负责从语法树提取XML
/// - YDocumentationProcessor：负责通用格式化（本类）
/// - YDocumentationTemplateGenerator：负责生成模板和智能选择
/// </summary>
public static class YDocumentationProcessor
{
    #region 🎨 通用格式化工具

    /// <summary>
    /// 通用的文档注释缩进调整方法 - 核心格式化引擎
    ///
    /// 🎯 核心功能：
    /// 统一处理所有类型的文本缩进，支持不同的缩进级别
    ///
    /// 💡 处理流程：
    /// 1. 分割文本为行
    /// 2. 清理每行的原有缩进
    /// 3. 应用新的统一缩进
    /// 4. 移除多余的空行
    /// 5. 确保正确的换行符
    ///
    /// 🔧 使用场景：
    /// - 文档注释缩进调整
    /// - 代码块缩进调整
    /// - 任何需要统一缩进的文本处理
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="indentLevel">缩进级别（I1, I2, I3等预制常量）</param>
    /// <param name="addTrailingNewline">是否在末尾添加额外的换行符</param>
    /// <returns>调整后的文本</returns>
    public static string AdjustIndentation(string text, string indentLevel, bool addTrailingNewline = false)
    {
        if (string.IsNullOrWhiteSpace(text))
            return "";

        // 🔧 按行分割文本，正确处理各种换行符格式
        var lines = text.Split(new[] { "\r\n", "\n", "\r" }, StringSplitOptions.None);
        var adjustedLines = new List<string>();

        foreach (var line in lines)
        {
            // 🔍 处理每一行，跳过空行以保持文本紧凑
            if (string.IsNullOrWhiteSpace(line))
            {
                // 跳过空行，不添加到结果中
                continue;
            }
            else
            {
                // 🔧 移除原有缩进，添加指定级别的缩进
                var trimmedLine = line.TrimStart();
                if (!string.IsNullOrEmpty(trimmedLine))
                {
                    adjustedLines.Add($"{indentLevel}{trimmedLine}");
                }
            }
        }

        // 🔧 移除末尾的空行
        while (adjustedLines.Count > 0 && string.IsNullOrEmpty(adjustedLines[adjustedLines.Count - 1]))
        {
            adjustedLines.RemoveAt(adjustedLines.Count - 1);
        }

        // 🔧 生成最终结果
        var result = string.Join("\n", adjustedLines);
        if (!string.IsNullOrEmpty(result))
        {
            // 确保文本结束后有换行符
            if (!result.EndsWith("\n"))
            {
                result += "\n";
            }

            // 根据需要添加额外的换行符（用于分隔文本和后续内容）
            if (addTrailingNewline)
            {
                result += "\n";
            }
        }
        return result;
    }

    #endregion

    #region 🏭 预设格式化方法

    /// <summary>
    /// 格式化为接口级别的缩进（I1级别缩进）
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>格式化后的文本</returns>
    public static string FormatForInterface(string text)
    {
        return AdjustIndentation(text, I1, addTrailingNewline: false);
    }

    /// <summary>
    /// 格式化为方法级别的缩进（I2级别缩进）
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>格式化后的文本</returns>
    public static string FormatForMethod(string text)
    {
        return AdjustIndentation(text, I2, addTrailingNewline: false);
    }

    /// <summary>
    /// 格式化为属性级别的缩进（I2级别缩进）
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <returns>格式化后的文本</returns>
    public static string FormatForProperty(string text)
    {
        return AdjustIndentation(text, I2, addTrailingNewline: false);
    }

    #endregion
}