using System.Text;
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

namespace Zylo.Toolkit.Helper;

/// <summary>
/// 文档注释模板生成器 - 专门负责生成和智能选择文档注释模板
/// 
/// 🎯 核心功能：
/// - 生成默认的XML文档注释模板
/// - 智能选择使用原始注释还是默认模板
/// - 特定场景的模板生成
/// 
/// 💡 设计原理：
/// - 专注于模板生成和智能选择逻辑
/// - 与提取和格式化功能分离
/// - 可扩展支持不同类型的模板
/// 
/// 🔧 使用场景：
/// - 接口文档注释模板生成
/// - 方法文档注释模板生成
/// - 智能选择最佳注释策略
/// 
/// 📝 架构分工：
/// - YXmlDocumentationExtractor：负责从语法树提取XML
/// - YDocumentationProcessor：负责通用格式化
/// - YDocumentationTemplateGenerator：负责模板生成和智能选择（本类）
/// </summary>
public static class YDocumentationTemplateGenerator
{
    #region 🛠️ 默认模板生成

    /// <summary>
    /// 生成默认的接口文档注释模板
    /// </summary>
    /// <param name="interfaceName">接口名称</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>默认的接口文档注释</returns>
    public static string GenerateInterfaceTemplate(string interfaceName, string? indentLevel = null)
    {
        indentLevel ??= I1;
        var sb = new StringBuilder();
        sb.AppendLine($"{indentLevel}/// <summary>")
          .AppendLine($"{indentLevel}/// {interfaceName}")
          .AppendLine($"{indentLevel}/// </summary>");
        return sb.ToString();
    }

    /// <summary>
    /// 生成默认的方法文档注释模板
    /// </summary>
    /// <param name="methodName">方法名称</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>默认的方法文档注释</returns>
    public static string GenerateMethodTemplate(string methodName, string? indentLevel = null)
    {
        indentLevel ??= I2;
        var sb = new StringBuilder();
        sb.AppendLine($"{indentLevel}/// <summary>")
          .AppendLine($"{indentLevel}/// {methodName}")
          .AppendLine($"{indentLevel}/// </summary>");
        return sb.ToString();
    }

    /// <summary>
    /// 生成默认的类文档注释模板
    /// </summary>
    /// <param name="className">类名称</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>默认的类文档注释</returns>
    public static string GenerateClassTemplate(string className, string? indentLevel = null)
    {
        indentLevel ??= I1;
        var sb = new StringBuilder();
        sb.AppendLine($"{indentLevel}/// <summary>")
          .AppendLine($"{indentLevel}/// {className}")
          .AppendLine($"{indentLevel}/// </summary>");
        return sb.ToString();
    }

    /// <summary>
    /// 生成默认的属性文档注释模板
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>默认的属性文档注释</returns>
    public static string GeneratePropertyTemplate(string propertyName, string? indentLevel = null)
    {
        indentLevel ??= I2;
        var sb = new StringBuilder();
        sb.AppendLine($"{indentLevel}/// <summary>")
          .AppendLine($"{indentLevel}/// {propertyName}")
          .AppendLine($"{indentLevel}/// </summary>");
        return sb.ToString();
    }

    #endregion

    #region 🧠 智能选择策略

    /// <summary>
    /// 智能处理文档注释 - 自动选择使用原始注释还是生成默认模板
    /// 
    /// 🎯 核心功能：
    /// 根据原始文档注释的质量，智能选择使用原始注释或生成默认模板
    /// 
    /// 💡 选择逻辑：
    /// 1. 检查原始注释是否存在且有效
    /// 2. 如果有效，清理并格式化原始注释
    /// 3. 如果无效，生成默认模板
    /// 
    /// 🔧 处理流程：
    /// 原始注释 -> 验证 -> 有效？-> 是：格式化原始注释
    ///                        -> 否：生成默认模板
    /// </summary>
    /// <param name="originalDocumentation">原始文档注释</param>
    /// <param name="fallbackName">回退名称（用于生成默认模板）</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>处理后的文档注释</returns>
    public static string ProcessDocumentationSmart(string? originalDocumentation, string fallbackName, string indentLevel)
    {
        // 🔍 检查是否有有效的原始文档注释
        if (!string.IsNullOrWhiteSpace(originalDocumentation) &&
            YXmlDocumentationExtractor.IsValidXmlDocumentation(originalDocumentation))
        {
            // 🔥 使用原始注释，清理并调整格式
            var cleanedDoc = YXmlDocumentationExtractor.CleanXmlDocumentation(originalDocumentation);
            return DocumentationProcessor.AdjustIndentation(cleanedDoc, indentLevel, addTrailingNewline: false);
        }
        else
        {
            // 🛡️ 生成默认模板
            return GenerateGenericTemplate(fallbackName, indentLevel);
        }
    }

    /// <summary>
    /// 生成通用的文档注释模板
    /// </summary>
    /// <param name="name">名称</param>
    /// <param name="indentLevel">缩进级别</param>
    /// <returns>通用文档注释模板</returns>
    private static string GenerateGenericTemplate(string name, string indentLevel)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"{indentLevel}/// <summary>")
          .AppendLine($"{indentLevel}/// {name}")
          .AppendLine($"{indentLevel}/// </summary>");
        return sb.ToString();
    }

    #endregion


}
