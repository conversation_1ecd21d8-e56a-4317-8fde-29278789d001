using Microsoft.CodeAnalysis;
using System.Linq;

namespace Zylo.Toolkit.Helper;

/// <summary>
/// 方法签名处理工具类 - 通用的方法签名处理工具
/// 
/// 🎯 设计目的：
/// 提供通用的方法签名处理功能，可被任何代码生成器使用
/// 
/// 💡 核心功能：
/// - 方法参数处理：格式化方法参数字符串
/// - 泛型参数处理：处理泛型类型参数
/// - 类型约束处理：处理泛型类型约束
/// - 方法签名生成：生成完整的方法签名
/// 
/// 🔧 设计理念：
/// - 通用性：可被任何功能的代码生成器使用
/// - 可复用：避免在不同生成器中重复编写相同逻辑
/// - 静态工具：无状态的静态方法
/// - 类型安全：强类型参数和返回值
/// 
/// 🚀 适用场景：
/// - YService 代码生成器
/// - YController 代码生成器（将来）
/// - YRepository 代码生成器（将来）
/// - 任何需要处理方法签名的代码生成器
/// </summary>
public static class YMethodSignatureHelper
{
    #region 🔧 方法参数处理

    /// <summary>
    /// 获取方法参数字符串
    ///
    /// 🎯 核心功能：
    /// 将方法的参数列表转换为字符串格式，包含类型、名称和默认值
    ///
    /// 💡 格式示例：
    /// - 无参数：""
    /// - 单参数："string name"
    /// - 多参数："int id, string name, bool isActive = true"
    /// - 复杂参数："List<string> items, Dictionary<int, string> map = null"
    /// - ref/out 参数："ref int value, out string result"
    ///
    /// 🔧 处理特性：
    /// - 完整类型名称：使用 ToDisplayString() 获取完整类型名
    /// - 默认值处理：正确格式化各种类型的默认值
    /// - 空值安全：处理可能的空值情况
    /// - 数组类型修复：正确处理多维数组类型显示
    /// - ref/out/in 修饰符：正确保留参数修饰符
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <returns>格式化的参数字符串</returns>
    public static string GetParametersString(IMethodSymbol methodSymbol)
    {
        return string.Join(", ", methodSymbol.Parameters.Select(p =>
        {
            var modifier = GetParameterModifier(p);
            var typeString = GetCorrectTypeDisplayString(p.Type);
            var defaultValue = p.HasExplicitDefaultValue ? $" = {FormatDefaultValue(p.ExplicitDefaultValue, p)}" : "";

            return $"{modifier}{typeString} {p.Name}{defaultValue}";
        }));
    }

    /// <summary>
    /// 获取参数修饰符（ref/out/in/params）
    /// </summary>
    /// <param name="parameter">参数符号</param>
    /// <returns>修饰符字符串，如果有修饰符则包含尾随空格</returns>
    private static string GetParameterModifier(IParameterSymbol parameter)
    {
        if (parameter.RefKind == RefKind.Ref)
            return "ref ";
        if (parameter.RefKind == RefKind.Out)
            return "out ";
        if (parameter.RefKind == RefKind.In)
            return "in ";
        if (parameter.IsParams)
            return "params ";

        return "";
    }

    /// <summary>
    /// 获取正确的类型显示字符串
    /// 修复多维数组类型显示问题：将 int[*, *] 修复为 int[,]
    /// </summary>
    /// <param name="typeSymbol">类型符号</param>
    /// <returns>正确的类型显示字符串</returns>
    private static string GetCorrectTypeDisplayString(ITypeSymbol typeSymbol)
    {
        var displayString = typeSymbol.ToDisplayString();

        // 修复多维数组类型显示问题
        // 将 int[*, *] 修复为 int[,]
        // 将 int[*, *, *] 修复为 int[,,]
        if (displayString.Contains("[*"))
        {
            // 计算逗号数量来确定维度
            var commaCount = displayString.Count(c => c == '*') - 1;
            var commas = new string(',', commaCount);

            // 替换 [*, *, ...] 为 [,,...]
            var startIndex = displayString.IndexOf('[');
            var endIndex = displayString.IndexOf(']', startIndex);
            if (startIndex >= 0 && endIndex > startIndex)
            {
                var prefix = displayString.Substring(0, startIndex + 1);
                var suffix = displayString.Substring(endIndex);
                displayString = prefix + commas + suffix;
            }
        }

        return displayString;
    }

    #endregion

    #region 🔧 泛型参数处理

    /// <summary>
    /// 获取类型参数字符串
    /// 
    /// 🎯 核心功能：
    /// 将泛型方法的类型参数转换为字符串格式
    /// 
    /// 💡 格式示例：
    /// - 无泛型：""
    /// - 单泛型："&lt;T&gt;"
    /// - 多泛型："&lt;T, U, V&gt;"
    /// 
    /// 🔧 处理特性：
    /// - 自动检测：自动检测是否有泛型参数
    /// - 格式化：正确添加尖括号和逗号分隔
    /// - 空值处理：无泛型时返回空字符串
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <returns>格式化的类型参数字符串</returns>
    public static string GetTypeParametersString(IMethodSymbol methodSymbol)
    {
        return methodSymbol.TypeParameters.Length > 0
            ? $"<{string.Join(", ", methodSymbol.TypeParameters.Select(tp => tp.Name))}>"
            : "";
    }

    #endregion

    #region 🔧 类型约束处理

    /// <summary>
    /// 获取类型约束字符串
    /// 
    /// 🎯 核心功能：
    /// 将泛型方法的类型约束转换为字符串格式
    /// 
    /// 💡 格式示例：
    /// - 无约束：""
    /// - 单约束：" where T : class"
    /// - 多约束：" where T : class where U : struct, new()"
    /// - 复杂约束：" where T : BaseClass, IInterface where U : struct"
    /// 
    /// 🔧 处理特性：
    /// - 完整约束：处理所有类型的约束（class、struct、new()、类型约束）
    /// - 正确格式：生成符合 C# 语法的约束字符串
    /// - 多约束支持：支持多个类型参数的约束
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <returns>格式化的类型约束字符串</returns>
    public static string GetTypeConstraintsString(IMethodSymbol methodSymbol)
    {
        if (methodSymbol.TypeParameters.Length == 0)
            return "";

        var constraints = new List<string>();

        foreach (var typeParam in methodSymbol.TypeParameters)
        {
            var paramConstraints = new List<string>();

            if (typeParam.HasReferenceTypeConstraint)
                paramConstraints.Add("class");

            if (typeParam.HasValueTypeConstraint)
                paramConstraints.Add("struct");

            paramConstraints.AddRange(typeParam.ConstraintTypes.Select(ct => ct.ToDisplayString()));

            if (typeParam.HasConstructorConstraint)
                paramConstraints.Add("new()");

            if (paramConstraints.Count > 0)
            {
                constraints.Add($" where {typeParam.Name} : {string.Join(", ", paramConstraints)}");
            }
        }

        return string.Join("", constraints);
    }

    /// <summary>
    /// 获取类型参数的约束列表
    /// 
    /// 🎯 核心功能：
    /// 提取泛型类型参数的所有约束，返回约束字符串列表
    /// 
    /// 💡 约束类型：
    /// - 类型约束：BaseClass, IInterface
    /// - 引用类型约束：class
    /// - 值类型约束：struct
    /// - 构造函数约束：new()
    /// 
    /// 🔧 使用场景：
    /// 当需要单独处理每个约束时使用，比如生成文档或进行约束验证
    /// </summary>
    /// <param name="typeParameter">泛型类型参数</param>
    /// <returns>约束字符串列表</returns>
    public static IEnumerable<string> GetTypeConstraints(ITypeParameterSymbol typeParameter)
    {
        var constraints = new List<string>();

        // 🔍 引用类型约束
        if (typeParameter.HasReferenceTypeConstraint)
            constraints.Add("class");

        // 🔍 值类型约束
        if (typeParameter.HasValueTypeConstraint)
            constraints.Add("struct");

        // 🔍 类型约束
        foreach (var constraintType in typeParameter.ConstraintTypes)
        {
            constraints.Add(constraintType.ToDisplayString());
        }

        // 🔍 构造函数约束
        if (typeParameter.HasConstructorConstraint)
            constraints.Add("new()");

        return constraints;
    }

    #endregion

    #region 🔧 完整签名生成

    /// <summary>
    /// 生成完整的方法签名
    /// 
    /// 🎯 核心功能：
    /// 生成包含返回类型、方法名、泛型参数、参数列表的完整方法签名
    /// 
    /// 💡 格式示例：
    /// - 普通方法："string GetName(int id)"
    /// - 泛型方法："T GetValue&lt;T&gt;(string key)"
    /// - 复杂方法："Task&lt;List&lt;T&gt;&gt; GetItemsAsync&lt;T&gt;(int count, bool includeDeleted = false)"
    /// 
    /// 🔧 使用场景：
    /// 生成接口声明、文档注释、调试信息等
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <returns>完整的方法签名字符串</returns>
    public static string GetFullMethodSignature(IMethodSymbol methodSymbol)
    {
        var returnType = methodSymbol.ReturnType.ToDisplayString();
        var methodName = methodSymbol.Name;
        var typeParameters = GetTypeParametersString(methodSymbol);
        var parameters = GetParametersString(methodSymbol);

        return $"{returnType} {methodName}{typeParameters}({parameters})";
    }

    /// <summary>
    /// 生成带约束的完整方法声明
    /// 
    /// 🎯 核心功能：
    /// 生成包含类型约束的完整方法声明
    /// 
    /// 💡 格式示例：
    /// - 无约束："string GetName(int id)"
    /// - 有约束："T GetValue&lt;T&gt;(string key) where T : class"
    /// - 多约束："T Process&lt;T, U&gt;(T input, U context) where T : class where U : struct, new()"
    /// 
    /// 🔧 使用场景：
    /// 生成接口方法声明、抽象方法声明等
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <returns>带约束的完整方法声明字符串</returns>
    public static string GetFullMethodDeclaration(IMethodSymbol methodSymbol)
    {
        var signature = GetFullMethodSignature(methodSymbol);
        var constraints = GetTypeConstraintsString(methodSymbol);

        return string.IsNullOrEmpty(constraints) ? signature : $"{signature}{constraints}";
    }

    /// <summary>
    /// 格式化默认值 - 默认值格式化器（移植自 YServiceCodeGenerator）
    ///
    /// 🎯 功能说明：
    /// 将参数的默认值转换为 C# 代码中的字符串表示
    /// 特别处理各种特殊类型的默认值
    /// </summary>
    /// <param name="defaultValue">默认值对象</param>
    /// <param name="parameterSymbol">参数符号，用于获取类型信息</param>
    /// <returns>格式化后的默认值字符串</returns>
    private static string FormatDefaultValue(object? defaultValue, IParameterSymbol? parameterSymbol = null)
    {
        return defaultValue switch
        {
            null => "default", // 🔧 使用 default 而不是 null，适用于值类型和引用类型
            string s => $"\"{s}\"",
            bool b => b.ToString().ToLower(),
            char c => $"'{c}'",
            float f => $"{f}f",
            double d => d.ToString(),
            decimal m => $"{m}m",
            _ => FormatEnumOrOtherDefaultValue(defaultValue, parameterSymbol)
        };
    }

    /// <summary>
    /// 格式化枚举或其他类型的默认值
    /// </summary>
    /// <param name="defaultValue">默认值对象</param>
    /// <param name="parameterSymbol">参数符号</param>
    /// <returns>格式化后的默认值字符串</returns>
    private static string FormatEnumOrOtherDefaultValue(object? defaultValue, IParameterSymbol? parameterSymbol)
    {
        if (defaultValue == null)
            return "default";

        // 检查是否为枚举类型
        if (parameterSymbol?.Type is INamedTypeSymbol namedType && namedType.TypeKind == TypeKind.Enum)
        {
            // 获取枚举的完整类型名
            var enumTypeName = namedType.ToDisplayString();

            // 尝试将数字值转换为枚举名称
            if (defaultValue is int intValue)
            {
                // 查找对应的枚举成员
                var enumMember = namedType.GetMembers()
                    .OfType<IFieldSymbol>()
                    .Where(f => f.IsStatic && f.HasConstantValue && f.ConstantValue?.Equals(intValue) == true)
                    .FirstOrDefault();

                if (enumMember != null)
                {
                    return $"{enumTypeName}.{enumMember.Name}";
                }
            }

            // 如果找不到对应的枚举成员，使用类型转换
            return $"({enumTypeName}){defaultValue}";
        }

        // 对于其他类型，直接返回字符串表示
        return defaultValue.ToString() ?? "default";
    }

    #endregion
}
