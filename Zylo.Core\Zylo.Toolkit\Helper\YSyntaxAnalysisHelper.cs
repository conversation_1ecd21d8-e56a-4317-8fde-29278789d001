using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace Zylo.Toolkit.Helper
{
    /// <summary>
    /// 语法分析帮助类 - 源代码生成器的"侦探工具箱"
    ///
    /// 🎯 作用：
    /// 提供各种快速筛选方法，帮助源代码生成器快速找到目标代码元素
    ///
    /// 🔍 包含功能：
    /// - 类筛选：找到有属性标记的类
    /// - 方法筛选：找到有属性标记的方法
    /// - 属性筛选：找到有属性标记的属性
    /// - 通用筛选：提供可复用的筛选逻辑
    /// - 属性检测：通用的属性检测工具
    ///
    /// 💡 设计理念：
    /// - 快速筛选：只做语法检查，不做语义分析
    /// - 性能优先：尽早过滤，减少后续处理负担
    /// - 通用性：可被任何代码生成器功能使用
    /// - 可扩展：为各种功能提供基础工具
    /// </summary>
    public static class YSyntaxAnalysisHelper
    {
        #region 🏗️ 类相关筛选

        /// <summary>
        /// 快速判断是否是有属性标记的类
        ///
        /// 🎯 核心功能：
        /// 检查语法节点是否为带有属性标记的类声明
        ///
        /// 💡 使用场景：
        /// - 在增量生成器的 predicate 阶段快速筛选
        /// - 找到可能需要代码生成的类
        /// - 避免处理无关的语法节点
        ///
        /// 🏃‍♂️ 性能优化：
        /// - 只检查语法结构，不涉及语义分析
        /// - 避免昂贵的符号解析操作
        /// - 尽早过滤掉不相关的节点
        ///
        /// ✅ 筛选条件：
        /// 1. 必须是类声明 (ClassDeclarationSyntax)
        /// 2. 必须有属性列表 (AttributeLists.Count > 0)
        /// </summary>
        /// <param name="node">语法节点</param>
        /// <returns>true = 有属性的类；false = 无属性或非类</returns>
        public static bool IsAttributedClass(SyntaxNode node)
        {
            // 🔍 模式匹配：检查是否为有属性的类
            // node is ClassDeclarationSyntax classDeclaration:
            //   如果 node 是类声明，则转换并赋值给 classDeclaration
            // &&: 逻辑与，两个条件都要满足
            // classDeclaration.AttributeLists.Count > 0:
            //   检查类是否有属性标记（如 [Test]、[Obsolete]、[Service] 等）
            return node is ClassDeclarationSyntax classDeclaration &&
                   classDeclaration.AttributeLists.Count > 0;
        }

        /// <summary>
        /// 检查类是否为 partial 类
        /// 
        /// 🎯 小白理解：
        /// partial 类可以分散在多个文件中定义，这对源代码生成很重要
        /// 因为我们生成的接口实现需要与用户的类合并
        /// 
        /// 💡 为什么重要？
        /// - 源代码生成器通常需要扩展现有类
        /// - partial 类允许在多个文件中定义同一个类
        /// - 用户写业务逻辑，生成器写基础设施代码
        /// </summary>
        /// <param name="classDeclaration">类声明语法节点</param>
        /// <returns>true = 是 partial 类；false = 不是</returns>
        public static bool IsPartialClass(ClassDeclarationSyntax classDeclaration)
        {
            // 🔍 检查类的修饰符中是否包含 partial 关键字
            // Modifiers: 类的修饰符集合（public、static、partial 等）
            // Any(): 检查是否存在满足条件的元素
            // SyntaxKind.PartialKeyword: partial 关键字的语法类型
            return classDeclaration.Modifiers.Any(SyntaxKind.PartialKeyword);
        }

        #endregion

        #region 🔧 方法相关筛选

        /// <summary>
        /// 快速判断是否是有属性标记的方法
        ///
        /// 🎯 核心功能：
        /// 检查语法节点是否为带有属性标记的方法声明
        ///
        /// 💡 使用场景：
        /// - 找到带有测试属性的方法：[Test]、[Fact]
        /// - 找到带有验证属性的方法：[Required]、[Validate]
        /// - 找到带有自定义属性的方法：[CustomAttribute]
        /// - 任何需要方法级别代码生成的场景
        ///
        /// 🏃‍♂️ 性能优化：
        /// - 只检查语法结构，快速筛选
        /// - 避免处理无属性的方法
        /// - 为各种功能扩展提供基础
        /// </summary>
        /// <param name="node">语法节点</param>
        /// <returns>true = 有属性的方法；false = 无属性或非方法</returns>
        public static bool IsAttributedMethod(SyntaxNode node)
        {
            // 🔍 检查是否为有属性的方法声明
            // MethodDeclarationSyntax: 方法声明的语法节点类型
            // 逻辑与类筛选相同，但针对方法
            return node is MethodDeclarationSyntax methodDeclaration &&
                   methodDeclaration.AttributeLists.Count > 0;
        }

        /// <summary>
        /// 检查方法是否为静态方法
        /// 
        /// 🎯 小白理解：
        /// 静态方法属于类本身，不属于类的实例
        /// 在依赖注入中，静态方法有特殊的处理方式
        /// 
        /// 💡 应用场景：
        /// - 工具方法：不需要实例状态的方法
        /// - 扩展方法：为现有类型添加功能
        /// - 配置方法：应用启动时的配置逻辑
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <returns>true = 是静态方法；false = 不是</returns>
        public static bool IsStaticMethod(MethodDeclarationSyntax methodDeclaration)
        {
            // 🔍 检查方法修饰符中是否包含 static 关键字
            return methodDeclaration.Modifiers.Any(SyntaxKind.StaticKeyword);
        }

        /// <summary>
        /// 检查方法是否为公共方法
        /// 
        /// 🎯 小白理解：
        /// 只有公共方法才能被外部调用，才有依赖注入的意义
        /// 私有方法通常是内部实现细节，不需要依赖注入
        /// 
        /// 💡 访问级别说明：
        /// - public: 任何地方都可以访问
        /// - private: 只有同一个类内部可以访问
        /// - protected: 只有继承类可以访问
        /// - internal: 只有同一个程序集可以访问
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <returns>true = 是公共方法；false = 不是</returns>
        public static bool IsPublicMethod(MethodDeclarationSyntax methodDeclaration)
        {
            // 🔍 检查方法修饰符中是否包含 public 关键字
            return methodDeclaration.Modifiers.Any(SyntaxKind.PublicKeyword);
        }

        #endregion

        #region 🏷️ 属性相关筛选

        /// <summary>
        /// 快速判断是否是有属性标记的属性（Property）
        ///
        /// 🎯 核心功能：
        /// 检查语法节点是否为带有属性标记的属性声明
        ///
        /// 💡 使用场景：
        /// - 找到带有验证属性的属性：[Required]、[Range]
        /// - 找到带有序列化属性的属性：[JsonProperty]、[XmlElement]
        /// - 找到带有自定义属性的属性：[CustomAttribute]
        /// - 任何需要属性级别代码生成的场景
        ///
        /// 🚀 扩展性：
        /// 为各种属性处理功能提供基础筛选能力
        /// </summary>
        /// <param name="node">语法节点</param>
        /// <returns>true = 有属性标记的属性；false = 无属性标记或非属性</returns>
        public static bool IsAttributedProperty(SyntaxNode node)
        {
            // 🔍 检查是否为有属性标记的属性声明
            // PropertyDeclarationSyntax: 属性声明的语法节点类型
            return node is PropertyDeclarationSyntax propertyDeclaration &&
                   propertyDeclaration.AttributeLists.Count > 0;
        }

        #endregion

        #region 🛠️ 通用筛选工具

        /// <summary>
        /// 检查语法节点是否有指定名称的属性
        /// 
        /// 🎯 小白理解：
        /// 这是一个通用工具，可以检查任何代码元素是否有特定的属性标记
        /// 比如检查是否有 [Obsolete]、[YService]、[Test] 等
        /// 
        /// 💡 使用示例：
        /// HasAttributeWithName(classNode, "Obsolete") // 检查是否有 [Obsolete]
        /// HasAttributeWithName(methodNode, "Test")    // 检查是否有 [Test]
        /// HasAttributeWithName(propNode, "Required")  // 检查是否有 [Required]
        /// 
        /// ⚠️ 注意：
        /// 这只是语法级别的检查，不能区分同名但不同命名空间的属性
        /// 精确检查需要在语义分析阶段进行
        /// </summary>
        /// <param name="node">要检查的语法节点</param>
        /// <param name="attributeName">属性名称（不包含 Attribute 后缀）</param>
        /// <returns>true = 有指定属性；false = 没有</returns>
        public static bool HasAttributeWithName(SyntaxNode node, string attributeName)
        {
            // 🔍 获取节点的属性列表
            var attributeLists = node switch
            {
                ClassDeclarationSyntax classDecl => classDecl.AttributeLists,
                MethodDeclarationSyntax methodDecl => methodDecl.AttributeLists,
                PropertyDeclarationSyntax propDecl => propDecl.AttributeLists,
                _ => default  // 其他类型返回空列表
            };

            // 🔍 在所有属性中查找指定名称的属性
            return attributeLists
                .SelectMany(list => list.Attributes)  // 展开所有属性
                .Any(attr =>
                {
                    var name = attr.Name.ToString();
                    // 支持两种形式：Test 和 TestAttribute
                    return name == attributeName ||
                           name == $"{attributeName}Attribute";
                });
        }

        /// <summary>
        /// 获取语法节点的所有属性名称
        /// 
        /// 🎯 小白理解：
        /// 提取一个代码元素上的所有属性标记的名称
        /// 用于调试和分析，了解代码元素有哪些属性
        /// 
        /// 💡 使用场景：
        /// - 调试：查看生成器处理了哪些属性
        /// - 分析：统计项目中使用了哪些属性
        /// - 验证：确认属性是否正确应用
        /// </summary>
        /// <param name="node">要分析的语法节点</param>
        /// <returns>属性名称列表</returns>
        public static IEnumerable<string> GetAttributeNames(SyntaxNode node)
        {
            // 🔍 获取节点的属性列表（复用上面的逻辑）
            var attributeLists = node switch
            {
                ClassDeclarationSyntax classDecl => classDecl.AttributeLists,
                MethodDeclarationSyntax methodDecl => methodDecl.AttributeLists,
                PropertyDeclarationSyntax propDecl => propDecl.AttributeLists,
                _ => default
            };

            // 🔍 提取所有属性的名称
            return attributeLists
                .SelectMany(list => list.Attributes)
                .Select(attr => attr.Name.ToString())
                .Distinct();  // 去重，避免重复的属性名
        }

        #endregion

        #region 🧠 智能组合筛选器

        /// <summary>
        /// 智能候选筛选器 - 找到包含任何属性标记的类
        ///
        /// 🎯 核心功能：
        /// 这是一个"更聪明"的筛选器，不仅找有属性的类，
        /// 还会找那些"内部可能有带属性成员"的类
        ///
        /// 🔍 筛选逻辑：
        /// 1. 类本身有属性 → 直接候选
        /// 2. 类没有属性，但内部有方法有属性 → 也是候选
        /// 3. 类没有属性，但内部有属性有属性标记 → 也是候选
        /// 4. 完全没有属性 → 不是候选
        ///
        /// 💡 使用场景：
        /// ```csharp
        /// // 情况1：类级别属性
        /// [Service]
        /// public class UserService { }
        ///
        /// // 情况2：方法级别属性
        /// public class DataService
        /// {
        ///     [Test] public void ProcessData() { }
        /// }
        ///
        /// // 情况3：属性级别属性
        /// public class ConfigService
        /// {
        ///     [Required] public string Name { get; set; }
        /// }
        /// ```
        /// </summary>
        /// <param name="node">语法节点</param>
        /// <returns>true = 可能包含属性相关内容；false = 肯定不包含</returns>
        public static bool IsAttributeRelatedCandidate(SyntaxNode node)
        {
            // 🔍 只处理类声明
            if (node is not ClassDeclarationSyntax classDeclaration)
                return false;

            // 🚀 情况1：类本身有属性
            if (classDeclaration.AttributeLists.Count > 0)
                return true;

            // 🔍 情况2：检查类内部是否有带属性的方法
            var hasAttributedMethods = classDeclaration.Members
                .OfType<MethodDeclarationSyntax>()
                .Any(method => method.AttributeLists.Count > 0);

            // 🔍 情况3：检查类内部是否有带属性的属性
            var hasAttributedProperties = classDeclaration.Members
                .OfType<PropertyDeclarationSyntax>()
                .Any(property => property.AttributeLists.Count > 0);

            // 🎯 只要有任何一种情况满足，就是候选
            return hasAttributedMethods || hasAttributedProperties;
        }

        #endregion

        #region 🚀 使用示例和扩展方法

        /// <summary>
        /// 组合筛选：找到公共的、非静态的、有属性的方法
        ///
        /// 🎯 小白理解：
        /// 这是一个组合筛选器，用于找到符合多个条件的方法
        /// 比如找到所有可以进行依赖注入的方法
        ///
        /// 💡 使用场景：
        /// 未来如果要支持方法级别的依赖注入，可以用这个筛选器
        /// 例如：[YService] public void ProcessData() { }
        /// </summary>
        /// <param name="node">语法节点</param>
        /// <returns>true = 符合条件的方法；false = 不符合</returns>
        public static bool IsInjectableMethodCandidate(SyntaxNode node)
        {
            // 🔍 组合多个条件：
            // 1. 是有属性的方法
            // 2. 是公共方法
            // 3. 不是静态方法（静态方法不需要依赖注入）
            if (node is MethodDeclarationSyntax methodDecl)
            {
                return IsAttributedMethod(node) &&
                       IsPublicMethod(methodDecl) &&
                       !IsStaticMethod(methodDecl);
            }
            return false;
        }

        /// <summary>
        /// 获取节点的详细分析信息（调试用）
        ///
        /// 🎯 小白理解：
        /// 这个方法用于调试，可以看到一个代码元素的详细信息
        /// 帮助理解生成器是如何分析代码的
        ///
        /// 💡 使用示例：
        /// var info = GetNodeAnalysisInfo(classNode);
        /// Console.WriteLine(info); // 输出：Class: UserService, Attributes: [YService, Obsolete], IsPartial: true
        /// </summary>
        /// <param name="node">要分析的语法节点</param>
        /// <returns>分析信息字符串</returns>
        public static string GetNodeAnalysisInfo(SyntaxNode node)
        {
            var nodeType = node switch
            {
                ClassDeclarationSyntax => "Class",
                MethodDeclarationSyntax => "Method",
                PropertyDeclarationSyntax => "Property",
                _ => node.GetType().Name
            };

            var name = node switch
            {
                ClassDeclarationSyntax classDecl => classDecl.Identifier.ValueText,
                MethodDeclarationSyntax methodDecl => methodDecl.Identifier.ValueText,
                PropertyDeclarationSyntax propDecl => propDecl.Identifier.ValueText,
                _ => "Unknown"
            };

            var attributes = GetAttributeNames(node).ToList();
            var attributeList = attributes.Any() ? $"[{string.Join(", ", attributes)}]" : "None";

            var extraInfo = node switch
            {
                ClassDeclarationSyntax classDecl => $", IsPartial: {IsPartialClass(classDecl)}",
                MethodDeclarationSyntax methodDecl => $", IsPublic: {IsPublicMethod(methodDecl)}, IsStatic: {IsStaticMethod(methodDecl)}",
                _ => ""
            };

            return $"{nodeType}: {name}, Attributes: {attributeList}{extraInfo}";
        }

        #endregion

        #region 🔧 通用方法检测工具 - 为各种生成器提供基础支持

        /// <summary>
        /// 检查类是否有带指定属性的方法
        ///
        /// 🎯 核心功能：
        /// 通用的方法级属性检测工具，可用于各种代码生成场景
        ///
        /// 💡 设计理念：
        /// 提供通用的检测机制，不依赖具体的属性名称
        /// 各个生成器可以传入自己需要检测的属性名称列表
        ///
        /// 📝 使用示例：
        /// ```csharp
        /// var yServiceAttributes = new[] { "YServiceScoped", "YServiceSingleton", "YServiceTransient" };
        /// var hasMethodAttributes = HasMethodsWithAttributes(classDecl, yServiceAttributes);
        /// ```
        /// </summary>
        /// <param name="classDeclaration">类声明语法节点</param>
        /// <param name="attributeNames">要检测的属性名称列表</param>
        /// <returns>如果找到带指定属性的方法返回 true，否则返回 false</returns>
        public static bool HasMethodsWithAttributes(ClassDeclarationSyntax classDeclaration, string[] attributeNames)
        {
            // 🔍 遍历类中的所有成员
            foreach (var member in classDeclaration.Members)
            {
                // 🎯 只检查方法声明
                if (member is not MethodDeclarationSyntax methodDeclaration)
                    continue;

                // 🔍 检查方法是否有属性
                if (methodDeclaration.AttributeLists.Count == 0)
                    continue;

                // 🎯 检查是否有指定的属性
                if (HasMethodAttribute(methodDeclaration, attributeNames))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 检查单个方法是否有指定的属性
        ///
        /// 🎯 核心功能：
        /// 通用的方法属性检测工具，支持多种属性名称格式
        ///
        /// 💡 支持格式：
        /// - 短名称：如 "YService"
        /// - 完整名称：如 "YServiceAttribute"
        /// - 大小写不敏感
        ///
        /// 🔧 实现策略：
        /// 使用字符串匹配进行快速检测，支持带 Attribute 后缀和不带后缀的形式
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <param name="attributeNames">要检测的属性名称列表</param>
        /// <returns>如果有指定属性返回 true，否则返回 false</returns>
        public static bool HasMethodAttribute(MethodDeclarationSyntax methodDeclaration, string[] attributeNames)
        {
            // 🔍 遍历方法的所有属性列表
            foreach (var attributeList in methodDeclaration.AttributeLists)
            {
                foreach (var attribute in attributeList.Attributes)
                {
                    var attributeName = attribute.Name.ToString();

                    // 🎯 检查是否匹配指定的属性名称
                    if (attributeNames.Any(name =>
                        attributeName.Equals(name, StringComparison.OrdinalIgnoreCase) ||
                        attributeName.Equals($"{name}Attribute", StringComparison.OrdinalIgnoreCase)))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 获取方法的属性类型
        ///
        /// 🎯 核心功能：
        /// 通用的方法属性类型获取工具，支持自定义属性映射
        ///
        /// 💡 使用场景：
        /// 各种生成器可以传入自己的属性映射规则来获取属性类型
        ///
        /// 📝 使用示例：
        /// ```csharp
        /// var attributeMapping = new Dictionary&lt;string, string&gt;
        /// {
        ///     { "YServiceScoped", "Scoped" },
        ///     { "YServiceSingleton", "Singleton" },
        ///     { "YServiceTransient", "Transient" }
        /// };
        /// var attributeType = GetMethodAttributeType(methodDecl, attributeMapping);
        /// ```
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <param name="attributeMapping">属性名称到类型的映射</param>
        /// <returns>属性类型字符串或 null</returns>
        public static string? GetMethodAttributeType(MethodDeclarationSyntax methodDeclaration,
            Dictionary<string, string> attributeMapping)
        {
            // 🔍 遍历方法的所有属性列表
            foreach (var attributeList in methodDeclaration.AttributeLists)
            {
                foreach (var attribute in attributeList.Attributes)
                {
                    var attributeName = attribute.Name.ToString();

                    // 🎯 在映射中查找匹配的属性
                    foreach (var mapping in attributeMapping)
                    {
                        if (attributeName.Equals(mapping.Key, StringComparison.OrdinalIgnoreCase) ||
                            attributeName.Equals($"{mapping.Key}Attribute", StringComparison.OrdinalIgnoreCase))
                        {
                            return mapping.Value;
                        }
                    }
                }
            }

            return null;
        }

        #endregion

        #region 🏷️ 通用属性检测

        /// <summary>
        /// 检查方法是否标记了指定的属性
        ///
        /// 🎯 核心功能：
        /// 通用的属性检测工具，可以检测任何属性名称
        ///
        /// 💡 支持格式：
        /// - 短名称：如 "YServiceIgnore"
        /// - 完整名称：如 "YServiceIgnoreAttribute"
        /// - 自动匹配：传入 "YServiceIgnore"，自动匹配 "YServiceIgnore" 和 "YServiceIgnoreAttribute"
        ///
        /// 🔧 使用场景：
        /// - YService: 检测 [YServiceIgnore]
        /// - YController: 检测 [YControllerIgnore]（将来）
        /// - YRepository: 检测 [YRepositoryIgnore]（将来）
        /// - 任何需要属性检测的功能
        ///
        /// 🚀 扩展性：
        /// 这是一个真正通用的工具，可以被任何代码生成器使用
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <param name="attributeName">属性名称（不包含 "Attribute" 后缀）</param>
        /// <returns>如果找到指定属性返回 true，否则返回 false</returns>
        public static bool HasMethodAttribute(MethodDeclarationSyntax methodDeclaration, string attributeName)
        {
            return methodDeclaration.AttributeLists
                .SelectMany(list => list.Attributes)
                .Any(attr =>
                {
                    var name = attr.Name.ToString();
                    return name == attributeName || name == $"{attributeName}Attribute";
                });
        }

        /// <summary>
        /// 检查类是否标记了指定的属性
        ///
        /// 🎯 核心功能：
        /// 通用的类级属性检测工具，可以检测任何属性名称
        ///
        /// 💡 使用场景：
        /// - 检测类级别的各种属性标记
        /// - 支持任何功能的类级属性检测
        ///
        /// 🔧 设计理念：
        /// 与 HasMethodAttribute 保持一致的接口设计
        /// </summary>
        /// <param name="classDeclaration">类声明语法节点</param>
        /// <param name="attributeName">属性名称（不包含 "Attribute" 后缀）</param>
        /// <returns>如果找到指定属性返回 true，否则返回 false</returns>
        public static bool HasClassAttribute(ClassDeclarationSyntax classDeclaration, string attributeName)
        {
            return classDeclaration.AttributeLists
                .SelectMany(list => list.Attributes)
                .Any(attr =>
                {
                    var name = attr.Name.ToString();
                    return name == attributeName || name == $"{attributeName}Attribute";
                });
        }

        /// <summary>
        /// 检查方法是否标记了多个可能的属性中的任意一个
        ///
        /// 🎯 核心功能：
        /// 批量属性检测，检查方法是否标记了指定属性列表中的任意一个
        ///
        /// 💡 使用场景：
        /// - 检测多种忽略属性：["YServiceIgnore", "Obsolete", "EditorBrowsable"]
        /// - 检测多种功能属性：["YServiceScoped", "YServiceSingleton", "YServiceTransient"]
        ///
        /// 🔧 性能优化：
        /// 使用 Any() 进行短路求值，找到第一个匹配就返回
        /// </summary>
        /// <param name="methodDeclaration">方法声明语法节点</param>
        /// <param name="attributeNames">属性名称列表</param>
        /// <returns>如果找到任意一个指定属性返回 true，否则返回 false</returns>
        public static bool HasAnyMethodAttribute(MethodDeclarationSyntax methodDeclaration, params string[] attributeNames)
        {
            return attributeNames.Any(attrName => HasMethodAttribute(methodDeclaration, attrName));
        }

        #endregion
    }
}
