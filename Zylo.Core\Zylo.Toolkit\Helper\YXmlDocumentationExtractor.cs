using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

namespace Zylo.Toolkit.Helper;

/// <summary>
/// XML 文档注释提取器 - 专业的文档注释处理工具
///
/// 🎯 核心功能：
/// 专门负责从语法树中提取和处理 XML 文档注释，支持从各种语法节点中提取完整的文档注释
///
/// 📋 功能分类：
/// ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
/// │  公共提取接口    │    │  核心处理引擎    │    │  辅助工具方法    │
/// │ ExtractFromXxx  │    │ ExtractFromNode │    │ Format/Clean    │
/// └─────────────────┘    └─────────────────┘    └─────────────────┘
///
/// 💡 设计优势：
/// 1. 职责单一：专注于 XML 文档注释处理
/// 2. 代码复用：可被多个生成器使用
/// 3. 易于测试：独立的功能模块
/// 4. 易于维护：集中管理 XML 处理逻辑
/// 5. 功能分区：使用 #region 清晰分类
/// </summary>
internal static class YXmlDocumentationExtractor
{
    #region 🔍 公共提取接口 - 对外提供的主要功能

    /// <summary>
    /// 从方法声明中提取 XML 文档注释
    ///
    /// 🎯 功能说明：
    /// 直接从语法树的前导 trivia 中提取 XML 文档注释
    /// 这种方法可以获取完整的原始注释内容
    ///
    /// 💡 处理逻辑：
    /// 1. 获取方法前的所有 trivia
    /// 2. 筛选出文档注释 trivia
    /// 3. 清理和格式化注释内容
    /// 4. 确保正确的缩进
    /// </summary>
    /// <param name="methodDeclaration">方法声明语法节点</param>
    /// <returns>格式化的 XML 文档注释字符串</returns>
    public static string ExtractFromMethod(MethodDeclarationSyntax methodDeclaration)
    {
        return ExtractFromSyntaxNode(methodDeclaration, methodDeclaration.Identifier.ValueText);
    }

    /// <summary>
    /// 从类声明中提取 XML 文档注释
    ///
    /// 🎯 功能说明：
    /// 提取类级别的 XML 文档注释，用于生成接口的文档注释
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <returns>格式化的 XML 文档注释字符串</returns>
    public static string ExtractFromClass(ClassDeclarationSyntax classDeclaration)
    {
        return ExtractFromSyntaxNode(classDeclaration, classDeclaration.Identifier.ValueText);
    }

    /// <summary>
    /// 从属性声明中提取 XML 文档注释
    ///
    /// 🎯 功能说明：
    /// 提取属性级别的 XML 文档注释，为未来的属性注入功能做准备
    /// </summary>
    /// <param name="propertyDeclaration">属性声明语法节点</param>
    /// <returns>格式化的 XML 文档注释字符串</returns>
    public static string ExtractFromProperty(PropertyDeclarationSyntax propertyDeclaration)
    {
        return ExtractFromSyntaxNode(propertyDeclaration, propertyDeclaration.Identifier.ValueText);
    }

    #endregion

    #region 🔧 核心处理引擎 - 文档注释提取的核心逻辑

    /// <summary>
    /// 从任意语法节点中提取 XML 文档注释 - 核心提取引擎
    ///
    /// 🎯 核心实现：
    /// 这是所有提取方法的核心实现，处理通用的 XML 文档注释提取逻辑
    ///
    /// 💡 处理步骤：
    /// 1. 获取节点的前导 trivia
    /// 2. 筛选文档注释类型的 trivia
    /// 3. 解析和清理注释内容
    /// 4. 格式化输出
    ///
    /// 🔍 处理流程：
    /// ┌─────────────────┐
    /// │ 1. 获取 Trivia   │ -> 获取语法节点前的所有 trivia
    /// └─────────────────┘
    ///          ↓
    /// ┌─────────────────┐
    /// │ 2. 筛选文档注释  │ -> 只保留文档注释类型的 trivia
    /// └─────────────────┘
    ///          ↓
    /// ┌─────────────────┐
    /// │ 3. 处理注释内容  │ -> 逐个处理每个文档注释
    /// └─────────────────┘
    ///          ↓
    /// ┌─────────────────┐
    /// │ 4. 格式化输出    │ -> 生成最终的格式化字符串
    /// └─────────────────┘
    /// </summary>
    /// <param name="syntaxNode">要提取注释的语法节点</param>
    /// <param name="fallbackName">当没有注释时使用的回退名称</param>
    /// <returns>格式化的 XML 文档注释字符串</returns>
    private static string ExtractFromSyntaxNode(SyntaxNode syntaxNode, string fallbackName)
    {
        var sb = new StringBuilder();

        // 🔍 第一步：获取节点前的所有 trivia（包括注释、空白等）
        var leadingTrivia = syntaxNode.GetLeadingTrivia();

        // 🔍 第二步：筛选出文档注释类型的 trivia
        // SingleLineDocumentationCommentTrivia: /// 格式的单行文档注释
        // MultiLineDocumentationCommentTrivia: /** */ 格式的多行文档注释
        var docComments = leadingTrivia
            .Where(trivia => trivia.IsKind(SyntaxKind.SingleLineDocumentationCommentTrivia) ||
                            trivia.IsKind(SyntaxKind.MultiLineDocumentationCommentTrivia))
            .ToList();

        // 🛡️ 第三步：处理没有文档注释的情况
        if (docComments.Count == 0)
        {
            // 生成简单的默认注释
            return GenerateSimpleDocumentation(fallbackName);
        }

        // 🔧 第四步：处理找到的文档注释
        foreach (var docComment in docComments)
        {
            ProcessDocumentationComment(sb, docComment);
        }

        return sb.ToString();
    }

    /// <summary>
    /// 处理单个文档注释 trivia - 注释内容解析器
    ///
    /// 🎯 功能说明：
    /// 解析和格式化单个文档注释 trivia 的内容
    ///
    /// 💡 处理逻辑：
    /// 1. 获取注释的原始文本
    /// 2. 按行分割处理
    /// 3. 清理每行的格式
    /// 4. 确保正确的缩进
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="docComment">文档注释 trivia</param>
    private static void ProcessDocumentationComment(StringBuilder sb, SyntaxTrivia docComment)
    {
        // 🔧 获取注释的原始文本内容
        var commentText = docComment.ToString();

        // 🔧 按行分割，移除空行
        var lines = commentText.Split('\n', StringSplitOptions.RemoveEmptyEntries);

        // 🔧 逐行处理和格式化
        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            if (!string.IsNullOrEmpty(trimmedLine))
            {
                // 🔧 格式化单行注释，确保正确的缩进和格式
                var formattedLine = FormatDocumentationLine(trimmedLine);
                if (!string.IsNullOrEmpty(formattedLine))
                {
                    sb.AppendLine(formattedLine);
                }
            }
        }
    }

    #endregion

    #region 🛠️ 辅助工具方法 - 格式化、验证和清理工具

    /// <summary>
    /// 生成简单的文档注释 - 默认注释生成器
    ///
    /// 🎯 功能说明：
    /// 当没有找到 XML 文档注释时，生成一个简单的 summary 注释
    /// 确保生成的接口始终有基本的文档注释
    /// </summary>
    /// <param name="name">要注释的名称</param>
    /// <returns>简单的文档注释字符串</returns>
    private static string GenerateSimpleDocumentation(string name)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"{I2}/// <summary>");
        sb.AppendLine($"{I2}/// {name}");
        sb.AppendLine($"{I2}/// </summary>");
        return sb.ToString();
    }

    /// <summary>
    /// 格式化单行文档注释 - 注释行格式化器
    ///
    /// 🎯 功能说明：
    /// 确保文档注释行有正确的格式和缩进
    ///
    /// 💡 处理规则：
    /// 1. 已经是 /// 格式的保持不变
    /// 2. // 格式的转换为 /// 格式
    /// 3. 其他格式的添加 /// 前缀
    /// 4. 确保正确的缩进（8个空格）
    ///
    /// 🔧 格式化示例：
    /// 输入："/// 这是注释"     -> 输出："        /// 这是注释"
    /// 输入："// 这是注释"      -> 输出："        /// 这是注释"
    /// 输入："这是注释"        -> 输出："        /// 这是注释"
    /// </summary>
    /// <param name="line">要格式化的行</param>
    /// <returns>格式化后的行</returns>
    private static string FormatDocumentationLine(string line)
    {
        // 🔧 移除行首的回车符和其他控制字符
        line = line.TrimStart('\r');

        if (string.IsNullOrWhiteSpace(line))
            return "";

        // 🔧 根据不同的格式进行处理
        if (line.StartsWith("///"))
        {
            // 已经是正确的 XML 文档注释格式，确保缩进
            return $"{I2}{line}";
        }
        else if (line.StartsWith("//"))
        {
            // 普通注释转换为 XML 文档注释
            return $"{I2}///{line.Substring(2)}";
        }
        else
        {
            // 其他格式，添加 /// 前缀
            return $"{I2}/// {line}";
        }
    }

    /// <summary>
    /// 验证 XML 文档注释的有效性 - 注释质量检查器
    ///
    /// 🎯 功能说明：
    /// 检查提取的 XML 文档注释是否符合标准格式
    ///
    /// 💡 验证规则：
    /// 1. 必须包含 summary 标签
    /// 2. XML 格式必须正确
    /// 3. 标签必须正确闭合
    ///
    /// 🔍 验证示例：
    /// 有效："/// <summary>\n/// 描述\n/// </summary>"
    /// 无效："/// 只有描述没有标签"
    /// </summary>
    /// <param name="xmlDocumentation">要验证的 XML 文档注释</param>
    /// <returns>true = 有效；false = 无效</returns>
    public static bool IsValidXmlDocumentation(string xmlDocumentation)
    {
        // 🛡️ 空值检查
        if (string.IsNullOrWhiteSpace(xmlDocumentation))
            return false;

        // 🔍 基本检查：必须包含 summary 标签
        if (!xmlDocumentation.Contains("<summary>") || !xmlDocumentation.Contains("</summary>"))
            return false;

        // 🔍 更多验证规则可以在这里添加
        // 例如：XML 格式验证、标签匹配验证等
        // 目前保持简单，只检查基本的 summary 标签

        return true;
    }

    /// <summary>
    /// 清理 XML 文档注释 - 注释内容清理器
    ///
    /// 🎯 功能说明：
    /// 移除多余的空行和格式化问题，确保注释内容的整洁
    ///
    /// 💡 清理规则：
    /// 1. 移除空行
    /// 2. 去除行尾空白
    /// 3. 保持注释结构完整
    /// </summary>
    /// <param name="xmlDocumentation">要清理的 XML 文档注释</param>
    /// <returns>清理后的 XML 文档注释</returns>
    public static string CleanXmlDocumentation(string xmlDocumentation)
    {
        // 🛡️ 空值检查
        if (string.IsNullOrWhiteSpace(xmlDocumentation))
            return "";

        // 🔧 按行分割并清理
        var lines = xmlDocumentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var cleanedLines = lines
            .Select(line => line.TrimEnd())           // 移除行尾空白
            .Where(line => !string.IsNullOrWhiteSpace(line))  // 移除空行
            .ToList();

        // 🔗 重新组合成字符串
        return string.Join("\n", cleanedLines);
    }

    #endregion
}
