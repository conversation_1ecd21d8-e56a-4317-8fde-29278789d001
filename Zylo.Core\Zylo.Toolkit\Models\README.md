# Models 数据模型层

> 🎯 **设计目标**：提供类型安全、不可变的数据模型，确保代码生成过程中的数据一致性

这个目录包含了 YService 功能的核心数据模型，使用现代 C# record 语法确保类型安全和不可变性。

## 📋 模型概览

### 🏗️ YServiceModels.cs
**统一数据模型定义文件**

包含了 YService 功能所需的所有数据模型，采用统一管理的方式确保一致性和易维护性。

## 🔧 核心数据模型

### 1. YServiceInfo - 服务信息模型

```csharp
/// <summary>
/// YService 服务信息的完整数据模型
/// </summary>
public record YServiceInfo(
    string ClassName,              // 类名
    string Namespace,              // 命名空间
    string Lifetime,               // 生命周期
    bool GenerateInterface,        // 是否生成接口
    string InterfacePrefix,        // 接口前缀
    bool IsPartial,               // 是否为 partial 类
    bool IsStatic,                // 是否为静态类
    string ClassDocumentation,     // 类文档注释
    List<MethodInfo> Methods,      // 方法列表
    bool IsMethodLevelTriggered    // 是否由方法级属性触发
);
```

#### 🎯 设计特点
- **不可变性**：使用 record 确保数据不可变
- **完整性**：包含代码生成所需的所有信息
- **类型安全**：强类型参数，避免运行时错误
- **文档友好**：详细的 XML 文档注释

#### 💡 计算属性
```csharp
/// <summary>
/// 接口名称（计算属性）
/// </summary>
public string InterfaceName => $"{InterfacePrefix}{ClassName}";

/// <summary>
/// 生成的接口文件名
/// </summary>
public string InterfaceFileName => $"{InterfaceName}.YService.yg.cs";

/// <summary>
/// 包装器类名称（用于静态类）
/// </summary>
public string WrapperClassName => $"{ClassName}Wrapper";
```

### 2. MethodInfo - 方法信息模型

```csharp
/// <summary>
/// 方法信息的详细数据模型
/// </summary>
public record MethodInfo(
    string Name,                   // 方法名
    string ReturnType,             // 返回类型
    string Parameters,             // 参数字符串
    string TypeParameters,         // 泛型参数
    string TypeConstraints,        // 类型约束
    string Documentation,          // 方法文档注释
    bool IsIgnored                 // 是否被忽略
);
```

#### 🎯 设计特点
- **完整签名**：包含方法签名的所有组成部分
- **文档保留**：完整保留 XML 文档注释
- **筛选支持**：支持忽略标记的方法
- **生成友好**：格式化后的字符串便于代码生成

#### 💡 计算属性
```csharp
/// <summary>
/// 完整的方法签名
/// </summary>
public string FullSignature => $"{ReturnType} {Name}{TypeParameters}({Parameters}){TypeConstraints}";

/// <summary>
/// 方法调用签名（用于包装器）
/// </summary>
public string CallSignature => $"{Name}{TypeParameters}({ParameterNames})";

/// <summary>
/// 参数名称列表（用于方法调用）
/// </summary>
public string ParameterNames => string.Join(", ", 
    Parameters.Split(',').Select(p => p.Trim().Split(' ').Last()));
```

## 🎨 设计模式

### 1. 不可变对象模式 (Immutable Object Pattern)

```csharp
// ✅ 使用 record 确保不可变性
public record YServiceInfo(...);

// ✅ 所有属性都是只读的
public string ClassName { get; init; }

// ✅ 修改时创建新实例
var updatedInfo = serviceInfo with { Lifetime = "Singleton" };
```

### 2. 值对象模式 (Value Object Pattern)

```csharp
// ✅ 基于值的相等性比较
var info1 = new YServiceInfo("UserService", "MyApp", "Scoped", ...);
var info2 = new YServiceInfo("UserService", "MyApp", "Scoped", ...);
Console.WriteLine(info1 == info2); // True

// ✅ 结构化相等性
Console.WriteLine(info1.Equals(info2)); // True
```

### 3. 建造者模式 (Builder Pattern)

虽然使用 record，但可以通过扩展方法提供建造者模式：

```csharp
public static class YServiceInfoExtensions
{
    public static YServiceInfo WithLifetime(this YServiceInfo info, string lifetime)
        => info with { Lifetime = lifetime };
    
    public static YServiceInfo WithMethods(this YServiceInfo info, List<MethodInfo> methods)
        => info with { Methods = methods };
}

// 使用示例
var serviceInfo = new YServiceInfo(...)
    .WithLifetime("Singleton")
    .WithMethods(methodList);
```

## 🔧 使用示例

### 创建 YServiceInfo

```csharp
// 类级模式
var classLevelInfo = new YServiceInfo(
    ClassName: "UserService",
    Namespace: "MyApp.Services",
    Lifetime: "Scoped",
    GenerateInterface: true,
    InterfacePrefix: "I",
    IsPartial: true,
    IsStatic: false,
    ClassDocumentation: "/// <summary>用户服务</summary>",
    Methods: methodList,
    IsMethodLevelTriggered: false
);

// 方法级模式
var methodLevelInfo = new YServiceInfo(
    ClassName: "DataProcessor",
    Namespace: "MyApp.Processors",
    Lifetime: "Scoped", // 默认值，实际生命周期在方法级处理
    GenerateInterface: true,
    InterfacePrefix: "I",
    IsPartial: true,
    IsStatic: false,
    ClassDocumentation: "/// <summary>数据处理器</summary>",
    Methods: filteredMethods,
    IsMethodLevelTriggered: true // 标记为方法级触发
);
```

### 创建 MethodInfo

```csharp
var methodInfo = new MethodInfo(
    Name: "GetUserAsync",
    ReturnType: "Task<User>",
    Parameters: "int id, bool includeDeleted = false",
    TypeParameters: "",
    TypeConstraints: "",
    Documentation: "/// <summary>获取用户信息</summary>",
    IsIgnored: false
);

// 泛型方法示例
var genericMethodInfo = new MethodInfo(
    Name: "ProcessAsync",
    ReturnType: "Task<T>",
    Parameters: "T input, CancellationToken cancellationToken = default",
    TypeParameters: "<T>",
    TypeConstraints: " where T : class, new()",
    Documentation: "/// <summary>处理数据</summary>",
    IsIgnored: false
);
```

## 💡 最佳实践

### 1. 数据验证

```csharp
// 在创建时进行基本验证
public record YServiceInfo(...)
{
    public YServiceInfo
    {
        if (string.IsNullOrEmpty(ClassName))
            throw new ArgumentException("ClassName cannot be null or empty");
        
        if (string.IsNullOrEmpty(Namespace))
            throw new ArgumentException("Namespace cannot be null or empty");
        
        Methods ??= new List<MethodInfo>();
    }
}
```

### 2. 默认值处理

```csharp
// 提供合理的默认值
public static YServiceInfo CreateDefault(string className, string namespaceName)
{
    return new YServiceInfo(
        ClassName: className,
        Namespace: namespaceName,
        Lifetime: "Scoped",           // 默认生命周期
        GenerateInterface: true,       // 默认生成接口
        InterfacePrefix: "I",         // 默认接口前缀
        IsPartial: true,              // 默认 partial
        IsStatic: false,              // 默认非静态
        ClassDocumentation: "",       // 空文档
        Methods: new List<MethodInfo>(),
        IsMethodLevelTriggered: false
    );
}
```

### 3. 集合处理

```csharp
// 安全的集合操作
public static class YServiceInfoExtensions
{
    public static YServiceInfo AddMethod(this YServiceInfo info, MethodInfo method)
    {
        var newMethods = new List<MethodInfo>(info.Methods) { method };
        return info with { Methods = newMethods };
    }
    
    public static YServiceInfo FilterMethods(this YServiceInfo info, Func<MethodInfo, bool> predicate)
    {
        var filteredMethods = info.Methods.Where(predicate).ToList();
        return info with { Methods = filteredMethods };
    }
}
```

## 🔄 与其他组件的协作

### 输入来源
- **Processors/YServiceClassProcessor** - 创建类级 YServiceInfo
- **Processors/YServiceMethodProcessor** - 创建方法级 YServiceInfo
- **Helper/XmlDocumentationExtractor** - 提供文档注释数据

### 输出用途
- **Temple/YServiceCodeGenerator** - 使用模型数据生成代码
- **Generators/YServiceGenerator** - 传递模型数据

### 数据流向
```
Processors (创建模型)
    ↓
Models (数据传递)
    ↓
Temple (使用模型生成代码)
```

## 🚀 扩展指南

### 添加新的模型属性

```csharp
// 1. 添加新属性到 record
public record YServiceInfo(
    // ... 现有属性
    string NewProperty  // 新属性
);

// 2. 更新所有创建点
var info = new YServiceInfo(
    // ... 现有参数
    NewProperty: "default value"
);

// 3. 添加计算属性（如需要）
public string ComputedProperty => $"Computed from {NewProperty}";
```

### 添加新的数据模型

```csharp
// 1. 定义新的 record
public record NewModelInfo(
    string Property1,
    int Property2,
    bool Property3
);

// 2. 添加到主模型（如需要）
public record YServiceInfo(
    // ... 现有属性
    NewModelInfo? AdditionalInfo = null
);
```

---

> 💡 **提示**：这些数据模型是整个 YService 功能的数据基础，确保了类型安全和数据一致性。在修改模型时，请确保更新所有相关的创建和使用代码。
