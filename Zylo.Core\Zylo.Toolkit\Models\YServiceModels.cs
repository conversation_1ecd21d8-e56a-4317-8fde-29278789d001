using Microsoft.Extensions.DependencyInjection;

namespace Zylo.Toolkit.Models;

/// <summary>
/// YService 数据模型集合 - 统一的数据模型定义
/// 
/// 🎯 设计目的：
/// 集中管理所有 YService 相关的数据模型，包括类级和方法级服务的数据结构
/// 
/// 📋 包含模型：
/// - YServiceInfo：统一的服务信息模型（支持类级和方法级）
/// - MethodInfo：方法信息模型
/// - YMethodInfo：方法级服务的扩展信息模型
/// 
/// 💡 设计理念：
/// - 统一管理：所有数据模型集中在一个文件中
/// - 类型安全：使用 record 确保数据不可变性
/// - 易于维护：清晰的结构和详细的文档
/// </summary>

#region 📊 核心数据模型

/// <summary>
/// 方法信息数据模型
///
/// 🎯 作用：
/// 封装从语法树中提取的方法信息，包括完整的 XML 文档注释
/// 
/// 💡 设计特点：
/// - 完整签名：包含返回类型、参数、泛型等完整信息
/// - 文档保留：保留原始的 XML 文档注释
/// - 忽略标记：支持 [YServiceIgnore] 属性排除
/// </summary>
/// <param name="Name">方法名称</param>
/// <param name="ReturnType">返回类型的完整名称</param>
/// <param name="Parameters">参数列表字符串（包含默认值）</param>
/// <param name="TypeParameters">泛型参数字符串（如 "&lt;T, U&gt;"）</param>
/// <param name="Constraints">泛型约束字符串（如 "where T : class"）</param>
/// <param name="XmlDocumentation">完整的 XML 文档注释</param>
/// <param name="IsIgnored">是否被 [YServiceIgnore] 标记</param>
public record MethodInfo(
    string Name,
    string ReturnType,
    string Parameters,
    string TypeParameters,
    string Constraints,
    string XmlDocumentation,
    bool IsIgnored,
    string? StaticLifetime = null // v1.2新增功能-静态方法级 YService
)
{
    /// <summary>
    /// 获取完整的方法签名（用于接口生成）
    /// </summary>
    public string FullSignature => $"{ReturnType} {Name}{TypeParameters}({Parameters})";

    /// <summary>
    /// 获取带约束的完整方法声明
    /// </summary>
    public string FullDeclaration => string.IsNullOrEmpty(Constraints)
        ? FullSignature
        : $"{FullSignature} {Constraints}";
}

/// <summary>
/// YService 统一信息数据模型
///
/// 🎯 作用：
/// 统一的服务信息模型，支持类级和方法级两种模式
/// 
/// 💡 设计理念：
/// - 统一模型：类级和方法级属性都使用同一个数据模型
/// - 完整信息：包含生成所有代码所需的信息
/// - 智能属性：提供计算属性简化代码生成逻辑
/// - 模式识别：通过 IsMethodLevelTriggered 区分处理模式
///
/// 🔧 关键特性：
/// - IsMethodLevelTriggered：区分类级和方法级属性模式
/// - 智能命名：自动处理静态类的包装器命名
/// - 灵活配置：支持各种生成选项的配置
/// </summary>
/// <param name="ClassName">类名称（不包含命名空间）</param>
/// <param name="Namespace">命名空间</param>
/// <param name="Lifetime">服务生命周期（Singleton/Scoped/Transient）</param>
/// <param name="GenerateInterface">是否生成对应的接口</param>
/// <param name="InterfacePrefix">接口名称前缀（通常为 "I"，当 CustomInterfaceName 为空时使用）</param>
/// <param name="CustomInterfaceName">自定义接口名称（优先级高于 InterfacePrefix）</param>
/// <param name="Description">服务描述（用于生成注释和文档）</param>
/// <param name="IsPartial">服务类是否声明为 partial（影响接口实现生成）</param>
/// <param name="IsStaticClass">服务类是否为静态类（影响包装器生成）</param>
/// <param name="ClassDocumentation">类级别的 XML 文档注释</param>
/// <param name="Methods">方法信息列表</param>
/// <param name="IsMethodLevelTriggered">是否由方法级属性触发（默认 false）</param>
public record YServiceInfo(
    string ClassName,
    string Namespace,
    string Lifetime,
    bool GenerateInterface,
    string InterfacePrefix,
    string? CustomInterfaceName,
    string? Description,
    bool IsPartial,
    bool IsStaticClass,
    string ClassDocumentation,
    List<MethodInfo> Methods,
    bool IsMethodLevelTriggered = false)
{
    /// <summary>
    /// 获取完整的服务类型名称（静态类使用包装器）
    /// </summary>
    public string FullServiceTypeName => $"{Namespace}.{ImplementationClassName}";

    /// <summary>
    /// 获取完整的接口类型名称（如果生成接口）
    /// </summary>
    public string? FullInterfaceTypeName => GenerateInterface ? $"{Namespace}.{InterfaceName}" : null;

    /// <summary>
    /// 获取接口名称（不包含命名空间）
    /// </summary>
    public string InterfaceName => !string.IsNullOrEmpty(CustomInterfaceName)
        ? CustomInterfaceName
        : $"{InterfacePrefix}{ClassName}";

    /// <summary>
    /// 获取包装器类名称（用于静态类）
    /// </summary>
    public string WrapperClassName => $"{ClassName}Wrapper";

    /// <summary>
    /// 获取实际实现类名称（静态类使用包装器，非静态类使用原类名）
    /// </summary>
    public string ImplementationClassName => IsStaticClass ? WrapperClassName : ClassName;

    /// <summary>
    /// 获取显示名称（用于调试和日志）
    /// </summary>
    public string DisplayName => GenerateInterface
        ? $"{InterfaceName} → {ImplementationClassName} ({Lifetime})" + (IsStaticClass ? " [Static Wrapper]" : "")
        : $"{ImplementationClassName} ({Lifetime})" + (IsStaticClass ? " [Static Wrapper]" : "");

    /// <summary>
    /// 获取处理模式描述
    /// </summary>
    public string ProcessingMode => IsMethodLevelTriggered ? "方法级属性模式" : "类级属性模式";
}

#endregion

#region 🔧 方法级服务扩展模型

/// <summary>
/// 方法级服务信息 - 方法级属性的扩展信息
/// 
/// 🎯 设计目的：
/// 为方法级属性提供额外的配置和信息，支持更细粒度的控制
/// 
/// 💡 核心特性：
/// - 支持混合生命周期：不同方法可以有不同的生命周期
/// - 选择性包含：只有标记了属性的方法才包含在接口中
/// - 灵活配置：每个方法可以独立配置接口名称等参数
/// 
/// 🔧 使用场景：
/// - 工具类：部分方法需要依赖注入，部分不需要
/// - 混合服务：不同方法有不同的生命周期需求
/// - 渐进式迁移：逐步将现有类迁移到依赖注入
/// </summary>
public record YMethodServiceInfo
{
    /// <summary>
    /// 类名
    /// </summary>
    public string ClassName { get; init; } = string.Empty;

    /// <summary>
    /// 命名空间
    /// </summary>
    public string Namespace { get; init; } = string.Empty;

    /// <summary>
    /// 接口名称，默认为 "I" + ClassName
    /// </summary>
    public string InterfaceName => $"I{ClassName}";

    /// <summary>
    /// 完整的接口名称（包含命名空间）
    /// </summary>
    public string FullInterfaceName => $"{Namespace}.{InterfaceName}";

    /// <summary>
    /// 完整的类名称（包含命名空间）
    /// </summary>
    public string FullClassName => $"{Namespace}.{ClassName}";

    /// <summary>
    /// 类级别的 XML 文档注释
    /// </summary>
    public string ClassDocumentation { get; init; } = string.Empty;

    /// <summary>
    /// 方法级服务信息列表
    /// 只包含标记了方法级服务属性的方法
    /// </summary>
    public List<YMethodInfo> Methods { get; init; } = new();
}

/// <summary>
/// 方法级服务的方法信息
/// 
/// 🎯 扩展功能：
/// 在基础 MethodInfo 的基础上，添加方法级服务特有的配置
/// </summary>
public record YMethodInfo
{
    /// <summary>
    /// 方法名称
    /// </summary>
    public string MethodName { get; init; } = string.Empty;

    /// <summary>
    /// 接口中的方法名称，默认与 MethodName 相同
    /// </summary>
    public string InterfaceMethodName { get; init; } = string.Empty;

    /// <summary>
    /// 实际的接口方法名称（如果未指定则使用原方法名）
    /// </summary>
    public string ActualInterfaceMethodName =>
        string.IsNullOrEmpty(InterfaceMethodName) ? MethodName : InterfaceMethodName;

    /// <summary>
    /// 返回类型
    /// </summary>
    public string ReturnType { get; init; } = string.Empty;

    /// <summary>
    /// 参数列表字符串
    /// 格式：type1 param1, type2 param2 = defaultValue
    /// </summary>
    public string Parameters { get; init; } = string.Empty;

    /// <summary>
    /// 泛型参数字符串
    /// 格式：&lt;T, U&gt; 或空字符串
    /// </summary>
    public string TypeParameters { get; init; } = string.Empty;

    /// <summary>
    /// 泛型约束字符串
    /// 格式：where T : class where U : struct
    /// </summary>
    public string TypeConstraints { get; init; } = string.Empty;

    /// <summary>
    /// XML 文档注释
    /// </summary>
    public string XmlDocumentation { get; init; } = string.Empty;

    /// <summary>
    /// 服务生命周期
    /// </summary>
    public ServiceLifetime Lifetime { get; init; } = ServiceLifetime.Scoped;

    /// <summary>
    /// 是否包含在接口中
    /// </summary>
    public bool IncludeInInterface { get; init; } = true;

    /// <summary>
    /// 是否被忽略（标记了 [YServiceIgnore]）
    /// </summary>
    public bool IsIgnored { get; init; } = false;

    /// <summary>
    /// 获取完整的方法签名
    /// </summary>
    public string FullSignature => $"{ReturnType} {ActualInterfaceMethodName}{TypeParameters}({Parameters})";

    /// <summary>
    /// 获取带约束的完整方法声明
    /// </summary>
    public string FullDeclaration => string.IsNullOrEmpty(TypeConstraints)
        ? FullSignature
        : $"{FullSignature} {TypeConstraints}";

    /// <summary>
    /// 获取生命周期描述
    /// </summary>
    public string LifetimeDescription => Lifetime switch
    {
        ServiceLifetime.Singleton => "Singleton",
        ServiceLifetime.Scoped => "Scoped",
        ServiceLifetime.Transient => "Transient",
        _ => "Unknown"
    };
}

#endregion
