namespace Zylo.Toolkit.Models;

/// <summary>
/// YStatic 数据模型集合 - 统一的数据模型定义
///
/// 🎯 设计目的：
/// 集中管理所有 YStatic 相关的数据模型，包括类级和方法级静态标签的数据结构
///
/// 📋 包含模型：
/// - YStaticInfo：统一的静态标签信息模型（支持类级和方法级）
/// - YStaticMethodInfo：方法信息模型
/// - YStaticConstants：统一的常量定义
///
/// 💡 设计理念：
/// - 统一管理：所有数据模型集中在一个文件中
/// - 类型安全：使用 record 确保数据不可变性
/// - 易于维护：清晰的结构和详细的文档
/// - 常量集中：统一管理命名规则和后缀
/// </summary>

#region 🔧 统一常量定义

/// <summary>
/// YStatic 常量定义 - 统一管理命名规则和后缀
///
/// 🎯 核心功能：
/// 集中定义所有 YStatic 相关的常量，便于统一修改和维护
///
/// 💡 设计理念：
/// - 统一命名：所有生成的类名后缀统一管理
/// - 易于修改：修改后缀只需要在这里改一处
/// - 类型安全：使用常量避免硬编码字符串
/// </summary>
public static class YStaticConstants
{
    /// <summary>
    /// 生成的扩展类名后缀
    ///
    /// 🎯 用途：
    /// - 类级属性：ClassName + Extensions
    /// - 方法级属性：ClassName + Extensions
    ///
    /// 💡 示例：
    /// - MathUtils → MathUtilsExtensions
    /// - StringHelper → StringHelperExtensions
    /// </summary>
    public const string ExtensionClassSuffix = "Es";

    /// <summary>
    /// 生成文件的扩展名
    /// </summary>
    public const string GeneratedFileExtension = ".yg.cs";

    /// <summary>
    /// 错误文件的扩展名
    /// </summary>
    public const string ErrorFileExtension = ".error.yg.cs";
}

#endregion

#region 📊 核心数据模型

/// <summary>
/// YStatic 方法信息数据模型
///
/// 🎯 作用：
/// 封装从语法树中提取的方法信息，包括完整的 XML 文档注释
///
/// 💡 设计特点：
/// - 完整签名：包含返回类型、参数、泛型等完整信息
/// - 文档保留：保留原始的 XML 文档注释
/// - 忽略标记：支持 [YStaticIgnore] 属性排除
/// </summary>
/// <param name="Name">方法名称</param>
/// <param name="ReturnType">返回类型的完整名称</param>
/// <param name="Parameters">参数列表字符串（包含默认值）</param>
/// <param name="GenericParameters">泛型参数字符串（如 "&lt;T, U&gt;"）</param>
/// <param name="GenericConstraints">泛型约束字符串（如 "where T : class"）</param>
/// <param name="Documentation">完整的 XML 文档注释</param>
/// <param name="IsExtensionMethod">是否为扩展方法模式</param>
/// <param name="ExtensionParameters">扩展方法的参数（第一个参数变为 this）</param>
/// <param name="IsStaticMethod">原始方法是否为静态方法</param>
/// <param name="IsIgnored">是否被 [YStaticIgnore] 标记</param>
public record YStaticMethodInfo(
    string Name,
    string ReturnType,
    string Parameters,
    string GenericParameters,
    string GenericConstraints,
    string? Documentation,
    bool IsExtensionMethod,
    string ExtensionParameters,
    bool IsStaticMethod,
    bool IsIgnored = false
);

/// <summary>
/// YStatic 信息数据模型
///
/// 🎯 作用：
/// 封装从语法树中提取的类信息，包括完整的静态标签生成配置
///
/// 💡 设计特点：
/// - 完整配置：包含类名、命名空间、生成模式等完整信息
/// - 方法集合：包含需要生成静态标签的方法列表
/// - 文档保留：保留原始的类 XML 文档注释
/// </summary>
/// <param name="ClassName">原始类名</param>
/// <param name="Namespace">命名空间</param>
/// <param name="ExtensionClassName">生成的扩展类名</param>
/// <param name="FullClassName">完整的原始类名（包含命名空间）</param>
/// <param name="FullExtensionClassName">完整的扩展类名（包含命名空间）</param>
/// <param name="IsStaticExtensionMode">是否为扩展方法模式</param>
/// <param name="IsClassLevelTriggered">是否由类级属性触发</param>
/// <param name="Methods">需要生成静态标签的方法列表</param>
/// <param name="ClassDocumentation">类的 XML 文档注释</param>
/// <param name="GenerateExtensions">是否生成扩展类</param>
public record YStaticInfo(
    string ClassName,
    string Namespace,
    string ExtensionClassName,
    string FullClassName,
    string FullExtensionClassName,
    bool IsStaticExtensionMode,
    bool IsClassLevelTriggered,
    List<YStaticMethodInfo> Methods,
    string? ClassDocumentation,
    bool GenerateExtensions = true
)
{
    /// <summary>
    /// 是否有需要生成的方法
    /// </summary>
    public bool HasMethods => Methods.Count > 0;

    /// <summary>
    /// 获取显示名称
    /// </summary>
    public string DisplayName => IsStaticExtensionMode ? $"{ClassName} (扩展方法)" : $"{ClassName} (静态方法)";
};

#endregion
