# Processors 业务处理层

> 🎯 **设计目标**：提供 YService 专用的业务处理器，实现复杂的业务逻辑和数据处理

这个目录包含了 YService 功能的核心业务处理器，负责处理类级和方法级的 YService 属性，并构建相应的数据模型。

## 📋 处理器概览

### 🔧 YServiceClassProcessor.cs
**类级处理器 - 处理类级 YService 属性**

#### 🎯 核心职责
- 🔍 类级属性验证：检查和验证类级 YService 属性
- ⚙️ 配置提取：从属性中提取服务配置信息
- 📝 文档处理：提取类级 XML 文档注释
- 🔧 方法分析：提取类中所有公共方法的信息
- 🏗️ 模型构建：构建完整的 YServiceInfo 对象

#### 🔧 处理特点
- 所有公共方法都包含在接口中（除非被 [YServiceIgnore] 排除）
- 支持静态类的包装器生成
- 完整保留 XML 文档注释
- 统一的生命周期管理

#### 🏗️ 逻辑结构
```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  🚀 公共入口点       │ -> │  🔍 属性验证与识别   │ -> │  ⚙️ 配置提取        │
│  ProcessClassLevel  │    │  属性检查和获取      │    │  参数解析和提取      │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                       ↓
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🔍 方法验证        │
│  YServiceInfo 构建  │    │  方法签名和文档      │    │  服务方法筛选        │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

### 🔧 YServiceMethodProcessor.cs
**方法级处理器 - 处理方法级 YService 属性**

#### 🎯 核心职责
- 🔍 方法级属性检测：识别标记了方法级属性的方法
- 🎯 选择性包含：只有标记了属性的方法包含在接口中
- 🔧 混合生命周期：支持不同方法使用不同的生命周期
- 🚫 智能筛选：排除私有方法、静态方法、被忽略的方法
- 🏗️ 模型构建：构建标准的 YServiceInfo 对象

#### 🔧 处理特点
- 只有标记了方法级属性的方法包含在接口中
- 支持不同方法使用不同的生命周期
- 自动排除不适合依赖注入的方法
- 冲突避免：只在类没有类级属性时生效

#### 🏗️ 逻辑结构
```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  🚀 公共入口点       │ -> │  🔍 候选识别        │ -> │  🎯 属性检测        │
│  ProcessMethodLevel │    │  类和方法筛选        │    │  方法级属性识别      │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                       ↓
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🚫 智能筛选        │
│  YServiceInfo 构建  │    │  方法签名和文档      │    │  不适合方法排除      │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## 🎯 设计模式

### 1. 策略模式 (Strategy Pattern)
不同的处理器实现不同的处理策略：

```csharp
// 类级处理策略
YServiceClassProcessor.ProcessClassLevel(...)

// 方法级处理策略  
YServiceMethodProcessor.ProcessMethodLevel(...)
```

### 2. 模板方法模式 (Template Method Pattern)
标准化的处理流程：

```csharp
public static YServiceInfo ProcessClassLevel(...)
{
    // 🔧 第一步：配置提取
    var config = ExtractConfiguration(...);
    
    // 📝 第二步：文档提取
    var docs = XmlDocumentationExtractor.ExtractFromClass(...);
    
    // 🔧 第三步：方法提取
    var methods = ExtractMethods(...);
    
    // 🏗️ 第四步：对象构建
    return new YServiceInfo(...);
}
```

### 3. 委托模式 (Delegation Pattern)
处理器委托给通用工具：

```csharp
// 委托给通用工具
var parameters = MethodSignatureHelper.GetParametersString(methodSymbol);
var isIgnored = SyntaxAnalysisHelper.HasMethodAttribute(method, "YServiceIgnore");
```

## 🔧 核心方法详解

### YServiceClassProcessor 核心方法

#### `ProcessClassLevel()` - 主入口点
```csharp
public static YServiceInfo ProcessClassLevel(
    ClassDeclarationSyntax classDeclaration,
    INamedTypeSymbol classSymbol,
    SemanticModel semanticModel,
    AttributeData yServiceAttribute)
```
- **功能**：处理类级 YService 属性的主入口点
- **流程**：配置提取 → 文档处理 → 方法分析 → 模型构建

#### `IsClassLevelYServiceCandidate()` - 候选识别
```csharp
public static bool IsClassLevelYServiceCandidate(SyntaxNode node)
```
- **功能**：检查类是否为类级 YService 候选
- **条件**：类声明 + 有属性 + partial 类

### YServiceMethodProcessor 核心方法

#### `ProcessMethodLevel()` - 主入口点
```csharp
public static YServiceInfo ProcessMethodLevel(
    ClassDeclarationSyntax classDeclaration,
    INamedTypeSymbol classSymbol,
    SemanticModel semanticModel)
```
- **功能**：处理方法级 YService 属性的主入口点
- **流程**：文档提取 → 方法筛选 → 模型构建

#### `IsYServiceRelatedCandidate()` - 智能候选识别
```csharp
public static bool IsYServiceRelatedCandidate(SyntaxNode node)
```
- **功能**：智能筛选器，协调类级和方法级候选识别
- **逻辑**：类级优先 → 方法级补充

## 🚀 使用示例

### 在 YServiceGenerator 中使用

```csharp
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
{
    var classDeclaration = (ClassDeclarationSyntax)context.Node;
    var classSymbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
    
    // 🏷️ 检查类级属性
    var yServiceAttribute = YServiceClassProcessor.GetYServiceAttribute(classSymbol);
    if (yServiceAttribute != null)
    {
        return YServiceClassProcessor.ProcessClassLevel(
            classDeclaration, classSymbol, context.SemanticModel, yServiceAttribute);
    }
    
    // 🔧 检查方法级属性
    if (YServiceMethodProcessor.HasMethodLevelYServiceAttributes(classDeclaration))
    {
        return YServiceMethodProcessor.ProcessMethodLevel(
            classDeclaration, classSymbol, context.SemanticModel);
    }
    
    return null;
}
```

## 💡 最佳实践

### 1. 代码组织
- 使用 #region 按功能分组
- 公共方法在前，私有方法在后
- 保持方法的单一职责

### 2. 错误处理
- 使用早期返回避免深层嵌套
- 使用 null 表示无效结果
- 提供清晰的错误信息

### 3. 性能优化
- 早期筛选减少后续处理
- 缓存计算结果
- 避免重复的语义分析

### 4. 扩展性设计
- 保持接口稳定
- 使用委托模式复用通用工具
- 为新功能预留扩展点

## 🔄 与其他组件的协作

### 输入依赖
- **Helper/SyntaxAnalysisHelper** - 语法分析和属性检测
- **Helper/MethodSignatureHelper** - 方法签名处理
- **Helper/XmlDocumentationExtractor** - 文档注释提取

### 输出产物
- **Models/YServiceInfo** - 完整的服务信息模型
- **Models/MethodInfo** - 详细的方法信息模型

### 调用关系
```
YServiceGenerator (协调者)
    ↓
YServiceClassProcessor / YServiceMethodProcessor (处理器)
    ↓
Helper/* (通用工具)
    ↓
Models/* (数据模型)
```

---

> 💡 **提示**：这些处理器是 YService 功能的核心，包含了所有的业务逻辑。在开发新功能时，可以参考这些处理器的设计模式和实现方式。
