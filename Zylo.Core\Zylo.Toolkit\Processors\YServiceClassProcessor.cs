using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Zylo.Toolkit.Helper;


namespace Zylo.Toolkit.Processors;

/// <summary>
/// YService 类级处理器 - 专门处理类级 YService 属性
/// 
/// 🎯 核心职责：
/// 1. 🔍 YService 类级属性验证：检查和验证类级 YService 属性
/// 2. ⚙️ YService 配置提取：从属性中提取服务配置信息
/// 3. 📝 文档处理：提取类级 XML 文档注释
/// 4. 🔧 方法分析：提取类中所有公共方法的信息
/// 5. 🏗️ YService 模型构建：构建完整的 YServiceInfo 对象
/// 
/// 💡 设计理念：
/// - YService 专用：专门为 YService 功能设计
/// - 单一职责：只处理类级属性相关的逻辑
/// - 完整处理：整个类作为一个服务单元
/// - 统一生命周期：类中所有方法使用相同的生命周期
/// 
/// 🔧 处理特点：
/// - 所有公共方法都包含在接口中（除非被 [YServiceIgnore] 排除）
/// - 支持静态类的包装器生成
/// - 完整保留 XML 文档注释
/// - 直接使用 Helper/ 中的通用工具，代码更直接高效
/// 
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 属性验证与识别   │ -> │  ⚙️ 配置提取        │
/// │  ProcessClassLevel  │    │  属性检查和获取      │    │  参数解析和提取      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
///                                       ↓
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🔍 方法验证        │
/// │  YServiceInfo 构建  │    │  方法签名和文档      │    │  服务方法筛选        │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
/// </summary>
public static class YServiceClassProcessor
{
    #region 🚀 公共入口点

    /// <summary>
    /// 处理类级 YService 属性的主入口点
    /// 
    /// 🎯 核心功能：
    /// 这是类级处理器的主入口点，负责协调整个处理流程
    /// 
    /// 🔧 处理流程：
    /// 1. ⚙️ 配置提取：从属性中提取服务配置信息
    /// 2. 📝 文档处理：提取类级 XML 文档注释
    /// 3. 🔧 方法分析：提取类中所有公共方法的信息
    /// 4. 🏗️ 模型构建：构建完整的 YServiceInfo 对象
    /// 
    /// 💡 设计理念：
    /// 作为协调者，将复杂的处理流程分解为清晰的步骤
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="classSymbol">类的语义符号</param>
    /// <param name="semanticModel">语义模型</param>
    /// <param name="yServiceAttribute">YService 属性数据</param>
    /// <returns>完整的 YServiceInfo 对象</returns>
    public static YServiceInfo ProcessClassLevel(
        ClassDeclarationSyntax classDeclaration,
        INamedTypeSymbol classSymbol,
        SemanticModel semanticModel,
        AttributeData yServiceAttribute)
    {
        // ⚙️ 第一步：配置提取
        var lifetime = GetLifetime(yServiceAttribute);
        var generateInterface = GetBooleanValue(yServiceAttribute, "GenerateInterface", true);
        var interfacePrefix = GetStringValue(yServiceAttribute, "InterfacePrefix", "I");
        var customInterfaceName = GetStringValue(yServiceAttribute, "InterfaceName", null);
        var description = GetStringValue(yServiceAttribute, "Description", null);

        // 📝 第二步：类文档注释提取
        var classDocumentation = YXmlDocumentationExtractor.ExtractFromClass(classDeclaration);

        // 🔧 第三步：方法信息提取
        var methods = ExtractMethodsWithDocumentation(classDeclaration, semanticModel);

        // 🏗️ 第四步：对象构建
        return new YServiceInfo(
            classSymbol.Name,
            classSymbol.ContainingNamespace.ToDisplayString(),
            lifetime,
            generateInterface,
            interfacePrefix,
            customInterfaceName,
            description,
            classDeclaration.Modifiers.Any(SyntaxKind.PartialKeyword),
            classDeclaration.Modifiers.Any(SyntaxKind.StaticKeyword),
            classDocumentation,
            methods,
            IsMethodLevelTriggered: false);
    }

    #endregion

    #region 🔍 属性验证与识别

    /// <summary>
    /// 检查类是否有类级 YService 属性
    /// 
    /// 🎯 核心功能：
    /// 快速检查类是否标记了类级 YService 相关属性
    /// 
    /// 💡 检查的属性：
    /// - [YService]
    /// - [YServiceScoped]
    /// - [YServiceSingleton] 
    /// - [YServiceTransient]
    /// </summary>
    /// <param name="classSymbol">类的语义符号</param>
    /// <returns>如果有类级属性返回 true，否则返回 false</returns>
    public static bool HasClassLevelYServiceAttribute(INamedTypeSymbol classSymbol)
    {
        return GetYServiceAttribute(classSymbol) != null;
    }

    /// <summary>
    /// 获取类的 YService 相关属性
    /// 
    /// 🎯 核心功能：
    /// 从类的属性列表中找到第一个 YService 相关属性
    /// 
    /// 🔍 查找逻辑：
    /// 遍历类的所有属性，找到名称以 "YService" 开头且以 "Attribute" 结尾的属性
    /// </summary>
    /// <param name="classSymbol">类的语义符号</param>
    /// <returns>YService 属性数据，如果没有找到返回 null</returns>
    public static AttributeData? GetYServiceAttribute(INamedTypeSymbol classSymbol)
    {
        return classSymbol.GetAttributes()
            .FirstOrDefault(attr => IsYServiceRelatedAttribute(attr.AttributeClass));
    }

    /// <summary>
    /// 检查属性类是否为 YService 相关属性
    /// 
    /// 🎯 识别规则：
    /// 属性类名称以 "YService" 开头且以 "Attribute" 结尾
    /// 
    /// 🔍 支持的属性：
    /// - YServiceAttribute (基础属性)
    /// - YServiceScopedAttribute (便捷属性)
    /// - YServiceSingletonAttribute (便捷属性)
    /// - YServiceTransientAttribute (便捷属性)
    /// </summary>
    /// <param name="attributeClass">属性类符号</param>
    /// <returns>如果是 YService 相关属性返回 true，否则返回 false</returns>
    private static bool IsYServiceRelatedAttribute(INamedTypeSymbol? attributeClass)
    {
        if (attributeClass == null)
            return false;

        return attributeClass.Name.StartsWith("YService") &&
               attributeClass.Name.EndsWith("Attribute");
    }

    #endregion

    #region ⚙️ 配置提取

    /// <summary>
    /// 从属性数据中提取服务生命周期
    /// 
    /// 🎯 提取策略（按优先级）：
    /// 1. 🏷️ 便捷属性类型：YServiceScopedAttribute → "Scoped"
    /// 2. 🔧 构造函数参数：[YService(ServiceLifetime.Singleton)]
    /// 3. 📝 命名参数：[YService(Lifetime = ServiceLifetime.Scoped)]
    /// 4. 🛡️ 默认值：如果都没找到，使用 "Scoped"
    /// 
    /// 💡 设计理念：
    /// 提供多种配置方式，便捷属性优先，向下兼容
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <returns>生命周期字符串</returns>
    public static string GetLifetime(AttributeData attribute)
    {
        // 🏷️ 第一优先级：检查便捷属性类型名称
        var attributeClassName = attribute.AttributeClass?.Name;
        if (attributeClassName != null)
        {
            var lifetimeFromName = attributeClassName switch
            {
                "YServiceScopedAttribute" => "Scoped",
                "YServiceSingletonAttribute" => "Singleton",
                "YServiceTransientAttribute" => "Transient",
                _ => null
            };

            if (lifetimeFromName != null)
                return lifetimeFromName;
        }

        // 🔧 第二优先级：检查构造函数参数
        if (attribute.ConstructorArguments.Length > 0)
        {
            var lifetimeValue = attribute.ConstructorArguments[0].Value;
            return lifetimeValue?.ToString() ?? "Scoped";
        }

        // 📝 第三优先级：检查命名参数
        var lifetimeArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == "Lifetime");
        if (!lifetimeArg.Equals(default))
        {
            return lifetimeArg.Value.Value?.ToString() ?? "Scoped";
        }

        // 🛡️ 默认值
        return "Scoped";
    }

    /// <summary>
    /// 从属性中获取布尔值参数
    /// 
    /// 🎯 使用场景：
    /// 提取 GenerateInterface、IsPartial 等布尔配置参数
    /// 
    /// 💡 处理逻辑：
    /// 查找命名参数，如果找到则使用其值，否则使用默认值
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <param name="name">参数名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>布尔值</returns>
    public static bool GetBooleanValue(AttributeData attribute, string name, bool defaultValue)
    {
        var arg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == name);
        return !arg.Equals(default) ? (bool)(arg.Value.Value ?? defaultValue) : defaultValue;
    }

    /// <summary>
    /// 从属性中获取字符串值参数
    /// 
    /// 🎯 使用场景：
    /// 提取 InterfacePrefix 等字符串配置参数
    /// 
    /// 💡 处理逻辑：
    /// 查找命名参数，如果找到则使用其值，否则使用默认值
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <param name="name">参数名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>字符串值</returns>
    public static string GetStringValue(AttributeData attribute, string name, string defaultValue)
    {
        // 首先检查命名参数（属性设置）
        var namedArg = attribute.NamedArguments.FirstOrDefault(arg => arg.Key == name);
        if (!namedArg.Equals(default) && namedArg.Value.Value != null)
        {
            return (string)namedArg.Value.Value;
        }

        // 然后检查构造函数参数（便捷属性的参数）
        // 对于便捷属性，InterfaceName 通常是第一个构造函数参数
        if (name == "InterfaceName" && attribute.ConstructorArguments.Length > 0)
        {
            var constructorArg = attribute.ConstructorArguments[0];
            if (constructorArg.Value != null && constructorArg.Value is string stringValue)
            {
                return stringValue;
            }
        }

        // 对于便捷属性，Description 通常是第二个构造函数参数
        if (name == "Description" && attribute.ConstructorArguments.Length > 1)
        {
            var constructorArg = attribute.ConstructorArguments[1];
            if (constructorArg.Value != null && constructorArg.Value is string stringValue)
            {
                return stringValue;
            }
        }

        return defaultValue;
    }

    #endregion

    #region 🔧 方法信息提取

    /// <summary>
    /// 从类声明中提取方法信息（包含 XML 文档注释）
    ///
    /// 🎯 核心功能：
    /// 提取类中所有公共方法的详细信息，用于接口生成
    ///
    /// 🔍 处理逻辑：
    /// 1. 遍历类中的所有成员
    /// 2. 筛选出公共方法
    /// 3. 处理静态/非静态方法的逻辑
    /// 4. 提取方法签名和文档注释
    /// 5. 检查 [YServiceIgnore] 标记
    ///
    /// 💡 设计理念：
    /// 完整提取所有适合的方法，为接口生成提供完整信息
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <returns>方法信息列表</returns>
    private static List<MethodInfo> ExtractMethodsWithDocumentation(
        ClassDeclarationSyntax classDeclaration,
        SemanticModel semanticModel)
    {
        var methods = new List<MethodInfo>();
        var isStaticClass = classDeclaration.Modifiers.Any(SyntaxKind.StaticKeyword);

        foreach (var member in classDeclaration.Members)
        {
            if (member is not MethodDeclarationSyntax methodDeclaration)
                continue;

            // 🔍 获取方法的语义信息
            if (semanticModel.GetDeclaredSymbol(methodDeclaration) is not IMethodSymbol methodSymbol)
                continue;

            // 🔍 验证方法是否适合包含在服务接口中
            if (!IsValidServiceMethod(methodSymbol, methodDeclaration, isStaticClass))
                continue;

            // 📝 提取 XML 文档注释
            var xmlDoc = YXmlDocumentationExtractor.ExtractFromMethod(methodDeclaration);

            // 🔍 检查是否被 YServiceIgnore 标记
            var isIgnored = YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YServiceIgnore");

            // 🔧 直接使用通用工具提取方法签名信息
            var returnType = methodSymbol.ReturnType.ToDisplayString();
            var parameters = YMethodSignatureHelper.GetParametersString(methodSymbol);
            var typeParameters = YMethodSignatureHelper.GetTypeParametersString(methodSymbol);
            var constraints = YMethodSignatureHelper.GetTypeConstraintsString(methodSymbol);

            var methodInfo = new MethodInfo(
                methodSymbol.Name,
                returnType,
                parameters,
                typeParameters,
                constraints,
                xmlDoc,
                isIgnored);

            methods.Add(methodInfo);
        }

        return methods;
    }

    #endregion

    #region 🔍 YService 专用验证方法

    /// <summary>
    /// 检查方法是否适合包含在 YService 接口中
    ///
    /// 🎯 核心功能：
    /// 综合检查方法是否符合 YService 接口的要求
    ///
    /// 💡 检查条件：
    /// - 必须是普通方法（不是构造函数、属性访问器等）
    /// - 必须是公共方法
    /// - 不能是静态方法（对于非静态类）
    /// - 不能被 [YServiceIgnore] 标记
    ///
    /// 🔧 YService 专用逻辑：
    /// 这个方法包含 YService 特有的业务规则，不适合放在通用工具中
    ///
    /// 💡 静态类处理：
    /// - 静态类：只包含静态方法
    /// - 非静态类：只包含非静态方法
    /// </summary>
    /// <param name="methodSymbol">方法符号</param>
    /// <param name="methodDeclaration">方法声明语法节点</param>
    /// <param name="isStaticClass">所属类是否为静态类</param>
    /// <returns>如果适合包含在接口中返回 true，否则返回 false</returns>
    private static bool IsValidServiceMethod(IMethodSymbol methodSymbol, MethodDeclarationSyntax methodDeclaration, bool isStaticClass)
    {
        // 🚫 排除构造函数等特殊方法
        if (methodSymbol.MethodKind != MethodKind.Ordinary)
            return false;

        // 🚫 排除非公共方法
        if (methodSymbol.DeclaredAccessibility != Accessibility.Public)
            return false;

        // 🔍 静态方法处理逻辑
        var isStaticMethod = methodSymbol.IsStatic;

        if (isStaticClass && !isStaticMethod)
            return false; // 静态类中的非静态方法（不应该存在）

        if (!isStaticClass && isStaticMethod)
            return false; // 非静态类中的静态方法（排除）

        // 🚫 排除被 YServiceIgnore 标记的方法
        if (YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YServiceIgnore"))
            return false;

        return true;
    }

    #endregion

    #region 🔍 YService 专用候选识别

    /// <summary>
    /// 检查类是否为类级 YService 候选
    ///
    /// 🎯 核心功能：
    /// 专门检查类是否标记了类级 YService 相关属性
    ///
    /// 💡 YService 专用逻辑：
    /// 这个方法包含 YService 特有的业务规则，属于 Processors 层的职责
    ///
    /// 🔧 检查条件：
    /// 1. 必须是类声明
    /// 2. 必须有属性标记
    /// 3. 必须是 partial 类（代码生成要求）
    /// </summary>
    /// <param name="node">语法节点</param>
    /// <returns>如果是类级 YService 候选返回 true，否则返回 false</returns>
    public static bool IsClassLevelYServiceCandidate(SyntaxNode node)
    {
        return node is ClassDeclarationSyntax classDeclaration &&
               classDeclaration.AttributeLists.Count > 0 &&
               YSyntaxAnalysisHelper.IsPartialClass(classDeclaration);
    }

    #endregion
}
