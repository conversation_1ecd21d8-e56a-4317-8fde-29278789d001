using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Zylo.Toolkit.Helper;


namespace Zylo.Toolkit.Processors;

/// <summary>
/// YService 方法级处理器 - 专门处理方法级 YService 属性
/// 
/// 🎯 核心职责：
/// 1. 🔍 YService 方法级属性检测：识别标记了方法级属性的方法
/// 2. 🎯 选择性包含：只有标记了属性的方法包含在接口中
/// 3. 🔧 混合生命周期：支持不同方法使用不同的生命周期
/// 4. 🚫 智能筛选：排除私有方法、静态方法、被忽略的方法
/// 5. 🏗️ YService 模型构建：构建标准的 YServiceInfo 对象
/// 
/// 💡 设计理念：
/// - YService 专用：专门为 YService 功能设计
/// - 细粒度控制：每个方法可以独立配置
/// - 渐进式迁移：支持逐步将现有类迁移到依赖注入
/// - 冲突避免：只在类没有类级属性时生效
/// 
/// 🔧 处理特点：
/// - 只有标记了方法级属性的方法包含在接口中
/// - 支持不同方法使用不同的生命周期
/// - 自动排除不适合依赖注入的方法
/// - 直接使用 Helper/ 中的通用工具，代码更直接高效
/// 
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 候选识别        │ -> │  🎯 属性检测        │
/// │  ProcessMethodLevel │    │  类和方法筛选        │    │  方法级属性识别      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
///                                       ↓
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🚫 智能筛选        │
/// │  YServiceInfo 构建  │    │  方法签名和文档      │    │  不适合方法排除      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
/// </summary>
public static class YServiceMethodProcessor
{
    #region 🚀 公共入口点

    /// <summary>
    /// 处理方法级 YService 属性的主入口点
    /// 
    /// 🎯 核心功能：
    /// 当类没有类级 YService 属性时，检查方法级属性并生成对应的服务信息
    /// 
    /// 💡 设计理念：
    /// - 只有标记了方法级属性的方法才包含在接口中
    /// - 每个方法可以有独立的生命周期配置
    /// - 支持混合生命周期：不同方法可以有不同的生命周期
    /// 
    /// 🔧 处理流程：
    /// 1. 遍历所有方法，找到有方法级 YService 属性的方法
    /// 2. 提取每个方法的属性配置（生命周期、接口名称等）
    /// 3. 构建方法级服务信息
    /// 4. 转换为标准的 YServiceInfo 对象（保持兼容性）
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="classSymbol">类的语义符号</param>
    /// <param name="semanticModel">语义模型</param>
    /// <returns>YServiceInfo 对象</returns>
    public static YServiceInfo ProcessMethodLevel(
        ClassDeclarationSyntax classDeclaration,
        INamedTypeSymbol classSymbol,
        SemanticModel semanticModel)
    {
        // 📝 第一步：提取类文档注释
        var classDocumentation = YXmlDocumentationExtractor.ExtractFromClass(classDeclaration);

        // 🔍 第二步：提取有方法级属性的方法信息
        var methodInfos = ExtractMethodLevelServiceMethods(classDeclaration, semanticModel);

        // 🏗️ 第三步：构建标准的 YServiceInfo 对象
        // 对于方法级属性，我们使用默认的类级配置，但标记为方法级触发
        return new YServiceInfo(
            classSymbol.Name,
            classSymbol.ContainingNamespace.ToDisplayString(),
            "Scoped", // 默认生命周期（具体的生命周期在代码生成时处理）
            true, // 生成接口
            "I", // 接口前缀
            null, // 自定义接口名称（方法级模式使用默认）
            null, // 服务描述（方法级模式使用默认）
            true, // partial 类
            false, // 非静态类
            classDocumentation,
            methodInfos,
            IsMethodLevelTriggered: true // 🔥 标记为方法级属性触发
        );
    }

    #endregion

    #region 🔍 候选识别

    /// <summary>
    /// 检查类是否为方法级 YService 候选
    /// 
    /// 🎯 核心功能：
    /// 综合检查类是否适合进行方法级 YService 生成
    /// 
    /// 💡 检查条件：
    /// 1. 必须是 partial 类
    /// 2. 没有类级 YService 属性（避免冲突）
    /// 3. 有至少一个方法标记了方法级 YService 属性
    /// 
    /// 🔧 设计理念：
    /// 方法级属性只在类没有类级属性时生效，避免配置冲突
    /// </summary>
    /// <param name="node">语法节点</param>
    /// <returns>如果是方法级服务候选返回 true，否则返回 false</returns>
    public static bool IsMethodLevelYServiceCandidate(SyntaxNode node)
    {
        // 🔍 第一步：检查是否是类声明
        if (node is not ClassDeclarationSyntax classDeclaration)
            return false;

        // 🔍 第二步：检查是否是 partial 类
        if (!YSyntaxAnalysisHelper.IsPartialClass(classDeclaration))
            return false;

        // 🔍 第三步：检查是否有类级 YService 属性
        // 如果有类级属性，则不处理方法级属性（避免冲突）
        if (YSyntaxAnalysisHelper.IsAttributedClass(node))
            return false;

        // 🔍 第四步：检查是否有方法级 YService 属性
        return HasMethodLevelYServiceAttributes(classDeclaration);
    }

    /// <summary>
    /// 检查类是否有方法级 YService 属性
    /// 
    /// 🎯 核心功能：
    /// 检测类中是否有方法标记了 YService 相关的方法级属性
    /// 
    /// 💡 使用场景：
    /// 当类没有类级 YService 属性时，检查是否有方法级属性来触发服务生成
    /// 
    /// 🔧 实现原理：
    /// 使用 YSyntaxAnalysisHelper 提供的通用方法检测工具
    /// 传入 YService 特定的属性名称列表进行检测
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <returns>如果找到方法级 YService 属性返回 true，否则返回 false</returns>
    public static bool HasMethodLevelYServiceAttributes(ClassDeclarationSyntax classDeclaration)
    {
        return YSyntaxAnalysisHelper.HasMethodsWithAttributes(classDeclaration, YServiceMethodLevelAttributes);
    }

    #endregion

    #region 🎯 属性检测配置

    /// <summary>
    /// YService 方法级属性名称列表
    /// 
    /// 🎯 设计目的：
    /// 集中管理 YService 相关的方法级属性名称，便于维护和扩展
    /// 
    /// 💡 支持的属性：
    /// - YServiceScoped: 方法级 Scoped 生命周期
    /// - YServiceSingleton: 方法级 Singleton 生命周期
    /// - YServiceTransient: 方法级 Transient 生命周期
    /// </summary>
    private static readonly string[] YServiceMethodLevelAttributes =
    [
        "YServiceScoped", "YServiceSingleton", "YServiceTransient"
    ];

    /// <summary>
    /// YService 属性名称到生命周期类型的映射
    /// 
    /// 🎯 设计目的：
    /// 提供属性名称到生命周期类型的映射，用于确定方法的服务生命周期
    /// 
    /// 💡 映射关系：
    /// - YServiceScoped → "Scoped"
    /// - YServiceSingleton → "Singleton"
    /// - YServiceTransient → "Transient"
    /// </summary>
    private static readonly Dictionary<string, string> YServiceAttributeMapping = new()
    {
        { "YServiceScoped", "Scoped" },
        { "YServiceSingleton", "Singleton" },
        { "YServiceTransient", "Transient" }
    };

    /// <summary>
    /// 获取方法的 YService 属性类型
    /// 
    /// 🎯 核心功能：
    /// 确定方法标记的具体 YService 属性类型（Scoped、Singleton、Transient）
    /// 
    /// 💡 返回值：
    /// - "Scoped": YServiceScoped 属性
    /// - "Singleton": YServiceSingleton 属性
    /// - "Transient": YServiceTransient 属性
    /// - null: 没有找到方法级 YService 属性
    /// 
    /// 🔧 实现原理：
    /// 使用 YSyntaxAnalysisHelper 提供的通用属性类型获取工具
    /// 传入 YService 特定的属性映射进行类型确定
    /// </summary>
    /// <param name="methodDeclaration">方法声明语法节点</param>
    /// <returns>属性类型字符串或 null</returns>
    public static string? GetMethodLevelYServiceAttributeType(MethodDeclarationSyntax methodDeclaration)
    {
        return YSyntaxAnalysisHelper.GetMethodAttributeType(methodDeclaration, YServiceAttributeMapping);
    }

    #endregion

    #region 🔧 方法信息提取

    /// <summary>
    /// 提取有方法级 YService 属性的方法信息
    ///
    /// 🎯 核心功能：
    /// 扫描类中的所有方法，找到标记了方法级 YService 属性的方法，并提取其详细信息
    ///
    /// 🔍 处理逻辑：
    /// 1. 遍历类的所有方法
    /// 2. 检查方法是否有 YService 属性
    /// 3. 验证方法是否适合依赖注入
    /// 4. 提取方法签名和文档信息
    /// 5. 构建方法信息对象
    ///
    /// 💡 筛选条件：
    /// - 必须是普通方法（不是构造函数等）
    /// - 必须是公共方法
    /// - 不能是静态方法
    /// - 不能被 [YServiceIgnore] 标记
    /// - 必须标记了方法级 YService 属性
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <returns>方法信息列表</returns>
    private static List<MethodInfo> ExtractMethodLevelServiceMethods(
        ClassDeclarationSyntax classDeclaration,
        SemanticModel semanticModel)
    {
        var methodInfos = new List<MethodInfo>();

        foreach (var member in classDeclaration.Members)
        {
            if (member is not MethodDeclarationSyntax methodDeclaration)
                continue;

            // 🚫 排除构造函数等特殊方法
            if (semanticModel.GetDeclaredSymbol(methodDeclaration) is not IMethodSymbol methodSymbol)
                continue;

            if (methodSymbol.MethodKind != MethodKind.Ordinary)
                continue;

            // 🚫 排除非公共方法
            if (methodSymbol.DeclaredAccessibility != Accessibility.Public)
                continue;

            // 🚀 v1.2新增：支持静态方法（移除静态方法排除限制）
            var isStaticMethod = methodSymbol.IsStatic;

            // 🔍 检查是否被 YServiceIgnore 标记
            var isIgnored = YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YServiceIgnore");
            if (isIgnored)
                continue;

            // 🎯 检查方法是否有 YService 属性
            var attributeType = GetMethodLevelYServiceAttributeType(methodDeclaration);
            if (attributeType == null)
                continue;

            // 🔧 直接使用通用工具提取方法详细信息
            var returnType = methodSymbol.ReturnType.ToDisplayString();
            var parameters = YMethodSignatureHelper.GetParametersString(methodSymbol);
            var typeParameters = YMethodSignatureHelper.GetTypeParametersString(methodSymbol);
            var typeConstraints = YMethodSignatureHelper.GetTypeConstraintsString(methodSymbol);
            var xmlDoc = YXmlDocumentationExtractor.ExtractFromMethod(methodDeclaration);

            // 🏗️ 构建方法信息 - v1.2新增：支持静态方法
            var methodInfo = new MethodInfo(
                methodSymbol.Name,
                returnType,
                parameters,
                typeParameters,
                typeConstraints,
                xmlDoc,
                isIgnored,
                isStaticMethod ? attributeType : null // v1.2新增功能-静态方法级 YService
            );

            methodInfos.Add(methodInfo);
        }

        return methodInfos;
    }
    #region 🔍 YService 智能候选识别

    /// <summary>
    /// 智能 YService 候选筛选器 - 找到所有可能相关的类
    ///
    /// 🎯 核心功能：
    /// 这是一个"更聪明"的筛选器，不仅找有属性的类，
    /// 还会找那些"内部可能有 YService 方法"的类
    ///
    /// 🔍 筛选逻辑：
    /// 1. 类本身有属性 → 委托给类级处理器
    /// 2. 类没有属性，但内部有方法有属性 → 方法级候选
    /// 3. 完全没有属性 → 不是候选
    ///
    /// 💡 YService 专用场景：
    /// ```csharp
    /// // 情况1：类级别属性（委托给 YServiceClassProcessor）
    /// [YService]
    /// public class UserService { }
    ///
    /// // 情况2：方法级别属性（本处理器处理）
    /// public class DataService
    /// {
    ///     [YServiceScoped] public void ProcessData() { }
    /// }
    /// ```
    ///
    /// 🏗️ 架构设计：
    /// 这个方法作为总入口，协调类级和方法级的候选识别
    /// </summary>
    /// <param name="node">语法节点</param>
    /// <returns>true = 可能包含 YService 相关内容；false = 肯定不包含</returns>
    public static bool IsYServiceRelatedCandidate(SyntaxNode node)
    {
        // 🔍 只处理类声明
        if (node is not ClassDeclarationSyntax classDeclaration)
            return false;

        // 🔍 必须是 partial 类
        if (!YSyntaxAnalysisHelper.IsPartialClass(classDeclaration))
            return false;

        // 🚀 情况1：类本身有属性 → 委托给类级处理器
        if (YServiceClassProcessor.IsClassLevelYServiceCandidate(node))
            return true;

        // 🔍 情况2：检查类内部是否有带 YService 属性的方法 → 方法级处理
        return IsMethodLevelYServiceCandidate(node);
    }

    #endregion
}

#endregion
