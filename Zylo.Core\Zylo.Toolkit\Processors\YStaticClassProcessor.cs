using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Zylo.Toolkit.Helper;
using Zylo.Toolkit.Models;

namespace Zylo.Toolkit.Processors;

/// <summary>
/// YStatic 类级处理器 - 专门处理类级 YStatic 属性
///
/// 🎯 核心职责：
/// 1. 🔍 YStatic 类级属性验证：检查和验证类级 YStatic 属性
/// 2. ⚙️ YStatic 配置提取：从属性中提取静态标签配置信息
/// 3. 📝 文档处理：提取类级 XML 文档注释
/// 4. 🔧 方法分析：提取类中所有公共方法的信息
/// 5. 🏗️ YStatic 模型构建：构建完整的 YStaticInfo 对象
///
/// 💡 设计理念：
/// - YStatic 专用：专门为 YStatic 功能设计
/// - 单一职责：只处理类级属性相关的逻辑
/// - 完整处理：整个类作为一个静态标签单元
/// - 统一生成模式：类中所有方法使用相同的生成模式
///
/// 🔧 处理特点：
/// - 所有公共方法都包含在生成中（除非被 [YStaticIgnore] 排除）
/// - 支持扩展方法模式的智能转换
/// - 完整保留 XML 文档注释
/// - 直接使用 Helper/ 中的通用工具，代码更直接高效
///
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 属性验证与识别   │ -> │  ⚙️ 配置提取        │
/// │  ProcessClassLevel  │    │  属性检查和获取      │    │  参数解析和提取      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
///                                       ↓
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🔍 方法验证        │
/// │  YStaticInfo 构建   │    │  方法签名和文档      │    │  静态标签方法筛选    │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
/// </summary>
public static class YStaticClassProcessor
{
    #region 🚀 公共入口点

    /// <summary>
    /// 处理类级 YStatic 属性的主入口点
    ///
    /// 🎯 核心功能：
    /// 这是类级处理器的主入口点，负责协调整个处理流程
    ///
    /// 💡 设计理念：
    /// - 完全按照 YService 的处理模式
    /// - 简洁直接的处理逻辑
    /// - 统一的错误处理和验证
    ///
    /// 🔧 处理流程：
    /// 1. 配置提取：从属性中提取生成配置
    /// 2. 文档提取：提取类的 XML 文档注释
    /// 3. 方法提取：提取类中所有公共方法的信息
    /// 4. 对象构建：构建完整的 YStaticInfo 对象
    ///
    /// 🚀 使用场景：
    /// - 类级 [YStatic] 属性：为所有公共方法生成普通静态方法
    /// - 类级 [YStaticExtension] 属性：为所有公共方法生成扩展方法
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="classSymbol">类的语义符号</param>
    /// <param name="semanticModel">语义模型</param>
    /// <param name="yStaticAttribute">YStatic 属性数据</param>
    /// <returns>完整的 YStaticInfo 对象</returns>
    public static YStaticInfo ProcessClassLevel(
        ClassDeclarationSyntax classDeclaration,
        INamedTypeSymbol classSymbol,
        SemanticModel semanticModel,
        AttributeData yStaticAttribute)
    {
        // ⚙️ 第一步：配置提取
        // 根据属性类型确定是否为扩展方法模式
        var isExtensionMode = IsYStaticExtensionAttribute(yStaticAttribute);
        var generateExtensions = GetBooleanValue(yStaticAttribute, "GenerateExtensions", true);
        var extensionClassName = GetStringValue(yStaticAttribute, "ExtensionClassName", null) ?? $"{classSymbol.Name}{YStaticConstants.ExtensionClassSuffix}";

        // 📝 第二步：类文档注释提取
        var classDocumentation = YXmlDocumentationExtractor.ExtractFromClass(classDeclaration);

        // 🔧 第三步：方法信息提取
        var methods = ExtractMethodsWithDocumentation(classDeclaration, semanticModel, isExtensionMode);

        // 🏗️ 第四步：对象构建
        return new YStaticInfo(
            classSymbol.Name,
            classSymbol.ContainingNamespace.ToDisplayString(),
            extensionClassName,
            classSymbol.ToDisplayString(),
            $"{classSymbol.ContainingNamespace.ToDisplayString()}.{extensionClassName}",
            isExtensionMode,
            true, // IsClassLevelTriggered
            methods,
            classDocumentation,
            generateExtensions);
    }

    #endregion

    #region 🔍 属性验证与识别

    /// <summary>
    /// 检查类是否有类级 YStatic 属性
    ///
    /// 🎯 核心功能：
    /// 快速检查类是否标记了类级 YStatic 相关属性
    ///
    /// 💡 检查的属性：
    /// - [YStatic]
    /// - [YStaticExtension]
    /// </summary>
    /// <param name="classSymbol">类的语义符号</param>
    /// <returns>如果有类级属性返回 true，否则返回 false</returns>
    public static bool HasClassLevelYStaticAttribute(INamedTypeSymbol classSymbol)
    {
        return GetYStaticAttribute(classSymbol) != null;
    }

    /// <summary>
    /// 获取类的 YStatic 相关属性
    ///
    /// 🎯 核心功能：
    /// 从类的属性列表中找到第一个 YStatic 相关属性
    ///
    /// 🔍 查找逻辑：
    /// 遍历类的所有属性，找到名称以 "YStatic" 开头且以 "Attribute" 结尾的属性
    /// </summary>
    /// <param name="classSymbol">类的语义符号</param>
    /// <returns>YStatic 属性数据，如果没有找到返回 null</returns>
    public static AttributeData? GetYStaticAttribute(INamedTypeSymbol classSymbol)
    {
        return classSymbol.GetAttributes()
            .FirstOrDefault(attr => IsYStaticRelatedAttribute(attr.AttributeClass));
    }

    /// <summary>
    /// 检查属性类是否为 YStatic 相关属性
    ///
    /// 🎯 识别规则：
    /// 属性类名称以 "YStatic" 开头且以 "Attribute" 结尾
    ///
    /// 🔍 支持的属性：
    /// - YStaticAttribute (基础属性)
    /// - YStaticExtensionAttribute (扩展方法属性)
    /// </summary>
    /// <param name="attributeClass">属性类符号</param>
    /// <returns>如果是 YStatic 相关属性返回 true，否则返回 false</returns>
    private static bool IsYStaticRelatedAttribute(INamedTypeSymbol? attributeClass)
    {
        if (attributeClass == null) return false;

        var name = attributeClass.Name;
        return name.StartsWith("YStatic") && name.EndsWith("Attribute");
    }

    /// <summary>
    /// 检查属性是否为 YStaticExtension 属性
    ///
    /// 🎯 核心功能：
    /// 根据属性类型判断是否应该生成扩展方法
    ///
    /// 💡 判断逻辑：
    /// - YStaticExtensionAttribute → 扩展方法模式
    /// - YStaticAttribute → 普通静态方法模式
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <returns>如果是扩展方法属性返回 true，否则返回 false</returns>
    private static bool IsYStaticExtensionAttribute(AttributeData attribute)
    {
        return attribute.AttributeClass?.Name == "YStaticExtensionAttribute";
    }



    #endregion

    #region ⚙️ 配置提取

    /// <summary>
    /// 从属性数据中提取布尔值
    ///
    /// 🎯 提取策略：
    /// 1. 🔧 构造函数参数：按位置查找
    /// 2. 📝 命名参数：按名称查找
    /// 3. 🛡️ 默认值：如果都没找到，使用提供的默认值
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <param name="parameterName">参数名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>布尔值</returns>
    private static bool GetBooleanValue(AttributeData attribute, string parameterName, bool defaultValue)
    {
        // 🔧 检查构造函数参数
        if (attribute.ConstructorArguments.Length > 0)
        {
            foreach (var arg in attribute.ConstructorArguments)
            {
                if (arg.Value is bool boolValue)
                    return boolValue;
            }
        }

        // 📝 检查命名参数
        foreach (var namedArg in attribute.NamedArguments)
        {
            if (namedArg.Key == parameterName && namedArg.Value.Value is bool namedBoolValue)
                return namedBoolValue;
        }

        return defaultValue;
    }

    /// <summary>
    /// 从属性数据中提取字符串值
    ///
    /// 🎯 提取策略：
    /// 1. 📝 命名参数：按名称查找
    /// 2. 🛡️ 默认值：如果没找到，使用提供的默认值
    /// </summary>
    /// <param name="attribute">属性数据</param>
    /// <param name="parameterName">参数名称</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>字符串值</returns>
    private static string? GetStringValue(AttributeData attribute, string parameterName, string? defaultValue)
    {
        // 📝 检查命名参数
        foreach (var namedArg in attribute.NamedArguments)
        {
            if (namedArg.Key == parameterName && namedArg.Value.Value is string stringValue)
                return stringValue;
        }

        return defaultValue;
    }

    #endregion

    #region 🔧 方法信息提取

    /// <summary>
    /// 从类声明中提取方法信息（包含 XML 文档注释）
    ///
    /// 🎯 核心功能：
    /// 提取类中所有公共方法的详细信息，用于静态标签生成
    ///
    /// 🔍 处理逻辑：
    /// 1. 遍历类中的所有成员
    /// 2. 筛选出公共方法
    /// 3. 处理扩展方法的参数转换
    /// 4. 提取方法签名和文档注释
    /// 5. 检查 [YStaticIgnore] 标记
    ///
    /// 💡 设计理念：
    /// - 完全基于 YService 的方法提取模式
    /// - 保持方法签名的完整性和准确性
    /// - 支持扩展方法的智能转换
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <param name="isExtensionMode">是否为扩展方法模式</param>
    /// <returns>方法信息列表</returns>
    private static List<YStaticMethodInfo> ExtractMethodsWithDocumentation(
        ClassDeclarationSyntax classDeclaration,
        SemanticModel semanticModel,
        bool isExtensionMode)
    {
        var methods = new List<YStaticMethodInfo>();

        foreach (var methodDeclaration in classDeclaration.Members.OfType<MethodDeclarationSyntax>())
        {
            // 只处理公共方法
            if (!YSyntaxAnalysisHelper.IsPublicMethod(methodDeclaration))
                continue;

            // 跳过被忽略的方法
            if (YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YStaticIgnore"))
                continue;

            // 提取方法信息
            var methodInfo = CreateMethodInfo(methodDeclaration, semanticModel, isExtensionMode);
            methods.Add(methodInfo);
        }

        return methods;
    }

    /// <summary>
    /// 创建方法信息对象
    ///
    /// 🎯 核心功能：
    /// 从方法声明中提取完整的方法信息，包括签名和文档注释
    ///
    /// 💡 设计理念：
    /// - 完全基于 YService 的方法信息提取模式
    /// - 使用 Helper 中的通用工具
    /// - 保持方法签名的完整性
    /// </summary>
    /// <param name="methodDeclaration">方法声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <param name="isExtensionMode">是否为扩展方法模式</param>
    /// <returns>方法信息对象</returns>
    private static YStaticMethodInfo CreateMethodInfo(
        MethodDeclarationSyntax methodDeclaration,
        SemanticModel semanticModel,
        bool isExtensionMode)
    {
        // 🔧 采用 YService 成功策略：使用语义模型获取方法符号
        var methodSymbol = semanticModel.GetDeclaredSymbol(methodDeclaration) as IMethodSymbol;

        var name = methodDeclaration.Identifier.ValueText;
        var returnType = methodSymbol?.ReturnType.ToDisplayString() ?? methodDeclaration.ReturnType.ToString();
        var parameters = methodSymbol != null
            ? YMethodSignatureHelper.GetParametersString(methodSymbol)
            : string.Join(", ", methodDeclaration.ParameterList.Parameters.Select(p => p.ToString()));
        var genericParameters = methodSymbol != null
            ? YMethodSignatureHelper.GetTypeParametersString(methodSymbol)
            : (methodDeclaration.TypeParameterList?.Parameters.Count > 0
                ? $"<{string.Join(", ", methodDeclaration.TypeParameterList.Parameters.Select(p => p.Identifier.ValueText))}>"
                : string.Empty);
        var genericConstraints = methodSymbol != null
            ? YMethodSignatureHelper.GetTypeConstraintsString(methodSymbol)
            : (methodDeclaration.ConstraintClauses.Count > 0
                ? string.Join(" ", methodDeclaration.ConstraintClauses.Select(c => c.ToString()))
                : string.Empty);
        var documentation = YXmlDocumentationExtractor.ExtractFromMethod(methodDeclaration);
        var isStaticMethod = methodDeclaration.Modifiers.Any(SyntaxKind.StaticKeyword);

        // 处理扩展方法的参数转换
        var extensionParameters = isExtensionMode ? ConvertToExtensionParameters(parameters) : parameters;

        return new YStaticMethodInfo(
            name,
            returnType,
            parameters,
            genericParameters,
            genericConstraints,
            documentation,
            isExtensionMode,
            extensionParameters,
            isStaticMethod);
    }



    /// <summary>
    /// 将普通参数转换为扩展方法参数
    ///
    /// 🎯 核心功能：
    /// 将第一个参数转换为 this 参数，用于扩展方法生成
    /// </summary>
    /// <param name="originalParameters">原始参数字符串</param>
    /// <returns>转换后的扩展方法参数字符串</returns>
    private static string ConvertToExtensionParameters(string originalParameters)
    {
        if (string.IsNullOrEmpty(originalParameters))
            return string.Empty;

        var trimmed = originalParameters.Trim();
        if (string.IsNullOrEmpty(trimmed))
            return string.Empty;

        // 检查是否已经包含 this 修饰符
        if (trimmed.StartsWith("this "))
            return trimmed;

        // 智能转换：只在第一个参数前添加 this
        var parameters = trimmed.Split(',');
        if (parameters.Length > 0)
        {
            var firstParam = parameters[0].Trim();
            if (!string.IsNullOrEmpty(firstParam))
            {
                parameters[0] = $"this {firstParam}";
                return string.Join(", ", parameters);
            }
        }

        return originalParameters;
    }

    #endregion
}
