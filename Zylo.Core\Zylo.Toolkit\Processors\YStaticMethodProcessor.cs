using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Zylo.Toolkit.Helper;
using Zylo.Toolkit.Models;

namespace Zylo.Toolkit.Processors;

/// <summary>
/// YStatic 方法级处理器 - 专门处理方法级 YStatic 属性
///
/// 🎯 核心职责：
/// 1. 🔍 YStatic 方法级属性检测：识别标记了方法级属性的方法
/// 2. 🎯 选择性包含：只有标记了属性的方法包含在生成中
/// 3. 🔧 混合生成模式：支持不同方法使用不同的生成模式
/// 4. 🚫 智能筛选：排除私有方法、被忽略的方法
/// 5. 🏗️ YStatic 模型构建：构建标准的 YStaticInfo 对象
///
/// 💡 设计理念：
/// - YStatic 专用：专门为 YStatic 功能设计
/// - 细粒度控制：每个方法可以独立配置
/// - 渐进式迁移：支持逐步将现有类迁移到静态标签
/// - 冲突避免：只在类没有类级属性时生效
///
/// 🔧 处理特点：
/// - 只有标记了方法级属性的方法包含在生成中
/// - 支持不同方法使用不同的生成模式
/// - 自动排除不适合静态标签的方法
/// - 直接使用 Helper/ 中的通用工具，代码更直接高效
///
/// 🏗️ 逻辑结构：
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🚀 公共入口点       │ -> │  🔍 候选识别        │ -> │  🎯 属性检测        │
/// │  ProcessMethodLevel │    │  类和方法筛选        │    │  方法级属性识别      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
///                                       ↓
/// ┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
/// │  🏗️ 模型构建        │ <- │  🔧 方法信息提取     │ <- │  🚫 智能筛选        │
/// │  YStaticInfo 构建   │    │  方法签名和文档      │    │  不适合方法排除      │
/// └─────────────────────┘    └─────────────────────┘    └─────────────────────┘
/// </summary>
public static class YStaticMethodProcessor
{
    #region 🚀 公共入口点

    /// <summary>
    /// 处理方法级 YStatic 属性的主入口点
    ///
    /// 🎯 核心功能：
    /// 当类没有类级 YStatic 属性时，检查方法级属性并生成对应的静态标签信息
    ///
    /// 💡 设计理念：
    /// - 只有标记了方法级属性的方法才包含在生成中
    /// - 每个方法可以有独立的生成模式配置
    /// - 支持混合生成模式：不同方法可以有不同的生成模式
    ///
    /// 🔧 处理流程：
    /// 1. 遍历所有方法，找到有方法级 YStatic 属性的方法
    /// 2. 提取每个方法的属性配置（生成模式、扩展类名等）
    /// 3. 构建方法级静态标签信息
    /// 4. 转换为标准的 YStaticInfo 对象（保持兼容性）
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="classSymbol">类的语义符号</param>
    /// <param name="semanticModel">语义模型</param>
    /// <returns>YStaticInfo 对象</returns>
    public static YStaticInfo ProcessMethodLevel(
        ClassDeclarationSyntax classDeclaration,
        INamedTypeSymbol classSymbol,
        SemanticModel semanticModel)
    {
        // 📝 第一步：提取类文档注释
        var classDocumentation = YXmlDocumentationExtractor.ExtractFromClass(classDeclaration);

        // 🔍 第二步：提取有方法级属性的方法信息
        var methodInfos = ExtractMethodLevelStaticMethods(classDeclaration, semanticModel);

        // 🏗️ 第三步：构建标准的 YStaticInfo 对象
        // 对于方法级属性，我们使用默认的类级配置，但标记为方法级触发
        return new YStaticInfo(
            classSymbol.Name,
            classSymbol.ContainingNamespace.ToDisplayString(),
            $"{classSymbol.Name}{YStaticConstants.ExtensionClassSuffix}", // 默认扩展类名
            classSymbol.ToDisplayString(),
            $"{classSymbol.ContainingNamespace.ToDisplayString()}.{classSymbol.Name}{YStaticConstants.ExtensionClassSuffix}",
            false, // 默认非扩展方法模式（具体的模式在代码生成时处理）
            true, // 🔥 标记为方法级属性触发
            methodInfos,
            classDocumentation,
            true // 生成扩展
        );
    }

    #endregion

    #region 🔍 候选识别

    /// <summary>
    /// 检查类是否为方法级 YStatic 候选
    ///
    /// 🎯 核心功能：
    /// 综合检查类是否适合进行方法级 YStatic 生成
    ///
    /// 💡 检查条件：
    /// 1. 必须是 partial 类
    /// 2. 没有类级 YStatic 属性（避免冲突）
    /// 3. 有至少一个方法标记了方法级 YStatic 属性
    ///
    /// 🔧 设计理念：
    /// 方法级属性只在类没有类级属性时生效，避免配置冲突
    /// </summary>
    /// <param name="node">语法节点</param>
    /// <returns>如果是方法级静态标签候选返回 true，否则返回 false</returns>
    public static bool IsMethodLevelYStaticCandidate(SyntaxNode node)
    {
        // 🔍 第一步：检查是否是类声明
        if (node is not ClassDeclarationSyntax classDeclaration)
            return false;

        // 🔍 第二步：检查是否是 partial 类
        if (!YSyntaxAnalysisHelper.IsPartialClass(classDeclaration))
            return false;

        // 🔍 第三步：检查是否有类级 YStatic 属性
        // 如果有类级属性，则不处理方法级属性（避免冲突）
        // 注意：这里需要语义模型来检查，但在候选识别阶段我们只能做语法检查
        // 所以这里暂时跳过，在实际处理时再检查

        // 🔍 第四步：检查是否有方法级 YStatic 属性
        return HasMethodLevelYStaticAttributes(classDeclaration);
    }

    /// <summary>
    /// 检查类是否有方法级 YStatic 属性
    ///
    /// 🎯 核心功能：
    /// 检测类中是否有方法标记了 YStatic 相关的方法级属性
    ///
    /// 💡 使用场景：
    /// 当类没有类级 YStatic 属性时，检查是否有方法级属性来触发静态标签生成
    ///
    /// 🔧 实现原理：
    /// 使用 YSyntaxAnalysisHelper 提供的通用方法检测工具
    /// 传入 YStatic 特定的属性名称列表进行检测
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <returns>如果找到方法级 YStatic 属性返回 true，否则返回 false</returns>
    public static bool HasMethodLevelYStaticAttributes(ClassDeclarationSyntax classDeclaration)
    {
        return YSyntaxAnalysisHelper.HasMethodsWithAttributes(classDeclaration, YStaticMethodLevelAttributes);
    }

    #endregion

    #region 🎯 属性检测配置

    /// <summary>
    /// YStatic 方法级属性名称列表
    ///
    /// 🎯 设计目的：
    /// 集中管理 YStatic 相关的方法级属性名称，便于维护和扩展
    ///
    /// 💡 支持的属性：
    /// - YStatic: 方法级普通静态方法生成
    /// - YStaticExtension: 方法级扩展方法生成
    /// </summary>
    private static readonly string[] YStaticMethodLevelAttributes =
    [
        "YStatic", "YStaticExtension"
    ];

    #endregion

    #region 🔧 方法信息提取

    /// <summary>
    /// 提取有方法级 YStatic 属性的方法信息
    ///
    /// 🎯 核心功能：
    /// 从类中提取所有标记了方法级 YStatic 属性的方法，并构建方法信息列表
    ///
    /// 💡 设计理念：
    /// - 完全基于 YService 的方法提取模式
    /// - 只处理有属性标记的方法
    /// - 保持方法签名和文档的完整性
    /// </summary>
    /// <param name="classDeclaration">类声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <returns>方法信息列表</returns>
    private static List<YStaticMethodInfo> ExtractMethodLevelStaticMethods(
        ClassDeclarationSyntax classDeclaration,
        SemanticModel semanticModel)
    {
        var methods = new List<YStaticMethodInfo>();

        foreach (var methodDeclaration in classDeclaration.Members.OfType<MethodDeclarationSyntax>())
        {
            // 只处理公共方法
            if (!YSyntaxAnalysisHelper.IsPublicMethod(methodDeclaration))
                continue;

            // 检查是否有 YStatic 相关属性
            var hasYStatic = YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YStatic");
            var hasYStaticExtension = YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YStaticExtension");

            // 跳过没有相关属性的方法
            if (!hasYStatic && !hasYStaticExtension)
                continue;

            // 跳过被忽略的方法
            if (YSyntaxAnalysisHelper.HasMethodAttribute(methodDeclaration, "YStaticIgnore"))
                continue;

            // 确定是否为扩展方法模式
            var isExtensionMode = hasYStaticExtension;

            // 提取方法信息
            var methodInfo = CreateMethodInfo(methodDeclaration, semanticModel, isExtensionMode);
            methods.Add(methodInfo);
        }

        return methods;
    }

    /// <summary>
    /// 创建方法信息对象
    ///
    /// 🎯 核心功能：
    /// 从方法声明中提取完整的方法信息，包括签名和文档注释
    ///
    /// 💡 设计理念：
    /// - 完全基于 YService 的方法信息提取模式
    /// - 使用简洁的方法签名提取
    /// - 保持方法签名的完整性
    /// </summary>
    /// <param name="methodDeclaration">方法声明语法节点</param>
    /// <param name="semanticModel">语义模型</param>
    /// <param name="isExtensionMode">是否为扩展方法模式</param>
    /// <returns>方法信息对象</returns>
    private static YStaticMethodInfo CreateMethodInfo(
        MethodDeclarationSyntax methodDeclaration,
        SemanticModel semanticModel,
        bool isExtensionMode)
    {
        // 🔧 采用 YService 成功策略：使用语义模型获取方法符号
        var methodSymbol = semanticModel.GetDeclaredSymbol(methodDeclaration) as IMethodSymbol;

        var name = methodDeclaration.Identifier.ValueText;
        var returnType = methodSymbol?.ReturnType.ToDisplayString() ?? methodDeclaration.ReturnType.ToString();
        var parameters = methodSymbol != null
            ? YMethodSignatureHelper.GetParametersString(methodSymbol)
            : string.Join(", ", methodDeclaration.ParameterList.Parameters.Select(p => p.ToString()));
        var genericParameters = methodSymbol != null
            ? YMethodSignatureHelper.GetTypeParametersString(methodSymbol)
            : (methodDeclaration.TypeParameterList?.Parameters.Count > 0
                ? $"<{string.Join(", ", methodDeclaration.TypeParameterList.Parameters.Select(p => p.Identifier.ValueText))}>"
                : string.Empty);
        var genericConstraints = methodSymbol != null
            ? YMethodSignatureHelper.GetTypeConstraintsString(methodSymbol)
            : (methodDeclaration.ConstraintClauses.Count > 0
                ? string.Join(" ", methodDeclaration.ConstraintClauses.Select(c => c.ToString()))
                : string.Empty);
        var documentation = YXmlDocumentationExtractor.ExtractFromMethod(methodDeclaration);
        var isStaticMethod = methodDeclaration.Modifiers.Any(SyntaxKind.StaticKeyword);

        // 处理扩展方法的参数转换
        var extensionParameters = isExtensionMode ? ConvertToExtensionParameters(parameters) : parameters;

        return new YStaticMethodInfo(
            name,
            returnType,
            parameters,
            genericParameters,
            genericConstraints,
            documentation,
            isExtensionMode,
            extensionParameters,
            isStaticMethod);
    }



    /// <summary>
    /// 将普通参数转换为扩展方法参数
    ///
    /// 🎯 核心功能：
    /// 将第一个参数转换为 this 参数，用于扩展方法生成
    /// </summary>
    /// <param name="originalParameters">原始参数字符串</param>
    /// <returns>转换后的扩展方法参数字符串</returns>
    private static string ConvertToExtensionParameters(string originalParameters)
    {
        if (string.IsNullOrEmpty(originalParameters))
            return string.Empty;

        var trimmed = originalParameters.Trim();
        if (string.IsNullOrEmpty(trimmed))
            return string.Empty;

        // 检查是否已经包含 this 修饰符
        if (trimmed.StartsWith("this "))
            return trimmed;

        // 智能转换：只在第一个参数前添加 this
        var parameters = trimmed.Split(',');
        if (parameters.Length > 0)
        {
            var firstParam = parameters[0].Trim();
            if (!string.IsNullOrEmpty(firstParam))
            {
                parameters[0] = $"this {firstParam}";
                return string.Join(", ", parameters);
            }
        }

        return originalParameters;
    }

    #endregion
}
