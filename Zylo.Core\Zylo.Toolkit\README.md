# 🚀 Zylo.Toolkit

**高性能依赖注入代码生成器** - 基于 Roslyn 的 C# 源代码生成器，为 .NET 应用程序提供零运行时开销的依赖注入解决方案。

[![NuGet](https://img.shields.io/nuget/v/Zylo.Toolkit.svg)](https://www.nuget.org/packages/Zylo.Toolkit/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![.NET](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)](https://dotnet.microsoft.com/)

## 📋 目录

- [核心特性](#核心特性)
- [快速开始](#快速开始)
- [新协调器架构 (v1.1)](#新协调器架构-v11)
- [🆕 v1.2 静态方法增强](#v12-静态方法增强)
- [使用模式](#使用模式)
- [项目架构](#项目架构)
- [生成的文件](#生成的文件)
- [高级特性](#高级特性)
- [故障排除](#故障排除)

## 🎯 概述

`Zylo.Toolkit` 是一个基于 Roslyn 的现代化 C# 源代码生成器，专为企业级应用设计。v1.1 版本引入了全新的**协调器架构**，v1.2 版本新增了**静态方法增强**功能，提供模块化、高性能的代码生成能力。

### 🌟 核心价值

- **🔥 零运行时开销**：编译时生成，运行时无反射
- **⚡ 高性能**：接口调用直接映射到具体实现
- **🛡️ 类型安全**：完全类型安全的代码生成
- **🔧 智能感知**：生成的代码完全支持 IntelliSense
- **📝 文档保留**：自动保留和转换 XML 文档注释
- **🏗️ 模块化架构**：新协调器架构，错误隔离，独立生成
- **🆕 静态方法支持**：v1.2 支持混合类中的静态方法依赖注入

### 🎨 设计理念 (v1.1)

- **协调器模式**：YServiceCodeCoordinator 统一协调多个专用生成器
- **模块化设计**：每个生成器负责单一职责，错误隔离
- **独立文件生成**：不同类型的代码生成到独立的 `.yg.cs` 文件
- **完整诊断**：详细的错误报告和生成统计

## ✨ 核心特性

### 🎯 **零运行时开销**

- **编译时生成**：所有代码在编译时生成，运行时无反射
- **高性能**：接口调用直接映射到具体实现
- **内存友好**：无额外的代理对象或包装器

### 🔧 **智能代码生成**

- **自动接口生成**：从服务类自动生成对应接口
- **服务注册生成**：自动生成 DI 容器注册代码
- **文档保留**：完整保留 XML 文档注释
- **复杂类型支持**：泛型、委托、元组等高级类型

### 🎨 **灵活的使用方式**

- **类级属性**：`[YService]` 标记整个类
- **方法级属性**：`[YService]` 标记特定方法
- **静态类支持**：为静态类生成包装器
- **生命周期控制**：Singleton、Scoped、Transient

### 🏗️ **新协调器架构 (v1.1)**

- **模块化设计**：每个生成器负责单一职责
- **独立文件生成**：不同类型的代码生成到独立的 `.yg.cs` 文件
- **错误隔离**：单个生成器错误不影响其他生成器
- **统计和诊断**：完整的生成统计和错误报告

### 🚀 **便捷属性**

- **YServiceScopedAttribute**：作用域生命周期便捷属性
- **YServiceSingletonAttribute**：单例生命周期便捷属性
- **YServiceTransientAttribute**：瞬态生命周期便捷属性
- **YServiceIgnoreAttribute**：方法排除属性
- **自定义接口命名**：支持 InterfaceName 和 Description 参数

## 📦 快速开始

### 安装

```xml
<PackageReference Include="Zylo.Toolkit" Version="1.2.0" />
```

### 基础用法

```csharp
[YService(ServiceLifetime.Scoped)]
public partial class UserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户信息</returns>
    public async Task<User> GetUserAsync(int id)
    {
        // 实现逻辑
    }
}
```

### 自动生成的代码

```csharp
// 自动生成的接口 (IUserService.YService.yg.cs)
public interface IUserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户信息</returns>
    Task<User> GetUserAsync(int id);
}

// 自动生成的注册扩展 (ServiceRegistration.Assembly.yg.cs)
public static IServiceCollection AddUserService(this IServiceCollection services)
{
    return services.AddScoped<IUserService, UserService>();
}
```

## 🏗️ 新协调器架构 (v1.1)

v1.1 版本引入了全新的**协调器架构**，提供更好的模块化和错误处理：

### 🎯 架构优势

- **模块化设计**：每个生成器负责单一职责
- **错误隔离**：单个生成器错误不影响其他生成器
- **独立文件生成**：不同类型的代码生成到独立文件
- **完整诊断**：详细的错误报告和生成统计

### 🔧 协调器组件

```text
YServiceCodeCoordinator (协调器)
├── InterfaceFileGenerator      # 接口生成器
├── ServiceRegistrationGenerator # 服务注册生成器
├── StatisticsGenerator         # 统计生成器
└── ErrorReportGenerator        # 错误报告生成器
```

### 📁 生成的文件

- **`I{ClassName}.YService.yg.cs`** - 接口定义和实现关系
- **`ServiceRegistration.{Assembly}.yg.cs`** - 服务注册扩展方法
- **`YServiceStatistics.{Assembly}.yg.cs`** - 生成统计信息
- **`YServiceErrorReport.{Assembly}.yg.cs`** - 错误报告（如有错误）

## 🎨 使用模式

### 类级属性

```csharp
[YService(ServiceLifetime.Scoped)]
public partial class UserService
{
    public async Task<User> GetUserAsync(int id) { /* ... */ }
    public async Task<User> CreateUserAsync(User user) { /* ... */ }
}
```

### 方法级属性

```csharp
public partial class DataProcessor
{
    [YService(ServiceLifetime.Singleton)]
    public string ProcessData(string input) => input.ToUpper();

    [YService(ServiceLifetime.Transient)]
    public async Task<Result> ProcessAsync(Data data) { /* ... */ }
}
```

### 静态类包装

```csharp
[YService(ServiceLifetime.Singleton)]
public static partial class MathUtils
{
    public static int Add(int a, int b) => a + b;
    public static double Multiply(double x, double y) => x * y;
}
```

### 便捷属性 (v1.1)

```csharp
// 基础用法
[YServiceSingleton]
public partial class CacheService { /* ... */ }

[YServiceScoped]
public partial class UserService { /* ... */ }

[YServiceTransient]
public partial class TokenGenerator { /* ... */ }

// 自定义接口名称
[YServiceScoped("ICustomUserManager")]
public partial class UserService { /* ... */ }

// 接口名称 + 描述
[YServiceSingleton("IGlobalCache", "全局缓存管理服务")]
public partial class CacheService { /* ... */ }

// 方法排除
[YService]
public partial class DataService
{
    public string GetData(int id) { /* ... */ }  // ✅ 包含在接口中

    [YServiceIgnore]
    public void InternalMethod() { /* ... */ }   // ❌ 不包含在接口中
}
```

## 🆕 v1.2 静态方法增强

v1.2 版本引入了革命性的**静态方法增强**功能，支持在同一个类中混合使用实例方法和静态方法的依赖注入。

### 🎯 核心特性

- **统一接口**：静态方法和实例方法都包含在同一个接口中
- **包装器模式**：自动生成包装器类处理静态方法调用
- **透明使用**：从使用者角度看，静态方法和实例方法没有区别
- **性能优化**：静态方法直接调用，实例方法委托调用

### 📝 使用方式

```csharp
public partial class DataProcessor  // 类上没有YService属性
{
    [YServiceScoped]
    public async Task<string> ProcessDataAsync(string data)
    {
        return $"Processed: {data}";
    }

    [YServiceScoped]
    public static string InternalUtility(string input)
    {
        return $"Internal: {input}";
    }
}
```

### 🔧 生成的代码

```csharp
// 自动生成的接口
public interface IDataProcessor
{
    Task<string> ProcessDataAsync(string data);  // 实例方法
    string InternalUtility(string input);       // 静态方法
}

// 自动生成的包装器类
public class DataProcessorWrapper : IDataProcessor
{
    private readonly DataProcessor _instance = new DataProcessor();

    // 实例方法：委托给实例
    public Task<string> ProcessDataAsync(string data) => _instance.ProcessDataAsync(data);

    // 静态方法：直接调用
    public string InternalUtility(string input) => DataProcessor.InternalUtility(input);
}

// 自动生成的服务注册
public static IServiceCollection AddDataProcessor(this IServiceCollection services)
{
    services.AddScoped<IDataProcessor, DataProcessorWrapper>();
    return services;
}
```

### 🎨 使用场景

- **工具类方法**：将静态工具方法集成到服务中
- **缓存操作**：混合实例状态和静态缓存操作
- **配置管理**：结合实例配置和全局静态配置
- **数据处理**：混合有状态和无状态的数据处理方法

### ⚠️ 注意事项

- 静态方法必须标记YService属性才会包含在接口中
- 包装器类会创建原始类的实例来处理实例方法
- 不支持静态构造函数的特殊处理

## 🎨 使用模式

### 自定义接口命名

```csharp
[YService(ServiceLifetime.Scoped, InterfaceName = "ICustomUserManager")]
public partial class UserService { /* ... */ }
```

## 🏗️ 项目架构

### 📋 **分层架构设计**

```
Zylo.Toolkit/ (现代化分层架构)
├── 📁 Attributes/          # 属性定义层
│   └── YServiceAttribute.cs        # 所有 YService 相关属性
├── 📁 Helper/              # 通用工具层 (可复用) - Y前缀命名
│   ├── YSyntaxAnalysisHelper.cs    # 语法分析和属性检测
│   ├── YMethodSignatureHelper.cs   # 方法签名处理
│   ├── YXmlDocumentationExtractor.cs # XML 文档提取
│   ├── YDocumentationProcessor.cs  # 文档格式化
│   ├── YDocumentationTemplateGenerator.cs # 文档模板生成
│   ├── YCodeIndentFormatter.cs     # 代码格式化 (Y前缀扩展方法)
│   └── README.md                   # 通用工具文档
├── 📁 Models/              # 数据模型层
│   ├── YServiceModels.cs           # 统一数据模型 (v1.2增强)
│   └── README.md                   # 数据模型文档
├── 📁 Processors/          # 业务处理层 (YService专用)
│   ├── YServiceClassProcessor.cs   # 类级处理器
│   ├── YServiceMethodProcessor.cs  # 方法级处理器
│   └── README.md                   # 处理器架构文档
├── 📁 Generators/          # 生成器协调层
│   ├── YServiceGenerator.cs        # 主协调器
│   └── README.md                   # 生成器文档
├── 📁 Temple/              # 代码生成层
│   ├── YServiceCodeGenerator.cs    # 原始生成器
│   └── 📁 Yservice/                # 新协调器架构 (v1.1+v1.2)
│       ├── YServiceCodeCoordinator.cs    # 代码生成协调器
│       ├── InterfaceFileGenerator.cs     # 接口生成器 (v1.2混合类支持)
│       ├── ServiceRegistrationGenerator.cs # 注册生成器 (v1.2包装器注册)
│       ├── StatisticsGenerator.cs        # 统计生成器
│       ├── ErrorReportGenerator.cs       # 错误报告生成器
│       └── README.md                     # 协调器架构文档
├── 📁 Diagnostics/         # 诊断和错误报告
│   ├── DiagnosticReporter.cs
│   └── YServiceDiagnostics.cs
├── 📁 Documentation/       # 架构文档
│   ├── YService架构设计文档.md
│   ├── YService升级计划.md
│   ├── YService开发指南.md
│   ├── v1.2-静态方法增强功能.md
│   └── v1.2-发布说明.md
└── 📁 build/               # 构建配置
    ├── Zylo.Toolkit.props
    ├── Zylo.Toolkit.targets
    └── README.md
```

### 🎯 **架构设计原则**

#### **分层职责**

- **Helper 层**：真正通用的工具，可被任何功能复用
- **Processors 层**：YService 专用处理器，包含业务逻辑
- **Models 层**：统一的数据模型，确保类型安全
- **Generators 层**：协调者，管理整个生成流程
- **Temple 层**：代码生成器，专注于代码输出

#### **协调器模式 (v1.1)**

```text
YServiceGenerator (主协调器)
    ↓ 分发任务
YServiceClassProcessor + YServiceMethodProcessor
    ↓ 生成数据模型
YServiceCodeCoordinator (代码协调器)
    ↓ 协调多个生成器
InterfaceFileGenerator + ServiceRegistrationGenerator + StatisticsGenerator + ErrorReportGenerator
    ↓ 输出
独立的 .yg.cs 文件
```

## 📁 生成的文件

YService 会为每个程序集生成以下文件：

- **`I{ClassName}.YService.yg.cs`** - 接口定义和实现关系
- **`ServiceRegistration.{Assembly}.yg.cs`** - 服务注册扩展方法
- **`YServiceStatistics.{Assembly}.yg.cs`** - 生成统计信息
- **`YServiceErrorReport.{Assembly}.yg.cs`** - 错误报告（如有错误）

## 🔧 高级特性

### 泛型方法支持

```csharp
[YService]
public partial class GenericService
{
    public T Process<T>(T input) where T : class
    {
        return input;
    }
}
```

### 复杂类型支持

```csharp
[YService]
public partial class ComplexService
{
    public async Task<(bool Success, string Message)> ProcessAsync(
        Dictionary<string, object> data,
        CancellationToken cancellationToken = default)
    {
        // 实现逻辑
    }
}
```

### 文档注释保留

```csharp
/// <summary>
/// 用户管理服务
/// </summary>
/// <remarks>
/// 提供完整的用户管理功能
/// </remarks>
[YService]
public partial class UserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="id">用户的唯一标识符</param>
    /// <returns>用户信息，如果不存在则返回 null</returns>
    /// <exception cref="ArgumentException">当 id 无效时抛出</exception>
    public async Task<User?> GetUserAsync(int id)
    {
        // 实现逻辑
    }
}
```

## 🔧 故障排除

### 常见问题

#### Q: 生成器没有运行，没有生成代码？

**A: 检查以下几点：**

1. **确保类是 partial 的**：

```csharp
// ❌ 错误：缺少 partial 关键字
[YService]
public class UserService { }

// ✅ 正确：包含 partial 关键字
[YService]
public partial class UserService { }
```

2. **检查 NuGet 包引用**：

```xml
<PackageReference Include="Zylo.Toolkit" Version="1.2.0">
  <PrivateAssets>all</PrivateAssets>
  <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
</PackageReference>
```

3. **清理和重新构建**：

```bash
dotnet clean
dotnet build
```

#### Q: 生成的接口缺少某些方法？

**A: 检查方法是否符合条件：**

```csharp
// ✅ 会包含在接口中
public async Task<User> GetUserAsync(int id) { }

// ❌ 不会包含：非公共方法
private void HelperMethod() { }

// ❌ 不会包含：被忽略的方法
[YServiceIgnore]
public void UtilityMethod() { }
```

#### Q: 静态类包装器不工作？

**A: 确保：**

1. 静态类标记了 `[YService]` 并且是 `partial`
2. 静态方法是 `public` 的
3. 在 DI 容器中注册了包装器类

### 调试技巧

1. **查看生成的文件**：在 `obj/Generated` 目录下查看生成的代码
2. **检查编译输出**：查看编译器是否有相关警告或错误
3. **清理重建**：使用 `dotnet clean` 和 `dotnet build` 重新生成

## 📚 详细文档

- **[新协调器架构](Temple/YService/README.md)** - v1.1 新架构说明
- **[架构设计文档](Documentation/YService架构设计文档.md)** - 完整的架构设计说明
- **[升级计划](Documentation/YService升级计划.md)** - 版本升级和功能规划
- **[Helper 工具指南](Helper/README.md)** - 辅助工具使用说明
- **[Processors 处理器指南](Processors/README.md)** - 语法处理器说明
- **[Models 数据模型](Models/README.md)** - 数据模型定义

## 🎯 版本信息

- **当前版本**: 1.0.0
- **目标框架**: .NET 6.0, .NET 8.0
- **C# 版本**: 13.0
- **架构**: 协调器架构 (v1.1)
- **NuGet 包**: 混合架构 (程序集 + 源代码生成器)

## 🔧 技术特性

### **源代码生成器**

- **Roslyn 组件**: 基于 Microsoft.CodeAnalysis.CSharp 4.5.0
- **增量生成**: 支持增量编译，提高构建性能
- **多目标框架**: 同时支持 .NET 6.0 和 .NET 8.0

### **便捷属性系统**

- ✅ **YServiceScopedAttribute**: 作用域生命周期便捷属性
- ✅ **YServiceSingletonAttribute**: 单例生命周期便捷属性
- ✅ **YServiceTransientAttribute**: 瞬态生命周期便捷属性
- ✅ **YServiceIgnoreAttribute**: 方法排除属性
- ✅ **参数支持**: InterfaceName 和 Description 自定义参数

### **协调器架构 (v1.1)**

- ✅ **模块化设计**: 每个生成器负责单一职责
- ✅ **独立文件生成**: 不同类型代码生成到独立的 `.yg.cs` 文件
- ✅ **错误隔离**: 单个生成器错误不影响其他生成器
- ✅ **完整诊断**: 详细的错误报告和生成统计

### **🆕 静态方法增强 (v1.2)**

- ✅ **混合类支持**: 同一类中混合使用实例方法和静态方法
- ✅ **包装器模式**: 自动生成包装器类处理静态方法调用
- ✅ **统一接口**: 静态方法和实例方法都包含在同一接口中
- ✅ **性能优化**: 静态方法直接调用，实例方法委托调用
- ✅ **透明使用**: 对使用者完全透明的依赖注入
- ✅ **向后兼容**: 完全兼容 v1.1 的所有功能

### **🔧 工具增强**

- ✅ **Y前缀命名**: 所有Helper工具类使用Y前缀，避免命名冲突
- ✅ **扩展方法优化**: YAppendLine, YAppendSeparator等Y前缀扩展方法
- ✅ **项目重命名**: Zylo.Service → Zylo.Toolkit，更好的定位

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

##打包

-   dotnet pack Zylo.Toolkit -c Release --no-build