using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;

namespace Zylo.Toolkit.Temple.YStatic;

/// <summary>
/// 错误报告生成器 - 专门负责生成错误报告文件
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成错误报告
/// 📄 生成内容：
/// 1. 详细的错误信息
/// 2. 调试信息和堆栈跟踪
/// 3. 修复建议
/// </remarks>
public static class ErrorReportGenerator
{
    #region 🎯 主要生成方法

    /// <summary>
    /// 生成错误报告文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void Generate(SourceProductionContext context, Exception exception, string assemblyName)
    {
        try
        {
            // 🔧 生成错误报告内容
            var fileContent = GenerateErrorReportContent(exception, assemblyName);

            // 📄 输出到文件
            var fileName = $"YStaticErrorReport.{SanitizeAssemblyName(assemblyName)}{YStaticConstants.GeneratedFileExtension}";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception reportException)
        {
            // 🚨 连错误报告都生成失败时，输出最简化版本
            var fallbackContent = GenerateFallbackErrorReport(exception, reportException, assemblyName);
            var fileName = $"YStaticErrorReport.{SanitizeAssemblyName(assemblyName)}.fallback{YStaticConstants.GeneratedFileExtension}";
            context.AddSource(fileName, SourceText.From(fallbackContent, Encoding.UTF8));
        }
    }

    #endregion

    #region 🔧 内容生成方法

    /// <summary>
    /// 生成错误报告内容
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>错误报告内容</returns>
    private static string GenerateErrorReportContent(Exception exception, string assemblyName)
    {
        var sb = new StringBuilder();

        sb.AppendLine("// <auto-generated />")
          .AppendLine("// YStatic 错误报告")
          .AppendLine($"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .AppendLine($"// 程序集: {assemblyName}")
          .AppendLine()
          .AppendLine("/*")
          .AppendLine("=== YStatic 静态标签生成错误报告 ===")
          .AppendLine()
          .AppendLine($"错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .AppendLine($"程序集名称: {assemblyName}")
          .AppendLine($"错误类型: {exception.GetType().Name}")
          .AppendLine($"错误消息: {exception.Message}")
          .AppendLine()
          .AppendLine("=== 详细错误信息 ===")
          .AppendLine(exception.ToString())
          .AppendLine()
          .AppendLine("=== 修复建议 ===")
          .AppendLine("1. 检查 YStatic 属性的使用是否正确")
          .AppendLine("2. 确保类是 public 且可访问")
          .AppendLine("3. 检查方法签名是否符合要求")
          .AppendLine("4. 查看详细错误信息以获取更多线索")
          .AppendLine()
          .AppendLine("=== 联系支持 ===")
          .AppendLine("如果问题持续存在，请联系 Zylo.Toolkit 支持团队")
          .AppendLine("*/");

        return sb.ToString();
    }

    /// <summary>
    /// 生成后备错误报告
    /// </summary>
    /// <param name="originalException">原始异常</param>
    /// <param name="reportException">报告生成异常</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>后备错误报告内容</returns>
    private static string GenerateFallbackErrorReport(Exception originalException, Exception reportException, string assemblyName)
    {
        var sb = new StringBuilder();

        sb.AppendLine("// <auto-generated />")
          .AppendLine("// YStatic 后备错误报告")
          .AppendLine($"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .AppendLine()
          .AppendLine("/*")
          .AppendLine("=== YStatic 严重错误 ===")
          .AppendLine("连错误报告都无法正常生成！")
          .AppendLine()
          .AppendLine($"原始错误: {originalException?.Message ?? "未知错误"}")
          .AppendLine($"报告错误: {reportException?.Message ?? "未知错误"}")
          .AppendLine($"程序集: {assemblyName}")
          .AppendLine()
          .AppendLine("请联系 Zylo.Toolkit 支持团队")
          .AppendLine("*/");

        return sb.ToString();
    }

    /// <summary>
    /// 清理程序集名称，使其适合作为文件名
    /// </summary>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>清理后的名称</returns>
    private static string SanitizeAssemblyName(string assemblyName)
    {
        if (string.IsNullOrEmpty(assemblyName))
            return "Unknown";

        // 移除文件名中不允许的字符
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new string(assemblyName.Where(c => !invalidChars.Contains(c)).ToArray());

        return string.IsNullOrEmpty(sanitized) ? "Unknown" : sanitized;
    }

    #endregion
}
