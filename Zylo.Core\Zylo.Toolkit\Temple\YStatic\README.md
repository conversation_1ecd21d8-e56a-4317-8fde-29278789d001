# 🚀 YStatic v1.3 - 静态标签生成器

**基于 YService 成熟架构的静态类标签生成功能**

## 🎯 概述

YStatic v1.3 是 Zylo.Toolkit 的核心新功能，专门用于为普通类生成静态标签版本。它完全基于 YService 的成熟架构，提供一模一样的开发体验和代码质量。

### 🌟 核心特性

- **🏗️ 静态标签生成**：为普通类生成对应的静态标签版本
- **🚀 委托实现**：使用委托调用原始实例方法，保持性能
- **🎨 架构一致性**：完全复用 YService 的成熟架构和设计模式
- **🔧 独立功能**：与 YService 完全独立，不涉及依赖注入
- **🤝 可共存使用**：与 YService 功能完全兼容，可以同时使用

## 🏗️ 架构设计

### 分层架构（基于 YService 模式）

```text
YStatic v1.3 架构
├── 📁 Attributes/           # 属性定义层
│   └── YStaticAttribute.cs  # YStatic、YStaticExtension、YStaticIgnore
├── 📁 Models/               # 数据模型层
│   └── YStaticModels.cs     # YStaticInfo、YStaticMethodInfo
├── 📁 Processors/           # 处理器层
│   ├── YStaticClassProcessor.cs   # 类级处理器
│   └── YStaticMethodProcessor.cs  # 方法级处理器
├── 📁 Generators/           # 协调器层
│   └── YStaticGenerator.cs # 主协调器
└── 📁 Temple/YStatic/       # 代码生成层
    ├── YStaticCodeCoordinator.cs    # 代码协调器
    ├── StaticExtensionGenerator.cs  # 扩展类生成器
    └── StaticStatisticsGenerator.cs # 统计生成器
```

### 协调器模式

```text
YStaticGenerator (主协调器)
    ↓ 分发任务
YStaticClassProcessor + YStaticMethodProcessor
    ↓ 生成数据模型
YStaticCodeCoordinator (代码协调器)
    ↓ 协调多个生成器
StaticExtensionGenerator + StaticStatisticsGenerator
    ↓ 输出
独立的 .ys.cs 文件
```

## 🔧 使用方式

### 1. YStatic - 普通静态方法

```csharp
[YStatic]
public partial class MathUtils
{
    public int Add(int a, int b) => a + b;
    public int Multiply(int a, int b) => a * b;
}

// 生成：
public static class MathUtilsExtensions
{
    private static readonly MathUtils _instance = new MathUtils();
    public static int Add(int a, int b) => _instance.Add(a, b);
    public static int Multiply(int a, int b) => _instance.Multiply(a, b);
}

// 使用：
var result = MathUtilsExtensions.Add(10, 20);
```

### 2. YStaticExtension - 扩展方法

```csharp
[YStaticExtension]
public partial class StringHelper
{
    public string ToTitleCase(string input) => /* 实现 */;
    public string Repeat(string input, int count) => /* 实现 */;
}

// 生成：
public static class StringHelperExtensions
{
    private static readonly StringHelper _instance = new StringHelper();
    public static string ToTitleCase(this string input) => _instance.ToTitleCase(input);
    public static string Repeat(this string input, int count) => _instance.Repeat(input, count);
}

// 使用：
var title = "hello world".ToTitleCase();
var repeated = "test".Repeat(3);
```

### 3. 方法级精细控制

```csharp
public partial class DataProcessor
{
    [YStatic]
    public string ProcessData(string data) => /* 实现 */;

    [YStaticExtension]
    public string FormatData(string data, string prefix) => /* 实现 */;

    [YStaticIgnore]
    public string IgnoredMethod(string data) => /* 实现 */;
}

// 生成：
public static class DataProcessorExtensions
{
    private static readonly DataProcessor _instance = new DataProcessor();
    
    // 普通静态方法
    public static string ProcessData(string data) => _instance.ProcessData(data);
    
    // 扩展方法
    public static string FormatData(this string data, string prefix) => _instance.FormatData(data, prefix);
    
    // IgnoredMethod 不会生成
}
```

### 4. 与 YService 共存

```csharp
[YService(ServiceLifetime.Scoped)]
[YStatic]  // 同时使用两个功能
public partial class HybridService
{
    public string Process(string input) => $"Processed: {input}";
}

// 生成两套代码：
// 1. YService 生成的依赖注入代码
// 2. YStatic 生成的静态方法代码

// 使用方式1：依赖注入
services.AddHybridService();
var service = serviceProvider.GetService<IHybridService>();
var result1 = service.Process("DI test");

// 使用方式2：静态方法
var result2 = HybridServiceExtensions.Process("Static test");
```

## 📊 生成文件

### 文件命名规范

- **扩展类文件**：`{ClassName}Extensions.ys.cs`
- **统计文件**：`YStaticStatistics.{Assembly}.ys.cs`
- **错误报告**：`YStaticError.{Assembly}.ys.cs`（如有错误）

### 生成示例

```csharp
// MathUtilsExtensions.ys.cs
// <auto-generated />
// 此文件由 Zylo.Toolkit YStatic v1.3 自动生成
// 原始类: YStaticTest.MathUtils
// 生成时间: 2025-01-09 15:30:00
// 生成模式: 静态方法

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace YStaticTest;

/// <summary>
/// MathUtilsExtensions - MathUtils 的静态标签扩展类
/// 
/// 🎯 核心功能：
/// - 提供静态方法形式的调用
/// - 无需创建实例即可调用
/// - 保持原始方法的完整签名
/// 
/// 💡 实现方式：
/// - 使用委托模式调用原始实例方法
/// - 编译时生成，运行时零开销
/// - 完全保留原始方法的类型安全性
/// 
/// 🚀 由 Zylo.Toolkit YStatic v1.3 自动生成
/// </summary>
public static class MathUtilsExtensions
{
    /// <summary>
    /// MathUtils 的私有静态实例，用于委托调用
    /// </summary>
    private static readonly YStaticTest.MathUtils _instance = new YStaticTest.MathUtils();

    /// <summary>
    /// 加法运算
    /// </summary>
    public static int Add(int a, int b)
    {
        return _instance.Add(a, b);
    }

    /// <summary>
    /// 乘法运算
    /// </summary>
    public static int Multiply(int a, int b)
    {
        return _instance.Multiply(a, b);
    }
}
```

## 🚀 技术特点

### 1. 委托模式实现

- **高性能**：使用 `private static readonly` 实例，避免重复创建
- **线程安全**：静态只读字段确保线程安全
- **零开销**：编译时生成，运行时无反射

### 2. 智能参数转换

- **扩展方法转换**：自动将第一个参数转换为 `this` 参数
- **类型保留**：完全保留原始方法的类型信息和约束
- **文档保留**：保持原始方法的 XML 文档注释

### 3. 错误处理

- **属性冲突检查**：YStatic 和 YStaticExtension 互斥
- **静态类跳过**：自动跳过已经是静态的类
- **异常隔离**：单个类的错误不影响其他类

## 📈 性能优势

1. **编译时生成**：零运行时开销
2. **委托调用**：直接方法调用，无反射
3. **内存友好**：单例模式，节省内存
4. **类型安全**：完整的编译时类型检查

## 🔍 调试支持

### 统计信息

生成的统计文件提供详细的使用数据：

```csharp
public static class YStaticStatistics_YStaticTest
{
    public static class OverallStatistics
    {
        public const int TotalClasses = 5;
        public const int TotalMethods = 18;
        public const int StaticModeClasses = 3;
        public const int ExtensionModeClasses = 2;
        // ...
    }
}
```

### 错误报告

如果生成过程中出现错误，会生成详细的错误报告文件，便于调试和修复。

## 🎯 最佳实践

1. **使用 partial 类**：确保类声明为 `partial`
2. **属性互斥**：不要同时使用 YStatic 和 YStaticExtension
3. **方法可见性**：只有 `public` 方法会被生成
4. **命名规范**：生成的扩展类统一使用 `Extensions` 后缀
5. **文档注释**：为原始方法添加 XML 文档注释，会被保留到生成的代码中

## 🚀 版本信息

- **版本**：v1.3
- **基于**：YService 成熟架构
- **兼容性**：与 YService 完全兼容
- **目标框架**：.NET 8.0+
- **生成器类型**：Roslyn 增量生成器
