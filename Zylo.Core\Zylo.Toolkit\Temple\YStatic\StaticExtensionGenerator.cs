using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;
using Zylo.Toolkit.Models;
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

namespace Zylo.Toolkit.Temple.YStatic;

/// <summary>
/// 静态文件生成器 - 专门负责生成静态标签文件
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成静态标签文件
/// 📄 生成内容：
/// 1. 静态扩展类定义（含完整 XML 文档注释）
/// 2. 静态方法实现（委托模式调用原始方法）
/// 3. 扩展方法实现（智能 this 参数转换）
/// </remarks>
public static class StaticFileGenerator
{
    #region 🏗️ 主生成入口

    /// <summary>
    /// 生成扩展类文件 - 完全按照 YService InterfaceFileGenerator 模式
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="staticInfo">YStatic 信息</param>
    public static void Generate(SourceProductionContext context, YStaticInfo staticInfo)
    {
        if (!staticInfo.GenerateExtensions)
        {
            return; // 不需要生成扩展类
        }

        try
        {
            // 🔧 完全按照 YService 模式：生成扩展类文件内容
            var fileContent = GenerateExtensionFileContent(staticInfo);

            // 📄 输出到文件
            var fileName = $"{staticInfo.ExtensionClassName}{YStaticConstants.GeneratedFileExtension}";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            // 🚨 生成错误时，输出错误信息到注释
            var errorContent = GenerateErrorContent(staticInfo, ex);
            var fileName = $"{staticInfo.ExtensionClassName}{YStaticConstants.ErrorFileExtension}";
            context.AddSource(fileName, SourceText.From(errorContent, Encoding.UTF8));
        }
    }

    /// <summary>
    /// 生成扩展类文件的完整内容 - 完全按照 YService InterfaceFileGenerator 结构
    /// </summary>
    /// <param name="staticInfo">YStatic 信息</param>
    /// <returns>生成的代码内容</returns>
    private static string GenerateExtensionFileContent(YStaticInfo staticInfo)
    {
        var sb = new StringBuilder();

        // 🔧 第一步：文件头部
        GenerateFileHeader(sb, staticInfo);

        // 🔧 第二步：命名空间开始
        GenerateNamespaceStart(sb, staticInfo);

        // 🔧 第三步：扩展类定义
        GenerateExtensionClassDefinition(sb, staticInfo);

        // 🔧 第四步：命名空间结束
        GenerateNamespaceEnd(sb);

        return sb.ToString();
    }



    #endregion

    #region 📝 文件头生成

    /// <summary>
    /// 生成文件头注释和 using 语句
    ///
    /// 🎯 核心功能：
    /// - 生成标准的文件头注释
    /// - 添加必要的 using 语句
    /// - 标明这是 YStatic v1.3 生成的代码
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfo">YStatic 信息</param>
    /// <summary>
    /// 生成文件头部 - 完全按照 YService 模式
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="staticInfo">静态信息</param>
    private static void GenerateFileHeader(StringBuilder sb, YStaticInfo staticInfo)
    {
        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "#nullable enable")  // 添加 nullable 支持
          .YAppendLine(I0, "// 此文件由 Zylo.Toolkit YStatic v1.3 自动生成")
          .YAppendLine(I0, $"// 原始类: {staticInfo.FullClassName}")
          .YAppendLine(I0, $"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 生成模式: {(staticInfo.IsStaticExtensionMode ? "扩展方法" : "静态方法")}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "using System;")
          .YAppendLine(I0, "using System.Collections.Generic;")
          .YAppendLine(I0, "using System.Linq;")
          .YAppendLine(I0, "using System.Threading.Tasks;")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成命名空间开始
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfo">YStatic 信息</param>
    /// <summary>
    /// 生成命名空间开始 - 完全按照 YService 模式
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="staticInfo">静态信息</param>
    private static void GenerateNamespaceStart(StringBuilder sb, YStaticInfo staticInfo)
    {
        if (!string.IsNullOrEmpty(staticInfo.Namespace))
        {
            sb.YAppendLine(I0, $"namespace {staticInfo.Namespace};")
              .YAppendEmptyLine();
        }
    }

    /// <summary>
    /// 生成命名空间结束 - 完全按照 YService 模式
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    private static void GenerateNamespaceEnd(StringBuilder sb)
    {
        // 对于文件范围的命名空间，不需要结束大括号
        // 这里保持空实现，与 YService 一致
    }

    /// <summary>
    /// 生成扩展类定义 - 完全按照 YService InterfaceFileGenerator 模式
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="staticInfo">静态信息</param>
    private static void GenerateExtensionClassDefinition(StringBuilder sb, YStaticInfo staticInfo)
    {
        // 🔧 生成类注释（读取原始类的 XML 注释）
        if (!string.IsNullOrEmpty(staticInfo.ClassDocumentation))
        {
            var docLines = staticInfo.ClassDocumentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                sb.YAppendLine(I0, line.Trim());
            }
        }

        // 🔧 生成类声明
        sb.YAppendLine(I0, $"public static partial class {staticInfo.ExtensionClassName}")
          .YAppendLine(I0, "{");

        // 🔧 生成私有实例字段
        sb.YAppendLine(I1, $"private static readonly {staticInfo.ClassName} _instance = new {staticInfo.ClassName}();")
          .YAppendEmptyLine();

        // 🔧 生成所有方法
        foreach (var method in staticInfo.Methods)
        {
            GenerateExtensionMethod(sb, method, staticInfo);
        }

        // 🔧 结束类定义
        sb.YAppendLine(I0, "}");
    }

    /// <summary>
    /// 生成扩展方法 - 完全按照 YService 模式
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="method">方法信息</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateExtensionMethod(StringBuilder sb, YStaticMethodInfo method, YStaticInfo staticInfo)
    {
        // 🔧 生成方法的 XML 注释（读取原始方法的注释）
        if (!string.IsNullOrEmpty(method.Documentation))
        {
            var docLines = method.Documentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                sb.YAppendLine(I1, line.Trim());
            }
        }

        // 🔧 生成方法签名（ExtensionParameters 已经包含 this 前缀）
        var signature = method.IsExtensionMethod
            ? $"public static {method.ReturnType} {method.Name}{method.GenericParameters}({method.ExtensionParameters})"
            : $"public static {method.ReturnType} {method.Name}{method.GenericParameters}({method.Parameters})";

        if (!string.IsNullOrEmpty(method.GenericConstraints))
        {
            signature += $" {method.GenericConstraints}";
        }

        sb.YAppendLine(I1, signature);

        // 🔧 生成方法体
        // 对于调用原始方法，我们需要所有参数，不管是否为扩展方法
        var callParameters = ExtractParameterNames(method.Parameters);
        var returnPrefix = method.ReturnType != "void" ? "return " : "";
        var genericParams = !string.IsNullOrEmpty(method.GenericParameters) ? method.GenericParameters : "";

        // 🔧 根据方法类型选择调用方式：静态方法直接调用类名，实例方法通过 _instance
        var caller = method.IsStaticMethod ? staticInfo.ClassName : "_instance";

        sb.YAppendLine(I1, "{")
          .YAppendLine(I2, $"{returnPrefix}{caller}.{method.Name}{genericParams}({callParameters});")
          .YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    #endregion

    #region 🏗️ 类声明生成

    /// <summary>
    /// 生成扩展类声明和文档
    ///
    /// 🎯 核心功能：
    /// - 生成静态扩展类的声明
    /// - 添加详细的 XML 文档注释
    /// - 说明生成的目的和使用方式
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateClassDeclaration(StringBuilder sb, YStaticInfo staticInfo)
    {
        // 🔧 使用 YCodeIndentFormatter 链式调用生成类文档注释
        sb.YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, $"/// {staticInfo.ExtensionClassName} - {staticInfo.ClassName} 的静态标签扩展类")
          .YAppendLine(I0, "/// ")
          .YAppendLine(I0, "/// 🎯 核心功能：");

        if (staticInfo.IsStaticExtensionMode)
        {
            sb.YAppendLine(I0, "/// - 提供扩展方法形式的静态调用")
              .YAppendLine(I0, "/// - 第一个参数转换为 this 参数")
              .YAppendLine(I0, "/// - 支持自然的链式调用");
        }
        else
        {
            sb.YAppendLine(I0, "/// - 提供静态方法形式的调用")
              .YAppendLine(I0, "/// - 无需创建实例即可调用")
              .YAppendLine(I0, "/// - 保持原始方法的完整签名");
        }

        sb.YAppendLine(I0, "/// ")
          .YAppendLine(I0, "/// 💡 实现方式：")
          .YAppendLine(I0, "/// - 使用委托模式调用原始实例方法")
          .YAppendLine(I0, "/// - 编译时生成，运行时零开销")
          .YAppendLine(I0, "/// - 完全保留原始方法的类型安全性")
          .YAppendLine(I0, "/// ")
          .YAppendLine(I0, $"/// 🚀 由 Zylo.Toolkit YStatic v1.3 自动生成")
          .YAppendLine(I0, "/// </summary>")
          .YAppendEmptyLine()
          .YAppendLine(I0, $"public static partial class {staticInfo.ExtensionClassName}")
          .YAppendLine(I0, "{");
    }

    /// <summary>
    /// 生成私有静态实例字段
    ///
    /// 🎯 核心功能：
    /// - 生成用于委托调用的私有静态实例
    /// - 使用 readonly 确保线程安全
    /// - 提供高性能的委托调用基础
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GeneratePrivateInstance(StringBuilder code, YStaticInfo staticInfo)
    {
        code.AppendLine("    /// <summary>");
        code.AppendLine($"    /// {staticInfo.ClassName} 的私有静态实例，用于委托调用");
        code.AppendLine("    /// </summary>");
        code.AppendLine($"    private static readonly {staticInfo.FullClassName} _instance = new {staticInfo.FullClassName}();");
        code.AppendLine();
    }

    #endregion

    #region 🔧 方法生成

    /// <summary>
    /// 生成所有静态方法
    ///
    /// 🎯 核心功能：
    /// - 遍历所有需要生成的方法
    /// - 为每个方法生成对应的静态版本
    /// - 保持原始方法的完整签名和文档
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateStaticMethods(StringBuilder code, YStaticInfo staticInfo)
    {
        foreach (var method in staticInfo.Methods.Where(m => !m.IsIgnored))
        {
            GenerateStaticMethod(code, method, staticInfo);
            code.AppendLine(); // 方法之间的空行
        }
    }

    /// <summary>
    /// 生成单个静态方法
    ///
    /// 🎯 核心功能：
    /// - 生成方法的 XML 文档注释
    /// - 生成方法签名（支持泛型和约束）
    /// - 生成委托调用实现
    /// - 处理扩展方法的特殊逻辑
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="method">方法信息</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateStaticMethod(StringBuilder sb, YStaticMethodInfo method, YStaticInfo staticInfo)
    {
        // 🔧 按照 YService 模式：读取原始方法的 XML 文档注释
        if (!string.IsNullOrEmpty(method.Documentation))
        {
            var docLines = method.Documentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                sb.YAppendLine(I1, line.Trim());
            }
        }

        // 🔧 生成方法签名
        var methodSignature = GenerateMethodSignature(method);
        sb.YAppendLine(I1, $"public static {methodSignature}");

        // 🔧 生成方法体
        GenerateMethodBody(sb, method, staticInfo);
    }

    /// <summary>
    /// 生成方法文档注释
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="method">方法信息</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateMethodDocumentation(StringBuilder code, YStaticMethodInfo method, YStaticInfo staticInfo)
    {
        code.AppendLine("    /// <summary>");

        if (!string.IsNullOrEmpty(method.Documentation))
        {
            // 使用原始方法的文档注释
            var lines = method.Documentation.Split('\n');
            foreach (var line in lines)
            {
                code.AppendLine($"    /// {line.Trim()}");
            }
        }
        else
        {
            // 生成默认文档注释
            if (method.IsExtensionMethod)
            {
                code.AppendLine($"    /// {method.Name} 的扩展方法版本");
            }
            else
            {
                code.AppendLine($"    /// {method.Name} 的静态方法版本");
            }
        }

        code.AppendLine("    /// </summary>");
    }

    /// <summary>
    /// 生成方法签名
    /// </summary>
    /// <param name="method">方法信息</param>
    /// <returns>方法签名字符串</returns>
    private static string GenerateMethodSignature(YStaticMethodInfo method)
    {
        var signature = new StringBuilder();

        // 返回类型
        signature.Append(method.ReturnType);
        signature.Append(" ");

        // 方法名
        signature.Append(method.Name);

        // 泛型参数
        if (!string.IsNullOrEmpty(method.GenericParameters))
        {
            signature.Append(method.GenericParameters);
        }

        // 参数列表
        signature.Append("(");
        signature.Append(method.IsExtensionMethod ? method.ExtensionParameters : method.Parameters);
        signature.Append(")");

        // 泛型约束
        if (!string.IsNullOrEmpty(method.GenericConstraints))
        {
            signature.Append(" ");
            signature.Append(method.GenericConstraints);
        }

        return signature.ToString();
    }

    /// <summary>
    /// 生成方法体
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="method">方法信息</param>
    /// <param name="staticInfo">YStatic 信息</param>
    private static void GenerateMethodBody(StringBuilder sb, YStaticMethodInfo method, YStaticInfo staticInfo)
    {
        // 🔧 按照 README 指导：使用 YCodeIndentFormatter 链式调用
        var callParameters = ExtractCallParameters(method);
        var returnPrefix = method.ReturnType != "void" ? "return " : "";
        var genericParams = !string.IsNullOrEmpty(method.GenericParameters) ? method.GenericParameters : "";

        // 🔧 根据方法类型选择调用方式：静态方法直接调用类名，实例方法通过 _instance
        var caller = method.IsStaticMethod ? staticInfo.ClassName : "_instance";

        sb.YAppendLine(I1, "{")
          .YAppendLine(I2, $"// Debug: IsExtensionMethod={method.IsExtensionMethod}, IsStaticMethod={method.IsStaticMethod}, Caller={caller}")
          .YAppendLine(I2, $"{returnPrefix}{caller}.{method.Name}{genericParams}({callParameters});")
          .YAppendLine(I1, "}");
    }

    #region v1.4 升级 - 复杂参数处理修复

    /// <summary>
    /// 从参数字符串中提取参数名称 - v1.4 增强版本
    ///
    /// 🆕 v1.4 新增功能：
    /// - 正确处理 params 参数
    /// - 正确处理 ref/out/in 修饰符
    /// - 保持参数修饰符在调用时的正确传递
    /// </summary>
    /// <param name="parametersString">参数字符串，如 "ref int value, params string[] items"</param>
    /// <returns>调用参数字符串，如 "ref value, items"</returns>
    private static string ExtractParameterNames(string parametersString)
    {
        if (string.IsNullOrEmpty(parametersString))
            return "";

        // v1.4 回到工作的版本：智能参数解析
        var parameters = SplitParametersSmartly(parametersString);
        var callParameters = new List<string>();

        foreach (var param in parameters)
        {
            var trimmed = param.Trim();
            if (string.IsNullOrEmpty(trimmed))
                continue;

            // v1.4 新增：解析参数修饰符和名称
            var parameterCall = ExtractParameterCallSyntax(trimmed);
            if (!string.IsNullOrEmpty(parameterCall))
            {
                callParameters.Add(parameterCall);
            }
        }

        return string.Join(", ", callParameters);
    }

    #region v1.4 升级 - 正则表达式方案

    /// <summary>
    /// v1.4 正则表达式方案：使用正则表达式提取参数调用语法
    ///
    /// 🎯 优势：
    /// - 简单直接，一个正则解决所有问题
    /// - 可靠稳定，不会有状态跟踪错误
    /// - 易于调试和维护
    ///
    /// 💡 处理的情况：
    /// - 简单参数：int value → value
    /// - 修饰符参数：ref int value → ref value
    /// - 默认值参数：string separator = ", " → separator
    /// - 元组参数：(double x, double y) point1 → point1
    /// - 泛型参数：Dictionary<string, int> dict → dict
    /// </summary>
    /// <param name="parametersString">参数字符串</param>
    /// <returns>调用参数字符串</returns>
    private static string ExtractParameterNamesWithRegex(string parametersString)
    {
        if (string.IsNullOrEmpty(parametersString))
            return "";

        // 使用正则表达式匹配参数
        // 模式说明：
        // (?:ref\s+|out\s+|in\s+|params\s+)? - 可选的修饰符
        // (?:[^,=]+?) - 类型部分（非贪婪匹配到逗号或等号）
        // \s+ - 空格
        // (\w+) - 参数名（捕获组）
        // (?:\s*=\s*[^,]+)? - 可选的默认值部分
        var pattern = @"(?:(ref|out|in|params)\s+)?(?:[^,=]+?)\s+(\w+)(?:\s*=\s*[^,]+)?(?=\s*,|\s*$)";

        var matches = System.Text.RegularExpressions.Regex.Matches(parametersString, pattern);
        var callParameters = new List<string>();

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            var modifier = match.Groups[1].Value; // ref/out/in/params
            var paramName = match.Groups[2].Value; // 参数名

            if (!string.IsNullOrEmpty(paramName))
            {
                // 组合修饰符和参数名
                if (!string.IsNullOrEmpty(modifier) && modifier != "params")
                {
                    callParameters.Add($"{modifier} {paramName}");
                }
                else
                {
                    callParameters.Add(paramName);
                }
            }
        }

        return string.Join(", ", callParameters);
    }

    #endregion

    /// <summary>
    /// v1.4 新增：智能分割参数字符串
    ///
    /// 🎯 解决问题：
    /// 处理泛型参数中的逗号，如 "Dictionary<string, int> dict, params object[] args"
    /// </summary>
    /// <param name="parametersString">参数字符串</param>
    /// <returns>正确分割的参数列表</returns>
    private static List<string> SplitParametersSmartly(string parametersString)
    {
        var parameters = new List<string>();
        var current = new StringBuilder();
        var bracketDepth = 0;
        var angleDepth = 0;
        var parenDepth = 0;  // v1.4 修复：添加括号深度跟踪
        var inString = false;  // v1.4 修复：添加字符串状态跟踪
        var stringChar = '\0';  // v1.4 修复：跟踪字符串引号类型
        var escapeNext = false;  // v1.4 修复：跟踪转义字符

        for (int i = 0; i < parametersString.Length; i++)
        {
            char c = parametersString[i];

            if (escapeNext)
            {
                escapeNext = false;
                current.Append(c);
                continue;
            }

            if (!inString)
            {
                switch (c)
                {
                    case '"':
                    case '\'':
                        inString = true;
                        stringChar = c;
                        current.Append(c);
                        break;
                    case '<':
                        angleDepth++;
                        current.Append(c);
                        break;
                    case '>':
                        angleDepth--;
                        current.Append(c);
                        break;
                    case '[':
                        bracketDepth++;
                        current.Append(c);
                        break;
                    case ']':
                        bracketDepth--;
                        current.Append(c);
                        break;
                    case '(':  // v1.4 修复：处理元组括号
                        parenDepth++;
                        current.Append(c);
                        break;
                    case ')':  // v1.4 修复：处理元组括号
                        parenDepth--;
                        current.Append(c);
                        break;
                    case ',':
                        // v1.4 修复：只有在所有括号都平衡且不在字符串内时，才认为是参数分隔符
                        if (bracketDepth == 0 && angleDepth == 0 && parenDepth == 0)
                        {
                            // 这是参数分隔符
                            parameters.Add(current.ToString());
                            current.Clear();
                        }
                        else
                        {
                            // 这是泛型、数组、元组或字符串中的逗号
                            current.Append(c);
                        }
                        break;
                    default:
                        current.Append(c);
                        break;
                }
            }
            else
            {
                // 在字符串内
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
                current.Append(c);
            }
        }

        // 添加最后一个参数
        if (current.Length > 0)
        {
            parameters.Add(current.ToString());
        }

        return parameters;
    }

    #region v1.4 升级 - 分类模板处理

    /// <summary>
    /// v1.4 重新设计：分类模板处理参数调用语法
    ///
    /// 🎯 设计理念：每种复杂情况对应一个专门的代码模板
    ///
    /// 💡 支持的模板：
    /// - 元组参数模板：(double x, double y) point1 → point1
    /// - 字符串默认值模板：string separator = ", " → separator
    /// - ref/out/in 模板：ref int value → ref value
    /// - params 模板：params string[] items → items
    /// - 简单参数模板：int count → count
    /// </summary>
    /// <param name="parameterDefinition">参数定义</param>
    /// <returns>调用语法</returns>
    private static string ExtractParameterCallSyntax(string parameterDefinition)
    {
        var trimmed = parameterDefinition.Trim();
        if (string.IsNullOrEmpty(trimmed))
            return "";

        // v1.4 分类模板处理

        // 模板1：元组参数 - (double x, double y) point1
        if (IsTupleParameter(trimmed))
        {
            return HandleTupleParameterTemplate(trimmed);
        }

        // 模板2：字符串默认值 - string separator = ", "
        if (HasStringDefaultValue(trimmed))
        {
            return HandleStringDefaultValueTemplate(trimmed);
        }

        // 模板3：ref/out/in 参数 - ref int value
        if (HasRefOutInModifier(trimmed))
        {
            return HandleRefOutInParameterTemplate(trimmed);
        }

        // 模板4：params 参数 - params string[] items
        if (HasParamsModifier(trimmed))
        {
            return HandleParamsParameterTemplate(trimmed);
        }

        // 模板5：简单参数（默认模板）- int count, Func<T, TResult> transformer
        return HandleSimpleParameterTemplate(trimmed);
    }

    #endregion

    #region v1.4 升级 - 参数模板实现

    /// <summary>
    /// 模板1：元组参数处理
    ///
    /// 🎯 专门处理：(double x, double y) point1, in (int a, int b) point2
    /// 输入：in (double x, double y) point1
    /// 输出：in point1
    /// </summary>
    private static string HandleTupleParameterTemplate(string parameterDefinition)
    {
        var modifiers = new List<string>();
        var workingString = parameterDefinition;

        // 提取修饰符
        if (workingString.StartsWith("ref "))
        {
            modifiers.Add("ref");
            workingString = workingString.Substring(4).Trim();
        }
        else if (workingString.StartsWith("out "))
        {
            modifiers.Add("out");
            workingString = workingString.Substring(4).Trim();
        }
        else if (workingString.StartsWith("in "))
        {
            modifiers.Add("in");
            workingString = workingString.Substring(3).Trim();
        }

        // 元组参数：查找最后一个 ')' 后的参数名
        var lastParenIndex = workingString.LastIndexOf(')');
        if (lastParenIndex >= 0 && lastParenIndex < workingString.Length - 1)
        {
            var afterParen = workingString.Substring(lastParenIndex + 1).Trim();
            var parts = afterParen.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length > 0)
            {
                var parameterName = parts[0];

                // 组合修饰符和参数名
                if (modifiers.Count > 0)
                {
                    return string.Join(" ", modifiers) + " " + parameterName;
                }
                return parameterName;
            }
        }

        return "";
    }

    /// <summary>
    /// 模板2：字符串默认值处理
    ///
    /// 🎯 专门处理：string separator = ", ", string path = "C:\\temp"
    /// 输入：string separator = ", "
    /// 输出：separator
    /// </summary>
    private static string HandleStringDefaultValueTemplate(string parameterDefinition)
    {
        // v1.4 修复：专门处理字符串默认值的简单算法
        // 对于 "string separator = \", \"" 这样的情况

        // 查找第一个不在字符串字面量内的 = 号
        var inString = false;
        var stringChar = '\0';
        var escapeNext = false;

        for (int i = 0; i < parameterDefinition.Length; i++)
        {
            char c = parameterDefinition[i];

            if (escapeNext)
            {
                escapeNext = false;
                continue;
            }

            if (!inString)
            {
                if (c == '"' || c == '\'')
                {
                    inString = true;
                    stringChar = c;
                }
                else if (c == '=')
                {
                    // 找到了默认值分隔符
                    var beforeEqual = parameterDefinition.Substring(0, i).Trim();
                    return ExtractParameterNameFromSimpleDefinition(beforeEqual);
                }
            }
            else
            {
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
            }
        }

        // 如果没有找到 = 号，按简单参数处理
        return ExtractParameterNameFromSimpleDefinition(parameterDefinition);
    }

    /// <summary>
    /// 模板3：ref/out/in 参数处理
    ///
    /// 🎯 专门处理：ref int value, out string result, in DateTime timestamp
    /// 输入：ref int value
    /// 输出：ref value
    /// </summary>
    private static string HandleRefOutInParameterTemplate(string parameterDefinition)
    {
        if (parameterDefinition.StartsWith("ref "))
        {
            var paramName = ExtractParameterNameFromSimpleDefinition(parameterDefinition.Substring(4).Trim());
            return "ref " + paramName;
        }
        else if (parameterDefinition.StartsWith("out "))
        {
            var paramName = ExtractParameterNameFromSimpleDefinition(parameterDefinition.Substring(4).Trim());
            return "out " + paramName;
        }
        else if (parameterDefinition.StartsWith("in "))
        {
            var paramName = ExtractParameterNameFromSimpleDefinition(parameterDefinition.Substring(3).Trim());
            return "in " + paramName;
        }

        return ExtractParameterNameFromSimpleDefinition(parameterDefinition);
    }

    /// <summary>
    /// 模板4：params 参数处理
    ///
    /// 🎯 专门处理：params string[] values, params object[] args
    /// 输入：params string[] values
    /// 输出：values
    /// </summary>
    private static string HandleParamsParameterTemplate(string parameterDefinition)
    {
        if (parameterDefinition.StartsWith("params "))
        {
            var withoutParams = parameterDefinition.Substring(7).Trim();
            return ExtractParameterNameFromSimpleDefinition(withoutParams);
        }

        return ExtractParameterNameFromSimpleDefinition(parameterDefinition);
    }

    /// <summary>
    /// 模板5：简单参数处理（默认模板）
    ///
    /// 🎯 专门处理：int count, Func<T, TResult> transformer, Dictionary<string, int> dict
    /// 输入：Func<T, TResult> transformer
    /// 输出：transformer
    /// </summary>
    private static string HandleSimpleParameterTemplate(string parameterDefinition)
    {
        return ExtractParameterNameFromSimpleDefinition(parameterDefinition);
    }

    #endregion

    #region v1.4 升级 - 辅助方法

    /// <summary>
    /// 检测是否为元组参数
    /// </summary>
    private static bool IsTupleParameter(string parameterDefinition)
    {
        return parameterDefinition.Contains('(') && parameterDefinition.Contains(')');
    }

    /// <summary>
    /// 检测是否有字符串默认值
    /// </summary>
    private static bool HasStringDefaultValue(string parameterDefinition)
    {
        return parameterDefinition.Contains('=') && parameterDefinition.Contains('"');
    }

    /// <summary>
    /// 检测是否有 ref/out/in 修饰符
    /// </summary>
    private static bool HasRefOutInModifier(string parameterDefinition)
    {
        return parameterDefinition.StartsWith("ref ") ||
               parameterDefinition.StartsWith("out ") ||
               parameterDefinition.StartsWith("in ");
    }

    /// <summary>
    /// 检测是否有 params 修饰符
    /// </summary>
    private static bool HasParamsModifier(string parameterDefinition)
    {
        return parameterDefinition.StartsWith("params ");
    }

    /// <summary>
    /// 查找默认值分隔符（=）的位置，避开字符串内的 =
    /// </summary>
    private static int FindDefaultValueSeparator(string parameterDefinition)
    {
        var inString = false;
        var stringChar = '\0';

        for (int i = 0; i < parameterDefinition.Length; i++)
        {
            char c = parameterDefinition[i];

            if (!inString)
            {
                if (c == '"' || c == '\'')
                {
                    inString = true;
                    stringChar = c;
                }
                else if (c == '=')
                {
                    return i;
                }
            }
            else
            {
                if (c == stringChar && (i == 0 || parameterDefinition[i - 1] != '\\'))
                {
                    inString = false;
                    stringChar = '\0';
                }
            }
        }

        return -1;
    }

    /// <summary>
    /// 从简单定义中提取参数名
    /// </summary>
    private static string ExtractParameterNameFromSimpleDefinition(string definition)
    {
        // v1.4 修复：简单直接的方法 - 先移除默认值
        var withoutDefault = definition;
        var equalIndex = definition.IndexOf('=');
        if (equalIndex > 0)
        {
            withoutDefault = definition.Substring(0, equalIndex).Trim();
        }

        var parts = withoutDefault.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // 从后往前找第一个有效的参数名
        for (int i = parts.Length - 1; i >= 0; i--)
        {
            var part = parts[i];

            // 跳过明显的类型符号
            if (part.Contains('<') || part.Contains('>') ||
                part.Contains('[') || part.Contains(']') ||
                part.Contains(','))
                continue;

            // 这应该是参数名
            return part;
        }

        return parts.Length > 0 ? parts[parts.Length - 1] : "";
    }

    #endregion

    /// <summary>
    /// v1.4 新增：从类型和名称字符串中提取参数名
    ///
    /// 🎯 处理复杂情况：
    /// - "int value" → "value"
    /// - "Func<T, TResult> transformer" → "transformer"
    /// - "Dictionary<string, int> dict = null" → "dict"
    /// - "(double x, double y) point1" → "point1"
    /// </summary>
    /// <param name="typeAndName">类型和名称字符串</param>
    /// <returns>参数名</returns>
    private static string ExtractParameterNameFromTypeAndName(string typeAndName)
    {
        if (string.IsNullOrEmpty(typeAndName))
            return "";

        // v1.4 修复：智能移除默认值部分
        var withoutDefault = RemoveDefaultValue(typeAndName);

        // v1.4 修复：通用的参数名提取算法
        return ExtractParameterNameUniversal(withoutDefault);
    }

    /// <summary>
    /// v1.4 新增：通用的参数名提取算法
    ///
    /// 🎯 通用处理各种复杂情况：
    /// - 元组类型：(double x, double y) point1 → point1
    /// - 泛型类型：Dictionary<string, int> dict → dict
    /// - 数组类型：string[] items → items
    /// - 委托类型：Func<T, TResult> processor → processor
    /// - 简单类型：int value → value
    /// </summary>
    /// <param name="typeAndName">不含默认值的类型和名称字符串</param>
    /// <returns>参数名</returns>
    private static string ExtractParameterNameUniversal(string typeAndName)
    {
        if (string.IsNullOrEmpty(typeAndName))
            return "";

        // v1.4 修复：更精确的元组处理
        // 策略1：如果包含元组，查找最后一个 ')' 后的内容
        if (typeAndName.Contains('(') && typeAndName.Contains(')'))
        {
            var lastParenIndex = typeAndName.LastIndexOf(')');
            if (lastParenIndex >= 0 && lastParenIndex < typeAndName.Length - 1)
            {
                var afterParen = typeAndName.Substring(lastParenIndex + 1).Trim();
                if (!string.IsNullOrEmpty(afterParen))
                {
                    // v1.4 修复：更精确的参数名提取
                    var parts = afterParen.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length > 0)
                    {
                        // 取第一个不包含特殊字符的部分作为参数名
                        foreach (var part in parts)
                        {
                            if (IsValidParameterName(part))
                            {
                                return part;
                            }
                        }
                        // 如果没有找到有效的参数名，返回第一个部分
                        return parts[0];
                    }
                }
            }
        }

        // 策略2：从右向左查找，找到第一个有效的参数名
        var allParts = typeAndName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        for (int i = allParts.Length - 1; i >= 0; i--)
        {
            var part = allParts[i];

            // 跳过明显的类型符号
            if (IsTypeSymbol(part))
                continue;

            // 检查是否为有效的参数名
            if (IsValidParameterName(part))
            {
                return part;
            }
        }

        // 如果没有找到，返回最后一个部分
        return allParts.Length > 0 ? allParts[allParts.Length - 1] : "";
    }

    /// <summary>
    /// v1.4 新增：判断是否为类型符号
    ///
    /// 🎯 识别各种类型符号，避免误认为参数名
    /// </summary>
    /// <param name="part">字符串部分</param>
    /// <returns>是否为类型符号</returns>
    private static bool IsTypeSymbol(string part)
    {
        if (string.IsNullOrEmpty(part))
            return true;

        // 包含泛型符号
        if (part.Contains('<') || part.Contains('>'))
            return true;

        // 包含数组符号
        if (part.Contains('[') || part.Contains(']'))
            return true;

        // 包含逗号（通常是泛型参数分隔符）
        if (part.Contains(','))
            return true;

        // 包含括号但不是完整的元组定义（可能是类型的一部分）
        if ((part.Contains('(') || part.Contains(')')) && !IsCompleteTupleType(part))
            return true;

        return false;
    }

    /// <summary>
    /// v1.4 新增：判断是否为完整的元组类型定义
    /// </summary>
    /// <param name="part">字符串部分</param>
    /// <returns>是否为完整的元组类型</returns>
    private static bool IsCompleteTupleType(string part)
    {
        // 简单判断：如果同时包含 '(' 和 ')'，可能是完整的元组
        return part.Contains('(') && part.Contains(')');
    }

    /// <summary>
    /// v1.4 新增：检查是否为有效的参数名
    ///
    /// 🎯 确保提取的参数名是有效的 C# 标识符
    /// </summary>
    /// <param name="name">候选参数名</param>
    /// <returns>是否为有效的参数名</returns>
    private static bool IsValidParameterName(string name)
    {
        if (string.IsNullOrEmpty(name))
            return false;

        // 不能包含特殊字符
        if (name.Contains('(') || name.Contains(')') ||
            name.Contains('<') || name.Contains('>') ||
            name.Contains('[') || name.Contains(']') ||
            name.Contains(',') || name.Contains('='))
        {
            return false;
        }

        // 必须是有效的 C# 标识符（简单检查）
        if (!char.IsLetter(name[0]) && name[0] != '_')
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// v1.4 新增：智能移除默认值部分
    ///
    /// 🎯 处理复杂情况：
    /// - "string separator = \", \"" → "string separator"
    /// - "int count = 10" → "int count"
    /// - "bool flag = true" → "bool flag"
    /// - "string? suffix = null" → "string? suffix"
    /// </summary>
    /// <param name="parameterWithDefault">包含默认值的参数定义</param>
    /// <returns>移除默认值后的参数定义</returns>
    private static string RemoveDefaultValue(string parameterWithDefault)
    {
        if (string.IsNullOrEmpty(parameterWithDefault) || !parameterWithDefault.Contains('='))
        {
            return parameterWithDefault;
        }

        // v1.4 修复：更强大的默认值处理，支持复杂字符串字面量
        return RemoveDefaultValueAdvanced(parameterWithDefault);
    }

    /// <summary>
    /// v1.4 新增：高级默认值移除算法
    ///
    /// 🎯 通用处理各种复杂的默认值情况：
    /// - 字符串字面量：separator = ", "
    /// - 转义字符：path = "C:\\temp"
    /// - 字符字面量：delimiter = ','
    /// - 嵌套括号：func = new Func<int, string>(x => x.ToString())
    /// - null 值：value = null
    /// </summary>
    /// <param name="parameterWithDefault">包含默认值的参数定义</param>
    /// <returns>移除默认值后的参数定义</returns>
    private static string RemoveDefaultValueAdvanced(string parameterWithDefault)
    {
        var inString = false;
        var stringChar = '\0';
        var bracketDepth = 0;
        var parenDepth = 0;
        var angleDepth = 0;
        var escapeNext = false;

        for (int i = 0; i < parameterWithDefault.Length; i++)
        {
            char c = parameterWithDefault[i];

            if (escapeNext)
            {
                escapeNext = false;
                continue;
            }

            if (!inString)
            {
                switch (c)
                {
                    case '"':
                    case '\'':
                        inString = true;
                        stringChar = c;
                        break;
                    case '[':
                        bracketDepth++;
                        break;
                    case ']':
                        bracketDepth--;
                        break;
                    case '(':
                        parenDepth++;
                        break;
                    case ')':
                        parenDepth--;
                        break;
                    case '<':
                        angleDepth++;
                        break;
                    case '>':
                        angleDepth--;
                        break;
                    case '=':
                        // 只有在不在字符串内，且不在任何括号内时，才认为是默认值分隔符
                        if (bracketDepth == 0 && parenDepth == 0 && angleDepth == 0)
                        {
                            return parameterWithDefault.Substring(0, i).Trim();
                        }
                        break;
                }
            }
            else
            {
                // 在字符串内
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
            }
        }

        // 如果没有找到有效的 = 分隔符，返回原字符串
        return parameterWithDefault;
    }

    #endregion

    /// <summary>
    /// 从参数字符串中提取参数名称 - v1.3 兼容版本（已弃用）
    ///
    /// ⚠️ 此方法已被 v1.4 增强版本替代，保留用于向后兼容
    /// </summary>
    [Obsolete("使用增强版本的 ExtractParameterNames 方法")]
    private static string ExtractParameterNames_Legacy(string parametersString)
    {
        if (string.IsNullOrEmpty(parametersString))
            return "";

        // 简单的参数名提取：分割参数，提取每个参数的名称部分
        var parameters = parametersString.Split(',');
        var names = new List<string>();

        foreach (var param in parameters)
        {
            var trimmed = param.Trim();
            if (string.IsNullOrEmpty(trimmed))
                continue;

            // 查找参数名：通常是类型后面的第一个标识符
            var parts = trimmed.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length >= 2)
            {
                var name = parts[1];
                // 移除默认值部分（如果有）
                if (name.Contains('='))
                {
                    name = name.Split('=')[0].Trim();
                }
                names.Add(name);
            }
        }

        return string.Join(", ", names);
    }

    /// <summary>
    /// 提取调用参数 - 按照 README 指导使用 YMethodSignatureHelper
    /// </summary>
    /// <param name="method">方法信息</param>
    /// <returns>调用参数字符串</returns>
    private static string ExtractCallParameters(YStaticMethodInfo method)
    {
        // 🔧 按照 README 指导：使用 YMethodSignatureHelper.GetParametersString()
        // 但我们这里已经有了 method.Parameters，所以直接使用 ExtractParameterNames
        var parameterNames = ExtractParameterNames(method.Parameters);

        // 🔧 对于扩展方法，跳过第一个参数（this 参数）
        if (method.IsExtensionMethod)
        {
            if (string.IsNullOrEmpty(parameterNames))
                return "";

            var paramNames = parameterNames.Split(',').Select(p => p.Trim()).Where(p => !string.IsNullOrEmpty(p)).ToList();
            if (paramNames.Count > 1)
            {
                return string.Join(", ", paramNames.Skip(1));
            }
            else
            {
                return ""; // 只有 this 参数，无需传递其他参数
            }
        }

        return parameterNames;
    }

    #endregion

    #region 🚨 错误处理

    /// <summary>
    /// 生成错误内容
    /// </summary>
    /// <param name="staticInfo">静态标签信息</param>
    /// <param name="exception">异常信息</param>
    /// <returns>错误内容</returns>
    private static string GenerateErrorContent(YStaticInfo staticInfo, Exception exception)
    {
        var sb = new StringBuilder();

        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YStatic 静态标签生成失败")
          .YAppendLine(I0, $"// 错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 源类: {staticInfo.ClassName}")
          .YAppendLine(I0, $"// 错误信息: {exception.Message}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, "生成静态标签时发生错误：")
          .YAppendLine(I0, $"类名: {staticInfo.ClassName}")
          .YAppendLine(I0, $"命名空间: {staticInfo.Namespace}")
          .YAppendLine(I0, $"扩展类名: {staticInfo.ExtensionClassName}")
          .YAppendLine(I0, $"错误详情: {exception}")
          .YAppendLine(I0, "*/");

        return sb.ToString();
    }

    #endregion
}
