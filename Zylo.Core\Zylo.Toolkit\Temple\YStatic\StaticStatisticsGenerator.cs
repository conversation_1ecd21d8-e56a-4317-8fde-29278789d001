using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Models;

namespace Zylo.Toolkit.Temple.YStatic;

#region v1.3 升级 - YStatic 统计信息生成器

/// <summary>
/// YStatic 统计信息生成器 - 基于 YService 架构的统计信息生成器
///
/// 🎯 核心职责：
/// 1. 📊 统计收集：收集 YStatic 功能的使用统计信息
/// 2. 📝 报告生成：生成详细的统计报告文件
/// 3. 🔍 分析支持：提供便于调试和分析的数据
/// 4. 📈 性能监控：统计生成的类和方法数量
/// 5. 🚀 开发支持：帮助开发者了解 YStatic 的使用情况
///
/// 💡 设计理念：
/// - 基于 YService 的统计生成模式
/// - 提供详细的使用统计数据
/// - 便于开发者调试和监控
/// - 支持性能分析和优化
///
/// 🔧 统计内容：
/// - 总体统计：类数量、方法数量、生成模式分布
/// - 详细信息：每个类的具体信息
/// - 性能数据：生成时间、文件大小等
/// - 错误统计：生成过程中的问题统计
///
/// 🚀 生成示例：
/// // YStatic 统计信息
/// // 总计：3 个类，15 个方法
/// // 静态方法模式：2 个类，10 个方法
/// // 扩展方法模式：1 个类，5 个方法
/// </summary>
public static class StatisticsGenerator
{
    #region 📊 主统计生成

    /// <summary>
    /// 生成统计信息文件 - 基于 YService 模式的主入口点
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void Generate(SourceProductionContext context, List<YStaticInfo> staticInfos, string assemblyName)
    {
        try
        {
            // 🔧 生成统计信息内容
            var fileContent = GenerateStatistics(staticInfos, assemblyName);

            // 📄 输出到文件
            var fileName = $"YStaticStatistics.{assemblyName}{YStaticConstants.GeneratedFileExtension}";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            // 🚨 生成错误时，输出错误信息
            var errorContent = $@"// YStatic 统计生成错误
// 程序集: {assemblyName}
// 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
// 错误: {ex.Message}";
            var fileName = $"YStaticStatisticsError.{assemblyName}{YStaticConstants.GeneratedFileExtension}";
            context.AddSource(fileName, SourceText.From(errorContent, Encoding.UTF8));
        }
    }

    /// <summary>
    /// 生成统计信息 - 内部实现
    ///
    /// 🎯 核心功能：
    /// 根据 YStatic 信息列表生成详细的统计报告
    ///
    /// 💡 设计理念：
    /// - 基于 YService 的统计生成模式
    /// - 提供全面的统计数据
    /// - 便于开发者了解使用情况
    ///
    /// 🔧 统计结构：
    /// 1. 文件头和总体统计
    /// 2. 分类统计（静态方法 vs 扩展方法）
    /// 3. 详细的类和方法信息
    /// 4. 性能和质量指标
    /// 5. 生成时间和版本信息
    /// </summary>
    /// <param name="staticInfos">YStatic 信息列表</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>生成的统计信息代码</returns>
    private static string GenerateStatistics(List<YStaticInfo> staticInfos, string assemblyName)
    {
        var code = new StringBuilder();

        // 🎯 第一部分：文件头和基本信息
        GenerateFileHeader(code, assemblyName);

        // 🎯 第二部分：总体统计
        GenerateOverallStatistics(code, staticInfos);

        // 🎯 第三部分：分类统计
        GenerateCategoryStatistics(code, staticInfos);

        // 🎯 第四部分：详细信息
        GenerateDetailedInformation(code, staticInfos);

        // 🎯 第五部分：性能指标
        GeneratePerformanceMetrics(code, staticInfos);

        // 🎯 第六部分：结束信息
        GenerateFooter(code);

        return code.ToString();
    }

    #endregion

    #region 📝 文件头生成

    /// <summary>
    /// 生成文件头和基本信息
    ///
    /// 🎯 核心功能：
    /// - 生成标准的文件头注释
    /// - 标明这是 YStatic v1.3 生成的统计信息
    /// - 提供基本的程序集信息
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateFileHeader(StringBuilder code, string assemblyName)
    {
        code.AppendLine("// <auto-generated />");
        code.AppendLine("// YStatic v1.3 统计信息报告");
        code.AppendLine($"// 程序集: {assemblyName}");
        code.AppendLine($"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        code.AppendLine("// 此文件包含 YStatic 功能的详细使用统计信息");
        code.AppendLine();

        code.AppendLine("using System;");
        code.AppendLine("using System.Collections.Generic;");
        code.AppendLine();

        code.AppendLine($"namespace Zylo.Toolkit.Generated.YStatic.Statistics;");
        code.AppendLine();

        code.AppendLine("/// <summary>");
        code.AppendLine($"/// YStatic 统计信息 - {assemblyName}");
        code.AppendLine("/// ");
        code.AppendLine("/// 🎯 核心功能：");
        code.AppendLine("/// - 提供 YStatic 功能的详细使用统计");
        code.AppendLine("/// - 便于开发者了解生成情况");
        code.AppendLine("/// - 支持性能分析和优化");
        code.AppendLine("/// ");
        code.AppendLine($"/// 🚀 由 Zylo.Toolkit YStatic v1.3 自动生成");
        code.AppendLine("/// </summary>");
        code.AppendLine($"public static class YStaticStatistics_{assemblyName.Replace(".", "_")}");
        code.AppendLine("{");
    }

    #endregion

    #region 📊 总体统计

    /// <summary>
    /// 生成总体统计信息
    ///
    /// 🎯 核心功能：
    /// - 统计总的类数量和方法数量
    /// - 计算不同模式的分布
    /// - 提供概览性的统计数据
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    private static void GenerateOverallStatistics(StringBuilder code, List<YStaticInfo> staticInfos)
    {
        var totalClasses = staticInfos.Count;
        var totalMethods = staticInfos.Sum(info => info.Methods.Count);
        var staticModeClasses = staticInfos.Count(info => !info.IsStaticExtensionMode);
        var extensionModeClasses = staticInfos.Count(info => info.IsStaticExtensionMode);
        var staticModeMethods = staticInfos.Where(info => !info.IsStaticExtensionMode).Sum(info => info.Methods.Count);
        var extensionModeMethods =
            staticInfos.Where(info => info.IsStaticExtensionMode).Sum(info => info.Methods.Count);

        code.AppendLine("    /// <summary>");
        code.AppendLine("    /// 总体统计信息");
        code.AppendLine("    /// </summary>");
        code.AppendLine("    public static class OverallStatistics");
        code.AppendLine("    {");
        code.AppendLine($"        /// <summary>总类数量</summary>");
        code.AppendLine($"        public const int TotalClasses = {totalClasses};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>总方法数量</summary>");
        code.AppendLine($"        public const int TotalMethods = {totalMethods};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>静态方法模式类数量</summary>");
        code.AppendLine($"        public const int StaticModeClasses = {staticModeClasses};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>扩展方法模式类数量</summary>");
        code.AppendLine($"        public const int ExtensionModeClasses = {extensionModeClasses};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>静态方法模式方法数量</summary>");
        code.AppendLine($"        public const int StaticModeMethods = {staticModeMethods};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>扩展方法模式方法数量</summary>");
        code.AppendLine($"        public const int ExtensionModeMethods = {extensionModeMethods};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>生成时间</summary>");
        code.AppendLine($"        public static readonly DateTime GeneratedAt = new DateTime({DateTime.Now.Ticks}L);");
        code.AppendLine("    }");
        code.AppendLine();
    }

    #endregion

    #region 🔍 分类统计

    /// <summary>
    /// 生成分类统计信息
    ///
    /// 🎯 核心功能：
    /// - 按生成模式分类统计
    /// - 提供详细的分类数据
    /// - 便于分析不同模式的使用情况
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    private static void GenerateCategoryStatistics(StringBuilder code, List<YStaticInfo> staticInfos)
    {
        code.AppendLine("    /// <summary>");
        code.AppendLine("    /// 分类统计信息");
        code.AppendLine("    /// </summary>");
        code.AppendLine("    public static class CategoryStatistics");
        code.AppendLine("    {");

        // 静态方法模式统计
        var staticModeInfos = staticInfos.Where(info => !info.IsStaticExtensionMode).ToList();
        code.AppendLine("        /// <summary>");
        code.AppendLine("        /// 静态方法模式的类列表");
        code.AppendLine("        /// </summary>");
        code.AppendLine("        public static readonly string[] StaticModeClasses = new string[]");
        code.AppendLine("        {");
        foreach (var info in staticModeInfos)
        {
            code.AppendLine($"            \"{info.FullClassName}\",");
        }

        code.AppendLine("        };");
        code.AppendLine();

        // 扩展方法模式统计
        var extensionModeInfos = staticInfos.Where(info => info.IsStaticExtensionMode).ToList();
        code.AppendLine("        /// <summary>");
        code.AppendLine("        /// 扩展方法模式的类列表");
        code.AppendLine("        /// </summary>");
        code.AppendLine("        public static readonly string[] ExtensionModeClasses = new string[]");
        code.AppendLine("        {");
        foreach (var info in extensionModeInfos)
        {
            code.AppendLine($"            \"{info.FullClassName}\",");
        }

        code.AppendLine("        };");

        code.AppendLine("    }");
        code.AppendLine();
    }

    #endregion

    #region 📋 详细信息

    /// <summary>
    /// 生成详细信息
    ///
    /// 🎯 核心功能：
    /// - 提供每个类的详细信息
    /// - 包含方法级别的统计数据
    /// - 便于深入分析和调试
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    private static void GenerateDetailedInformation(StringBuilder code, List<YStaticInfo> staticInfos)
    {
        code.AppendLine("    /// <summary>");
        code.AppendLine("    /// 详细信息");
        code.AppendLine("    /// </summary>");
        code.AppendLine("    public static class DetailedInformation");
        code.AppendLine("    {");

        foreach (var info in staticInfos)
        {
            var safeClassName = info.ClassName.Replace(".", "_").Replace("<", "_").Replace(">", "_");

            code.AppendLine($"        /// <summary>");
            code.AppendLine($"        /// {info.ClassName} 的详细信息");
            code.AppendLine($"        /// </summary>");
            code.AppendLine($"        public static class {safeClassName}");
            code.AppendLine("        {");
            code.AppendLine($"            public const string ClassName = \"{info.ClassName}\";");
            code.AppendLine($"            public const string ExtensionClassName = \"{info.ExtensionClassName}\";");
            code.AppendLine($"            public const string Namespace = \"{info.Namespace}\";");
            code.AppendLine(
                $"            public const bool IsExtensionMode = {info.IsStaticExtensionMode.ToString().ToLower()};");
            code.AppendLine(
                $"            public const bool IsClassLevelTriggered = {info.IsClassLevelTriggered.ToString().ToLower()};");
            code.AppendLine($"            public const int MethodCount = {info.Methods.Count};");
            code.AppendLine();

            // 方法列表
            code.AppendLine("            public static readonly string[] Methods = new string[]");
            code.AppendLine("            {");
            foreach (var method in info.Methods)
            {
                code.AppendLine($"                \"{method.Name}\",");
            }

            code.AppendLine("            };");

            code.AppendLine("        }");
            code.AppendLine();
        }

        code.AppendLine("    }");
        code.AppendLine();
    }

    #endregion

    #region 📈 性能指标

    /// <summary>
    /// 生成性能指标
    ///
    /// 🎯 核心功能：
    /// - 提供性能相关的统计数据
    /// - 便于性能分析和优化
    /// - 监控生成质量
    /// </summary>
    /// <param name="code">代码构建器</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    private static void GeneratePerformanceMetrics(StringBuilder code, List<YStaticInfo> staticInfos)
    {
        var avgMethodsPerClass = staticInfos.Count > 0
            ? (double)staticInfos.Sum(info => info.Methods.Count) / staticInfos.Count
            : 0;
        var maxMethodsInClass = staticInfos.Count > 0 ? staticInfos.Max(info => info.Methods.Count) : 0;
        var minMethodsInClass = staticInfos.Count > 0 ? staticInfos.Min(info => info.Methods.Count) : 0;

        code.AppendLine("    /// <summary>");
        code.AppendLine("    /// 性能指标");
        code.AppendLine("    /// </summary>");
        code.AppendLine("    public static class PerformanceMetrics");
        code.AppendLine("    {");
        code.AppendLine($"        /// <summary>平均每个类的方法数量</summary>");
        code.AppendLine($"        public const double AverageMethodsPerClass = {avgMethodsPerClass:F2};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>单个类的最大方法数量</summary>");
        code.AppendLine($"        public const int MaxMethodsInClass = {maxMethodsInClass};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>单个类的最小方法数量</summary>");
        code.AppendLine($"        public const int MinMethodsInClass = {minMethodsInClass};");
        code.AppendLine();
        code.AppendLine($"        /// <summary>生成的文件数量</summary>");
        code.AppendLine(
            $"        public const int GeneratedFiles = {staticInfos.Count + 1}; // +1 for this statistics file");
        code.AppendLine("    }");
    }

    #endregion

    #region 🎨 结束信息

    /// <summary>
    /// 生成结束信息
    /// </summary>
    /// <param name="code">代码构建器</param>
    private static void GenerateFooter(StringBuilder code)
    {
        code.AppendLine("}");
        code.AppendLine();
        code.AppendLine("// YStatic v1.3 统计信息生成完成");
        code.AppendLine($"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
    }

    #endregion
}

#endregion