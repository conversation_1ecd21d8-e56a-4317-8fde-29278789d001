using Microsoft.CodeAnalysis;
using Zylo.Toolkit.Models;

namespace Zylo.Toolkit.Temple.YStatic;

#region v1.3 升级 - YStatic 代码协调器

/// <summary>
/// YStatic 代码协调器 - 基于 YService 架构的代码生成协调中心
///
/// 🎯 核心职责（基于 YServiceCodeCoordinator 设计）：
/// 1. 🚀 生成协调：协调多个代码生成器的执行
/// 2. 📋 任务分发：将不同类型的生成任务分发给专门的生成器
/// 3. 🔧 文件管理：管理生成文件的命名和输出
/// 4. 📊 统计收集：收集生成统计信息
/// 5. 🚨 错误处理：统一的错误处理和报告
///
/// 🏗️ 协调器架构（基于 YService 模式）：
/// ┌─────────────────────────┐    ┌─────────────────────────┐
/// │  YStaticCodeCoordinator │    │     生成器生态系统       │
/// │       (协调者)          │ -> │  StaticFileGenerator      │
/// │                         │    │  ErrorReportGenerator     │
/// │                         │    │  StatisticsGenerator      │
/// │                         │    │  (无服务注册 - 静态方法)  │
/// └─────────────────────────┘    └─────────────────────────┘
///
/// 💡 设计理念：
/// - 完全基于 YService 的 YServiceCodeCoordinator 架构
/// - 协调者模式：不直接生成代码，专注于协调和管理
/// - 职责分离：每个生成器专注于特定类型的代码生成
/// - 可扩展性：新的生成需求可以轻松添加新的生成器
/// - 统一管理：统一的文件命名、错误处理、统计收集
///
/// 🔧 生成流程：
/// 1. 验证输入数据的有效性
/// 2. 为每个 YStaticInfo 生成对应的扩展类
/// 3. 生成统计信息文件
/// 4. 处理错误和异常情况
/// </summary>
public static class YStaticCodeCoordinator
{
    #region 🚀 主协调入口

    /// <summary>
    /// 执行完整的代码生成流程 - 完全按照 YService 协调器模式
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="staticInfos">YStatic 信息列表</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void ExecuteGeneration(
        SourceProductionContext context,
        List<YStaticInfo> staticInfos,
        string assemblyName)
    {
        try
        {
            // 🔍 第一步：数据预处理
            var staticList = PreprocessStaticInfos(staticInfos);

            if (!staticList.Any())
            {
                // 没有静态信息需要生成，直接返回
                return;
            }

            // 🔧 第二步：生成扩展类文件
            GenerateExtensionFiles(context, staticList);

            // 📊 第三步：生成统计信息文件（可选）
            GenerateStatisticsFile(context, staticList, assemblyName);
        }
        catch (Exception ex)
        {
            // 🚨 错误处理：生成错误报告
            GenerateErrorReport(context, ex, assemblyName);
        }
    }

    #endregion

    #region 🔧 协调流程方法

    /// <summary>
    /// 预处理静态信息
    /// </summary>
    /// <param name="staticInfos">原始静态信息</param>
    /// <returns>处理后的静态信息列表</returns>
    private static List<YStaticInfo> PreprocessStaticInfos(List<YStaticInfo> staticInfos)
    {
        var staticList = staticInfos.Where(info =>
            info != null &&
            info.GenerateExtensions &&
            info.HasMethods &&
            !string.IsNullOrWhiteSpace(info.ClassName) &&
            !string.IsNullOrWhiteSpace(info.ExtensionClassName)
        ).ToList();

        // 🔍 数据验证
        ValidateStaticInfos(staticList);

        // 📊 数据统计
        LogGenerationStatistics(staticList);

        return staticList;
    }

    /// <summary>
    /// 生成所有扩展类文件 - 完全按照 YService 模式
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="staticInfos">静态信息列表</param>
    private static void GenerateExtensionFiles(SourceProductionContext context, List<YStaticInfo> staticInfos)
    {
        foreach (var staticInfo in staticInfos)
        {
            if (staticInfo.GenerateExtensions)
            {
                // 🎯 完全按照 YService 模式：委托给专门的静态文件生成器
                StaticFileGenerator.Generate(context, staticInfo);
            }
        }
    }

    /// <summary>
    /// 生成统计信息文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="staticInfos">静态信息列表</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateStatisticsFile(SourceProductionContext context, List<YStaticInfo> staticInfos, string assemblyName)
    {
        try
        {
            // 🎯 委托给专门的统计生成器
            StatisticsGenerator.Generate(context, staticInfos, assemblyName);
        }
        catch (Exception ex)
        {
            // 统计生成失败不应该影响主要功能
            var errorContent = $@"// YStatic 统计生成错误
// 错误: {ex.Message}
// 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            context.AddSource($"YStaticStatisticsError.{assemblyName}.ys.cs", errorContent);
        }
    }

    /// <summary>
    /// 验证静态信息
    /// </summary>
    /// <param name="staticInfos">静态信息列表</param>
    private static void ValidateStaticInfos(List<YStaticInfo> staticInfos)
    {
        // 基本验证逻辑
        foreach (var info in staticInfos)
        {
            if (string.IsNullOrWhiteSpace(info.ClassName))
                throw new InvalidOperationException($"YStatic 类名不能为空");

            if (string.IsNullOrWhiteSpace(info.ExtensionClassName))
                throw new InvalidOperationException($"YStatic 扩展类名不能为空：{info.ClassName}");
        }
    }

    /// <summary>
    /// 记录生成统计信息
    /// </summary>
    /// <param name="staticInfos">静态信息列表</param>
    private static void LogGenerationStatistics(List<YStaticInfo> staticInfos)
    {
        // 这里可以添加日志记录逻辑
        // 目前只是占位符
    }

    #endregion

    #region 🚨 错误处理

    /// <summary>
    /// 生成错误报告
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="ex">异常</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateErrorReport(SourceProductionContext context, Exception ex, string assemblyName)
    {
        var errorContent = $@"// YStatic 全局生成错误报告
// 程序集: {assemblyName}
// 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
// 错误: {ex.Message}
// 堆栈: {ex.StackTrace}

namespace Zylo.Toolkit.Generated.YStatic.Errors
{{
    // 此文件表示 YStatic 生成过程中发生了全局错误
    // 请检查上述错误信息并修复相关问题
}}";

        context.AddSource($"YStaticGlobalError.{assemblyName}.ys.cs", errorContent);
    }

    #endregion
}

#endregion
