using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;

namespace Zylo.Toolkit.Temple.Yservice;

/// <summary>
/// 错误报告生成器 - 专门负责生成错误报告文件
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成错误报告
/// 📄 生成内容：
/// 1. 详细的错误信息
/// 2. 调试信息和堆栈跟踪
/// 3. 修复建议
/// </remarks>
public static class ErrorReportGenerator
{
    #region 🎯 主要生成方法

    /// <summary>
    /// 生成错误报告文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void Generate(SourceProductionContext context, Exception exception, string assemblyName)
    {
        try
        {
            // 🔧 生成错误报告内容
            var fileContent = GenerateErrorReportContent(exception, assemblyName);

            // 📄 输出到文件
            var fileName = $"YServiceErrorReport.{SanitizeAssemblyName(assemblyName)}.yg.cs";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception reportException)
        {
            // 🚨 连错误报告都生成失败时，输出最简化版本
            var fallbackContent = GenerateFallbackErrorReport(exception, reportException, assemblyName);
            var fileName = $"YServiceErrorReport.{SanitizeAssemblyName(assemblyName)}.fallback.yg.cs";
            context.AddSource(fileName, SourceText.From(fallbackContent, Encoding.UTF8));
        }
    }

    #endregion

    #region 🔧 内容生成方法

    /// <summary>
    /// 生成错误报告内容
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>错误报告内容</returns>
    private static string GenerateErrorReportContent(Exception exception, string assemblyName)
    {
        var sb = new StringBuilder();

        // 🔧 文件头部
        GenerateFileHeader(sb, exception, assemblyName);

        // 🔧 错误摘要
        GenerateErrorSummary(sb, exception);

        // 🔧 详细错误信息
        GenerateDetailedErrorInfo(sb, exception);

        // 🔧 修复建议
        GenerateFixSuggestions(sb, exception);

        // 🔧 联系信息
        GenerateContactInfo(sb);

        return sb.ToString();
    }

    /// <summary>
    /// 生成文件头部
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateFileHeader(StringBuilder sb, Exception exception, string assemblyName)
    {
        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 错误报告")
          .YAppendLine(I0, $"// 错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .YAppendLine(I0, $"// 错误类型: {exception.GetType().Name}")
          .YAppendLine(I0, $"// 错误消息: {exception.Message}")
          .AppendLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, "=".PadRight(80, '='))
          .YAppendLine(I0, "YService 代码生成错误报告")
          .YAppendLine(I0, "=".PadRight(80, '='))
          .AppendLine();
    }

    /// <summary>
    /// 生成错误摘要
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="exception">异常信息</param>
    private static void GenerateErrorSummary(StringBuilder sb, Exception exception)
    {
        sb.YAppendLine(I0, "📋 错误摘要:")
          .YAppendLine(I0, $"   错误类型: {exception.GetType().Name}")
          .YAppendLine(I0, $"   错误消息: {exception.Message}")
          .YAppendLine(I0, $"   发生时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .AppendLine();

        if (exception.InnerException != null)
        {
            sb.YAppendLine(I0, "🔍 内部异常:")
              .YAppendLine(I0, $"   类型: {exception.InnerException.GetType().Name}")
              .YAppendLine(I0, $"   消息: {exception.InnerException.Message}")
              .AppendLine();
        }
    }

    /// <summary>
    /// 生成详细错误信息
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="exception">异常信息</param>
    private static void GenerateDetailedErrorInfo(StringBuilder sb, Exception exception)
    {
        sb.YAppendLine(I0, "🔧 详细错误信息:")
          .AppendLine();

        // 堆栈跟踪
        if (!string.IsNullOrEmpty(exception.StackTrace))
        {
            sb.YAppendLine(I0, "📍 堆栈跟踪:")
              .YAppendLine(I0, exception.StackTrace)
              .AppendLine();
        }

        // 数据信息
        if (exception.Data.Count > 0)
        {
            sb.YAppendLine(I0, "📊 附加数据:");
            foreach (var key in exception.Data.Keys)
            {
                sb.YAppendLine(I0, $"   {key}: {exception.Data[key]}");
            }
            sb.AppendLine();
        }

        // 源信息
        if (!string.IsNullOrEmpty(exception.Source))
        {
            sb.YAppendLine(I0, $"📦 错误源: {exception.Source}")
              .AppendLine();
        }
    }

    /// <summary>
    /// 生成修复建议
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="exception">异常信息</param>
    private static void GenerateFixSuggestions(StringBuilder sb, Exception exception)
    {
        sb.YAppendLine(I0, "💡 修复建议:")
          .AppendLine();

        // 根据异常类型提供具体建议
        var suggestions = GetFixSuggestions(exception);
        foreach (var suggestion in suggestions)
        {
            sb.YAppendLine(I0, $"   • {suggestion}");
        }

        sb.AppendLine()
          .YAppendLine(I0, "🔧 通用排查步骤:")
          .YAppendLine(I0, "   1. 检查 YService 属性的使用是否正确")
          .YAppendLine(I0, "   2. 确认类是否标记为 partial")
          .YAppendLine(I0, "   3. 验证命名空间和类名是否有效")
          .YAppendLine(I0, "   4. 检查是否有语法错误")
          .YAppendLine(I0, "   5. 尝试清理和重新构建项目")
          .YAppendLine(I0, "   6. 更新到最新版本的 YService")
          .AppendLine();
    }

    /// <summary>
    /// 生成联系信息
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    private static void GenerateContactInfo(StringBuilder sb)
    {
        sb.YAppendLine(I0, "📞 获取帮助:")
          .YAppendLine(I0, "   如果问题持续存在，请提供此错误报告并联系:")
          .YAppendLine(I0, "   • GitHub Issues: https://github.com/zylo-tech/yservice/issues")
          .YAppendLine(I0, "   • 文档: https://docs.zylo.dev/yservice")
          .YAppendLine(I0, "   • 邮箱: <EMAIL>")
          .AppendLine()
          .YAppendLine(I0, "=".PadRight(80, '='))
          .YAppendLine(I0, "*/");
    }

    #endregion

    #region 🔧 辅助方法

    /// <summary>
    /// 根据异常类型获取修复建议
    /// </summary>
    /// <param name="exception">异常信息</param>
    /// <returns>修复建议列表</returns>
    private static List<string> GetFixSuggestions(Exception exception)
    {
        var suggestions = new List<string>();

        switch (exception)
        {
            case ArgumentNullException:
                suggestions.Add("检查是否有 null 值传递给不允许为 null 的参数");
                suggestions.Add("验证 YService 属性的参数是否正确设置");
                break;

            case InvalidOperationException:
                suggestions.Add("检查服务类的配置是否正确");
                suggestions.Add("确认类是否实现了必要的接口");
                break;

            case NotSupportedException:
                suggestions.Add("检查是否使用了不支持的语言特性");
                suggestions.Add("验证泛型约束是否过于复杂");
                break;

            case System.IO.FileNotFoundException:
                suggestions.Add("检查项目引用是否正确");
                suggestions.Add("确认 YService NuGet 包是否正确安装");
                break;

            default:
                suggestions.Add("检查错误消息中的具体提示");
                suggestions.Add("查看堆栈跟踪定位问题源头");
                break;
        }

        return suggestions;
    }

    /// <summary>
    /// 清理程序集名称
    /// </summary>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>清理后的名称</returns>
    private static string SanitizeAssemblyName(string assemblyName)
    {
        return assemblyName.Replace(".", "").Replace("-", "").Replace(" ", "");
    }

    #endregion

    #region 🚨 备用错误处理

    /// <summary>
    /// 生成备用错误报告（当错误报告生成器本身出错时）
    /// </summary>
    /// <param name="originalException">原始异常</param>
    /// <param name="reportException">报告生成异常</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>备用错误报告内容</returns>
    private static string GenerateFallbackErrorReport(Exception originalException, Exception reportException, string assemblyName)
    {
        var sb = new StringBuilder();

        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 备用错误报告")
          .YAppendLine(I0, $"// 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .AppendLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, "严重错误：连错误报告都无法生成")
          .AppendLine()
          .YAppendLine(I0, "原始错误:")
          .YAppendLine(I0, $"  类型: {originalException.GetType().Name}")
          .YAppendLine(I0, $"  消息: {originalException.Message}")
          .AppendLine()
          .YAppendLine(I0, "报告生成错误:")
          .YAppendLine(I0, $"  类型: {reportException.GetType().Name}")
          .YAppendLine(I0, $"  消息: {reportException.Message}")
          .AppendLine()
          .YAppendLine(I0, "请联系技术支持并提供此信息。")
          .YAppendLine(I0, "*/");

        return sb.ToString();
    }

    #endregion
}
