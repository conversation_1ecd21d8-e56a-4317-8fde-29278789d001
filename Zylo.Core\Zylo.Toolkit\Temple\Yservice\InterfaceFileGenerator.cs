using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;


/// <summary>
/// 接口文件生成器 - 专门负责生成接口文件
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成接口文件
/// 📄 生成内容：
/// 1. 接口定义（含完整 XML 文档注释）
/// 2. partial 类实现关系（非静态类）
/// 3. 静态类包装器（静态类专用）
/// </remarks>
public static class InterfaceFileGenerator
{
    #region 🎯 主要生成方法

    /// <summary>
    /// 生成接口文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="service">服务信息</param>
    public static void Generate(SourceProductionContext context, YServiceInfo service)
    {
        if (!service.GenerateInterface)
        {
            return; // 不需要生成接口
        }

        try
        {
            // 🔧 生成接口文件内容
            var fileContent = GenerateInterfaceFileContent(service);

            // 📄 输出到文件
            var fileName = $"{service.InterfaceName}.YService.yg.cs";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            // 🚨 生成错误时，输出错误信息到注释
            var errorContent = GenerateErrorContent(service, ex);
            var fileName = $"{service.InterfaceName}.YService.error.yg.cs";
            context.AddSource(fileName, SourceText.From(errorContent, Encoding.UTF8));
        }
    }

    #endregion

    #region 🔧 内容生成方法

    /// <summary>
    /// 生成接口文件的完整内容
    /// </summary>
    /// <param name="service">服务信息</param>
    /// <returns>生成的代码内容</returns>
    private static string GenerateInterfaceFileContent(YServiceInfo service)
    {
        var sb = new StringBuilder();

        // 🔧 第一步：文件头部
        GenerateFileHeader(sb, service);

        // 🔧 第二步：命名空间开始
        GenerateNamespaceStart(sb, service);

        // 🔧 第三步：接口定义（包含所有方法：实例方法和静态方法）
        GenerateInterfaceDefinition(sb, service);

        // 🔧 第四步：实现关系（非静态类）或包装器（静态类）
        if (service.IsStaticClass)
        {
            GenerateStaticWrapper(sb, service); // 静态类生成静态包装器
        }
        else
        {
            // 🚀 v1.2新增：检查是否有静态方法
            var hasStaticMethods = service.Methods.Any(m => m.StaticLifetime != null);
            if (hasStaticMethods)
            {
                // 有静态方法时，只生成混合包装器（包含所有方法）
                GenerateMixedWrapper(sb, service); // v1.2新增方法
            }
            else
            {
                // 没有静态方法时，生成 partial 实现关系
                GeneratePartialImplementation(sb, service);// 生成 partial 实现关系
            }
        }

        // 🔧 第五步：命名空间结束
        GenerateNamespaceEnd(sb);

        return sb.ToString();
    }

    /// <summary>
    /// 生成文件头部注释
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateFileHeader(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// 此文件由 YService 自动生成，请勿手动修改")
          .YAppendLine(I0, $"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 源类: {service.ClassName}")
          .YAppendLine(I0, $"// 生命周期: {service.Lifetime}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "using Microsoft.Extensions.DependencyInjection;")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成命名空间开始
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateNamespaceStart(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendLine($"namespace {service.Namespace};")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成接口定义
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateInterfaceDefinition(StringBuilder sb, YServiceInfo service)
    {
        // 🔧 接口文档注释
        GenerateInterfaceDocumentation(sb, service);

        // 🔧 接口声明
        sb.YAppendLine(I0, $"public interface {service.InterfaceName}")
          .YAppendLine(I0, "{");

        // 🔧 接口方法（包含所有方法：实例方法和静态方法）
        foreach (var method in service.Methods)
        {
            GenerateInterfaceMethod(sb, method);
        }

        sb.YAppendLine(I0, "}");
    }

    /// <summary>
    /// 生成接口文档注释
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateInterfaceDocumentation(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendLine(I0, "/// <summary>");

        if (!string.IsNullOrEmpty(service.Description))
        {
            sb.YAppendLine(I0, $"/// {service.Description}");
        }
        else
        {
            sb.YAppendLine(I0, $"/// {service.ClassName} 的服务接口");
        }

        sb.YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, "/// <remarks>")
          .YAppendLine(I0, $"/// 此接口由 YService 自动生成，基于 {service.ClassName} 类")
          .YAppendLine(I0, $"/// 生命周期: {service.Lifetime}")
          .YAppendLine(I0, $"/// 生成方式: {(service.IsMethodLevelTriggered ? "方法级属性" : "类级属性")}")
          .YAppendLine(I0, "/// </remarks>");
    }

    /// <summary>
    /// 生成接口方法
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="method">方法信息</param>
    private static void GenerateInterfaceMethod(StringBuilder sb, MethodInfo method)
    {
        // 🔧 方法文档注释
        if (!string.IsNullOrEmpty(method.XmlDocumentation))
        {
            // 处理多行文档注释
            var docLines = method.XmlDocumentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                sb.YAppendLine(I1, line.Trim());
            }
        }

        // 🔧 方法签名
        var signature = $"{method.ReturnType} {method.Name}{method.TypeParameters}({method.Parameters})";
        if (!string.IsNullOrEmpty(method.Constraints))
        {
            signature += $" {method.Constraints}";
        }
        sb.YAppendLine(I1, $"{signature};");
        sb.YAppendEmptyLine();
    }

    /// <summary>
    /// 生成静态类包装器
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateStaticWrapper(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendEmptyLine()
          .YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, $"/// {service.ClassName} 的静态包装器实现")
          .YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, $"public class {service.ClassName}Wrapper : {service.InterfaceName}")
          .YAppendLine(I0, "{");

        foreach (var method in service.Methods)
        {
            GenerateWrapperMethod(sb, method, service);
        }

        sb.YAppendLine(I0, "}");
    }

    /// <summary>
    /// 生成包装器方法
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="method">方法信息</param>
    /// <param name="service">服务信息</param>
    private static void GenerateWrapperMethod(StringBuilder sb, MethodInfo method, YServiceInfo service)
    {
        // 🔧 生成方法签名
        var signature = $"{method.ReturnType} {method.Name}{method.TypeParameters}({method.Parameters})";
        if (!string.IsNullOrEmpty(method.Constraints))
        {
            signature += $" {method.Constraints}";
        }

        // 🔧 提取参数名称用于调用
        var parameterNames = ExtractParameterNames(method.Parameters);

        // 🔧 生成方法文档注释（如果有）
        if (!string.IsNullOrEmpty(method.XmlDocumentation))
        {
            var docLines = method.XmlDocumentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                sb.YAppendLine(I1, line.Trim());
            }
        }

        // 🔧 生成方法实现
        sb.YAppendLine(I1, $"public {signature}")
          .YAppendLine(I1, "{")
          .YAppendLine(I2, $"return {service.ClassName}.{method.Name}({parameterNames});")
          .YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成 partial 类实现关系
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GeneratePartialImplementation(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendEmptyLine()
          .YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, $"/// {service.ClassName} 的接口实现关系")
          .YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, $"public partial class {service.ClassName} : {service.InterfaceName}")
          .YAppendLine(I0, "{")
          .YAppendLine(I1, "// 接口实现由原始类提供")
          .YAppendLine(I0, "}");
    }

    /// <summary>
    /// 生成命名空间结束
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    private static void GenerateNamespaceEnd(StringBuilder sb)
    {
        // C# 10+ 文件作用域命名空间不需要结束大括号
    }

    #endregion

    #region 🔧 辅助方法

    /// <summary>
    /// v1.4 升级：从参数字符串中提取参数名称 - 智能参数处理
    ///
    /// 🎯 核心功能：
    /// 将完整的参数字符串转换为纯参数名称字符串，用于方法调用
    ///
    /// 💡 转换示例：
    /// - 基本参数："string name, int age = 0" → "name, age"
    /// - 复杂泛型："Dictionary<string, int> dict" → "dict"
    /// - 字符串默认值："string separator = \", \"" → "separator"
    /// - 元组参数："(int x, int y) point" → "point"
    /// - ref/out 参数："ref int value, out string result" → "ref value, out result"
    ///
    /// 🔧 v1.4 增强功能：
    /// - 智能参数分割：正确处理泛型、元组、字符串中的逗号
    /// - 复杂参数支持：ref/out/in/params 修饰符
    /// - 字符串安全：正确处理字符串默认值中的特殊字符
    /// </summary>
    /// <param name="parametersString">参数字符串，如 "string name, int age = 0"</param>
    /// <returns>参数名称字符串，如 "name, age"</returns>
    private static string ExtractParameterNames(string parametersString)
    {
        if (string.IsNullOrEmpty(parametersString))
            return "";

        // v1.4 升级：使用智能参数处理逻辑（移植自 YStatic）
        return ExtractParameterNamesAdvanced(parametersString);
    }

    #region v1.4 升级 - 复杂参数处理（移植自 YStatic）

    /// <summary>
    /// v1.4 新增：智能参数名称提取
    ///
    /// 🎯 移植自 YStatic v1.4 的复杂参数处理逻辑
    /// 支持泛型、元组、字符串默认值、ref/out 参数等复杂情况
    /// </summary>
    /// <param name="parametersString">参数字符串</param>
    /// <returns>调用参数字符串</returns>
    private static string ExtractParameterNamesAdvanced(string parametersString)
    {
        if (string.IsNullOrEmpty(parametersString))
            return "";

        // 智能分割参数
        var parameters = SplitParametersSmartly(parametersString);
        var callParameters = new List<string>();

        foreach (var param in parameters)
        {
            var trimmed = param.Trim();
            if (string.IsNullOrEmpty(trimmed))
                continue;

            // 解析参数修饰符和名称
            var parameterCall = ExtractParameterCallSyntax(trimmed);
            if (!string.IsNullOrEmpty(parameterCall))
            {
                callParameters.Add(parameterCall);
            }
        }

        return string.Join(", ", callParameters);
    }

    /// <summary>
    /// v1.4 新增：智能分割参数字符串
    ///
    /// 🎯 解决问题：
    /// 处理泛型参数中的逗号，如 "Dictionary<string, int> dict, params object[] args"
    /// 处理字符串默认值中的逗号，如 "string separator = \", \""
    /// 处理元组参数中的逗号，如 "(int x, int y) point"
    /// </summary>
    /// <param name="parametersString">参数字符串</param>
    /// <returns>正确分割的参数列表</returns>
    private static List<string> SplitParametersSmartly(string parametersString)
    {
        var parameters = new List<string>();
        var current = new StringBuilder();
        var bracketDepth = 0;
        var angleDepth = 0;
        var parenDepth = 0;
        var inString = false;
        var stringChar = '\0';
        var escapeNext = false;

        for (int i = 0; i < parametersString.Length; i++)
        {
            char c = parametersString[i];

            if (escapeNext)
            {
                escapeNext = false;
                current.Append(c);
                continue;
            }

            if (!inString)
            {
                switch (c)
                {
                    case '"':
                    case '\'':
                        inString = true;
                        stringChar = c;
                        current.Append(c);
                        break;
                    case '<':
                        angleDepth++;
                        current.Append(c);
                        break;
                    case '>':
                        angleDepth--;
                        current.Append(c);
                        break;
                    case '[':
                        bracketDepth++;
                        current.Append(c);
                        break;
                    case ']':
                        bracketDepth--;
                        current.Append(c);
                        break;
                    case '(':
                        parenDepth++;
                        current.Append(c);
                        break;
                    case ')':
                        parenDepth--;
                        current.Append(c);
                        break;
                    case ',':
                        if (bracketDepth == 0 && angleDepth == 0 && parenDepth == 0)
                        {
                            parameters.Add(current.ToString());
                            current.Clear();
                        }
                        else
                        {
                            current.Append(c);
                        }
                        break;
                    default:
                        current.Append(c);
                        break;
                }
            }
            else
            {
                if (c == '\\')
                {
                    escapeNext = true;
                }
                else if (c == stringChar)
                {
                    inString = false;
                    stringChar = '\0';
                }
                current.Append(c);
            }
        }

        if (current.Length > 0)
        {
            parameters.Add(current.ToString());
        }

        return parameters;
    }

    /// <summary>
    /// v1.4 新增：提取参数调用语法
    ///
    /// 🎯 核心功能：
    /// 将参数定义转换为调用语法，正确处理修饰符
    ///
    /// 💡 转换示例：
    /// - "ref int value" → "ref value"
    /// - "out string result" → "out result"
    /// - "params string[] items" → "items"
    /// - "int count = 10" → "count"
    /// - "string separator = \", \"" → "separator"
    /// </summary>
    /// <param name="parameterDefinition">参数定义字符串</param>
    /// <returns>调用语法字符串</returns>
    private static string ExtractParameterCallSyntax(string parameterDefinition)
    {
        var trimmed = parameterDefinition.Trim();
        if (string.IsNullOrEmpty(trimmed))
            return "";

        // 移除默认值部分
        var equalIndex = trimmed.IndexOf('=');
        if (equalIndex > 0)
        {
            trimmed = trimmed.Substring(0, equalIndex).Trim();
        }

        // 分割为单词
        var parts = trimmed.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length < 2)
            return "";

        // 检查修饰符
        var hasRefModifier = parts[0] == "ref";
        var hasOutModifier = parts[0] == "out";
        var hasInModifier = parts[0] == "in";
        var hasParamsModifier = parts[0] == "params";

        // 提取参数名（最后一个部分）
        var paramName = parts[parts.Length - 1];

        // 组合调用语法
        if (hasRefModifier)
            return $"ref {paramName}";
        else if (hasOutModifier)
            return $"out {paramName}";
        else if (hasInModifier)
            return $"in {paramName}";
        else
            return paramName; // params 修饰符在调用时不需要
    }

    #endregion

    #endregion

    #region 🚨 错误处理

    /// <summary>
    /// 生成错误内容
    /// </summary>
    /// <param name="service">服务信息</param>
    /// <param name="exception">异常信息</param>
    /// <returns>错误内容</returns>
    private static string GenerateErrorContent(YServiceInfo service, Exception exception)
    {
        var sb = new StringBuilder();

        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 接口生成失败")
          .YAppendLine(I0, $"// 错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 源类: {service.ClassName}")
          .YAppendLine(I0, $"// 错误信息: {exception.Message}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, "生成接口时发生错误：")
          .YAppendLine(I0, $"类名: {service.ClassName}")
          .YAppendLine(I0, $"命名空间: {service.Namespace}")
          .YAppendLine(I0, $"接口名: {service.InterfaceName}")
          .YAppendLine(I0, $"错误详情: {exception}")
          .YAppendLine(I0, "*/");

        return sb.ToString();
    }

    #endregion

    #region v1.2新增功能-混合类包装器

    /// <summary>
    /// 生成混合类包装器 - v1.2新增功能
    /// 用于处理既有实例方法又有静态方法的类
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateMixedWrapper(StringBuilder sb, YServiceInfo service)
    {
        sb.YAppendEmptyLine()
          .YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, $"/// {service.ClassName} 的混合包装器实现 - v1.2新增功能")
          .YAppendLine(I0, "/// 支持实例方法和静态方法的统一包装")
          .YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, $"public class {service.ClassName}Wrapper : {service.InterfaceName}")
          .YAppendLine(I0, "{");

        // 🔧 生成私有实例字段
        sb.YAppendLine(I1, $"private readonly {service.ClassName} _instance = new {service.ClassName}();")
          .YAppendEmptyLine();

        // 🔧 生成所有方法（委托给实例）
        foreach (var method in service.Methods)
        {
            GenerateServiceMethod(sb, service, method);
        }

        sb.YAppendLine(I0, "}");
    }

    /// <summary>
    /// 生成Service方法 - v1.2新增功能
    /// 委托给原实例调用
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    /// <param name="method">方法信息</param>
    private static void GenerateServiceMethod(StringBuilder sb, YServiceInfo service, MethodInfo method)
    {
        // 🔧 方法文档注释
        if (!string.IsNullOrEmpty(method.XmlDocumentation))
        {
            var docLines = method.XmlDocumentation.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            foreach (var line in docLines)
            {
                var trimmedLine = line.Trim();
                if (trimmedLine.StartsWith("///"))
                {
                    sb.YAppendLine(I1, trimmedLine);
                }
                else
                {
                    sb.YAppendLine(I1, $"/// {trimmedLine}");
                }
            }
        }

        // 🔧 方法签名（去掉static，直接用public）
        var methodSignature = $"public {method.FullDeclaration}";
        sb.YAppendLine(I1, methodSignature)
          .YAppendLine(I1, "{");

        // 🔧 方法体：根据是否为静态方法选择调用方式
        var parameterNames = ExtractParameterNames(method.Parameters);
        var returnPrefix = method.ReturnType == "void" ? "" : "return ";

        if (!string.IsNullOrEmpty(method.StaticLifetime))
        {
            // 静态方法：直接调用静态方法
            sb.YAppendLine(I2, $"{returnPrefix}{service.ClassName}.{method.Name}({parameterNames});");
        }
        else
        {
            // 实例方法：委托给实例
            sb.YAppendLine(I2, $"{returnPrefix}_instance.{method.Name}({parameterNames});");
        }

        sb.YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    #endregion
}
