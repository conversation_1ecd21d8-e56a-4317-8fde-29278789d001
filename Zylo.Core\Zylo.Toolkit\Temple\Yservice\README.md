# 🏗️ YService 协调器架构 (v1.1)

**新一代模块化代码生成架构** - 提供更好的错误隔离、独立文件生成和完整诊断功能。

## 🎯 架构概述

v1.1 版本引入了全新的**协调器架构**，将原来的单一生成器拆分为多个专用生成器，每个生成器负责单一职责：

```text
YServiceCodeCoordinator (协调器)
├── InterfaceFileGenerator      # 接口生成器
├── ServiceRegistrationGenerator # 服务注册生成器
├── StatisticsGenerator         # 统计生成器
└── ErrorReportGenerator        # 错误报告生成器
```

## ✨ 架构优势

### 🔧 **模块化设计**

- **单一职责**：每个生成器只负责一种类型的代码生成
- **独立运行**：生成器之间相互独立，可以单独运行
- **易于维护**：修改某个生成器不影响其他生成器

### 🛡️ **错误隔离**

- **局部错误**：单个生成器的错误不会影响其他生成器
- **继续生成**：即使某个生成器失败，其他生成器仍能正常工作
- **详细报告**：每个错误都有详细的错误报告

### 📁 **独立文件生成**

- **分离关注点**：不同类型的代码生成到独立的 `.yg.cs` 文件
- **避免冲突**：使用 `.yg.cs` 扩展名避免与其他代码生成器冲突
- **清晰结构**：生成的文件结构清晰，易于理解和调试

### 📊 **完整诊断**

- **生成统计**：详细的生成过程统计信息
- **错误报告**：完整的错误报告和修复建议
- **性能监控**：生成过程的性能监控

## 🔧 组件详解

### 🎯 YServiceCodeCoordinator

**职责**：协调所有生成器的执行

```csharp
public class YServiceCodeCoordinator
{
    // 协调多个生成器
    private readonly InterfaceFileGenerator _interfaceGenerator;
    private readonly ServiceRegistrationGenerator _registrationGenerator;
    private readonly StatisticsGenerator _statisticsGenerator;
    private readonly ErrorReportGenerator _errorGenerator;
}
```

**特点**：

- 不包含具体的生成逻辑
- 只负责协调和分发任务
- 处理生成器之间的依赖关系

### 🔧 InterfaceFileGenerator

**职责**：生成接口定义和实现关系

**生成文件**：`I{ClassName}.YService.yg.cs`

**功能**：

- 生成接口定义
- 生成静态类包装器
- 生成接口实现关系
- 保留 XML 文档注释

### 📋 ServiceRegistrationGenerator

**职责**：生成服务注册扩展方法

**生成文件**：`ServiceRegistration.{Assembly}.yg.cs`

**功能**：

- 生成批量注册方法
- 生成单个服务注册方法
- 处理不同生命周期
- 生成详细的文档注释

### 📊 StatisticsGenerator

**职责**：生成统计信息

**生成文件**：`YServiceStatistics.{Assembly}.yg.cs`

**功能**：

- 基础统计信息
- 详细统计信息
- 服务列表
- 生成时间记录

### 🚨 ErrorReportGenerator

**职责**：生成错误报告

**生成文件**：`YServiceErrorReport.{Assembly}.yg.cs`

**功能**：

- 详细错误信息
- 修复建议
- 联系信息
- 备用错误报告

## 📁 生成的文件

### 接口文件：`I{ClassName}.YService.yg.cs`

```csharp
// 接口定义
public interface IUserService
{
    Task<User> GetUserAsync(int id);
}

// 实现关系（非静态类）
public partial class UserService : IUserService
{
    // 接口实现由原始类提供
}

// 包装器（静态类）
public class UserServiceWrapper : IUserService
{
    public Task<User> GetUserAsync(int id) => UserService.GetUserAsync(id);
}
```

### 注册文件：`ServiceRegistration.{Assembly}.yg.cs`

```csharp
public static class {Assembly}ServiceCollectionExtensions
{
    // 批量注册
    public static IServiceCollection AddAll{Assembly}Services(this IServiceCollection services)
    {
        services.AddUserService();
        // ... 其他服务
        return services;
    }

    // 单个注册
    public static IServiceCollection AddUserService(this IServiceCollection services)
    {
        return services.AddScoped<IUserService, UserService>();
    }
}
```

### 统计文件：`YServiceStatistics.{Assembly}.yg.cs`

```csharp
public static class YServiceGenerationStatistics
{
    public static class Basic
    {
        public const int TotalServices = 5;
        public const int InterfaceServices = 4;
        public const int StaticServices = 1;
        public const string GenerationTime = "2025-01-08 10:30:00";
    }

    public static class Services
    {
        public static readonly string[] ServiceList = new[]
        {
            "IUserService → UserService (Scoped)",
            "IOrderService → OrderService (Scoped)",
            // ...
        };
    }
}
```

### 错误报告文件：`YServiceErrorReport.{Assembly}.yg.cs`（仅在有错误时生成）

```csharp
/*
=================================================================================
YService 代码生成错误报告
=================================================================================

📋 错误摘要:
   错误类型: ArgumentException
   错误消息: 类名不能为空
   发生时间: 2025-01-08 10:30:00

💡 修复建议:
   • 检查类名是否有效
   • 确认命名空间是否正确
   • 验证属性使用是否正确

📞 获取帮助:
   如果问题持续存在，请提供此错误报告并联系:
   • GitHub Issues: https://github.com/zylo-tech/yservice/issues
   • 文档: https://docs.zylo.dev/yservice
   • 邮箱: <EMAIL>
*/
```

## 🎯 使用 YCodeIndentFormatter

所有生成器都使用 `YCodeIndentFormatter` 来确保生成的代码有正确的缩进：

```csharp
using static Zylo.Toolkit.Helper.YCodeIndentFormatter;

// 使用预定义的缩进常量
sb.YAppendLine(I0, "public class UserService")     // 顶级，无缩进
  .YAppendLine(I0, "{")
  .YAppendLine(I1, "public void Method()")          // 类内容，4个空格
  .YAppendLine(I1, "{")
  .YAppendLine(I2, "// 方法内容，8个空格")            // 方法内容，8个空格
  .YAppendLine(I1, "}")
  .YAppendLine(I0, "}");
```

## 🚀 扩展指南

### 添加新的生成器

1. **创建生成器类**：

```csharp
public static class CustomGenerator
{
    public static SourceText Generate(List<YServiceInfo> services, string assemblyName)
    {
        var sb = new StringBuilder();

        // 使用 YCodeIndentFormatter
        sb.YAppendLine(I0, "// 自定义生成的代码")
          .YAppendLine(I0, $"namespace {assemblyName}.Custom;");

        return SourceText.From(sb.ToString(), Encoding.UTF8);
    }
}
```

2. **在协调器中注册**：

```csharp
// 在 YServiceCodeCoordinator 中添加
var customContent = CustomGenerator.Generate(services, assemblyName);
var customFileName = $"Custom.{assemblyName}.yg.cs";
context.AddSource(customFileName, customContent);
```

### 错误处理模式

```csharp
try
{
    var content = SomeGenerator.Generate(services, assemblyName);
    context.AddSource(fileName, content);
}
catch (Exception ex)
{
    // 生成错误报告
    var errorContent = ErrorReportGenerator.GenerateForSpecificError(ex, assemblyName);
    context.AddSource($"Error.{fileName}", errorContent);
}
```

## 🎯 最佳实践

### 1. 文件命名约定

- **接口文件**：`I{ClassName}.YService.yg.cs`
- **注册文件**：`ServiceRegistration.{Assembly}.yg.cs`
- **统计文件**：`YServiceStatistics.{Assembly}.yg.cs`
- **错误文件**：`YServiceErrorReport.{Assembly}.yg.cs`

### 2. 错误处理

- 每个生成器都应该有独立的错误处理
- 错误不应该影响其他生成器的执行
- 提供详细的错误信息和修复建议

### 3. 代码格式化

- 统一使用 `CodeIndentFormatter`
- 使用预定义的缩进常量（I0, I1, I2, I3）
- 保持代码格式的一致性

### 4. 文档注释

- 为生成的代码添加详细的文档注释
- 保留原始类的文档注释
- 添加生成器相关的元信息

## 🔧 调试和诊断

### 查看生成的文件

生成的文件位于：`obj/Generated/Zylo.Service/Zylo.Service.Generators.YServiceGenerator/`

### 常见问题

1. **某个生成器没有生成文件**：检查该生成器的错误处理逻辑
2. **生成的代码格式不正确**：检查 `CodeIndentFormatter` 的使用
3. **错误报告没有生成**：检查 `ErrorReportGenerator` 的调用

### 性能监控

- 每个生成器的执行时间
- 生成的文件大小
- 内存使用情况

---

## 🎯 总结

新的协调器架构提供了：

- **🏗️ 模块化设计**：清晰的职责分离
- **🛡️ 错误隔离**：单点故障不影响整体
- **📁 独立文件**：清晰的文件结构
- **📊 完整诊断**：详细的统计和错误报告
- **🔧 易于扩展**：简单的扩展机制

这个架构为 YService 的未来发展奠定了坚实的基础，同时保持了向后兼容性。
