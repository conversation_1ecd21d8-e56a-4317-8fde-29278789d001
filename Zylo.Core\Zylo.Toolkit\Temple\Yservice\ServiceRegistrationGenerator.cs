using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;

namespace Zylo.Toolkit.Temple.Yservice;

/// <summary>
/// 服务注册生成器 - 专门负责生成服务注册代码
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成服务注册文件
/// 📄 生成内容：
/// 1. 批量注册方法（AddAllXxxServices）
/// 2. 单个服务注册方法（AddXxxService）
/// 3. 支持所有生命周期（Singleton/Scoped/Transient）
/// </remarks>
public static class ServiceRegistrationGenerator
{
    #region 🎯 主要生成方法

    /// <summary>
    /// 生成服务注册文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void Generate(SourceProductionContext context, List<YServiceInfo> services, string assemblyName)
    {
        if (!services.Any())
        {
            return; // 没有服务需要注册
        }

        try
        {
            // 🔧 生成注册文件内容
            var fileContent = GenerateRegistrationFileContent(services, assemblyName);

            // 📄 输出到文件
            var fileName = $"ServiceRegistration.{SanitizeAssemblyName(assemblyName)}.YService.yg.cs";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            // 🚨 生成错误时，输出错误信息
            var errorContent = GenerateErrorContent(services, assemblyName, ex);
            var fileName = $"ServiceRegistration.{SanitizeAssemblyName(assemblyName)}.YService.error.yg.cs";
            context.AddSource(fileName, SourceText.From(errorContent, Encoding.UTF8));
        }
    }

    #endregion

    #region 🔧 内容生成方法

    /// <summary>
    /// 生成注册文件的完整内容
    /// </summary>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>生成的代码内容</returns>
    private static string GenerateRegistrationFileContent(List<YServiceInfo> services, string assemblyName)
    {
        var sb = new StringBuilder();

        // 🔧 第一步：文件头部
        GenerateFileHeader(sb, services, assemblyName);

        // 🔧 第二步：命名空间和类开始
        GenerateClassStart(sb, assemblyName);

        // 🔧 第三步：批量注册方法
        GenerateBatchRegistrationMethod(sb, services, assemblyName);

        // 🔧 第四步：单个服务注册方法
        foreach (var service in services)
        {
            GenerateIndividualRegistrationMethod(sb, service);
        }

        // 🔧 第五步：类结束
        GenerateClassEnd(sb);

        return sb.ToString();
    }

    /// <summary>
    /// 生成文件头部
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateFileHeader(StringBuilder sb, List<YServiceInfo> services, string assemblyName)
    {
        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// 此文件由 YService 自动生成，请勿手动修改")
          .YAppendLine(I0, $"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .YAppendLine(I0, $"// 服务数量: {services.Count}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "using Microsoft.Extensions.DependencyInjection;")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成类开始
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateClassStart(StringBuilder sb, string assemblyName)
    {
        var sanitizedName = SanitizeAssemblyName(assemblyName);

        sb.YAppendLine(I0, $"namespace Microsoft.Extensions.DependencyInjection;")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, $"/// {assemblyName} 程序集的 YService 服务注册扩展")
          .YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, $"public static class {sanitizedName}ServiceCollectionExtensions")
          .YAppendLine(I0, "{");
    }

    /// <summary>
    /// 生成批量注册方法
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateBatchRegistrationMethod(StringBuilder sb, List<YServiceInfo> services, string assemblyName)
    {
        var sanitizedName = SanitizeAssemblyName(assemblyName);
        var methodName = $"AddAll{sanitizedName}Services";

        // 🔧 方法文档注释
        sb.YAppendLine(I1, "/// <summary>")
          .YAppendLine(I1, $"/// 注册 {assemblyName} 程序集中的所有 YService 服务")
          .YAppendLine(I1, "/// </summary>")
          .YAppendLine(I1, "/// <remarks>")
          .YAppendLine(I1, "/// 🔧 自动注册的服务：");

        foreach (var service in services)
        {
            var serviceDesc = service.GenerateInterface
                ? $"{service.InterfaceName} → {service.ClassName} ({service.Lifetime})"
                : $"{service.ClassName} ({service.Lifetime})";
            sb.YAppendLine(I1, $"/// - {serviceDesc}");
        }

        sb.YAppendLine(I1, "/// </remarks>")
          .YAppendLine(I1, "/// <param name=\"services\">服务集合</param>")
          .YAppendLine(I1, "/// <returns>服务集合（支持链式调用）</returns>")
          .YAppendLine(I1, $"public static IServiceCollection {methodName}(this IServiceCollection services)")
          .YAppendLine(I1, "{");

        // 🔧 调用各个服务的注册方法
        foreach (var service in services)
        {
            sb.YAppendLine(I2, $"services.Add{service.ClassName}();");
        }

        sb.YAppendLine(I2, "return services;")
          .YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成单个服务注册方法
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="service">服务信息</param>
    private static void GenerateIndividualRegistrationMethod(StringBuilder sb, YServiceInfo service)
    {
        var methodName = $"Add{service.ClassName}";
        var lifetimeMethod = GetLifetimeMethod(service.Lifetime);

        // 🔧 方法文档注释
        sb.YAppendLine(I1, "/// <summary>")
          .YAppendLine(I1, $"/// 注册 {service.ClassName} 服务");

        if (!string.IsNullOrEmpty(service.Description))
        {
            sb.YAppendLine(I1, $"/// {service.Description}");
        }

        sb.YAppendLine(I1, "/// </summary>")
          .YAppendLine(I1, "/// <param name=\"services\">服务集合</param>")
          .YAppendLine(I1, "/// <returns>服务集合（支持链式调用）</returns>")
          .YAppendLine(I1, $"public static IServiceCollection {methodName}(this IServiceCollection services)")
          .YAppendLine(I1, "{");

        // 🔧 注册逻辑 - v1.2新增：支持混合类
        if (service.GenerateInterface)
        {
            // 🚀 v1.2新增：检查是否有静态方法，决定使用哪个实现类
            var hasStaticMethods = service.Methods.Any(m => m.StaticLifetime != null);
            var implementationTypeName = hasStaticMethods
                ? service.FullServiceTypeName.Replace(service.ClassName, $"{service.ClassName}Wrapper")
                : service.FullServiceTypeName;

            sb.YAppendLine(I2, $"services.{lifetimeMethod}<{service.FullInterfaceTypeName}, {implementationTypeName}>();");
        }
        else
        {
            sb.YAppendLine(I2, $"services.{lifetimeMethod}<{service.FullServiceTypeName}>();");
        }

        sb.YAppendLine(I2, "return services;")
          .YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成类结束
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    private static void GenerateClassEnd(StringBuilder sb)
    {
        sb.YAppendLine(I0, "}");
    }

    #endregion

    #region 🔧 辅助方法

    /// <summary>
    /// 获取生命周期对应的注册方法名
    /// </summary>
    /// <param name="lifetime">生命周期</param>
    /// <returns>注册方法名</returns>
    private static string GetLifetimeMethod(string lifetime)
    {
        return lifetime switch
        {
            "Singleton" => "AddSingleton",
            "Scoped" => "AddScoped",
            "Transient" => "AddTransient",
            _ => "AddScoped" // 默认为 Scoped
        };
    }

    /// <summary>
    /// 清理程序集名称，使其适合作为类名
    /// </summary>
    /// <param name="assemblyName">原始程序集名称</param>
    /// <returns>清理后的名称</returns>
    private static string SanitizeAssemblyName(string assemblyName)
    {
        return assemblyName.Replace(".", "").Replace("-", "").Replace(" ", "");
    }

    #endregion

    #region 🚨 错误处理

    /// <summary>
    /// 生成错误内容
    /// </summary>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <param name="exception">异常信息</param>
    /// <returns>错误内容</returns>
    private static string GenerateErrorContent(List<YServiceInfo> services, string assemblyName, Exception exception)
    {
        var sb = new StringBuilder();

        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 服务注册生成失败")
          .YAppendLine(I0, $"// 错误时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .YAppendLine(I0, $"// 服务数量: {services.Count}")
          .YAppendLine(I0, $"// 错误信息: {exception.Message}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, "生成服务注册时发生错误：")
          .YAppendLine(I0, $"程序集: {assemblyName}")
          .YAppendLine(I0, $"服务数量: {services.Count}")
          .YAppendLine(I0, $"错误详情: {exception}")
          .YAppendLine(I0, "*/");

        return sb.ToString();
    }

    #endregion
}
