using System.Text;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Text;
using Zylo.Toolkit.Helper;

namespace Zylo.Toolkit.Temple.Yservice;

/// <summary>
/// 统计信息生成器 - 专门负责生成统计信息文件
/// </summary>
/// <remarks>
/// 🎯 单一职责：只负责生成统计信息
/// 📄 生成内容：
/// 1. 服务统计信息
/// 2. 生成时间和版本信息
/// 3. 性能统计（可选）
/// </remarks>
public static class StatisticsGenerator
{
    #region 🎯 主要生成方法

    /// <summary>
    /// 生成统计信息文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void Generate(SourceProductionContext context, List<YServiceInfo> services, string assemblyName)
    {
        try
        {
            // 🔧 生成统计文件内容
            var fileContent = GenerateStatisticsContent(services, assemblyName);

            // 📄 输出到文件
            var fileName = $"YServiceStatistics.{SanitizeAssemblyName(assemblyName)}.yg.cs";
            context.AddSource(fileName, SourceText.From(fileContent, Encoding.UTF8));
        }
        catch (Exception ex)
        {
            // 🚨 统计生成失败时，输出简化版本
            var errorContent = GenerateSimpleStatistics(services, assemblyName, ex);
            var fileName = $"YServiceStatistics.{SanitizeAssemblyName(assemblyName)}.simple.yg.cs";
            context.AddSource(fileName, SourceText.From(errorContent, Encoding.UTF8));
        }
    }

    #endregion

    #region 🔧 内容生成方法

    /// <summary>
    /// 生成统计信息内容
    /// </summary>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>统计信息内容</returns>
    private static string GenerateStatisticsContent(List<YServiceInfo> services, string assemblyName)
    {
        var sb = new StringBuilder();
        var stats = CalculateStatistics(services);

        // 🔧 文件头部
        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 统计信息")
          .YAppendLine(I0, $"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .YAppendEmptyLine()
          .YAppendLine(I0, $"namespace {assemblyName}.YServiceStatistics;")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/// <summary>")
          .YAppendLine(I0, "/// YService 生成统计信息")
          .YAppendLine(I0, "/// </summary>")
          .YAppendLine(I0, "public static class YServiceGenerationStatistics")
          .YAppendLine(I0, "{");

        // 🔧 基础统计
        GenerateBasicStatistics(sb, stats);

        // 🔧 详细统计
        GenerateDetailedStatistics(sb, services);

        // 🔧 服务列表
        GenerateServiceList(sb, services);

        sb.YAppendLine(I0, "}");

        return sb.ToString();
    }

    /// <summary>
    /// 生成基础统计信息
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="stats">统计数据</param>
    private static void GenerateBasicStatistics(StringBuilder sb, ServiceStatistics stats)
    {
        sb.YAppendLine(I1, "/// <summary>")
          .YAppendLine(I1, "/// 基础统计信息")
          .YAppendLine(I1, "/// </summary>")
          .YAppendLine(I1, "public static class Basic")
          .YAppendLine(I1, "{")
          .YAppendLine(I2, $"/// <summary>服务总数</summary>")
          .YAppendLine(I2, $"public const int TotalServices = {stats.TotalServices};")
          .YAppendLine(I2, $"/// <summary>生成接口的服务数</summary>")
          .YAppendLine(I2, $"public const int InterfaceServices = {stats.InterfaceServices};")
          .YAppendLine(I2, $"/// <summary>静态类服务数</summary>")
          .YAppendLine(I2, $"public const int StaticServices = {stats.StaticServices};")
          .YAppendLine(I2, $"/// <summary>方法级触发的服务数</summary>")
          .YAppendLine(I2, $"public const int MethodLevelServices = {stats.MethodLevelServices};")
          .YAppendLine(I2, $"/// <summary>生成时间</summary>")
          .YAppendLine(I2, $"public const string GenerationTime = \"{DateTime.Now:yyyy-MM-dd HH:mm:ss}\";")
          .YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成详细统计信息
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="services">服务列表</param>
    private static void GenerateDetailedStatistics(StringBuilder sb, List<YServiceInfo> services)
    {
        // 标准化生命周期名称，避免重复
        var lifetimeStats = services.GroupBy(s => NormalizeLifetimeName(s.Lifetime))
                                  .ToDictionary(g => g.Key, g => g.Count());

        sb.YAppendLine(I1, "/// <summary>")
          .YAppendLine(I1, "/// 详细统计信息")
          .YAppendLine(I1, "/// </summary>")
          .YAppendLine(I1, "public static class Detailed")
          .YAppendLine(I1, "{");

        foreach (var (lifetime, count) in lifetimeStats)
        {
            sb.YAppendLine(I2, $"/// <summary>{lifetime} 生命周期服务数</summary>")
              .YAppendLine(I2, $"public const int {lifetime}Services = {count};");
        }

        sb.YAppendLine(I1, "}")
          .YAppendEmptyLine();
    }

    /// <summary>
    /// 生成服务列表
    /// </summary>
    /// <param name="sb">字符串构建器</param>
    /// <param name="services">服务列表</param>
    private static void GenerateServiceList(StringBuilder sb, List<YServiceInfo> services)
    {
        sb.YAppendLine(I1, "/// <summary>")
          .YAppendLine(I1, "/// 所有服务的详细信息")
          .YAppendLine(I1, "/// </summary>")
          .YAppendLine(I1, "public static class Services")
          .YAppendLine(I1, "{")
          .YAppendLine(I2, "/// <summary>服务信息列表</summary>")
          .YAppendLine(I2, "public static readonly string[] ServiceList = new[]")
          .YAppendLine(I2, "{");

        foreach (var service in services)
        {
            var serviceInfo = service.GenerateInterface
                ? $"{service.InterfaceName} → {service.ClassName} ({service.Lifetime})"
                : $"{service.ClassName} ({service.Lifetime})";
            sb.YAppendLine(I3, $"\"{serviceInfo}\",");
        }

        sb.YAppendLine(I2, "};")
          .YAppendLine(I1, "}");
    }

    #endregion

    #region 🔧 统计计算

    /// <summary>
    /// 计算服务统计信息
    /// </summary>
    /// <param name="services">服务列表</param>
    /// <returns>统计信息</returns>
    private static ServiceStatistics CalculateStatistics(List<YServiceInfo> services)
    {
        return new ServiceStatistics
        {
            TotalServices = services.Count,
            InterfaceServices = services.Count(s => s.GenerateInterface),
            StaticServices = services.Count(s => s.IsStaticClass),
            MethodLevelServices = services.Count(s => s.IsMethodLevelTriggered)
        };
    }

    /// <summary>
    /// 清理程序集名称
    /// </summary>
    /// <param name="assemblyName">程序集名称</param>
    /// <returns>清理后的名称</returns>
    private static string SanitizeAssemblyName(string assemblyName)
    {
        return assemblyName.Replace(".", "").Replace("-", "").Replace(" ", "");
    }

    /// <summary>
    /// 标准化生命周期名称，确保一致性
    /// </summary>
    /// <param name="lifetime">生命周期名称</param>
    /// <returns>标准化的生命周期名称</returns>
    private static string NormalizeLifetimeName(string lifetime)
    {
        if (string.IsNullOrEmpty(lifetime))
            return "Unknown";

        // 处理常见的生命周期名称，统一为标准格式
        return lifetime.ToLowerInvariant() switch
        {
            "singleton" => "Singleton",
            "scoped" => "Scoped",
            "transient" => "Transient",
            "0" => "Singleton",  // 枚举值转换
            "1" => "Scoped",     // 枚举值转换
            "2" => "Transient",  // 枚举值转换
            _ => lifetime.Contains("singleton", StringComparison.OrdinalIgnoreCase) ? "Singleton" :
                 lifetime.Contains("scoped", StringComparison.OrdinalIgnoreCase) ? "Scoped" :
                 lifetime.Contains("transient", StringComparison.OrdinalIgnoreCase) ? "Transient" :
                 "Unknown"
        };
    }

    /// <summary>
    /// 清理生命周期名称，确保是有效的 C# 标识符（保留用于向后兼容）
    /// </summary>
    /// <param name="lifetime">生命周期名称</param>
    /// <returns>有效的标识符</returns>
    private static string SanitizeLifetimeName(string lifetime)
    {
        return NormalizeLifetimeName(lifetime);
    }

    #endregion

    #region 🚨 错误处理

    /// <summary>
    /// 生成简化统计信息（错误时使用）
    /// </summary>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    /// <param name="exception">异常信息</param>
    /// <returns>简化统计内容</returns>
    private static string GenerateSimpleStatistics(List<YServiceInfo> services, string assemblyName, Exception exception)
    {
        var sb = new StringBuilder();

        sb.YAppendLine(I0, "// <auto-generated />")
          .YAppendLine(I0, "// YService 简化统计信息（生成时出现错误）")
          .YAppendLine(I0, $"// 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
          .YAppendLine(I0, $"// 程序集: {assemblyName}")
          .YAppendLine(I0, $"// 错误: {exception.Message}")
          .YAppendEmptyLine()
          .YAppendLine(I0, "/*")
          .YAppendLine(I0, $"服务总数: {services.Count}")
          .YAppendLine(I0, $"生成接口的服务: {services.Count(s => s.GenerateInterface)}")
          .YAppendLine(I0, $"静态类服务: {services.Count(s => s.IsStaticClass)}")
          .YAppendLine(I0, $"方法级服务: {services.Count(s => s.IsMethodLevelTriggered)}")
          .YAppendLine(I0, "*/");

        return sb.ToString();
    }

    #endregion
}

/// <summary>
/// 服务统计信息数据结构
/// </summary>
internal record ServiceStatistics
{
    public int TotalServices { get; init; }
    public int InterfaceServices { get; init; }
    public int StaticServices { get; init; }
    public int MethodLevelServices { get; init; }
}
