using Microsoft.CodeAnalysis;

namespace Zylo.Toolkit.Temple.Yservice;

/// <summary>
/// YService 代码生成协调器 - 真正的协调器
/// </summary>
/// <remarks>
/// 🎯 核心职责：
/// 1. 协调各个专门的代码生成器
/// 2. 控制代码生成流程
/// 3. 处理生成过程中的异常和错误
/// 4. 管理生成器之间的依赖关系
/// 
/// 📋 设计原则：
/// - 单一职责：只负责协调，不负责具体生成
/// - 开放封闭：易于扩展新的生成器
/// - 依赖倒置：依赖抽象的生成器接口
/// - 接口隔离：每个生成器只关心自己的职责
/// </remarks>
public static class YServiceCodeCoordinator
{
    #region 🎯 主要协调方法

    /// <summary>
    /// 执行完整的代码生成流程
    /// </summary>
    /// <param name="context">源代码生成上下文</param>
    /// <param name="services">服务信息列表</param>
    /// <param name="assemblyName">程序集名称</param>
    public static void ExecuteGeneration(
        SourceProductionContext context, 
        IEnumerable<YServiceInfo> services,
        string assemblyName)
    {
        try
        {
            // 🔍 第一步：数据预处理
            var serviceList = PreprocessServices(services);
            
            if (!serviceList.Any())
            {
                // 没有服务需要生成，直接返回
                return;
            }

            // 🔧 第二步：生成接口文件
            GenerateInterfaceFiles(context, serviceList);

            // 📋 第三步：生成服务注册文件
            GenerateServiceRegistrationFile(context, serviceList, assemblyName);

            // 📊 第四步：生成统计信息（可选）
            GenerateStatisticsFile(context, serviceList, assemblyName);
        }
        catch (Exception ex)
        {
            // 🚨 错误处理：生成错误报告
            GenerateErrorReport(context, ex, assemblyName);
        }
    }

    #endregion

    #region 🔧 协调流程方法

    /// <summary>
    /// 预处理服务信息
    /// </summary>
    /// <param name="services">原始服务信息</param>
    /// <returns>处理后的服务列表</returns>
    private static List<YServiceInfo> PreprocessServices(IEnumerable<YServiceInfo> services)
    {
        var serviceList = services.ToList();
        
        // 🔍 数据验证
        ValidateServices(serviceList);
        
        // 📊 数据统计
        LogGenerationStatistics(serviceList);
        
        return serviceList;
    }

    /// <summary>
    /// 生成所有接口文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务列表</param>
    private static void GenerateInterfaceFiles(SourceProductionContext context, List<YServiceInfo> services)
    {
        foreach (var service in services)
        {
            if (service.GenerateInterface)
            {
                // 🎯 委托给专门的接口生成器
                InterfaceFileGenerator.Generate(context, service);
            }
        }
    }

    /// <summary>
    /// 生成服务注册文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateServiceRegistrationFile(
        SourceProductionContext context, 
        List<YServiceInfo> services, 
        string assemblyName)
    {
        // 🎯 委托给专门的注册生成器
        ServiceRegistrationGenerator.Generate(context, services, assemblyName);
    }

    /// <summary>
    /// 生成统计信息文件（可选功能）
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务列表</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateStatisticsFile(
        SourceProductionContext context, 
        List<YServiceInfo> services, 
        string assemblyName)
    {
        // 🎯 委托给专门的统计生成器
        StatisticsGenerator.Generate(context, services, assemblyName);
    }

    #endregion

    #region 🔍 验证和统计方法

    /// <summary>
    /// 验证服务信息的有效性
    /// </summary>
    /// <param name="services">服务列表</param>
    private static void ValidateServices(List<YServiceInfo> services)
    {
        foreach (var service in services)
        {
            // 验证服务信息的完整性
            if (string.IsNullOrEmpty(service.ClassName))
            {
                throw new InvalidOperationException($"服务类名不能为空");
            }

            if (string.IsNullOrEmpty(service.Namespace))
            {
                throw new InvalidOperationException($"服务命名空间不能为空：{service.ClassName}");
            }

            // 验证接口生成配置
            if (service.GenerateInterface && string.IsNullOrEmpty(service.InterfaceName))
            {
                throw new InvalidOperationException($"需要生成接口但接口名为空：{service.ClassName}");
            }
        }
    }

    /// <summary>
    /// 记录生成统计信息
    /// </summary>
    /// <param name="services">服务列表</param>
    private static void LogGenerationStatistics(List<YServiceInfo> services)
    {
        var totalServices = services.Count;
        var interfaceServices = services.Count(s => s.GenerateInterface);
        var staticServices = services.Count(s => s.IsStaticClass);
        var methodLevelServices = services.Count(s => s.IsMethodLevelTriggered);

        // 这里可以输出到诊断或日志
        // 目前只是内部统计，不输出到生成的代码中
    }

    #endregion

    #region 🚨 错误处理方法

    /// <summary>
    /// 生成错误报告文件
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="exception">异常信息</param>
    /// <param name="assemblyName">程序集名称</param>
    private static void GenerateErrorReport(
        SourceProductionContext context, 
        Exception exception, 
        string assemblyName)
    {
        // 🎯 委托给专门的错误报告生成器
        ErrorReportGenerator.Generate(context, exception, assemblyName);
    }

    #endregion

    #region 🔧 扩展点

    /// <summary>
    /// 注册自定义生成器
    /// </summary>
    /// <param name="generator">自定义生成器</param>
    public static void RegisterCustomGenerator(IYServiceGenerator generator)
    {
        // 为将来扩展预留的接口
        // 可以支持插件式的生成器扩展
    }

    /// <summary>
    /// 获取支持的生成器列表
    /// </summary>
    /// <returns>生成器列表</returns>
    public static IEnumerable<string> GetSupportedGenerators()
    {
        return new[]
        {
            "InterfaceFileGenerator",
            "ServiceRegistrationGenerator", 
            "StatisticsGenerator",
            "ErrorReportGenerator"
        };
    }

    #endregion
}

/// <summary>
/// YService 生成器接口 - 为将来扩展预留
/// </summary>
public interface IYServiceGenerator
{
    /// <summary>
    /// 生成器名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 生成代码
    /// </summary>
    /// <param name="context">生成上下文</param>
    /// <param name="services">服务信息</param>
    /// <param name="assemblyName">程序集名称</param>
    void Generate(SourceProductionContext context, IEnumerable<YServiceInfo> services, string assemblyName);
}
