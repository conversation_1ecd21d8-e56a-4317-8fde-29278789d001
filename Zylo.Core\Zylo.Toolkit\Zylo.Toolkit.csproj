<Project Sdk="Microsoft.NET.Sdk">  <!-- 使用标准 .NET SDK 项目格式 -->

  <!-- ========== 基本项目信息 ========== -->
  <PropertyGroup>
    <!-- 基本配置 - 使用 Directory.Build.props 中的全局配置 -->
    <LangVersion>13</LangVersion>                                    <!-- 使用 C# 13 语言版本，支持最新语法特性 -->

    <!-- 🔥 关键：混合架构 - 既包含程序集又包含源代码生成器 -->
    <IncludeBuildOutput>true</IncludeBuildOutput>                    <!-- 将编译输出包含到 NuGet 包的 lib 目录中 -->
    <IsRoslynComponent>true</IsRoslynComponent>                      <!-- 标记为 Roslyn 组件，启用源代码生成器功能 -->
    <EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules> <!-- 强制执行扩展分析器规则，确保生成器稳定性 -->

    <!-- NuGet 包信息 -->
    <PackageId>Zylo.Toolkit</PackageId>                              <!-- NuGet 包的唯一标识符 -->
    <Description>🚀 Zylo.Toolkit v1.3.4 - 企业级代码生成工具包，支持依赖注入和静态方法生成，像 CommunityToolkit 一样简单易用</Description>
    <PackageTags>dependency-injection;source-generator;zylo;service;static-methods;code-generation</PackageTags>
    <PackageProjectUrl>https://github.com/zylo/toolkit</PackageProjectUrl>

    <!-- 符号包 -->
    <IncludeSymbols>true</IncludeSymbols>                            <!-- 包含调试符号文件 -->
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>                <!-- 使用新的符号包格式 -->

    <!-- 🔥 关键：混合包配置 - 既有运行时库又有源代码生成器 -->
    <DevelopmentDependency>false</DevelopmentDependency>
    <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
    <Title>Zylo.Toolkit</Title>             <!-- 明确设置为 false，确保运行时库被包含 -->
  </PropertyGroup>

  <!-- ========== 依赖包 ========== -->
  <ItemGroup>
    <!-- 源代码生成器依赖 - 更新到最新稳定版本 -->
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.8.0" PrivateAssets="all" /><!--
    **Roslyn C# 编译器 API，用于源代码生成 -->

    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.11.0"
      PrivateAssets="all" /><!--
    **分析器基础设施，PrivateAssets="all"表示不传递给使用者 -->


    <!-- 依赖注入支持 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"
      Version="6.0.0" /><!-- **微软依赖注入抽象接口，提供 IServiceCollection 等 -->

  </ItemGroup>

  <!-- ========== NuGet 包文件配置 ========== -->
  <ItemGroup>
    <!-- README 文件 -->
    <None Include="README.md" Pack="true" PackagePath="\" /><!-- **将 README.md 包含到包根目录，提供使用说明 -->

    <None Include="build\README.md" Pack="true" PackagePath="docs\" /><!-- **将构建配置说明包含到 docs 目录 -->


    <!-- 构建文件 -->
    <None Include="build\Zylo.Toolkit.props" Pack="true" PackagePath="build\" /> <!--
    **MSBuild属性文件，直接引用时生效 -->

    <None Include="build\Zylo.Toolkit.targets" Pack="true" PackagePath="build\" /><!--
    **MSBuild目标文件，直接引用时生效 -->

    <None Include="build\Zylo.Toolkit.props" Pack="true" PackagePath="buildTransitive\" /><!--
    **传递性属性文件，间接引用时也生效 -->

    <None Include="build\Zylo.Toolkit.targets" Pack="true" PackagePath="buildTransitive\" /><!--
    **传递性目标文件，间接引用时也生效 -->

  </ItemGroup>

  <!-- ========== 源代码生成器打包 ========== -->
  <!-- 🔥 最直接的方法：无条件包含 -->
  <ItemGroup>
    <None Include="bin\Release\net8.0\Zylo.Toolkit.dll" Pack="true"
      PackagePath="analyzers\dotnet\cs\Zylo.Toolkit.dll" Visible="false" />
  </ItemGroup>

  <!-- ========== 编译器常量 ========== -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DefineConstants>$(DefineConstants);ZYLO_TOOLKIT_DEBUG</DefineConstants>
  </PropertyGroup>

</Project>