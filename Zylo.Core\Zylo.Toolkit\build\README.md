# Zylo.Service 构建配置说明

## 📋 概述

本文档解释了 Zylo.Service 项目中各种构建配置的设计原理和实现细节。

## 🎯 设计目标

**像 CommunityToolkit 一样的零配置体验**：

- ✅ 引用即用，无需复杂设置
- ✅ 自动代码生成
- ✅ 广泛的框架兼容性
- ✅ 优秀的开发体验

## 🏗️ 架构设计

### **混合架构模式**

```xml
<IncludeBuildOutput>true</IncludeBuildOutput>                    <!-- 程序集功能 -->
<IsRoslynComponent>true</IsRoslynComponent>                      <!-- 源代码生成器功能 -->
<EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules> <!-- 分析器稳定性 -->
```

**为什么选择混合架构？**

1. **程序集部分**：提供属性类型和扩展方法
   - `[Service]` 属性
   - `ServiceLifetime` 枚举
   - `AddYServices()` 扩展方法

2. **源代码生成器部分**：自动生成代码
   - 接口定义：`IUserService`
   - 接口实现：`UserService : IUserService`
   - 注册扩展：`AddAllXxxServices()`

### **双重注册机制**

**主要方式**：源代码生成器自动生成

```csharp
// 自动生成的扩展方法（理想情况）
services.AddAllZyloServiceTestServices();
```

**备用方式**：手动扩展方法

```csharp
// 手动备用方法（保险机制）
services.AddYServices();
```

**为什么需要备用方式？**

1. **容错性**：源代码生成器可能在某些环境下失败
   - IDE 缓存问题
   - 构建环境差异
   - 分析器加载失败

2. **调试支持**：便于排查注册问题
   - 可以验证哪些服务被发现
   - 提供运行时反射扫描
   - 帮助诊断生成器问题

3. **向后兼容**：提供传统的手动注册方式
   - 不依赖源代码生成
   - 适用于所有 .NET 环境
   - 降低技术门槛

4. **开发体验**：确保在任何情况下都能工作
   - 新手友好的备用方案
   - 渐进式采用策略
   - 减少学习成本

## 🎯 多目标框架策略

### **程序集：支持多框架**

```xml
<TargetFrameworks>net6.0;net8.0</TargetFrameworks>
```

**结果**：

- `lib/net6.0/Zylo.Service.dll` - 给 .NET 6.0 项目使用
- `lib/net8.0/Zylo.Service.dll` - 给 .NET 8.0 项目使用

### **源代码生成器：向下兼容策略**

```xml
<ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
  <None Include="$(OutputPath)$(TargetFramework)\$(AssemblyName).dll" Pack="true" 
        PackagePath="analyzers\dotnet\cs\$(AssemblyName).dll" Visible="false" />
</ItemGroup>
```

**为什么只打包 net8.0 版本的生成器？**

1. **向下兼容性**：net8.0 编译的源代码生成器可以在 net6.0 项目中正常工作
2. **Roslyn API 稳定性**：分析器 API 向后兼容
3. **包大小优化**：避免重复打包相同功能的文件
4. **CommunityToolkit 的做法**：业界标准实践

## 📦 NuGet 包结构设计

### **传递性构建文件**

```xml
<None Include="build\Zylo.Service.props" Pack="true" PackagePath="build\" />
<None Include="build\Zylo.Service.props" Pack="true" PackagePath="buildTransitive\" />
```

**为什么需要 buildTransitive？**

- **build/**：只在直接引用时生效
- **buildTransitive/**：在间接引用时也生效
- **场景**：A 项目引用 B 项目，B 项目引用 Zylo.Service，A 项目也能使用源代码生成功能

### **分析器目录结构**

```
analyzers/dotnet/cs/
├── Zylo.Service.dll  # 源代码生成器
└── Zylo.Service.pdb  # 调试符号
```

**为什么这样组织？**

- **analyzers/**：MSBuild 识别的分析器根目录
- **dotnet/cs/**：.NET C# 项目专用
- **包含 PDB**：便于调试源代码生成器

## 🔧 依赖管理策略

### **私有依赖**

```xml
<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.5.0" PrivateAssets="all" />
```

**PrivateAssets="all" 的作用**：

- 依赖不会传递给使用者
- 避免版本冲突
- 减少用户项目的依赖复杂度

### **公共依赖**

```xml
<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
```

**为什么不设置 PrivateAssets？**

- 用户代码需要使用 `IServiceCollection`
- 这是必要的公共 API 依赖

## 🐛 调试支持

### **调试常量**

```xml
<PropertyGroup Condition="'$(Configuration)' == 'Debug'">
  <DefineConstants>$(DefineConstants);ZYLO_SERVICE_DEBUG</DefineConstants>
</PropertyGroup>
```

**用途**：

```csharp
#if ZYLO_SERVICE_DEBUG
    // 调试代码
    Console.WriteLine("生成器正在运行...");
#endif
```

### **符号包**

```xml
<IncludeSymbols>true</IncludeSymbols>
<SymbolPackageFormat>snupkg</SymbolPackageFormat>
```

**好处**：

- 支持源代码调试
- 更好的错误堆栈信息
- 便于问题排查

## 🚀 最佳实践总结

1. **混合架构**：程序集 + 源代码生成器，提供完整功能
2. **多目标框架**：广泛兼容性，覆盖主流 .NET 版本
3. **向下兼容**：使用最新版本生成器，兼容旧版本项目
4. **传递性构建**：确保间接引用也能正常工作
5. **私有依赖**：避免依赖污染，简化用户体验
6. **调试友好**：完整的调试支持和错误诊断

## 📚 参考资料

- [CommunityToolkit.Mvvm 源码](https://github.com/CommunityToolkit/dotnet)
- [.NET 源代码生成器文档](https://docs.microsoft.com/en-us/dotnet/csharp/roslyn-sdk/source-generators-overview)
- [NuGet 包创建最佳实践](https://docs.microsoft.com/en-us/nuget/create-packages/package-authoring-best-practices)

---

## 🎓 小白教程：从零开始理解 Zylo.Service

### 📚 基础概念

#### **什么是依赖注入？**

想象你在做饭：

- **传统方式**：你需要自己去买菜、洗菜、切菜
- **依赖注入**：有人帮你准备好所有食材，你只需要专心做菜

```csharp
// ❌ 传统方式：自己创建依赖
public class OrderController
{
    private UserService userService = new UserService();  // 自己创建
    private ProductService productService = new ProductService();  // 自己创建

    public void CreateOrder()
    {
        // 使用服务...
    }
}

// ✅ 依赖注入：别人帮你准备好
public class OrderController
{
    private readonly IUserService userService;
    private readonly IProductService productService;

    // 构造函数注入：系统自动传入
    public OrderController(IUserService userService, IProductService productService)
    {
        this.userService = userService;
        this.productService = productService;
    }
}
```

#### **什么是源代码生成器？**

就像 **自动写代码的机器人**：

- 你写：`[YService] public class UserService { }`
- 机器人自动写：接口、注册代码、扩展方法等

### 🔧 YServiceGenerator 函数详解

#### **1. Initialize() - 生成器的大脑**

```csharp
public void Initialize(IncrementalGeneratorInitializationContext context)
```

**🧠 作用**：这是生成器的"大脑"，负责制定工作计划

**🔍 工作流程**：

1. **设置监听器**：监听代码变化
2. **制定筛选规则**：决定哪些类需要处理
3. **安排工作流程**：决定什么时候生成代码

**💡 比喻**：
就像工厂的生产线主管，负责：

- 监控原材料（你的代码）
- 制定生产计划（筛选规则）
- 安排生产流程（代码生成）

**📋 详细步骤**：

```csharp
// 第1步：创建"筛选器"
var yServiceClasses = context.SyntaxProvider.CreateSyntaxProvider(
    predicate: IsYServiceCandidate,    // 快速筛选：这个类可能是YService吗？
    transform: GetYServiceInfo         // 详细分析：提取具体信息
);

// 第2步：收集所有信息
var compilation = context.CompilationProvider.Combine(yServiceClasses.Collect());

// 第3步：注册生成任务
context.RegisterSourceOutput(compilation, Execute);
```

#### **2. IsYServiceCandidate() - 快速筛选器**

```csharp
private static bool IsYServiceCandidate(SyntaxNode node)
```

**🏃‍♂️ 作用**：快速判断一个类是否"可能"是 YService

**🎯 原理**：
只看代码的"外表"，不深入分析，就像看人的外貌判断是否是医生（穿白大褂）

**📋 判断条件**：

```csharp
return node is ClassDeclarationSyntax classDeclaration &&  // 1. 必须是类
       classDeclaration.AttributeLists.Count > 0;          // 2. 必须有属性标记
```

**💡 为什么要快速筛选？**

- **性能优化**：避免对每个类都做复杂分析
- **早期过滤**：快速排除不相关的代码

**🔍 示例**：

```csharp
// ✅ 会被选中（有属性）
[YService]
public class UserService { }

// ✅ 会被选中（有其他属性）
[Obsolete]
public class OldService { }

// ❌ 不会被选中（没有属性）
public class PlainClass { }

// ❌ 不会被选中（不是类）
public interface IService { }
```

#### **3. GetYServiceInfo() - 详细分析师**

```csharp
private static YServiceInfo? GetYServiceInfo(GeneratorSyntaxContext context)
```

**🔍 作用**：深入分析类，提取所有需要的信息

**🧐 工作流程**：

**第1步：获取类的详细信息**

```csharp
// 从语法树获取语义信息
if (semanticModel.GetDeclaredSymbol(classDeclaration) is not INamedTypeSymbol classSymbol)
    return null;  // 如果获取失败，说明代码有问题
```

**第2步：查找 YService 属性**

```csharp
var yServiceAttribute = classSymbol.GetAttributes()
    .FirstOrDefault(attr =>
        attr.AttributeClass?.Name == "YServiceAttribute" ||     // [YServiceAttribute]
        (attr.AttributeClass?.Name == "Attribute" &&           // 嵌套属性
         attr.AttributeClass?.ContainingType?.Name == "YServiceAttribute"));
```

**第3步：提取属性参数**

```csharp
var lifetime = GetLifetime(yServiceAttribute);                    // 生命周期
var generateInterface = GetBooleanValue(attr, "GenerateInterface", true);  // 是否生成接口
var interfacePrefix = GetStringValue(attr, "InterfacePrefix", "I");        // 接口前缀
```

**💡 比喻**：
就像医生给病人做详细检查：

- 快速筛选：看外表判断是否需要检查
- 详细分析：做各种检测，获取完整信息

#### **4. Execute() - 代码生成工厂**

```csharp
private static void Execute(Compilation compilation, ImmutableArray<YServiceInfo?> yServiceInfos, SourceProductionContext context)
```

**🏭 作用**：根据收集的信息，批量生成代码

**📋 生产流程**：

**第1步：质量检查**

```csharp
if (yServiceInfos.IsDefaultOrEmpty) return;  // 没有原材料，停止生产

var validInfos = yServiceInfos.Where(info => info != null).Cast<YServiceInfo>().ToList();
if (validInfos.Count == 0) return;  // 没有合格的原材料，停止生产
```

**第2步：生产接口**

```csharp
foreach (var info in validInfos.Where(i => i.GenerateInterface))
{
    GenerateInterface(context, info);  // 为每个需要接口的服务生成接口
}
```

**第3步：生产注册代码**

```csharp
GenerateServiceRegistrationExtensions(context, validInfos, assemblyName);
```

**💡 比喻**：
就像汽车工厂的生产线：

- 质量检查：确保零件合格
- 组装车身：生成接口
- 安装引擎：生成注册代码

#### **5. GenerateInterface() - 接口制造机**

```csharp
private static void GenerateInterface(SourceProductionContext context, YServiceInfo info)
```

**🏗️ 作用**：为服务类生成对应的接口

**📝 生成内容**：

**接口定义文件**：

```csharp
// 生成 IUserService.g.cs
public partial interface IUserService
{
    // 用户可以在其他文件中扩展这个接口
}
```

**接口实现文件**：

```csharp
// 生成 UserService.Interface.g.cs
public partial class UserService : IUserService
{
    // 让原始类自动实现接口
}
```

**🎯 设计理念**：

- **分离关注点**：接口定义和实现分开
- **用户友好**：用户只需要在原始类中写业务逻辑
- **自动连接**：系统自动建立接口和实现的关系

#### **6. GenerateServiceRegistrationExtensions() - 注册代码工厂**

```csharp
private static void GenerateServiceRegistrationExtensions(SourceProductionContext context, List<YServiceInfo> infos, string assemblyName)
```

**🔧 作用**：生成依赖注入注册的扩展方法

**📝 生成示例**：

```csharp
// 生成 ServiceRegistration.MyProject.g.cs
public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAllMyProjectServices(this IServiceCollection services)
    {
        // 注册 UserService (Scoped)
        services.AddScoped<IUserService, UserService>();

        // 注册 ProductService (Singleton)
        services.AddSingleton<ProductService>();

        return services;
    }
}
```

**🎯 用户使用**：

```csharp
var services = new ServiceCollection();
services.AddAllMyProjectServices();  // 一行代码注册所有服务！
```

### 🛠️ 辅助函数详解

#### **GetLifetime() - 生命周期解析器**

**🔍 作用**：从属性中提取服务的生命周期

**📋 查找顺序**：

1. **构造函数参数**：`[YService(ServiceLifetime.Singleton)]`
2. **命名参数**：`[YService(Lifetime = ServiceLifetime.Scoped)]`
3. **默认值**：`Scoped`

**💡 示例**：

```csharp
[YService(ServiceLifetime.Singleton)]     // GetLifetime() 返回 "Singleton"
public class CacheService { }

[YService(Lifetime = ServiceLifetime.Transient)]  // 返回 "Transient"
public class LogService { }

[YService]  // 返回 "Scoped"（默认值）
public class UserService { }
```

#### **SanitizeAssemblyName() - 名称清理器**

**🧹 作用**：清理程序集名称，使其适合作为方法名

**📋 清理规则**：

```csharp
"MyProject.WebApi" → "MyProjectWebApi"    // 移除点号
"My-Cool-Project" → "MyCoolProject"       // 移除连字符
"My Project" → "MyProject"                // 移除空格
```

**🎯 目的**：确保生成的方法名符合 C# 语法规范

### 📊 数据模型：YServiceInfo

**🎯 作用**：存储从 YService 属性中提取的所有信息

**📋 包含信息**：

```csharp
record YServiceInfo(
    string ClassName,        // "UserService"
    string Namespace,        // "MyProject.Services"
    string Lifetime,         // "Scoped"
    bool GenerateInterface,  // true
    string InterfacePrefix,  // "I"
    bool IsPartial          // true
);
```

**💡 便利属性**：

```csharp
info.FullServiceTypeName    // "MyProject.Services.UserService"
info.FullInterfaceTypeName  // "MyProject.Services.IUserService"
info.DisplayName           // "IUserService → UserService (Scoped)"
```

### 🎯 完整工作流程示例

**输入代码**：

```csharp
namespace MyProject.Services;

[YService(ServiceLifetime.Singleton)]
public partial class UserService
{
    public string GetUser(int id) => $"User_{id}";
}
```

**生成器工作流程**：

1. **Initialize()** 设置监听
2. **IsYServiceCandidate()** 发现这个类有属性 ✅
3. **GetYServiceInfo()** 提取信息：
   - ClassName: "UserService"
   - Namespace: "MyProject.Services"
   - Lifetime: "Singleton"
   - GenerateInterface: true
4. **Execute()** 开始生成
5. **GenerateInterface()** 生成接口文件
6. **GenerateServiceRegistrationExtensions()** 生成注册代码

**生成的文件**：

**IUserService.g.cs**：

```csharp
namespace MyProject.Services
{
    public partial interface IUserService
    {
        // 用户可以扩展
    }
}
```

**UserService.Interface.g.cs**：

```csharp
namespace MyProject.Services
{
    public partial class UserService : IUserService
    {
        // 自动实现接口
    }
}
```

**ServiceRegistration.MyProject.g.cs**：

```csharp
public static IServiceCollection AddAllMyProjectServices(this IServiceCollection services)
{
    services.AddSingleton<IUserService, UserService>();
    return services;
}
```

**最终使用**：

```csharp
var services = new ServiceCollection();
services.AddAllMyProjectServices();  // 🎉 一行代码搞定！

var provider = services.BuildServiceProvider();
var userService = provider.GetRequiredService<IUserService>();
```

---

**🎯 核心理念**：让依赖注入像使用 CommunityToolkit 一样简单！
