<Project>
  <!-- Zylo.Toolkit 构建目标 -->

  <!-- 🔥 关键：自动配置分析器，像 CommunityToolkit 一样无需手动配置 -->
  <ItemGroup>
    <Analyzer Include="$(MSBuildThisFileDirectory)../analyzers/dotnet/cs/Zylo.Toolkit.dll" />
  </ItemGroup>

  <!-- 确保源代码生成器正确加载 -->
  <Target Name="ZyloToolkitEnsureAnalyzer" BeforeTargets="CoreCompile"
    Condition="'$(EnableZyloToolkitSourceGenerator)' == 'true'">
    <Message Text="🚀 Zylo.Toolkit 源代码生成器已启用" Importance="normal"
      Condition="'$(ZyloToolkitDebug)' == 'true'" />
  </Target>

  <!-- 清理生成的文件 -->
  <Target Name="ZyloToolkitCleanGeneratedFiles" BeforeTargets="Clean">
    <ItemGroup>


      <!-- 🔧 通用模式：所有 .yg.cs 文件（兜底） -->
      <ZyloToolkitGeneratedFiles Include="*.yg.cs" />
      <ZyloToolkitGeneratedFiles Include="**/*.*.yg.cs" />
      <ZyloToolkitGeneratedFiles Include="**/*.yg.cs" />

    </ItemGroup>

    <Delete Files="@(ZyloToolkitGeneratedFiles)" ContinueOnError="true" />
    <Message
      Text="🧹 已清理 Zylo.Toolkit 生成的文件: @(ZyloToolkitGeneratedFiles->'%(Filename)%(Extension)', ', ')"
      Importance="normal" Condition="'$(ZyloToolkitDebug)' == 'true'" />
  </Target>
</Project>