using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using FreeSql.DataAnnotations;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Zylo.YData.Extensions;
using Zylo.YData.Models;

namespace Zylo.YData.Demo;

/// <summary>
/// 全面测试所有功能的综合实例
/// 包含数据库操作、验证、缓存等所有功能的完整测试
/// </summary>
public class ComprehensiveTestExample
{
    private readonly ILogger<ComprehensiveTestExample> _logger;
    private readonly string _databasePath;

    public ComprehensiveTestExample()
    {
        // 配置日志
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        var provider = services.BuildServiceProvider();
        _logger = provider.GetRequiredService<ILogger<ComprehensiveTestExample>>();

        // 设置数据库路径
        _databasePath = Path.Combine(Environment.CurrentDirectory, "comprehensive_test.db");

        // 配置数据库
        YData.ConfigureAuto($"Data Source={_databasePath}", YDataType.Sqlite);

        _logger.LogInformation($"📁 数据库文件位置: {_databasePath}");
    }

    /// <summary>
    /// 运行所有功能的综合测试
    /// </summary>
    public async Task RunComprehensiveTestAsync()
    {
        Console.WriteLine("🚀 Zylo.YData 全功能综合测试");
        Console.WriteLine(new string('=', 80));
        Console.WriteLine($"📁 数据库文件: {_databasePath}");
        Console.WriteLine($"📊 数据库大小: {GetDatabaseSize()} KB");
        Console.WriteLine();

        try
        {
            // 1. 数据库结构测试
            await TestDatabaseStructureAsync();

            // 2. 基础CRUD操作测试
            await TestBasicCrudOperationsAsync();

            // 3. 数据验证功能测试
            await TestDataValidationAsync();

            // 4. 批量操作测试
            await TestBatchOperationsAsync();

            // 5. 事务操作测试
            await TestTransactionOperationsAsync();

            // 6. 查询功能测试
            await TestQueryFeaturesAsync();

            // 7. 性能测试
            await TestPerformanceAsync();

            // 8. 数据完整性测试
            await TestDataIntegrityAsync();

            Console.WriteLine("\n🎉 所有测试完成！");
            Console.WriteLine($"📁 最终数据库文件: {_databasePath}");
            Console.WriteLine($"📊 最终数据库大小: {GetDatabaseSize()} KB");

            // 显示数据库内容统计
            await ShowDatabaseStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "综合测试过程中发生错误");
            throw;
        }
    }

    /// <summary>
    /// 测试数据库结构
    /// </summary>
    private async Task TestDatabaseStructureAsync()
    {
        Console.WriteLine("📋 1. 数据库结构测试");
        Console.WriteLine(new string('-', 50));

        try
        {
            // 创建表结构
            var freeSql = YData.FreeSql;

            // 同步表结构
            freeSql.CodeFirst.SyncStructure<TestUser>();
            freeSql.CodeFirst.SyncStructure<TestProduct>();
            freeSql.CodeFirst.SyncStructure<TestOrder>();
            freeSql.CodeFirst.SyncStructure<TestOrderItem>();

            Console.WriteLine("✅ 数据库表结构创建成功");

            // 检查表是否存在
            var tables = new[] { "TestUser", "TestProduct", "TestOrder", "TestOrderItem" };
            foreach (var table in tables)
            {
                var exists = await freeSql.Ado.QuerySingleAsync<int>(
                    $"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='{table}'") > 0;
                Console.WriteLine($"   📋 表 {table}: {(exists ? "✅ 存在" : "❌ 不存在")}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 数据库结构测试失败: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// 测试基础CRUD操作
    /// </summary>
    private async Task TestBasicCrudOperationsAsync()
    {
        Console.WriteLine("\n📋 2. 基础CRUD操作测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        // 创建测试用户
        var user = new TestUser
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 25,
            Phone = "13812345678",
            CreatedAt = DateTime.Now
        };

        // 插入操作
        var insertResult = await freeSql.Insert(user).ExecuteAffrowsAsync();
        Console.WriteLine($"✅ 插入用户: {insertResult} 行受影响");

        // 查询操作
        var users = await freeSql.Select<TestUser>().ToListAsync();
        Console.WriteLine($"✅ 查询用户: 共 {users.Count} 个用户");

        // 更新操作
        var updateResult = await freeSql.Update<TestUser>()
            .Set(u => u.Age, 26)
            .Where(u => u.Email == "<EMAIL>")
            .ExecuteAffrowsAsync();
        Console.WriteLine($"✅ 更新用户: {updateResult} 行受影响");

        // 删除操作（先不删除，留作后续测试）
        Console.WriteLine($"✅ 保留用户数据用于后续测试");
    }

    /// <summary>
    /// 测试数据验证功能
    /// </summary>
    private async Task TestDataValidationAsync()
    {
        Console.WriteLine("\n📋 3. 数据验证功能测试");
        Console.WriteLine(new string('-', 50));

        // 测试有效数据
        var validUser = new TestUser
        {
            Name = "李四",
            Email = "<EMAIL>",
            Age = 30,
            Phone = "15987654321"
        };

        var validResult = await validUser.YValidateAsync();
        Console.WriteLine($"✅ 有效用户验证: {(validResult.IsValid ? "通过" : "失败")}");

        // 测试无效数据
        var invalidUser = new TestUser
        {
            Name = "", // 空名称
            Email = "invalid-email", // 无效邮箱
            Age = -1, // 无效年龄
            Phone = "123" // 无效手机号
        };

        var invalidResult = await invalidUser.YValidateAsync();
        Console.WriteLine($"❌ 无效用户验证: {(invalidResult.IsValid ? "通过" : "失败")}");
        if (!invalidResult.IsValid)
        {
            foreach (var error in invalidResult.Errors)
            {
                Console.WriteLine($"   • {error.PropertyName}: {error.ErrorMessage}");
            }
        }

        // 测试扩展验证方法
        Console.WriteLine("\n🔍 扩展验证方法测试:");
        var emails = new[] { "<EMAIL>", "invalid-email", "" };
        foreach (var email in emails)
        {
            var isValid = email.YIsValidEmail();
            Console.WriteLine($"   📧 {email ?? "null"}: {(isValid ? "✅ 有效" : "❌ 无效")}");
        }
    }

    /// <summary>
    /// 获取数据库文件大小（KB）
    /// </summary>
    private double GetDatabaseSize()
    {
        if (File.Exists(_databasePath))
        {
            var fileInfo = new FileInfo(_databasePath);
            return Math.Round(fileInfo.Length / 1024.0, 2);
        }
        return 0;
    }

    /// <summary>
    /// 测试批量操作
    /// </summary>
    private async Task TestBatchOperationsAsync()
    {
        Console.WriteLine("\n📋 4. 批量操作测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        // 批量插入产品
        var products = new List<TestProduct>();
        for (int i = 1; i <= 10; i++)
        {
            products.Add(new TestProduct
            {
                Name = $"产品{i}",
                Price = i * 10.5m,
                Stock = i * 5
            });
        }

        var insertCount = await freeSql.Insert(products).ExecuteAffrowsAsync();
        Console.WriteLine($"✅ 批量插入产品: {insertCount} 行受影响");

        // 批量验证
        var validationResults = await products.YValidateCollectionAsync();
        Console.WriteLine($"✅ 批量验证: {validationResults.SuccessCount}/{validationResults.TotalCount} 个有效");

        // 批量更新
        var updateCount = await freeSql.Update<TestProduct>()
            .Set(p => p.Stock + 10)
            .Where(p => p.Price < 50)
            .ExecuteAffrowsAsync();
        Console.WriteLine($"✅ 批量更新: {updateCount} 行受影响");
    }

    /// <summary>
    /// 测试事务操作
    /// </summary>
    private async Task TestTransactionOperationsAsync()
    {
        Console.WriteLine("\n📋 5. 事务操作测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        try
        {
            // 创建订单
            var order = new TestOrder
            {
                OrderNumber = $"ORD{DateTime.Now:yyyyMMddHHmmss}",
                UserId = 1,
                TotalAmount = 0
            };

            var orderId = await freeSql.Insert(order).ExecuteIdentityAsync();
            Console.WriteLine($"✅ 创建订单: ID={orderId}");

            // 添加订单项
            var orderItems = new List<TestOrderItem>
            {
                new() { OrderId = (int)orderId, ProductId = 1, Quantity = 2, UnitPrice = 10.5m },
                new() { OrderId = (int)orderId, ProductId = 2, Quantity = 1, UnitPrice = 20.5m }
            };

            var itemCount = await freeSql.Insert(orderItems).ExecuteAffrowsAsync();
            Console.WriteLine($"✅ 添加订单项: {itemCount} 行受影响");

            // 更新订单总金额
            var totalAmount = orderItems.Sum(item => item.Quantity * item.UnitPrice);
            await freeSql.Update<TestOrder>()
                .Set(o => o.TotalAmount, totalAmount)
                .Where(o => o.Id == orderId)
                .ExecuteAffrowsAsync();

            Console.WriteLine($"✅ 事务操作成功，订单总金额: {totalAmount:C}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 事务操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试查询功能
    /// </summary>
    private async Task TestQueryFeaturesAsync()
    {
        Console.WriteLine("\n📋 6. 查询功能测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        // 简单查询
        var users = await freeSql.Select<TestUser>().ToListAsync();
        Console.WriteLine($"✅ 简单查询: 共 {users.Count} 个用户");

        // 条件查询
        var youngUsers = await freeSql.Select<TestUser>()
            .Where(u => u.Age < 30)
            .ToListAsync();
        Console.WriteLine($"✅ 条件查询: {youngUsers.Count} 个年轻用户");

        // 分页查询
        var pagedUsers = await freeSql.Select<TestUser>()
            .Page(1, 5)
            .ToListAsync();
        Console.WriteLine($"✅ 分页查询: 第1页，{pagedUsers.Count} 条记录");

        // 排序查询
        var sortedUsers = await freeSql.Select<TestUser>()
            .OrderBy(u => u.Age)
            .ToListAsync();
        Console.WriteLine($"✅ 排序查询: 按年龄排序，{sortedUsers.Count} 条记录");

        // 聚合查询
        var avgAge = await freeSql.Select<TestUser>().AvgAsync(u => u.Age);
        var maxAge = await freeSql.Select<TestUser>().MaxAsync(u => u.Age);
        var minAge = await freeSql.Select<TestUser>().MinAsync(u => u.Age);
        Console.WriteLine($"✅ 聚合查询: 平均年龄={avgAge:F1}, 最大年龄={maxAge}, 最小年龄={minAge}");
    }

    /// <summary>
    /// 测试性能
    /// </summary>
    private async Task TestPerformanceAsync()
    {
        Console.WriteLine("\n📋 7. 性能测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;
        var stopwatch = Stopwatch.StartNew();

        // 大批量插入测试
        var testUsers = new List<TestUser>();
        for (int i = 1; i <= 1000; i++)
        {
            testUsers.Add(new TestUser
            {
                Name = $"测试用户{i}",
                Email = $"test{i}@example.com",
                Age = 20 + (i % 50),
                Phone = $"138{i:D8}"
            });
        }

        stopwatch.Restart();
        var insertCount = await freeSql.Insert(testUsers).ExecuteAffrowsAsync();
        stopwatch.Stop();
        Console.WriteLine($"✅ 批量插入 {insertCount} 条记录，耗时: {stopwatch.ElapsedMilliseconds} ms");

        // 大批量查询测试
        stopwatch.Restart();
        var allUsers = await freeSql.Select<TestUser>().ToListAsync();
        stopwatch.Stop();
        Console.WriteLine($"✅ 查询 {allUsers.Count} 条记录，耗时: {stopwatch.ElapsedMilliseconds} ms");

        // 批量验证性能测试
        stopwatch.Restart();
        var validationResult = await testUsers.Take(100).ToList().YValidateCollectionAsync();
        stopwatch.Stop();
        Console.WriteLine($"✅ 验证 100 条记录，耗时: {stopwatch.ElapsedMilliseconds} ms，成功率: {validationResult.SuccessCount}/{validationResult.TotalCount}");
    }

    /// <summary>
    /// 测试数据完整性
    /// </summary>
    private async Task TestDataIntegrityAsync()
    {
        Console.WriteLine("\n📋 8. 数据完整性测试");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        // 检查数据一致性
        var userCount = await freeSql.Select<TestUser>().CountAsync();
        var orderCount = await freeSql.Select<TestOrder>().CountAsync();
        var productCount = await freeSql.Select<TestProduct>().CountAsync();
        var orderItemCount = await freeSql.Select<TestOrderItem>().CountAsync();

        Console.WriteLine($"✅ 数据一致性检查:");
        Console.WriteLine($"   👥 用户: {userCount} 条");
        Console.WriteLine($"   📦 产品: {productCount} 条");
        Console.WriteLine($"   📋 订单: {orderCount} 条");
        Console.WriteLine($"   📄 订单项: {orderItemCount} 条");

        // 检查外键关系
        var ordersWithUsers = await freeSql.Select<TestOrder>()
            .InnerJoin<TestUser>((o, u) => o.UserId == u.Id)
            .CountAsync();
        Console.WriteLine($"✅ 订单-用户关联: {ordersWithUsers}/{orderCount} 条有效");

        // 检查数据有效性
        var invalidUsers = await freeSql.Select<TestUser>()
            .Where(u => u.Age < 0 || u.Age > 150 || u.Name == "" || !u.Email.Contains("@"))
            .CountAsync();
        Console.WriteLine($"✅ 用户数据有效性: {userCount - invalidUsers}/{userCount} 条有效");
    }

    /// <summary>
    /// 显示数据库统计信息
    /// </summary>
    private async Task ShowDatabaseStatisticsAsync()
    {
        Console.WriteLine("\n📊 数据库统计信息");
        Console.WriteLine(new string('-', 50));

        var freeSql = YData.FreeSql;

        try
        {
            var userCount = await freeSql.Select<TestUser>().CountAsync();
            var productCount = await freeSql.Select<TestProduct>().CountAsync();
            var orderCount = await freeSql.Select<TestOrder>().CountAsync();
            var orderItemCount = await freeSql.Select<TestOrderItem>().CountAsync();

            Console.WriteLine($"👥 用户数量: {userCount}");
            Console.WriteLine($"📦 产品数量: {productCount}");
            Console.WriteLine($"📋 订单数量: {orderCount}");
            Console.WriteLine($"📄 订单项数量: {orderItemCount}");

            // 显示最新的几条记录
            var latestUsers = await freeSql.Select<TestUser>()
                .OrderByDescending(u => u.Id)
                .Take(3)
                .ToListAsync();

            Console.WriteLine("\n📋 最新用户:");
            foreach (var user in latestUsers)
            {
                Console.WriteLine($"   • {user.Name} ({user.Email}) - 年龄: {user.Age}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 获取统计信息失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 测试用户模型
/// </summary>
[Table(Name = "TestUser")]
public class TestUser
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    [Required(ErrorMessage = "姓名是必填字段")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "姓名长度必须在2-50个字符之间")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "邮箱是必填字段")]
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    public string Email { get; set; } = string.Empty;

    [Range(0, 150, ErrorMessage = "年龄必须在0-150之间")]
    public int Age { get; set; }

    [Phone(ErrorMessage = "手机号格式不正确")]
    public string? Phone { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 测试产品模型
/// </summary>
[Table(Name = "TestProduct")]
public class TestProduct
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Range(0.01, double.MaxValue, ErrorMessage = "价格必须大于0")]
    public decimal Price { get; set; }

    [Range(0, int.MaxValue, ErrorMessage = "库存不能为负数")]
    public int Stock { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 测试订单模型
/// </summary>
[Table(Name = "TestOrder")]
public class TestOrder
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string OrderNumber { get; set; } = string.Empty;

    public int UserId { get; set; }

    [Range(0.01, double.MaxValue)]
    public decimal TotalAmount { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 测试订单项模型
/// </summary>
[Table(Name = "TestOrderItem")]
public class TestOrderItem
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    public int OrderId { get; set; }
    public int ProductId { get; set; }

    [Range(1, int.MaxValue)]
    public int Quantity { get; set; }

    [Range(0.01, double.MaxValue)]
    public decimal UnitPrice { get; set; }
}
