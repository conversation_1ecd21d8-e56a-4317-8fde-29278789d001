using System.Diagnostics;

namespace Zylo.YData.Demo;

/// <summary>
/// 全功能综合测试程序入口
/// </summary>
public class ComprehensiveTestProgram
{
    public static async Task RunComprehensiveTestAsync(string[] args)
    {
        Console.WriteLine("🚀 Zylo.YData 全功能综合测试程序");
        Console.WriteLine("=====================================");
        Console.WriteLine("本程序将测试 Zylo.YData 的所有功能模块");
        Console.WriteLine();

        var stopwatch = Stopwatch.StartNew();

        try
        {
            // 创建测试实例
            var testExample = new ComprehensiveTestExample();

            // 运行综合测试
            await testExample.RunComprehensiveTestAsync();

            stopwatch.Stop();

            Console.WriteLine("\n🎉 所有测试完成！");
            Console.WriteLine($"⏱️ 总耗时: {stopwatch.ElapsedMilliseconds} ms ({stopwatch.Elapsed.TotalSeconds:F2} 秒)");

            // 显示数据库文件信息
            ShowDatabaseFileInfo();

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 测试过程中发生错误:");
            Console.WriteLine($"错误信息: {ex.Message}");
            Console.WriteLine($"详细堆栈: {ex.StackTrace}");

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }

    /// <summary>
    /// 显示数据库文件信息
    /// </summary>
    private static void ShowDatabaseFileInfo()
    {
        Console.WriteLine("\n📁 数据库文件信息");
        Console.WriteLine(new string('-', 40));

        var currentDir = Environment.CurrentDirectory;
        var databaseFiles = new[]
        {
            "comprehensive_test.db",
            "demo.db",
            "test.db"
        };

        foreach (var dbFile in databaseFiles)
        {
            var fullPath = Path.Combine(currentDir, dbFile);
            if (File.Exists(fullPath))
            {
                var fileInfo = new FileInfo(fullPath);
                var sizeKB = Math.Round(fileInfo.Length / 1024.0, 2);
                var lastModified = fileInfo.LastWriteTime;

                Console.WriteLine($"📄 {dbFile}:");
                Console.WriteLine($"   📍 路径: {fullPath}");
                Console.WriteLine($"   📊 大小: {sizeKB} KB");
                Console.WriteLine($"   🕒 修改时间: {lastModified:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();
            }
        }

        // 显示当前目录下的所有 .db 文件
        var allDbFiles = Directory.GetFiles(currentDir, "*.db");
        if (allDbFiles.Length > 0)
        {
            Console.WriteLine($"📋 当前目录下共有 {allDbFiles.Length} 个数据库文件:");
            foreach (var file in allDbFiles)
            {
                var fileName = Path.GetFileName(file);
                var fileInfo = new FileInfo(file);
                var sizeKB = Math.Round(fileInfo.Length / 1024.0, 2);
                Console.WriteLine($"   • {fileName} ({sizeKB} KB)");
            }
        }
        else
        {
            Console.WriteLine("❌ 当前目录下没有找到数据库文件");
        }
    }
}
