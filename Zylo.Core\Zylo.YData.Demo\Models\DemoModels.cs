using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace Zylo.YData.Demo.Models;

/// <summary>
/// 演示用户模型
/// </summary>
[Table(Name = "demo_users")]
public class DemoUser
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, MinimumLength = 2, ErrorMessage = "姓名长度必须在2-50个字符之间")]
    public string Name { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "邮箱不能为空")]
    [EmailAddress(ErrorMessage = "邮箱格式不正确")]
    [StringLength(100, ErrorMessage = "邮箱长度不能超过100个字符")]
    public string Email { get; set; } = string.Empty;
    
    [Range(0, 150, ErrorMessage = "年龄必须在0-150岁之间")]
    public int Age { get; set; }
    
    [StringLength(20, ErrorMessage = "手机号长度不能超过20个字符")]
    public string Phone { get; set; } = string.Empty;
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public bool IsActive { get; set; } = true;
    
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remarks { get; set; }

    public override string ToString()
    {
        return $"用户[{Id}]: {Name} ({Email}), 年龄: {Age}, 手机: {Phone}";
    }
}

/// <summary>
/// 演示产品模型
/// </summary>
[Table(Name = "demo_products")]
public class DemoProduct
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    
    [Required(ErrorMessage = "产品名称不能为空")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "产品名称长度必须在1-100个字符之间")]
    public string Name { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "产品描述长度不能超过1000个字符")]
    public string? Description { get; set; }
    
    [Range(0.01, double.MaxValue, ErrorMessage = "价格必须大于0")]
    public decimal Price { get; set; }
    
    [Range(0, int.MaxValue, ErrorMessage = "库存数量不能为负数")]
    public int Stock { get; set; }
    
    [Required(ErrorMessage = "分类不能为空")]
    [StringLength(50, ErrorMessage = "分类长度不能超过50个字符")]
    public string Category { get; set; } = string.Empty;
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public bool IsActive { get; set; } = true;

    public override string ToString()
    {
        return $"产品[{Id}]: {Name}, 价格: ¥{Price:F2}, 库存: {Stock}, 分类: {Category}";
    }
}

/// <summary>
/// 演示订单模型
/// </summary>
[Table(Name = "demo_orders")]
public class DemoOrder
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    
    [Required(ErrorMessage = "订单号不能为空")]
    [StringLength(50, ErrorMessage = "订单号长度不能超过50个字符")]
    public string OrderNo { get; set; } = string.Empty;
    
    [Range(1, int.MaxValue, ErrorMessage = "用户ID必须大于0")]
    public int UserId { get; set; }
    
    [Range(0.01, double.MaxValue, ErrorMessage = "订单总金额必须大于0")]
    public decimal TotalAmount { get; set; }
    
    [Required(ErrorMessage = "订单状态不能为空")]
    [StringLength(20, ErrorMessage = "订单状态长度不能超过20个字符")]
    public string Status { get; set; } = string.Empty;
    
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remarks { get; set; }
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public DateTime? UpdateTime { get; set; }

    public override string ToString()
    {
        return $"订单[{Id}]: {OrderNo}, 用户ID: {UserId}, 金额: ¥{TotalAmount:F2}, 状态: {Status}";
    }
}
