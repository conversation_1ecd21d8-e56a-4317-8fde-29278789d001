using Zylo.YData;
using Zylo.YData.Extensions;
using Zylo.YData.Demo.Models;

namespace Zylo.YData.Demo;

/// <summary>
/// 多数据库功能演示
/// </summary>
public static class MultiDatabaseExample
{
    /// <summary>
    /// 运行多数据库演示
    /// </summary>
    public static async Task RunAsync()
    {
        Console.WriteLine("🚀 Zylo.YData 多数据库功能演示");
        Console.WriteLine("=====================================");

        try
        {
            // 1. 注册多个数据库
            await RegisterMultipleDatabases();

            // 2. 演示数据库切换
            await DemoDatabaseSwitching();

            // 3. 演示多表关联查询
            await DemoMultiTableQueries();

            // 4. 演示跨数据库操作
            await DemoCrossDatabaseOperations();

            // 5. 演示并行数据库操作
            await DemoParallelDatabaseOperations();

            // 6. 演示数据库健康检查
            await DemoDatabaseHealthCheck();

            Console.WriteLine("\n🎉 多数据库演示完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 演示过程中发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 注册多个数据库
    /// </summary>
    private static async Task RegisterMultipleDatabases()
    {
        Console.WriteLine("\n📋 1. 注册多个数据库");
        Console.WriteLine("--------------------------------------------------");

        // 注册用户数据库
        YData.RegisterDatabase("users", "Data Source=users.db", YDataType.Sqlite);
        Console.WriteLine("✅ 注册用户数据库: users.db");

        // 注册订单数据库
        YData.RegisterDatabase("orders", "Data Source=orders.db", YDataType.Sqlite);
        Console.WriteLine("✅ 注册订单数据库: orders.db");

        // 注册产品数据库
        YData.RegisterDatabase("products", "Data Source=products.db", YDataType.Sqlite);
        Console.WriteLine("✅ 注册产品数据库: products.db");

        // 批量注册多个数据库
        var databases = new Dictionary<string, (string connectionString, YDataType dataType)>
        {
            ["analytics"] = ("Data Source=analytics.db", YDataType.Sqlite),
            ["logs"] = ("Data Source=logs.db", YDataType.Sqlite)
        };
        YData.RegisterDatabases(databases);
        Console.WriteLine("✅ 批量注册分析和日志数据库");

        // 快速注册 SQLite 数据库
        YData.RegisterSqliteDatabases(
            ("cache", "cache.db"),
            ("temp", "temp.db")
        );
        Console.WriteLine("✅ 快速注册缓存和临时数据库");

        // 显示所有注册的数据库
        var allDatabases = YData.GetDatabaseNames();
        Console.WriteLine($"📊 总共注册了 {allDatabases.Count()} 个数据库:");
        foreach (var dbName in allDatabases)
        {
            var info = YData.GetDatabaseInfo(dbName);
            Console.WriteLine($"   • {dbName} ({info?.DataType}) {(info?.IsDefault == true ? "[默认]" : "")}");
        }
    }

    /// <summary>
    /// 演示数据库切换
    /// </summary>
    private static async Task DemoDatabaseSwitching()
    {
        Console.WriteLine("\n📋 2. 数据库切换演示");
        Console.WriteLine("--------------------------------------------------");

        // 设置默认数据库
        YData.UseDatabase("users");
        Console.WriteLine("✅ 切换到用户数据库");

        // 在用户数据库中创建表和插入数据
        YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
        await YData.InsertAsync(new TestUser { Name = "张三", Email = "<EMAIL>", Age = 25 });
        var userCount = await YData.Select<TestUser>().CountAsync();
        Console.WriteLine($"✅ 用户数据库中有 {userCount} 个用户");

        // 切换到订单数据库
        YData.UseDatabase("orders");
        Console.WriteLine("✅ 切换到订单数据库");

        // 在订单数据库中创建表和插入数据
        YData.FreeSql.CodeFirst.SyncStructure<TestOrder>();
        await YData.InsertAsync(new TestOrder { OrderNumber = "ORD001", UserId = 1, TotalAmount = 99.99m });
        var orderCount = await YData.Select<TestOrder>().CountAsync();
        Console.WriteLine($"✅ 订单数据库中有 {orderCount} 个订单");

        // 直接指定数据库操作
        await YData.InsertAsync(new TestUser { Name = "李四", Email = "<EMAIL>", Age = 30 }, "users");
        var userCount2 = await YData.Select<TestUser>("users").CountAsync();
        Console.WriteLine($"✅ 直接在用户数据库中操作，现在有 {userCount2} 个用户");
    }

    /// <summary>
    /// 演示多表关联查询
    /// </summary>
    private static async Task DemoMultiTableQueries()
    {
        Console.WriteLine("\n📋 3. 多表关联查询演示");
        Console.WriteLine("--------------------------------------------------");

        // 在用户数据库中准备数据
        YData.UseDatabase("users");
        YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
        YData.FreeSql.CodeFirst.SyncStructure<TestOrder>();

        // 插入测试数据
        var users = new List<TestUser>
        {
            new() { Name = "用户1", Email = "<EMAIL>", Age = 25 },
            new() { Name = "用户2", Email = "<EMAIL>", Age = 30 },
            new() { Name = "用户3", Email = "<EMAIL>", Age = 35 }
        };
        await YData.Insert<TestUser>().AppendData(users).ExecuteAffrowsAsync();

        var orders = new List<TestOrder>
        {
            new() { OrderNumber = "ORD001", UserId = 1, TotalAmount = 100.00m },
            new() { OrderNumber = "ORD002", UserId = 1, TotalAmount = 200.00m },
            new() { OrderNumber = "ORD003", UserId = 2, TotalAmount = 150.00m }
        };
        await YData.Insert<TestOrder>().AppendData(orders).ExecuteAffrowsAsync();

        // 多表关联查询
        var userOrders = await YData.Select<TestUser>()
            .From<TestOrder>()
            .LeftJoin((u, o) => u.Id == o.UserId)
            .Where((u, o) => u.Age > 20)
            .ToListAsync((u, o) => new
            {
                UserName = u.Name,
                UserAge = u.Age,
                OrderNumber = o.OrderNumber,
                OrderAmount = o.TotalAmount
            });

        Console.WriteLine($"✅ 多表关联查询结果: {userOrders.Count} 条记录");
        foreach (var item in userOrders.Take(3))
        {
            Console.WriteLine($"   • {item.UserName} (年龄: {item.UserAge}) - 订单: {item.OrderNumber} - 金额: {item.OrderAmount:C}");
        }

        // 聚合查询 - 简化版本
        var userOrderStats = await YData.Select<TestUser>()
            .ToListAsync();

        Console.WriteLine($"✅ 用户统计: {userOrderStats.Count} 个用户");
        foreach (var user in userOrderStats.Take(3))
        {
            var orderCount = await YData.Select<TestOrder>().Where(o => o.UserId == user.Id).CountAsync();
            var totalAmount = await YData.Select<TestOrder>().Where(o => o.UserId == user.Id).SumAsync(o => o.TotalAmount);
            Console.WriteLine($"   • {user.Name}: {orderCount} 个订单, 总金额: {totalAmount:C}");
        }



        // 分页查询
        var pagedUsers = await YData.Select<TestUser>()
            .Where(u => u.Age > 20)
            .OrderBy(u => u.Age)
            .Page(1, 2)
            .ToListAsync();

        Console.WriteLine($"✅ 分页查询: 获取到 {pagedUsers.Count} 条记录");
        foreach (var user in pagedUsers)
        {
            Console.WriteLine($"   • {user.Name} (年龄: {user.Age})");
        }
    }

    /// <summary>
    /// 演示跨数据库操作
    /// </summary>
    private static async Task DemoCrossDatabaseOperations()
    {
        Console.WriteLine("\n📋 4. 跨数据库操作演示");
        Console.WriteLine("--------------------------------------------------");

        // 首先在所有数据库中创建表结构和测试数据
        await YDataSwitcher.ExecuteInAllDatabasesAsync(async dbName =>
        {
            // 创建表结构
            YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
            YData.FreeSql.CodeFirst.SyncStructure<TestOrder>();

            // 在每个数据库中插入一些测试数据（如果还没有的话）
            var existingUsers = await YData.Select<TestUser>().CountAsync();
            if (existingUsers == 0)
            {
                await YData.InsertAsync(new TestUser
                {
                    Name = $"用户_{dbName}",
                    Email = $"user_{dbName}@test.com",
                    Age = 25
                });
            }

            return true;
        });

        // 使用 YDataSwitcher 临时切换数据库
        var userCount = await YDataSwitcher.WithDatabaseAsync("users", async () =>
        {
            return await YData.Select<TestUser>().CountAsync();
        });
        Console.WriteLine($"✅ 临时切换到用户数据库查询: {userCount} 个用户");

        var orderCount = await YDataSwitcher.WithDatabaseAsync("orders", async () =>
        {
            return await YData.Select<TestOrder>().CountAsync();
        });
        Console.WriteLine($"✅ 临时切换到订单数据库查询: {orderCount} 个订单");

        // 在所有数据库中查询用户表
        var tableCounts = await YDataSwitcher.ExecuteInAllDatabasesAsync(async dbName =>
        {
            return await YData.Select<TestUser>().CountAsync();
        });

        Console.WriteLine("✅ 在所有数据库中查询用户表:");
        foreach (var kvp in tableCounts)
        {
            Console.WriteLine($"   • {kvp.Key}: {kvp.Value} 个用户");
        }
    }

    /// <summary>
    /// 演示并行数据库操作
    /// </summary>
    private static async Task DemoParallelDatabaseOperations()
    {
        Console.WriteLine("\n📋 5. 并行数据库操作演示");
        Console.WriteLine("--------------------------------------------------");

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // 并行查询所有数据库
        var parallelResults = await YDataSwitcher.ExecuteInAllDatabasesParallelAsync(async dbName =>
        {
            await Task.Delay(100); // 模拟查询延迟
            try
            {
                return await YData.Select<TestUser>().CountAsync();
            }
            catch
            {
                return 0;
            }
        });

        stopwatch.Stop();

        Console.WriteLine($"✅ 并行查询完成，耗时: {stopwatch.ElapsedMilliseconds} ms");
        foreach (var kvp in parallelResults)
        {
            Console.WriteLine($"   • {kvp.Key}: {kvp.Value} 个用户");
        }

        // 安全的批量操作
        var batchResult = await YDataSwitcher.SafeExecuteInAllDatabasesAsync(async dbName =>
        {
            try
            {
                return await YData.Select<TestUser>().CountAsync();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"查询 {dbName} 失败: {ex.Message}");
            }
        });

        Console.WriteLine($"✅ 批量操作结果: 成功 {batchResult.SuccessCount}/{batchResult.TotalOperations}, 成功率: {batchResult.SuccessRate:P}");
        if (batchResult.FailedOperations.Any())
        {
            Console.WriteLine("❌ 失败的操作:");
            foreach (var failure in batchResult.FailedOperations)
            {
                Console.WriteLine($"   • {failure.Key}: {failure.Value.Message}");
            }
        }
    }

    /// <summary>
    /// 演示数据库健康检查
    /// </summary>
    private static async Task DemoDatabaseHealthCheck()
    {
        Console.WriteLine("\n📋 6. 数据库健康检查");
        Console.WriteLine("--------------------------------------------------");

        // 健康检查
        var healthStatus = await YDataSwitcher.HealthCheckAsync();
        Console.WriteLine("✅ 数据库健康状态:");
        foreach (var kvp in healthStatus)
        {
            var status = kvp.Value ? "🟢 健康" : "🔴 异常";
            Console.WriteLine($"   • {kvp.Key}: {status}");
        }

        // 获取数据库统计信息
        var allStats = await YDataSwitcher.GetAllDatabaseStatsAsync();
        Console.WriteLine("\n📊 数据库统计信息:");
        foreach (var kvp in allStats)
        {
            var stats = kvp.Value;
            Console.WriteLine($"   • {kvp.Key}:");
            Console.WriteLine($"     - 连接状态: {(stats.IsConnected ? "已连接" : "未连接")}");
            Console.WriteLine($"     - 最后查询: {stats.LastQueryTime:yyyy-MM-dd HH:mm:ss}");
        }

        // 显示所有数据库信息
        var allDbInfo = YData.GetAllDatabaseInfo();
        Console.WriteLine("\n📋 数据库详细信息:");
        foreach (var info in allDbInfo)
        {
            Console.WriteLine($"   • {info.Name} ({info.DataType}):");
            Console.WriteLine($"     - 创建时间: {info.CreatedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"     - 最后使用: {info.LastUsedAt:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"     - 状态: {(info.IsEnabled ? "启用" : "禁用")} {(info.IsDefault ? "[默认]" : "")}");
        }
    }
}
