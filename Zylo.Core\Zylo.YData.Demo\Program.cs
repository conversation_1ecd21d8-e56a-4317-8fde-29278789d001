using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Zylo.YData.Demo.Services;

namespace Zylo.YData.Demo;

/// <summary>
/// Zylo.YData 数据验证扩展演示程序
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 Zylo.YData 数据验证扩展演示");
        Console.WriteLine(new string('=', 60));

        // 创建主机构建器
        var builder = Host.CreateDefaultBuilder(args);

        // 配置服务
        builder.ConfigureServices((context, services) =>
        {
            services.AddLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.SetMinimumLevel(LogLevel.Information);
            });

            services.AddScoped<IDemoService, DemoService>();
        });

        // 构建并运行主机
        using var host = builder.Build();

        try
        {
            Console.WriteLine("请选择运行模式:");
            Console.WriteLine("1. 原始演示 (交互式)");
            Console.WriteLine("2. 综合测试 (全功能测试)");
            Console.WriteLine("3. 多数据库功能演示 (多表、多数据库)");
            Console.WriteLine("4. YConfigHelper 零参数配置演示 (配置文件管理)");
            Console.Write("请输入选择 (1、2、3 或 4): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "2":
                    // 运行综合测试
                    await ComprehensiveTestProgram.RunComprehensiveTestAsync(args);
                    break;
                case "3":
                    // 运行多数据库演示
                    await MultiDatabaseExample.RunAsync();
                    break;
                case "4":
                    // 运行 YConfigHelper 演示
                    await YConfigHelperDemo.RunAllDemos();
                    break;
                default:
                    // 获取演示服务
                    var demoService = host.Services.GetRequiredService<IDemoService>();
                    // 运行所有演示
                    await demoService.RunAllDemosAsync();
                    break;
            }

            Console.WriteLine("\n🎉 演示完成！程序将在 3 秒后退出...");
            await Task.Delay(3000);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 演示过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
    }
}
