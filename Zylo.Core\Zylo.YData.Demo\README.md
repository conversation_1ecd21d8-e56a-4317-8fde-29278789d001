# Zylo.YData 数据验证扩展演示项目

## 项目概述

这是一个控制台应用程序，用于演示 **Zylo.YData** 数据验证扩展的各种功能和使用场景。通过交互式的演示，您可以深入了解数据验证扩展的强大功能。

## 功能演示

### 🔍 1. 基础验证演示
- 单个实体的同步验证
- 单个实体的异步验证
- 验证结果的错误信息展示
- DataAnnotations 特性验证

### 📊 2. 集合验证演示
- 多个实体的批量验证
- 同步和异步集合验证
- 验证结果统计信息
- 失败实体的详细错误信息

### 🔧 3. 自定义验证规则演示
- 添加自定义同步验证规则
- 添加自定义异步验证规则
- 业务逻辑验证
- 复杂验证场景

### 🛠️ 4. 验证扩展方法演示
- 邮箱格式验证
- 手机号格式验证
- 数值范围验证
- 字符串长度验证
- 日期年龄验证

### ⚡ 5. 性能测试演示
- 大量数据的验证性能测试
- 同步 vs 异步验证性能对比
- 单个实体验证性能测试
- 吞吐量统计

### 💼 6. 实际业务场景演示
- **用户注册场景**: 包含基础验证和业务规则验证
- **产品信息验证场景**: 批量产品数据验证
- **订单创建场景**: 订单数据验证和状态检查

## 运行环境

- **.NET 6.0** 或 **.NET 8.0**
- **Windows/Linux/macOS** 跨平台支持
- **SQLite** 数据库（自动创建）

## 快速开始

### 1. 构建项目

```bash
# 进入项目目录
cd Zylo.YData.Demo

# 还原依赖包
dotnet restore

# 构建项目
dotnet build
```

### 2. 运行演示

```bash
# 运行演示程序
dotnet run
```

### 3. 交互式演示

程序启动后，将按顺序运行以下演示：

1. **基础验证演示** - 展示基本的实体验证功能
2. **集合验证演示** - 展示批量数据验证
3. **自定义验证规则演示** - 展示如何添加自定义验证逻辑
4. **验证扩展方法演示** - 展示各种验证扩展方法
5. **性能测试演示** - 展示验证性能和吞吐量
6. **实际业务场景演示** - 展示真实业务场景的应用

每个演示完成后，按任意键继续下一个演示。

## 演示内容详解

### 基础验证演示

```csharp
// 有效用户验证
var validUser = new DemoUser
{
    Name = "张三",
    Email = "<EMAIL>", 
    Age = 25,
    Phone = "13812345678"
};

var result = validUser.YValidate();
// 结果: ✅ 验证通过

// 无效用户验证
var invalidUser = new DemoUser
{
    Name = "", // 必填字段为空
    Email = "invalid-email", // 邮箱格式错误
    Age = -1 // 年龄无效
};

var result = invalidUser.YValidate();
// 结果: ❌ 验证失败，包含详细错误信息
```

### 集合验证演示

```csharp
var users = new List<DemoUser> { /* 多个用户 */ };

// 批量验证
var collectionResult = users.YValidateCollection();

Console.WriteLine($"总数: {collectionResult.TotalCount}");
Console.WriteLine($"成功: {collectionResult.SuccessCount}");
Console.WriteLine($"失败: {collectionResult.FailureCount}");
```

### 自定义验证规则演示

```csharp
var options = new YValidationOptions();

// 添加自定义规则
options.AddRule<DemoUser>(
    user => user.Age >= 18 && user.Age <= 65,
    "用户年龄必须在工作年龄范围内(18-65岁)",
    nameof(DemoUser.Age)
);

// 添加异步规则
options.AddAsyncRule<DemoUser>(
    async user => {
        await Task.Delay(50); // 模拟异步操作
        return !user.Email.Contains("duplicate");
    },
    "邮箱地址已被其他用户使用",
    nameof(DemoUser.Email)
);

var result = await user.YValidateAsync(options);
```

### 验证扩展方法演示

```csharp
// 各种扩展验证方法
bool isValidEmail = "<EMAIL>".YIsValidEmail();
bool isValidPhone = "13812345678".YIsValidPhone();
bool isInRange = 25.YIsInRange(18, 65);
bool isValidLength = "张三".YIsValidLength(2, 10);
bool isValidAge = birthDate.YIsValidAge(18, 65);
```

## 性能测试结果示例

```
⚡ 性能测试演示
========================================
📊 生成 1000 个测试用户

🔄 同步集合验证性能:
   ⏱️  耗时: 45 ms
   📊 总数: 1000
   ✅ 成功: 900
   ❌ 失败: 100
   📈 吞吐量: 22222 个/秒

⚡ 异步集合验证性能:
   ⏱️  耗时: 52 ms
   📊 总数: 1000
   ✅ 成功: 900
   ❌ 失败: 100
   📈 吞吐量: 19230 个/秒

🔄 单个实体验证性能 (10000 次):
   ⏱️  总耗时: 125 ms
   📈 平均耗时: 0.013 ms/次
   📈 吞吐量: 80000 次/秒
```

## 项目结构

```
Zylo.YData.Demo/
├── Models/
│   └── DemoModels.cs          # 演示用的数据模型
├── Services/
│   ├── IDemoService.cs        # 演示服务接口
│   └── DemoService.cs         # 演示服务实现
├── Program.cs                 # 程序入口点
├── Zylo.YData.Demo.csproj     # 项目文件
└── README.md                  # 本文档
```

## 依赖项

- **Zylo.YData** - 数据验证扩展库
- **Microsoft.Extensions.Hosting** - 主机服务
- **Microsoft.Extensions.Logging.Console** - 控制台日志

## 注意事项

1. **数据库文件**: 程序会在运行目录创建 `demo.db` SQLite 数据库文件
2. **性能测试**: 性能测试结果可能因硬件配置而异
3. **交互式操作**: 每个演示之间需要按键继续
4. **错误处理**: 程序包含完整的错误处理和日志记录

## 扩展演示

您可以通过修改 `DemoService.cs` 来添加自己的演示场景：

```csharp
// 添加新的演示方法
public async Task RunMyCustomDemoAsync()
{
    // 您的自定义演示代码
}

// 在 RunAllDemosAsync 中调用
await RunMyCustomDemoAsync();
```

## 技术支持

如果您在运行演示过程中遇到问题，请检查：

1. **.NET 版本**: 确保安装了 .NET 6.0 或 8.0
2. **依赖项**: 运行 `dotnet restore` 确保所有依赖项已安装
3. **权限**: 确保程序有权限在运行目录创建数据库文件

---

*这个演示项目展示了 Zylo.YData 数据验证扩展的完整功能。通过运行演示，您可以快速了解如何在实际项目中使用这些强大的验证功能。*
