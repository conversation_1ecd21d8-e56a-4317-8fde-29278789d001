using Microsoft.Extensions.Logging;
using System.Diagnostics;
using Zylo.YData.Demo.Models;
using Zylo.YData.Extensions;
using Zylo.YData.Models;

namespace Zylo.YData.Demo.Services;

/// <summary>
/// 演示服务实现
/// </summary>
public class DemoService : IDemoService
{
    private readonly ILogger<DemoService> _logger;

    public DemoService(ILogger<DemoService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 运行所有演示
    /// </summary>
    public async Task RunAllDemosAsync()
    {
        _logger.LogInformation("开始运行 Zylo.YData 数据验证扩展演示");

        // 配置数据库
        YData.ConfigureAuto("Data Source=demo.db", YDataType.Sqlite);
        _logger.LogInformation("✅ 数据库配置完成");

        var demos = new (string name, Func<Task> demo)[]
        {
            ("基础验证演示", RunBasicValidationDemoAsync),
            ("集合验证演示", RunCollectionValidationDemoAsync),
            ("自定义验证规则演示", RunCustomValidationDemoAsync),
            ("验证扩展方法演示", () => { RunValidationExtensionsDemo(); return Task.CompletedTask; }),
            ("性能测试演示", RunPerformanceTestDemoAsync),
            ("实际业务场景演示", RunBusinessScenarioDemoAsync)
        };

        for (int i = 0; i < demos.Length; i++)
        {
            var (name, demo) = demos[i];

            Console.WriteLine($"\n📋 演示 {i + 1}/{demos.Length}: {name}");
            Console.WriteLine(new string('=', 60));

            try
            {
                await demo();
                Console.WriteLine($"✅ {name} 完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ {name} 失败: {ex.Message}");
                _logger.LogError(ex, "演示 {DemoName} 执行失败", name);
            }

            if (i < demos.Length - 1)
            {
                Console.WriteLine("\n等待 2 秒后继续下一个演示...");
                await Task.Delay(2000);
            }
        }
    }

    /// <summary>
    /// 基础验证演示
    /// </summary>
    public async Task RunBasicValidationDemoAsync()
    {
        Console.WriteLine("🔍 基础验证演示");
        Console.WriteLine(new string('-', 40));

        // 创建有效用户
        var validUser = new DemoUser
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 25,
            Phone = "***********",
            Remarks = "这是一个有效的用户"
        };

        Console.WriteLine($"📝 测试用户: {validUser}");

        // 同步验证
        var syncResult = validUser.YValidate();
        Console.WriteLine($"🔄 同步验证结果: {(syncResult.IsValid ? "✅ 通过" : "❌ 失败")}");

        // 异步验证
        var asyncResult = await validUser.YValidateAsync();
        Console.WriteLine($"⚡ 异步验证结果: {(asyncResult.IsValid ? "✅ 通过" : "❌ 失败")}");

        // 创建无效用户
        var invalidUser = new DemoUser
        {
            Name = "", // 必填字段为空
            Email = "invalid-email", // 邮箱格式错误
            Age = -1, // 年龄无效
            Phone = "123", // 手机号格式可能有问题
            Remarks = new string('x', 600) // 超过长度限制
        };

        Console.WriteLine($"\n📝 测试无效用户: 姓名为空, 邮箱格式错误, 年龄-1, 备注超长");

        // 验证无效用户
        var invalidResult = invalidUser.YValidate();
        Console.WriteLine($"🔄 无效用户验证: {(invalidResult.IsValid ? "✅ 通过" : "❌ 失败")}");

        if (!invalidResult.IsValid)
        {
            Console.WriteLine("📋 验证错误详情:");
            foreach (var error in invalidResult.Errors)
            {
                Console.WriteLine($"   • {error.PropertyName}: {error.ErrorMessage}");
            }
        }
    }

    /// <summary>
    /// 集合验证演示
    /// </summary>
    public async Task RunCollectionValidationDemoAsync()
    {
        Console.WriteLine("📊 集合验证演示");
        Console.WriteLine(new string('-', 40));

        var users = new List<DemoUser>
        {
            new DemoUser { Name = "张三", Email = "<EMAIL>", Age = 25, Phone = "***********" },
            new DemoUser { Name = "", Email = "invalid", Age = -1, Phone = "123" }, // 无效
            new DemoUser { Name = "李四", Email = "<EMAIL>", Age = 30, Phone = "13987654321" },
            new DemoUser { Name = "王五", Email = "<EMAIL>", Age = 28, Phone = "13555666777" },
            new DemoUser { Name = "赵六", Email = "<EMAIL>", Age = 35, Phone = "13444555666" }
        };

        Console.WriteLine($"📝 测试用户集合 ({users.Count} 个用户):");
        for (int i = 0; i < users.Count; i++)
        {
            var user = users[i];
            var status = (i == 1) ? "❌ 无效" : "✅ 有效";
            Console.WriteLine($"   {i + 1}. {user.Name} ({user.Email}) - {status}");
        }

        // 同步集合验证
        var collectionResult = users.YValidateCollection();
        Console.WriteLine($"\n🔄 同步集合验证结果:");
        Console.WriteLine($"   📊 总数: {collectionResult.TotalCount}");
        Console.WriteLine($"   ✅ 成功: {collectionResult.SuccessCount}");
        Console.WriteLine($"   ❌ 失败: {collectionResult.FailureCount}");
        Console.WriteLine($"   🎯 全部有效: {(collectionResult.IsAllValid ? "是" : "否")}");

        // 异步集合验证
        var asyncCollectionResult = await users.YValidateCollectionAsync();
        Console.WriteLine($"⚡ 异步集合验证: 成功 {asyncCollectionResult.SuccessCount}/{asyncCollectionResult.TotalCount}");

        // 显示失败的用户
        var failedUsers = collectionResult.GetFailedEntities();
        if (failedUsers.Any())
        {
            Console.WriteLine("\n📋 验证失败的用户:");
            foreach (var user in failedUsers)
            {
                Console.WriteLine($"   • {user.Name} ({user.Email})");

                // 获取该用户的验证错误
                var userResult = collectionResult.Results.FirstOrDefault(r => r.Entity.Equals(user));
                if (userResult?.ValidationResult != null && !userResult.ValidationResult.IsValid)
                {
                    foreach (var error in userResult.ValidationResult.Errors)
                    {
                        Console.WriteLine($"     - {error.PropertyName}: {error.ErrorMessage}");
                    }
                }
            }
        }
    }

    /// <summary>
    /// 自定义验证规则演示
    /// </summary>
    public async Task RunCustomValidationDemoAsync()
    {
        Console.WriteLine("🔧 自定义验证规则演示");
        Console.WriteLine(new string('-', 40));

        // 创建自定义验证选项
        var customOptions = new YValidationOptions();

        // 添加自定义规则：年龄必须在18-65之间（工作年龄）
        customOptions.AddRule<DemoUser>(
            user => user.Age >= 18 && user.Age <= 65,
            "用户年龄必须在工作年龄范围内(18-65岁)",
            nameof(DemoUser.Age)
        );

        // 添加自定义规则：姓名不能包含数字
        customOptions.AddRule<DemoUser>(
            user => !string.IsNullOrEmpty(user.Name) && !user.Name.Any(char.IsDigit),
            "姓名不能包含数字",
            nameof(DemoUser.Name)
        );

        // 添加异步规则：模拟检查邮箱唯一性
        customOptions.AddAsyncRule<DemoUser>(
            async user =>
            {
                // 模拟异步数据库查询
                await Task.Delay(50);
                // 假设包含 "duplicate" 的邮箱已存在
                return !user.Email.Contains("duplicate", StringComparison.OrdinalIgnoreCase);
            },
            "邮箱地址已被其他用户使用",
            nameof(DemoUser.Email)
        );

        Console.WriteLine("📋 自定义验证规则:");
        Console.WriteLine("   1. 年龄必须在18-65岁之间");
        Console.WriteLine("   2. 姓名不能包含数字");
        Console.WriteLine("   3. 邮箱不能包含 'duplicate'");

        // 测试用户
        var testUsers = new[]
        {
            new DemoUser { Name = "张三", Email = "<EMAIL>", Age = 25, Phone = "***********" }, // 有效
            new DemoUser { Name = "李四4", Email = "<EMAIL>", Age = 30, Phone = "13987654321" }, // 姓名包含数字
            new DemoUser { Name = "王五", Email = "<EMAIL>", Age = 16, Phone = "13555666777" }, // 年龄不符合，邮箱重复
            new DemoUser { Name = "赵六", Email = "<EMAIL>", Age = 70, Phone = "13444555666" } // 年龄超出范围
        };

        Console.WriteLine($"\n📝 测试用户 ({testUsers.Length} 个):");
        for (int i = 0; i < testUsers.Length; i++)
        {
            var user = testUsers[i];
            Console.WriteLine($"\n   用户 {i + 1}: {user.Name} ({user.Email}), 年龄: {user.Age}");

            var result = await user.YValidateAsync(customOptions);
            Console.WriteLine($"   验证结果: {(result.IsValid ? "✅ 通过" : "❌ 失败")}");

            if (!result.IsValid)
            {
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"     - {error.PropertyName}: {error.ErrorMessage}");
                }
            }
        }
    }

    /// <summary>
    /// 验证扩展方法演示
    /// </summary>
    public void RunValidationExtensionsDemo()
    {
        Console.WriteLine("🛠️ 验证扩展方法演示");
        Console.WriteLine(new string('-', 40));

        // 邮箱验证
        var emails = new[] { "<EMAIL>", "<EMAIL>", "invalid-email", "user@", "@domain.com", "", null };
        Console.WriteLine("📧 邮箱格式验证:");
        foreach (var email in emails)
        {
            var isValid = email.YIsValidEmail();
            var displayEmail = email ?? "null";
            Console.WriteLine($"   '{displayEmail}' => {(isValid ? "✅ 有效" : "❌ 无效")}");
        }

        // 手机号验证
        var phones = new[] { "***********", "15987654321", "18666777888", "12345678901", "1381234567", "***********9", "" };
        Console.WriteLine("\n📱 手机号格式验证:");
        foreach (var phone in phones)
        {
            var isValid = phone.YIsValidPhone();
            Console.WriteLine($"   '{phone}' => {(isValid ? "✅ 有效" : "❌ 无效")}");
        }

        // 数值范围验证
        var ages = new[] { 25, 17, 18, 65, 70, -1, 0 };
        Console.WriteLine("\n🔢 年龄范围验证 (18-65):");
        foreach (var age in ages)
        {
            var isValid = age.YIsInRange(18, 65);
            Console.WriteLine($"   {age} => {(isValid ? "✅ 有效" : "❌ 无效")}");
        }

        // 字符串长度验证
        var names = new[] { "张三", "李", "王五六七八九十一二三四五", "", "AB", "张三李四" };
        Console.WriteLine("\n📏 姓名长度验证 (2-10):");
        foreach (var name in names)
        {
            var isValid = name.YIsValidLength(2, 10);
            Console.WriteLine($"   '{name}' (长度:{name.Length}) => {(isValid ? "✅ 有效" : "❌ 无效")}");
        }

        // 日期年龄验证
        var birthDates = new[]
        {
            new DateTime(1990, 1, 1),
            new DateTime(2010, 1, 1),
            new DateTime(1950, 1, 1),
            new DateTime(2005, 1, 1)
        };

        Console.WriteLine("\n📅 出生日期年龄验证 (18-65岁):");
        foreach (var birthDate in birthDates)
        {
            var isValid = birthDate.YIsValidAge(18, 65);
            var currentAge = DateTime.Now.Year - birthDate.Year;
            Console.WriteLine($"   {birthDate:yyyy-MM-dd} (约{currentAge}岁) => {(isValid ? "✅ 有效" : "❌ 无效")}");
        }
    }

    /// <summary>
    /// 性能测试演示
    /// </summary>
    public async Task RunPerformanceTestDemoAsync()
    {
        Console.WriteLine("⚡ 性能测试演示");
        Console.WriteLine(new string('-', 40));

        // 生成测试数据
        var testUsers = GenerateTestUsers(1000);
        Console.WriteLine($"📊 生成 {testUsers.Count} 个测试用户");

        // 同步验证性能测试
        var sw = Stopwatch.StartNew();
        var syncResults = testUsers.YValidateCollection();
        sw.Stop();

        Console.WriteLine($"\n🔄 同步集合验证性能:");
        Console.WriteLine($"   ⏱️  耗时: {sw.ElapsedMilliseconds} ms");
        Console.WriteLine($"   📊 总数: {syncResults.TotalCount}");
        Console.WriteLine($"   ✅ 成功: {syncResults.SuccessCount}");
        Console.WriteLine($"   ❌ 失败: {syncResults.FailureCount}");
        Console.WriteLine($"   📈 吞吐量: {(double)syncResults.TotalCount / sw.ElapsedMilliseconds * 1000:F0} 个/秒");

        // 异步验证性能测试
        sw.Restart();
        var asyncResults = await testUsers.YValidateCollectionAsync();
        sw.Stop();

        Console.WriteLine($"\n⚡ 异步集合验证性能:");
        Console.WriteLine($"   ⏱️  耗时: {sw.ElapsedMilliseconds} ms");
        Console.WriteLine($"   📊 总数: {asyncResults.TotalCount}");
        Console.WriteLine($"   ✅ 成功: {asyncResults.SuccessCount}");
        Console.WriteLine($"   ❌ 失败: {asyncResults.FailureCount}");
        Console.WriteLine($"   📈 吞吐量: {(double)asyncResults.TotalCount / sw.ElapsedMilliseconds * 1000:F0} 个/秒");

        // 单个实体验证性能测试
        var singleUser = testUsers.First();
        var iterations = 10000;

        sw.Restart();
        for (int i = 0; i < iterations; i++)
        {
            singleUser.YValidate();
        }
        sw.Stop();

        Console.WriteLine($"\n🔄 单个实体验证性能 ({iterations} 次):");
        Console.WriteLine($"   ⏱️  总耗时: {sw.ElapsedMilliseconds} ms");
        Console.WriteLine($"   📈 平均耗时: {(double)sw.ElapsedMilliseconds / iterations:F3} ms/次");
        Console.WriteLine($"   📈 吞吐量: {(double)iterations / sw.ElapsedMilliseconds * 1000:F0} 次/秒");
    }

    /// <summary>
    /// 实际业务场景演示
    /// </summary>
    public async Task RunBusinessScenarioDemoAsync()
    {
        Console.WriteLine("💼 实际业务场景演示");
        Console.WriteLine(new string('-', 40));

        Console.WriteLine("📋 场景1: 用户注册验证");
        await UserRegistrationScenario();

        Console.WriteLine("\n📋 场景2: 产品信息验证");
        await ProductValidationScenario();

        Console.WriteLine("\n📋 场景3: 订单创建验证");
        await OrderCreationScenario();
    }

    /// <summary>
    /// 用户注册场景
    /// </summary>
    private async Task UserRegistrationScenario()
    {
        var registrationData = new DemoUser
        {
            Name = "新用户",
            Email = "<EMAIL>",
            Age = 25,
            Phone = "***********",
            Remarks = "通过网站注册"
        };

        Console.WriteLine($"   📝 注册用户: {registrationData.Name} ({registrationData.Email})");

        // 基础验证
        var basicResult = registrationData.YValidate();
        Console.WriteLine($"   🔍 基础验证: {(basicResult.IsValid ? "✅ 通过" : "❌ 失败")}");

        if (basicResult.IsValid)
        {
            // 业务规则验证
            var businessOptions = new YValidationOptions();
            businessOptions.AddAsyncRule<DemoUser>(
                async user =>
                {
                    // 模拟检查邮箱是否已注册
                    await Task.Delay(20);
                    return !user.Email.Contains("existing");
                },
                "该邮箱已被注册",
                nameof(DemoUser.Email)
            );

            var businessResult = await registrationData.YValidateAsync(businessOptions);
            Console.WriteLine($"   💼 业务验证: {(businessResult.IsValid ? "✅ 通过" : "❌ 失败")}");

            if (businessResult.IsValid)
            {
                Console.WriteLine($"   🎉 用户注册成功！");
            }
        }
    }

    /// <summary>
    /// 产品验证场景
    /// </summary>
    private async Task ProductValidationScenario()
    {
        var products = new List<DemoProduct>
        {
            new DemoProduct { Name = "iPhone 15", Description = "最新款苹果手机", Price = 7999, Stock = 100, Category = "手机" },
            new DemoProduct { Name = "", Description = "无名产品", Price = -100, Stock = -5, Category = "" }, // 无效
            new DemoProduct { Name = "MacBook Pro", Description = "专业笔记本电脑", Price = 15999, Stock = 50, Category = "电脑" }
        };

        Console.WriteLine($"   📦 验证 {products.Count} 个产品");

        var result = await products.YValidateCollectionAsync();
        Console.WriteLine($"   📊 验证结果: {result.SuccessCount}/{result.TotalCount} 个产品有效");

        var failedProducts = result.GetFailedEntities();
        if (failedProducts.Any())
        {
            Console.WriteLine($"   ❌ 无效产品:");
            foreach (var product in failedProducts)
            {
                Console.WriteLine($"     • {product.Name} - 存在验证错误");
            }
        }
    }

    /// <summary>
    /// 订单创建场景
    /// </summary>
    private async Task OrderCreationScenario()
    {
        var order = new DemoOrder
        {
            OrderNo = "ORD" + DateTime.Now.ToString("yyyyMMddHHmmss"),
            UserId = 1001,
            TotalAmount = 299.99m,
            Status = "待支付",
            Remarks = "在线下单"
        };

        Console.WriteLine($"   📋 创建订单: {order.OrderNo}");

        // 订单验证
        var orderOptions = new YValidationOptions();
        orderOptions.AddRule<DemoOrder>(
            o => o.TotalAmount >= 0.01m,
            "订单金额必须大于0.01元",
            nameof(DemoOrder.TotalAmount)
        );

        orderOptions.AddRule<DemoOrder>(
            o => new[] { "待支付", "已支付", "已发货", "已完成", "已取消" }.Contains(o.Status),
            "订单状态无效",
            nameof(DemoOrder.Status)
        );

        var result = await order.YValidateAsync(orderOptions);
        Console.WriteLine($"   🔍 订单验证: {(result.IsValid ? "✅ 通过" : "❌ 失败")}");

        if (result.IsValid)
        {
            Console.WriteLine($"   🎉 订单创建成功！订单号: {order.OrderNo}");
        }
    }

    /// <summary>
    /// 生成测试用户数据
    /// </summary>
    private List<DemoUser> GenerateTestUsers(int count)
    {
        var random = new Random();
        var users = new List<DemoUser>();
        var names = new[] { "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十" };
        var domains = new[] { "example.com", "test.com", "demo.org", "sample.net" };

        for (int i = 0; i < count; i++)
        {
            var name = names[random.Next(names.Length)] + i;
            var email = $"user{i}@{domains[random.Next(domains.Length)]}";
            var age = random.Next(16, 70);
            var phone = $"138{random.Next(10000000, 99999999)}";

            // 故意制造一些无效数据
            if (i % 10 == 0) // 10% 的数据是无效的
            {
                name = ""; // 姓名为空
                email = "invalid-email"; // 邮箱格式错误
                age = -1; // 年龄无效
            }

            users.Add(new DemoUser
            {
                Id = i + 1,
                Name = name,
                Email = email,
                Age = age,
                Phone = phone,
                IsActive = true
            });
        }

        return users;
    }
}
