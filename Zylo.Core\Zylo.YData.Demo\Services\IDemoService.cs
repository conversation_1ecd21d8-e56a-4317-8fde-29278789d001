namespace Zylo.YData.Demo.Services;

/// <summary>
/// 演示服务接口
/// </summary>
public interface IDemoService
{
    /// <summary>
    /// 运行所有演示
    /// </summary>
    Task RunAllDemosAsync();
    
    /// <summary>
    /// 基础验证演示
    /// </summary>
    Task RunBasicValidationDemoAsync();
    
    /// <summary>
    /// 集合验证演示
    /// </summary>
    Task RunCollectionValidationDemoAsync();
    
    /// <summary>
    /// 自定义验证规则演示
    /// </summary>
    Task RunCustomValidationDemoAsync();
    
    /// <summary>
    /// 验证扩展方法演示
    /// </summary>
    void RunValidationExtensionsDemo();
    
    /// <summary>
    /// 性能测试演示
    /// </summary>
    Task RunPerformanceTestDemoAsync();
    
    /// <summary>
    /// 实际业务场景演示
    /// </summary>
    Task RunBusinessScenarioDemoAsync();
}
