using Zylo.YData;
using Zylo.YData.Demo.Models;
using Zylo.YData.Examples;
using Zylo.YData.Helpers;
using Zylo.YData.Extensions;

namespace Zylo.YData.Demo;

/// <summary>
/// YConfigHelper 零参数配置和多数据库切换功能演示
/// </summary>
public static class YConfigHelperDemo
{
    /// <summary>
    /// 演示零参数配置功能
    /// </summary>
    public static async Task DemoZeroParameterConfiguration()
    {
        Console.WriteLine("=== YConfigHelper 零参数配置演示 ===\n");

        try
        {
            // 测试1：创建默认配置文件
            Console.WriteLine("1. 创建默认 appsettings.json 配置文件...");
            var success = YConfigHelper.CreateDefaultConfig("Data Source=demo.db");
            Console.WriteLine($"   创建结果: {(success ? "成功" : "失败")}");

            if (success)
            {
                Console.WriteLine("   配置文件内容预览:");
                if (File.Exists("appsettings.json"))
                {
                    var content = await File.ReadAllTextAsync("appsettings.json");
                    Console.WriteLine($"   {content.Substring(0, Math.Min(200, content.Length))}...");
                }
            }

            // 测试2：读取连接字符串
            Console.WriteLine("\n2. 读取连接字符串...");
            var connectionString = YConfigHelper.GetConnectionString();
            Console.WriteLine($"   当前连接字符串: {connectionString}");

            // 测试3：零参数启动 YData
            Console.WriteLine("\n3. 使用零参数配置启动 YData...");
            if (!string.IsNullOrEmpty(connectionString))
            {
                YData.ConfigureAuto(); // 自动读取 appsettings.json
                Console.WriteLine("   YData 配置成功！");

                // 测试基础操作
                await TestBasicOperations();
            }
            else
            {
                Console.WriteLine("   警告：未找到连接字符串，使用内存数据库");
                YData.ConfigureAuto(); // 零参数，使用内存数据库
                await TestBasicOperations();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 零参数配置测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示多数据库切换功能
    /// </summary>
    public static async Task DemoMultiDatabaseSwitching()
    {
        Console.WriteLine("\n=== 多数据库切换功能演示 ===\n");

        try
        {
            // 测试1：切换到 SQLite 数据库
            Console.WriteLine("1. 切换到 SQLite 数据库...");
            var success1 = YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "sqlite_demo.db" });
            Console.WriteLine($"   切换结果: {(success1 ? "成功" : "失败")}");

            if (success1)
            {
                YData.ConfigureAuto(); // 重新配置
                await TestDatabaseOperations("SQLite");
            }

            // 测试2：切换到另一个 SQLite 数据库
            Console.WriteLine("\n2. 切换到另一个 SQLite 数据库...");
            var success2 = YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "sqlite_demo2.db" });
            Console.WriteLine($"   切换结果: {(success2 ? "成功" : "失败")}");

            if (success2)
            {
                YData.ConfigureAuto(); // 重新配置
                await TestDatabaseOperations("SQLite-2");
            }

            // 测试3：显示所有连接字符串
            Console.WriteLine("\n3. 查看所有连接字符串...");
            var allConnections = YConfigHelper.GetAllConnectionStrings();
            foreach (var conn in allConnections)
            {
                Console.WriteLine($"   {conn.Key}: {conn.Value}");
            }

            // 测试4：备份配置
            Console.WriteLine("\n4. 备份当前配置...");
            var backupFile = YConfigHelper.BackupConfig();
            Console.WriteLine($"   备份文件: {backupFile ?? "备份失败"}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 多数据库切换测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 演示多数据库是否还有用
    /// </summary>
    public static async Task DemoMultiDatabaseUsefulnessTest()
    {
        Console.WriteLine("\n=== 多数据库功能实用性测试 ===\n");

        try
        {
            // 场景1：主数据库 + 日志数据库
            Console.WriteLine("场景1：主数据库 + 日志数据库");

            // 设置主数据库
            YConfigHelper.SetConnectionString("Data Source=main.db", "DefaultConnection");
            YConfigHelper.SetConnectionString("Data Source=logs.db", "LogsConnection");

            // 使用主数据库
            YData.ConfigureAuto(); // 使用 DefaultConnection
            await InsertTestUser("主数据库用户");

            // 切换到日志数据库
            YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "logs.db" });
            YData.ConfigureAuto();
            await InsertTestLog("用户操作日志");

            Console.WriteLine("   ✅ 主数据库和日志数据库切换成功");

            // 场景2：开发环境 vs 测试环境
            Console.WriteLine("\n场景2：环境切换测试");

            // 开发环境
            YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "dev.db" });
            YData.ConfigureAuto();
            await InsertTestUser("开发环境用户");

            // 测试环境
            YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "test.db" });
            YData.ConfigureAuto();
            await InsertTestUser("测试环境用户");

            Console.WriteLine("   ✅ 环境切换功能正常");

            // 结论
            Console.WriteLine("\n📊 结论分析:");
            Console.WriteLine("   ✅ 多数据库切换功能仍然有用！");
            Console.WriteLine("   📌 适用场景:");
            Console.WriteLine("      - 主数据库 + 日志数据库分离");
            Console.WriteLine("      - 开发/测试/生产环境切换");
            Console.WriteLine("      - 多租户系统（每个租户独立数据库）");
            Console.WriteLine("      - 数据备份和迁移");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 多数据库实用性测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试基础数据库操作
    /// </summary>
    private static async Task TestBasicOperations()
    {
        try
        {
            // 创建测试用户
            var user = new DemoUser
            {
                Name = "测试用户",
                Email = "<EMAIL>",
                Age = 25
            };

            await YData.InsertAsync(user);
            Console.WriteLine($"   ✅ 插入用户成功，ID: {user.Id}");

            // 查询用户
            var users = await YData.Select<DemoUser>().ToListAsync();
            Console.WriteLine($"   ✅ 查询到 {users.Count} 个用户");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 基础操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试特定数据库操作
    /// </summary>
    private static async Task TestDatabaseOperations(string dbName)
    {
        try
        {
            var user = new DemoUser
            {
                Name = $"{dbName} 用户",
                Email = $"{dbName.ToLower()}@example.com",
                Age = 30
            };

            await YData.InsertAsync(user);
            var count = await YData.Select<DemoUser>().CountAsync();
            Console.WriteLine($"   ✅ {dbName} 数据库操作成功，当前用户数: {count}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ {dbName} 数据库操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 插入测试用户
    /// </summary>
    private static async Task InsertTestUser(string name)
    {
        try
        {
            var user = new DemoUser { Name = name, Email = $"{name}@example.com", Age = 25 };
            await YData.InsertAsync(user);
            Console.WriteLine($"   ✅ 插入用户: {name}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 插入用户失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 插入测试日志
    /// </summary>
    private static async Task InsertTestLog(string message)
    {
        try
        {
            // 假设有一个 Log 实体，这里用 DemoUser 代替演示
            var log = new DemoUser { Name = "LOG", Email = message, Age = 0 };
            await YData.InsertAsync(log);
            Console.WriteLine($"   ✅ 插入日志: {message}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 插入日志失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 运行所有演示
    /// </summary>
    public static async Task RunAllDemos()
    {
        Console.WriteLine("🚀 YConfigHelper 功能演示开始\n");

        await DemoZeroParameterConfiguration();
        await DemoMultiDatabaseSwitching();
        await DemoMultiDatabaseUsefulnessTest();

        Console.WriteLine("\n🎉 YConfigHelper 功能演示完成！");
    }
}
