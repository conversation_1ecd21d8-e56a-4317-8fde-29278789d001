using System.IO;

namespace Zylo.YData.Tests;

/// <summary>
/// 基础功能测试
/// </summary>
[Collection("YData Tests")]
public class BasicFunctionalityTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _testDbPath;

    public BasicFunctionalityTests(ITestOutputHelper output)
    {
        _output = output;
        // 为每个测试实例创建独立的数据库文件
        _testDbPath = Path.Combine(Path.GetTempPath(), $"test_basic_{Guid.NewGuid():N}.db");

        // 配置独立的 FreeSql 实例
        YData.ConfigureAuto($"Data Source={_testDbPath}");
    }

    public void Dispose()
    {
        try
        {
            // 清理测试数据库文件
            if (File.Exists(_testDbPath))
            {
                File.Delete(_testDbPath);
            }
        }
        catch
        {
            // 忽略清理错误
        }
    }

    /// <summary>
    /// 测试静态 API 配置
    /// </summary>
    [Fact]
    public void Test_StaticApi_Configuration()
    {
        // Arrange & Act - 使用当前实例的数据库配置
        var exception = Record.Exception(() =>
        {
            // 配置已经在构造函数中完成，这里只是验证
            Assert.NotNull(YData.FreeSql);
        });

        // Assert
        Assert.Null(exception);
        Assert.NotNull(YData.FreeSql);

        _output.WriteLine("✅ 静态 API 配置测试通过");
    }

    /// <summary>
    /// 测试依赖注入配置
    /// </summary>
    [Fact]
    public void Test_DependencyInjection_Configuration()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());

        // Act
        var exception = Record.Exception(() =>
        {
            services.AddYDataAuto("Data Source=:memory:");
        });

        // Assert
        Assert.Null(exception);

        var serviceProvider = services.BuildServiceProvider();
        var context = serviceProvider.GetService<IYDataContext>();
        var freeSql = serviceProvider.GetService<IFreeSql>();

        Assert.NotNull(context);
        Assert.NotNull(freeSql);

        _output.WriteLine("✅ 依赖注入配置测试通过");
    }

    /// <summary>
    /// 测试数据库类型检测
    /// </summary>
    [Theory]
    [InlineData("Data Source=test.db", "SQLite")]
    [InlineData("Data Source=:memory:", "SQLite")]
    [InlineData("Server=.;Database=TestDB;Trusted_Connection=true;", "SQL Server")]
    [InlineData("Host=localhost;Database=testdb;Username=user;Password=****", "PostgreSQL")]
    [InlineData("Server=localhost;Database=testdb;Uid=user;Pwd=****", "MySQL")]
    public void Test_DatabaseType_Detection(string connectionString, string expectedType)
    {
        // Arrange & Act
        var exception = Record.Exception(() =>
        {
            var services = new ServiceCollection();
            services.AddYDataAuto(connectionString);
        });

        // Assert
        Assert.Null(exception);
        _output.WriteLine($"✅ 数据库类型检测成功: {connectionString} -> {expectedType}");
    }

    /// <summary>
    /// 测试基础 CRUD 操作
    /// </summary>
    [Fact]
    public async Task Test_Basic_CRUD_Operations()
    {
        // Arrange
        YData.ConfigureAuto("Data Source=:memory:", YDataType.Sqlite);

        // 创建表结构
        var freeSql = YData.FreeSql;
        freeSql.CodeFirst.SyncStructure<User>();

        var user = new User
        {
            Name = "测试用户",
            Email = "<EMAIL>",
            Age = 25,
            IsActive = true
        };

        // Act & Assert - 插入
        var insertResult = await YData.InsertAsync(user);
        Assert.True(insertResult > 0);
        Assert.True(user.Id > 0);
        _output.WriteLine($"✅ 插入成功，用户ID: {user.Id}");

        // Act & Assert - 查询
        var retrievedUser = await YData.GetAsync<User>(user.Id);
        Assert.NotNull(retrievedUser);
        Assert.Equal(user.Name, retrievedUser.Name);
        Assert.Equal(user.Email, retrievedUser.Email);
        _output.WriteLine($"✅ 查询成功: {retrievedUser.Name}");

        // Act & Assert - 更新
        retrievedUser.Age = 26;
        retrievedUser.UpdateTime = DateTime.Now;
        var updateResult = await YData.UpdateAsync(retrievedUser);
        Assert.True(updateResult > 0);
        _output.WriteLine($"✅ 更新成功，新年龄: {retrievedUser.Age}");

        // Act & Assert - 删除
        var deleteResult = await YData.DeleteAsync<User>(user.Id);
        Assert.True(deleteResult > 0);
        _output.WriteLine($"✅ 删除成功");

        // 验证删除
        var deletedUser = await YData.GetAsync<User>(user.Id);
        Assert.Null(deletedUser);
        _output.WriteLine($"✅ 删除验证成功");
    }

    /// <summary>
    /// 测试查询功能
    /// </summary>
    [Fact]
    public async Task Test_Query_Operations()
    {
        // Arrange
        YData.ConfigureAuto("Data Source=:memory:");
        var freeSql = YData.FreeSql;
        freeSql.CodeFirst.SyncStructure<User>();

        // 插入测试数据
        var users = new[]
        {
            new User { Name = "用户1", Email = "<EMAIL>", Age = 20, IsActive = true },
            new User { Name = "用户2", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "用户3", Email = "<EMAIL>", Age = 30, IsActive = false }
        };

        foreach (var user in users)
        {
            await YData.InsertAsync(user);
        }

        // Act & Assert - 获取所有
        var allUsers = await YData.GetAllAsync<User>();
        Assert.Equal(3, allUsers.Count);
        _output.WriteLine($"✅ 获取所有用户: {allUsers.Count} 个");

        // Act & Assert - 条件查询
        var activeUsers = await YData.Select<User>()
            .Where(u => u.IsActive)
            .ToListAsync();
        Assert.Equal(2, activeUsers.Count);
        _output.WriteLine($"✅ 活跃用户查询: {activeUsers.Count} 个");

        // Act & Assert - 分页查询
        var pagedResult = await YData.GetPagedAsync<User>(1, 2, u => u.IsActive);
        Assert.Equal(2, pagedResult.TotalCount);
        Assert.Equal(2, pagedResult.Items.Count);
        Assert.Equal(1, pagedResult.TotalPages);
        _output.WriteLine($"✅ 分页查询: 第{pagedResult.PageIndex}页，共{pagedResult.TotalCount}条");
    }

    /// <summary>
    /// 测试事务功能
    /// </summary>
    [Fact]
    public async Task Test_Transaction_Operations()
    {
        // Arrange
        YData.ConfigureAuto("Data Source=:memory:", YDataType.Sqlite);
        var freeSql = YData.FreeSql;
        freeSql.CodeFirst.SyncStructure<User>();
        freeSql.CodeFirst.SyncStructure<Order>();

        // Act & Assert - 成功事务
        var result = await YData.TransactionAsync(async () =>
        {
            var user = new User
            {
                Name = "事务用户",
                Email = "<EMAIL>",
                Age = 30,
                IsActive = true
            };
            await YData.InsertAsync(user);

            var order = new Order
            {
                UserId = user.Id,
                OrderNo = $"ORD{DateTime.Now:yyyyMMddHHmmss}",
                Amount = 99.99m,
                Status = OrderStatus.Pending
            };
            await YData.InsertAsync(order);

            return new { UserId = user.Id, OrderId = order.Id };
        });

        Assert.NotNull(result);
        Assert.True(result.UserId > 0);
        Assert.True(result.OrderId > 0);
        _output.WriteLine($"✅ 事务成功: 用户ID={result.UserId}, 订单ID={result.OrderId}");

        // 验证数据存在
        var user = await YData.GetAsync<User>(result.UserId);
        var order = await YData.GetAsync<Order>(result.OrderId);
        Assert.NotNull(user);
        Assert.NotNull(order);
        _output.WriteLine($"✅ 事务数据验证成功");
    }
}
