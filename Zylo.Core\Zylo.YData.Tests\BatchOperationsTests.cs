using Xunit;
using Xunit.Abstractions;
using FreeSql;
using FreeSql.DataAnnotations;
using Zylo.YData.Extensions;
using static Zylo.YData.Extensions.YDataExtensions;
using System.Threading;

namespace Zylo.YData.Tests;

/// <summary>
/// 批量操作扩展方法测试
/// </summary>
[Collection("YData Tests")]
public class BatchOperationsTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _dbFile;
    private readonly IFreeSql _freeSql;

    public BatchOperationsTests(ITestOutputHelper output)
    {
        _output = output;
        _dbFile = $"test_{Guid.NewGuid():N}.db";

        // 使用内存数据库避免文件锁定问题
        var connectionString = "Data Source=:memory:";

        // 创建独立的 FreeSql 实例
        _freeSql = new FreeSqlBuilder()
            .UseConnectionString(DataType.Sqlite, connectionString)
            .UseAutoSyncStructure(true)
            .Build();

        // 配置 YData 使用内存数据库
        YData.ConfigureAuto(connectionString, YDataType.Sqlite);

        // 确保表结构存在
        _freeSql.CodeFirst.SyncStructure<TestUser>();
        _freeSql.CodeFirst.SyncStructure<TestProduct>();

        _output.WriteLine($"测试数据库已创建: 内存数据库 (避免文件锁定)");
    }

    public void Dispose()
    {
        try
        {
            // 确保 FreeSql 连接完全关闭
            _freeSql?.Dispose();

            _output.WriteLine($"✅ 测试资源已清理完成");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"⚠️ 清理资源时发生错误: {ex.Message}");
        }
    }

    #region 测试实体类

    [Table(Name = "test_users")]
    public class TestUser
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public int Id { get; set; }

        [Column(StringLength = 50)]
        public string Name { get; set; } = string.Empty;

        [Column(StringLength = 100)]
        public string Email { get; set; } = string.Empty;

        public int Age { get; set; }
        public DateTime CreateTime { get; set; } = DateTime.Now;
        public DateTime? UpdateTime { get; set; }
        public bool IsActive { get; set; } = true;
    }

    [Table(Name = "test_products")]
    public class TestProduct
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public int Id { get; set; }

        [Column(StringLength = 100)]
        public string Name { get; set; } = string.Empty;

        public decimal Price { get; set; }
        public int Stock { get; set; }
        public DateTime CreateTime { get; set; } = DateTime.Now;
    }

    #endregion

    #region 批量插入测试

    [Fact]
    public async Task YBatchInsertAsync_ShouldInsertMultipleEntities()
    {
        // Arrange
        var users = new List<TestUser>
        {
            new() { Name = "张三", Email = "<EMAIL>", Age = 25 },
            new() { Name = "李四", Email = "<EMAIL>", Age = 30 },
            new() { Name = "王五", Email = "<EMAIL>", Age = 28 }
        };

        // Act
        var result = await users.YBatchInsertAsync();

        // Assert
        Assert.Equal(3, result);

        var count = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(3, count);

        _output.WriteLine($"批量插入了 {result} 条记录");
    }

    [Fact]
    public void YBatchInsert_ShouldInsertMultipleEntities()
    {
        // Arrange
        var users = new List<TestUser>
        {
            new() { Name = "同步张三", Email = "<EMAIL>", Age = 25 },
            new() { Name = "同步李四", Email = "<EMAIL>", Age = 30 }
        };

        // Act
        var result = users.YBatchInsert();

        // Assert
        Assert.Equal(2, result);

        var count = YData.Select<TestUser>().Count();
        Assert.Equal(2, count);

        _output.WriteLine($"同步批量插入了 {result} 条记录");
    }

    [Fact]
    public async Task YBatchInsertAsync_WithEmptyList_ShouldReturnZero()
    {
        // Arrange
        var users = new List<TestUser>();

        // Act
        var result = await users.YBatchInsertAsync();

        // Assert
        Assert.Equal(0, result);
        _output.WriteLine("空列表批量插入返回 0");
    }

    [Fact]
    public async Task YBatchInsertAsync_WithNullList_ShouldThrowException()
    {
        // Arrange
        List<TestUser>? users = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => users!.YBatchInsertAsync());
        _output.WriteLine("空引用批量插入抛出异常");
    }

    #endregion

    #region 批量更新测试

    [Fact]
    public async Task YBatchUpdateAsync_ShouldUpdateMultipleEntities()
    {
        // Arrange - 先插入数据
        var users = new List<TestUser>
        {
            new() { Name = "更新前张三", Email = "<EMAIL>", Age = 25 },
            new() { Name = "更新前李四", Email = "<EMAIL>", Age = 30 }
        };
        await users.YBatchInsertAsync();

        // 获取插入的数据
        var insertedUsers = await YData.Select<TestUser>().ToListAsync();

        // 修改数据
        foreach (var user in insertedUsers)
        {
            user.Name = "更新后" + user.Name;
            user.UpdateTime = DateTime.Now;
        }

        // Act
        var result = await insertedUsers.YBatchUpdateAsync();

        // Assert
        Assert.Equal(2, result);

        var updatedUsers = await YData.Select<TestUser>().ToListAsync();
        Assert.All(updatedUsers, user =>
        {
            Assert.StartsWith("更新后", user.Name);
            Assert.NotNull(user.UpdateTime);
        });

        _output.WriteLine($"批量更新了 {result} 条记录");
    }

    [Fact]
    public void YBatchUpdate_ShouldUpdateMultipleEntities()
    {
        // Arrange - 先插入数据
        var users = new List<TestUser>
        {
            new() { Name = "同步更新前张三", Email = "<EMAIL>", Age = 25 }
        };
        users.YBatchInsert();

        // 获取插入的数据
        var insertedUsers = YData.Select<TestUser>().ToList();

        // 修改数据
        foreach (var user in insertedUsers)
        {
            user.Name = "同步更新后" + user.Name;
            user.UpdateTime = DateTime.Now;
        }

        // Act
        var result = insertedUsers.YBatchUpdate();

        // Assert
        Assert.Equal(1, result);

        var updatedUser = YData.Select<TestUser>().First();
        Assert.StartsWith("同步更新后", updatedUser.Name);
        Assert.NotNull(updatedUser.UpdateTime);

        _output.WriteLine($"同步批量更新了 {result} 条记录");
    }

    #endregion

    #region 批量删除测试

    [Fact]
    public async Task YBatchDeleteAsync_ShouldDeleteMultipleEntities()
    {
        // Arrange - 先插入数据
        var users = new List<TestUser>
        {
            new() { Name = "待删除张三", Email = "<EMAIL>", Age = 25 },
            new() { Name = "待删除李四", Email = "<EMAIL>", Age = 30 },
            new() { Name = "保留王五", Email = "<EMAIL>", Age = 28 }
        };
        await users.YBatchInsertAsync();

        // 获取要删除的数据
        var usersToDelete = await YData.Select<TestUser>()
            .Where(u => u.Name.Contains("待删除"))
            .ToListAsync();

        // Act
        var result = await usersToDelete.YBatchDeleteAsync();

        // Assert
        Assert.Equal(2, result);

        var remainingCount = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(1, remainingCount);

        var remainingUser = await YData.Select<TestUser>().FirstAsync();
        Assert.Contains("保留", remainingUser.Name);

        _output.WriteLine($"批量删除了 {result} 条记录");
    }

    [Fact]
    public async Task YBatchDeleteAsync_ByKeys_ShouldDeleteMultipleEntities()
    {
        // Arrange - 先插入数据
        var users = new List<TestUser>
        {
            new() { Name = "按ID删除1", Email = "<EMAIL>", Age = 25 },
            new() { Name = "按ID删除2", Email = "<EMAIL>", Age = 30 },
            new() { Name = "按ID保留", Email = "<EMAIL>", Age = 28 }
        };
        await users.YBatchInsertAsync();

        // 获取要删除的ID
        var userIds = await YData.Select<TestUser>()
            .Where(u => u.Name.Contains("按ID删除"))
            .ToListAsync(u => u.Id);

        // Act
        var result = await userIds.YBatchDeleteAsync<TestUser, int>();

        // Assert
        Assert.Equal(2, result);

        var remainingCount = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(1, remainingCount);

        _output.WriteLine($"按主键批量删除了 {result} 条记录");
    }

    #endregion

    #region 批量保存测试

    [Fact]
    public async Task YBatchSaveAsync_ShouldInsertAndUpdateEntities()
    {
        // Arrange - 先插入一条数据
        var existingUser = new TestUser
        {
            Name = "已存在用户",
            Email = "<EMAIL>",
            Age = 25
        };
        await YData.InsertAsync(existingUser);

        // 准备批量保存的数据（包含新增和更新）
        var users = new List<TestUser>
        {
            new() { Id = existingUser.Id, Name = "更新的用户", Email = "<EMAIL>", Age = 26 }, // 更新
            new() { Name = "新增用户1", Email = "<EMAIL>", Age = 30 }, // 新增
            new() { Name = "新增用户2", Email = "<EMAIL>", Age = 28 }  // 新增
        };

        // Act
        var result = await users.YBatchSaveAsync();

        // Assert
        Assert.Equal(3, result);

        var totalCount = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(3, totalCount); // 1个更新 + 2个新增 = 3个总数

        var updatedUser = await YData.Select<TestUser>().Where(u => u.Id == existingUser.Id).FirstAsync();
        Assert.Equal("更新的用户", updatedUser.Name);

        _output.WriteLine($"批量保存了 {result} 条记录");
    }

    #endregion

    #region 智能批量操作测试

    [Fact]
    public async Task YSmartBatchInsertAsync_WithSmallBatch_ShouldUseIndividualInserts()
    {
        // Arrange - 小批量数据（少于阈值）
        var users = new List<TestUser>
        {
            new() { Name = "智能小批量1", Email = "<EMAIL>", Age = 25 },
            new() { Name = "智能小批量2", Email = "<EMAIL>", Age = 30 }
        };

        // Act
        var result = await users.YSmartBatchInsertAsync();

        // Assert
        Assert.Equal(2, result);

        var count = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(2, count);

        _output.WriteLine($"智能小批量插入了 {result} 条记录");
    }

    [Fact]
    public async Task YSmartBatchInsertAsync_WithLargeBatch_ShouldUseBatchInsert()
    {
        // Arrange - 大批量数据（超过阈值）
        var users = new List<TestUser>();
        for (int i = 1; i <= 15; i++) // 超过默认阈值 10
        {
            users.Add(new TestUser
            {
                Name = $"智能大批量{i}",
                Email = $"smart_large_{i}@test.com",
                Age = 20 + i
            });
        }

        // Act
        var result = await users.YSmartBatchInsertAsync();

        // Assert
        Assert.Equal(15, result);

        var count = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(15, count);

        _output.WriteLine($"智能大批量插入了 {result} 条记录");
    }

    #endregion

    #region 批量操作配置测试

    [Fact]
    public void YBatchOptions_ShouldHaveDefaultValues()
    {
        // Assert
        Assert.Equal(1000, YBatchOptions.DefaultBatchSize);
        Assert.Equal(10, YBatchOptions.SmallBatchThreshold);
        Assert.True(YBatchOptions.EnableTransaction);
        Assert.Equal(300, YBatchOptions.TransactionTimeoutSeconds);

        _output.WriteLine("批量操作配置默认值正确");
    }

    [Fact]
    public void YBatchOptions_ShouldAllowCustomization()
    {
        // Arrange
        var originalBatchSize = YBatchOptions.DefaultBatchSize;
        var originalThreshold = YBatchOptions.SmallBatchThreshold;

        try
        {
            // Act
            YBatchOptions.DefaultBatchSize = 500;
            YBatchOptions.SmallBatchThreshold = 5;

            // Assert
            Assert.Equal(500, YBatchOptions.DefaultBatchSize);
            Assert.Equal(5, YBatchOptions.SmallBatchThreshold);

            _output.WriteLine("批量操作配置可以自定义");
        }
        finally
        {
            // Cleanup
            YBatchOptions.DefaultBatchSize = originalBatchSize;
            YBatchOptions.SmallBatchThreshold = originalThreshold;
        }
    }

    #endregion
}
