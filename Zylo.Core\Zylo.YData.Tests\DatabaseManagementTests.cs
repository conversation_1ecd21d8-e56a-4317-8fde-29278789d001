using Xunit;
using Xunit.Abstractions;
using Zylo.YData;
using Zylo.YData.Examples;

namespace Zylo.YData.Tests;

/// <summary>
/// 数据库管理功能测试 - 测试包装的 FreeSql 功能
/// </summary>
[Collection("YData Tests")]
public class DatabaseManagementTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _testId;

    public DatabaseManagementTests(ITestOutputHelper output)
    {
        _output = output;
        _testId = Guid.NewGuid().ToString("N")[..8];

        // 使用内存数据库进行测试
        YData.ConfigureAuto("Data Source=:memory:");

        // 创建测试表
        YData.FreeSql.CodeFirst.SyncStructure<User>();

        _output.WriteLine($"✅ 测试环境初始化完成: {_testId}");
    }

    [Fact]
    public void SyncTable_ShouldCreateTable()
    {
        // Act
        var result = YDatabaseManagementExtensions.SyncTable<User>();

        // Assert
        Assert.True(result);
        _output.WriteLine("✅ 同步表结构成功");
    }

    [Fact]
    public void TableExists_ShouldReturnTrue()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        // Act
        var exists = YDatabaseManagementExtensions.TableExists<User>();

        // Assert
        Assert.True(exists);
        _output.WriteLine("✅ 表存在检查成功");
    }

    [Fact]
    public void GetTableNames_ShouldReturnTableList()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        // Act
        var tableNames = YDatabaseManagementExtensions.GetTableNames();

        // Assert
        Assert.NotEmpty(tableNames);
        Assert.Contains("Users", tableNames); // FreeSql 默认使用复数形式
        _output.WriteLine($"✅ 获取表名列表成功: {string.Join(", ", tableNames)}");
    }

    [Fact]
    public async Task GetTableCount_ShouldReturnCorrectCount()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        var users = new[]
        {
            new User { Name = "用户1", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "用户2", Email = "<EMAIL>", Age = 30, IsActive = true }
        };

        await YData.Insert<User>().AppendData(users).ExecuteAffrowsAsync();

        // Act
        var count = YDatabaseManagementExtensions.GetTableCount<User>();

        // Assert
        Assert.Equal(2, count);
        _output.WriteLine($"✅ 获取表记录数成功: {count}");
    }

    [Fact]
    public void GetDatabaseInfo_ShouldReturnInfo()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        // Act
        var info = YDatabaseManagementExtensions.GetDatabaseInfo();

        // Assert
        Assert.NotNull(info);
        Assert.NotEmpty(info.DatabaseType);
        Assert.True(info.TableCount > 0);
        _output.WriteLine($"✅ 获取数据库信息成功: {info.DatabaseType}, {info.TableCount} 个表");
    }

    [Fact]
    public void CheckConnection_ShouldReturnTrue()
    {
        // Act
        var isConnected = YDatabaseManagementExtensions.CheckConnection();

        // Assert
        Assert.True(isConnected);
        _output.WriteLine("✅ 数据库连接检查成功");
    }

    [Fact]
    public async Task TruncateTable_ShouldClearData()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        var user = new User { Name = "测试用户", Email = "<EMAIL>", Age = 25, IsActive = true };
        await YData.InsertAsync(user);

        var countBefore = YDatabaseManagementExtensions.GetTableCount<User>();
        Assert.True(countBefore > 0);

        // Act
        var result = YDatabaseManagementExtensions.TruncateTable<User>();

        // Assert
        Assert.True(result);
        var countAfter = YDatabaseManagementExtensions.GetTableCount<User>();
        Assert.Equal(0, countAfter);
        _output.WriteLine("✅ 清空表数据成功");
    }

    [Fact]
    public void GetTableInfo_ShouldReturnTableInfo()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        // Act
        var tableInfo = YDatabaseManagementExtensions.GetTableInfo<User>();

        // Assert
        Assert.NotNull(tableInfo);
        Assert.Equal("User", tableInfo.TableName);
        Assert.True(tableInfo.ColumnCount >= 0);
        _output.WriteLine($"✅ 获取表信息成功: {tableInfo.TableName}, {tableInfo.ColumnCount} 列, {tableInfo.RecordCount} 行");
    }

    [Fact]
    public void ExecuteSql_ShouldExecuteSuccessfully()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        // Act
        var result = YDatabaseManagementExtensions.ExecuteSql("SELECT COUNT(*) FROM Users"); // 使用正确的表名

        // Assert
        Assert.True(result >= 0);
        _output.WriteLine($"✅ 执行SQL成功: 影响 {result} 行");
    }

    [Fact]
    public async Task ExportToJson_ShouldCreateFile()
    {
        // Arrange - 确保表结构存在
        YData.FreeSql.CodeFirst.SyncStructure<User>();

        var users = new[]
        {
            new User { Name = "导出用户1", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "导出用户2", Email = "<EMAIL>", Age = 30, IsActive = true }
        };

        var insertResult = await YData.Insert<User>().AppendData(users).ExecuteAffrowsAsync();
        _output.WriteLine($"📝 插入结果: {insertResult}");

        var filePath = $"test_export_{_testId}.json";

        try
        {
            // 先检查数据是否存在
            var dataCount = YDatabaseManagementExtensions.GetTableCount<User>();
            _output.WriteLine($"📊 表中数据数量: {dataCount}");

            // Act
            var count = await YDataImportExportExtensions.ExportToJsonAsync<User>(filePath);
            _output.WriteLine($"📤 导出结果: {count}");

            // Assert
            Assert.True(count > 0, $"导出失败，返回值: {count}，表中数据: {dataCount}");
            Assert.True(File.Exists(filePath), $"文件不存在: {filePath}");
            _output.WriteLine($"✅ 导出JSON成功: {count} 条记录");

            // 验证文件内容
            var content = await File.ReadAllTextAsync(filePath);
            Assert.Contains("导出用户1", content);
            Assert.Contains("<EMAIL>", content);
        }
        finally
        {
            // 清理测试文件
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }
    }

    [Fact]
    public async Task ImportFromJson_ShouldImportData()
    {
        // Arrange
        YDatabaseManagementExtensions.SyncTable<User>();

        var testData = new[]
        {
            new User { Name = "导入用户1", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "导入用户2", Email = "<EMAIL>", Age = 30, IsActive = true }
        };

        var filePath = $"test_import_{_testId}.json";

        // 先导出测试数据
        await YData.Insert<User>().AppendData(testData).ExecuteAffrowsAsync();
        await YDataImportExportExtensions.ExportToJsonAsync<User>(filePath);

        // 清空表
        YDatabaseManagementExtensions.TruncateTable<User>();

        try
        {
            // Act
            var count = await YDataImportExportExtensions.ImportFromJsonAsync<User>(filePath);

            // Assert
            Assert.True(count > 0);
            var actualCount = YDatabaseManagementExtensions.GetTableCount<User>();
            Assert.Equal(count, actualCount);
            _output.WriteLine($"✅ 导入JSON成功: {count} 条记录");
        }
        finally
        {
            // 清理测试文件
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }
    }

    [Fact]
    public async Task BackupAndRestore_ShouldWork()
    {
        // Arrange - 确保表结构存在
        YData.FreeSql.CodeFirst.SyncStructure<User>();

        var users = new[]
        {
            new User { Name = "备份用户1", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "备份用户2", Email = "<EMAIL>", Age = 30, IsActive = true }
        };

        var insertResult = await YData.Insert<User>().AppendData(users).ExecuteAffrowsAsync();
        _output.WriteLine($"📝 插入结果: {insertResult}");

        var originalCount = YDatabaseManagementExtensions.GetTableCount<User>();
        _output.WriteLine($"📊 原始数据数量: {originalCount}");

        var backupDir = $"TestBackups_{_testId}";

        try
        {
            // Act - 备份
            var backupFile = await YDataImportExportExtensions.BackupTableAsync<User>(backupDir);
            Assert.NotNull(backupFile);
            Assert.True(File.Exists(backupFile));
            _output.WriteLine($"✅ 备份成功: {backupFile}");

            // 清空表
            YDatabaseManagementExtensions.TruncateTable<User>();
            Assert.Equal(0, YDatabaseManagementExtensions.GetTableCount<User>());

            // Act - 恢复
            var restoredCount = await YDataImportExportExtensions.RestoreTableAsync<User>(backupFile);

            // Assert
            Assert.Equal(originalCount, restoredCount);
            Assert.Equal(originalCount, YDatabaseManagementExtensions.GetTableCount<User>());
            _output.WriteLine($"✅ 恢复成功: {restoredCount} 条记录");
        }
        finally
        {
            // 清理测试目录
            if (Directory.Exists(backupDir))
            {
                Directory.Delete(backupDir, true);
            }
        }
    }

    public void Dispose()
    {
        try
        {
            _output.WriteLine($"✅ 测试清理完成: {_testId}");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"⚠️ 清理时发生错误: {ex.Message}");
        }
    }
}
