using Microsoft.Extensions.Options;

namespace Zylo.YData.Tests;

/// <summary>
/// 依赖注入功能测试
/// </summary>
public class DependencyInjectionTests
{
    private readonly ITestOutputHelper _output;

    public DependencyInjectionTests(ITestOutputHelper output)
    {
        _output = output;
    }

    /// <summary>
    /// 测试服务注册
    /// </summary>
    [Fact]
    public void Test_Service_Registration()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole());

        // Act
        services.AddYDataAuto("Data Source=:memory:");
        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var context = serviceProvider.GetService<IYDataContext>();
        var freeSql = serviceProvider.GetService<IFreeSql>();
        var optionsService = serviceProvider.GetService<IOptions<YDataOptions>>();

        Assert.NotNull(context);
        Assert.NotNull(freeSql);
        Assert.NotNull(optionsService);

        var options = optionsService?.Value;
        Assert.NotNull(options);

        _output.WriteLine("✅ 服务注册测试通过");
        _output.WriteLine($"   IYDataContext: {context.GetType().Name}");
        _output.WriteLine($"   IFreeSql: {freeSql.GetType().Name}");
    }

    /// <summary>
    /// 测试配置选项
    /// </summary>
    [Fact]
    public void Test_Configuration_Options()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        services.AddYData(options =>
        {
            options.ConnectionString = "Data Source=:memory:";
            options.DataType = YDataType.Sqlite;
            options.EnableAutoSyncStructure = true;
            options.EnableMonitorCommand = true;
            options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
            options.SlowQueryThreshold = TimeSpan.FromSeconds(5);
        });

        var serviceProvider = services.BuildServiceProvider();

        // Assert
        var options = serviceProvider.GetRequiredService<IOptions<YDataOptions>>().Value;
        Assert.Equal("Data Source=:memory:", options.ConnectionString);
        Assert.Equal(YDataType.Sqlite, options.DataType);
        Assert.True(options.EnableAutoSyncStructure);
        Assert.True(options.EnableMonitorCommand);
        Assert.Equal(TimeSpan.FromSeconds(30), options.DefaultQueryTimeout);
        Assert.Equal(TimeSpan.FromSeconds(5), options.SlowQueryThreshold);

        _output.WriteLine("✅ 配置选项测试通过");
        _output.WriteLine($"   连接字符串: {options.ConnectionString}");
        _output.WriteLine($"   数据库类型: {options.DataType}");
        _output.WriteLine($"   自动同步结构: {options.EnableAutoSyncStructure}");
    }

    /// <summary>
    /// 测试服务生命周期
    /// </summary>
    [Fact]
    public void Test_Service_Lifetime()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.AddYDataAuto("Data Source=:memory:");

        var serviceProvider = services.BuildServiceProvider();

        // Act & Assert - 测试单例
        var context1 = serviceProvider.GetService<IYDataContext>();
        var context2 = serviceProvider.GetService<IYDataContext>();
        var freeSql1 = serviceProvider.GetService<IFreeSql>();
        var freeSql2 = serviceProvider.GetService<IFreeSql>();

        Assert.Same(context1, context2);
        Assert.Same(freeSql1, freeSql2);

        _output.WriteLine("✅ 服务生命周期测试通过");
        _output.WriteLine("   IYDataContext 和 IFreeSql 都是单例模式");
    }

    /// <summary>
    /// 测试在服务中使用 YData
    /// </summary>
    [Fact]
    public async Task Test_YData_In_Service()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.AddYDataAuto("Data Source=:memory:", YDataType.Sqlite);
        services.AddScoped<TestUserService>();

        var serviceProvider = services.BuildServiceProvider();

        // 初始化数据库
        var freeSql = serviceProvider.GetRequiredService<IFreeSql>();
        freeSql.CodeFirst.SyncStructure<User>();

        // Act
        using var scope = serviceProvider.CreateScope();
        var userService = scope.ServiceProvider.GetRequiredService<TestUserService>();

        var user = await userService.CreateUserAsync("测试用户", "<EMAIL>", 25);
        var retrievedUser = await userService.GetUserAsync(user.Id);
        var allUsers = await userService.GetAllUsersAsync();

        // Assert
        Assert.NotNull(user);
        Assert.True(user.Id > 0);
        Assert.NotNull(retrievedUser);
        Assert.Equal(user.Name, retrievedUser.Name);
        Assert.Single(allUsers);

        _output.WriteLine("✅ 服务中使用 YData 测试通过");
        _output.WriteLine($"   创建用户: {user.Name} (ID: {user.Id})");
        _output.WriteLine($"   查询用户: {retrievedUser.Name}");
        _output.WriteLine($"   所有用户数量: {allUsers.Count}");
    }
}

/// <summary>
/// 测试用户服务
/// </summary>
public class TestUserService
{
    private readonly IYDataContext _context;
    private readonly ILogger<TestUserService> _logger;

    public TestUserService(IYDataContext context, ILogger<TestUserService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<User> CreateUserAsync(string name, string email, int age)
    {
        var user = new User
        {
            Name = name,
            Email = email,
            Age = age,
            IsActive = true
        };

        await _context.InsertAsync(user);
        _logger.LogInformation("创建用户: {Name} (ID: {Id})", user.Name, user.Id);

        return user;
    }

    public async Task<User?> GetUserAsync(int id)
    {
        var user = await _context.GetAsync<User>(id);
        _logger.LogInformation("查询用户: ID={Id}, Found={Found}", id, user != null);

        return user;
    }

    public async Task<List<User>> GetAllUsersAsync()
    {
        var users = await _context.GetAllAsync<User>();
        _logger.LogInformation("查询所有用户: {Count} 个", users.Count);

        return users;
    }
}
