using System.IO;

namespace Zylo.YData.Tests;

/// <summary>
/// 扩展方法测试
/// </summary>
[Collection("YData Tests")]
public class ExtensionMethodsTests : IDisposable
{
    private readonly string _testDbPath;

    public ExtensionMethodsTests()
    {
        // 为每个测试实例创建独立的数据库文件
        _testDbPath = Path.Combine(Path.GetTempPath(), $"test_extensions_{Guid.NewGuid():N}.db");
        YData.ConfigureAuto($"Data Source={_testDbPath}");

        // 创建测试表
        YData.FreeSql.CodeFirst.SyncStructure<User>();

        // 插入测试数据
        var users = new[]
        {
            new User { Name = "张三", Age = 25, Email = "<EMAIL>", IsActive = true },
            new User { Name = "李四", Age = 30, Email = "<EMAIL>", IsActive = true },
            new User { Name = "王五", Age = 35, Email = "<EMAIL>", IsActive = false },
            new User { Name = "赵六", Age = 28, Email = "<EMAIL>", IsActive = true },
            new User { Name = "", Age = 20, Email = "<EMAIL>", IsActive = true }, // 空名称测试
        };

        YData.FreeSql.Insert(users).ExecuteAffrows();
    }

    #region WhereIf 测试

    [Fact]
    public async Task WhereIf_WhenConditionTrue_ShouldApplyFilter()
    {
        // Arrange
        var nameFilter = "张三";
        var shouldFilter = true;

        // Act
        var result = await YData.Select<User>()
            .WhereIf(shouldFilter, u => u.Name == nameFilter)
            .ToListAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("张三", result[0].Name);
    }

    [Fact]
    public async Task WhereIf_WhenConditionFalse_ShouldNotApplyFilter()
    {
        // Arrange
        var nameFilter = "张三";
        var shouldFilter = false;

        // Act
        var result = await YData.Select<User>()
            .WhereIf(shouldFilter, u => u.Name == nameFilter)
            .ToListAsync();

        // Assert
        Assert.True(result.Count > 1); // 应该返回所有用户
    }

    [Fact]
    public async Task WhereIf_MultipleConditions_ShouldWorkCorrectly()
    {
        // Arrange
        var nameFilter = "张";
        var minAge = 20;
        var hasNameFilter = !string.IsNullOrEmpty(nameFilter);
        var hasAgeFilter = true;

        // Act
        var result = await YData.Select<User>()
            .WhereIf(hasNameFilter, u => u.Name.Contains(nameFilter))
            .WhereIf(hasAgeFilter, u => u.Age >= minAge)
            .ToListAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("张三", result[0].Name);
        Assert.True(result[0].Age >= minAge);
    }

    #endregion

    #region WhereIfNotNull 测试

    [Fact]
    public async Task WhereIfNotNull_WhenValueHasValue_ShouldApplyFilter()
    {
        // Arrange
        int? minAge = 30;

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotNull(minAge, (query, val) => query.Where(u => u.Age >= val))
            .ToListAsync();

        // Assert
        Assert.Equal(2, result.Count); // 李四(30) 和 王五(35)
        Assert.All(result, u => Assert.True(u.Age >= 30));
    }

    [Fact]
    public async Task WhereIfNotNull_WhenValueIsNull_ShouldNotApplyFilter()
    {
        // Arrange
        int? minAge = null;

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotNull(minAge, (query, val) => query.Where(u => u.Age >= val))
            .ToListAsync();

        // Assert
        Assert.True(result.Count > 2); // 应该返回所有用户
    }

    #endregion

    #region WhereIfNotEmpty 测试

    [Fact]
    public async Task WhereIfNotEmpty_WhenValueNotEmpty_ShouldApplyFilter()
    {
        // Arrange
        var nameFilter = "李";

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotEmpty(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .ToListAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("李四", result[0].Name);
    }

    [Fact]
    public async Task WhereIfNotEmpty_WhenValueIsEmpty_ShouldNotApplyFilter()
    {
        // Arrange
        var nameFilter = "";

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotEmpty(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .ToListAsync();

        // Assert
        Assert.True(result.Count > 1); // 应该返回所有用户
    }

    [Fact]
    public async Task WhereIfNotEmpty_WhenValueIsNull_ShouldNotApplyFilter()
    {
        // Arrange
        string? nameFilter = null;

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotEmpty(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .ToListAsync();

        // Assert
        Assert.True(result.Count > 1); // 应该返回所有用户
    }

    #endregion

    #region WhereIfNotWhiteSpace 测试

    [Fact]
    public async Task WhereIfNotWhiteSpace_WhenValueNotWhiteSpace_ShouldApplyFilter()
    {
        // Arrange
        var nameFilter = "王";

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotWhiteSpace(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .ToListAsync();

        // Assert
        Assert.Single(result);
        Assert.Equal("王五", result[0].Name);
    }

    [Fact]
    public async Task WhereIfNotWhiteSpace_WhenValueIsWhiteSpace_ShouldNotApplyFilter()
    {
        // Arrange
        var nameFilter = "   "; // 空白字符

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotWhiteSpace(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .ToListAsync();

        // Assert
        Assert.True(result.Count > 1); // 应该返回所有用户
    }

    #endregion

    #region 复合查询测试

    [Fact]
    public async Task ComplexQuery_WithMultipleExtensions_ShouldWorkCorrectly()
    {
        // Arrange
        var nameFilter = ""; // 空字符串，不应用过滤
        int? minAge = 25;    // 有值，应用过滤
        var isActive = true; // 条件为真，应用过滤

        // Act
        var result = await YData.Select<User>()
            .WhereIfNotEmpty(nameFilter, (query, val) => query.Where(u => u.Name.Contains(val)))
            .WhereIfNotNull(minAge, (query, val) => query.Where(u => u.Age >= val))
            .WhereIf(isActive, u => u.IsActive)
            .OrderBy(u => u.Age)
            .ToListAsync();

        // Assert
        Assert.Equal(3, result.Count); // 张三(25), 赵六(28), 李四(30) - 都是活跃且年龄>=25
        Assert.All(result, u => Assert.True(u.IsActive));
        Assert.All(result, u => Assert.True(u.Age >= 25));

        // 验证排序
        Assert.Equal("张三", result[0].Name);
        Assert.Equal("赵六", result[1].Name);
        Assert.Equal("李四", result[2].Name);
    }

    #endregion

    #region 分页查询测试

    [Fact]
    public async Task ToPagedResultAsync_FirstPage_ShouldReturnCorrectData()
    {
        // Arrange
        var pageIndex = 1;
        var pageSize = 2;

        // Act
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Age)
            .ToPagedResultAsync(pageIndex, pageSize);

        // Assert
        Assert.Equal(pageIndex, result.PageIndex);
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(4, result.TotalCount); // 4个活跃用户
        Assert.Equal(2, result.TotalPages);
        Assert.Equal(2, result.Items.Count);
        Assert.True(result.IsFirstPage);
        Assert.False(result.IsLastPage);
        Assert.False(result.HasPreviousPage);
        Assert.True(result.HasNextPage);

        // 验证数据按年龄排序
        Assert.Equal("", result.Items[0].Name); // 空名称用户，年龄20
        Assert.Equal("张三", result.Items[1].Name); // 年龄25
    }

    [Fact]
    public async Task ToPagedResultAsync_LastPage_ShouldReturnCorrectData()
    {
        // Arrange
        var pageIndex = 2;
        var pageSize = 2;

        // Act
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Age)
            .ToPagedResultAsync(pageIndex, pageSize);

        // Assert
        Assert.Equal(pageIndex, result.PageIndex);
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(4, result.TotalCount);
        Assert.Equal(2, result.TotalPages);
        Assert.Equal(2, result.Items.Count);
        Assert.False(result.IsFirstPage);
        Assert.True(result.IsLastPage);
        Assert.True(result.HasPreviousPage);
        Assert.False(result.HasNextPage);

        // 验证数据
        Assert.Equal("赵六", result.Items[0].Name); // 年龄28
        Assert.Equal("李四", result.Items[1].Name); // 年龄30
    }

    [Fact]
    public async Task ToPagedResultAsync_EmptyResult_ShouldReturnEmptyPage()
    {
        // Arrange
        var pageIndex = 1;
        var pageSize = 10;

        // Act
        var result = await YData.Select<User>()
            .Where(u => u.Age > 100) // 没有用户年龄超过100
            .ToPagedResultAsync(pageIndex, pageSize);

        // Assert
        Assert.Equal(pageIndex, result.PageIndex);
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal(0, result.TotalPages);
        Assert.Empty(result.Items);
        Assert.True(result.IsFirstPage);
        Assert.True(result.IsLastPage);
        Assert.False(result.HasPreviousPage);
        Assert.False(result.HasNextPage);
    }

    [Fact]
    public async Task ToPagedResultAsync_PageIndexOutOfRange_ShouldReturnLastPage()
    {
        // Arrange
        var pageIndex = 10; // 超出范围的页码
        var pageSize = 2;

        // Act
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .ToPagedResultAsync(pageIndex, pageSize);

        // Assert
        Assert.Equal(2, result.PageIndex); // 应该返回最后一页
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(4, result.TotalCount);
        Assert.Equal(2, result.TotalPages);
        Assert.True(result.IsLastPage);
    }

    [Fact]
    public async Task ToPagedResultAsync_InvalidParameters_ShouldUseDefaults()
    {
        // Arrange & Act
        var result = await YData.Select<User>()
            .ToPagedResultAsync(-1, -5); // 无效参数

        // Assert
        Assert.Equal(1, result.PageIndex); // 默认第1页
        Assert.Equal(10, result.PageSize); // 默认页大小10
    }

    [Fact]
    public void ToPagedResult_SyncVersion_ShouldWork()
    {
        // Arrange
        var pageIndex = 1;
        var pageSize = 3;

        // Act
        var result = YData.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Name)
            .ToPagedResult(pageIndex, pageSize);

        // Assert
        Assert.Equal(pageIndex, result.PageIndex);
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(4, result.TotalCount);
        Assert.Equal(2, result.TotalPages);
        Assert.Equal(3, result.Items.Count);
    }

    [Fact]
    public async Task ToFastPagedResultAsync_ShouldNotCountTotal()
    {
        // Arrange
        var pageIndex = 1;
        var pageSize = 2;

        // Act
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Age)
            .ToFastPagedResultAsync(pageIndex, pageSize);

        // Assert
        Assert.Equal(pageIndex, result.PageIndex);
        Assert.Equal(pageSize, result.PageSize);
        Assert.Equal(-1, result.TotalCount); // 快速分页不统计总数
        Assert.Equal(2, result.Items.Count);
    }

    [Fact]
    public void PagedResult_Properties_ShouldCalculateCorrectly()
    {
        // Arrange
        var data = new List<User>
        {
            new User { Name = "用户1" },
            new User { Name = "用户2" }
        };

        // Act
        var result = PagedResult<User>.Create(data, 2, 5, 23);

        // Assert
        Assert.Equal(2, result.PageIndex);
        Assert.Equal(5, result.PageSize);
        Assert.Equal(23, result.TotalCount);
        Assert.Equal(5, result.TotalPages); // Math.Ceiling(23/5) = 5
        Assert.Equal(6, result.StartIndex); // (2-1)*5+1 = 6
        Assert.Equal(10, result.EndIndex); // Min(2*5, 23) = 10
        Assert.True(result.HasPreviousPage);
        Assert.True(result.HasNextPage);
        Assert.False(result.IsFirstPage);
        Assert.False(result.IsLastPage);
    }

    [Fact]
    public void PagedResult_Map_ShouldTransformData()
    {
        // Arrange
        var users = new List<User>
        {
            new User { Name = "张三", Age = 25 },
            new User { Name = "李四", Age = 30 }
        };
        var result = PagedResult<User>.Create(users, 1, 10, 2);

        // Act
        var mappedResult = result.Map(u => new { u.Name, u.Age });

        // Assert
        Assert.Equal(result.PageIndex, mappedResult.PageIndex);
        Assert.Equal(result.PageSize, mappedResult.PageSize);
        Assert.Equal(result.TotalCount, mappedResult.TotalCount);
        Assert.Equal(2, mappedResult.Items.Count);
        Assert.Equal("张三", mappedResult.Items[0].Name);
        Assert.Equal(25, mappedResult.Items[0].Age);
    }

    [Fact]
    public void PagedResult_GetSummary_ShouldReturnCorrectString()
    {
        // Arrange
        var data = new List<User> { new User { Name = "测试" } };
        var result = PagedResult<User>.Create(data, 2, 5, 23);

        // Act
        var summary = result.GetSummary();

        // Assert
        Assert.Equal("第 6-10 条，共 23 条记录，第 2/5 页", summary);
    }

    [Fact]
    public void PagedResult_Empty_ShouldReturnEmptyResult()
    {
        // Act
        var result = PagedResult<User>.Empty(1, 10);

        // Assert
        Assert.Empty(result.Items);
        Assert.Equal(1, result.PageIndex);
        Assert.Equal(10, result.PageSize);
        Assert.Equal(0, result.TotalCount);
        Assert.Equal("没有找到数据", result.GetSummary());
    }

    #endregion

    public void Dispose()
    {
        try
        {
            YData.FreeSql?.Dispose();

            // 清理测试数据库文件
            if (File.Exists(_testDbPath))
            {
                File.Delete(_testDbPath);
            }
        }
        catch
        {
            // 忽略清理错误
        }
    }
}
