// 🧪 测试框架
global using Xunit;
global using Xunit.Abstractions;

// 💉 依赖注入和配置
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Logging;

// 🔧 系统命名空间
global using System;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading.Tasks;

// 📦 Zylo.YData
global using Zylo.YData;
global using Zylo.YData.Examples;
global using Zylo.YData.Extensions;
