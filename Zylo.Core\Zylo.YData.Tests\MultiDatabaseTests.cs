using Xunit;
using Xunit.Abstractions;
using Zylo.YData;
using FreeSql.DataAnnotations;

namespace Zylo.YData.Tests;

/// <summary>
/// 多数据库功能测试
/// </summary>
[Collection("YData Tests")]
public class MultiDatabaseTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _testId;
    private readonly string _defaultDbFile;
    private readonly List<string> _testDbFiles;

    public MultiDatabaseTests(ITestOutputHelper output)
    {
        _output = output;
        _testId = Guid.NewGuid().ToString("N")[..8];
        _defaultDbFile = $"test_default_{_testId}.db";
        _testDbFiles = new List<string> { _defaultDbFile };

        // 初始化 YData（使用唯一的文件数据库避免冲突）
        var defaultDbFile = $"default_{_testId}.db";
        _testDbFiles.Add(defaultDbFile);
        YData.Configure(options =>
        {
            options.ConnectionString = $"Data Source={defaultDbFile}";
            options.DataType = YDataType.Sqlite;
        });

        // 创建测试表结构
        YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
    }

    [Fact]
    public void RegisterDatabase_ShouldAddNewDatabase()
    {
        // Arrange
        var dbName = $"test_db_{_testId}";
        var dbFile = $"test_{_testId}.db";
        var connectionString = $"Data Source={dbFile}";
        _testDbFiles.Add(dbFile);

        // Act
        YData.RegisterDatabase(dbName, connectionString, YDataType.Sqlite);

        // Assert
        Assert.True(YData.DatabaseExists(dbName));
        var dbInfo = YData.GetDatabaseInfo(dbName);
        Assert.NotNull(dbInfo);
        Assert.Equal(dbName, dbInfo.Name);
        Assert.Equal(YDataType.Sqlite, dbInfo.DataType);
    }

    [Fact]
    public void RegisterSqliteDatabases_ShouldAddMultipleDatabases()
    {
        // Arrange & Act
        var usersDb = $"users_{_testId}.db";
        var ordersDb = $"orders_{_testId}.db";
        var productsDb = $"products_{_testId}.db";
        _testDbFiles.AddRange(new[] { usersDb, ordersDb, productsDb });

        YData.RegisterSqliteDatabases(
            ($"users_{_testId}", usersDb),
            ($"orders_{_testId}", ordersDb),
            ($"products_{_testId}", productsDb)
        );

        // Assert
        var expectedDbs = new[] { $"users_{_testId}", $"orders_{_testId}", $"products_{_testId}" };
        foreach (var dbName in expectedDbs)
        {
            Assert.True(YData.DatabaseExists(dbName));
            var dbInfo = YData.GetDatabaseInfo(dbName);
            Assert.NotNull(dbInfo);
            Assert.Equal(dbName, dbInfo.Name);
            Assert.Equal(YDataType.Sqlite, dbInfo.DataType);
        }
    }

    [Fact]
    public void UseDatabase_ShouldSwitchDefaultDatabase()
    {
        // Arrange
        var dbName = $"switch_test_db_{_testId}";
        var dbFile = $"switch_test_{_testId}.db";
        _testDbFiles.Add(dbFile);
        YData.RegisterDatabase(dbName, $"Data Source={dbFile}", YDataType.Sqlite);

        // Act
        YData.UseDatabase(dbName);

        // Assert
        var currentDb = YData.GetCurrentDatabaseName();
        Assert.Equal(dbName, currentDb);
    }

    [Fact]
    public async Task WithDatabaseAsync_ShouldExecuteInSpecificDatabase()
    {
        // Arrange - 使用唯一的文件数据库避免冲突
        var dbName = $"temp_db_{_testId}";
        var tempDbFile = $"temp_{_testId}.db";
        _testDbFiles.Add(tempDbFile);
        YData.RegisterDatabase(dbName, $"Data Source={tempDbFile}", YDataType.Sqlite);

        // 获取当前默认数据库名称
        var defaultDbName = YData.GetCurrentDatabaseName();
        _output.WriteLine($"Default database name: {defaultDbName}");

        // 在临时数据库中创建表和数据
        var tempInsertResult = await YDataSwitcher.WithDatabaseAsync(dbName, async () =>
        {
            var currentDb = YData.GetCurrentDatabaseName();
            _output.WriteLine($"Current database in WithDatabaseAsync: {currentDb}");

            // 验证确实切换到了临时数据库
            Assert.Equal(dbName, currentDb);

            YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
            var tempUser = new TestUser { Name = "Temp User", Email = "<EMAIL>", Age = 30 };
            var result = await YData.InsertAsync(tempUser);
            _output.WriteLine($"Temp database insert result: {result}");

            // 在临时数据库中验证数据
            var tempCount = await YData.Select<TestUser>().CountAsync();
            _output.WriteLine($"Temp database count after insert: {tempCount}");
            Assert.True(tempCount >= 1L, $"Expected at least 1 user in temp database during operation, but got {tempCount}");

            return result;
        });

        // 确保已经切换回默认数据库
        var currentDbAfterSwitch = YData.GetCurrentDatabaseName();
        _output.WriteLine($"Current database after switch: {currentDbAfterSwitch}");
        _output.WriteLine($"Expected default database: {defaultDbName}");
        Assert.Equal(defaultDbName, currentDbAfterSwitch);

        // 验证临时数据库中的数据仍然存在
        var tempCount = await YDataSwitcher.WithDatabaseAsync(dbName, async () =>
        {
            var count = await YData.Select<TestUser>().CountAsync();
            _output.WriteLine($"Final temp database count: {count}");
            return count;
        });

        // 断言
        Assert.True(tempInsertResult > 0, "Temp database insert should succeed");
        Assert.True(tempCount >= 1L, $"Expected at least 1 user in temp database, but got {tempCount}");
    }

    [Fact]
    public async Task ExecuteInAllDatabasesAsync_ShouldExecuteInAllDatabases()
    {
        // Arrange
        var databases = new[] { $"db1_{_testId}", $"db2_{_testId}", $"db3_{_testId}" };
        foreach (var db in databases)
        {
            var dbFile = $"{db}.db";
            _testDbFiles.Add(dbFile);
            YData.RegisterDatabase(db, $"Data Source={dbFile}", YDataType.Sqlite);
        }

        // Act
        var results = await YDataSwitcher.ExecuteInAllDatabasesAsync(async dbName =>
        {
            // 在每个数据库中创建表
            YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
            await YData.InsertAsync(new TestUser { Name = $"User in {dbName}", Email = $"{dbName}@test.com", Age = 25 });
            return await YData.Select<TestUser>().CountAsync();
        });

        // Assert
        Assert.True(results.Count >= databases.Length); // 至少包含注册的数据库
        foreach (var db in databases)
        {
            Assert.True(results.ContainsKey(db));
            Assert.Equal(1L, results[db]); // CountAsync 返回 long
        }
    }

    [Fact]
    public async Task ExecuteInAllDatabasesParallelAsync_ShouldExecuteInParallel()
    {
        // Arrange
        var databases = new[] { $"parallel_db1_{_testId}", $"parallel_db2_{_testId}", $"parallel_db3_{_testId}" };
        foreach (var db in databases)
        {
            var dbFile = $"{db}.db";
            _testDbFiles.Add(dbFile);
            YData.RegisterDatabase(db, $"Data Source={dbFile}", YDataType.Sqlite);
        }

        var startTime = DateTime.Now;

        // Act
        var results = await YDataSwitcher.ExecuteInAllDatabasesParallelAsync(async dbName =>
        {
            // 模拟一些工作
            await Task.Delay(100);
            YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
            return await YData.Select<TestUser>().CountAsync();
        });

        var endTime = DateTime.Now;
        var duration = endTime - startTime;

        // Assert
        // 应该包含所有注册的数据库（包括默认数据库）
        Assert.True(results.Count >= databases.Length);
        // 并行执行应该比串行快
        Assert.True(duration.TotalMilliseconds < (databases.Length + 1) * 100 * 0.8);
    }

    [Fact]
    public async Task SafeExecuteInAllDatabasesAsync_ShouldHandleErrors()
    {
        // Arrange
        var databases = new[] { $"safe_db1_{_testId}", $"safe_db2_{_testId}" };
        foreach (var db in databases)
        {
            var dbFile = $"{db}.db";
            _testDbFiles.Add(dbFile);
            YData.RegisterDatabase(db, $"Data Source={dbFile}", YDataType.Sqlite);
        }

        // Act
        var results = await YDataSwitcher.SafeExecuteInAllDatabasesAsync(async dbName =>
        {
            // 使用完整的数据库名称进行比较
            if (dbName == $"safe_db1_{_testId}")
            {
                throw new InvalidOperationException("Test error");
            }
            YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
            return await YData.Select<TestUser>().CountAsync();
        });

        // Assert
        Assert.True(results.SuccessCount > 0, $"Expected some successful operations, but got {results.SuccessCount}");
        Assert.True(results.FailureCount > 0, $"Expected some failed operations, but got {results.FailureCount}");
        Assert.True(results.FailedOperations.ContainsKey($"safe_db1_{_testId}"),
            $"Expected failed operation for safe_db1_{_testId}, but failed operations were: {string.Join(", ", results.FailedOperations.Keys)}");
        Assert.Contains("Test error", results.FailedOperations[$"safe_db1_{_testId}"].Message);
    }

    [Fact]
    public async Task HealthCheckAsync_ShouldReturnDatabaseHealth()
    {
        // Arrange
        var databases = new[] { $"health_db1_{_testId}", $"health_db2_{_testId}" };
        foreach (var db in databases)
        {
            var dbFile = $"{db}.db";
            _testDbFiles.Add(dbFile);
            YData.RegisterDatabase(db, $"Data Source={dbFile}", YDataType.Sqlite);
        }

        // Act
        var healthResults = await YDataSwitcher.HealthCheckAsync();

        // Assert
        Assert.True(healthResults.Count >= databases.Length);
        foreach (var db in databases)
        {
            Assert.True(healthResults.ContainsKey(db));
            Assert.True(healthResults[db]); // Should be healthy
        }
    }

    [Fact]
    public void GetDatabaseNames_ShouldReturnAllRegisteredDatabases()
    {
        // Arrange
        var initialCount = YData.GetDatabaseNames().Count();
        var newDatabases = new[] { $"list_db1_{_testId}", $"list_db2_{_testId}", $"list_db3_{_testId}" };

        foreach (var db in newDatabases)
        {
            var dbFile = $"{db}.db";
            _testDbFiles.Add(dbFile);
            YData.RegisterDatabase(db, $"Data Source={dbFile}", YDataType.Sqlite);
        }

        // Act
        var allDatabases = YData.GetDatabaseNames();

        // Assert
        Assert.True(allDatabases.Count() >= initialCount + newDatabases.Length);
        foreach (var db in newDatabases)
        {
            Assert.Contains(db, allDatabases);
        }
    }

    [Fact]
    public void RemoveDatabase_ShouldRemoveDatabase()
    {
        // Arrange
        var dbName = $"remove_test_db_{_testId}";
        var dbFile = $"remove_test_{_testId}.db";
        _testDbFiles.Add(dbFile);
        YData.RegisterDatabase(dbName, $"Data Source={dbFile}", YDataType.Sqlite);
        Assert.True(YData.DatabaseExists(dbName));

        // Act
        YData.RemoveDatabase(dbName);

        // Assert
        Assert.False(YData.DatabaseExists(dbName));
    }

    [Fact]
    public async Task GetDatabaseStatsAsync_ShouldReturnStatistics()
    {
        // Arrange
        var dbName = $"stats_test_db_{_testId}";
        var dbFile = $"stats_test_{_testId}.db";
        _testDbFiles.Add(dbFile);
        YData.RegisterDatabase(dbName, $"Data Source={dbFile}", YDataType.Sqlite);

        // Act
        var stats = await YData.GetDatabaseStatsAsync(dbName);

        // Assert
        Assert.NotNull(stats);
        Assert.Equal(dbName, stats.DatabaseName);
        Assert.True(stats.IsConnected);
        Assert.True(stats.QueryCount >= 0);
    }

    public void Dispose()
    {
        // 清理测试数据库文件
        foreach (var dbFile in _testDbFiles)
        {
            try
            {
                if (File.Exists(dbFile))
                {
                    File.Delete(dbFile);
                }
            }
            catch (Exception ex)
            {
                _output.WriteLine($"清理数据库文件 {dbFile} 失败: {ex.Message}");
            }
        }
    }
}

/// <summary>
/// 测试用户实体
/// </summary>
[Table(Name = "test_users")]
public partial class TestUser
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }

    [Column(StringLength = 50)]
    public string Name { get; set; } = string.Empty;

    [Column(StringLength = 100)]
    public string Email { get; set; } = string.Empty;

    public int Age { get; set; }
    public DateTime CreateTime { get; set; } = DateTime.Now;
    public DateTime? UpdateTime { get; set; }
    public bool IsActive { get; set; } = true;
}
