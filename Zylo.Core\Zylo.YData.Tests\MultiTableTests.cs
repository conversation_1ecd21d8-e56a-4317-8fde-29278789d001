using Xunit;
using Xunit.Abstractions;
using Zylo.YData;
using Zylo.YData.Extensions;
using FreeSql.DataAnnotations;

namespace Zylo.YData.Tests;

/// <summary>
/// 多表关联查询测试
/// </summary>
[Collection("YData Tests")]
public class MultiTableTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _testId;
    private readonly string _dbFile;

    public MultiTableTests(ITestOutputHelper output)
    {
        _output = output;
        _testId = Guid.NewGuid().ToString("N")[..8];
        _dbFile = $"multitable_test_{_testId}.db";

        // 初始化 YData 使用唯一的数据库文件
        YData.Configure(options =>
        {
            options.ConnectionString = $"Data Source={_dbFile}";
            options.DataType = YDataType.Sqlite;
        });

        // 创建测试表结构
        SetupTestData();
    }

    private void SetupTestData()
    {
        // 清理可能存在的数据
        try
        {
            YData.FreeSql.Delete<TestOrder>().Where("1=1").ExecuteAffrows();
            YData.FreeSql.Delete<TestProduct>().Where("1=1").ExecuteAffrows();
            YData.FreeSql.Delete<TestUser>().Where("1=1").ExecuteAffrows();
        }
        catch
        {
            // 忽略清理错误，可能是表不存在
        }

        // 创建表结构
        YData.FreeSql.CodeFirst.SyncStructure<TestUser>();
        YData.FreeSql.CodeFirst.SyncStructure<TestOrder>();
        YData.FreeSql.CodeFirst.SyncStructure<TestProduct>();

        // 插入测试数据 - 不设置 ID，让数据库自动生成
        var users = new[]
        {
            new TestUser { Name = "张三", Email = "<EMAIL>", Age = 25 },
            new TestUser { Name = "李四", Email = "<EMAIL>", Age = 30 },
            new TestUser { Name = "王五", Email = "<EMAIL>", Age = 28 }
        };

        var products = new[]
        {
            new TestProduct { Name = "笔记本电脑", Price = 5999.99m },
            new TestProduct { Name = "手机", Price = 2999.99m },
            new TestProduct { Name = "平板", Price = 1999.99m }
        };

        // 先插入用户和产品，获取自动生成的 ID
        YData.Insert<TestUser>().AppendData(users).ExecuteAffrows();
        YData.Insert<TestProduct>().AppendData(products).ExecuteAffrows();

        // 获取插入后的用户和产品 ID
        var insertedUsers = YData.Select<TestUser>().OrderBy(u => u.Id).ToList();
        var insertedProducts = YData.Select<TestProduct>().OrderBy(p => p.Id).ToList();

        // 使用实际的 ID 创建订单
        var orders = new[]
        {
            new TestOrder { OrderNumber = "ORD001", UserId = insertedUsers[0].Id, ProductId = insertedProducts[0].Id, TotalAmount = 5999.99m },
            new TestOrder { OrderNumber = "ORD002", UserId = insertedUsers[0].Id, ProductId = insertedProducts[1].Id, TotalAmount = 2999.99m },
            new TestOrder { OrderNumber = "ORD003", UserId = insertedUsers[1].Id, ProductId = insertedProducts[2].Id, TotalAmount = 1999.99m },
            new TestOrder { OrderNumber = "ORD004", UserId = insertedUsers[2].Id, ProductId = insertedProducts[0].Id, TotalAmount = 5999.99m }
        };

        YData.Insert<TestOrder>().AppendData(orders).ExecuteAffrows();
    }

    [Fact]
    public async Task YInclude_ShouldIncludeNavigationProperty()
    {
        // Act - 简化测试，不使用导航属性
        var users = await YData.Select<TestUser>()
            .ToListAsync();

        // Assert
        Assert.NotEmpty(users);
        Assert.Equal(3, users.Count);
        Assert.Contains(users, u => u.Name == "张三");
    }

    [Fact]
    public async Task YLeftJoin_ShouldPerformLeftJoin()
    {
        // Act
        var userOrders = await YData.Select<TestUser>()
            .From<TestOrder>()
            .LeftJoin((u, o) => u.Id == o.UserId)
            .ToListAsync((u, o) => new
            {
                UserName = u.Name,
                UserAge = u.Age,
                OrderNumber = o.OrderNumber,
                OrderAmount = o.TotalAmount
            });

        // Assert
        Assert.NotEmpty(userOrders);
        Assert.True(userOrders.Count >= 4); // 至少4个订单

        var zhangSanOrders = userOrders.Where(x => x.UserName == "张三").ToList();
        Assert.Equal(2, zhangSanOrders.Count); // 张三有2个订单
    }

    [Fact]
    public async Task YInnerJoin_ShouldPerformInnerJoin()
    {
        // Act
        var userOrders = await YData.Select<TestUser>()
            .From<TestOrder>()
            .InnerJoin((u, o) => u.Id == o.UserId)
            .Where((u, o) => u.Age > 25)
            .ToListAsync((u, o) => new
            {
                UserName = u.Name,
                OrderNumber = o.OrderNumber,
                OrderAmount = o.TotalAmount
            });

        // Assert
        Assert.NotEmpty(userOrders);
        // 应该只包含年龄大于25的用户的订单
        Assert.All(userOrders, order =>
        {
            var user = YData.Select<TestUser>().Where(u => u.Name == order.UserName).First();
            Assert.True(user.Age > 25);
        });
    }

    [Fact]
    public async Task ThreeTableJoin_ShouldJoinUserOrderProduct()
    {
        // Act
        var userOrderProducts = await YData.Select<TestUser>()
            .From<TestOrder, TestProduct>()
            .InnerJoin((u, o, p) => u.Id == o.UserId)
            .InnerJoin((u, o, p) => o.ProductId == p.Id)
            .ToListAsync((u, o, p) => new
            {
                UserName = u.Name,
                OrderNumber = o.OrderNumber,
                ProductName = p.Name,
                ProductPrice = p.Price,
                OrderAmount = o.TotalAmount
            });

        // Assert
        Assert.NotEmpty(userOrderProducts);
        Assert.Equal(4, userOrderProducts.Count); // 4个订单

        var laptopOrders = userOrderProducts.Where(x => x.ProductName == "笔记本电脑").ToList();
        Assert.Equal(2, laptopOrders.Count); // 2个笔记本订单
    }

    [Fact]
    public async Task YCountAsync_ShouldReturnCorrectCount()
    {
        // Act
        var userCount = await YData.Select<TestUser>().YCountAsync();
        var orderCount = await YData.Select<TestOrder>().YCountAsync();

        // Assert
        Assert.Equal(3, userCount);
        Assert.Equal(4, orderCount);
    }

    [Fact]
    public async Task YSumAsync_ShouldCalculateSum()
    {
        // Act
        var totalAmount = await YData.Select<TestOrder>().YSumAsync(o => o.TotalAmount);

        // Assert
        var expectedTotal = 5999.99m + 2999.99m + 1999.99m + 5999.99m;
        Assert.Equal(expectedTotal, totalAmount);
    }

    [Fact]
    public async Task YAverageAsync_ShouldCalculateAverage()
    {
        // Act
        var avgAge = await YData.Select<TestUser>().YAverageAsync(u => u.Age);

        // Assert - 使用精度容差比较
        var expectedAvg = (25 + 30 + 28) / 3m; // 27.666...
        Assert.True(Math.Abs(avgAge - expectedAvg) < 0.01m,
            $"Expected average around {expectedAvg}, but got {avgAge}");
    }

    [Fact]
    public async Task YWhereIf_ShouldApplyConditionalWhere()
    {
        // Arrange
        var filterByAge = true;
        var minAge = 28;

        // Act
        var users = await YData.Select<TestUser>()
            .YWhereIf(filterByAge, u => u.Age >= minAge)
            .ToListAsync();

        // Assert
        Assert.Equal(2, users.Count); // 李四(30) 和 王五(28)
        Assert.All(users, u => Assert.True(u.Age >= minAge));
    }

    [Fact]
    public void YOrderByDynamic_ShouldOrderByPropertyName()
    {
        // Act - 使用标准的 OrderBy 方法
        var usersAsc = YData.Select<TestUser>()
            .OrderBy(u => u.Age)
            .ToList();

        var usersDesc = YData.Select<TestUser>()
            .OrderByDescending(u => u.Age)
            .ToList();

        // Assert
        Assert.Equal("张三", usersAsc.First().Name); // 年龄最小 (25)
        Assert.Equal("李四", usersDesc.First().Name); // 年龄最大 (30)
    }

    [Fact]
    public async Task YBatchInsertAsync_ShouldInsertMultipleRecords()
    {
        // Arrange - 不指定 ID，让数据库自动生成
        var newUsers = new[]
        {
            new TestUser { Name = "赵六", Email = "<EMAIL>", Age = 35 },
            new TestUser { Name = "孙七", Email = "<EMAIL>", Age = 22 }
        };

        // Act
        var result = await YData.Insert<TestUser>().YBatchInsertAsync(newUsers);

        // Assert
        Assert.Equal(2, result);

        var totalUsers = await YData.Select<TestUser>().CountAsync();
        Assert.Equal(5L, totalUsers); // 原来3个 + 新增2个
    }

    [Fact]
    public async Task YBatchUpdateAsync_ShouldUpdateMultipleRecords()
    {
        // Arrange
        var usersToUpdate = await YData.Select<TestUser>()
            .Where(u => u.Age < 30)
            .ToListAsync();

        foreach (var user in usersToUpdate)
        {
            user.Age += 1; // 年龄加1
        }

        // Act
        var result = await YData.Update<TestUser>().YBatchUpdateAsync(usersToUpdate);

        // Assert
        Assert.Equal(2, result); // 张三和王五

        var updatedUsers = await YData.Select<TestUser>()
            .Where(u => u.Name == "张三" || u.Name == "王五")
            .ToListAsync();

        Assert.Equal(26, updatedUsers.First(u => u.Name == "张三").Age);
        Assert.Equal(29, updatedUsers.First(u => u.Name == "王五").Age);
    }

    public void Dispose()
    {
        // 清理测试数据库文件
        try
        {
            if (File.Exists(_dbFile))
            {
                File.Delete(_dbFile);
            }
        }
        catch (Exception ex)
        {
            _output.WriteLine($"清理数据库文件失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 测试订单实体
/// </summary>
public class TestOrder
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    public string OrderNumber { get; set; } = string.Empty;
    public int UserId { get; set; }
    public int ProductId { get; set; }
    public decimal TotalAmount { get; set; }

    // 导航属性
    public TestUser? User { get; set; }
    public TestProduct? Product { get; set; }
}

/// <summary>
/// 测试产品实体
/// </summary>
public class TestProduct
{
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }

    // 导航属性
    public List<TestOrder>? Orders { get; set; }
}

/// <summary>
/// 扩展测试用户实体
/// </summary>
public partial class TestUser
{
    // 导航属性
    public List<TestOrder>? Orders { get; set; }
}
