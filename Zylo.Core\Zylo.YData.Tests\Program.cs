namespace Zylo.YData.Tests;

/// <summary>
/// 测试程序入口
/// </summary>
public class Program
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 Zylo.YData 功能测试程序");
        Console.WriteLine("=====================================");

        try
        {
            // 测试静态 API
            await TestStaticApi();

            // 测试依赖注入
            await TestDependencyInjection();

            // 测试高级功能
            await TestAdvancedFeatures();

            Console.WriteLine("\n🎉 所有测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }

        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }

    /// <summary>
    /// 测试静态 API
    /// </summary>
    private static async Task TestStaticApi()
    {
        Console.WriteLine("\n=== 测试静态 API ===");

        try
        {
            // 配置 YData
            YData.ConfigureAuto("Data Source=:memory:");
            Console.WriteLine("✅ YData 配置成功");

            // 创建表结构
            var freeSql = YData.FreeSql;
            freeSql.CodeFirst.SyncStructure<User>();
            freeSql.CodeFirst.SyncStructure<Order>();
            Console.WriteLine("✅ 数据库表结构创建成功");

            // 测试 CRUD 操作
            await TestCrudOperations();

            // 测试查询功能
            await TestQueryFeatures();

            // 测试事务功能
            await TestTransactionFeatures();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 静态 API 测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试 CRUD 操作
    /// </summary>
    private static async Task TestCrudOperations()
    {
        Console.WriteLine("\n--- CRUD 操作测试 ---");

        // 插入
        var user = new User
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 28,
            IsActive = true
        };

        var insertResult = await YData.InsertAsync(user);
        Console.WriteLine($"✅ 插入用户: {user.Name} (ID: {user.Id})");

        // 查询
        var retrievedUser = await YData.GetAsync<User>(user.Id);
        Console.WriteLine($"✅ 查询用户: {retrievedUser?.Name}");

        // 更新
        if (retrievedUser != null)
        {
            retrievedUser.Age = 29;
            retrievedUser.UpdateTime = DateTime.Now;
            await YData.UpdateAsync(retrievedUser);
            Console.WriteLine($"✅ 更新用户年龄: {retrievedUser.Age}");
        }

        // 删除
        var deleteResult = await YData.DeleteAsync<User>(user.Id);
        Console.WriteLine($"✅ 删除用户: 影响行数 {deleteResult}");
    }

    /// <summary>
    /// 测试查询功能
    /// </summary>
    private static async Task TestQueryFeatures()
    {
        Console.WriteLine("\n--- 查询功能测试 ---");

        // 批量插入测试数据
        var users = new[]
        {
            new User { Name = "李四", Email = "<EMAIL>", Age = 25, IsActive = true },
            new User { Name = "王五", Email = "<EMAIL>", Age = 30, IsActive = true },
            new User { Name = "赵六", Email = "<EMAIL>", Age = 35, IsActive = false }
        };

        foreach (var user in users)
        {
            await YData.InsertAsync(user);
        }
        Console.WriteLine($"✅ 批量插入 {users.Length} 个用户");

        // 获取所有用户
        var allUsers = await YData.GetAllAsync<User>();
        Console.WriteLine($"✅ 获取所有用户: {allUsers.Count} 个");

        // 条件查询
        var activeUsers = await YData.Select<User>()
            .Where(u => u.IsActive && u.Age >= 25)
            .OrderBy(u => u.Age)
            .ToListAsync();
        Console.WriteLine($"✅ 活跃用户查询: {activeUsers.Count} 个");

        // 分页查询
        var pagedResult = await YData.GetPagedAsync<User>(1, 2, u => u.IsActive);
        Console.WriteLine($"✅ 分页查询: 第{pagedResult.PageIndex}页，共{pagedResult.TotalCount}条，当前页{pagedResult.Items.Count}条");
    }

    /// <summary>
    /// 测试事务功能
    /// </summary>
    private static async Task TestTransactionFeatures()
    {
        Console.WriteLine("\n--- 事务功能测试 ---");

        var result = await YData.TransactionAsync(async () =>
        {
            var user = new User
            {
                Name = "事务用户",
                Email = "<EMAIL>",
                Age = 32,
                IsActive = true
            };
            await YData.InsertAsync(user);

            var order = new Order
            {
                UserId = user.Id,
                OrderNo = $"ORD{DateTime.Now:yyyyMMddHHmmss}",
                Amount = 199.99m,
                Status = OrderStatus.Pending
            };
            await YData.InsertAsync(order);

            return new { User = user, Order = order };
        });

        Console.WriteLine($"✅ 事务操作成功: 用户ID={result.User.Id}, 订单ID={result.Order.Id}");
    }

    /// <summary>
    /// 测试依赖注入
    /// </summary>
    private static async Task TestDependencyInjection()
    {
        Console.WriteLine("\n=== 测试依赖注入 ===");

        try
        {
            var services = new ServiceCollection();

            // 注册服务
            services.AddLogging(builder => builder.AddConsole());
            services.AddYDataAuto("Data Source=:memory:");
            services.AddScoped<UserService>();

            var serviceProvider = services.BuildServiceProvider();

            // 初始化数据库
            var freeSql = serviceProvider.GetRequiredService<IFreeSql>();
            freeSql.CodeFirst.SyncStructure<User>();
            Console.WriteLine("✅ 依赖注入服务注册成功");

            // 使用服务
            using var scope = serviceProvider.CreateScope();
            var userService = scope.ServiceProvider.GetRequiredService<UserService>();

            var user = await userService.CreateUserAsync("DI用户", "<EMAIL>", 27);
            var users = await userService.GetActiveUsersAsync();

            Console.WriteLine($"✅ 通过依赖注入创建用户: {user.Name}");
            Console.WriteLine($"✅ 查询活跃用户: {users.Count} 个");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 依赖注入测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试高级功能
    /// </summary>
    private static async Task TestAdvancedFeatures()
    {
        Console.WriteLine("\n=== 测试高级功能 ===");

        try
        {
            // 测试数据库类型检测
            TestDatabaseTypeDetection();

            // 测试配置选项
            TestConfigurationOptions();

            Console.WriteLine("✅ 高级功能测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 高级功能测试失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试数据库类型检测
    /// </summary>
    private static void TestDatabaseTypeDetection()
    {
        Console.WriteLine("\n--- 数据库类型检测测试 ---");

        var testCases = new[]
        {
            ("Data Source=test.db", "SQLite"),
            ("Data Source=:memory:", "SQLite"),
            ("Server=.;Database=TestDB;Trusted_Connection=true;", "SQL Server"),
            ("Host=localhost;Database=testdb;Username=user;Password=****", "PostgreSQL"),
            ("Server=localhost;Database=testdb;Uid=user;Pwd=****", "MySQL")
        };

        foreach (var (connectionString, expectedType) in testCases)
        {
            try
            {
                var services = new ServiceCollection();
                services.AddYDataAuto(connectionString);
                Console.WriteLine($"✅ 检测成功: {expectedType}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 检测失败: {expectedType} - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 测试配置选项
    /// </summary>
    private static void TestConfigurationOptions()
    {
        Console.WriteLine("\n--- 配置选项测试 ---");

        var services = new ServiceCollection();
        services.AddLogging();

        services.AddYData(options =>
        {
            options.ConnectionString = "Data Source=:memory:";
            options.DataType = YDataType.Sqlite;
            options.EnableAutoSyncStructure = true;
            options.EnableMonitorCommand = true;
            options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
            options.SlowQueryThreshold = TimeSpan.FromSeconds(5);
        });

        var serviceProvider = services.BuildServiceProvider();
        var options = serviceProvider.GetRequiredService<YDataOptions>();

        Console.WriteLine($"✅ 连接字符串: {options.ConnectionString}");
        Console.WriteLine($"✅ 数据库类型: {options.DataType}");
        Console.WriteLine($"✅ 自动同步结构: {options.EnableAutoSyncStructure}");
        Console.WriteLine($"✅ 监控命令: {options.EnableMonitorCommand}");
        Console.WriteLine($"✅ 查询超时: {options.DefaultQueryTimeout.TotalSeconds}秒");
    }
}

/// <summary>
/// 用户服务示例
/// </summary>
public class UserService
{
    private readonly IYDataContext _context;
    private readonly ILogger<UserService> _logger;

    public UserService(IYDataContext context, ILogger<UserService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<User> CreateUserAsync(string name, string email, int age)
    {
        var user = new User
        {
            Name = name,
            Email = email,
            Age = age,
            IsActive = true
        };

        await _context.InsertAsync(user);
        _logger.LogInformation("创建用户: {Name}", user.Name);

        return user;
    }

    public async Task<List<User>> GetActiveUsersAsync()
    {
        var users = await _context.Select<User>()
            .Where(u => u.IsActive)
            .ToListAsync();

        _logger.LogInformation("查询活跃用户: {Count} 个", users.Count);
        return users;
    }
}
