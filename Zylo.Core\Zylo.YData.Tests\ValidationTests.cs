using Xunit;
using Xunit.Abstractions;
using FreeSql.DataAnnotations;
using Zylo.YData.Extensions;
using Zylo.YData.Models;
using System.ComponentModel.DataAnnotations;

namespace Zylo.YData.Tests;

/// <summary>
/// 数据验证功能测试
/// </summary>
public class ValidationTests : IDisposable
{
    private readonly ITestOutputHelper _output;
    private readonly string _dbFile;
    private readonly IFreeSql _freeSql;

    public ValidationTests(ITestOutputHelper output)
    {
        _output = output;
        _dbFile = $"validation_test_{Guid.NewGuid():N}.db";

        // 创建独立的 FreeSql 实例
        _freeSql = new FreeSql.FreeSqlBuilder()
            .UseConnectionString(FreeSql.DataType.Sqlite, $"Data Source={_dbFile}")
            .UseAutoSyncStructure(true)
            .Build();

        // 配置 YData 使用独立数据库
        YData.ConfigureAuto($"Data Source={_dbFile}", YDataType.Sqlite);

        // 确保表结构存在
        _freeSql.CodeFirst.SyncStructure<TestUser>();

        _output.WriteLine($"验证测试数据库已创建: {_dbFile}");
    }

    public void Dispose()
    {
        try
        {
            _freeSql?.Dispose();
            _output.WriteLine($"✅ 验证测试资源已清理完成");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"⚠️ 清理资源时发生错误: {ex.Message}");
        }
    }

    #region 基础验证测试

    [Fact]
    public void YValidate_WithValidEntity_ShouldReturnSuccess()
    {
        // Arrange
        var user = new TestUser
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 25,
            Phone = "13812345678"
        };

        // Act
        var result = user.YValidate();

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);

        _output.WriteLine($"✅ 有效实体验证通过");
    }

    [Fact]
    public void YValidate_WithInvalidEntity_ShouldReturnFailure()
    {
        // Arrange
        var user = new TestUser
        {
            Name = "", // 必填字段为空
            Email = "invalid-email", // 邮箱格式错误
            Age = -1, // 年龄无效
            Phone = "123" // 手机号格式错误
        };

        // Act
        var result = user.YValidate();

        // Assert
        Assert.False(result.IsValid);
        Assert.NotEmpty(result.Errors);

        _output.WriteLine($"❌ 无效实体验证失败，错误数量: {result.ErrorCount}");
        foreach (var error in result.Errors)
        {
            _output.WriteLine($"   • {error.PropertyName}: {error.ErrorMessage}");
        }
    }

    [Fact]
    public void YValidate_WithNullEntity_ShouldReturnFailure()
    {
        // Arrange
        TestUser? user = null;

        // Act
        var result = user!.YValidate();

        // Assert
        Assert.False(result.IsValid);
        Assert.Single(result.Errors);
        Assert.Equal("实体不能为空", result.Errors[0].ErrorMessage);

        _output.WriteLine($"❌ 空实体验证失败: {result.Errors[0].ErrorMessage}");
    }

    #endregion

    #region 集合验证测试

    [Fact]
    public void YValidateCollection_WithMixedEntities_ShouldReturnCorrectResults()
    {
        // Arrange
        var users = new List<TestUser>
        {
            new TestUser { Name = "张三", Email = "<EMAIL>", Age = 25, Phone = "13812345678" }, // 有效
            new TestUser { Name = "", Email = "invalid", Age = -1, Phone = "123" }, // 无效
            new TestUser { Name = "李四", Email = "<EMAIL>", Age = 30, Phone = "13987654321" } // 有效
        };

        // Act
        var result = users.YValidateCollection();

        // Assert
        Assert.Equal(3, result.TotalCount);
        Assert.Equal(2, result.SuccessCount);
        Assert.Equal(1, result.FailureCount);
        Assert.False(result.IsAllValid);

        _output.WriteLine($"📊 集合验证结果:");
        _output.WriteLine($"   总数: {result.TotalCount}");
        _output.WriteLine($"   成功: {result.SuccessCount}");
        _output.WriteLine($"   失败: {result.FailureCount}");

        var failedEntities = result.GetFailedEntities();
        Assert.Single(failedEntities);

        var validEntities = result.GetValidEntities();
        Assert.Equal(2, validEntities.Count);
    }

    #endregion

    #region 自定义验证规则测试

    [Fact]
    public void YValidate_WithCustomRules_ShouldApplyCustomValidation()
    {
        // Arrange
        var options = new YValidationOptions();
        options.AddRule<TestUser>(user => user.Age >= 18, "用户必须年满18岁", nameof(TestUser.Age));
        options.AddRule<TestUser>(user => !string.IsNullOrEmpty(user.Name) && user.Name.Length >= 2, "姓名至少2个字符", nameof(TestUser.Name));

        var user = new TestUser
        {
            Name = "李",
            Email = "<EMAIL>",
            Age = 16,
            Phone = "13812345678"
        };

        // Act
        var result = user.YValidate(options);

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("年满18岁"));
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("至少2个字符"));

        _output.WriteLine($"🔧 自定义验证规则测试:");
        foreach (var error in result.Errors)
        {
            _output.WriteLine($"   • {error.PropertyName}: {error.ErrorMessage}");
        }
    }

    #endregion

    #region 验证扩展方法测试

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void YIsValidEmail_ShouldValidateEmailCorrectly(string? email, bool expected)
    {
        // Act
        var result = email.YIsValidEmail();

        // Assert
        Assert.Equal(expected, result);

        _output.WriteLine($"📧 邮箱验证: '{email}' => {result}");
    }

    [Theory]
    [InlineData("13812345678", true)]
    [InlineData("12345678901", false)]
    [InlineData("1381234567", false)]
    [InlineData("", false)]
    [InlineData(null, false)]
    public void YIsValidPhone_ShouldValidatePhoneCorrectly(string? phone, bool expected)
    {
        // Act
        var result = phone.YIsValidPhone();

        // Assert
        Assert.Equal(expected, result);

        _output.WriteLine($"📱 手机号验证: '{phone}' => {result}");
    }

    [Theory]
    [InlineData(25, 18, 65, true)]
    [InlineData(17, 18, 65, false)]
    [InlineData(70, 18, 65, false)]
    public void YIsInRange_ShouldValidateRangeCorrectly(int value, int min, int max, bool expected)
    {
        // Act
        var result = value.YIsInRange(min, max);

        // Assert
        Assert.Equal(expected, result);

        _output.WriteLine($"🔢 范围验证: {value} 在 [{min}, {max}] => {result}");
    }

    #endregion

    #region 测试实体类

    [Table(Name = "test_users")]
    public class TestUser
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public int Id { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 2)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [System.ComponentModel.DataAnnotations.Range(0, 150)]
        public int Age { get; set; }

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        public DateTime CreateTime { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }

    #endregion
}
