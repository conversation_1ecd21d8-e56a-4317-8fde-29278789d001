namespace Zylo.YData;

/// <summary>
/// YData 支持的数据库类型枚举
/// </summary>
/// <remarks>
/// 定义了 YData 框架支持的所有数据库类型，基于 FreeSql 的数据库支持
/// </remarks>
public enum YDataType
{
    /// <summary>
    /// Microsoft SQL Server 数据库
    /// </summary>
    /// <remarks>
    /// 支持 SQL Server 2008 及以上版本
    /// </remarks>
    SqlServer = 0,

    /// <summary>
    /// MySQL 数据库
    /// </summary>
    /// <remarks>
    /// 支持 MySQL 5.6 及以上版本，包括 MariaDB
    /// </remarks>
    MySql = 1,

    /// <summary>
    /// PostgreSQL 数据库
    /// </summary>
    /// <remarks>
    /// 支持 PostgreSQL 9.5 及以上版本
    /// </remarks>
    PostgreSQL = 2,

    /// <summary>
    /// SQLite 数据库
    /// </summary>
    /// <remarks>
    /// 轻量级文件数据库，适用于本地存储和小型应用
    /// </remarks>
    Sqlite = 3,

    /// <summary>
    /// Oracle 数据库
    /// </summary>
    /// <remarks>
    /// 支持 Oracle 11g 及以上版本
    /// </remarks>
    Oracle = 4
}

/// <summary>
/// YData 配置选项
/// <para>包含数据库连接、性能调优、日志记录等所有配置参数</para>
/// </summary>
/// <remarks>
/// 此类定义了 YData 框架的所有配置选项，包括：
/// <list type="bullet">
/// <item>数据库连接配置（类型、连接字符串等）</item>
/// <item>性能优化配置（超时时间、连接池等）</item>
/// <item>日志和监控配置</item>
/// <item>开发和调试配置</item>
/// </list>
/// </remarks>
public class YDataOptions
{
    #region 基础数据库配置

    /// <summary>
    /// 数据库类型
    /// </summary>
    /// <remarks>
    /// 指定要连接的数据库类型，默认为 SQL Server
    /// </remarks>
    public YDataType DataType { get; set; } = YDataType.SqlServer;

    /// <summary>
    /// 数据库连接字符串
    /// </summary>
    /// <remarks>
    /// 包含数据库服务器地址、端口、用户名、密码等连接信息
    /// </remarks>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 数据库命名策略
    /// </summary>
    /// <remarks>
    /// 定义实体类属性名到数据库字段名的转换规则，如驼峰转下划线等
    /// </remarks>
    public FreeSql.Internal.NameConvertType? NamingStrategy { get; set; }

    #endregion

    #region 功能开关配置

    /// <summary>
    /// 是否启用自动同步数据库结构
    /// </summary>
    /// <remarks>
    /// 启用后会根据实体类自动创建或更新数据库表结构，生产环境建议关闭
    /// </remarks>
    public bool EnableAutoSyncStructure { get; set; } = false;

    /// <summary>
    /// 是否启用 SQL 命令监控
    /// </summary>
    /// <remarks>
    /// 启用后会记录所有执行的 SQL 命令，用于调试和性能分析
    /// </remarks>
    public bool EnableMonitorCommand { get; set; } = true;

    /// <summary>
    /// 是否启用敏感数据日志记录
    /// </summary>
    /// <remarks>
    /// 启用后会在日志中记录 SQL 参数值，可能包含敏感信息，生产环境建议关闭
    /// </remarks>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// 是否启用详细错误信息
    /// </summary>
    /// <remarks>
    /// 启用后会在异常中包含更多调试信息，生产环境建议关闭
    /// </remarks>
    public bool EnableDetailedErrors { get; set; } = false;

    #endregion

    #region 性能和超时配置

    /// <summary>
    /// 默认查询超时时间
    /// </summary>
    /// <remarks>
    /// 单个查询的最大执行时间，超时后会取消查询，默认 30 秒
    /// </remarks>
    public TimeSpan DefaultQueryTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// 慢查询阈值
    /// </summary>
    /// <remarks>
    /// 超过此时间的查询会被标记为慢查询并记录日志，默认 1 秒
    /// </remarks>
    public TimeSpan SlowQueryThreshold { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// 最大连接池大小
    /// </summary>
    /// <remarks>
    /// 连接池中允许的最大连接数，默认 100
    /// </remarks>
    public int MaxConnectionPoolSize { get; set; } = 100;

    /// <summary>
    /// 最小连接池大小
    /// </summary>
    /// <remarks>
    /// 连接池中保持的最小连接数，默认 5
    /// </remarks>
    public int MinConnectionPoolSize { get; set; } = 5;

    #endregion
}

/// <summary>
/// 分页结果
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class PagedResult<T>
{
    /// <summary>
    /// 数据项
    /// </summary>
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页索引（从1开始）
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages { get; set; }

    /// <summary>
    /// 是否有上一页
    /// </summary>
    public bool HasPreviousPage => PageIndex > 1;

    /// <summary>
    /// 是否有下一页
    /// </summary>
    public bool HasNextPage => PageIndex < TotalPages;

    /// <summary>
    /// 是否为第一页
    /// </summary>
    public bool IsFirstPage => PageIndex == 1;

    /// <summary>
    /// 是否为最后一页
    /// </summary>
    public bool IsLastPage => TotalPages == 0 ? PageIndex == 1 : PageIndex == TotalPages;

    /// <summary>
    /// 当前页起始记录索引（从1开始）
    /// </summary>
    public int StartIndex => (PageIndex - 1) * PageSize + 1;

    /// <summary>
    /// 当前页结束记录索引
    /// </summary>
    public int EndIndex => Math.Min(PageIndex * PageSize, TotalCount);

    /// <summary>
    /// 创建空的分页结果
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>空的分页结果</returns>
    public static PagedResult<T> Empty(int pageIndex = 1, int pageSize = 10)
    {
        return new PagedResult<T>
        {
            Items = new List<T>(),
            PageIndex = pageIndex,
            PageSize = pageSize,
            TotalCount = 0,
            TotalPages = 0
        };
    }

    /// <summary>
    /// 创建分页结果
    /// </summary>
    /// <param name="items">数据</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="totalCount">总记录数</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> Create(IList<T> items, int pageIndex, int pageSize, long totalCount)
    {
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        return new PagedResult<T>
        {
            Items = items?.ToList() ?? new List<T>(),
            PageIndex = pageIndex,
            PageSize = pageSize,
            TotalCount = (int)totalCount,
            TotalPages = totalPages
        };
    }

    /// <summary>
    /// 转换为其他类型的分页结果
    /// </summary>
    /// <typeparam name="TResult">目标类型</typeparam>
    /// <param name="converter">转换函数</param>
    /// <returns>转换后的分页结果</returns>
    public PagedResult<TResult> Map<TResult>(Func<T, TResult> converter)
    {
        return new PagedResult<TResult>
        {
            Items = Items.Select(converter).ToList(),
            PageIndex = PageIndex,
            PageSize = PageSize,
            TotalCount = TotalCount,
            TotalPages = TotalPages
        };
    }

    /// <summary>
    /// 获取分页信息摘要
    /// </summary>
    /// <returns>分页信息字符串</returns>
    public string GetSummary()
    {
        if (TotalCount == 0)
            return "没有找到数据";

        return $"第 {StartIndex}-{EndIndex} 条，共 {TotalCount} 条记录，第 {PageIndex}/{TotalPages} 页";
    }

    /// <summary>
    /// 重写ToString方法
    /// </summary>
    /// <returns>分页信息字符串</returns>
    public override string ToString()
    {
        return GetSummary();
    }
}

/// <summary>
/// YDataType 扩展方法
/// </summary>
public static class YDataTypeExtensions
{
    /// <summary>
    /// 转换为 FreeSql.DataType
    /// </summary>
    /// <param name="dataType">YData 数据库类型</param>
    /// <returns>FreeSql 数据库类型</returns>
    public static FreeSql.DataType ToFreeSqlDataType(this YDataType dataType)
    {
        return dataType switch
        {
            YDataType.SqlServer => FreeSql.DataType.SqlServer,
            YDataType.MySql => FreeSql.DataType.MySql,
            YDataType.PostgreSQL => FreeSql.DataType.PostgreSQL,
            YDataType.Sqlite => FreeSql.DataType.Sqlite,
            YDataType.Oracle => FreeSql.DataType.Oracle,
            _ => FreeSql.DataType.SqlServer
        };
    }
}

/// <summary>
/// 数据库连接信息
/// </summary>
public class YDatabaseInfo
{
    /// <summary>
    /// 数据库名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// 数据库类型
    /// </summary>
    public YDataType DataType { get; set; }

    /// <summary>
    /// 是否为默认数据库
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime LastUsedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 数据库描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}
