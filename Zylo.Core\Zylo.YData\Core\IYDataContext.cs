namespace Zylo.YData;

/// <summary>
/// YData 核心数据访问接口
/// <para>定义了 YData 框架的核心数据访问功能，提供统一的数据操作抽象</para>
/// </summary>
/// <remarks>
/// 此接口封装了以下核心功能：
/// <list type="bullet">
/// <item>基础 CRUD 操作接口（Select、Insert、Update、Delete）</item>
/// <item>简化的 CRUD 方法（GetAsync、InsertAsync 等）</item>
/// <item>分页查询支持</item>
/// <item>事务操作支持</item>
/// <item>FreeSql 原生功能访问</item>
/// </list>
/// </remarks>
public interface IYDataContext : IDisposable
{
    #region 公共属性

    /// <summary>
    /// 获取底层 FreeSql 实例
    /// </summary>
    /// <remarks>
    /// 提供对 FreeSql 原生功能的直接访问，用于高级操作和扩展
    /// </remarks>
    IFreeSql FreeSql { get; }

    #endregion

    #region 基础 CRUD 操作接口

    /// <summary>
    /// 创建查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>查询接口，支持链式调用</returns>
    /// <remarks>
    /// 返回 FreeSql 的 ISelect 接口，支持复杂查询操作
    /// </remarks>
    ISelect<T> Select<T>() where T : class;

    /// <summary>
    /// 创建插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>插入接口，支持批量插入</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IInsert 接口，支持批量操作和高级配置
    /// </remarks>
    IInsert<T> Insert<T>() where T : class;

    /// <summary>
    /// 创建更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>更新接口，支持条件更新</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IUpdate 接口，支持复杂的更新条件
    /// </remarks>
    IUpdate<T> Update<T>() where T : class;

    /// <summary>
    /// 创建删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>删除接口，支持条件删除</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IDelete 接口，支持复杂的删除条件
    /// </remarks>
    IDelete<T> Delete<T>() where T : class;

    #endregion

    #region 简化的 CRUD 方法

    /// <summary>
    /// 根据主键获取实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>实体对象，不存在时返回 null</returns>
    /// <remarks>
    /// 这是一个便捷方法，等同于 Select&lt;T&gt;().Where(主键 == id).First()
    /// </remarks>
    Task<T?> GetAsync<T>(object id) where T : class;

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>实体列表</returns>
    /// <remarks>
    /// 注意：此方法会加载表中所有数据，大表慎用
    /// </remarks>
    Task<List<T>> GetAllAsync<T>() where T : class;

    /// <summary>
    /// 插入单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 自动处理自增主键，插入后实体的主键会被更新
    /// </remarks>
    Task<int> InsertAsync<T>(T entity) where T : class;

    /// <summary>
    /// 更新单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 根据主键更新实体，只更新非空字段
    /// </remarks>
    Task<int> UpdateAsync<T>(T entity) where T : class;

    /// <summary>
    /// 根据主键删除实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 物理删除，数据将永久丢失
    /// </remarks>
    Task<int> DeleteAsync<T>(object id) where T : class;

    #endregion

    #region 高级查询和事务方法

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="pageIndex">页索引（从1开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <param name="where">查询条件（可选）</param>
    /// <returns>分页结果，包含数据和分页信息</returns>
    /// <remarks>
    /// 自动计算总记录数和总页数，适用于需要分页显示的场景
    /// </remarks>
    Task<PagedResult<T>> GetPagedAsync<T>(int pageIndex, int pageSize, Expression<Func<T, bool>>? where = null) where T : class;

    /// <summary>
    /// 事务操作（有返回值）
    /// </summary>
    /// <typeparam name="TResult">返回值类型</typeparam>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <returns>操作结果</returns>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚
    /// </remarks>
    Task<TResult> TransactionAsync<TResult>(Func<Task<TResult>> operation, IsolationLevel? isolationLevel = null);

    /// <summary>
    /// 事务操作（无返回值）
    /// </summary>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚
    /// </remarks>
    Task TransactionAsync(Func<Task> operation, IsolationLevel? isolationLevel = null);

    #endregion
}
