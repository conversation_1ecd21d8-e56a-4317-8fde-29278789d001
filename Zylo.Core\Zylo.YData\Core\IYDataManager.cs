namespace Zylo.YData;

/// <summary>
/// 多数据库管理器接口
/// <para>定义了多数据库环境下的数据库注册、管理和切换功能</para>
/// </summary>
/// <remarks>
/// 此接口提供以下核心功能：
/// <list type="bullet">
/// <item>动态注册和管理多个数据库连接</item>
/// <item>支持设置和获取默认数据库</item>
/// <item>提供数据库连接测试和统计功能</item>
/// <item>支持数据库的添加、移除和查询</item>
/// <item>线程安全的数据库操作</item>
/// </list>
/// </remarks>
public interface IYDataManager
{
    #region 数据库注册和管理

    /// <summary>
    /// 注册数据库（简化版本）
    /// </summary>
    /// <param name="name">数据库名称，用于后续引用</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <param name="dataType">数据库类型</param>
    /// <remarks>
    /// 便捷方法，内部会创建 YDataOptions 并调用完整版本的注册方法
    /// </remarks>
    void RegisterDatabase(string name, string connectionString, YDataType dataType);

    /// <summary>
    /// 注册数据库（完整版本）
    /// </summary>
    /// <param name="name">数据库名称，用于后续引用</param>
    /// <param name="options">详细配置选项</param>
    /// <remarks>
    /// 支持详细配置，如连接池大小、超时时间等
    /// </remarks>
    void RegisterDatabase(string name, YDataOptions options);

    /// <summary>
    /// 获取指定数据库的上下文
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库上下文</returns>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    /// <remarks>
    /// 每次获取时会更新数据库的最后使用时间
    /// </remarks>
    IYDataContext GetDatabase(string name);

    /// <summary>
    /// 获取默认数据库上下文
    /// </summary>
    /// <returns>默认数据库上下文</returns>
    /// <remarks>
    /// 返回通过 SetDefaultDatabase() 设置的默认数据库
    /// </remarks>
    IYDataContext GetDefaultDatabase();

    /// <summary>
    /// 设置默认数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <remarks>
    /// 设置后，所有不指定数据库的操作都会使用此数据库
    /// </remarks>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    void SetDefaultDatabase(string name);

    #endregion

    #region 数据库查询和信息

    /// <summary>
    /// 获取所有已注册的数据库名称
    /// </summary>
    /// <returns>数据库名称列表</returns>
    /// <remarks>
    /// 返回所有通过 RegisterDatabase 注册的数据库名称
    /// </remarks>
    IEnumerable<string> GetDatabaseNames();

    /// <summary>
    /// 检查数据库是否已注册
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>是否已注册</returns>
    /// <remarks>
    /// 只检查是否在管理器中注册，不检查实际数据库文件是否存在
    /// </remarks>
    bool DatabaseExists(string name);

    /// <summary>
    /// 移除已注册的数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <remarks>
    /// 只是从注册列表中移除，不会删除实际的数据库文件
    /// </remarks>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    void RemoveDatabase(string name);

    /// <summary>
    /// 获取指定数据库的详细信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库信息，不存在时返回 null</returns>
    /// <remarks>
    /// 包含连接字符串、数据库类型、是否为默认数据库等信息
    /// </remarks>
    YDatabaseInfo? GetDatabaseInfo(string name);

    /// <summary>
    /// 获取所有已注册数据库的详细信息
    /// </summary>
    /// <returns>数据库信息列表</returns>
    /// <remarks>
    /// 用于管理界面显示或配置导出等场景
    /// </remarks>
    IEnumerable<YDatabaseInfo> GetAllDatabaseInfo();

    #endregion

    #region 数据库测试和统计

    /// <summary>
    /// 测试指定数据库的连接
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>连接是否成功</returns>
    /// <remarks>
    /// 执行简单的连接测试，不会影响现有连接
    /// </remarks>
    Task<bool> TestConnectionAsync(string name);

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>统计信息，包含表数量、记录数等</returns>
    /// <remarks>
    /// 用于监控和管理数据库，获取表数量、记录数等统计信息
    /// </remarks>
    Task<YDatabaseStats> GetDatabaseStatsAsync(string name);

    #endregion
}

/// <summary>
/// 数据库统计信息
/// </summary>
/// <remarks>
/// 包含数据库的运行时统计信息，用于监控和性能分析
/// </remarks>
public class YDatabaseStats
{
    /// <summary>
    /// 数据库名称
    /// </summary>
    /// <remarks>
    /// 在多数据库环境中用于标识具体的数据库
    /// </remarks>
    public string DatabaseName { get; set; } = string.Empty;

    /// <summary>
    /// 连接状态
    /// </summary>
    /// <remarks>
    /// 指示数据库当前是否可以正常连接
    /// </remarks>
    public bool IsConnected { get; set; }

    /// <summary>
    /// 表数量
    /// </summary>
    /// <remarks>
    /// 数据库中的表总数
    /// </remarks>
    public int TableCount { get; set; }

    /// <summary>
    /// 总记录数（估算）
    /// </summary>
    /// <remarks>
    /// 所有表的记录数总和，可能是估算值
    /// </remarks>
    public long TotalRecords { get; set; }

    /// <summary>
    /// 数据库大小（字节）
    /// </summary>
    /// <remarks>
    /// 数据库文件的物理大小，单位为字节
    /// </remarks>
    public long DatabaseSize { get; set; }

    /// <summary>
    /// 最后查询时间
    /// </summary>
    /// <remarks>
    /// 记录最近一次执行查询的时间
    /// </remarks>
    public DateTime LastQueryTime { get; set; }

    /// <summary>
    /// 查询次数
    /// </summary>
    /// <remarks>
    /// 累计执行的查询总数
    /// </remarks>
    public long QueryCount { get; set; }

    /// <summary>
    /// 平均查询时间（毫秒）
    /// </summary>
    /// <remarks>
    /// 所有查询的平均执行时间，用于性能分析
    /// </remarks>
    public double AverageQueryTime { get; set; }
}
