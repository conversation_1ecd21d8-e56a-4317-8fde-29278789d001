namespace Zylo.YData;

/// <summary>
/// 服务集合扩展方法
/// <para>提供 YData 框架的依赖注入配置，支持多种配置方式</para>
/// </summary>
/// <remarks>
/// 此扩展类提供以下功能：
/// <list type="bullet">
/// <item>零参数自动配置（从 appsettings.json 读取）</item>
/// <item>单参数配置（只需连接字符串，自动检测数据库类型）</item>
/// <item>双参数配置（连接字符串 + 数据库类型）</item>
/// <item>完整配置（使用 YDataOptions 进行详细配置）</item>
/// </list>
/// </remarks>
public static class ServiceCollectionExtensions
{
    #region 自动配置方法

    /// <summary>
    /// 添加 YData 服务（零参数 - 自动从配置文件读取）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合，支持链式调用</returns>
    /// <exception cref="InvalidOperationException">未找到连接字符串时抛出</exception>
    /// <remarks>
    /// 自动从 appsettings.json 中读取 "DefaultConnection" 连接字符串
    /// </remarks>
    /// <example>
    /// <code>
    /// // 在 Startup.cs 或 Program.cs 中
    /// services.AddYDataAuto();
    /// </code>
    /// </example>
    public static IServiceCollection AddYDataAuto(this IServiceCollection services)
    {
        // 尝试从配置中获取连接字符串
        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetService<IConfiguration>();
        var connectionString = configuration?.GetConnectionString("DefaultConnection") ?? "";

        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("No connection string found. Please provide a connection string or configure 'DefaultConnection' in appsettings.json");
        }

        return AddYDataAuto(services, connectionString);
    }

    /// <summary>
    /// 添加 YData 服务（单参数 - 连接字符串自动检测数据库类型）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <returns>服务集合，支持链式调用</returns>
    /// <remarks>
    /// 根据连接字符串的特征自动检测数据库类型
    /// </remarks>
    /// <example>
    /// <code>
    /// // SQLite 数据库
    /// services.AddYDataAuto("Data Source=app.db");
    ///
    /// // SQL Server 数据库
    /// services.AddYDataAuto("Server=localhost;Database=MyApp;Trusted_Connection=true;");
    /// </code>
    /// </example>
    public static IServiceCollection AddYDataAuto(this IServiceCollection services, string connectionString)
    {
        var dataType = DetectDataTypeFromConnectionString(connectionString);
        return AddYDataAuto(services, connectionString, dataType);
    }

    /// <summary>
    /// 添加 YData 服务（双参数 - 连接字符串 + 数据库类型）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <param name="dataType">数据库类型</param>
    /// <returns>服务集合，支持链式调用</returns>
    /// <remarks>
    /// 推荐使用此方法，明确指定数据库类型避免自动检测错误
    /// </remarks>
    /// <example>
    /// <code>
    /// // 明确指定 SQLite
    /// services.AddYDataAuto("Data Source=app.db", YDataType.Sqlite);
    ///
    /// // 明确指定 MySQL
    /// services.AddYDataAuto("Server=localhost;Database=myapp;Uid=root;Pwd=******;", YDataType.MySql);
    /// </code>
    /// </example>
    public static IServiceCollection AddYDataAuto(this IServiceCollection services, string connectionString, YDataType dataType)
    {
        return services.AddYData(options =>
        {
            options.ConnectionString = connectionString;
            options.DataType = dataType;
            ApplyIntelligentDefaults(options);
        });
    }

    #endregion

    #region 完整配置方法

    /// <summary>
    /// 添加 YData 服务（完整配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项委托</param>
    /// <returns>服务集合，支持链式调用</returns>
    /// <remarks>
    /// 提供最灵活的配置方式，支持所有 YDataOptions 的配置项
    /// </remarks>
    /// <example>
    /// <code>
    /// services.AddYData(options =>
    /// {
    ///     options.ConnectionString = "Data Source=app.db";
    ///     options.DataType = YDataType.Sqlite;
    ///     options.EnableAutoSyncStructure = true;
    ///     options.DefaultQueryTimeout = TimeSpan.FromSeconds(60);
    ///     options.EnableMonitorCommand = true;
    /// });
    /// </code>
    /// </example>
    public static IServiceCollection AddYData(this IServiceCollection services, Action<YDataOptions> configureOptions)
    {
        // 配置选项
        services.Configure(configureOptions);

        // 注册 FreeSql
        services.AddSingleton<IFreeSql>(provider =>
        {
            var options = provider.GetRequiredService<IOptions<YDataOptions>>().Value;
            var logger = provider.GetService<ILogger<IFreeSql>>();

            var builder = new FreeSqlBuilder()
                .UseConnectionString(options.DataType.ToFreeSqlDataType(), options.ConnectionString);

            // 性能配置
            if (options.EnableAutoSyncStructure)
            {
                builder.UseAutoSyncStructure(true);
            }

            // 命名策略
            if (options.NamingStrategy.HasValue)
            {
                builder.UseNameConvert(options.NamingStrategy.Value);
            }

            // 监控命令
            if (options.EnableMonitorCommand)
            {
                builder.UseMonitorCommand(cmd =>
                {
                    // FreeSql 的监控命令回调，记录 SQL 执行信息
                    logger?.LogDebug("SQL: {Sql}", cmd.CommandText);

                    // 如果需要记录慢查询，可以在这里添加时间检测逻辑
                    // 注意：FreeSql 的 DbCommand 可能没有 ElapsedMilliseconds 属性
                    // 需要使用其他方式来测量执行时间
                });
            }

            var freeSql = builder.Build();

            logger?.LogInformation("FreeSql initialized with {DataType} database", options.DataType);
            return freeSql;
        });

        // 注册核心服务
        services.AddScoped<IYDataContext, YDataContext>();

        return services;
    }

    /// <summary>
    /// 应用智能默认配置
    /// </summary>
    /// <param name="options">配置选项</param>
    private static void ApplyIntelligentDefaults(YDataOptions options)
    {
        // 根据数据库类型设置默认配置
        switch (options.DataType)
        {
            case YDataType.Sqlite:
                options.NamingStrategy = FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower;
                options.EnableAutoSyncStructure = true; // SQLite 通常用于开发和测试
                break;

            case YDataType.SqlServer:
                options.NamingStrategy = FreeSql.Internal.NameConvertType.None;
                break;

            case YDataType.MySql:
                options.NamingStrategy = FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower;
                break;

            case YDataType.PostgreSQL:
                options.NamingStrategy = FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower;
                break;
        }

        // 通用默认配置
        options.EnableMonitorCommand = true;
        options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
        options.SlowQueryThreshold = TimeSpan.FromSeconds(1);
        options.MaxConnectionPoolSize = 100;
        options.MinConnectionPoolSize = 5;
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 自动检测数据库类型
    /// </summary>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <returns>检测到的数据库类型</returns>
    /// <remarks>
    /// 根据连接字符串的关键字自动判断数据库类型：
    /// <list type="bullet">
    /// <item>包含 ".db"、".sqlite" 或 ":memory:" → SQLite</item>
    /// <item>包含 "host=" 和 "port=" → PostgreSQL</item>
    /// <item>包含 "server=" 和 "uid=" → MySQL</item>
    /// <item>其他情况 → SQL Server</item>
    /// </list>
    /// </remarks>
    private static YDataType DetectDataTypeFromConnectionString(string connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
            return YDataType.SqlServer;

        var lower = connectionString.ToLower();

        // SQLite 检测 - 文件数据库或内存数据库
        if (lower.Contains("data source=") && (lower.Contains(".db") || lower.Contains(".sqlite") || lower.Contains(":memory:")))
            return YDataType.Sqlite;

        // PostgreSQL 检测 - 通常包含 host 和 port
        if (lower.Contains("host=") || lower.Contains("server=") && lower.Contains("port=") && lower.Contains("database="))
            return YDataType.PostgreSQL;

        // MySQL 检测 - 通常包含 server、database 和 uid
        if (lower.Contains("server=") && lower.Contains("database=") && (lower.Contains("uid=") || lower.Contains("user id=")))
            return YDataType.MySql;

        // SQL Server 检测 - 通常包含 server 或 data source
        if (lower.Contains("server=") || lower.Contains("data source=") || lower.Contains("initial catalog="))
            return YDataType.SqlServer;

        // 默认返回 SQL Server
        return YDataType.SqlServer;
    }

    #endregion
}
