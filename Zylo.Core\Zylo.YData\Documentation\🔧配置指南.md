# 🔧 Zylo.YData 配置指南

本指南详细介绍了 Zylo.YData 的所有配置选项，帮助您根据不同环境和需求进行最佳配置。

## 📋 配置方式概览

Zylo.YData 提供多种配置方式，适应不同的使用场景：

| 配置方式 | 适用场景 | 复杂度 | 推荐度 |
|---------|---------|--------|--------|
| 自动配置 | 快速开发、原型 | ⭐ | 🔥🔥🔥 |
| 依赖注入配置 | 生产环境、Web应用 | ⭐⭐ | 🔥🔥🔥🔥 |
| 详细配置 | 高级定制、性能调优 | ⭐⭐⭐ | 🔥🔥 |
| 多数据库配置 | 微服务、多租户 | ⭐⭐⭐⭐ | 🔥🔥🔥 |

## ⚡ 自动配置

### 基础自动配置
最简单的配置方式，适合快速开发和测试。

```csharp
using Zylo.YData;

// SQLite（推荐用于开发）
YData.ConfigureAuto("Data Source=myapp.db");

// 内存数据库（适合单元测试）
YData.ConfigureAuto("Data Source=:memory:");

// 文件数据库（指定完整路径）
YData.ConfigureAuto(@"Data Source=C:\MyApp\data\app.db");
```

### 指定数据库类型
当连接字符串无法自动识别数据库类型时使用。

```csharp
// SQL Server
YData.ConfigureAuto(
    "Server=localhost;Database=MyApp;Trusted_Connection=true;", 
    YDataType.SqlServer);

// MySQL
YData.ConfigureAuto(
    "Server=localhost;Database=myapp;Uid=root;Pwd=******;", 
    YDataType.MySql);

// PostgreSQL
YData.ConfigureAuto(
    "Host=localhost;Database=myapp;Username=postgres;Password=******;", 
    YDataType.PostgreSQL);

// Oracle
YData.ConfigureAuto(
    "Data Source=localhost:1521/XE;User Id=hr;Password=******;", 
    YDataType.Oracle);
```

## 💉 依赖注入配置

### ASP.NET Core 配置
推荐在生产环境中使用的配置方式。

#### Program.cs 配置
```csharp
using Zylo.YData;

var builder = WebApplication.CreateBuilder(args);

// 方式1：从配置文件读取
builder.Services.AddYDataAuto();  // 自动读取 "DefaultConnection"

// 方式2：直接指定连接字符串
builder.Services.AddYDataAuto("Data Source=myapp.db");

// 方式3：指定数据库类型
builder.Services.AddYDataAuto("Data Source=myapp.db", YDataType.Sqlite);

// 方式4：详细配置
builder.Services.AddYData(options =>
{
    options.ConnectionString = builder.Configuration.GetConnectionString("DefaultConnection")!;
    options.DataType = YDataType.Sqlite;
    options.EnableAutoSyncStructure = builder.Environment.IsDevelopment();
    options.EnableMonitorCommand = true;
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
});

var app = builder.Build();
```

#### appsettings.json 配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=myapp.db",
    "LogsConnection": "Data Source=logs.db",
    "ReportsConnection": "Server=localhost;Database=Reports;Trusted_Connection=true;"
  },
  "YData": {
    "EnableAutoSyncStructure": false,
    "EnableMonitorCommand": true,
    "DefaultQueryTimeout": "00:00:30",
    "SlowQueryThreshold": "00:00:01"
  }
}
```

### 在服务中使用
```csharp
public class UserService
{
    private readonly IYDataContext _context;
    private readonly IYDataManager _manager;
    
    public UserService(IYDataContext context, IYDataManager manager)
    {
        _context = context;
        _manager = manager;
    }
    
    public async Task<List<User>> GetUsersAsync()
    {
        return await _context.Select<User>().ToListAsync();
    }
}
```

## 🔧 详细配置选项

### YDataOptions 完整配置
```csharp
YData.Configure(options =>
{
    // === 基础数据库配置 ===
    options.ConnectionString = "Data Source=myapp.db";
    options.DataType = YDataType.Sqlite;
    options.NamingStrategy = FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower;
    
    // === 功能开关配置 ===
    options.EnableAutoSyncStructure = false;        // 生产环境建议关闭
    options.EnableMonitorCommand = true;             // 启用SQL监控
    options.EnableSensitiveDataLogging = false;     // 生产环境建议关闭
    options.EnableDetailedErrors = false;           // 生产环境建议关闭
    
    // === 性能和超时配置 ===
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
    options.SlowQueryThreshold = TimeSpan.FromSeconds(1);
    options.MaxConnectionPoolSize = 100;
    options.MinConnectionPoolSize = 5;
});
```

### 配置选项详解

#### 基础数据库配置
```csharp
// 数据库类型
options.DataType = YDataType.Sqlite;  // SqlServer, MySql, PostgreSQL, Sqlite, Oracle

// 连接字符串
options.ConnectionString = "Data Source=myapp.db";

// 命名策略
options.NamingStrategy = FreeSql.Internal.NameConvertType.PascalCaseToUnderscoreWithLower;
// 可选值：
// - None: 不转换
// - PascalCaseToUnderscoreWithLower: UserName -> user_name
// - ToLower: UserName -> username
// - ToUpper: UserName -> USERNAME
```

#### 功能开关配置
```csharp
// 自动同步数据库结构（开发环境可开启，生产环境建议关闭）
options.EnableAutoSyncStructure = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";

// SQL命令监控（用于调试和性能分析）
options.EnableMonitorCommand = true;

// 敏感数据日志（包含SQL参数值，生产环境建议关闭）
options.EnableSensitiveDataLogging = false;

// 详细错误信息（生产环境建议关闭）
options.EnableDetailedErrors = false;
```

#### 性能和超时配置
```csharp
// 默认查询超时时间
options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);

// 慢查询阈值（超过此时间的查询会被记录）
options.SlowQueryThreshold = TimeSpan.FromSeconds(1);

// 连接池配置
options.MaxConnectionPoolSize = 100;  // 最大连接数
options.MinConnectionPoolSize = 5;    // 最小连接数
```

## 🗄️ 多数据库配置

### 注册多个数据库
```csharp
// 注册主数据库
YData.Manager.RegisterDatabase("main", new YDataOptions
{
    ConnectionString = "Data Source=main.db",
    DataType = YDataType.Sqlite,
    EnableAutoSyncStructure = true
});

// 注册日志数据库
YData.Manager.RegisterDatabase("logs", new YDataOptions
{
    ConnectionString = "Data Source=logs.db",
    DataType = YDataType.Sqlite,
    EnableMonitorCommand = false  // 日志库不需要监控
});

// 注册报表数据库（SQL Server）
YData.Manager.RegisterDatabase("reports", new YDataOptions
{
    ConnectionString = "Server=localhost;Database=Reports;Trusted_Connection=true;",
    DataType = YDataType.SqlServer,
    DefaultQueryTimeout = TimeSpan.FromMinutes(5)  // 报表查询可能较慢
});

// 设置默认数据库
YData.Manager.SetDefaultDatabase("main");
```

### 多数据库使用模式
```csharp
public class DataService
{
    private readonly IYDataManager _manager;
    
    public DataService(IYDataManager manager)
    {
        _manager = manager;
    }
    
    public async Task ProcessOrderAsync(Order order)
    {
        // 在主数据库中保存订单
        var mainDb = _manager.GetDatabase("main");
        await mainDb.InsertAsync(order);
        
        // 在日志数据库中记录操作
        var logDb = _manager.GetDatabase("logs");
        await logDb.InsertAsync(new OperationLog 
        { 
            Action = "CreateOrder", 
            OrderId = order.Id,
            Timestamp = DateTime.Now 
        });
        
        // 在报表数据库中更新统计
        var reportDb = _manager.GetDatabase("reports");
        await reportDb.Update<DailySales>()
            .Set(s => s.TotalAmount, s => s.TotalAmount + order.Amount)
            .Where(s => s.Date == DateTime.Today)
            .ExecuteAffrowsAsync();
    }
}
```

## 🌍 环境特定配置

### 开发环境配置
```csharp
if (builder.Environment.IsDevelopment())
{
    builder.Services.AddYData(options =>
    {
        options.ConnectionString = "Data Source=dev.db";
        options.DataType = YDataType.Sqlite;
        options.EnableAutoSyncStructure = true;      // 开发环境自动同步表结构
        options.EnableMonitorCommand = true;         // 启用SQL监控
        options.EnableSensitiveDataLogging = true;   // 显示SQL参数
        options.EnableDetailedErrors = true;         // 显示详细错误
        options.DefaultQueryTimeout = TimeSpan.FromMinutes(1);
    });
}
```

### 测试环境配置
```csharp
if (builder.Environment.IsEnvironment("Testing"))
{
    builder.Services.AddYData(options =>
    {
        options.ConnectionString = "Data Source=:memory:";  // 内存数据库
        options.DataType = YDataType.Sqlite;
        options.EnableAutoSyncStructure = true;
        options.EnableMonitorCommand = false;        // 测试时不需要监控
        options.DefaultQueryTimeout = TimeSpan.FromSeconds(10);
    });
}
```

### 生产环境配置
```csharp
if (builder.Environment.IsProduction())
{
    builder.Services.AddYData(options =>
    {
        options.ConnectionString = builder.Configuration.GetConnectionString("Production")!;
        options.DataType = YDataType.SqlServer;
        options.EnableAutoSyncStructure = false;     // 生产环境不自动同步
        options.EnableMonitorCommand = true;         // 保留监控用于性能分析
        options.EnableSensitiveDataLogging = false;  // 不记录敏感数据
        options.EnableDetailedErrors = false;        // 不暴露详细错误
        options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
        options.SlowQueryThreshold = TimeSpan.FromMilliseconds(500);
        options.MaxConnectionPoolSize = 200;         // 生产环境增大连接池
        options.MinConnectionPoolSize = 10;
    });
}
```

## 🔒 安全配置

### 连接字符串安全
```csharp
// 使用用户机密（开发环境）
// dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Server=...;Password=..."

// 使用环境变量（生产环境）
var connectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECTION_STRING") 
    ?? builder.Configuration.GetConnectionString("DefaultConnection");

builder.Services.AddYDataAuto(connectionString);
```

### 敏感数据保护
```csharp
builder.Services.AddYData(options =>
{
    options.ConnectionString = GetSecureConnectionString();
    options.EnableSensitiveDataLogging = false;  // 生产环境必须关闭
    options.EnableDetailedErrors = false;        // 避免泄露内部信息
});
```

## 📊 性能优化配置

### 高性能配置
```csharp
builder.Services.AddYData(options =>
{
    // 连接池优化
    options.MaxConnectionPoolSize = 200;
    options.MinConnectionPoolSize = 20;
    
    // 超时优化
    options.DefaultQueryTimeout = TimeSpan.FromSeconds(15);
    options.SlowQueryThreshold = TimeSpan.FromMilliseconds(200);
    
    // 功能优化
    options.EnableMonitorCommand = true;   // 保留监控用于性能分析
    options.EnableAutoSyncStructure = false;  // 关闭自动同步提升性能
});
```

### 批量操作优化
```csharp
// 在具体使用时配置批量大小
var users = GetLargeUserList();
await YData.Insert<User>().YBatchInsertAsync(users, batchSize: 2000);  // 增大批次
```

## ❓ 常见配置问题

### 问题1：连接字符串格式
```csharp
// ❌ 错误
"Data Source=C:\MyApp\data.db"  // 路径分隔符问题

// ✅ 正确
@"Data Source=C:\MyApp\data.db"  // 使用原始字符串
"Data Source=C:\\MyApp\\data.db"  // 或转义反斜杠
```

### 问题2：数据库类型自动检测失败
```csharp
// ❌ 可能失败
YData.ConfigureAuto("Server=myserver;Database=mydb;...");

// ✅ 明确指定
YData.ConfigureAuto("Server=myserver;Database=mydb;...", YDataType.SqlServer);
```

### 问题3：依赖注入配置冲突
```csharp
// ❌ 重复配置
builder.Services.AddYDataAuto("...");
YData.ConfigureAuto("...");  // 会覆盖上面的配置

// ✅ 选择一种方式
builder.Services.AddYDataAuto("...");  // 推荐用于Web应用
```

---

**🎯 下一步：** 查看 [最佳实践](最佳实践.md) 了解如何优化您的配置。
