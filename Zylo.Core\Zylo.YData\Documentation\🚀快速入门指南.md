# 🚀 Zylo.YData 快速入门指南

欢迎使用 Zylo.YData！这是一个基于 FreeSql 的现代化数据访问层，让数据库操作变得简单而强大。

## 📦 安装

### NuGet 包管理器

```bash
dotnet add package Zylo.YData
```

### Package Manager Console

```powershell
Install-Package Zylo.YData
```

## ⚡ 5分钟快速上手

### 第一步：定义实体类

```csharp
using System.ComponentModel.DataAnnotations;

public class User
{
    [Key]
    public int Id { get; set; }
    
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;
    
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    public int Age { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    public DateTime CreateTime { get; set; } = DateTime.Now;
}
```

### 第二步：配置数据库

#### 方式一：静态配置（最简单）

```csharp
using Zylo.YData;

// SQLite 数据库（推荐用于开发和测试）
YData.ConfigureAuto("Data Source=myapp.db");

// SQL Server 数据库
YData.ConfigureAuto("Server=localhost;Database=MyApp;Trusted_Connection=true;", YDataType.SqlServer);

// MySQL 数据库
YData.ConfigureAuto("Server=localhost;Database=myapp;Uid=root;Pwd=******;", YDataType.MySql);
```

#### 方式二：依赖注入配置（推荐用于生产环境）

```csharp
// Program.cs 或 Startup.cs
using Zylo.YData;

var builder = WebApplication.CreateBuilder(args);

// 自动检测数据库类型
builder.Services.AddYDataAuto("Data Source=myapp.db");

// 或者明确指定数据库类型
builder.Services.AddYDataAuto("Data Source=myapp.db", YDataType.Sqlite);

var app = builder.Build();
```

### 第三步：基础 CRUD 操作

#### 插入数据

```csharp
// 插入单个用户
var user = new User 
{ 
    Name = "张三", 
    Email = "<EMAIL>", 
    Age = 25 
};

await YData.InsertAsync(user);
Console.WriteLine($"用户已创建，ID: {user.Id}");

// 批量插入
var users = new[]
{
    new User { Name = "李四", Email = "<EMAIL>", Age = 30 },
    new User { Name = "王五", Email = "<EMAIL>", Age = 28 },
    new User { Name = "赵六", Email = "<EMAIL>", Age = 32 }
};

await YData.Insert<User>().AppendData(users).ExecuteAffrowsAsync();
```

#### 查询数据

```csharp
// 根据 ID 查询单个用户
var user = await YData.GetAsync<User>(1);
if (user != null)
{
    Console.WriteLine($"找到用户: {user.Name}");
}

// 查询所有活跃用户
var activeUsers = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToListAsync();

// 条件查询
var youngUsers = await YData.Select<User>()
    .Where(u => u.Age < 30)
    .Where(u => u.IsActive)
    .OrderBy(u => u.Name)
    .ToListAsync();

// 分页查询
var pagedResult = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToPagedResultAsync(pageIndex: 1, pageSize: 10);

Console.WriteLine($"总共 {pagedResult.TotalCount} 个用户，当前第 {pagedResult.PageIndex} 页");
foreach (var u in pagedResult.Items)
{
    Console.WriteLine($"- {u.Name} ({u.Age}岁)");
}
```

#### 更新数据

```csharp
// 更新单个用户
var user = await YData.GetAsync<User>(1);
if (user != null)
{
    user.Age = 26;
    user.Email = "<EMAIL>";
    await YData.UpdateAsync(user);
}

// 批量更新
await YData.Update<User>()
    .Set(u => u.IsActive, false)
    .Where(u => u.Age > 60)
    .ExecuteAffrowsAsync();
```

#### 删除数据

```csharp
// 根据 ID 删除
await YData.DeleteAsync<User>(1);

// 条件删除
await YData.Delete<User>()
    .Where(u => !u.IsActive)
    .Where(u => u.CreateTime < DateTime.Now.AddYears(-1))
    .ExecuteAffrowsAsync();
```

## 🔧 智能扩展方法

Zylo.YData 提供了丰富的扩展方法，让查询更加简洁：

### 条件查询扩展

```csharp
string? nameFilter = "张";
int? minAge = 25;

var users = await YData.Select<User>()
    .WhereIf(!string.IsNullOrEmpty(nameFilter), u => u.Name.Contains(nameFilter))
    .WhereIf(minAge.HasValue, u => u.Age >= minAge.Value)
    .ToListAsync();
```

### 分页扩展

```csharp
// 简化的分页查询
var result = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToPagedResultAsync(1, 20);

// 获取分页信息
Console.WriteLine($"第 {result.PageIndex}/{result.TotalPages} 页");
Console.WriteLine($"共 {result.TotalCount} 条记录");
```

### 聚合查询扩展

```csharp
// 统计用户数量
var totalUsers = await YData.Select<User>().YCountAsync();
var activeUsers = await YData.Select<User>().YCountAsync(u => u.IsActive);

// 计算平均年龄
var avgAge = await YData.Select<User>().YAverageAsync(u => u.Age);

// 查找最大最小年龄
var maxAge = await YData.Select<User>().YMaxAsync(u => u.Age);
var minAge = await YData.Select<User>().YMinAsync(u => u.Age);
```

## 🗄️ 多数据库支持

### 注册多个数据库

```csharp
// 注册主数据库
YData.Manager.RegisterDatabase("main", "Data Source=main.db", YDataType.Sqlite);

// 注册日志数据库
YData.Manager.RegisterDatabase("logs", "Data Source=logs.db", YDataType.Sqlite);

// 注册报表数据库
YData.Manager.RegisterDatabase("reports", 
    "Server=localhost;Database=Reports;Trusted_Connection=true;", 
    YDataType.SqlServer);

// 设置默认数据库
YData.Manager.SetDefaultDatabase("main");
```

### 使用不同数据库

```csharp
// 使用默认数据库
var users = await YData.Select<User>().ToListAsync();

// 使用指定数据库
var mainDb = YData.Manager.GetDatabase("main");
var logDb = YData.Manager.GetDatabase("logs");

// 在主数据库中查询用户
var user = await mainDb.GetAsync<User>(1);

// 在日志数据库中记录操作
await logDb.InsertAsync(new OperationLog 
{ 
    UserId = user.Id, 
    Action = "查询用户", 
    Timestamp = DateTime.Now 
});
```

## 💉 依赖注入使用

### 在服务中使用

```csharp
public class UserService
{
    private readonly IYDataContext _context;
    
    public UserService(IYDataContext context)
    {
        _context = context;
    }
    
    public async Task<List<User>> GetActiveUsersAsync()
    {
        return await _context.Select<User>()
            .Where(u => u.IsActive)
            .OrderBy(u => u.Name)
            .ToListAsync();
    }
    
    public async Task<User?> CreateUserAsync(string name, string email, int age)
    {
        var user = new User { Name = name, Email = email, Age = age };
        await _context.InsertAsync(user);
        return user;
    }
}
```

### 在控制器中使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class UsersController : ControllerBase
{
    private readonly IYDataContext _context;
    
    public UsersController(IYDataContext context)
    {
        _context = context;
    }
    
    [HttpGet]
    public async Task<ActionResult<PagedResult<User>>> GetUsers(
        int pageIndex = 1, 
        int pageSize = 20)
    {
        var result = await _context.Select<User>()
            .Where(u => u.IsActive)
            .ToPagedResultAsync(pageIndex, pageSize);
            
        return Ok(result);
    }
    
    [HttpPost]
    public async Task<ActionResult<User>> CreateUser(User user)
    {
        await _context.InsertAsync(user);
        return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
    }
}
```

## 🖥️ WPF 应用使用

### WPF 数据绑定

```csharp
// ViewModel 中使用 ObservableCollection
public class UserViewModel : INotifyPropertyChanged
{
    public ObservableCollection<User> Users { get; set; } = new();

    public async Task LoadUsersAsync()
    {
        // 使用 WPF 扩展方法
        Users = await YData.Select<User>()
            .Where(u => u.IsActive)
            .ToObservableCollectionAsync();
    }

    // 分页加载
    public async Task LoadPagedUsersAsync(int pageIndex, int pageSize)
    {
        var result = await YData.Select<User>()
            .Where(u => u.IsActive)
            .ToPagedObservableCollectionAsync(pageIndex, pageSize);

        Users = result.Items;
        TotalCount = result.TotalCount;
    }
}
```

### XAML 数据绑定

```xml
<DataGrid ItemsSource="{Binding Users}" AutoGenerateColumns="False">
    <DataGrid.Columns>
        <DataGridTextColumn Header="姓名" Binding="{Binding Name}" />
        <DataGridTextColumn Header="邮箱" Binding="{Binding Email}" />
        <DataGridTextColumn Header="年龄" Binding="{Binding Age}" />
    </DataGrid.Columns>
</DataGrid>
```

## 📊 JSON 数据导入导出

### 导出数据为 JSON

```csharp
// 导出所有用户
var count = await YDataImportExportExtensions.ExportToJsonAsync<User>("users.json");
Console.WriteLine($"导出了 {count} 个用户");

// 导出符合条件的用户
var adultCount = await YDataImportExportExtensions.ExportToJsonAsync<User>(
    "adults.json",
    u => u.Age >= 18);

// 导出到指定数据库
var count = await YDataImportExportExtensions.ExportToJsonAsync<User>(
    "backup_users.json",
    where: u => u.IsActive,
    databaseName: "backup");
```

### 从 JSON 导入数据

```csharp
// 导入用户数据（追加模式）
var count = await YDataImportExportExtensions.ImportFromJsonAsync<User>("users.json");

// 导入用户数据（替换模式 - 先清空表）
var count = await YDataImportExportExtensions.ImportFromJsonAsync<User>("users.json", clearTable: true);

Console.WriteLine($"导入了 {count} 条记录");
```

## ⚡ SQLite 零参数快速开始

### 真正的零配置

```csharp
// 什么都不用配置，直接使用内存数据库
YData.ConfigureAuto(); // 自动使用 SQLite 内存数据库

// 立即可用
var user = new User { Name = "测试用户", Email = "<EMAIL>" };
await YData.InsertAsync(user);

var users = await YData.Select<User>().ToListAsync();
Console.WriteLine($"找到 {users.Count} 个用户");
```

### 零配置文件数据库

```csharp
// 自动创建本地 SQLite 文件
YData.ConfigureAuto("Data Source=myapp.db");

// 数据会持久化到文件
var user = new User { Name = "持久用户" };
await YData.InsertAsync(user);
```

### 🎯 SQLite 超级便捷方法

```csharp
using Zylo.YData.Helpers;

// 🚀 一行代码创建 SQLite 配置文件
YConfigHelper.CreateSQLiteConfig("myapp");  // 自动创建 myapp.db 配置

// 🔄 一行代码切换数据库
YConfigHelper.SwitchToSQLite("dev");        // 切换到开发数据库
YConfigHelper.SwitchToSQLite("test");       // 切换到测试数据库
YConfigHelper.SwitchToSQLite("prod");       // 切换到生产数据库

// 🌍 多环境配置（一次设置，随时切换）
var environments = new Dictionary<string, string>
{
    ["Development"] = "dev",
    ["Testing"] = "test",
    ["Production"] = "prod"
};
YConfigHelper.CreateMultiEnvironmentSQLiteConfig(environments, "Development");

// 🔄 环境切换
YConfigHelper.SwitchEnvironment("Testing");   // 切换到测试环境
YConfigHelper.SwitchEnvironment("Production"); // 切换到生产环境

// 📊 环境信息查询
var currentEnv = YConfigHelper.GetCurrentEnvironment();
var allEnvs = YConfigHelper.GetAvailableEnvironments();
Console.WriteLine($"当前环境: {currentEnv}");
Console.WriteLine($"可用环境: {string.Join(", ", allEnvs)}");
```

## 🔧 配置文件管理

### 自动创建 appsettings.json

```csharp
using Zylo.YData.Helpers;

// 创建默认配置文件
YConfigHelper.CreateDefaultConfig("Data Source=myapp.db");

// 创建 SQL Server 配置
YConfigHelper.CreateDefaultConfig("Server=localhost;Database=MyApp;Integrated Security=true;");

// 覆盖已存在的文件
YConfigHelper.CreateDefaultConfig("Data Source=newapp.db", overwrite: true);
```

### 读取和修改连接字符串

```csharp
// 读取当前连接字符串
var currentConn = YConfigHelper.GetConnectionString();
Console.WriteLine($"当前连接: {currentConn}");

// 修改连接字符串
YConfigHelper.SetConnectionString("Data Source=newapp.db");

// 添加新的连接字符串
YConfigHelper.SetConnectionString("Data Source=logs.db", "LogsConnection");

// 获取所有连接字符串
var allConnections = YConfigHelper.GetAllConnectionStrings();
foreach (var conn in allConnections)
{
    Console.WriteLine($"{conn.Key}: {conn.Value}");
}
```

### 快速切换数据库

```csharp
// 切换到 SQLite
YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "myapp.db" });

// 切换到 SQL Server（集成认证）
YConfigHelper.SwitchDatabase("SqlServer", new { Server = "localhost", Database = "MyApp" });

// 切换到 SQL Server（用户名密码）
YConfigHelper.SwitchDatabase("SqlServer", new
{
    Server = "localhost",
    Database = "MyApp",
    UserId = "sa",
    Password = "password123"
});

// 切换到 MySQL
YConfigHelper.SwitchDatabase("MySQL", new
{
    Server = "localhost",
    Database = "myapp",
    UserId = "root",
    Password = "******"
});

// 切换后重新配置 YData
YData.ConfigureAuto(); // 自动读取新的连接字符串
```

### 配置文件备份和恢复

```csharp
// 备份当前配置
var backupFile = YConfigHelper.BackupConfig();
Console.WriteLine($"配置已备份到: {backupFile}");

// 从备份恢复
YConfigHelper.RestoreConfig("appsettings.backup.20241201_143022.json");
```

## 🎯 下一步

恭喜！您已经掌握了 Zylo.YData 的基础用法。接下来可以：

- 📖 查看 [API 参考文档](📖API参考文档.md) 了解更多方法
- 🔧 阅读 [配置指南](🔧配置指南.md) 进行高级配置
- 🔄 学习 [数据库切换策略指南](🔄数据库切换策略指南.md) 掌握多数据库管理
- 💡 学习 [最佳实践](💡最佳实践.md) 优化您的代码
- 🧪 参考 [测试指南](🧪测试指南.md) 编写测试

## ❓ 遇到问题？

- 查看 [常见问题](常见问题.md)
- 提交 [GitHub Issue](https://github.com/your-repo/issues)
- 加入我们的技术交流群

---

**🎉 开始您的 Zylo.YData 之旅吧！**
