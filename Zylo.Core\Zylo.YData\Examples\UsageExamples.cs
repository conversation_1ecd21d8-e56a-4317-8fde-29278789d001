namespace Zylo.YData.Examples;

/// <summary>
/// YData 使用示例
/// </summary>
public static class UsageExamples
{
    /// <summary>
    /// 静态 API 使用示例
    /// </summary>
    public static async Task StaticApiExamples()
    {
        // 🔥 一键配置 - 零参数（自动从 appsettings.json 读取）
        // YData.ConfigureAuto();

        // 🔥 一键配置 - 单参数（连接字符串）
        YData.ConfigureAuto("Data Source=test.db");

        // 🔥 一键配置 - 双参数（连接字符串 + 数据库类型）
        // YData.ConfigureAuto("Data Source=test.db", YDataType.Sqlite);

        // 🔥 高级配置
        // YData.Configure(options =>
        // {
        //     options.ConnectionString = "Data Source=test.db";
        //     options.DataType = YDataType.Sqlite;
        //     options.EnableAutoSyncStructure = true;
        //     options.EnableMonitorCommand = true;
        // });

        // 基础 CRUD 操作
        Console.WriteLine("=== 基础 CRUD 操作 ===");

        // 插入用户
        var user = new User
        {
            Name = "张三",
            Email = "<EMAIL>",
            Age = 25
        };

        var insertResult = await YData.InsertAsync(user);
        Console.WriteLine($"插入用户成功，影响行数: {insertResult}");

        // 查询所有用户
        var allUsers = await YData.GetAllAsync<User>();
        Console.WriteLine($"查询到 {allUsers.Count} 个用户");

        // 根据ID查询用户
        if (allUsers.Any())
        {
            var firstUser = allUsers.First();
            var userById = await YData.GetAsync<User>(firstUser.Id);
            Console.WriteLine($"根据ID查询用户: {userById?.Name}");

            // 更新用户
            if (userById != null)
            {
                userById.Age = 26;
                userById.UpdateTime = DateTime.Now;
                var updateResult = await YData.UpdateAsync(userById);
                Console.WriteLine($"更新用户成功，影响行数: {updateResult}");
            }
        }

        // 高级查询
        Console.WriteLine("\n=== 高级查询 ===");

        // 条件查询
        var activeUsers = await YData.Select<User>()
            .Where(u => u.IsActive && u.Age > 20)
            .OrderBy(u => u.CreateTime)
            .ToListAsync();
        Console.WriteLine($"查询到 {activeUsers.Count} 个活跃用户");

        // 分页查询
        var pagedUsers = await YData.GetPagedAsync<User>(1, 10, u => u.IsActive);
        Console.WriteLine($"分页查询: 第1页，共 {pagedUsers.TotalCount} 条记录，当前页 {pagedUsers.Items.Count} 条");

        // 事务操作
        Console.WriteLine("\n=== 事务操作 ===");

        var transactionResult = await YData.TransactionAsync(async () =>
        {
            // 创建用户
            var newUser = new User
            {
                Name = "李四",
                Email = "<EMAIL>",
                Age = 30
            };
            await YData.InsertAsync(newUser);

            // 创建订单
            var order = new Order
            {
                UserId = newUser.Id,
                OrderNo = $"ORD{DateTime.Now:yyyyMMddHHmmss}",
                Amount = 99.99m,
                Status = OrderStatus.Pending
            };
            await YData.InsertAsync(order);

            return true;
        });

        Console.WriteLine($"事务操作结果: {transactionResult}");

        // 关联查询
        Console.WriteLine("\n=== 关联查询 ===");

        var ordersWithUsers = await YData.Select<Order>()
            .Include(o => o.User)
            .Where(o => o.Status == OrderStatus.Pending)
            .ToListAsync();

        foreach (var order in ordersWithUsers)
        {
            Console.WriteLine($"订单 {order.OrderNo}: 用户 {order.User?.Name}, 金额 {order.Amount}");
        }
    }

    /// <summary>
    /// 依赖注入使用示例
    /// </summary>
    public static void DependencyInjectionExample()
    {
        // 在 Program.cs 或 Startup.cs 中配置
        var services = new ServiceCollection();

        // 🔥 零参数配置（自动检测）
        // services.AddYDataAuto();

        // 🔥 单参数配置（连接字符串）
        services.AddYDataAuto("Data Source=test.db");

        // 🔥 双参数配置（连接字符串 + 数据库类型）
        // services.AddYDataAuto("Data Source=test.db", YDataType.Sqlite);

        // 🔥 完整配置
        // services.AddYData(options =>
        // {
        //     options.ConnectionString = "Data Source=test.db";
        //     options.DataType = YDataType.Sqlite;
        //     options.EnableAutoSyncStructure = true;
        //     options.EnableMonitorCommand = true;
        //     options.DefaultQueryTimeout = TimeSpan.FromSeconds(30);
        // });

        // 添加日志
        services.AddLogging(builder => builder.AddConsole());

        var serviceProvider = services.BuildServiceProvider();

        // 使用服务
        var context = serviceProvider.GetRequiredService<IYDataContext>();

        Console.WriteLine("依赖注入配置完成，可以在控制器或服务中注入 IYDataContext 使用");
    }

    /// <summary>
    /// 服务类使用示例
    /// </summary>
    public class UserService
    {
        private readonly IYDataContext _context;
        private readonly ILogger<UserService> _logger;

        public UserService(IYDataContext context, ILogger<UserService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取活跃用户
        /// </summary>
        public async Task<List<User>> GetActiveUsersAsync()
        {
            _logger.LogInformation("获取活跃用户列表");

            return await _context.Select<User>()
                .Where(u => u.IsActive)
                .OrderBy(u => u.Name)
                .ToListAsync();
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        public async Task<int> CreateUserAsync(string name, string email, int age)
        {
            _logger.LogInformation("创建用户: {Name}", name);

            var user = new User
            {
                Name = name,
                Email = email,
                Age = age,
                IsActive = true,
                CreateTime = DateTime.Now
            };

            return await _context.InsertAsync(user);
        }

        /// <summary>
        /// 获取用户订单
        /// </summary>
        public async Task<List<Order>> GetUserOrdersAsync(int userId)
        {
            _logger.LogInformation("获取用户订单: {UserId}", userId);

            return await _context.Select<Order>()
                .Where(o => o.UserId == userId)
                .Include(o => o.User)
                .OrderByDescending(o => o.CreateTime)
                .ToListAsync();
        }
    }
}
