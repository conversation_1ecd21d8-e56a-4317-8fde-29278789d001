namespace Zylo.YData.Examples;

/// <summary>
/// 用户实体示例
/// </summary>
[Table(Name = "Users")]
public class User
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    
    /// <summary>
    /// 用户名
    /// </summary>
    [Column(StringLength = 50)]
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [Column(StringLength = 100)]
    public string Email { get; set; } = string.Empty;
    
    /// <summary>
    /// 年龄
    /// </summary>
    public int Age { get; set; }
    
    /// <summary>
    /// 是否激活
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}

/// <summary>
/// 订单实体示例
/// </summary>
[Table(Name = "Orders")]
public class Order
{
    /// <summary>
    /// 订单ID
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public int Id { get; set; }
    
    /// <summary>
    /// 用户ID
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    /// 订单号
    /// </summary>
    [Column(StringLength = 50)]
    public string OrderNo { get; set; } = string.Empty;
    
    /// <summary>
    /// 金额
    /// </summary>
    [Column(Precision = 18, Scale = 2)]
    public decimal Amount { get; set; }
    
    /// <summary>
    /// 订单状态
    /// </summary>
    public OrderStatus Status { get; set; } = OrderStatus.Pending;
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 用户导航属性
    /// </summary>
    [Navigate(nameof(UserId))]
    public User? User { get; set; }
}

/// <summary>
/// 订单状态枚举
/// </summary>
public enum OrderStatus
{
    /// <summary>
    /// 待处理
    /// </summary>
    Pending = 0,
    
    /// <summary>
    /// 已确认
    /// </summary>
    Confirmed = 1,
    
    /// <summary>
    /// 已发货
    /// </summary>
    Shipped = 2,
    
    /// <summary>
    /// 已完成
    /// </summary>
    Completed = 3,
    
    /// <summary>
    /// 已取消
    /// </summary>
    Cancelled = 4
}
