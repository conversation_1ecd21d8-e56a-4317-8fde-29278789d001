using System.Linq.Expressions;
using FreeSql;

namespace Zylo.YData.Extensions;

/// <summary>
/// YData 智能扩展方法
/// 提供更便捷的查询和操作方法
/// </summary>
public static class YDataExtensions
{
    #region WhereIf 条件查询扩展

    /// <summary>
    /// 条件查询扩展 - 当条件为真时才应用 Where 条件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="condition">条件</param>
    /// <param name="predicate">查询表达式</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// // 只有当 name 不为空时才添加名称过滤条件
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereIf(!string.IsNullOrEmpty(name), u => u.Name.Contains(name))
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereIf<T>(this ISelect<T> query, bool condition, Expression<Func<T, bool>> predicate)
        where T : class
    {
        return condition ? query.Where(predicate) : query;
    }

    /// <summary>
    /// 条件查询扩展 - 当值不为 null 时才应用 Where 条件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TValue">值类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="value">值</param>
    /// <param name="predicate">查询表达式</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// // 只有当 age 不为 null 时才添加年龄过滤条件
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereIfNotNull(age, (query, val) => query.Where(u => u.Age >= val))
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereIfNotNull<T, TValue>(this ISelect<T> query, TValue? value, Func<ISelect<T>, TValue, ISelect<T>> predicate)
        where T : class
        where TValue : struct
    {
        return value.HasValue ? predicate(query, value.Value) : query;
    }

    /// <summary>
    /// 条件查询扩展 - 当字符串不为空时才应用 Where 条件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="value">字符串值</param>
    /// <param name="predicate">查询表达式</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// // 只有当 name 不为空时才添加名称过滤条件
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereIfNotEmpty(name, (query, val) => query.Where(u => u.Name.Contains(val)))
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereIfNotEmpty<T>(this ISelect<T> query, string? value, Func<ISelect<T>, string, ISelect<T>> predicate)
        where T : class
    {
        return !string.IsNullOrEmpty(value) ? predicate(query, value) : query;
    }

    /// <summary>
    /// 条件查询扩展 - 当字符串不为空白时才应用 Where 条件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="value">字符串值</param>
    /// <param name="predicate">查询表达式</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// // 只有当 name 不为空白时才添加名称过滤条件
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereIfNotWhiteSpace(name, (query, val) => query.Where(u => u.Name.Contains(val)))
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereIfNotWhiteSpace<T>(this ISelect<T> query, string? value, Func<ISelect<T>, string, ISelect<T>> predicate)
        where T : class
    {
        return !string.IsNullOrWhiteSpace(value) ? predicate(query, value) : query;
    }

    #endregion

    #region 常用查询扩展

    /// <summary>
    /// In 查询扩展
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="property">属性表达式</param>
    /// <param name="values">值集合</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// var userIds = new[] { 1, 2, 3 };
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereIn(u => u.Id, userIds)
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereIn<T, TProperty>(this ISelect<T> query, Expression<Func<T, TProperty>> property, IEnumerable<TProperty> values)
        where T : class
    {
        var valueList = values?.ToList();
        if (valueList == null || !valueList.Any())
            return query.Where(x => false); // 空集合返回空结果

        return query.Where($"{property.GetPropertyName()} IN @values", new { values = valueList });
    }

    /// <summary>
    /// Between 查询扩展
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="property">属性表达式</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>查询对象</returns>
    /// <example>
    /// <code>
    /// var users = await YData.Select&lt;User&gt;()
    ///     .WhereBetween(u => u.Age, 18, 65)
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> WhereBetween<T, TProperty>(this ISelect<T> query, Expression<Func<T, TProperty>> property, TProperty min, TProperty max)
        where T : class
        where TProperty : IComparable<TProperty>
    {
        return query.Where($"{property.GetPropertyName()} BETWEEN @min AND @max", new { min, max });
    }

    #endregion

    #region 分页查询扩展

    /// <summary>
    /// 分页查询扩展
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页结果</returns>
    /// <example>
    /// <code>
    /// var result = await YData.Select&lt;User&gt;()
    ///     .Where(u => u.IsActive)
    ///     .ToPagedResultAsync(1, 10);
    ///
    /// Console.WriteLine($"总共 {result.TotalCount} 条记录，当前第 {result.PageIndex} 页");
    /// foreach (var user in result.Data)
    /// {
    ///     Console.WriteLine($"用户: {user.Name}");
    /// }
    /// </code>
    /// </example>
    public static async Task<PagedResult<T>> ToPagedResultAsync<T>(this ISelect<T> query, int pageIndex = 1, int pageSize = 10)
        where T : class
    {
        // 参数验证
        if (pageIndex < 1) pageIndex = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 1000) pageSize = 1000; // 限制最大页大小

        // 获取总记录数
        var totalCount = await query.CountAsync();

        if (totalCount == 0)
        {
            return PagedResult<T>.Empty(pageIndex, pageSize);
        }

        // 计算总页数，确保页码不超出范围
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        if (pageIndex > totalPages)
        {
            pageIndex = totalPages;
        }

        // 获取当前页数据
        var data = await query
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return PagedResult<T>.Create(data, pageIndex, pageSize, totalCount);
    }

    /// <summary>
    /// 分页查询扩展（同步版本）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页结果</returns>
    public static PagedResult<T> ToPagedResult<T>(this ISelect<T> query, int pageIndex = 1, int pageSize = 10)
        where T : class
    {
        // 参数验证
        if (pageIndex < 1) pageIndex = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 1000) pageSize = 1000; // 限制最大页大小

        // 获取总记录数
        var totalCount = query.Count();

        if (totalCount == 0)
        {
            return PagedResult<T>.Empty(pageIndex, pageSize);
        }

        // 计算总页数，确保页码不超出范围
        var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        if (pageIndex > totalPages)
        {
            pageIndex = totalPages;
        }

        // 获取当前页数据
        var data = query
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return PagedResult<T>.Create(data, pageIndex, pageSize, totalCount);
    }

    /// <summary>
    /// 快速分页查询扩展 - 适用于大数据量场景
    /// 不查询总记录数，只返回当前页数据和基本分页信息
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="query">查询对象</param>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">每页大小</param>
    /// <returns>分页结果（TotalCount为-1表示未统计）</returns>
    /// <example>
    /// <code>
    /// // 适用于大数据量场景，不统计总数，性能更好
    /// var result = await YData.Select&lt;User&gt;()
    ///     .Where(u => u.IsActive)
    ///     .ToFastPagedResultAsync(1, 10);
    /// </code>
    /// </example>
    public static async Task<PagedResult<T>> ToFastPagedResultAsync<T>(this ISelect<T> query, int pageIndex = 1, int pageSize = 10)
        where T : class
    {
        // 参数验证
        if (pageIndex < 1) pageIndex = 1;
        if (pageSize < 1) pageSize = 10;
        if (pageSize > 1000) pageSize = 1000;

        // 多取一条记录来判断是否还有下一页
        var data = await query
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize + 1)
            .ToListAsync();

        var hasNextPage = data.Count > pageSize;
        if (hasNextPage)
        {
            data = data.Take(pageSize).ToList();
        }

        // 使用 -1 表示未统计总数
        return new PagedResult<T>
        {
            Items = data,
            PageIndex = pageIndex,
            PageSize = pageSize,
            TotalCount = -1, // 表示未统计
            TotalPages = -1  // 表示未统计
        };
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 获取属性名称
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="expression">属性表达式</param>
    /// <returns>属性名称</returns>
    private static string GetPropertyName<T, TProperty>(this Expression<Func<T, TProperty>> expression)
    {
        if (expression.Body is MemberExpression memberExpression)
        {
            return memberExpression.Member.Name;
        }

        throw new ArgumentException("Expression must be a member expression", nameof(expression));
    }

    #endregion

    #region 批量操作扩展

    /// <summary>
    /// 批量插入扩展 - 异步版本
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插入的记录数</returns>
    /// <example>
    /// <code>
    /// var users = new List&lt;User&gt; { new User { Name = "张三" }, new User { Name = "李四" } };
    /// var count = await users.YBatchInsertAsync();
    /// Console.WriteLine($"插入了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YBatchInsertAsync<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量插入功能
            return await YData.FreeSql.Insert(entityList).ExecuteAffrowsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量插入失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量插入扩展 - 同步版本
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <returns>插入的记录数</returns>
    /// <example>
    /// <code>
    /// var users = new List&lt;User&gt; { new User { Name = "张三" }, new User { Name = "李四" } };
    /// var count = users.YBatchInsert();
    /// Console.WriteLine($"插入了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YBatchInsert<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量插入功能
            return YData.FreeSql.Insert(entityList).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量插入失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量更新扩展 - 异步版本
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要更新的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的记录数</returns>
    /// <example>
    /// <code>
    /// var users = await YData.Select&lt;User&gt;().ToListAsync();
    /// users.ForEach(u => u.UpdateTime = DateTime.Now);
    /// var count = await users.YBatchUpdateAsync();
    /// Console.WriteLine($"更新了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YBatchUpdateAsync<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量更新功能
            return await YData.FreeSql.Update<T>().SetSource(entityList).ExecuteAffrowsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量更新失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量更新扩展 - 同步版本
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要更新的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <returns>更新的记录数</returns>
    /// <example>
    /// <code>
    /// var users = YData.Select&lt;User&gt;().ToList();
    /// users.ForEach(u => u.UpdateTime = DateTime.Now);
    /// var count = users.YBatchUpdate();
    /// Console.WriteLine($"更新了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YBatchUpdate<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量更新功能
            return YData.FreeSql.Update<T>().SetSource(entityList).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量更新失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量删除扩展 - 异步版本（根据主键删除）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要删除的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的记录数</returns>
    /// <example>
    /// <code>
    /// var users = await YData.Select&lt;User&gt;().Where(u => u.IsDeleted).ToListAsync();
    /// var count = await users.YBatchDeleteAsync();
    /// Console.WriteLine($"删除了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YBatchDeleteAsync<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 逐个删除（FreeSql 的批量删除需要主键值）
            int totalCount = 0;
            foreach (var entity in entityList)
            {
                totalCount += await YData.FreeSql.Delete<T>().Where(entity).ExecuteAffrowsAsync(cancellationToken);
            }
            return totalCount;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量删除失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量删除扩展 - 同步版本（根据主键删除）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要删除的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <returns>删除的记录数</returns>
    /// <example>
    /// <code>
    /// var users = YData.Select&lt;User&gt;().Where(u => u.IsDeleted).ToList();
    /// var count = users.YBatchDelete();
    /// Console.WriteLine($"删除了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YBatchDelete<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 逐个删除（FreeSql 的批量删除需要主键值）
            int totalCount = 0;
            foreach (var entity in entityList)
            {
                totalCount += YData.FreeSql.Delete<T>().Where(entity).ExecuteAffrows();
            }
            return totalCount;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量删除失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量删除扩展 - 根据主键值删除（异步版本）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TKey">主键类型</typeparam>
    /// <param name="keys">主键值集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>删除的记录数</returns>
    /// <example>
    /// <code>
    /// var userIds = new[] { 1, 2, 3, 4, 5 };
    /// var count = await userIds.YBatchDeleteAsync&lt;User&gt;();
    /// Console.WriteLine($"删除了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YBatchDeleteAsync<T, TKey>(
        this IEnumerable<TKey> keys,
        int batchSize = 1000,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (keys == null) throw new ArgumentNullException(nameof(keys));

        var keyList = keys.ToList();
        if (keyList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量删除功能（根据主键值）
            return await YData.FreeSql.Delete<T>(keyList).ExecuteAffrowsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量删除失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量删除扩展 - 根据主键值删除（同步版本）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TKey">主键类型</typeparam>
    /// <param name="keys">主键值集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <returns>删除的记录数</returns>
    /// <example>
    /// <code>
    /// var userIds = new[] { 1, 2, 3, 4, 5 };
    /// var count = userIds.YBatchDelete&lt;User&gt;();
    /// Console.WriteLine($"删除了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YBatchDelete<T, TKey>(
        this IEnumerable<TKey> keys,
        int batchSize = 1000)
        where T : class
    {
        if (keys == null) throw new ArgumentNullException(nameof(keys));

        var keyList = keys.ToList();
        if (keyList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的批量删除功能（根据主键值）
            return YData.FreeSql.Delete<T>(keyList).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量删除失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量保存扩展 - 异步版本（自动判断插入或更新）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要保存的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保存的记录数</returns>
    /// <example>
    /// <code>
    /// var users = new List&lt;User&gt;
    /// {
    ///     new User { Id = 1, Name = "张三" }, // 更新
    ///     new User { Name = "李四" }          // 插入
    /// };
    /// var count = await users.YBatchSaveAsync();
    /// Console.WriteLine($"保存了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YBatchSaveAsync<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的 InsertOrUpdate 功能
            return await YData.FreeSql.InsertOrUpdate<T>().SetSource(entityList).ExecuteAffrowsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量保存失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量保存扩展 - 同步版本（自动判断插入或更新）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要保存的实体集合</param>
    /// <param name="batchSize">批量大小，默认 1000</param>
    /// <returns>保存的记录数</returns>
    /// <example>
    /// <code>
    /// var users = new List&lt;User&gt;
    /// {
    ///     new User { Id = 1, Name = "张三" }, // 更新
    ///     new User { Name = "李四" }          // 插入
    /// };
    /// var count = users.YBatchSave();
    /// Console.WriteLine($"保存了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YBatchSave<T>(
        this IEnumerable<T> entities,
        int batchSize = 1000)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        try
        {
            // 使用 FreeSql 的 InsertOrUpdate 功能
            return YData.FreeSql.InsertOrUpdate<T>().SetSource(entityList).ExecuteAffrows();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"批量保存失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 批量操作配置选项
    /// </summary>
    public static class YBatchOptions
    {
        /// <summary>
        /// 默认批量大小
        /// </summary>
        public static int DefaultBatchSize { get; set; } = 1000;

        /// <summary>
        /// 小批量操作的阈值（小于此值时使用单条操作）
        /// </summary>
        public static int SmallBatchThreshold { get; set; } = 10;

        /// <summary>
        /// 是否启用事务（默认启用）
        /// </summary>
        public static bool EnableTransaction { get; set; } = true;

        /// <summary>
        /// 事务超时时间（秒）
        /// </summary>
        public static int TransactionTimeoutSeconds { get; set; } = 300;
    }

    /// <summary>
    /// 智能批量操作扩展 - 根据数据量自动选择最优策略
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体集合</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插入的记录数</returns>
    /// <example>
    /// <code>
    /// var users = GetUsers(); // 可能是 5 条，也可能是 50000 条
    /// var count = await users.YSmartBatchInsertAsync();
    /// Console.WriteLine($"智能插入了 {count} 条记录");
    /// </code>
    /// </example>
    public static async Task<int> YSmartBatchInsertAsync<T>(
        this IEnumerable<T> entities,
        CancellationToken cancellationToken = default)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        // 根据数据量选择策略
        if (entityList.Count <= YBatchOptions.SmallBatchThreshold)
        {
            // 小批量：使用单条插入
            int count = 0;
            foreach (var entity in entityList)
            {
                count += await YData.FreeSql.Insert(entity).ExecuteAffrowsAsync(cancellationToken);
            }
            return count;
        }
        else
        {
            // 大批量：使用批量插入
            return await entityList.YBatchInsertAsync(YBatchOptions.DefaultBatchSize, cancellationToken);
        }
    }

    /// <summary>
    /// 智能批量操作扩展 - 根据数据量自动选择最优策略（同步版本）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要插入的实体集合</param>
    /// <returns>插入的记录数</returns>
    /// <example>
    /// <code>
    /// var users = GetUsers(); // 可能是 5 条，也可能是 50000 条
    /// var count = users.YSmartBatchInsert();
    /// Console.WriteLine($"智能插入了 {count} 条记录");
    /// </code>
    /// </example>
    public static int YSmartBatchInsert<T>(this IEnumerable<T> entities)
        where T : class
    {
        if (entities == null) throw new ArgumentNullException(nameof(entities));

        var entityList = entities.ToList();
        if (entityList.Count == 0) return 0;

        // 根据数据量选择策略
        if (entityList.Count <= YBatchOptions.SmallBatchThreshold)
        {
            // 小批量：使用单条插入
            int count = 0;
            foreach (var entity in entityList)
            {
                count += YData.FreeSql.Insert(entity).ExecuteAffrows();
            }
            return count;
        }
        else
        {
            // 大批量：使用批量插入
            return entityList.YBatchInsert(YBatchOptions.DefaultBatchSize);
        }
    }

    #endregion

    #region 数据验证扩展

    /// <summary>
    /// 验证实体数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要验证的实体</param>
    /// <param name="options">验证选项</param>
    /// <returns>验证结果</returns>
    /// <example>
    /// <code>
    /// var user = new User { Name = "", Age = -1 };
    /// var result = user.YValidate();
    ///
    /// if (!result.IsValid)
    /// {
    ///     foreach (var error in result.Errors)
    ///     {
    ///         Console.WriteLine($"{error.PropertyName}: {error.ErrorMessage}");
    ///     }
    /// }
    /// </code>
    /// </example>
    public static YValidationResult YValidate<T>(this T entity, YValidationOptions? options = null)
        where T : class
    {
        if (entity == null)
        {
            return YValidationResult.Failure("实体不能为空");
        }

        options ??= YValidationOptions.Default;
        var result = new YValidationResult();

        try
        {
            // 执行基础验证
            ValidateBasicRules(entity, result, options);

            // 执行自定义验证规则
            ValidateCustomRules(entity, result, options);

            // 执行业务验证规则
            ValidateBusinessRules(entity, result, options);
        }
        catch (Exception ex)
        {
            result.AddError("ValidationError", $"验证过程中发生异常: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 异步验证实体数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要验证的实体</param>
    /// <param name="options">验证选项</param>
    /// <returns>验证结果</returns>
    public static async Task<YValidationResult> YValidateAsync<T>(this T entity, YValidationOptions? options = null)
        where T : class
    {
        if (entity == null)
        {
            return YValidationResult.Failure("实体不能为空");
        }

        options ??= YValidationOptions.Default;
        var result = new YValidationResult();

        try
        {
            // 执行基础验证
            ValidateBasicRules(entity, result, options);

            // 执行自定义验证规则
            ValidateCustomRules(entity, result, options);

            // 执行业务验证规则
            ValidateBusinessRules(entity, result, options);

            // 执行异步验证规则（如数据库唯一性检查）
            await ValidateAsyncRules(entity, result, options);
        }
        catch (Exception ex)
        {
            result.AddError("ValidationError", $"验证过程中发生异常: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 验证实体集合
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要验证的实体集合</param>
    /// <param name="options">验证选项</param>
    /// <returns>验证结果</returns>
    /// <example>
    /// <code>
    /// var users = new List&lt;User&gt; { user1, user2, user3 };
    /// var result = users.YValidateCollection();
    ///
    /// Console.WriteLine($"验证了 {result.TotalCount} 个实体");
    /// Console.WriteLine($"成功: {result.SuccessCount}, 失败: {result.FailureCount}");
    ///
    /// foreach (var error in result.AllErrors)
    /// {
    ///     Console.WriteLine($"[{error.Index}] {error.PropertyName}: {error.ErrorMessage}");
    /// }
    /// </code>
    /// </example>
    public static YValidationCollectionResult<T> YValidateCollection<T>(this IEnumerable<T> entities, YValidationOptions? options = null)
        where T : class
    {
        if (entities == null)
        {
            throw new ArgumentNullException(nameof(entities));
        }

        var entityList = entities.ToList();
        var collectionResult = new YValidationCollectionResult<T>();
        options ??= YValidationOptions.Default;

        for (int i = 0; i < entityList.Count; i++)
        {
            var entity = entityList[i];
            var result = entity.YValidate(options);
            collectionResult.AddResult(i, entity, result);
        }

        return collectionResult;
    }

    /// <summary>
    /// 异步验证实体集合
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entities">要验证的实体集合</param>
    /// <param name="options">验证选项</param>
    /// <returns>验证结果</returns>
    public static async Task<YValidationCollectionResult<T>> YValidateCollectionAsync<T>(this IEnumerable<T> entities, YValidationOptions? options = null)
        where T : class
    {
        if (entities == null)
        {
            throw new ArgumentNullException(nameof(entities));
        }

        var entityList = entities.ToList();
        var collectionResult = new YValidationCollectionResult<T>();
        options ??= YValidationOptions.Default;

        // 并行验证以提高性能
        var tasks = entityList.Select(async (entity, index) =>
        {
            var result = await entity.YValidateAsync(options);
            return new { Index = index, Entity = entity, Result = result };
        });

        var results = await Task.WhenAll(tasks);

        foreach (var item in results)
        {
            collectionResult.AddResult(item.Index, item.Entity, item.Result);
        }

        return collectionResult;
    }

    /// <summary>
    /// 执行基础验证规则
    /// </summary>
    private static void ValidateBasicRules<T>(T entity, YValidationResult result, YValidationOptions options)
        where T : class
    {
        var type = typeof(T);
        var properties = type.GetProperties();

        foreach (var property in properties)
        {
            var value = property.GetValue(entity);
            var propertyName = property.Name;

            // 必填验证
            if (options.ValidateRequired && IsRequired(property))
            {
                if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
                {
                    result.AddError(propertyName, $"{propertyName} 是必填字段");
                    continue;
                }
            }

            // 字符串长度验证
            if (value is string stringValue && options.ValidateStringLength)
            {
                var maxLength = GetMaxLength(property);
                if (maxLength > 0 && stringValue.Length > maxLength)
                {
                    result.AddError(propertyName, $"{propertyName} 长度不能超过 {maxLength} 个字符");
                }

                var minLength = GetMinLength(property);
                if (minLength > 0 && stringValue.Length < minLength)
                {
                    result.AddError(propertyName, $"{propertyName} 长度不能少于 {minLength} 个字符");
                }
            }

            // 数值范围验证
            if (options.ValidateRange && IsNumericType(property.PropertyType))
            {
                var range = GetRange(property);
                if (range.HasValue && value != null)
                {
                    var numericValue = Convert.ToDouble(value);
                    if (numericValue < range.Value.Min || numericValue > range.Value.Max)
                    {
                        result.AddError(propertyName, $"{propertyName} 必须在 {range.Value.Min} 到 {range.Value.Max} 之间");
                    }
                }
            }

            // 邮箱格式验证
            if (options.ValidateEmailFormat && IsEmailProperty(property) && value is string email)
            {
                if (!IsValidEmail(email))
                {
                    result.AddError(propertyName, $"{propertyName} 邮箱格式不正确");
                }
            }
        }
    }

    /// <summary>
    /// 执行自定义验证规则
    /// </summary>
    private static void ValidateCustomRules<T>(T entity, YValidationResult result, YValidationOptions options)
        where T : class
    {
        if (options.CustomRules.TryGetValue(typeof(T), out var rules))
        {
            foreach (var rule in rules)
            {
                try
                {
                    if (!rule.Validate(entity))
                    {
                        result.AddError(rule.PropertyName ?? "Entity", rule.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    result.AddError(rule.PropertyName ?? "Entity", $"自定义验证规则执行失败: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    /// 执行业务验证规则
    /// </summary>
    private static void ValidateBusinessRules<T>(T entity, YValidationResult result, YValidationOptions options)
        where T : class
    {
        // 这里可以添加特定的业务验证逻辑
        // 例如：检查用户名是否重复、检查订单状态是否合法等

        // 这里可以添加特定的业务验证逻辑
        // 例如：检查用户名是否重复、检查订单状态是否合法等
        // 示例代码（需要根据实际实体类型进行调整）：
        /*
        if (entity is SomeUserEntity user && options.ValidateBusinessRules)
        {
            if (user.Age < 0 || user.Age > 150)
            {
                result.AddError(nameof(user.Age), "年龄必须在 0 到 150 之间");
            }
        }
        */
    }

    /// <summary>
    /// 执行异步验证规则
    /// </summary>
    private static async Task ValidateAsyncRules<T>(T entity, YValidationResult result, YValidationOptions options)
        where T : class
    {
        if (options.AsyncRules.TryGetValue(typeof(T), out var asyncRules))
        {
            foreach (var rule in asyncRules)
            {
                try
                {
                    var isValid = await rule.ValidateAsync(entity);
                    if (!isValid)
                    {
                        result.AddError(rule.PropertyName ?? "Entity", rule.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    result.AddError(rule.PropertyName ?? "Entity", $"异步验证规则执行失败: {ex.Message}");
                }
            }
        }

        // 数据库唯一性验证示例（需要根据实际实体类型进行调整）
        /*
        if (options.ValidateUniqueness && entity is SomeUserEntity userEntity)
        {
            try
            {
                var existingUser = await YData.Select<SomeUserEntity>()
                    .Where(u => u.Email == userEntity.Email && u.Id != userEntity.Id)
                    .FirstAsync();

                if (existingUser != null)
                {
                    result.AddError(nameof(userEntity.Email), "邮箱地址已被使用");
                }
            }
            catch
            {
                // 忽略查询异常，可能是表不存在等情况
            }
        }
        */
    }

    /// <summary>
    /// 验证工具方法
    /// </summary>

    /// <summary>
    /// 检查属性是否为必填
    /// </summary>
    private static bool IsRequired(System.Reflection.PropertyInfo property)
    {
        // 检查 Required 特性
        return property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.RequiredAttribute), false).Any();
    }

    /// <summary>
    /// 获取字符串最大长度
    /// </summary>
    private static int GetMaxLength(System.Reflection.PropertyInfo property)
    {
        var maxLengthAttr = property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.MaxLengthAttribute), false)
            .FirstOrDefault() as System.ComponentModel.DataAnnotations.MaxLengthAttribute;

        if (maxLengthAttr != null)
            return maxLengthAttr.Length;

        var stringLengthAttr = property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.StringLengthAttribute), false)
            .FirstOrDefault() as System.ComponentModel.DataAnnotations.StringLengthAttribute;

        return stringLengthAttr?.MaximumLength ?? 0;
    }

    /// <summary>
    /// 获取字符串最小长度
    /// </summary>
    private static int GetMinLength(System.Reflection.PropertyInfo property)
    {
        var stringLengthAttr = property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.StringLengthAttribute), false)
            .FirstOrDefault() as System.ComponentModel.DataAnnotations.StringLengthAttribute;

        return stringLengthAttr?.MinimumLength ?? 0;
    }

    /// <summary>
    /// 检查是否为数值类型
    /// </summary>
    private static bool IsNumericType(Type type)
    {
        var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
        return underlyingType == typeof(int) || underlyingType == typeof(long) ||
               underlyingType == typeof(float) || underlyingType == typeof(double) ||
               underlyingType == typeof(decimal) || underlyingType == typeof(short) ||
               underlyingType == typeof(byte);
    }

    /// <summary>
    /// 获取数值范围
    /// </summary>
    private static (double Min, double Max)? GetRange(System.Reflection.PropertyInfo property)
    {
        var rangeAttr = property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.RangeAttribute), false)
            .FirstOrDefault() as System.ComponentModel.DataAnnotations.RangeAttribute;

        if (rangeAttr != null)
        {
            return (Convert.ToDouble(rangeAttr.Minimum), Convert.ToDouble(rangeAttr.Maximum));
        }

        return null;
    }

    /// <summary>
    /// 检查是否为邮箱属性
    /// </summary>
    private static bool IsEmailProperty(System.Reflection.PropertyInfo property)
    {
        // 检查 EmailAddress 特性
        if (property.GetCustomAttributes(typeof(System.ComponentModel.DataAnnotations.EmailAddressAttribute), false).Any())
            return true;

        // 检查属性名是否包含 Email
        return property.Name.ToLower().Contains("email");
    }

    /// <summary>
    /// 验证邮箱格式
    /// </summary>
    private static bool IsValidEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    #endregion
}
