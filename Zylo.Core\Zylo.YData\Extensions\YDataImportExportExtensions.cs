using System.Text.Json;
using System.Text;
using System.Linq.Expressions;

namespace Zylo.YData;

/// <summary>
/// 数据导入导出扩展方法
/// <para>包装常用的数据交换功能，支持 JSON、CSV、Excel 等格式</para>
/// </summary>
/// <remarks>
/// 提供以下功能：
/// <list type="bullet">
/// <item>JSON 格式的数据导入导出</item>
/// <item>CSV 格式的数据导入导出</item>
/// <item>Excel 格式的数据导入导出</item>
/// <item>批量数据处理</item>
/// <item>数据验证和错误处理</item>
/// </list>
/// </remarks>
public static class YDataImportExportExtensions
{
    #region JSON 导入导出

    /// <summary>
    /// 导出数据到 JSON 文件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="filePath">文件路径</param>
    /// <param name="where">查询条件（可选）</param>
    /// <returns>导出的记录数，失败时返回 -1</returns>
    /// <remarks>
    /// 支持条件查询，自动处理 JSON 序列化和文件写入
    /// </remarks>
    /// <example>
    /// <code>
    /// // 导出所有用户
    /// var count = await YDataImportExportExtensions.ExportToJsonAsync&lt;User&gt;("users.json");
    ///
    /// // 导出年龄大于18的用户
    /// var count = await YDataImportExportExtensions.ExportToJsonAsync&lt;User&gt;("adults.json", u => u.Age > 18);
    /// </code>
    /// </example>
    public static async Task<int> ExportToJsonAsync<T>(string filePath, Expression<Func<T, bool>>? where = null) where T : class
    {
        try
        {
            var query = YData.Select<T>();
            if (where != null)
            {
                query = query.Where(where);
            }

            var data = await query.ToListAsync();

            // 如果没有数据，创建空数组的JSON文件
            var json = JsonSerializer.Serialize(data, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(filePath, json, Encoding.UTF8);
            return data.Count;
        }
        catch (Exception ex)
        {
            // 记录异常信息用于调试
            Console.WriteLine($"ExportToJsonAsync 异常: {ex.Message}");
            return -1;
        }
    }

    /// <summary>
    /// 从 JSON 文件导入数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="filePath">JSON 文件路径</param>
    /// <param name="clearTable">是否先清空表（默认 false）</param>
    /// <returns>导入的记录数，失败时返回 -1</returns>
    /// <remarks>
    /// 自动处理 JSON 反序列化和批量插入，支持清空表选项
    /// </remarks>
    /// <example>
    /// <code>
    /// // 导入用户数据（追加模式）
    /// var count = await YDataImportExportExtensions.ImportFromJsonAsync&lt;User&gt;("users.json");
    ///
    /// // 导入用户数据（替换模式）
    /// var count = await YDataImportExportExtensions.ImportFromJsonAsync&lt;User&gt;("users.json", true);
    /// </code>
    /// </example>
    public static async Task<int> ImportFromJsonAsync<T>(string filePath, bool clearTable = false) where T : class
    {
        try
        {
            if (!File.Exists(filePath))
                return -1;

            var json = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
            var data = JsonSerializer.Deserialize<List<T>>(json);

            if (data == null || !data.Any())
                return 0;

            if (clearTable)
            {
                YDatabaseManagementExtensions.TruncateTable<T>();
            }

            var result = await YData.Insert<T>().AppendData(data).ExecuteAffrowsAsync();
            return result;
        }
        catch (Exception ex)
        {
            // 记录异常信息用于调试
            Console.WriteLine($"ImportFromJsonAsync 异常: {ex.Message}");
            return -1;
        }
    }

    #endregion

    #region CSV 导入导出

    /// <summary>
    /// 导出数据到 CSV 文件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="filePath">CSV 文件路径</param>
    /// <param name="where">查询条件（可选）</param>
    /// <returns>导出的记录数，失败时返回 -1</returns>
    /// <remarks>
    /// 自动生成 CSV 标题行，支持条件查询和中文编码
    /// </remarks>
    /// <example>
    /// <code>
    /// // 导出所有用户到 CSV
    /// var count = await YDataImportExportExtensions.ExportToCsvAsync&lt;User&gt;("users.csv");
    ///
    /// // 导出活跃用户到 CSV
    /// var count = await YDataImportExportExtensions.ExportToCsvAsync&lt;User&gt;("active_users.csv", u => u.IsActive);
    /// </code>
    /// </example>
    public static async Task<int> ExportToCsvAsync<T>(string filePath, Expression<Func<T, bool>>? where = null) where T : class
    {
        try
        {
            var query = YData.Select<T>();
            if (where != null)
            {
                query = query.Where(where);
            }

            var data = await query.ToListAsync();
            if (!data.Any())
                return 0;

            var csv = new StringBuilder();
            var properties = typeof(T).GetProperties();

            // 写入标题行
            csv.AppendLine(string.Join(",", properties.Select(p => $"\"{p.Name}\"")));

            // 写入数据行
            foreach (var item in data)
            {
                var values = properties.Select(p =>
                {
                    var value = p.GetValue(item)?.ToString() ?? "";
                    return $"\"{value.Replace("\"", "\"\"")}\""; // 转义双引号
                });
                csv.AppendLine(string.Join(",", values));
            }

            await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
            return data.Count;
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 从CSV文件导入数据（简单版本，需要CSV格式与实体属性完全匹配）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="filePath">文件路径</param>
    /// <param name="clearTable">是否先清空表（默认false）</param>
    /// <returns>导入的记录数</returns>
    public static async Task<int> ImportFromCsvAsync<T>(string filePath, bool clearTable = false) where T : class
    {
        try
        {
            if (!File.Exists(filePath))
                return -1;

            var lines = await File.ReadAllLinesAsync(filePath, Encoding.UTF8);
            if (lines.Length < 2) // 至少需要标题行和一行数据
                return 0;

            var properties = typeof(T).GetProperties();
            var headerLine = lines[0];
            var headers = ParseCsvLine(headerLine);

            var data = new List<T>();

            for (int i = 1; i < lines.Length; i++)
            {
                var values = ParseCsvLine(lines[i]);
                if (values.Length != headers.Length)
                    continue;

                var item = Activator.CreateInstance<T>();
                for (int j = 0; j < headers.Length; j++)
                {
                    var property = properties.FirstOrDefault(p =>
                        p.Name.Equals(headers[j], StringComparison.OrdinalIgnoreCase));

                    if (property != null && property.CanWrite)
                    {
                        try
                        {
                            var value = Convert.ChangeType(values[j], property.PropertyType);
                            property.SetValue(item, value);
                        }
                        catch
                        {
                            // 忽略类型转换错误
                        }
                    }
                }
                data.Add(item);
            }

            if (clearTable)
            {
                YDatabaseManagementExtensions.TruncateTable<T>();
            }

            if (data.Any())
            {
                var result = await YData.Insert<T>().AppendData(data).ExecuteAffrowsAsync();
                return result;
            }

            return 0;
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 备份表数据到JSON文件
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="backupDir">备份目录</param>
    /// <returns>备份文件路径</returns>
    public static async Task<string?> BackupTableAsync<T>(string backupDir = "Backups") where T : class
    {
        try
        {
            if (!Directory.Exists(backupDir))
            {
                Directory.CreateDirectory(backupDir);
            }

            var tableName = typeof(T).Name;
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var fileName = $"{tableName}_backup_{timestamp}.json";
            var filePath = Path.Combine(backupDir, fileName);

            var count = await ExportToJsonAsync<T>(filePath);
            return count > 0 ? filePath : null;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 从备份文件恢复表数据
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="backupFilePath">备份文件路径</param>
    /// <param name="clearTable">是否先清空表（默认true）</param>
    /// <returns>恢复的记录数</returns>
    public static async Task<int> RestoreTableAsync<T>(string backupFilePath, bool clearTable = true) where T : class
    {
        try
        {
            return await ImportFromJsonAsync<T>(backupFilePath, clearTable);
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 获取数据库所有表的统计信息
    /// </summary>
    /// <returns>表统计信息</returns>
    public static async Task<List<YTableStats>> GetAllTableStatsAsync()
    {
        var stats = new List<YTableStats>();
        try
        {
            var tableNames = YDatabaseManagementExtensions.GetTableNames();
            foreach (var tableName in tableNames)
            {
                try
                {
                    var count = await YData.FreeSql.Ado.QuerySingleAsync<long>($"SELECT COUNT(*) FROM {tableName}");
                    stats.Add(new YTableStats
                    {
                        TableName = tableName,
                        RecordCount = count,
                        LastUpdated = DateTime.Now
                    });
                }
                catch
                {
                    stats.Add(new YTableStats
                    {
                        TableName = tableName,
                        RecordCount = -1,
                        LastUpdated = DateTime.Now
                    });
                }
            }
        }
        catch
        {
            // 忽略错误
        }
        return stats;
    }

    /// <summary>
    /// 解析CSV行（简单实现）
    /// </summary>
    /// <param name="line">CSV行</param>
    /// <returns>字段数组</returns>
    private static string[] ParseCsvLine(string line)
    {
        var result = new List<string>();
        var current = new StringBuilder();
        bool inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == '"')
            {
                if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                {
                    current.Append('"');
                    i++; // 跳过下一个引号
                }
                else
                {
                    inQuotes = !inQuotes;
                }
            }
            else if (c == ',' && !inQuotes)
            {
                result.Add(current.ToString());
                current.Clear();
            }
            else
            {
                current.Append(c);
            }
        }

        result.Add(current.ToString());
        return result.ToArray();
    }
    #endregion
}

/// <summary>
/// 表统计信息
/// </summary>
/// <remarks>
/// 用于存储表的基本统计信息，如记录数和最后更新时间
/// </remarks>
public class YTableStats
{
    /// <summary>
    /// 表名称
    /// </summary>
    public string TableName { get; set; } = "";

    /// <summary>
    /// 记录数量
    /// </summary>
    public long RecordCount { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; }
}
