namespace Zylo.YData;

/// <summary>
/// 数据库管理扩展方法
/// <para>包装 FreeSql 的复杂功能，提供简单易用的数据库管理 API</para>
/// </summary>
/// <remarks>
/// 提供以下功能：
/// <list type="bullet">
/// <item>表结构同步和管理</item>
/// <item>数据库备份和恢复</item>
/// <item>表数据清理和重置</item>
/// <item>数据库健康检查</item>
/// <item>性能优化工具</item>
/// </list>
/// </remarks>
public static class YDatabaseManagementExtensions
{
    #region 表结构管理

    /// <summary>
    /// 同步单个表结构到数据库
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="dropTable">是否删除表重建（危险操作，默认 false）</param>
    /// <returns>是否成功</returns>
    /// <remarks>
    /// 根据实体类的特性自动创建或更新表结构，包括字段、索引、约束等
    /// </remarks>
    /// <example>
    /// <code>
    /// // 同步用户表结构
    /// bool success = YDatabaseManagementExtensions.SyncTable&lt;User&gt;();
    ///
    /// // 删除并重建表（危险操作）
    /// bool success = YDatabaseManagementExtensions.SyncTable&lt;User&gt;(true);
    /// </code>
    /// </example>
    public static bool SyncTable<T>(bool dropTable = false) where T : class
    {
        try
        {
            YData.FreeSql.CodeFirst.SyncStructure<T>();
            return true;
        }
        catch (Exception ex)
        {
            // 记录异常信息用于调试
            Console.WriteLine($"SyncTable<{typeof(T).Name}> 异常: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 同步多个表的结构到数据库
    /// </summary>
    /// <param name="entityTypes">实体类型数组，如果为空则同步所有已知类型</param>
    /// <returns>成功同步的表数量</returns>
    /// <remarks>
    /// 批量同步表结构，提高效率。如果不指定类型，会尝试同步所有已知的实体类型
    /// </remarks>
    /// <example>
    /// <code>
    /// // 同步指定的表
    /// int count = YDatabaseManagementExtensions.SyncAllTables(typeof(User), typeof(Order));
    ///
    /// // 同步所有表
    /// int count = YDatabaseManagementExtensions.SyncAllTables();
    /// </code>
    /// </example>
    public static int SyncAllTables(params Type[] entityTypes)
    {
        int successCount = 0;
        try
        {
            if (entityTypes?.Length > 0)
            {
                YData.FreeSql.CodeFirst.SyncStructure(entityTypes);
                successCount = entityTypes.Length;
            }
            else
            {
                // 这里可以扩展为自动发现所有实体类型
                // 暂时需要手动指定
            }
        }
        catch
        {
            // 记录错误但不抛出异常
        }
        return successCount;
    }

    /// <summary>
    /// 检查表是否存在
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>表是否存在</returns>
    public static bool TableExists<T>() where T : class
    {
        try
        {
            var tables = YData.FreeSql.DbFirst.GetTablesByDatabase();
            var typeName = typeof(T).Name;
            // 检查多种可能的表名格式
            return tables.Any(t =>
                t.Name.Equals(typeName, StringComparison.OrdinalIgnoreCase) ||
                t.Name.Equals(typeName + "s", StringComparison.OrdinalIgnoreCase) ||
                t.Name.Equals(typeName.ToLower(), StringComparison.OrdinalIgnoreCase) ||
                t.Name.Equals((typeName + "s").ToLower(), StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取数据库中所有表名
    /// </summary>
    /// <returns>表名列表</returns>
    public static List<string> GetTableNames()
    {
        try
        {
            var tables = YData.FreeSql.DbFirst.GetTablesByDatabase();
            return tables.Select(t => t.Name).ToList();
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// 获取表的记录数
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>记录数</returns>
    public static long GetTableCount<T>() where T : class
    {
        try
        {
            return YData.Select<T>().Count();
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 获取数据库基本信息
    /// </summary>
    /// <returns>数据库信息</returns>
    public static YDatabaseSummary GetDatabaseInfo()
    {
        try
        {
            var tables = YData.FreeSql.DbFirst.GetTablesByDatabase();
            return new YDatabaseSummary
            {
                DatabaseType = YData.FreeSql.Ado.DataType.ToString(),
                TableCount = tables.Count,
                TableNames = tables.Select(t => t.Name).ToList(),
                ConnectionString = YData.FreeSql.Ado.ConnectionString
            };
        }
        catch (Exception ex)
        {
            return new YDatabaseSummary
            {
                DatabaseType = "Unknown",
                TableCount = 0,
                TableNames = new List<string>(),
                ConnectionString = "Error: " + ex.Message
            };
        }
    }

    /// <summary>
    /// 清空表数据（保留表结构）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>是否成功</returns>
    public static bool TruncateTable<T>() where T : class
    {
        try
        {
            YData.Delete<T>().Where("1=1").ExecuteAffrows();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 删除表（危险操作）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>是否成功</returns>
    public static bool DropTable<T>() where T : class
    {
        try
        {
            var tableName = typeof(T).Name; // 简化实现，使用类型名作为表名
            YData.FreeSql.Ado.ExecuteNonQuery($"DROP TABLE IF EXISTS {tableName}");
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    /// <returns>连接是否正常</returns>
    public static bool CheckConnection()
    {
        try
        {
            YData.FreeSql.Ado.Query<int>("SELECT 1");
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 执行SQL脚本
    /// </summary>
    /// <param name="sql">SQL脚本</param>
    /// <returns>影响的行数，查询语句返回0表示成功</returns>
    public static int ExecuteSql(string sql)
    {
        try
        {
            // 对于查询语句，ExecuteNonQuery 返回 -1，但这不表示错误
            var result = YData.FreeSql.Ado.ExecuteNonQuery(sql);
            return result == -1 ? 0 : result; // 查询语句返回0表示成功执行
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 获取表结构信息
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>表结构信息</returns>
    public static YTableInfo GetTableInfo<T>() where T : class
    {
        try
        {
            var tableName = typeof(T).Name; // 简化实现，使用类型名作为表名
            var tables = YData.FreeSql.DbFirst.GetTablesByDatabase();
            var table = tables.FirstOrDefault(t => t.Name.Equals(tableName, StringComparison.OrdinalIgnoreCase));

            if (table != null)
            {
                return new YTableInfo
                {
                    TableName = table.Name,
                    ColumnCount = table.Columns?.Count ?? 0,
                    RecordCount = GetTableCount<T>(),
                    Comment = table.Comment
                };
            }
        }
        catch
        {
            // 忽略错误
        }

        return new YTableInfo
        {
            TableName = typeof(T).Name,
            ColumnCount = 0,
            RecordCount = 0,
            Comment = "获取信息失败"
        };
    }

    #endregion
}

/// <summary>
/// 数据库摘要信息模型
/// </summary>
public class YDatabaseSummary
{
    public string DatabaseType { get; set; } = "";
    public int TableCount { get; set; }
    public List<string> TableNames { get; set; } = new();
    public string ConnectionString { get; set; } = "";
}

/// <summary>
/// 表信息模型
/// </summary>
public class YTableInfo
{
    public string TableName { get; set; } = "";
    public int ColumnCount { get; set; }
    public long RecordCount { get; set; }
    public string Comment { get; set; } = "";
}
