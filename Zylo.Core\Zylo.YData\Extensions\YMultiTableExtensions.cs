using System.Linq.Expressions;

namespace Zylo.YData.Extensions;

/// <summary>
/// 多表关联查询扩展
/// <para>提供简化的多表关联查询功能，包装 FreeSql 的复杂关联操作</para>
/// </summary>
/// <remarks>
/// 此扩展类提供以下功能：
/// <list type="bullet">
/// <item>简化的导航属性包含操作</item>
/// <item>多表联合查询支持</item>
/// <item>复杂关联条件构建</item>
/// <item>性能优化的预加载策略</item>
/// </list>
/// </remarks>
public static class YMultiTableExtensions
{
    #region 导航属性包含

    /// <summary>
    /// 包含单个导航属性（一对一、一对多）
    /// </summary>
    /// <typeparam name="T">主实体类型</typeparam>
    /// <typeparam name="TProperty">导航属性类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="navigationProperty">导航属性表达式</param>
    /// <returns>查询接口，支持链式调用</returns>
    /// <remarks>
    /// 用于预加载关联数据，避免 N+1 查询问题
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查询用户及其订单
    /// var users = await YData.Select&lt;User&gt;()
    ///     .YInclude(u => u.Orders)
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> YInclude<T, TProperty>(this ISelect<T> select, Expression<Func<T, TProperty>> navigationProperty)
        where T : class
        where TProperty : class
    {
        return select.Include(navigationProperty);
    }

    /// <summary>
    /// 包含多个导航属性
    /// </summary>
    /// <typeparam name="T">主实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="navigationProperties">导航属性表达式数组</param>
    /// <returns>查询接口，支持链式调用</returns>
    /// <remarks>
    /// 一次性包含多个关联实体，提高查询效率
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查询用户及其订单和地址
    /// var users = await YData.Select&lt;User&gt;()
    ///     .YIncludeMany(u => u.Orders, u => u.Address, u => u.Profile)
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> YIncludeMany<T>(this ISelect<T> select, params Expression<Func<T, object>>[] navigationProperties)
        where T : class
    {
        var result = select;
        foreach (var property in navigationProperties)
        {
            result = result.Include(property);
        }
        return result;
    }

    #endregion

    #region 表连接操作

    /// <summary>
    /// 左连接（LEFT JOIN）
    /// </summary>
    /// <typeparam name="T">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="joinCondition">连接条件表达式</param>
    /// <returns>连接查询接口，支持进一步操作</returns>
    /// <remarks>
    /// 左连接会返回主表的所有记录，即使连接表中没有匹配的记录
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查询用户及其订单（包括没有订单的用户）
    /// var result = await YData.Select&lt;User&gt;()
    ///     .YLeftJoin&lt;User, Order&gt;((u, o) => u.Id == o.UserId)
    ///     .ToListAsync((u, o) => new { User = u, Order = o });
    /// </code>
    /// </example>
    public static ISelect<T, T2> YLeftJoin<T, T2>(this ISelect<T> select, Expression<Func<T, T2, bool>> joinCondition)
        where T : class
        where T2 : class
    {
        return select.From<T2>().LeftJoin(joinCondition);
    }

    /// <summary>
    /// 内连接（INNER JOIN）
    /// </summary>
    /// <typeparam name="T">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="joinCondition">连接条件表达式</param>
    /// <returns>连接查询接口，支持进一步操作</returns>
    /// <remarks>
    /// 内连接只返回两个表中都有匹配记录的数据
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查询有订单的用户及其订单信息
    /// var result = await YData.Select&lt;User&gt;()
    ///     .YInnerJoin&lt;User, Order&gt;((u, o) => u.Id == o.UserId)
    ///     .ToListAsync((u, o) => new { User = u, Order = o });
    /// </code>
    /// </example>
    public static ISelect<T, T2> YInnerJoin<T, T2>(this ISelect<T> select, Expression<Func<T, T2, bool>> joinCondition)
        where T : class
        where T2 : class
    {
        return select.From<T2>().InnerJoin(joinCondition);
    }

    /// <summary>
    /// 右连接（RIGHT JOIN）
    /// </summary>
    /// <typeparam name="T">主表类型</typeparam>
    /// <typeparam name="T2">连接表类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="joinCondition">连接条件表达式</param>
    /// <returns>连接查询接口，支持进一步操作</returns>
    /// <remarks>
    /// 右连接会返回连接表的所有记录，即使主表中没有匹配的记录
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查询所有订单及其用户信息（包括用户已删除的订单）
    /// var result = await YData.Select&lt;User&gt;()
    ///     .YRightJoin&lt;User, Order&gt;((u, o) => u.Id == o.UserId)
    ///     .ToListAsync((u, o) => new { User = u, Order = o });
    /// </code>
    /// </example>
    public static ISelect<T, T2> YRightJoin<T, T2>(this ISelect<T> select, Expression<Func<T, T2, bool>> joinCondition)
        where T : class
        where T2 : class
    {
        return select.From<T2>().RightJoin(joinCondition);
    }

    #endregion

    #region 分组和聚合查询

    /// <summary>
    /// 分组查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TKey">分组键类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="keySelector">分组键选择器表达式</param>
    /// <returns>分组查询接口，支持聚合操作</returns>
    /// <remarks>
    /// 用于按指定字段对数据进行分组，通常配合聚合函数使用
    /// </remarks>
    /// <example>
    /// <code>
    /// // 按部门分组统计员工数量
    /// var result = await YData.Select&lt;Employee&gt;()
    ///     .YGroupBy(e => e.DepartmentId)
    ///     .ToListAsync(g => new { DepartmentId = g.Key, Count = g.Count() });
    /// </code>
    /// </example>
    public static ISelectGrouping<TKey, T> YGroupBy<T, TKey>(this ISelect<T> select, Expression<Func<T, TKey>> keySelector)
        where T : class
    {
        return select.GroupBy(keySelector);
    }

    /// <summary>
    /// 聚合查询 - 计数
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="predicate">计数条件（可选）</param>
    /// <returns>符合条件的记录数</returns>
    /// <remarks>
    /// 统计符合条件的记录数量，如果不指定条件则统计所有记录
    /// </remarks>
    /// <example>
    /// <code>
    /// // 统计所有用户数量
    /// var totalUsers = await YData.Select&lt;User&gt;().YCountAsync();
    /// 
    /// // 统计活跃用户数量
    /// var activeUsers = await YData.Select&lt;User&gt;().YCountAsync(u => u.IsActive);
    /// </code>
    /// </example>
    public static async Task<long> YCountAsync<T>(this ISelect<T> select, Expression<Func<T, bool>>? predicate = null)
        where T : class
    {
        if (predicate != null)
        {
            select = select.Where(predicate);
        }
        return await select.CountAsync();
    }

    /// <summary>
    /// 聚合查询 - 求和
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="selector">要求和的字段选择器</param>
    /// <returns>求和结果</returns>
    /// <remarks>
    /// 计算指定数值字段的总和
    /// </remarks>
    /// <example>
    /// <code>
    /// // 计算所有订单的总金额
    /// var totalAmount = await YData.Select&lt;Order&gt;().YSumAsync(o => o.Amount);
    /// </code>
    /// </example>
    public static async Task<decimal> YSumAsync<T>(this ISelect<T> select, Expression<Func<T, object>> selector)
        where T : class
    {
        return await select.SumAsync(selector);
    }

    /// <summary>
    /// 聚合查询 - 平均值
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="selector">要计算平均值的字段选择器</param>
    /// <returns>平均值</returns>
    /// <remarks>
    /// 计算指定数值字段的平均值
    /// </remarks>
    /// <example>
    /// <code>
    /// // 计算所有订单的平均金额
    /// var avgAmount = await YData.Select&lt;Order&gt;().YAverageAsync(o => o.Amount);
    /// </code>
    /// </example>
    public static async Task<decimal> YAverageAsync<T>(this ISelect<T> select, Expression<Func<T, object>> selector)
        where T : class
    {
        var result = await select.AvgAsync(selector);
        return Convert.ToDecimal(result);
    }

    /// <summary>
    /// 聚合查询 - 最大值
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="selector">要查找最大值的字段选择器</param>
    /// <returns>最大值</returns>
    /// <remarks>
    /// 查找指定字段的最大值
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查找最高的订单金额
    /// var maxAmount = await YData.Select&lt;Order&gt;().YMaxAsync(o => o.Amount);
    /// </code>
    /// </example>
    public static async Task<TResult> YMaxAsync<T, TResult>(this ISelect<T> select, Expression<Func<T, TResult>> selector)
        where T : class
    {
        return await select.MaxAsync(selector);
    }

    /// <summary>
    /// 聚合查询 - 最小值
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <typeparam name="TResult">结果类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="selector">要查找最小值的字段选择器</param>
    /// <returns>最小值</returns>
    /// <remarks>
    /// 查找指定字段的最小值
    /// </remarks>
    /// <example>
    /// <code>
    /// // 查找最低的订单金额
    /// var minAmount = await YData.Select&lt;Order&gt;().YMinAsync(o => o.Amount);
    /// </code>
    /// </example>
    public static async Task<TResult> YMinAsync<T, TResult>(this ISelect<T> select, Expression<Func<T, TResult>> selector)
        where T : class
    {
        return await select.MinAsync(selector);
    }

    /// <summary>
    /// 联合查询（UNION）
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select1">第一个查询</param>
    /// <param name="select2">第二个查询</param>
    /// <returns>联合查询结果，自动去重</returns>
    /// <remarks>
    /// 将两个查询的结果合并，自动去除重复记录
    /// </remarks>
    /// <example>
    /// <code>
    /// // 合并活跃用户和VIP用户查询结果
    /// var activeUsers = YData.Select&lt;User&gt;().Where(u => u.IsActive);
    /// var vipUsers = YData.Select&lt;User&gt;().Where(u => u.IsVip);
    /// var result = await activeUsers.YUnionAsync(vipUsers);
    /// </code>
    /// </example>
    public static async Task<List<T>> YUnionAsync<T>(this ISelect<T> select1, ISelect<T> select2)
        where T : class
    {
        var result1 = await select1.ToListAsync();
        var result2 = await select2.ToListAsync();
        return [.. result1.Union(result2)];
    }

    /// <summary>
    /// 多表分页查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="pageIndex">页索引（从1开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <returns>分页结果，包含数据和分页信息</returns>
    /// <remarks>
    /// 专门用于多表查询的分页，自动计算总数和分页信息
    /// </remarks>
    /// <example>
    /// <code>
    /// // 多表联合查询分页
    /// var result = await YData.Select&lt;User&gt;()
    ///     .YLeftJoin&lt;User, Order&gt;((u, o) => u.Id == o.UserId)
    ///     .YToPagedResultAsync(1, 20);
    /// </code>
    /// </example>
    public static async Task<PagedResult<T>> YToPagedResultAsync<T>(this ISelect<T> select, int pageIndex, int pageSize)
        where T : class
    {
        var totalCount = await select.CountAsync();
        var items = await select.Page(pageIndex, pageSize).ToListAsync();

        return PagedResult<T>.Create(items, pageIndex, pageSize, totalCount);
    }

    #endregion

    #region 批量操作

    /// <summary>
    /// 批量插入
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="insert">插入接口</param>
    /// <param name="entities">要插入的实体列表</param>
    /// <param name="batchSize">批次大小，默认 1000</param>
    /// <returns>总影响行数</returns>
    /// <remarks>
    /// 将大量数据分批插入，避免单次操作数据量过大导致的性能问题
    /// </remarks>
    /// <example>
    /// <code>
    /// // 批量插入用户数据
    /// var users = GetLargeUserList();
    /// var affected = await YData.Insert&lt;User&gt;().YBatchInsertAsync(users, 500);
    /// </code>
    /// </example>
    public static async Task<int> YBatchInsertAsync<T>(this IInsert<T> insert, IEnumerable<T> entities, int batchSize = 1000)
        where T : class
    {
        var entityList = entities.ToList();
        var totalAffected = 0;

        for (int i = 0; i < entityList.Count; i += batchSize)
        {
            var batch = entityList.Skip(i).Take(batchSize);
            var affected = await insert.AppendData(batch).ExecuteAffrowsAsync();
            totalAffected += affected;
        }

        return totalAffected;
    }

    /// <summary>
    /// 批量更新
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="update">更新接口</param>
    /// <param name="entities">要更新的实体列表</param>
    /// <param name="batchSize">批次大小，默认 1000</param>
    /// <returns>总影响行数</returns>
    /// <remarks>
    /// 将大量数据分批更新，避免单次操作数据量过大导致的性能问题
    /// </remarks>
    /// <example>
    /// <code>
    /// // 批量更新用户数据
    /// var users = GetUpdatedUserList();
    /// var affected = await YData.Update&lt;User&gt;().YBatchUpdateAsync(users, 500);
    /// </code>
    /// </example>
    public static async Task<int> YBatchUpdateAsync<T>(this IUpdate<T> update, IEnumerable<T> entities, int batchSize = 1000)
        where T : class
    {
        var entityList = entities.ToList();
        var totalAffected = 0;

        for (int i = 0; i < entityList.Count; i += batchSize)
        {
            var batch = entityList.Skip(i).Take(batchSize);
            var affected = await update.SetSource(batch).ExecuteAffrowsAsync();
            totalAffected += affected;
        }

        return totalAffected;
    }

    #endregion

    #region 条件查询和动态排序

    /// <summary>
    /// 条件查询扩展
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="condition">是否应用条件</param>
    /// <param name="predicate">查询条件表达式</param>
    /// <returns>查询接口</returns>
    /// <remarks>
    /// 只有当 condition 为 true 时才应用查询条件，用于动态查询构建
    /// </remarks>
    /// <example>
    /// <code>
    /// // 根据条件动态添加过滤
    /// var query = YData.Select&lt;User&gt;()
    ///     .YWhereIf(!string.IsNullOrEmpty(name), u => u.Name.Contains(name))
    ///     .YWhereIf(age.HasValue, u => u.Age >= age.Value);
    /// </code>
    /// </example>
    public static ISelect<T> YWhereIf<T>(this ISelect<T> select, bool condition, Expression<Func<T, bool>> predicate)
        where T : class
    {
        return condition ? select.Where(predicate) : select;
    }

    /// <summary>
    /// 动态排序
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="select">查询接口</param>
    /// <param name="propertyName">要排序的属性名</param>
    /// <param name="ascending">是否升序，默认 true</param>
    /// <returns>查询接口</returns>
    /// <remarks>
    /// 根据字符串属性名进行动态排序，适用于前端传递排序字段的场景
    /// </remarks>
    /// <example>
    /// <code>
    /// // 根据前端传递的字段名排序
    /// var users = await YData.Select&lt;User&gt;()
    ///     .YOrderByDynamic("CreateTime", false) // 按创建时间降序
    ///     .ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> YOrderByDynamic<T>(this ISelect<T> select, string propertyName, bool ascending = true)
        where T : class
    {
        if (string.IsNullOrWhiteSpace(propertyName))
            return select;

        // 使用字符串形式的排序
        var sql = ascending ? $"ORDER BY {propertyName}" : $"ORDER BY {propertyName} DESC";
        return select.OrderBy(sql);
    }

    #endregion
}
