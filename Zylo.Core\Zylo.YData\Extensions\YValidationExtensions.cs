namespace Zylo.YData.Extensions;

/// <summary>
/// 常用验证扩展方法
/// 提供便捷的验证功能
/// </summary>
public static class YValidationExtensions
{
    #region 字符串验证扩展

    /// <summary>
    /// 验证字符串是否为有效邮箱
    /// </summary>
    /// <param name="email">邮箱字符串</param>
    /// <returns>是否为有效邮箱</returns>
    /// <example>
    /// <code>
    /// var isValid = "<EMAIL>".YIsValidEmail();
    /// Console.WriteLine($"邮箱有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsValidEmail(this string? email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证字符串是否为有效手机号
    /// </summary>
    /// <param name="phone">手机号字符串</param>
    /// <returns>是否为有效手机号</returns>
    /// <example>
    /// <code>
    /// var isValid = "13812345678".YIsValidPhone();
    /// Console.WriteLine($"手机号有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsValidPhone(this string? phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return false;

        // 简单的中国手机号验证规则
        return System.Text.RegularExpressions.Regex.IsMatch(phone, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 验证字符串是否为有效身份证号
    /// </summary>
    /// <param name="idCard">身份证号字符串</param>
    /// <returns>是否为有效身份证号</returns>
    /// <example>
    /// <code>
    /// var isValid = "110101199001011234".YIsValidIdCard();
    /// Console.WriteLine($"身份证号有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsValidIdCard(this string? idCard)
    {
        if (string.IsNullOrWhiteSpace(idCard))
            return false;

        // 18位身份证号验证
        if (idCard.Length == 18)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(idCard, @"^\d{17}[\dXx]$");
        }
        // 15位身份证号验证
        else if (idCard.Length == 15)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(idCard, @"^\d{15}$");
        }

        return false;
    }

    /// <summary>
    /// 验证字符串长度是否在指定范围内
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>是否在范围内</returns>
    /// <example>
    /// <code>
    /// var isValid = "Hello".YIsValidLength(3, 10);
    /// Console.WriteLine($"长度有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsValidLength(this string? value, int minLength, int maxLength)
    {
        if (value == null) return minLength == 0;
        return value.Length >= minLength && value.Length <= maxLength;
    }

    /// <summary>
    /// 验证字符串是否只包含数字
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>是否只包含数字</returns>
    /// <example>
    /// <code>
    /// var isValid = "12345".YIsNumeric();
    /// Console.WriteLine($"是数字: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsNumeric(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        return value.All(char.IsDigit);
    }

    /// <summary>
    /// 验证字符串是否只包含字母
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>是否只包含字母</returns>
    /// <example>
    /// <code>
    /// var isValid = "Hello".YIsAlpha();
    /// Console.WriteLine($"是字母: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsAlpha(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        return value.All(char.IsLetter);
    }

    /// <summary>
    /// 验证字符串是否只包含字母和数字
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>是否只包含字母和数字</returns>
    /// <example>
    /// <code>
    /// var isValid = "Hello123".YIsAlphaNumeric();
    /// Console.WriteLine($"是字母数字: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsAlphaNumeric(this string? value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return false;

        return value.All(char.IsLetterOrDigit);
    }

    #endregion

    #region 数值验证扩展

    /// <summary>
    /// 验证数值是否在指定范围内
    /// </summary>
    /// <param name="value">数值</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>是否在范围内</returns>
    /// <example>
    /// <code>
    /// var isValid = 25.YIsInRange(18, 65);
    /// Console.WriteLine($"年龄有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsInRange(this int value, int min, int max)
    {
        return value >= min && value <= max;
    }

    /// <summary>
    /// 验证数值是否在指定范围内
    /// </summary>
    /// <param name="value">数值</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>是否在范围内</returns>
    public static bool YIsInRange(this double value, double min, double max)
    {
        return value >= min && value <= max;
    }

    /// <summary>
    /// 验证数值是否在指定范围内
    /// </summary>
    /// <param name="value">数值</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>是否在范围内</returns>
    public static bool YIsInRange(this decimal value, decimal min, decimal max)
    {
        return value >= min && value <= max;
    }

    /// <summary>
    /// 验证数值是否为正数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为正数</returns>
    /// <example>
    /// <code>
    /// var isValid = 100.YIsPositive();
    /// Console.WriteLine($"是正数: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsPositive(this int value)
    {
        return value > 0;
    }

    /// <summary>
    /// 验证数值是否为正数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为正数</returns>
    public static bool YIsPositive(this double value)
    {
        return value > 0;
    }

    /// <summary>
    /// 验证数值是否为正数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为正数</returns>
    public static bool YIsPositive(this decimal value)
    {
        return value > 0;
    }

    /// <summary>
    /// 验证数值是否为非负数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为非负数</returns>
    /// <example>
    /// <code>
    /// var isValid = 0.YIsNonNegative();
    /// Console.WriteLine($"是非负数: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsNonNegative(this int value)
    {
        return value >= 0;
    }

    /// <summary>
    /// 验证数值是否为非负数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为非负数</returns>
    public static bool YIsNonNegative(this double value)
    {
        return value >= 0;
    }

    /// <summary>
    /// 验证数值是否为非负数
    /// </summary>
    /// <param name="value">数值</param>
    /// <returns>是否为非负数</returns>
    public static bool YIsNonNegative(this decimal value)
    {
        return value >= 0;
    }

    #endregion

    #region 日期验证扩展

    /// <summary>
    /// 验证日期是否在指定范围内
    /// </summary>
    /// <param name="date">日期</param>
    /// <param name="minDate">最小日期</param>
    /// <param name="maxDate">最大日期</param>
    /// <returns>是否在范围内</returns>
    /// <example>
    /// <code>
    /// var isValid = DateTime.Now.YIsInRange(DateTime.Today.AddYears(-100), DateTime.Today);
    /// Console.WriteLine($"日期有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsInRange(this DateTime date, DateTime minDate, DateTime maxDate)
    {
        return date >= minDate && date <= maxDate;
    }

    /// <summary>
    /// 验证日期是否为过去的日期
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>是否为过去的日期</returns>
    /// <example>
    /// <code>
    /// var isValid = DateTime.Now.AddDays(-1).YIsPastDate();
    /// Console.WriteLine($"是过去日期: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsPastDate(this DateTime date)
    {
        return date < DateTime.Now;
    }

    /// <summary>
    /// 验证日期是否为未来的日期
    /// </summary>
    /// <param name="date">日期</param>
    /// <returns>是否为未来的日期</returns>
    /// <example>
    /// <code>
    /// var isValid = DateTime.Now.AddDays(1).YIsFutureDate();
    /// Console.WriteLine($"是未来日期: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsFutureDate(this DateTime date)
    {
        return date > DateTime.Now;
    }

    /// <summary>
    /// 验证年龄是否合理
    /// </summary>
    /// <param name="birthDate">出生日期</param>
    /// <param name="minAge">最小年龄</param>
    /// <param name="maxAge">最大年龄</param>
    /// <returns>是否合理</returns>
    /// <example>
    /// <code>
    /// var birthDate = new DateTime(1990, 1, 1);
    /// var isValid = birthDate.YIsValidAge(18, 65);
    /// Console.WriteLine($"年龄有效: {isValid}");
    /// </code>
    /// </example>
    public static bool YIsValidAge(this DateTime birthDate, int minAge = 0, int maxAge = 150)
    {
        var age = DateTime.Today.Year - birthDate.Year;
        if (birthDate.Date > DateTime.Today.AddYears(-age)) age--;
        
        return age >= minAge && age <= maxAge;
    }

    #endregion
}
