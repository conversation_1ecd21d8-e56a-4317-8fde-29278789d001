// 🔥 FreeSql 核心命名空间
global using FreeSql;
global using FreeSql.DataAnnotations;
global using FreeSqlDataType = FreeSql.DataType; // 使用别名避免冲突

// 💉 依赖注入和配置
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Options;
global using Microsoft.Extensions.Logging;

// 🔧 系统命名空间
global using System;
global using System.Collections.Generic;
global using System.Collections.Concurrent;
global using System.Linq;
global using System.Threading.Tasks;
global using System.Threading;
global using System.Linq.Expressions;
global using System.Text.Json;
global using System.Data;
global using System.Reflection;
global using System.ComponentModel.DataAnnotations;

// 🎯 Zylo.YData 内部命名空间
global using Zylo.YData.Models;

// 注释：暂时不需要其他 Zylo 组件
// global using Zylo.Core;
// global using Zylo.YIO;
// global using Zylo.YLog.Runtime;
