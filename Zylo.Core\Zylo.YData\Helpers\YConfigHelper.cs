using System.Text.Json;
using System.Text.Json.Nodes;

namespace Zylo.YData.Helpers;

/// <summary>
/// 配置文件管理辅助类
/// <para>提供 appsettings.json 文件的创建、读取和修改功能</para>
/// </summary>
/// <remarks>
/// <para>此类按功能分为以下几个模块：</para>
/// <list type="number">
/// <item><strong>配置文件创建</strong> - 自动创建标准的 appsettings.json 文件</item>
/// <item><strong>连接字符串读取</strong> - 读取单个或所有连接字符串</item>
/// <item><strong>连接字符串修改</strong> - 设置、更新、删除连接字符串</item>
/// <item><strong>数据库快速切换</strong> - 一键切换不同类型的数据库</item>
/// <item><strong>配置文件管理</strong> - 备份和恢复配置文件</item>
/// </list>
/// <para>所有方法都包含完善的错误处理，确保操作的安全性和可靠性。</para>
/// </remarks>
public static class YConfigHelper
{
    /// <summary>
    /// 默认配置文件名称
    /// </summary>
    private const string DefaultConfigFile = "appsettings.json";

    #region 配置文件创建功能

    /// <summary>
    /// 创建默认的 appsettings.json 文件
    /// </summary>
    /// <param name="defaultConnectionString">默认连接字符串</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <param name="overwrite">是否覆盖已存在的文件，默认为 false</param>
    /// <returns>是否成功创建</returns>
    /// <example>
    /// <code>
    /// // 创建基础配置文件
    /// YConfigHelper.CreateDefaultConfig("Data Source=myapp.db");
    /// 
    /// // 创建 SQL Server 配置
    /// YConfigHelper.CreateDefaultConfig("Server=localhost;Database=MyApp;Integrated Security=true;");
    /// 
    /// // 覆盖已存在的文件
    /// YConfigHelper.CreateDefaultConfig("Data Source=newapp.db", overwrite: true);
    /// </code>
    /// </example>
    public static bool CreateDefaultConfig(string defaultConnectionString, string filePath = DefaultConfigFile, bool overwrite = false)
    {
        if (string.IsNullOrWhiteSpace(defaultConnectionString))
            throw new ArgumentException("连接字符串不能为空", nameof(defaultConnectionString));

        if (File.Exists(filePath) && !overwrite)
        {
            return false; // 文件已存在且不覆盖
        }

        var config = new
        {
            ConnectionStrings = new
            {
                DefaultConnection = defaultConnectionString
            },
            Logging = new
            {
                LogLevel = new
                {
                    Default = "Information",
                    Microsoft = "Warning",
                    System = "Warning"
                }
            },
            YData = new
            {
                EnableAutoSyncStructure = true,
                EnableMonitorCommand = true,
                DefaultQueryTimeout = "00:00:30"
            }
        };

        try
        {
            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            File.WriteAllText(filePath, json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 连接字符串读取功能

    /// <summary>
    /// 读取配置文件中的连接字符串
    /// </summary>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>连接字符串，如果未找到则返回 null</returns>
    /// <example>
    /// <code>
    /// // 读取默认连接字符串
    /// var connStr = YConfigHelper.GetConnectionString();
    /// 
    /// // 读取指定名称的连接字符串
    /// var logConnStr = YConfigHelper.GetConnectionString("LogsConnection");
    /// </code>
    /// </example>
    public static string? GetConnectionString(string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        if (!File.Exists(filePath))
            return null;

        try
        {
            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);

            return config?["ConnectionStrings"]?[connectionName]?.ToString();
        }
        catch
        {
            return null;
        }
    }

    #endregion

    #region 连接字符串修改功能

    /// <summary>
    /// 设置配置文件中的连接字符串
    /// </summary>
    /// <param name="connectionString">新的连接字符串</param>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功设置</returns>
    /// <example>
    /// <code>
    /// // 更新默认连接字符串
    /// YConfigHelper.SetConnectionString("Data Source=newapp.db");
    /// 
    /// // 添加新的连接字符串
    /// YConfigHelper.SetConnectionString("Data Source=logs.db", "LogsConnection");
    /// 
    /// // 切换到 SQL Server
    /// YConfigHelper.SetConnectionString("Server=localhost;Database=MyApp;Integrated Security=true;");
    /// </code>
    /// </example>
    public static bool SetConnectionString(string connectionString, string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        // 参数验证：确保连接字符串不为空
        if (string.IsNullOrWhiteSpace(connectionString))
            throw new ArgumentException("连接字符串不能为空", nameof(connectionString));

        try
        {
            JsonNode? config;

            // 检查配置文件是否存在
            if (File.Exists(filePath))
            {
                // 文件存在：读取现有配置文件内容
                var json = File.ReadAllText(filePath);
                // 解析 JSON 内容为 JsonNode 对象，便于操作
                config = JsonNode.Parse(json);
            }
            else
            {
                // 文件不存在：创建新的空 JSON 对象
                config = new JsonObject();
            }

            // 确保 ConnectionStrings 节点存在
            // 如果配置中没有 ConnectionStrings 节点，则创建一个新的
            if (config!["ConnectionStrings"] == null)
            {
                config["ConnectionStrings"] = new JsonObject();
            }

            // 设置指定名称的连接字符串
            // 如果连接字符串已存在则更新，不存在则添加
            config["ConnectionStrings"]![connectionName] = connectionString;

            // 将更新后的配置写回文件
            // 配置 JSON 序列化选项：
            // - WriteIndented: true - 格式化输出，便于阅读
            // - UnsafeRelaxedJsonEscaping - 允许更宽松的字符编码，避免过度转义
            var updatedJson = config.ToJsonString(new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            // 将格式化后的 JSON 字符串写入文件
            File.WriteAllText(filePath, updatedJson);

            // 操作成功，返回 true
            return true;
        }
        catch
        {
            // 发生任何异常（文件权限、JSON 格式错误等），返回 false
            // 这里使用 catch-all 是为了确保方法的健壮性
            return false;
        }
    }

    /// <summary>
    /// 获取配置文件中的所有连接字符串
    /// </summary>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>连接字符串字典</returns>
    /// <example>
    /// <code>
    /// var connections = YConfigHelper.GetAllConnectionStrings();
    /// foreach (var conn in connections)
    /// {
    ///     Console.WriteLine($"{conn.Key}: {conn.Value}");
    /// }
    /// </code>
    /// </example>
    public static Dictionary<string, string> GetAllConnectionStrings(string filePath = DefaultConfigFile)
    {
        var result = new Dictionary<string, string>();

        if (!File.Exists(filePath))
            return result;

        try
        {
            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);
            var connectionStrings = config?["ConnectionStrings"]?.AsObject();

            if (connectionStrings != null)
            {
                foreach (var kvp in connectionStrings)
                {
                    if (kvp.Value != null)
                    {
                        result[kvp.Key] = kvp.Value.ToString();
                    }
                }
            }
        }
        catch
        {
            // 忽略错误，返回空字典
        }

        return result;
    }

    /// <summary>
    /// 删除配置文件中的连接字符串
    /// </summary>
    /// <param name="connectionName">要删除的连接字符串名称</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功删除</returns>
    /// <example>
    /// <code>
    /// // 删除指定的连接字符串
    /// YConfigHelper.RemoveConnectionString("OldConnection");
    /// </code>
    /// </example>
    public static bool RemoveConnectionString(string connectionName, string filePath = DefaultConfigFile)
    {
        if (string.IsNullOrWhiteSpace(connectionName))
            throw new ArgumentException("连接字符串名称不能为空", nameof(connectionName));

        if (!File.Exists(filePath))
            return false;

        try
        {
            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);
            var connectionStrings = config?["ConnectionStrings"]?.AsObject();

            if (connectionStrings != null && connectionStrings.ContainsKey(connectionName))
            {
                connectionStrings.Remove(connectionName);

                var updatedJson = config!.ToJsonString(new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(filePath, updatedJson);
                return true;
            }
        }
        catch
        {
            // 忽略错误
        }

        return false;
    }

    #endregion

    #region 配置文件管理功能

    /// <summary>
    /// 备份配置文件
    /// </summary>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <param name="backupPath">备份文件路径，如果为空则自动生成</param>
    /// <returns>备份文件路径，失败时返回 null</returns>
    /// <example>
    /// <code>
    /// // 自动生成备份文件名
    /// var backupFile = YConfigHelper.BackupConfig();
    /// 
    /// // 指定备份文件名
    /// var backupFile = YConfigHelper.BackupConfig("appsettings.json", "appsettings.backup.json");
    /// </code>
    /// </example>
    public static string? BackupConfig(string filePath = DefaultConfigFile, string? backupPath = null)
    {
        if (!File.Exists(filePath))
            return null;

        try
        {
            if (string.IsNullOrEmpty(backupPath))
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                backupPath = $"{Path.GetFileNameWithoutExtension(filePath)}.backup.{timestamp}.json";
            }

            File.Copy(filePath, backupPath, true);
            return backupPath;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 从备份恢复配置文件
    /// </summary>
    /// <param name="backupPath">备份文件路径</param>
    /// <param name="filePath">目标配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功恢复</returns>
    /// <example>
    /// <code>
    /// // 从备份恢复
    /// YConfigHelper.RestoreConfig("appsettings.backup.20241201_143022.json");
    /// </code>
    /// </example>
    public static bool RestoreConfig(string backupPath, string filePath = DefaultConfigFile)
    {
        if (!File.Exists(backupPath))
            return false;

        try
        {
            File.Copy(backupPath, filePath, true);
            return true;
        }
        catch
        {
            return false;
        }
    }

    #endregion

    #region 数据库快速切换功能

    /// <summary>
    /// 快速切换数据库连接
    /// </summary>
    /// <param name="databaseType">数据库类型</param>
    /// <param name="parameters">连接参数</param>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功切换</returns>
    /// <example>
    /// <code>
    /// // 切换到 SQLite
    /// YConfigHelper.SwitchDatabase("SQLite", new { FilePath = "myapp.db" });
    /// 
    /// // 切换到 SQL Server
    /// YConfigHelper.SwitchDatabase("SqlServer", new { Server = "localhost", Database = "MyApp" });
    /// 
    /// // 切换到 MySQL
    /// YConfigHelper.SwitchDatabase("MySQL", new { Server = "localhost", Database = "myapp", UserId = "root", Password = "123456" });
    /// </code>
    /// </example>
    public static bool SwitchDatabase(string databaseType, object parameters, string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        // 根据数据库类型构建相应的连接字符串
        // 使用 switch 表达式进行模式匹配，支持多种数据库类型
        // 将数据库类型转换为小写以实现大小写不敏感的匹配
        string connectionString = databaseType.ToLower() switch
        {
            // SQLite 数据库：轻量级文件数据库，适合开发和小型应用
            "sqlite" => BuildSQLiteConnectionString(parameters),

            // SQL Server 数据库：微软企业级数据库
            "sqlserver" => BuildSqlServerConnectionString(parameters),

            // MySQL 数据库：开源关系型数据库
            "mysql" => BuildMySQLConnectionString(parameters),

            // PostgreSQL 数据库：功能强大的开源对象关系数据库
            "postgresql" => BuildPostgreSQLConnectionString(parameters),

            // 不支持的数据库类型：抛出异常
            _ => throw new ArgumentException($"不支持的数据库类型: {databaseType}")
        };

        // 将构建好的连接字符串设置到配置文件中
        // 如果设置成功返回 true，失败返回 false
        return SetConnectionString(connectionString, connectionName, filePath);
    }

    #endregion

    #region SQLite 专用便捷方法

    /// <summary>
    /// 快速创建 SQLite 配置文件
    /// </summary>
    /// <param name="dbFileName">数据库文件名（不需要路径和扩展名）</param>
    /// <param name="password">数据库密码（可选）</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功创建</returns>
    /// <example>
    /// <code>
    /// // 创建名为 myapp.db 的 SQLite 配置
    /// YConfigHelper.CreateSQLiteConfig("myapp");
    ///
    /// // 创建带密码的 SQLite 配置
    /// YConfigHelper.CreateSQLiteConfig("secure", "password123");
    ///
    /// // 创建到指定配置文件
    /// YConfigHelper.CreateSQLiteConfig("test", filePath: "test.json");
    /// </code>
    /// </example>
    public static bool CreateSQLiteConfig(string dbFileName, string? password = null, string filePath = DefaultConfigFile)
    {
        if (string.IsNullOrWhiteSpace(dbFileName))
            throw new ArgumentException("数据库文件名不能为空", nameof(dbFileName));

        // 自动添加 .db 扩展名（如果没有的话）
        if (!dbFileName.EndsWith(".db", StringComparison.OrdinalIgnoreCase))
        {
            dbFileName += ".db";
        }

        // 构建 SQLite 连接字符串
        var connectionString = YConnectionStringHelper.BuildSQLite(dbFileName, password);

        return CreateDefaultConfig(connectionString, filePath);
    }

    /// <summary>
    /// 快速切换到 SQLite 数据库
    /// </summary>
    /// <param name="dbFileName">数据库文件名（不需要路径和扩展名）</param>
    /// <param name="password">数据库密码（可选）</param>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功切换</returns>
    /// <example>
    /// <code>
    /// // 切换到开发数据库
    /// YConfigHelper.SwitchToSQLite("dev");
    ///
    /// // 切换到测试数据库
    /// YConfigHelper.SwitchToSQLite("test");
    ///
    /// // 切换到生产数据库（带密码）
    /// YConfigHelper.SwitchToSQLite("prod", "prod_password");
    ///
    /// // 切换指定连接的数据库
    /// YConfigHelper.SwitchToSQLite("logs", connectionName: "LogsConnection");
    /// </code>
    /// </example>
    public static bool SwitchToSQLite(string dbFileName, string? password = null, string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        if (string.IsNullOrWhiteSpace(dbFileName))
            throw new ArgumentException("数据库文件名不能为空", nameof(dbFileName));

        // 自动添加 .db 扩展名（如果没有的话）
        if (!dbFileName.EndsWith(".db", StringComparison.OrdinalIgnoreCase))
        {
            dbFileName += ".db";
        }

        // 构建 SQLite 连接字符串
        var connectionString = YConnectionStringHelper.BuildSQLite(dbFileName, password);

        return SetConnectionString(connectionString, connectionName, filePath);
    }

    /// <summary>
    /// 创建多环境 SQLite 配置
    /// </summary>
    /// <param name="environments">环境配置字典，键为环境名，值为数据库文件名</param>
    /// <param name="defaultEnvironment">默认环境名称</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功创建</returns>
    /// <example>
    /// <code>
    /// // 创建多环境配置
    /// var environments = new Dictionary&lt;string, string&gt;
    /// {
    ///     ["Development"] = "dev",
    ///     ["Testing"] = "test",
    ///     ["Production"] = "prod"
    /// };
    /// YConfigHelper.CreateMultiEnvironmentSQLiteConfig(environments, "Development");
    /// </code>
    /// </example>
    public static bool CreateMultiEnvironmentSQLiteConfig(Dictionary<string, string> environments, string defaultEnvironment, string filePath = DefaultConfigFile)
    {
        if (environments == null || !environments.Any())
            throw new ArgumentException("环境配置不能为空", nameof(environments));

        if (!environments.ContainsKey(defaultEnvironment))
            throw new ArgumentException($"默认环境 '{defaultEnvironment}' 不存在于环境配置中", nameof(defaultEnvironment));

        try
        {
            // 构建多环境连接字符串
            var connectionStrings = new Dictionary<string, string>();

            foreach (var env in environments)
            {
                var dbFileName = env.Value;
                // 自动添加 .db 扩展名
                if (!dbFileName.EndsWith(".db", StringComparison.OrdinalIgnoreCase))
                {
                    dbFileName += ".db";
                }

                var connectionString = YConnectionStringHelper.BuildSQLite(dbFileName);
                connectionStrings[$"{env.Key}Connection"] = connectionString;
            }

            // 设置默认连接
            var defaultConnectionString = connectionStrings[$"{defaultEnvironment}Connection"];

            // 创建配置对象
            var config = new
            {
                ConnectionStrings = connectionStrings.ToDictionary(
                    kvp => kvp.Key == $"{defaultEnvironment}Connection" ? "DefaultConnection" : kvp.Key,
                    kvp => kvp.Value
                ).Concat(connectionStrings.Where(kvp => kvp.Key != $"{defaultEnvironment}Connection"))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value),

                Logging = new
                {
                    LogLevel = new
                    {
                        Default = "Information",
                        Microsoft = "Warning",
                        System = "Warning"
                    }
                },
                YData = new
                {
                    EnableAutoSyncStructure = true,
                    EnableMonitorCommand = true,
                    DefaultQueryTimeout = "00:00:30"
                },
                Environment = new
                {
                    Current = defaultEnvironment,
                    Available = environments.Keys.ToArray()
                }
            };

            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            File.WriteAllText(filePath, json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 快速切换环境
    /// </summary>
    /// <param name="environment">环境名称（Development、Testing、Production等）</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>是否成功切换</returns>
    /// <example>
    /// <code>
    /// // 切换到开发环境
    /// YConfigHelper.SwitchEnvironment("Development");
    ///
    /// // 切换到生产环境
    /// YConfigHelper.SwitchEnvironment("Production");
    /// </code>
    /// </example>
    public static bool SwitchEnvironment(string environment, string filePath = DefaultConfigFile)
    {
        if (string.IsNullOrWhiteSpace(environment))
            throw new ArgumentException("环境名称不能为空", nameof(environment));

        try
        {
            // 读取现有配置
            if (!File.Exists(filePath))
                return false;

            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);

            if (config == null)
                return false;

            // 查找环境对应的连接字符串
            var environmentConnectionName = $"{environment}Connection";
            var environmentConnection = config["ConnectionStrings"]?[environmentConnectionName]?.ToString();

            if (string.IsNullOrEmpty(environmentConnection))
                return false;

            // 更新默认连接字符串
            config["ConnectionStrings"]!["DefaultConnection"] = environmentConnection;

            // 更新当前环境
            if (config["Environment"] == null)
            {
                config["Environment"] = new JsonObject();
            }
            config["Environment"]!["Current"] = environment;

            // 写回文件
            var updatedJson = config.ToJsonString(new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            File.WriteAllText(filePath, updatedJson);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取当前环境名称
    /// </summary>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>当前环境名称，如果未找到则返回 null</returns>
    /// <example>
    /// <code>
    /// var currentEnv = YConfigHelper.GetCurrentEnvironment();
    /// Console.WriteLine($"当前环境: {currentEnv}");
    /// </code>
    /// </example>
    public static string? GetCurrentEnvironment(string filePath = DefaultConfigFile)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);

            return config?["Environment"]?["Current"]?.ToString();
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 获取所有可用环境
    /// </summary>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>可用环境列表</returns>
    /// <example>
    /// <code>
    /// var environments = YConfigHelper.GetAvailableEnvironments();
    /// foreach (var env in environments)
    /// {
    ///     Console.WriteLine($"可用环境: {env}");
    /// }
    /// </code>
    /// </example>
    public static List<string> GetAvailableEnvironments(string filePath = DefaultConfigFile)
    {
        try
        {
            if (!File.Exists(filePath))
                return new List<string>();

            var json = File.ReadAllText(filePath);
            var config = JsonNode.Parse(json);

            var environments = config?["Environment"]?["Available"]?.AsArray();
            if (environments == null)
                return new List<string>();

            return environments.Select(e => e?.ToString()).Where(e => !string.IsNullOrEmpty(e)).ToList()!;
        }
        catch
        {
            return new List<string>();
        }
    }

    /// <summary>
    /// 检查 SQLite 数据库文件是否存在
    /// </summary>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>数据库文件是否存在</returns>
    /// <example>
    /// <code>
    /// if (YConfigHelper.SQLiteDatabaseExists())
    /// {
    ///     Console.WriteLine("数据库文件存在");
    /// }
    /// else
    /// {
    ///     Console.WriteLine("数据库文件不存在，将自动创建");
    /// }
    /// </code>
    /// </example>
    public static bool SQLiteDatabaseExists(string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        try
        {
            var connectionString = GetConnectionString(connectionName, filePath);
            if (string.IsNullOrEmpty(connectionString))
                return false;

            // 从连接字符串中提取数据库文件路径
            var dbPath = ExtractSQLiteFilePath(connectionString);
            if (string.IsNullOrEmpty(dbPath))
                return false;

            return File.Exists(dbPath);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取 SQLite 数据库文件大小
    /// </summary>
    /// <param name="connectionName">连接字符串名称，默认为 DefaultConnection</param>
    /// <param name="filePath">配置文件路径，默认为 appsettings.json</param>
    /// <returns>数据库文件大小（字节），如果文件不存在返回 -1</returns>
    /// <example>
    /// <code>
    /// var size = YConfigHelper.GetSQLiteDatabaseSize();
    /// if (size > 0)
    /// {
    ///     Console.WriteLine($"数据库大小: {size / 1024 / 1024:F2} MB");
    /// }
    /// </code>
    /// </example>
    public static long GetSQLiteDatabaseSize(string connectionName = "DefaultConnection", string filePath = DefaultConfigFile)
    {
        try
        {
            var connectionString = GetConnectionString(connectionName, filePath);
            if (string.IsNullOrEmpty(connectionString))
                return -1;

            var dbPath = ExtractSQLiteFilePath(connectionString);
            if (string.IsNullOrEmpty(dbPath) || !File.Exists(dbPath))
                return -1;

            var fileInfo = new FileInfo(dbPath);
            return fileInfo.Length;
        }
        catch
        {
            return -1;
        }
    }

    /// <summary>
    /// 从 SQLite 连接字符串中提取文件路径
    /// </summary>
    /// <param name="connectionString">SQLite 连接字符串</param>
    /// <returns>数据库文件路径，如果提取失败返回 null</returns>
    private static string? ExtractSQLiteFilePath(string connectionString)
    {
        try
        {
            // 处理不同的 SQLite 连接字符串格式
            var patterns = new[] { "Data Source=", "DataSource=", "Database=" };

            foreach (var pattern in patterns)
            {
                var index = connectionString.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);
                if (index >= 0)
                {
                    var start = index + pattern.Length;
                    var end = connectionString.IndexOf(';', start);
                    if (end == -1) end = connectionString.Length;

                    var filePath = connectionString.Substring(start, end - start).Trim();

                    // 移除可能的引号
                    if (filePath.StartsWith("\"") && filePath.EndsWith("\""))
                    {
                        filePath = filePath.Substring(1, filePath.Length - 2);
                    }
                    if (filePath.StartsWith("'") && filePath.EndsWith("'"))
                    {
                        filePath = filePath.Substring(1, filePath.Length - 2);
                    }

                    return filePath;
                }
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    #endregion

    #region 私有辅助方法

    private static string BuildSQLiteConnectionString(object parameters)
    {
        // 使用反射获取参数对象的所有公共属性
        // 这样可以支持匿名对象和任何具有相应属性的对象
        var props = parameters.GetType().GetProperties();

        // 查找 FilePath 属性（忽略大小写）
        // 使用 FirstOrDefault 确保即使没有找到属性也不会抛出异常
        // 通过 ?. 操作符安全地调用 GetValue 和 ToString
        var filePath = props.FirstOrDefault(p => p.Name.Equals("FilePath", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();

        // 查找 Password 属性（可选，忽略大小写）
        var password = props.FirstOrDefault(p => p.Name.Equals("Password", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();

        // 验证必需参数：SQLite 必须指定文件路径
        if (string.IsNullOrEmpty(filePath))
            throw new ArgumentException("SQLite 需要 FilePath 参数");

        // 调用连接字符串辅助类构建 SQLite 连接字符串
        return YConnectionStringHelper.BuildSQLite(filePath, password);
    }

    private static string BuildSqlServerConnectionString(object parameters)
    {
        // 使用反射获取参数对象的所有公共属性
        var props = parameters.GetType().GetProperties();

        // 提取 SQL Server 连接所需的各个参数（忽略大小写）
        var server = props.FirstOrDefault(p => p.Name.Equals("Server", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var database = props.FirstOrDefault(p => p.Name.Equals("Database", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var userId = props.FirstOrDefault(p => p.Name.Equals("UserId", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var password = props.FirstOrDefault(p => p.Name.Equals("Password", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();

        // 验证必需参数：SQL Server 必须指定服务器地址和数据库名称
        if (string.IsNullOrEmpty(server) || string.IsNullOrEmpty(database))
            throw new ArgumentException("SQL Server 需要 Server 和 Database 参数");

        // 根据是否提供用户名来决定使用哪种认证方式
        if (string.IsNullOrEmpty(userId))
        {
            // 没有提供用户名：使用 Windows 集成认证（推荐用于内网环境）
            return YConnectionStringHelper.BuildSqlServerIntegrated(server, database);
        }
        else
        {
            // 提供了用户名：使用 SQL Server 认证（用户名密码方式）
            // 如果密码为空，使用空字符串作为默认值
            return YConnectionStringHelper.BuildSqlServer(server, database, userId, password ?? "");
        }
    }

    private static string BuildMySQLConnectionString(object parameters)
    {
        // 使用反射获取参数对象的所有公共属性
        var props = parameters.GetType().GetProperties();

        // 提取 MySQL 连接所需的各个参数（忽略大小写）
        var server = props.FirstOrDefault(p => p.Name.Equals("Server", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var database = props.FirstOrDefault(p => p.Name.Equals("Database", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var userId = props.FirstOrDefault(p => p.Name.Equals("UserId", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var password = props.FirstOrDefault(p => p.Name.Equals("Password", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();

        // 提取端口号参数（可选，默认为 3306）
        var port = props.FirstOrDefault(p => p.Name.Equals("Port", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters);

        // 验证必需参数：MySQL 必须指定服务器地址、数据库名称和用户名
        if (string.IsNullOrEmpty(server) || string.IsNullOrEmpty(database) || string.IsNullOrEmpty(userId))
            throw new ArgumentException("MySQL 需要 Server、Database 和 UserId 参数");

        // 处理端口号：如果未指定则使用 MySQL 默认端口 3306
        var portValue = port != null ? Convert.ToInt32(port) : 3306;

        // 调用连接字符串辅助类构建 MySQL 连接字符串
        return YConnectionStringHelper.BuildMySQL(server, database, userId, password ?? "", portValue);
    }

    private static string BuildPostgreSQLConnectionString(object parameters)
    {
        // 使用反射获取参数对象的所有公共属性
        var props = parameters.GetType().GetProperties();

        // 提取 PostgreSQL 连接所需的各个参数（忽略大小写）
        // 注意：PostgreSQL 使用 Host 而不是 Server，Username 而不是 UserId
        var host = props.FirstOrDefault(p => p.Name.Equals("Host", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var database = props.FirstOrDefault(p => p.Name.Equals("Database", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var username = props.FirstOrDefault(p => p.Name.Equals("Username", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();
        var password = props.FirstOrDefault(p => p.Name.Equals("Password", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters)?.ToString();

        // 提取端口号参数（可选，默认为 5432）
        var port = props.FirstOrDefault(p => p.Name.Equals("Port", StringComparison.OrdinalIgnoreCase))?.GetValue(parameters);

        // 验证必需参数：PostgreSQL 必须指定主机地址、数据库名称和用户名
        if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(database) || string.IsNullOrEmpty(username))
            throw new ArgumentException("PostgreSQL 需要 Host、Database 和 Username 参数");

        // 处理端口号：如果未指定则使用 PostgreSQL 默认端口 5432
        var portValue = port != null ? Convert.ToInt32(port) : 5432;

        // 调用连接字符串辅助类构建 PostgreSQL 连接字符串
        return YConnectionStringHelper.BuildPostgreSQL(host, database, username, password ?? "", portValue);
    }

    #endregion
}
