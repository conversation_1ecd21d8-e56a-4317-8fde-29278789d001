using System.Text;

namespace Zylo.YData.Helpers;

/// <summary>
/// 连接字符串构建辅助类
/// <para>提供各种数据库连接字符串的快速构建功能</para>
/// </summary>
/// <remarks>
/// <para>此类提供以下核心功能：</para>
/// <list type="bullet">
/// <item><strong>SQLite</strong> - 文件数据库和内存数据库连接字符串构建</item>
/// <item><strong>SQL Server</strong> - 集成认证和用户名密码认证连接字符串构建</item>
/// <item><strong>MySQL</strong> - 标准连接配置和自定义端口支持</item>
/// <item><strong>PostgreSQL</strong> - 标准连接配置和 SSL 模式支持</item>
/// <item><strong>Oracle</strong> - 标准连接配置和超时设置</item>
/// <item><strong>连接字符串修改</strong> - 动态修改数据库名称、服务器地址等</item>
/// </list>
/// <para>所有方法都包含参数验证和错误处理，确保生成的连接字符串格式正确。</para>
/// </remarks>
public static class YConnectionStringHelper
{
    // ==========================================
    // SQLite 连接字符串构建方法
    // ==========================================

    /// <summary>
    /// 构建 SQLite 文件数据库连接字符串
    /// </summary>
    /// <param name="filePath">数据库文件路径</param>
    /// <param name="password">密码（可选）</param>
    /// <param name="version">SQLite 版本，默认为 3</param>
    /// <param name="pooling">是否启用连接池，默认为 true</param>
    /// <returns>SQLite 连接字符串</returns>
    /// <remarks>
    /// 如果文件路径不存在，SQLite 会自动创建数据库文件
    /// </remarks>
    /// <example>
    /// <code>
    /// // 基础文件数据库
    /// var connStr = YConnectionStringHelper.BuildSQLite("myapp.db");
    /// 
    /// // 带密码的数据库
    /// var connStr = YConnectionStringHelper.BuildSQLite("secure.db", "mypassword");
    /// 
    /// // 指定完整路径
    /// var connStr = YConnectionStringHelper.BuildSQLite(@"C:\Data\myapp.db");
    /// </code>
    /// </example>
    public static string BuildSQLite(string filePath, string? password = null, int version = 3, bool pooling = true)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            throw new ArgumentException("文件路径不能为空", nameof(filePath));

        var builder = new StringBuilder();
        builder.Append($"Data Source={filePath};");
        builder.Append($"Version={version};");

        if (!string.IsNullOrEmpty(password))
        {
            builder.Append($"Password={password};");
        }

        if (pooling)
        {
            builder.Append("Pooling=true;");
        }

        return builder.ToString();
    }

    /// <summary>
    /// 构建 SQLite 内存数据库连接字符串
    /// </summary>
    /// <param name="shared">是否为共享内存数据库，默认为 false</param>
    /// <param name="cacheName">共享缓存名称（仅在 shared=true 时有效）</param>
    /// <returns>SQLite 内存数据库连接字符串</returns>
    /// <remarks>
    /// 内存数据库在应用程序关闭时会丢失所有数据，适合测试和临时存储
    /// </remarks>
    /// <example>
    /// <code>
    /// // 私有内存数据库（每个连接独立）
    /// var connStr = YConnectionStringHelper.BuildSQLiteMemory();
    /// 
    /// // 共享内存数据库（多个连接共享）
    /// var connStr = YConnectionStringHelper.BuildSQLiteMemory(shared: true, "MyCache");
    /// </code>
    /// </example>
    public static string BuildSQLiteMemory(bool shared = false, string? cacheName = null)
    {
        if (shared)
        {
            var name = string.IsNullOrEmpty(cacheName) ? "SharedCache" : cacheName;
            return $"Data Source=file:{name}?mode=memory&cache=shared;";
        }

        return "Data Source=:memory:;";
    }


    #region SQL Server 连接字符串

    /// <summary>
    /// 构建 SQL Server 连接字符串（集成认证）
    /// </summary>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="timeout">连接超时时间（秒），默认为 30</param>
    /// <param name="trustServerCertificate">是否信任服务器证书，默认为 true</param>
    /// <returns>SQL Server 连接字符串</returns>
    /// <example>
    /// <code>
    /// // 本地数据库
    /// var connStr = YConnectionStringHelper.BuildSqlServerIntegrated("localhost", "MyApp");
    /// 
    /// // 远程数据库
    /// var connStr = YConnectionStringHelper.BuildSqlServerIntegrated("*************", "MyApp");
    /// </code>
    /// </example>
    public static string BuildSqlServerIntegrated(string server, string database, int timeout = 30, bool trustServerCertificate = true)
    {
        if (string.IsNullOrWhiteSpace(server))
            throw new ArgumentException("服务器地址不能为空", nameof(server));

        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));

        return $"Server={server};Database={database};Integrated Security=true;Connection Timeout={timeout};TrustServerCertificate={trustServerCertificate};";
    }

    /// <summary>
    /// 构建 SQL Server 连接字符串（用户名密码认证）
    /// </summary>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="userId">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="timeout">连接超时时间（秒），默认为 30</param>
    /// <param name="trustServerCertificate">是否信任服务器证书，默认为 true</param>
    /// <returns>SQL Server 连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YConnectionStringHelper.BuildSqlServer("localhost", "MyApp", "sa", "password123");
    /// </code>
    /// </example>
    public static string BuildSqlServer(string server, string database, string userId, string password, int timeout = 30, bool trustServerCertificate = true)
    {
        if (string.IsNullOrWhiteSpace(server))
            throw new ArgumentException("服务器地址不能为空", nameof(server));

        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));

        if (string.IsNullOrWhiteSpace(userId))
            throw new ArgumentException("用户名不能为空", nameof(userId));

        return $"Server={server};Database={database};User Id={userId};Password={password};Connection Timeout={timeout};TrustServerCertificate={trustServerCertificate};";
    }

    #endregion

    #region MySQL 连接字符串

    /// <summary>
    /// 构建 MySQL 连接字符串
    /// </summary>
    /// <param name="server">服务器地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="userId">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="port">端口号，默认为 3306</param>
    /// <param name="charset">字符集，默认为 utf8mb4</param>
    /// <param name="sslMode">SSL 模式，默认为 None</param>
    /// <returns>MySQL 连接字符串</returns>
    /// <example>
    /// <code>
    /// // 基础连接
    /// var connStr = YConnectionStringHelper.BuildMySQL("localhost", "myapp", "root", "password");
    /// 
    /// // 自定义端口和字符集
    /// var connStr = YConnectionStringHelper.BuildMySQL("*************", "myapp", "user", "pass", 3307, "utf8");
    /// </code>
    /// </example>
    public static string BuildMySQL(string server, string database, string userId, string password,
        int port = 3306, string charset = "utf8mb4", string sslMode = "None")
    {
        if (string.IsNullOrWhiteSpace(server))
            throw new ArgumentException("服务器地址不能为空", nameof(server));

        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));

        if (string.IsNullOrWhiteSpace(userId))
            throw new ArgumentException("用户名不能为空", nameof(userId));

        return $"Server={server};Port={port};Database={database};Uid={userId};Pwd={password};Charset={charset};SslMode={sslMode};";
    }

    #endregion

    #region PostgreSQL 连接字符串

    /// <summary>
    /// 构建 PostgreSQL 连接字符串
    /// </summary>
    /// <param name="host">主机地址</param>
    /// <param name="database">数据库名称</param>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="port">端口号，默认为 5432</param>
    /// <param name="sslMode">SSL 模式，默认为 Prefer</param>
    /// <returns>PostgreSQL 连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YConnectionStringHelper.BuildPostgreSQL("localhost", "myapp", "postgres", "password");
    /// </code>
    /// </example>
    public static string BuildPostgreSQL(string host, string database, string username, string password,
        int port = 5432, string sslMode = "Prefer")
    {
        if (string.IsNullOrWhiteSpace(host))
            throw new ArgumentException("主机地址不能为空", nameof(host));

        if (string.IsNullOrWhiteSpace(database))
            throw new ArgumentException("数据库名称不能为空", nameof(database));

        if (string.IsNullOrWhiteSpace(username))
            throw new ArgumentException("用户名不能为空", nameof(username));

        return $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode={sslMode};";
    }

    #endregion

    #region Oracle 连接字符串

    /// <summary>
    /// 构建 Oracle 连接字符串
    /// </summary>
    /// <param name="dataSource">数据源（如：localhost:1521/XE）</param>
    /// <param name="userId">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="connectionTimeout">连接超时时间（秒），默认为 30</param>
    /// <returns>Oracle 连接字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = YConnectionStringHelper.BuildOracle("localhost:1521/XE", "hr", "password");
    /// </code>
    /// </example>
    public static string BuildOracle(string dataSource, string userId, string password, int connectionTimeout = 30)
    {
        if (string.IsNullOrWhiteSpace(dataSource))
            throw new ArgumentException("数据源不能为空", nameof(dataSource));

        if (string.IsNullOrWhiteSpace(userId))
            throw new ArgumentException("用户名不能为空", nameof(userId));

        return $"Data Source={dataSource};User Id={userId};Password={password};Connection Timeout={connectionTimeout};";
    }

    #endregion

    #region 连接字符串修改辅助方法

    /// <summary>
    /// 修改连接字符串中的数据库名称
    /// </summary>
    /// <param name="connectionString">原连接字符串</param>
    /// <param name="newDatabase">新数据库名称</param>
    /// <returns>修改后的连接字符串</returns>
    /// <example>
    /// <code>
    /// var originalConn = "Server=localhost;Database=OldDB;Integrated Security=true;";
    /// var newConn = YConnectionStringHelper.ChangeDatabase(originalConn, "NewDB");
    /// // 结果: "Server=localhost;Database=NewDB;Integrated Security=true;"
    /// </code>
    /// </example>
    public static string ChangeDatabase(string connectionString, string newDatabase)
    {
        // 参数验证：确保连接字符串不为空
        if (string.IsNullOrWhiteSpace(connectionString))
            throw new ArgumentException("连接字符串不能为空", nameof(connectionString));

        // 参数验证：确保新数据库名称不为空
        if (string.IsNullOrWhiteSpace(newDatabase))
            throw new ArgumentException("数据库名称不能为空", nameof(newDatabase));

        // 定义不同数据库系统使用的数据库名称关键字
        // Database= - SQL Server, MySQL 等常用格式
        // Initial Catalog= - SQL Server 的另一种格式
        // Data Source= - SQLite, Oracle 等使用的格式
        var patterns = new[] { "Database=", "Initial Catalog=", "Data Source=" };

        // 遍历所有可能的数据库名称关键字
        foreach (var pattern in patterns)
        {
            // 在连接字符串中查找当前关键字的位置（忽略大小写）
            var index = connectionString.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);

            // 如果找到了关键字
            if (index >= 0)
            {
                // 计算数据库名称的开始位置（关键字之后）
                var start = index + pattern.Length;

                // 查找数据库名称的结束位置（下一个分号的位置）
                var end = connectionString.IndexOf(';', start);

                // 如果没有找到分号，说明数据库名称在字符串末尾
                if (end == -1) end = connectionString.Length;

                // 重新构建连接字符串：
                // 1. 保留关键字之前的部分
                // 2. 添加新的数据库名称
                // 3. 保留原数据库名称之后的部分
                return connectionString.Substring(0, start) + newDatabase + connectionString.Substring(end);
            }
        }

        // 如果没有找到任何数据库关键字，则在连接字符串末尾添加 Database 参数
        // 检查连接字符串是否以分号结尾，如果不是则添加分号
        var separator = connectionString.EndsWith(";") ? "" : ";";
        return connectionString + separator + $"Database={newDatabase};";
    }

    /// <summary>
    /// 从连接字符串中提取数据库名称
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <returns>数据库名称，如果未找到则返回空字符串</returns>
    /// <example>
    /// <code>
    /// var connStr = "Server=localhost;Database=MyApp;Integrated Security=true;";
    /// var dbName = YConnectionStringHelper.ExtractDatabase(connStr);
    /// // 结果: "MyApp"
    /// </code>
    /// </example>
    public static string ExtractDatabase(string connectionString)
    {
        // 参数验证：如果连接字符串为空或只包含空白字符，直接返回空字符串
        if (string.IsNullOrWhiteSpace(connectionString))
            return string.Empty;

        // 定义不同数据库系统使用的数据库名称关键字
        // 按照常用程度排序，提高查找效率
        var patterns = new[] { "Database=", "Initial Catalog=", "Data Source=" };

        // 遍历所有可能的数据库名称关键字
        foreach (var pattern in patterns)
        {
            // 在连接字符串中查找当前关键字的位置（忽略大小写）
            var index = connectionString.IndexOf(pattern, StringComparison.OrdinalIgnoreCase);

            // 如果找到了关键字
            if (index >= 0)
            {
                // 计算数据库名称的开始位置（关键字之后）
                var start = index + pattern.Length;

                // 查找数据库名称的结束位置（下一个分号的位置）
                var end = connectionString.IndexOf(';', start);

                // 如果没有找到分号，说明数据库名称在字符串末尾
                if (end == -1) end = connectionString.Length;

                // 提取数据库名称并去除首尾空白字符
                // 使用 Substring(start, length) 格式提取指定长度的子字符串
                return connectionString.Substring(start, end - start).Trim();
            }
        }

        // 如果没有找到任何数据库关键字，返回空字符串
        return string.Empty;
    }

    #endregion
}
