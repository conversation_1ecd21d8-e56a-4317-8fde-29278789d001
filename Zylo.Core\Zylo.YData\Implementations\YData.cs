namespace Zylo.YData;

/// <summary>
/// YData 静态访问类 - 提供简化的数据访问接口
/// <para>基于 FreeSql 的现代化数据访问层，支持多数据库、依赖注入、智能扩展等功能</para>
/// </summary>
/// <remarks>
/// 使用示例：
/// <code>
/// // 1. 配置数据库
/// YData.ConfigureAuto("Data Source=test.db", YDataType.Sqlite);
///
/// // 2. 基础操作
/// var user = new User { Name = "张三", Age = 25 };
/// await YData.InsertAsync(user);
///
/// var users = await YData.Select&lt;User&gt;().Where(u => u.Age > 18).ToListAsync();
/// </code>
/// </remarks>
public static class YData
{
    #region 私有字段和属性

    /// <summary>
    /// 数据库上下文实例
    /// </summary>
    private static IYDataContext? _context;

    /// <summary>
    /// 多数据库管理器实例
    /// </summary>
    private static IYDataManager? _manager;

    /// <summary>
    /// 线程安全锁对象
    /// </summary>
    private static readonly object _lock = new();

    #endregion

    #region 配置方法

    /// <summary>
    /// 一键配置（零参数 - 自动从配置文件读取）
    /// </summary>
    /// <remarks>
    /// 自动从 appsettings.json 中读取 "DefaultConnection" 连接字符串
    /// </remarks>
    /// <exception cref="InvalidOperationException">未找到连接字符串时抛出</exception>
    public static void ConfigureAuto()
    {
        var configuration = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true)
            .AddJsonFile("appsettings.Development.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("No connection string found in configuration. Please add 'DefaultConnection' to your appsettings.json or call ConfigureAuto(connectionString) directly.");
        }

        ConfigureAuto(connectionString);
    }

    /// <summary>
    /// 一键配置（单参数 - 连接字符串）
    /// </summary>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <remarks>
    /// 自动检测数据库类型，支持 SQLite、MySQL、PostgreSQL、SQL Server
    /// </remarks>
    public static void ConfigureAuto(string connectionString)
    {
        var dataType = DetectDataTypeFromConnectionString(connectionString);
        ConfigureAuto(connectionString, dataType);
    }

    /// <summary>
    /// 一键配置（双参数 - 连接字符串 + 数据库类型）
    /// </summary>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <param name="dataType">数据库类型</param>
    /// <remarks>
    /// 推荐使用此方法，明确指定数据库类型避免自动检测错误
    /// </remarks>
    public static void ConfigureAuto(string connectionString, YDataType dataType)
    {
        lock (_lock)
        {
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddYDataAuto(connectionString, dataType);

            var provider = services.BuildServiceProvider();
            _context = provider.GetRequiredService<IYDataContext>();
        }
    }

    /// <summary>
    /// 高级配置
    /// </summary>
    /// <param name="configure">配置委托，用于详细配置数据库选项</param>
    /// <remarks>
    /// 适用于需要详细配置的场景，如连接池大小、超时时间等
    /// </remarks>
    public static void Configure(Action<YDataOptions> configure)
    {
        lock (_lock)
        {
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddYData(configure);

            var provider = services.BuildServiceProvider();
            _context = provider.GetRequiredService<IYDataContext>();
        }
    }

    #endregion

    #region 多数据库管理器访问

    /// <summary>
    /// 多数据库管理器
    /// </summary>
    /// <remarks>
    /// 提供多数据库的注册、管理和切换功能
    /// </remarks>
    /// <example>
    /// <code>
    /// // 注册数据库
    /// YData.Manager.RegisterDatabase("main", "Data Source=main.db", YDataType.Sqlite);
    ///
    /// // 获取数据库
    /// var mainDb = YData.Manager.GetDatabase("main");
    ///
    /// // 设置默认数据库
    /// YData.Manager.SetDefaultDatabase("main");
    /// </code>
    /// </example>
    public static IYDataManager Manager
    {
        get
        {
            EnsureManagerInitialized();
            return _manager!;
        }
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 确保多数据库管理器已初始化
    /// </summary>
    /// <remarks>
    /// 使用双重检查锁定模式确保线程安全的单例初始化
    /// </remarks>
    private static void EnsureManagerInitialized()
    {
        if (_manager == null)
        {
            lock (_lock)
            {
                _manager ??= new YDataManager();
            }
        }
    }

    /// <summary>
    /// 确保数据库上下文已初始化
    /// </summary>
    /// <exception cref="InvalidOperationException">未配置数据库时抛出</exception>
    /// <remarks>
    /// 如果未配置，会尝试自动从配置文件读取，失败则抛出异常
    /// </remarks>
    private static void EnsureInitialized()
    {
        if (_context == null)
        {
            try
            {
                ConfigureAuto(); // 尝试自动配置
            }
            catch
            {
                // 自动配置失败，抛出更明确的错误信息
            }
        }

        if (_context == null)
        {
            throw new InvalidOperationException("YData not configured. Please call YData.ConfigureAuto() or YData.Configure() first.");
        }
    }

    /// <summary>
    /// 自动检测数据库类型
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <returns>检测到的数据库类型</returns>
    /// <remarks>
    /// 根据连接字符串的关键字自动判断数据库类型：
    /// <list type="bullet">
    /// <item>包含 ".db"、".sqlite" 或 ":memory:" → SQLite</item>
    /// <item>包含 "host=" 和 "port=" → PostgreSQL</item>
    /// <item>包含 "server=" 和 "uid=" → MySQL</item>
    /// <item>其他情况 → SQL Server</item>
    /// </list>
    /// </remarks>
    private static YDataType DetectDataTypeFromConnectionString(string connectionString)
    {
        if (string.IsNullOrEmpty(connectionString))
            return YDataType.SqlServer;

        var lower = connectionString.ToLower();

        // SQLite 检测
        if (lower.Contains("data source=") && (lower.Contains(".db") || lower.Contains(".sqlite") || lower.Contains(":memory:")))
            return YDataType.Sqlite;

        // PostgreSQL 检测
        if (lower.Contains("host=") || lower.Contains("server=") && lower.Contains("port=") && lower.Contains("database="))
            return YDataType.PostgreSQL;

        // MySQL 检测
        if (lower.Contains("server=") && lower.Contains("database=") && (lower.Contains("uid=") || lower.Contains("user id=")))
            return YDataType.MySql;

        // SQL Server 检测
        if (lower.Contains("server=") || lower.Contains("data source=") || lower.Contains("initial catalog="))
            return YDataType.SqlServer;

        return YDataType.SqlServer;
    }

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取 FreeSql 实例
    /// </summary>
    /// <remarks>
    /// 提供对底层 FreeSql 实例的直接访问，用于高级操作
    /// </remarks>
    public static IFreeSql FreeSql
    {
        get
        {
            EnsureInitialized();
            return _context!.FreeSql;
        }
    }

    #endregion

    #region 基础 CRUD 操作接口

    /// <summary>
    /// 创建查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>查询接口，支持链式调用</returns>
    /// <remarks>
    /// 返回 FreeSql 的 ISelect 接口，支持复杂查询操作
    /// </remarks>
    public static ISelect<T> Select<T>() where T : class
    {
        EnsureInitialized();
        return _context!.Select<T>();
    }

    /// <summary>
    /// 创建插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>插入接口，支持批量插入</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IInsert 接口，支持批量插入和高级配置
    /// </remarks>
    public static IInsert<T> Insert<T>() where T : class
    {
        EnsureInitialized();
        return _context!.Insert<T>();
    }

    /// <summary>
    /// 创建更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>更新接口，支持条件更新</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IUpdate 接口，支持条件更新和批量更新
    /// </remarks>
    public static IUpdate<T> Update<T>() where T : class
    {
        EnsureInitialized();
        return _context!.Update<T>();
    }

    /// <summary>
    /// 创建删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>删除接口，支持条件删除</returns>
    /// <remarks>
    /// 返回 FreeSql 的 IDelete 接口，支持条件删除和批量删除
    /// </remarks>
    public static IDelete<T> Delete<T>() where T : class
    {
        EnsureInitialized();
        return _context!.Delete<T>();
    }

    #endregion

    #region 简化的 CRUD 方法

    /// <summary>
    /// 根据主键获取实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>实体对象，不存在时返回 null</returns>
    /// <remarks>
    /// 这是一个便捷方法，等同于 Select&lt;T&gt;().Where(主键 == id).First()
    /// </remarks>
    public static async Task<T?> GetAsync<T>(object id) where T : class
    {
        EnsureInitialized();
        return await _context!.GetAsync<T>(id);
    }

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>实体列表</returns>
    /// <remarks>
    /// 注意：此方法会加载表中所有数据，大表慎用
    /// </remarks>
    public static async Task<List<T>> GetAllAsync<T>() where T : class
    {
        EnsureInitialized();
        return await _context!.GetAllAsync<T>();
    }

    /// <summary>
    /// 插入单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 自动处理自增主键，插入后实体的主键会被更新
    /// </remarks>
    public static async Task<int> InsertAsync<T>(T entity) where T : class
    {
        EnsureInitialized();
        return await _context!.InsertAsync(entity);
    }

    /// <summary>
    /// 更新单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 根据主键更新实体，只更新非空字段
    /// </remarks>
    public static async Task<int> UpdateAsync<T>(T entity) where T : class
    {
        EnsureInitialized();
        return await _context!.UpdateAsync(entity);
    }

    /// <summary>
    /// 根据主键删除实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 物理删除，数据将永久丢失
    /// </remarks>
    public static async Task<int> DeleteAsync<T>(object id) where T : class
    {
        EnsureInitialized();
        return await _context!.DeleteAsync<T>(id);
    }

    #endregion

    #region 高级查询和事务方法

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="pageIndex">页索引（从1开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <param name="where">查询条件（可选）</param>
    /// <returns>分页结果，包含数据和分页信息</returns>
    /// <remarks>
    /// 自动计算总记录数和总页数，适用于需要分页显示的场景
    /// </remarks>
    public static async Task<PagedResult<T>> GetPagedAsync<T>(int pageIndex, int pageSize, Expression<Func<T, bool>>? where = null) where T : class
    {
        EnsureInitialized();
        return await _context!.GetPagedAsync(pageIndex, pageSize, where);
    }

    /// <summary>
    /// 事务操作（有返回值）
    /// </summary>
    /// <typeparam name="TResult">返回值类型</typeparam>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <returns>操作结果</returns>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚
    /// </remarks>
    public static async Task<TResult> TransactionAsync<TResult>(Func<Task<TResult>> operation, IsolationLevel? isolationLevel = null)
    {
        EnsureInitialized();
        return await _context!.TransactionAsync(operation, isolationLevel);
    }

    /// <summary>
    /// 事务操作（无返回值）
    /// </summary>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚
    /// </remarks>
    public static async Task TransactionAsync(Func<Task> operation, IsolationLevel? isolationLevel = null)
    {
        EnsureInitialized();
        await _context!.TransactionAsync(operation, isolationLevel);
    }

    #endregion



    #region 多数据库支持

    /// <summary>
    /// 注册数据库
    /// </summary>
    /// <param name="name">数据库名称，用于后续引用</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <param name="dataType">数据库类型（可选，不指定时自动检测）</param>
    /// <remarks>
    /// 注册后可通过 YDataSwitcher.WithDatabase() 或指定数据库名称的方法来使用
    /// </remarks>
    /// <example>
    /// <code>
    /// // 注册 SQLite 数据库
    /// YData.RegisterDatabase("users", "Data Source=users.db", YDataType.Sqlite);
    ///
    /// // 注册 MySQL 数据库
    /// YData.RegisterDatabase("orders", "Server=localhost;Database=orders;Uid=root;Pwd=******;", YDataType.MySql);
    /// </code>
    /// </example>
    public static void RegisterDatabase(string name, string connectionString, YDataType? dataType = null)
    {
        EnsureManagerInitialized();
        var detectedType = dataType ?? DetectDataTypeFromConnectionString(connectionString);
        _manager!.RegisterDatabase(name, connectionString, detectedType);
    }

    /// <summary>
    /// 注册数据库（使用详细配置）
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <param name="options">详细配置选项</param>
    /// <remarks>
    /// 适用于需要详细配置连接池、超时等参数的场景
    /// </remarks>
    public static void RegisterDatabase(string name, YDataOptions options)
    {
        EnsureManagerInitialized();
        _manager!.RegisterDatabase(name, options);
    }

    /// <summary>
    /// 快速注册多个 SQLite 数据库
    /// </summary>
    /// <param name="databases">数据库信息数组，包含名称和文件路径</param>
    /// <remarks>
    /// 便捷方法，专门用于批量注册 SQLite 数据库文件
    /// </remarks>
    /// <example>
    /// <code>
    /// YData.RegisterSqliteDatabases(
    ///     ("users", "users.db"),
    ///     ("orders", "orders.db"),
    ///     ("logs", "logs.db")
    /// );
    /// </code>
    /// </example>
    public static void RegisterSqliteDatabases(params (string name, string filePath)[] databases)
    {
        foreach (var (name, filePath) in databases)
        {
            var connectionString = $"Data Source={filePath}";
            RegisterDatabase(name, connectionString, YDataType.Sqlite);
        }
    }

    /// <summary>
    /// 批量注册数据库
    /// </summary>
    /// <param name="databases">数据库字典，键为名称，值为连接信息</param>
    /// <remarks>
    /// 适用于从配置文件或其他数据源批量注册数据库的场景
    /// </remarks>
    public static void RegisterDatabases(Dictionary<string, (string connectionString, YDataType dataType)> databases)
    {
        foreach (var kvp in databases)
        {
            RegisterDatabase(kvp.Key, kvp.Value.connectionString, kvp.Value.dataType);
        }
    }

    /// <summary>
    /// 设置默认数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <remarks>
    /// 设置后，所有不指定数据库的操作都会使用此数据库
    /// </remarks>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    public static void UseDatabase(string name)
    {
        EnsureManagerInitialized();
        _manager!.SetDefaultDatabase(name);

        // 更新当前上下文
        _context = _manager.GetDefaultDatabase();
    }

    /// <summary>
    /// 获取指定数据库的上下文
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库上下文</returns>
    /// <remarks>
    /// 用于直接操作指定数据库，不影响默认数据库设置
    /// </remarks>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    public static IYDataContext GetDatabase(string name)
    {
        EnsureManagerInitialized();
        return _manager!.GetDatabase(name);
    }

    /// <summary>
    /// 检查数据库是否已注册
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>是否已注册</returns>
    /// <remarks>
    /// 只检查是否在 YData 中注册，不检查实际数据库文件是否存在
    /// </remarks>
    public static bool DatabaseExists(string name)
    {
        EnsureManagerInitialized();
        return _manager!.DatabaseExists(name);
    }

    /// <summary>
    /// 获取所有已注册的数据库名称
    /// </summary>
    /// <returns>数据库名称列表</returns>
    /// <remarks>
    /// 返回所有通过 RegisterDatabase 注册的数据库名称
    /// </remarks>
    public static IEnumerable<string> GetDatabaseNames()
    {
        EnsureManagerInitialized();
        return _manager!.GetDatabaseNames();
    }

    /// <summary>
    /// 获取当前默认数据库名称
    /// </summary>
    /// <returns>默认数据库名称，未设置时返回空字符串</returns>
    /// <remarks>
    /// 返回通过 UseDatabase() 设置的默认数据库名称
    /// </remarks>
    public static string GetCurrentDatabaseName()
    {
        EnsureManagerInitialized();
        var allInfos = _manager!.GetAllDatabaseInfo();
        return allInfos.FirstOrDefault(info => info.IsDefault)?.Name ?? string.Empty;
    }

    /// <summary>
    /// 移除已注册的数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <remarks>
    /// 只是从注册列表中移除，不会删除实际的数据库文件
    /// </remarks>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    public static void RemoveDatabase(string name)
    {
        EnsureManagerInitialized();
        _manager!.RemoveDatabase(name);
    }

    /// <summary>
    /// 获取指定数据库的详细信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库信息，不存在时返回 null</returns>
    /// <remarks>
    /// 包含连接字符串、数据库类型、是否为默认数据库等信息
    /// </remarks>
    public static YDatabaseInfo? GetDatabaseInfo(string name)
    {
        EnsureManagerInitialized();
        return _manager!.GetDatabaseInfo(name);
    }

    /// <summary>
    /// 获取所有已注册数据库的详细信息
    /// </summary>
    /// <returns>数据库信息列表</returns>
    /// <remarks>
    /// 用于管理界面显示或配置导出等场景
    /// </remarks>
    public static IEnumerable<YDatabaseInfo> GetAllDatabaseInfo()
    {
        EnsureManagerInitialized();
        return _manager!.GetAllDatabaseInfo();
    }

    /// <summary>
    /// 测试指定数据库的连接
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>连接是否成功</returns>
    /// <remarks>
    /// 执行简单的连接测试，不会影响现有连接
    /// </remarks>
    public static async Task<bool> TestConnectionAsync(string name)
    {
        EnsureManagerInitialized();
        return await _manager!.TestConnectionAsync(name);
    }

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>统计信息，包含表数量、记录数等</returns>
    /// <remarks>
    /// 用于监控和管理数据库，获取表数量、记录数等统计信息
    /// </remarks>
    public static async Task<YDatabaseStats> GetDatabaseStatsAsync(string name)
    {
        EnsureManagerInitialized();
        return await _manager!.GetDatabaseStatsAsync(name);
    }

    #endregion

    #region 指定数据库的操作方法

    /// <summary>
    /// 在指定数据库中创建查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>查询接口</returns>
    /// <remarks>
    /// 用于在特定数据库中执行查询操作，不影响默认数据库设置
    /// </remarks>
    /// <example>
    /// <code>
    /// var users = await YData.Select&lt;User&gt;("users_db").Where(u => u.Age > 18).ToListAsync();
    /// </code>
    /// </example>
    public static ISelect<T> Select<T>(string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return database.Select<T>();
    }

    /// <summary>
    /// 在指定数据库中创建插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>插入接口</returns>
    /// <remarks>
    /// 用于在特定数据库中执行插入操作
    /// </remarks>
    public static IInsert<T> Insert<T>(string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return database.Insert<T>();
    }

    /// <summary>
    /// 在指定数据库中创建更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>更新接口</returns>
    /// <remarks>
    /// 用于在特定数据库中执行更新操作
    /// </remarks>
    public static IUpdate<T> Update<T>(string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return database.Update<T>();
    }

    /// <summary>
    /// 在指定数据库中创建删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>删除接口</returns>
    /// <remarks>
    /// 用于在特定数据库中执行删除操作
    /// </remarks>
    public static IDelete<T> Delete<T>(string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return database.Delete<T>();
    }

    /// <summary>
    /// 在指定数据库中插入单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 便捷方法，等同于 Insert&lt;T&gt;(databaseName).AppendData(entity).ExecuteAffrowsAsync()
    /// </remarks>
    public static async Task<int> InsertAsync<T>(T entity, string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return await database.InsertAsync(entity);
    }

    /// <summary>
    /// 在指定数据库中更新单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 根据主键更新实体，便捷方法
    /// </remarks>
    public static async Task<int> UpdateAsync<T>(T entity, string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return await database.UpdateAsync(entity);
    }

    /// <summary>
    /// 在指定数据库中根据主键删除实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <param name="databaseName">数据库名称</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 便捷方法，等同于 Delete&lt;T&gt;(databaseName).Where(主键 == id).ExecuteAffrowsAsync()
    /// </remarks>
    public static async Task<int> DeleteAsync<T>(object id, string databaseName) where T : class
    {
        var database = GetDatabase(databaseName);
        return await database.DeleteAsync<T>(id);
    }

    #endregion
}
