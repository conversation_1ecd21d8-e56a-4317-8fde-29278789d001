namespace Zylo.YData;

/// <summary>
/// YData 核心数据访问实现
/// <para>提供对 FreeSql 的封装，增加日志记录、错误处理和配置管理功能</para>
/// </summary>
/// <remarks>
/// 此类是 YData 框架的核心实现，负责：
/// <list type="bullet">
/// <item>封装 FreeSql 的基础 CRUD 操作</item>
/// <item>提供统一的日志记录</item>
/// <item>处理异常和错误</item>
/// <item>支持分页查询和事务操作</item>
/// <item>管理配置选项</item>
/// </list>
/// </remarks>
public class YDataContext : IYDataContext
{
    #region 私有字段

    /// <summary>
    /// FreeSql 实例，提供底层数据访问功能
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 日志记录器，用于记录操作日志和错误信息
    /// </summary>
    private readonly ILogger<YDataContext>? _logger;

    /// <summary>
    /// 配置选项，包含超时设置等配置参数
    /// </summary>
    private readonly YDataOptions _options;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化 YDataContext 实例
    /// </summary>
    /// <param name="freeSql">FreeSql 实例，不能为 null</param>
    /// <param name="logger">日志记录器（可选）</param>
    /// <param name="options">配置选项（可选，使用默认配置）</param>
    /// <exception cref="ArgumentNullException">当 freeSql 为 null 时抛出</exception>
    /// <remarks>
    /// 如果未提供 logger，将不记录日志；如果未提供 options，将使用默认配置
    /// </remarks>
    public YDataContext(IFreeSql freeSql, ILogger<YDataContext>? logger = null, IOptions<YDataOptions>? options = null)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _logger = logger;
        _options = options?.Value ?? new YDataOptions();
    }

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取底层 FreeSql 实例
    /// </summary>
    /// <remarks>
    /// 提供对 FreeSql 原生功能的直接访问，用于高级操作和扩展
    /// </remarks>
    public IFreeSql FreeSql => _freeSql;

    #endregion

    #region 基础 CRUD 操作接口

    /// <summary>
    /// 创建查询接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>查询接口，支持链式调用</returns>
    /// <remarks>
    /// 自动应用默认查询超时设置，支持复杂查询操作
    /// </remarks>
    public ISelect<T> Select<T>() where T : class
    {
        var query = _freeSql.Select<T>();

        // 设置默认超时
        if (_options.DefaultQueryTimeout > TimeSpan.Zero)
        {
            query = query.CommandTimeout((int)_options.DefaultQueryTimeout.TotalSeconds);
        }

        return query;
    }

    /// <summary>
    /// 创建插入接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>插入接口，支持批量插入和高级配置</returns>
    /// <remarks>
    /// 返回 FreeSql 的原生插入接口，支持批量操作和性能优化
    /// </remarks>
    public IInsert<T> Insert<T>() where T : class => _freeSql.Insert<T>();

    /// <summary>
    /// 创建更新接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>更新接口，支持条件更新和批量更新</returns>
    /// <remarks>
    /// 返回 FreeSql 的原生更新接口，支持复杂的更新条件
    /// </remarks>
    public IUpdate<T> Update<T>() where T : class => _freeSql.Update<T>();

    /// <summary>
    /// 创建删除接口
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>删除接口，支持条件删除和批量删除</returns>
    /// <remarks>
    /// 返回 FreeSql 的原生删除接口，支持复杂的删除条件
    /// </remarks>
    public IDelete<T> Delete<T>() where T : class => _freeSql.Delete<T>();

    #endregion

    #region 简化的 CRUD 方法

    /// <summary>
    /// 根据主键获取实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>实体对象，不存在时返回 null</returns>
    /// <remarks>
    /// 这是一个便捷方法，自动处理日志记录和异常处理
    /// </remarks>
    public async Task<T?> GetAsync<T>(object id) where T : class
    {
        try
        {
            _logger?.LogDebug("Getting entity {EntityType} with id {Id}", typeof(T).Name, id);

            var result = await Select<T>().WhereDynamic(id).FirstAsync();

            _logger?.LogDebug("Successfully retrieved entity {EntityType} with id {Id}", typeof(T).Name, id);
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting entity {EntityType} with id {Id}", typeof(T).Name, id);
            throw;
        }
    }

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <returns>实体列表</returns>
    /// <remarks>
    /// 注意：此方法会加载表中所有数据，大表慎用。建议使用分页查询
    /// </remarks>
    public async Task<List<T>> GetAllAsync<T>() where T : class
    {
        try
        {
            _logger?.LogDebug("Getting all entities of type {EntityType}", typeof(T).Name);

            var result = await Select<T>().ToListAsync();

            _logger?.LogDebug("Successfully retrieved {Count} entities of type {EntityType}", result.Count, typeof(T).Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting all entities of type {EntityType}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// 插入单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要插入的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 自动处理自增主键，插入后实体的主键会被更新。包含完整的日志记录和异常处理
    /// </remarks>
    public async Task<int> InsertAsync<T>(T entity) where T : class
    {
        try
        {
            _logger?.LogDebug("Inserting entity {EntityType}", typeof(T).Name);

            // 使用 Task.Run 来异步执行同步的 ExecuteIdentity 方法获取自增ID
            var insertedId = await Task.Run(() => Insert<T>().AppendData(entity).ExecuteIdentity());

            // 如果有自增ID，将其设置到实体的主键属性上
            if (insertedId > 0)
            {
                // 查找标记为主键且自增的属性
                var entityType = typeof(T);
                var properties = entityType.GetProperties();

                foreach (var property in properties)
                {
                    // 查找具有 Column 特性且 IsIdentity = true 的属性
                    var columnAttr = property.GetCustomAttributes(typeof(ColumnAttribute), false)
                        .Cast<ColumnAttribute>()
                        .FirstOrDefault();

                    if (columnAttr != null && columnAttr.IsIdentity && property.CanWrite)
                    {
                        // 将自增ID设置到属性上，需要转换类型
                        var convertedId = Convert.ChangeType(insertedId, property.PropertyType);
                        property.SetValue(entity, convertedId);
                        break;
                    }
                }

                _logger?.LogDebug("Successfully inserted entity {EntityType}, identity: {Identity}", typeof(T).Name, insertedId);
                return 1;
            }

            _logger?.LogWarning("Insert operation completed but no identity was returned for {EntityType}", typeof(T).Name);
            return 0;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error inserting entity {EntityType}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// 更新单个实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="entity">要更新的实体</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 根据主键更新实体，只更新非空字段。包含完整的日志记录和异常处理
    /// </remarks>
    public async Task<int> UpdateAsync<T>(T entity) where T : class
    {
        try
        {
            _logger?.LogDebug("Updating entity {EntityType}", typeof(T).Name);

            var result = await Update<T>().SetSource(entity).ExecuteAffrowsAsync();

            _logger?.LogDebug("Successfully updated entity {EntityType}, affected rows: {AffectedRows}", typeof(T).Name, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error updating entity {EntityType}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// 根据主键删除实体
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="id">主键值</param>
    /// <returns>影响的行数</returns>
    /// <remarks>
    /// 物理删除，数据将永久丢失。包含完整的日志记录和异常处理
    /// </remarks>
    public async Task<int> DeleteAsync<T>(object id) where T : class
    {
        try
        {
            _logger?.LogDebug("Deleting entity {EntityType} with id {Id}", typeof(T).Name, id);

            var result = await Delete<T>().WhereDynamic(id).ExecuteAffrowsAsync();

            _logger?.LogDebug("Successfully deleted entity {EntityType} with id {Id}, affected rows: {AffectedRows}", typeof(T).Name, id, result);
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error deleting entity {EntityType} with id {Id}", typeof(T).Name, id);
            throw;
        }
    }

    #endregion

    #region 高级查询和事务方法

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="pageIndex">页索引（从1开始）</param>
    /// <param name="pageSize">每页记录数</param>
    /// <param name="where">查询条件（可选）</param>
    /// <returns>分页结果，包含数据和分页信息</returns>
    /// <remarks>
    /// 自动计算总记录数和总页数，适用于需要分页显示的场景。包含完整的日志记录和异常处理
    /// </remarks>
    public async Task<PagedResult<T>> GetPagedAsync<T>(int pageIndex, int pageSize, Expression<Func<T, bool>>? where = null) where T : class
    {
        try
        {
            _logger?.LogDebug("Getting paged entities {EntityType}, page {PageIndex}, size {PageSize}", typeof(T).Name, pageIndex, pageSize);

            var query = Select<T>();
            if (where != null)
            {
                query = query.Where(where);
            }

            var total = await query.CountAsync();
            var items = await query.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToListAsync();

            var result = new PagedResult<T>
            {
                Items = items,
                TotalCount = (int)total, // 转换 long 到 int
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((int)total / (double)pageSize)
            };

            _logger?.LogDebug("Successfully retrieved paged entities {EntityType}, total: {Total}, returned: {Count}", typeof(T).Name, total, items.Count);
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error getting paged entities {EntityType}", typeof(T).Name);
            throw;
        }
    }

    /// <summary>
    /// 事务操作（有返回值）
    /// </summary>
    /// <typeparam name="TResult">返回值类型</typeparam>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <returns>操作结果</returns>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚。包含完整的日志记录
    /// </remarks>
    public async Task<TResult> TransactionAsync<TResult>(Func<Task<TResult>> operation, IsolationLevel? isolationLevel = null)
    {
        try
        {
            _logger?.LogDebug("Starting transaction with isolation level {IsolationLevel}", isolationLevel);

            // 简化的事务实现，暂时不使用 UnitOfWork
            var result = await operation();

            _logger?.LogDebug("Transaction completed successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in transaction");
            throw;
        }
    }

    /// <summary>
    /// 事务操作（无返回值）
    /// </summary>
    /// <param name="operation">事务内的操作</param>
    /// <param name="isolationLevel">事务隔离级别（可选）</param>
    /// <remarks>
    /// 自动管理事务的开始、提交和回滚，发生异常时自动回滚
    /// </remarks>
    public async Task TransactionAsync(Func<Task> operation, IsolationLevel? isolationLevel = null)
    {
        await TransactionAsync(async () =>
        {
            await operation();
            return true;
        }, isolationLevel);
    }

    #endregion

    #region 资源管理

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <remarks>
    /// 释放底层 FreeSql 实例占用的资源，包括数据库连接等
    /// </remarks>
    public void Dispose()
    {
        _freeSql?.Dispose();
        GC.SuppressFinalize(this);
    }

    #endregion
}
