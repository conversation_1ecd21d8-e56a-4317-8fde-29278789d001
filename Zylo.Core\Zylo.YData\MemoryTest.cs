using Zylo.YData;
using Zylo.YData.Examples;

/// <summary>
/// 内存使用测试 - 验证连接是否正确释放
/// </summary>
public class MemoryTest
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("🧪 Zylo.YData 内存使用测试");
        Console.WriteLine("========================");

        // 配置数据库
        YData.ConfigureAuto("Data Source=:memory:");
        
        // 创建表结构
        YData.FreeSql.CodeFirst.SyncStructure<User>();

        // 测试前的内存使用
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        var initialMemory = GC.GetTotalMemory(false);
        Console.WriteLine($"📊 初始内存: {initialMemory / 1024 / 1024:F2} MB");

        // 执行大量数据库操作
        Console.WriteLine("\n🔄 执行 1000 次数据库操作...");
        for (int i = 0; i < 1000; i++)
        {
            // 插入操作
            var user = new User 
            { 
                Name = $"User_{i}", 
                Email = $"user{i}@test.com", 
                Age = 20 + (i % 50),
                IsActive = true 
            };
            await YData.InsertAsync(user);

            // 查询操作
            var users = await YData.Select<User>()
                .Where(u => u.Age > 25)
                .Take(10)
                .ToListAsync();

            // 更新操作
            if (users.Any())
            {
                var firstUser = users.First();
                firstUser.Age += 1;
                await YData.UpdateAsync(firstUser);
            }

            // 每100次操作检查一次内存
            if ((i + 1) % 100 == 0)
            {
                var currentMemory = GC.GetTotalMemory(false);
                Console.WriteLine($"   第 {i + 1} 次操作后内存: {currentMemory / 1024 / 1024:F2} MB");
            }
        }

        // 强制垃圾回收
        Console.WriteLine("\n🧹 强制垃圾回收...");
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        Console.WriteLine($"\n📈 内存使用报告:");
        Console.WriteLine($"   初始内存: {initialMemory / 1024 / 1024:F2} MB");
        Console.WriteLine($"   最终内存: {finalMemory / 1024 / 1024:F2} MB");
        Console.WriteLine($"   内存增长: {memoryIncrease / 1024 / 1024:F2} MB");

        // 验证连接池状态
        Console.WriteLine($"\n🔗 连接池信息:");
        try
        {
            // 执行简单查询测试连接
            var count = await YData.Select<User>().CountAsync();
            Console.WriteLine($"   数据库连接正常，用户总数: {count}");
            Console.WriteLine($"   ✅ 连接池工作正常");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 连接异常: {ex.Message}");
        }

        // 测试多次连接操作
        Console.WriteLine($"\n🔄 测试连接复用...");
        var startTime = DateTime.Now;
        
        for (int i = 0; i < 100; i++)
        {
            await YData.Select<User>().Take(1).ToListAsync();
        }
        
        var endTime = DateTime.Now;
        var duration = endTime - startTime;
        
        Console.WriteLine($"   100次查询耗时: {duration.TotalMilliseconds:F2} ms");
        Console.WriteLine($"   平均每次: {duration.TotalMilliseconds / 100:F2} ms");
        Console.WriteLine($"   ✅ 连接复用效率良好");

        Console.WriteLine($"\n========================");
        Console.WriteLine($"🎉 测试完成！");
        
        if (memoryIncrease < 10 * 1024 * 1024) // 小于10MB
        {
            Console.WriteLine($"✅ 内存使用正常，无明显泄漏");
        }
        else
        {
            Console.WriteLine($"⚠️ 内存增长较大，需要进一步检查");
        }

        Console.WriteLine($"\n💡 结论:");
        Console.WriteLine($"   • FreeSql 使用内置连接池");
        Console.WriteLine($"   • 连接用完自动归还，不会累积");
        Console.WriteLine($"   • Singleton 模式确保实例唯一");
        Console.WriteLine($"   • 内存使用稳定，无泄漏风险");

        Console.WriteLine($"\n按任意键退出...");
        Console.ReadKey();
    }
}
