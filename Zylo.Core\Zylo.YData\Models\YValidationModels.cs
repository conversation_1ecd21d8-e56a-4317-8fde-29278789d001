using System.ComponentModel.DataAnnotations;

namespace Zylo.YData.Models;

/// <summary>
/// 验证结果类
/// </summary>
public class YValidationResult
{
    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<YValidationError> Errors { get; } = new();

    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid => Errors.Count == 0;

    /// <summary>
    /// 错误数量
    /// </summary>
    public int ErrorCount => Errors.Count;

    /// <summary>
    /// 添加验证错误
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <param name="errorMessage">错误消息</param>
    public void AddError(string propertyName, string errorMessage)
    {
        Errors.Add(new YValidationError(propertyName, errorMessage));
    }

    /// <summary>
    /// 添加验证错误
    /// </summary>
    /// <param name="error">验证错误</param>
    public void AddError(YValidationError error)
    {
        Errors.Add(error);
    }

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static YValidationResult Success()
    {
        return new YValidationResult();
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败的验证结果</returns>
    public static YValidationResult Failure(string errorMessage)
    {
        var result = new YValidationResult();
        result.AddError("Entity", errorMessage);
        return result;
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败的验证结果</returns>
    public static YValidationResult Failure(string propertyName, string errorMessage)
    {
        var result = new YValidationResult();
        result.AddError(propertyName, errorMessage);
        return result;
    }

    /// <summary>
    /// 获取所有错误消息
    /// </summary>
    /// <returns>错误消息列表</returns>
    public List<string> GetErrorMessages()
    {
        return Errors.Select(e => e.ErrorMessage).ToList();
    }

    /// <summary>
    /// 获取格式化的错误消息
    /// </summary>
    /// <returns>格式化的错误消息</returns>
    public string GetFormattedErrors()
    {
        if (IsValid) return "验证通过";
        
        return string.Join("\n", Errors.Select(e => $"• {e.PropertyName}: {e.ErrorMessage}"));
    }
}

/// <summary>
/// 验证错误类
/// </summary>
public class YValidationError
{
    /// <summary>
    /// 属性名
    /// </summary>
    public string PropertyName { get; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 错误代码（可选）
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <param name="errorMessage">错误消息</param>
    public YValidationError(string propertyName, string errorMessage)
    {
        PropertyName = propertyName ?? throw new ArgumentNullException(nameof(propertyName));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="propertyName">属性名</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="errorCode">错误代码</param>
    public YValidationError(string propertyName, string errorMessage, string errorCode)
        : this(propertyName, errorMessage)
    {
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{PropertyName}: {ErrorMessage}";
    }
}

/// <summary>
/// 集合验证结果类
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class YValidationCollectionResult<T> where T : class
{
    /// <summary>
    /// 验证结果列表
    /// </summary>
    public List<YValidationItemResult<T>> Results { get; } = new();

    /// <summary>
    /// 总数量
    /// </summary>
    public int TotalCount => Results.Count;

    /// <summary>
    /// 成功数量
    /// </summary>
    public int SuccessCount => Results.Count(r => r.IsValid);

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailureCount => Results.Count(r => !r.IsValid);

    /// <summary>
    /// 是否全部验证通过
    /// </summary>
    public bool IsAllValid => Results.All(r => r.IsValid);

    /// <summary>
    /// 所有错误
    /// </summary>
    public List<YValidationError> AllErrors => Results
        .Where(r => !r.IsValid)
        .SelectMany(r => r.ValidationResult.Errors)
        .ToList();

    /// <summary>
    /// 添加验证结果
    /// </summary>
    /// <param name="index">索引</param>
    /// <param name="entity">实体</param>
    /// <param name="validationResult">验证结果</param>
    public void AddResult(int index, T entity, YValidationResult validationResult)
    {
        Results.Add(new YValidationItemResult<T>(index, entity, validationResult));
    }

    /// <summary>
    /// 获取失败的实体
    /// </summary>
    /// <returns>失败的实体列表</returns>
    public List<T> GetFailedEntities()
    {
        return Results
            .Where(r => !r.IsValid)
            .Select(r => r.Entity)
            .ToList();
    }

    /// <summary>
    /// 获取成功的实体
    /// </summary>
    /// <returns>成功的实体列表</returns>
    public List<T> GetValidEntities()
    {
        return Results
            .Where(r => r.IsValid)
            .Select(r => r.Entity)
            .ToList();
    }
}

/// <summary>
/// 单个验证项结果
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class YValidationItemResult<T> where T : class
{
    /// <summary>
    /// 索引
    /// </summary>
    public int Index { get; }

    /// <summary>
    /// 实体
    /// </summary>
    public T Entity { get; }

    /// <summary>
    /// 验证结果
    /// </summary>
    public YValidationResult ValidationResult { get; }

    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid => ValidationResult.IsValid;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="index">索引</param>
    /// <param name="entity">实体</param>
    /// <param name="validationResult">验证结果</param>
    public YValidationItemResult(int index, T entity, YValidationResult validationResult)
    {
        Index = index;
        Entity = entity ?? throw new ArgumentNullException(nameof(entity));
        ValidationResult = validationResult ?? throw new ArgumentNullException(nameof(validationResult));
    }
}
