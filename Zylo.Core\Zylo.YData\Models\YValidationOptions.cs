namespace Zylo.YData.Models;

/// <summary>
/// 验证选项配置类
/// </summary>
public class YValidationOptions
{
    /// <summary>
    /// 默认验证选项
    /// </summary>
    public static YValidationOptions Default => new();

    /// <summary>
    /// 是否验证必填字段
    /// </summary>
    public bool ValidateRequired { get; set; } = true;

    /// <summary>
    /// 是否验证字符串长度
    /// </summary>
    public bool ValidateStringLength { get; set; } = true;

    /// <summary>
    /// 是否验证数值范围
    /// </summary>
    public bool ValidateRange { get; set; } = true;

    /// <summary>
    /// 是否验证邮箱格式
    /// </summary>
    public bool ValidateEmailFormat { get; set; } = true;

    /// <summary>
    /// 是否验证业务规则
    /// </summary>
    public bool ValidateBusinessRules { get; set; } = true;

    /// <summary>
    /// 是否验证数据库唯一性
    /// </summary>
    public bool ValidateUniqueness { get; set; } = false;

    /// <summary>
    /// 是否在第一个错误时停止验证
    /// </summary>
    public bool StopOnFirstError { get; set; } = false;

    /// <summary>
    /// 自定义验证规则
    /// </summary>
    public Dictionary<Type, List<YValidationRule>> CustomRules { get; } = new();

    /// <summary>
    /// 异步验证规则
    /// </summary>
    public Dictionary<Type, List<YAsyncValidationRule>> AsyncRules { get; } = new();

    /// <summary>
    /// 添加自定义验证规则
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="rule">验证规则</param>
    public void AddRule<T>(YValidationRule rule) where T : class
    {
        var type = typeof(T);
        if (!CustomRules.ContainsKey(type))
        {
            CustomRules[type] = new List<YValidationRule>();
        }
        CustomRules[type].Add(rule);
    }

    /// <summary>
    /// 添加自定义验证规则
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">验证谓词</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    public void AddRule<T>(Func<T, bool> predicate, string errorMessage, string? propertyName = null) where T : class
    {
        var rule = new YValidationRule<T>(predicate, errorMessage, propertyName);
        AddRule<T>(rule);
    }

    /// <summary>
    /// 添加异步验证规则
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="rule">异步验证规则</param>
    public void AddAsyncRule<T>(YAsyncValidationRule rule) where T : class
    {
        var type = typeof(T);
        if (!AsyncRules.ContainsKey(type))
        {
            AsyncRules[type] = new List<YAsyncValidationRule>();
        }
        AsyncRules[type].Add(rule);
    }

    /// <summary>
    /// 添加异步验证规则
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    /// <param name="predicate">异步验证谓词</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    public void AddAsyncRule<T>(Func<T, Task<bool>> predicate, string errorMessage, string? propertyName = null) where T : class
    {
        var rule = new YAsyncValidationRule<T>(predicate, errorMessage, propertyName);
        AddAsyncRule<T>(rule);
    }

    /// <summary>
    /// 创建严格验证选项
    /// </summary>
    /// <returns>严格验证选项</returns>
    public static YValidationOptions Strict()
    {
        return new YValidationOptions
        {
            ValidateRequired = true,
            ValidateStringLength = true,
            ValidateRange = true,
            ValidateEmailFormat = true,
            ValidateBusinessRules = true,
            ValidateUniqueness = true,
            StopOnFirstError = false
        };
    }

    /// <summary>
    /// 创建宽松验证选项
    /// </summary>
    /// <returns>宽松验证选项</returns>
    public static YValidationOptions Lenient()
    {
        return new YValidationOptions
        {
            ValidateRequired = true,
            ValidateStringLength = false,
            ValidateRange = false,
            ValidateEmailFormat = false,
            ValidateBusinessRules = false,
            ValidateUniqueness = false,
            StopOnFirstError = false
        };
    }

    /// <summary>
    /// 创建仅必填验证选项
    /// </summary>
    /// <returns>仅必填验证选项</returns>
    public static YValidationOptions RequiredOnly()
    {
        return new YValidationOptions
        {
            ValidateRequired = true,
            ValidateStringLength = false,
            ValidateRange = false,
            ValidateEmailFormat = false,
            ValidateBusinessRules = false,
            ValidateUniqueness = false,
            StopOnFirstError = false
        };
    }
}

/// <summary>
/// 验证规则基类
/// </summary>
public abstract class YValidationRule
{
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 属性名
    /// </summary>
    public string? PropertyName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    protected YValidationRule(string errorMessage, string? propertyName = null)
    {
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        PropertyName = propertyName;
    }

    /// <summary>
    /// 验证实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>是否验证通过</returns>
    public abstract bool Validate(object entity);
}

/// <summary>
/// 泛型验证规则
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class YValidationRule<T> : YValidationRule where T : class
{
    private readonly Func<T, bool> _predicate;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="predicate">验证谓词</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    public YValidationRule(Func<T, bool> predicate, string errorMessage, string? propertyName = null)
        : base(errorMessage, propertyName)
    {
        _predicate = predicate ?? throw new ArgumentNullException(nameof(predicate));
    }

    /// <summary>
    /// 验证实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>是否验证通过</returns>
    public override bool Validate(object entity)
    {
        if (entity is T typedEntity)
        {
            return _predicate(typedEntity);
        }
        return true; // 类型不匹配时跳过验证
    }
}

/// <summary>
/// 异步验证规则基类
/// </summary>
public abstract class YAsyncValidationRule
{
    /// <summary>
    /// 错误消息
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 属性名
    /// </summary>
    public string? PropertyName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    protected YAsyncValidationRule(string errorMessage, string? propertyName = null)
    {
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        PropertyName = propertyName;
    }

    /// <summary>
    /// 异步验证实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>是否验证通过</returns>
    public abstract Task<bool> ValidateAsync(object entity);
}

/// <summary>
/// 泛型异步验证规则
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public class YAsyncValidationRule<T> : YAsyncValidationRule where T : class
{
    private readonly Func<T, Task<bool>> _predicate;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="predicate">异步验证谓词</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="propertyName">属性名</param>
    public YAsyncValidationRule(Func<T, Task<bool>> predicate, string errorMessage, string? propertyName = null)
        : base(errorMessage, propertyName)
    {
        _predicate = predicate ?? throw new ArgumentNullException(nameof(predicate));
    }

    /// <summary>
    /// 异步验证实体
    /// </summary>
    /// <param name="entity">实体</param>
    /// <returns>是否验证通过</returns>
    public override async Task<bool> ValidateAsync(object entity)
    {
        if (entity is T typedEntity)
        {
            return await _predicate(typedEntity);
        }
        return true; // 类型不匹配时跳过验证
    }
}
