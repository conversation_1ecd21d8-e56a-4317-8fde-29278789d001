using System.Collections.Concurrent;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Zylo.YData;

/// <summary>
/// 多数据库管理器实现
/// <para>负责管理多个数据库实例，支持动态注册、切换和管理数据库连接</para>
/// </summary>
/// <remarks>
/// 此类提供以下核心功能：
/// <list type="bullet">
/// <item>动态注册和管理多个数据库连接</item>
/// <item>支持设置默认数据库</item>
/// <item>提供数据库连接测试功能</item>
/// <item>收集和管理数据库统计信息</item>
/// <item>线程安全的数据库操作</item>
/// </list>
/// </remarks>
public class YDataManager : IYDataManager
{
    #region 私有字段

    /// <summary>
    /// 数据库上下文字典，键为数据库名称，值为数据库上下文
    /// </summary>
    private readonly ConcurrentDictionary<string, IYDataContext> _databases = new();

    /// <summary>
    /// 数据库信息字典，存储数据库的配置信息
    /// </summary>
    private readonly ConcurrentDictionary<string, YDatabaseInfo> _databaseInfos = new();

    /// <summary>
    /// 数据库统计信息字典，存储数据库的运行统计
    /// </summary>
    private readonly ConcurrentDictionary<string, YDatabaseStats> _databaseStats = new();

    /// <summary>
    /// 默认数据库名称
    /// </summary>
    private string _defaultDatabase = "default";

    /// <summary>
    /// 线程安全锁对象
    /// </summary>
    private readonly object _lock = new();

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<YDataManager> _logger;

    #endregion

    #region 构造函数

    /// <summary>
    /// 初始化多数据库管理器
    /// </summary>
    /// <param name="logger">日志记录器（可选）</param>
    /// <remarks>
    /// 如果未提供日志记录器，将使用空日志记录器
    /// </remarks>
    public YDataManager(ILogger<YDataManager>? logger = null)
    {
        _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<YDataManager>.Instance;
    }

    #endregion

    #region 数据库注册和管理

    /// <summary>
    /// 注册数据库（简化版本）
    /// </summary>
    /// <param name="name">数据库名称，用于后续引用</param>
    /// <param name="connectionString">数据库连接字符串</param>
    /// <param name="dataType">数据库类型</param>
    /// <remarks>
    /// 便捷方法，内部会创建 YDataOptions 并调用完整版本的注册方法
    /// </remarks>
    /// <example>
    /// <code>
    /// manager.RegisterDatabase("users", "Data Source=users.db", YDataType.Sqlite);
    /// </code>
    /// </example>
    public void RegisterDatabase(string name, string connectionString, YDataType dataType)
    {
        var options = new YDataOptions
        {
            ConnectionString = connectionString,
            DataType = dataType
        };
        RegisterDatabase(name, options);
    }

    /// <summary>
    /// 注册数据库（完整版本）
    /// </summary>
    /// <param name="name">数据库名称，用于后续引用</param>
    /// <param name="options">详细配置选项</param>
    /// <remarks>
    /// 支持详细配置，如连接池大小、超时时间等
    /// </remarks>
    public void RegisterDatabase(string name, YDataOptions options)
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("数据库名称不能为空", nameof(name));

        if (options == null)
            throw new ArgumentNullException(nameof(options));

        if (string.IsNullOrWhiteSpace(options.ConnectionString))
            throw new ArgumentException("连接字符串不能为空", nameof(options));

        lock (_lock)
        {
            try
            {
                _logger.LogInformation("正在注册数据库: {DatabaseName}", name);

                // 创建服务容器
                var services = new ServiceCollection();
                services.AddLogging(builder => builder.AddConsole());
                services.AddYData(opt =>
                {
                    opt.ConnectionString = options.ConnectionString;
                    opt.DataType = options.DataType;
                    opt.NamingStrategy = options.NamingStrategy;
                    opt.EnableAutoSyncStructure = options.EnableAutoSyncStructure;
                    opt.EnableMonitorCommand = options.EnableMonitorCommand;
                    opt.EnableSensitiveDataLogging = options.EnableSensitiveDataLogging;
                    opt.EnableDetailedErrors = options.EnableDetailedErrors;
                    opt.DefaultQueryTimeout = options.DefaultQueryTimeout;
                    opt.SlowQueryThreshold = options.SlowQueryThreshold;
                    opt.MaxConnectionPoolSize = options.MaxConnectionPoolSize;
                    opt.MinConnectionPoolSize = options.MinConnectionPoolSize;
                });

                var provider = services.BuildServiceProvider();
                var context = provider.GetRequiredService<IYDataContext>();

                // 存储数据库上下文
                _databases[name] = context;

                // 存储数据库信息
                _databaseInfos[name] = new YDatabaseInfo
                {
                    Name = name,
                    ConnectionString = options.ConnectionString,
                    DataType = options.DataType,
                    IsDefault = _databases.Count == 1, // 第一个注册的数据库设为默认
                    CreatedAt = DateTime.Now,
                    LastUsedAt = DateTime.Now,
                    Description = $"{options.DataType} 数据库",
                    IsEnabled = true
                };

                // 初始化统计信息
                _databaseStats[name] = new YDatabaseStats
                {
                    DatabaseName = name,
                    IsConnected = false,
                    LastQueryTime = DateTime.Now
                };

                // 如果是第一个数据库，设为默认
                if (_databases.Count == 1)
                {
                    _defaultDatabase = name;
                    _logger.LogInformation("数据库 '{DatabaseName}' 已设置为默认数据库", name);
                }

                _logger.LogInformation("数据库 '{DatabaseName}' 注册成功，类型: {DataType}", name, options.DataType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册数据库 '{DatabaseName}' 失败", name);
                throw;
            }
        }
    }

    /// <summary>
    /// 获取指定数据库的上下文
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库上下文</returns>
    /// <exception cref="ArgumentException">数据库不存在时抛出</exception>
    /// <remarks>
    /// 每次获取时会更新数据库的最后使用时间
    /// </remarks>
    public IYDataContext GetDatabase(string name)
    {
        if (_databases.TryGetValue(name, out var context))
        {
            // 更新最后使用时间
            if (_databaseInfos.TryGetValue(name, out var info))
            {
                info.LastUsedAt = DateTime.Now;
            }

            return context;
        }

        throw new InvalidOperationException($"数据库 '{name}' 未找到。请先注册该数据库。");
    }

    /// <summary>
    /// 获取默认数据库上下文
    /// </summary>
    /// <returns>默认数据库上下文</returns>
    public IYDataContext GetDefaultDatabase()
    {
        return GetDatabase(_defaultDatabase);
    }

    /// <summary>
    /// 设置默认数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    public void SetDefaultDatabase(string name)
    {
        if (!DatabaseExists(name))
            throw new InvalidOperationException($"数据库 '{name}' 未找到。");

        // 更新旧默认数据库状态
        if (_databaseInfos.TryGetValue(_defaultDatabase, out var oldDefault))
        {
            oldDefault.IsDefault = false;
        }

        // 设置新默认数据库
        _defaultDatabase = name;
        if (_databaseInfos.TryGetValue(name, out var newDefault))
        {
            newDefault.IsDefault = true;
        }

        _logger.LogInformation("默认数据库已切换到 '{DatabaseName}'", name);
    }

    /// <summary>
    /// 获取所有数据库名称
    /// </summary>
    /// <returns>数据库名称列表</returns>
    public IEnumerable<string> GetDatabaseNames()
    {
        return _databases.Keys.ToList();
    }

    /// <summary>
    /// 检查数据库是否存在
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>是否存在</returns>
    public bool DatabaseExists(string name)
    {
        return _databases.ContainsKey(name);
    }

    /// <summary>
    /// 移除数据库
    /// </summary>
    /// <param name="name">数据库名称</param>
    public void RemoveDatabase(string name)
    {
        lock (_lock)
        {
            if (_databases.TryRemove(name, out var context))
            {
                // 如果移除的是默认数据库，选择新的默认数据库
                if (_defaultDatabase == name && _databases.Any())
                {
                    _defaultDatabase = _databases.Keys.First();
                    if (_databaseInfos.TryGetValue(_defaultDatabase, out var newDefault))
                    {
                        newDefault.IsDefault = true;
                    }
                }

                _databaseInfos.TryRemove(name, out _);
                _databaseStats.TryRemove(name, out _);

                // 释放资源
                if (context is IDisposable disposable)
                {
                    disposable.Dispose();
                }

                _logger.LogInformation("数据库 '{DatabaseName}' 已移除", name);
            }
        }
    }

    /// <summary>
    /// 获取数据库信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>数据库信息</returns>
    public YDatabaseInfo? GetDatabaseInfo(string name)
    {
        return _databaseInfos.TryGetValue(name, out var info) ? info : null;
    }

    /// <summary>
    /// 获取所有数据库信息
    /// </summary>
    /// <returns>数据库信息列表</returns>
    public IEnumerable<YDatabaseInfo> GetAllDatabaseInfo()
    {
        return _databaseInfos.Values.ToList();
    }

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>连接是否成功</returns>
    public async Task<bool> TestConnectionAsync(string name)
    {
        try
        {
            var context = GetDatabase(name);
            // 执行简单查询测试连接
            await context.FreeSql.Ado.QueryAsync<int>("SELECT 1");

            // 更新统计信息
            if (_databaseStats.TryGetValue(name, out var stats))
            {
                stats.IsConnected = true;
                stats.LastQueryTime = DateTime.Now;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "测试数据库 '{DatabaseName}' 连接失败", name);

            // 更新统计信息
            if (_databaseStats.TryGetValue(name, out var stats))
            {
                stats.IsConnected = false;
            }

            return false;
        }
    }

    /// <summary>
    /// 获取数据库统计信息
    /// </summary>
    /// <param name="name">数据库名称</param>
    /// <returns>统计信息</returns>
    public async Task<YDatabaseStats> GetDatabaseStatsAsync(string name)
    {
        if (!_databaseStats.TryGetValue(name, out var stats))
        {
            throw new InvalidOperationException($"数据库 '{name}' 未找到。");
        }

        try
        {
            var context = GetDatabase(name);

            // 更新连接状态
            stats.IsConnected = await TestConnectionAsync(name);

            if (stats.IsConnected)
            {
                // 这里可以添加更多统计信息的获取逻辑
                // 例如：表数量、记录数等
                stats.LastQueryTime = DateTime.Now;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "获取数据库 '{DatabaseName}' 统计信息失败", name);
            stats.IsConnected = false;
        }

        return stats;
    }

    #endregion
}
