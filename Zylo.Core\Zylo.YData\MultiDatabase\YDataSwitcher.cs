namespace Zylo.YData;

/// <summary>
/// 数据库切换助手
/// </summary>
public static class YDataSwitcher
{
    /// <summary>
    /// 临时切换到指定数据库执行操作
    /// </summary>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="databaseName">数据库名称</param>
    /// <param name="operation">操作</param>
    /// <returns>操作结果</returns>
    public static async Task<TResult> WithDatabaseAsync<TResult>(string databaseName, Func<Task<TResult>> operation)
    {
        if (string.IsNullOrWhiteSpace(databaseName))
            throw new ArgumentException("数据库名称不能为空", nameof(databaseName));

        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        // 保存当前默认数据库
        var originalDefault = YData.GetCurrentDatabaseName();

        try
        {
            // 切换到指定数据库
            YData.UseDatabase(databaseName);

            // 执行操作
            return await operation();
        }
        finally
        {
            // 恢复原默认数据库
            if (!string.IsNullOrEmpty(originalDefault))
            {
                YData.UseDatabase(originalDefault);
            }
        }
    }

    /// <summary>
    /// 临时切换到指定数据库执行操作（无返回值）
    /// </summary>
    /// <param name="databaseName">数据库名称</param>
    /// <param name="operation">操作</param>
    public static async Task WithDatabaseAsync(string databaseName, Func<Task> operation)
    {
        await WithDatabaseAsync(databaseName, async () =>
        {
            await operation();
            return 0; // 返回虚拟值
        });
    }

    /// <summary>
    /// 在所有数据库中执行相同操作
    /// </summary>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <returns>每个数据库的操作结果</returns>
    public static async Task<Dictionary<string, TResult>> ExecuteInAllDatabasesAsync<TResult>(Func<string, Task<TResult>> operation)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var results = new Dictionary<string, TResult>();
        var databaseNames = YData.GetDatabaseNames();

        foreach (var dbName in databaseNames)
        {
            try
            {
                var result = await WithDatabaseAsync(dbName, () => operation(dbName));
                results[dbName] = result;
            }
            catch (Exception ex)
            {
                // 记录错误但继续处理其他数据库
                Console.WriteLine($"在数据库 '{dbName}' 中执行操作失败: {ex.Message}");
                // 可以选择抛出异常或设置默认值
                results[dbName] = default(TResult)!;
            }
        }

        return results;
    }

    /// <summary>
    /// 在所有数据库中并行执行相同操作
    /// </summary>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <param name="maxDegreeOfParallelism">最大并行度</param>
    /// <returns>每个数据库的操作结果</returns>
    public static async Task<Dictionary<string, TResult>> ExecuteInAllDatabasesParallelAsync<TResult>(
        Func<string, Task<TResult>> operation,
        int maxDegreeOfParallelism = 4)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var databaseNames = YData.GetDatabaseNames().ToList();
        var results = new ConcurrentDictionary<string, TResult>();

        var semaphore = new SemaphoreSlim(maxDegreeOfParallelism, maxDegreeOfParallelism);
        var tasks = databaseNames.Select(async dbName =>
        {
            await semaphore.WaitAsync();
            try
            {
                var result = await WithDatabaseAsync(dbName, () => operation(dbName));
                results[dbName] = result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"在数据库 '{dbName}' 中执行并行操作失败: {ex.Message}");
                results[dbName] = default(TResult)!;
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);
        return new Dictionary<string, TResult>(results);
    }

    /// <summary>
    /// 在指定数据库列表中执行操作
    /// </summary>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="databaseNames">数据库名称列表</param>
    /// <param name="operation">操作</param>
    /// <returns>每个数据库的操作结果</returns>
    public static async Task<Dictionary<string, TResult>> ExecuteInDatabasesAsync<TResult>(
        IEnumerable<string> databaseNames,
        Func<string, Task<TResult>> operation)
    {
        if (databaseNames == null)
            throw new ArgumentNullException(nameof(databaseNames));

        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var results = new Dictionary<string, TResult>();

        foreach (var dbName in databaseNames)
        {
            if (!YData.DatabaseExists(dbName))
            {
                Console.WriteLine($"数据库 '{dbName}' 不存在，跳过执行");
                continue;
            }

            try
            {
                var result = await WithDatabaseAsync(dbName, () => operation(dbName));
                results[dbName] = result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"在数据库 '{dbName}' 中执行操作失败: {ex.Message}");
                results[dbName] = default(TResult)!;
            }
        }

        return results;
    }

    /// <summary>
    /// 批量数据库操作结果
    /// </summary>
    /// <typeparam name="TResult">结果类型</typeparam>
    public class BatchOperationResult<TResult>
    {
        /// <summary>
        /// 成功的操作结果
        /// </summary>
        public Dictionary<string, TResult> SuccessResults { get; set; } = new();

        /// <summary>
        /// 失败的操作
        /// </summary>
        public Dictionary<string, Exception> FailedOperations { get; set; } = new();

        /// <summary>
        /// 总操作数
        /// </summary>
        public int TotalOperations => SuccessResults.Count + FailedOperations.Count;

        /// <summary>
        /// 成功操作数
        /// </summary>
        public int SuccessCount => SuccessResults.Count;

        /// <summary>
        /// 失败操作数
        /// </summary>
        public int FailureCount => FailedOperations.Count;

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalOperations == 0 ? 0 : (double)SuccessCount / TotalOperations;
    }

    /// <summary>
    /// 安全的批量数据库操作（记录详细错误信息）
    /// </summary>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="operation">操作</param>
    /// <returns>批量操作结果</returns>
    public static async Task<BatchOperationResult<TResult>> SafeExecuteInAllDatabasesAsync<TResult>(Func<string, Task<TResult>> operation)
    {
        if (operation == null)
            throw new ArgumentNullException(nameof(operation));

        var result = new BatchOperationResult<TResult>();
        var databaseNames = YData.GetDatabaseNames();

        foreach (var dbName in databaseNames)
        {
            try
            {
                var operationResult = await WithDatabaseAsync(dbName, () => operation(dbName));
                result.SuccessResults[dbName] = operationResult;
            }
            catch (Exception ex)
            {
                result.FailedOperations[dbName] = ex;
            }
        }

        return result;
    }

    /// <summary>
    /// 数据库健康检查
    /// </summary>
    /// <returns>每个数据库的健康状态</returns>
    public static async Task<Dictionary<string, bool>> HealthCheckAsync()
    {
        return await ExecuteInAllDatabasesAsync(async dbName =>
        {
            try
            {
                return await YData.TestConnectionAsync(dbName);
            }
            catch
            {
                return false;
            }
        });
    }

    /// <summary>
    /// 获取所有数据库的统计信息
    /// </summary>
    /// <returns>数据库统计信息</returns>
    public static async Task<Dictionary<string, YDatabaseStats>> GetAllDatabaseStatsAsync()
    {
        return await ExecuteInAllDatabasesAsync(async dbName =>
        {
            return await YData.GetDatabaseStatsAsync(dbName);
        });
    }
}
