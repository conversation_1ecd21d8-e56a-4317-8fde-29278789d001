# 🚀 Zylo.YData - 现代化数据访问层框架

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/zylo/zylo.ydata)
[![NuGet Version](https://img.shields.io/badge/nuget-v1.0.0-blue.svg)](https://www.nuget.org/packages/Zylo.YData)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![.NET](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-purple.svg)](https://dotnet.microsoft.com/)

**Zylo.YData** 是一个基于 FreeSql 的现代化数据访问层框架，在保持 FreeSql 强大功能的基础上，提供最简单的使用方式，支持容器注入，一行代码搞定数据操作。

## ✨ 核心特性

- 🔥 **基于 FreeSql** - 继承 FreeSql 的所有强大功能和高性能
- 💉 **完美依赖注入** - 原生支持 .NET 依赖注入容器
- 🚀 **极简 API** - 一行代码完成复杂数据操作
- 🗄️ **多数据库支持** - SQL Server、MySQL、PostgreSQL、SQLite、Oracle
- 🔧 **智能扩展** - 丰富的扩展方法，提升开发效率
- 📊 **数据验证** - 内置强大的数据验证功能
- 🧪 **完整测试** - 93 个测试用例，99% 通过率

## 📦 快速开始

### 安装

```bash
dotnet add package Zylo.YData
```

### 基础使用

```csharp
// 1. 配置数据库（一行代码）
YData.ConfigureAuto("Data Source=test.db", YDataType.Sqlite);

// 2. 基础 CRUD 操作
var user = new User { Name = "张三", Email = "<EMAIL>", Age = 25 };

// 插入
await YData.InsertAsync(user);

// 查询
var users = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToListAsync();

// 更新
user.Age = 26;
await YData.UpdateAsync(user);

// 删除
await YData.DeleteAsync<User>(user.Id);
```

### 依赖注入使用

```csharp
// Program.cs
builder.Services.AddYDataAuto("Data Source=test.db", YDataType.Sqlite);

// 在服务中使用
public class UserService
{
    private readonly IYDataContext _context;
    
    public UserService(IYDataContext context)
    {
        _context = context;
    }
    
    public async Task<List<User>> GetActiveUsersAsync()
    {
        return await _context.Select<User>()
            .Where(u => u.IsActive)
            .ToListAsync();
    }
}
```

## 🏗️ 项目结构

```text
Zylo.YData/
├── 📁 Core/                    # 🔥 核心接口和抽象定义
├── 📁 Implementations/         # 🚀 核心功能实现
├── 📁 Configuration/           # ⚙️ 配置相关
├── 📁 DependencyInjection/     # 💉 依赖注入支持
├── 📁 MultiDatabase/           # 🗄️ 多数据库支持
├── 📁 Extensions/              # 🔧 扩展方法
├── 📁 Models/                  # 📊 数据模型
├── 📁 Examples/                # 📝 示例代码
├── 📁 Tests/                   # 🧪 测试代码
└── 📁 Documentation/           # 📚 项目文档
```

## 💡 高级功能

### 智能查询扩展

```csharp
// 条件查询
var users = await YData.Select<User>()
    .WhereIf(!string.IsNullOrEmpty(name), u => u.Name.Contains(name))
    .WhereIfNotNull(age, u => u.Age == age)
    .ToListAsync();

// 分页查询
var pagedResult = await YData.Select<User>()
    .Where(u => u.IsActive)
    .ToPagedResultAsync(pageIndex: 1, pageSize: 10);

Console.WriteLine($"总共 {pagedResult.TotalCount} 条记录");
Console.WriteLine($"当前第 {pagedResult.PageIndex} 页");
```

### 数据验证

```csharp
// 实体验证
var result = await user.YValidateAsync();
if (!result.IsValid)
{
    foreach (var error in result.Errors)
    {
        Console.WriteLine($"{error.PropertyName}: {error.ErrorMessage}");
    }
}

// 扩展验证方法
bool isValidEmail = "<EMAIL>".YIsValidEmail();
bool isValidPhone = "13812345678".YIsValidPhone();
bool isInRange = 25.YIsInRange(18, 65);
```

### 事务支持

```csharp
// 异步事务
var result = await YData.TransactionAsync(async () =>
{
    await YData.InsertAsync(user);
    await YData.InsertAsync(order);
    return true;
});
```

## 📚 文档

- [📖 完整文档](Documentation/README.md)
- [🔧 数据验证指南](Documentation/YDataValidation_README.md)
- [🏗️ 项目结构说明](Documentation/ProjectStructure.md)
- [📋 设计方案](Documentation/Zylo.YData基于FreeSql的现代化方案.md)

## 🧪 测试

项目包含完整的测试覆盖：

```bash
# 运行测试
dotnet test

# 查看测试覆盖率
dotnet test --collect:"XPlat Code Coverage"
```

**测试统计**: 93 个测试用例，99% 通过率

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🔗 相关项目

- [Zylo.Core](../Zylo.Core) - 核心容器支持
- [Zylo.YIO](../Zylo.YIO) - IO 操作库
- [Zylo.AutoG](../Zylo.AutoG) - 代码生成器
- [FreeSql](https://github.com/dotnetcore/FreeSql) - 底层 ORM 框架

---

> 💡 **提示**: 这是一个现代化的数据访问层框架，旨在提供最简单的使用方式和最强大的功能。如果您有任何问题或建议，请随时提出 Issue。
