<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
    <PackageId>Zylo.YData</PackageId>
    <Product>Zylo Data Access Framework</Product>

    <!-- NuGet 包信息 -->
    <Description>🚀 Zylo.YData -
      现代化数据访问层框架。基于FreeSql的强大功能，提供最简单的使用方式，支持多数据库切换、容器注入、高性能数据操作等企业级数据访问功能。</Description>
    <PackageTags>data;orm;freesql;simple;container;multi-database;performance;zylo</PackageTags>
    <PackageProjectUrl>https://github.com/zylo/zylo.ydata</PackageProjectUrl>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

    <!-- 发布说明 -->
    <PackageReleaseNotes>
      🎉 v1.3.2 - 功能增强与稳定性提升：
      - 🔧 基于 FreeSql 3.5.100 的强大 ORM 功能
      - 🗄️ 支持多数据库：SQL Server、MySQL、PostgreSQL、SQLite
      - 💉 完整的依赖注入支持
      - 🚀 高性能数据操作和批量处理
      - 📊 企业级数据访问解决方案
    </PackageReleaseNotes>
  </PropertyGroup>
  <!-- ========== NuGet 包版本号 ========== -->
  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" /><!-- 将README.md包含到NuGet包中 -->
  </ItemGroup>

  <!-- 🔥 FreeSql 核心依赖 -->
  <ItemGroup>
    <PackageReference Include="FreeSql" Version="3.5.100" />
    <PackageReference Include="FreeSql.Provider.SqlServer" Version="3.5.100" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.100" />
    <PackageReference Include="FreeSql.Provider.MySql" Version="3.5.100" />
    <PackageReference Include="FreeSql.Provider.PostgreSQL" Version="3.5.100" />
  </ItemGroup>

  <!-- 💉 依赖注入和配置 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables"
      Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="6.0.0" />
  </ItemGroup>

  <!-- 🔧 序列化和工具 -->
  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="6.0.0" />
  </ItemGroup>

  <!-- Zylo 生态系统依赖 -->
  <!-- 注释：当前 Zylo.YData 专注于数据访问功能，暂不需要其他 Zylo 组件 -->
  <!--
  <ItemGroup>
    <ProjectReference Include="..\Zylo.AutoG\Zylo.AutoG.csproj" OutputItemType="Analyzer"
  ReferenceOutputAssembly="false" />
    <ProjectReference Include="..\Zylo.Core\Zylo.Core.csproj" />
    <ProjectReference Include="..\Zylo.YIO\Zylo.YIO.csproj" />
    <ProjectReference Include="..\Zylo.YLog.Runtime\Zylo.YLog.Runtime.csproj" />
  </ItemGroup>
  -->


  <!-- 代码分析 -->
  <PropertyGroup>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    <!-- 抑制XML注释警告 -->
    <NoWarn>$(NoWarn);CS1591;CS1587;NU1605;MSB3277</NoWarn>
  </PropertyGroup>

</Project>