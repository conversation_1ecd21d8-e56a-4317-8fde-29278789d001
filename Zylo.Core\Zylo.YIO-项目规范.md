# 🚀 Zylo.YIO - 企业级文件操作工具库

## 📋 项目概述

**Zylo.YIO** 是一个基于 Zylo.AutoG 框架的企业级文件操作工具库，提供全面的文件和目录操作功能，支持依赖注入、扩展方法、配置管理和自动文档生成。

### 🎯 设计目标

- **🔧 全面性** - 覆盖所有常见的文件操作需求
- **⚡ 高性能** - 支持异步操作和批量处理
- **🛡️ 安全性** - 内置文件验证和安全检查
- **🔌 易集成** - 支持依赖注入和扩展方法
- **📝 自文档** - 自动生成完整的API文档

## 🏗️ 项目结构

```csharp
Zylo.YIO/
├── 📁 Core/                    # 核心基础功能
│   ├── YFileOperations.cs      # 完整文件操作（创建、读写、复制、移动、删除、权限）
│   ├── YDirectoryOperations.cs # 完整目录操作（创建、遍历、搜索、复制、权限、监控）
│   └── YPathUtilities.cs       # 完整路径工具（组合、转换、验证、安全、临时路径）
├── 📁 Management/              # 文件管理功能  
│   ├── YFileBackup.cs          # 备份功能（完整、增量、差异）
│   ├── YFileSync.cs            # 同步功能（单向、双向、镜像）
│   ├── YFileLifecycle.cs       # 生命周期管理（清理、归档、保留策略）
│   └── YFileWatcher.cs         # 文件监控（实时监控、事件处理）
├── 📁 Analysis/                # 分析和工具
│   ├── YFileAnalyzer.cs        # 文件分析、重复检测、统计
│   ├── YSpaceAnalyzer.cs       # 磁盘空间分析、使用报告
│   └── YFileValidator.cs       # 文件验证、格式检查、安全检查
├── 📁 Security/                # 安全操作
│   ├── YSecureFileOperations.cs # 安全文件操作、权限检查
│   ├── YFileEncryption.cs      # 文件加密、解密、哈希
│   ├── YSecureDelete.cs        # 安全删除、数据清除
│   └── YAccessControl.cs       # 访问控制、权限管理
├── 📁 Async/                   # 异步处理
│   ├── YAsyncFileProcessor.cs  # 异步文件处理、进度跟踪
│   ├── YTaskQueue.cs           # 任务队列、并发控制
│   └── YProgressReporter.cs    # 进度报告、状态监控
├── 📁 Formats/                 # 格式处理（自实现）
│   ├── YTextProcessor.cs       # TXT、CSV、INI 处理
│   ├── YConfigProcessor.cs     # JSON、XML 处理
│   └── YFormatSettings.cs      # 格式配置和设置
├── 📁 Models/                  # 数据模型和结果类型
│   ├── YFileOperationResult.cs # 统一操作结果
│   ├── YFileInfoModel.cs       # 文件信息模型
│   ├── YBackupInfo.cs          # 备份信息模型
│   └── YSyncResult.cs          # 同步结果模型
├── 📁 Config/                  # 配置管理
│   └── YIOConfig.cs            # 统一配置类
└── Program.cs                  # 演示程序
```

## 🔧 核心功能模块

### 1. 基础文件操作 (YFileOperations)

**框架属性**: `[YDoc]` `[YService.Scoped]` `[YStatic]`

**核心功能**:
- 文件创建、读写、复制、移动、删除
- 文件信息获取（大小、修改时间、MIME类型）
- 文件权限和安全操作
- 异步文件处理支持

### 2. 目录操作 (YDirectoryOperations)

**框架属性**: `[YDoc]` `[YService.Scoped]` `[YStatic]`

**核心功能**:
- 目录创建、删除、复制、移动
- 目录遍历（递归/非递归）
- 目录搜索和过滤
- 目录权限和监控

### 3. 路径处理工具 (YPathUtilities)

**框架属性**: `[YDoc]` `[YService.Singleton]` `[YStatic]`

**核心功能**:
- 路径组合和规范化
- 相对路径和绝对路径转换
- 路径验证和安全检查
- 临时路径和唯一路径生成

### 4. 格式处理器 (YConfigProcessor)

**框架属性**: `[YDoc]` `[YService.Transient]` `[YStatic]`

**支持格式**:
- **JSON** - 完整的读写、格式化、验证功能
- **XML** - 完整的解析、生成、查询功能
- **INI** - 自实现的配置文件处理
- **CSV** - 自实现的数据文件处理

**配置功能**:
- 格式自动检测
- 编码自动识别
- 错误处理和验证
- 性能优化设置

## 📊 格式处理配置

### JSON 配置选项
```csharp
public class JsonSettings
{
    public bool PrettyPrint { get; set; } = true;           // 格式化输出
    public bool IgnoreComments { get; set; } = true;        // 忽略注释
    public bool AllowTrailingCommas { get; set; } = true;   // 允许尾随逗号
    public JsonNamingPolicy NamingPolicy { get; set; }      // 命名策略
    public int MaxDepth { get; set; } = 64;                 // 最大深度
}
```

### XML 配置选项
```csharp
public class XmlSettings
{
    public bool PreserveWhitespace { get; set; } = false;   // 保留空白
    public bool ValidateOnParse { get; set; } = true;       // 解析时验证
    public string DefaultEncoding { get; set; } = "UTF-8";  // 默认编码
    public bool IndentOutput { get; set; } = true;          // 缩进输出
    public string IndentChars { get; set; } = "  ";         // 缩进字符
}
```

### INI 配置选项
```csharp
public class IniSettings
{
    public char CommentChar { get; set; } = '#';            // 注释字符
    public char SectionStart { get; set; } = '[';           // 节开始字符
    public char SectionEnd { get; set; } = ']';             // 节结束字符
    public char KeyValueSeparator { get; set; } = '=';      // 键值分隔符
    public bool TrimValues { get; set; } = true;            // 去除值的空白
    public bool CaseSensitive { get; set; } = false;        // 大小写敏感
}
```

## 🚀 使用示例

### 依赖注入使用

```csharp
// 注册服务
services.AddYFileOperations();
services.AddYDirectoryOperations();
services.AddYPathUtilities();
services.AddYConfigProcessor();

// 使用服务
var fileOps = serviceProvider.GetService<IYFileOperations>();
var configProcessor = serviceProvider.GetService<IYConfigProcessor>();

// 读取配置文件
var jsonData = configProcessor.ReadJson<AppConfig>("appsettings.json");
var xmlData = configProcessor.ReadXml("config.xml");
```

### 扩展方法使用

```csharp
// 文件操作
var content = "config.json".ReadText();
var success = "data.txt".WriteText("Hello World");

// 配置文件处理
var config = "appsettings.json".ReadAsJson<AppConfig>();
var xmlDoc = "config.xml".ReadAsXml();
var iniData = "settings.ini".ReadAsIni();

// 写入配置
config.WriteToJson("output.json", prettyPrint: true);
xmlDoc.WriteToXml("output.xml", indent: true);
```

### 静态方法使用

```csharp
// 路径工具
var combined = YPathUtilities.CombinePath("base", "sub", "file.txt");
var normalized = YPathUtilities.NormalizePath(userInput);

// 配置处理
var jsonSettings = new JsonSettings { PrettyPrint = true };
var data = YConfigProcessor.ReadJson<MyConfig>("config.json", jsonSettings);

// 文件验证
var isValid = YFileValidator.IsValidJsonFile("config.json");
var isXmlValid = YFileValidator.IsValidXmlFile("data.xml");
```

## 📝 开发计划

### Phase 1: 核心功能 (Week 1-2)
- ✅ 项目结构搭建
- ✅ YFileOperations 完整功能
- ✅ YDirectoryOperations 完整功能
- ✅ YPathUtilities 工具类

### Phase 2: 格式处理 (Week 3-4)
- 🔄 YConfigProcessor 配置处理
- 🔄 JSON/XML/INI 自实现
- 🔄 格式配置和设置
- 🔄 YTextProcessor 文本处理

### Phase 3: 管理功能 (Week 5-6)
- ⏳ YFileBackup 备份功能
- ⏳ YFileSync 同步功能
- ⏳ YFileLifecycle 生命周期管理
- ⏳ YFileWatcher 监控功能

### Phase 4: 分析和安全 (Week 7-8)
- ⏳ YFileAnalyzer 分析功能
- ⏳ YSpaceAnalyzer 空间分析
- ⏳ YSecureFileOperations 安全操作
- ⏳ YFileEncryption 加密功能

## 🎯 质量标准

- **代码覆盖率**: ≥ 90%
- **完整注释**: 所有类、方法、属性都有XML注释
- **#region 分组**: 所有功能都用 #region 清晰分组
- **性能基准**: 大文件操作 ≥ 100MB/s
- **内存使用**: 流式处理，避免大文件全加载
- **异常处理**: 100% 覆盖，提供友好错误信息
- **文档完整性**: 所有公开API都有完整文档

---

**项目负责人**: Zylo.AutoG Team  
**创建时间**: 2025-07-01  
**最后更新**: 2025-07-01  
**版本**: v1.0.0-planning  
**项目名称**: Zylo.YIO
