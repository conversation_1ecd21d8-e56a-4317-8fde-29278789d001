
using System;
using System.IO;
using Zylo.YIO.Formats;
using Zylo.YIO.Extensions;

namespace Zylo.YIO.Demo
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("🚀 Zylo.YIO.Demo - YSave/YLoad 扩展方法测试");
            Console.WriteLine(new string('=', 60));

            // 测试 YSave 和 YLoad 扩展方法
            TestYSaveYLoadExtensions();

            Console.WriteLine("\n✅ 测试完成！按任意键退出...");
            Console.ReadKey();
        }

        static void TestYSaveYLoadExtensions()
        {
            Console.WriteLine("\n🔧 测试 YSave 和 YLoad 扩展方法 - 增强版");
            Console.WriteLine("支持多种路径和文件名配置方式");
            Console.WriteLine();

            // 1. 测试默认方式
            TestDefaultSaveLoad();

            // 2. 测试全局配置修改
            TestGlobalConfiguration();

            // 3. 测试多种重载方法
            TestMultipleOverloads();

            // 4. 测试自定义路径和文件名
            TestCustomPathAndFileName();

            Console.WriteLine("🎯 增强版测试总结:");
            Console.WriteLine("   - 默认保存/加载 ✅");
            Console.WriteLine("   - 全局配置修改 ✅");
            Console.WriteLine("   - 多种重载方法 ✅");
            Console.WriteLine("   - 自定义路径和文件名 ✅");
        }

        static void TestDefaultSaveLoad()
        {
            Console.WriteLine("📁 1. 测试默认保存/加载方式");

            var formats = new Formats
            {
                Name = "默认格式处理器",
                SupportedFormats = new() { "JSON", "XML", "INI" },
                IsEnabled = true,
                CreatedAt = DateTime.Now
            };

            // 默认保存：./json/Formats.json
            var saved = formats.YSave();
            Console.WriteLine($"   默认保存: {(saved ? "✅ 成功" : "❌ 失败")}");

            // 默认加载
            var loadedFormats = new Formats();
            loadedFormats.YLoad();
            Console.WriteLine($"   默认加载: {loadedFormats.Name}");
            Console.WriteLine();
        }

        static void TestGlobalConfiguration()
        {
            Console.WriteLine("📁 2. 测试新的配置处理器");

            var ddd = new Ddd
            {
                Id = 2024,
                Description = "使用新配置处理器的DDD",
                IsActive = true
            };

            // 使用新的扩展方法保存
            var saved = ddd.YSave();
            Console.WriteLine($"   保存结果: {(saved ? "✅ 成功" : "❌ 失败")}");

            // 使用新的扩展方法加载
            var loaded = ddd.YLoad();
            if (loaded != null)
            {
                Console.WriteLine($"   加载成功: ID={loaded.Id}, Description={loaded.Description}");
            }
            else
            {
                Console.WriteLine("   ❌ 加载失败");
            }

            // 测试静态方法
            var saved2 = YConfigHelper.Save(ddd, "./custom/ddd.json");
            Console.WriteLine($"   静态保存结果: {(saved2 ? "✅ 成功" : "❌ 失败")}");

            var loaded2 = YConfigHelper.Load<Ddd>("./custom/ddd.json");
            if (loaded2 != null)
            {
                Console.WriteLine($"   静态加载成功: ID={loaded2.Id}");
            }

            Console.WriteLine();
        }

        static void TestMultipleOverloads()
        {
            Console.WriteLine("📁 3. 测试多种重载方法");

            var user = new User
            {
                Name = "测试用户",
                Age = 30,
                Email = "<EMAIL>",
                Roles = new() { "Admin", "User" }
            };

            // 方法1：完整路径
            var saved1 = user.YSave("./test-data/user-full-path.json");
            Console.WriteLine($"   完整路径保存: {(saved1 ? "✅ 成功" : "❌ 失败")}");

            // 方法2：指定目录
            var saved2 = user.YSave(null, "./test-data");
            Console.WriteLine($"   指定目录保存: {(saved2 ? "✅ 成功" : "❌ 失败")}");

            // 方法3：完整路径
            var saved3 = user.YSave("./test-data/user-custom.json");
            Console.WriteLine($"   完整路径保存: {(saved3 ? "✅ 成功" : "❌ 失败")}");

            // 对应的加载测试
            var loadedUser1 = user.YLoad("./test-data/user-full-path.json");
            if (loadedUser1 != null)
            {
                Console.WriteLine($"   完整路径加载: {loadedUser1.Name}");
            }

            var loadedUser2 = user.YLoad(null, "./test-data");
            Console.WriteLine($"   目录+文件名加载: {loadedUser2.Name}");
            Console.WriteLine();
        }

        static void TestCustomPathAndFileName()
        {
            Console.WriteLine("📁 4. 测试自定义路径和文件名");

            var config = new AppConfig
            {
                AppName = "自定义配置测试",
                Version = "2.0.0",
                DebugMode = true,
                Settings = new()
                {
                    ["CustomKey1"] = "CustomValue1",
                    ["CustomKey2"] = "CustomValue2"
                }
            };

            // 测试各种自定义组合
            var testCases = new (string, string?, string?, string)[]
            {
                ("./configs/app.json", null, null, "完整自定义路径"),
                ("./data", "my-app", null, "目录+自定义文件名"),
                ("./backup", "app-backup", ".bak", "目录+文件名+自定义扩展名")
            };

            foreach (var testCase in testCases)
            {
                bool saved;
                if (testCase.Item3 == null)
                {
                    if (testCase.Item2 == null)
                    {
                        saved = config.YSave(testCase.Item1);
                    }
                    else
                    {
                        saved = config.YSave(testCase.Item1, testCase.Item2);
                    }
                }
                else
                {
                    // 使用完整路径
                    var fullPath = Path.Combine(testCase.Item2!, testCase.Item1!);
                    saved = config.YSave(fullPath);
                }

                Console.WriteLine($"   {testCase.Item4}: {(saved ? "✅ 成功" : "❌ 失败")}");
            }
            Console.WriteLine();
        }
    }
}
