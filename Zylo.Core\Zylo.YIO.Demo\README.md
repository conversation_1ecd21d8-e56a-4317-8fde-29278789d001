# Zylo.YIO - 企业级文件操作工具库

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/your-repo/Zylo.YIO)

**Zylo.YIO** 是 Zylo 框架生态系统中的核心文件操作组件，提供全面的企业级文件处理解决方案。该项目集成了文件操作、监控、安全、压缩、批量处理等功能，支持自动代码生成和依赖注入。

## 🚀 核心特性

### 📁 **完整的文件操作体系**

- **基础操作** - 文件和目录的创建、复制、移动、删除
- **高级功能** - 文件同步、备份、压缩、加密
- **智能分析** - 文件分析、空间分析、性能监控
- **批量处理** - 高并发批量文件操作

### 🔧 **自动代码生成**

- **YStatic** - 自动生成静态扩展方法
- **YService** - 自动服务注册和依赖注入
- **统一命名空间** - 所有服务统一注册到 `Zylo.YIO` 命名空间

### 🛡️ **企业级特性**

- **安全性** - 文件加密、安全删除、文件验证
- **可靠性** - 完善的错误处理和异常管理
- **性能** - 异步操作、并发控制、性能监控
- **可扩展** - 模块化设计、插件化架构

## 📦 项目结构

```
Zylo.YIO/
├── Analysis/           # 文件分析模块
│   ├── YFileAnalyzer.cs       # 文件分析器
│   └── YSpaceAnalyzer.cs      # 磁盘空间分析器
├── Core/              # 核心文件操作
│   ├── YFileOperations.cs     # 文件操作
│   ├── YDirectoryOperations.cs # 目录操作
│   ├── YPathUtilities.cs      # 路径工具
│   └── YCommonPaths.cs        # 常用路径
├── Formats/           # 配置文件处理
│   └── YConfigProcessor.cs    # 配置处理器
├── Management/        # 文件管理
│   ├── YFileBackup.cs         # 文件备份
│   └── YFileCompressor.cs     # 文件压缩
├── Monitoring/        # 监控和性能
│   ├── YFileWatcher.cs        # 文件系统监控
│   └── YPerformanceMonitor.cs # 性能监控
├── Processing/        # 批量处理
│   └── YBatchProcessor.cs     # 批量处理器
├── Security/          # 安全功能
│   ├── YFileEncryption.cs     # 文件加密
│   ├── YFileValidator.cs      # 文件验证
│   └── YSecureDelete.cs       # 安全删除
└── Config/            # 配置
    └── YIOConfig.cs           # 全局配置
```

## 🎯 核心模块详解

### 1. **Core - 核心文件操作**

- **YFileOperations** (Singleton) - 基础文件操作
- **YDirectoryOperations** (Singleton) - 目录操作和项目管理
- **YPathUtilities** (Static) - 路径处理工具
- **YCommonPaths** (Static) - 系统常用路径

### 2. **Analysis - 智能分析**

- **YFileAnalyzer** (Singleton) - 文件内容分析、重复检测
- **YSpaceAnalyzer** (Singleton) - 磁盘空间分析、趋势预测

### 3. **Management - 文件管理**

- **YFileBackup** (Singleton) - 增量备份、版本管理
- **YFileCompressor** (Singleton) - ZIP压缩、批量压缩

### 4. **Security - 安全功能**

- **YFileEncryption** (Singleton) - AES文件加密
- **YFileValidator** (Singleton) - 文件完整性验证
- **YSecureDelete** (Singleton) - 安全文件删除

### 5. **Monitoring - 监控系统**

- **YFileWatcher** (Singleton) - 实时文件系统监控
- **YPerformanceMonitor** (Singleton) - 操作性能监控

### 6. **Processing - 批量处理**

- **YBatchProcessor** (Scoped) - 高并发批量文件操作

### 7. **Formats - 配置处理**

- **YConfigProcessor** (Singleton) - JSON/XML/INI配置文件处理

## 🔧 快速开始

### 安装

```bash
# 通过 NuGet 安装
Install-Package Zylo.YIO

# 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### 服务注册

```csharp
using Zylo.YIO;

// 在 Program.cs 或 Startup.cs 中注册所有服务
builder.Services.AddALL();

// 或者手动注册特定服务
builder.Services.AddSingleton<IYFileOperations, YFileOperations>();
builder.Services.AddSingleton<IYPerformanceMonitor, YPerformanceMonitor>();
```

### 基础使用

```csharp
// 1. 文件操作
var fileOps = new YFileOperations();
await fileOps.CopyFileAsync("source.txt", "target.txt");

// 2. 使用扩展方法
"source.txt".CopyTo("target.txt");

// 3. 批量处理
var batchProcessor = new YBatchProcessor();
var copyTasks = new List<FileCopyPair> {
    new FileCopyPair { SourcePath = "file1.txt", TargetPath = "copy1.txt" },
    new FileCopyPair { SourcePath = "file2.txt", TargetPath = "copy2.txt" }
};
var result = await batchProcessor.BatchCopyFilesAsync(copyTasks);

// 4. 文件监控
var fileWatcher = new YFileWatcher();
fileWatcher.FileCreated += (sender, e) => Console.WriteLine($"文件创建: {e.FullPath}");
fileWatcher.StartWatching(@"C:\MyFolder");

// 5. 性能监控
var perfMonitor = new YPerformanceMonitor();
var result = await perfMonitor.MonitorAsync("FileOperation", "test.txt", async () => {
    await File.WriteAllTextAsync("test.txt", "content");
    return "success";
});
```

## 📊 功能特性矩阵

| 模块 | 服务类 | 生命周期 | 主要功能 | 扩展方法 |
|------|--------|----------|----------|----------|
| Core | YFileOperations | Singleton | 文件CRUD、异步操作 | ✅ |
| Core | YDirectoryOperations | Singleton | 目录管理、项目创建 | ✅ |
| Analysis | YFileAnalyzer | Singleton | 文件分析、重复检测 | ✅ |
| Analysis | YSpaceAnalyzer | Singleton | 空间分析、趋势预测 | ✅ |
| Management | YFileBackup | Singleton | 备份管理、版本控制 | ✅ |
| Management | YFileCompressor | Singleton | 压缩解压、批量处理 | ✅ |
| Security | YFileEncryption | Singleton | 文件加密解密 | ✅ |
| Security | YFileValidator | Singleton | 文件验证、完整性检查 | ✅ |
| Security | YSecureDelete | Singleton | 安全删除、数据擦除 | ✅ |
| Monitoring | YFileWatcher | Singleton | 文件系统监控 | ✅ |
| Monitoring | YPerformanceMonitor | Singleton | 性能监控分析 | ✅ |
| Processing | YBatchProcessor | Scoped | 批量文件处理 | ✅ |
| Formats | YConfigProcessor | Singleton | 配置文件处理 | ✅ |

## 🧪 测试覆盖

项目包含 **160+ 个单元测试**，覆盖所有核心功能：

- ✅ **Core模块** - 文件和目录操作测试
- ✅ **Analysis模块** - 文件分析和空间分析测试  
- ✅ **Management模块** - 备份和压缩功能测试
- ✅ **Security模块** - 加密、验证、安全删除测试
- ✅ **Monitoring模块** - 文件监控和性能监控测试
- ✅ **Processing模块** - 批量处理功能测试
- ✅ **Formats模块** - 配置文件处理测试
- ✅ **Generator模块** - 代码生成器测试

```bash
# 运行所有测试
dotnet test

# 运行特定类别的测试
dotnet test --filter "Category=FileOperations"
dotnet test --filter "Category=BatchProcessor"
dotnet test --filter "Category=PerformanceMonitor"
```

## 🔄 代码生成

Zylo.YIO 使用 Zylo.AutoG 框架自动生成代码：

### YStatic 生成器

- 自动为所有服务类生成静态扩展方法
- 支持异步和同步方法的扩展
- 自动处理泛型参数和复杂类型

### YService 生成器  

- 自动生成服务接口
- 自动注册到依赖注入容器
- 统一的 `AddALL()` 注册方法

## 📈 性能特性

- **异步优先** - 所有I/O操作都支持异步
- **并发控制** - 可配置的并发级别和资源管理
- **内存优化** - 流式处理大文件，避免内存溢出
- **性能监控** - 内置性能监控和优化建议
- **缓存机制** - 智能缓存提高重复操作性能

## 🛡️ 安全特性

- **文件加密** - AES-256 加密算法
- **安全删除** - 多次覆写确保数据无法恢复
- **完整性验证** - SHA-256 哈希验证
- **权限检查** - 文件访问权限验证
- **路径安全** - 防止路径遍历攻击

## 🔧 配置选项

```csharp
// 全局配置
var config = new YIOConfig
{
    DefaultBufferSize = 8192,
    EnablePerformanceMonitoring = true,
    MaxConcurrentOperations = Environment.ProcessorCount,
    TempDirectory = Path.GetTempPath(),
    EnableLogging = true
};
```

## 📚 高级用法

### 批量文件处理

```csharp
var batchProcessor = new YBatchProcessor(maxConcurrency: 8);

// 批量压缩
var compressionTasks = files.Select(f => new CompressionTask
{
    SourcePath = f,
    TargetPath = f + ".zip",
    CompressionLevel = CompressionLevel.Optimal
});
var result = await batchProcessor.BatchCompressFilesAsync(compressionTasks);

// 进度监控
var progress = new Progress<BatchProgress>(p =>
    Console.WriteLine($"进度: {p.CompletedTasks}/{p.TotalTasks}"));
await batchProcessor.BatchCopyFilesAsync(copyTasks, progress);
```

### 文件系统监控

```csharp
var watcher = new YFileWatcher();

// 配置监控选项
watcher.Configure(new FileWatcherConfig
{
    IncludeSubdirectories = true,
    NotifyFilter = NotifyFilters.All,
    FileFilters = new[] { "*.txt", "*.log" },
    ExcludePaths = new[] { @"C:\Temp" },
    EnableBatching = true,
    BatchTimeout = TimeSpan.FromSeconds(1)
});

// 事件处理
watcher.FileCreated += (s, e) => Console.WriteLine($"创建: {e.FullPath}");
watcher.FileChanged += (s, e) => Console.WriteLine($"修改: {e.FullPath}");
watcher.BatchChanges += (s, e) => Console.WriteLine($"批量变更: {e.TotalChanges} 个文件");

watcher.StartWatching(@"C:\MyProject");
```

### 性能监控和分析

```csharp
var perfMonitor = new YPerformanceMonitor();

// 监控文件操作
var result = await perfMonitor.MonitorAsync("LargeFileCopy", "bigfile.dat", async () =>
{
    await "source.dat".CopyToAsync("target.dat");
    return "复制完成";
});

// 获取性能统计
var stats = perfMonitor.GetOperationStatistics("LargeFileCopy");
Console.WriteLine($"平均耗时: {stats.AverageDuration.TotalMilliseconds}ms");
Console.WriteLine($"成功率: {stats.SuccessRate:P2}");

// 生成性能报告
var report = await perfMonitor.GeneratePerformanceReportAsync();
Console.WriteLine($"总操作数: {report.Summary.TotalOperations}");
Console.WriteLine($"处理字节数: {report.Summary.TotalBytesProcessed:N0}");
```

### 文件加密和安全

```csharp
var encryption = new YFileEncryption();

// 加密文件
var encryptResult = await encryption.EncryptFileAsync("sensitive.txt", "encrypted.dat", "mypassword");
if (encryptResult.Success)
{
    Console.WriteLine($"文件已加密: {encryptResult.OutputPath}");
}

// 解密文件
var decryptResult = await encryption.DecryptFileAsync("encrypted.dat", "decrypted.txt", "mypassword");

// 安全删除
var secureDelete = new YSecureDelete();
await secureDelete.SecureDeleteFileAsync("sensitive.txt", overwritePasses: 3);
```

### 文件分析和重复检测

```csharp
var analyzer = new YFileAnalyzer();

// 分析文件
var analysis = await analyzer.AnalyzeFileAsync("document.pdf");
Console.WriteLine($"文件类型: {analysis.FileType}");
Console.WriteLine($"编码: {analysis.Encoding}");
Console.WriteLine($"行数: {analysis.LineCount}");

// 查找重复文件
var duplicates = await analyzer.FindDuplicateFilesAsync(@"C:\MyDocuments");
foreach (var group in duplicates)
{
    Console.WriteLine($"重复文件组 (大小: {group.FileSize} 字节):");
    foreach (var file in group.Files)
    {
        Console.WriteLine($"  - {file}");
    }
}
```

### 磁盘空间分析

```csharp
var spaceAnalyzer = new YSpaceAnalyzer();

// 分析目录空间使用
var analysis = await spaceAnalyzer.AnalyzeDirectoryAsync(@"C:\Projects");
Console.WriteLine($"总大小: {analysis.TotalSize:N0} 字节");
Console.WriteLine($"文件数: {analysis.FileCount:N0}");
Console.WriteLine($"最大文件: {analysis.LargestFile.Name} ({analysis.LargestFile.Size:N0} 字节)");

// 空间趋势预测
var prediction = await spaceAnalyzer.PredictSpaceTrendAsync("C:");
if (prediction.DaysUntilFull > 0)
{
    Console.WriteLine($"预计 {prediction.DaysUntilFull} 天后磁盘将满");
    Console.WriteLine($"预计满盘日期: {prediction.EstimatedFullDate:yyyy-MM-dd}");
}
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Zylo.AutoG](https://github.com/your-org/Zylo.AutoG) - 代码生成框架
- [Microsoft.Extensions.DependencyInjection](https://docs.microsoft.com/en-us/dotnet/core/extensions/dependency-injection) - 依赖注入
- [System.IO.Compression](https://docs.microsoft.com/en-us/dotnet/api/system.io.compression) - 压缩功能

---

**Zylo.YIO** - 让文件操作变得简单而强大！ 🚀
