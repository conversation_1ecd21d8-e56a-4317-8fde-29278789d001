using System;
using System.Collections.Generic;

namespace Zylo.YIO.Demo
{
    /// <summary>
    /// 测试类 - Formats
    /// 应该生成 Formats.json
    /// </summary>
    public class Formats
    {
        public string Name { get; set; } = "格式处理器";
        public List<string> SupportedFormats { get; set; } = new() { "JSON", "XML", "INI" };
        public bool IsEnabled { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 测试类 - Ddd
    /// 应该生成 Ddd.json
    /// </summary>
    public class Ddd
    {
        public int Id { get; set; } = 1;
        public string Description { get; set; } = "DDD 领域驱动设计";
        public Dictionary<string, object> Properties { get; set; } = new();
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 测试类 - User
    /// 应该生成 User.json
    /// </summary>
    public class User
    {
        public string Name { get; set; } = "张三";
        public int Age { get; set; } = 25;
        public string Email { get; set; } = "<EMAIL>";
        public List<string> Roles { get; set; } = new() { "User", "Admin" };
    }

    /// <summary>
    /// 测试类 - AppConfig
    /// 应该生成 AppConfig.json
    /// </summary>
    public class AppConfig
    {
        public string AppName { get; set; } = "Zylo.YIO.Demo";
        public string Version { get; set; } = "1.0.0";
        public bool DebugMode { get; set; } = true;
        public Dictionary<string, string> Settings { get; set; } = new()
        {
            ["Theme"] = "Dark",
            ["Language"] = "zh-CN"
        };
    }

    /// <summary>
    /// 测试类 - ProductInfo
    /// 应该生成 ProductInfo.json
    /// </summary>
    public class ProductInfo
    {
        public int ProductId { get; set; } = 1001;
        public string ProductName { get; set; } = "超级产品";
        public decimal Price { get; set; } = 99.99m;
        public string Category { get; set; } = "电子产品";
        public DateTime LaunchDate { get; set; } = DateTime.Now;
        public List<string> Features { get; set; } = new() { "高性能", "低功耗", "易使用" };
    }
}
