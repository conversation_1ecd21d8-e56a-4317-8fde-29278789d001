using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Xunit;
using Zylo.YIO.Analysis;
using Zylo.YIO.Config;

namespace Zylo.YIO.Tests.Analysis
{
    /// <summary>
    /// YFileAnalyzer 测试类
    /// 测试文件分析、重复检测、统计等功能
    /// </summary>
    public class YFileAnalyzerTests : IDisposable
    {
        private readonly string _testDirectory;
        private readonly YFileAnalyzer _analyzer;

        public YFileAnalyzerTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileAnalyzerTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);
            _analyzer = new YFileAnalyzer(new YIOConfig());
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 文件分析测试

        [Fact]
        [Trait("Category", "FileAnalysis")]
        public void AnalyzeFile_WithValidFile_ShouldReturnCorrectInfo()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test.txt");
            var content = "Hello, World! This is a test file.";
            File.WriteAllText(testFile, content);

            // Act
            var result = _analyzer.AnalyzeFile(testFile);

            // Assert
            Assert.Equal(testFile, result.FilePath);
            Assert.Equal(content.Length, result.FileSize);
            Assert.Equal(".txt", result.Extension);
            Assert.False(string.IsNullOrEmpty(result.Hash));
            Assert.Equal("text/plain", result.MimeType);
        }

        [Fact]
        [Trait("Category", "FileAnalysis")]
        public void AnalyzeFile_WithNonExistentFile_ShouldReturnEmptyResult()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _analyzer.AnalyzeFile(nonExistentFile);

            // Assert
            Assert.Equal(nonExistentFile, result.FilePath);
            Assert.Equal(0, result.FileSize);
            Assert.True(string.IsNullOrEmpty(result.Hash));
        }

        [Fact]
        [Trait("Category", "FileAnalysis")]
        public async Task AnalyzeFileAsync_WithValidFile_ShouldReturnCorrectInfo()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "async_test.txt");
            var content = "Async test content";
            File.WriteAllText(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.Equal(testFile, result.FilePath);
            Assert.Equal(content.Length, result.FileSize);
            Assert.False(string.IsNullOrEmpty(result.Hash));
        }

        #endregion

        #region 目录分析测试

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public void AnalyzeDirectory_WithMultipleFiles_ShouldReturnStatistics()
        {
            // Arrange
            CreateTestFiles();

            // Act
            var statistics = _analyzer.AnalyzeDirectory(_testDirectory);

            // Assert
            Assert.True(statistics.TotalFiles > 0);
            Assert.True(statistics.TotalSize > 0);
            Assert.True(statistics.ExtensionCounts.Count > 0);
            Assert.True(statistics.LargestFiles.Count > 0);
        }

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public async Task AnalyzeDirectoryAsync_WithMultipleFiles_ShouldReturnStatistics()
        {
            // Arrange
            CreateTestFiles();

            // Act
            var statistics = await _analyzer.AnalyzeDirectoryAsync(_testDirectory);

            // Assert
            Assert.True(statistics.TotalFiles > 0);
            Assert.True(statistics.TotalSize > 0);
        }

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public void AnalyzeDirectory_WithNonExistentDirectory_ShouldReturnEmptyStatistics()
        {
            // Arrange
            var nonExistentDir = Path.Combine(_testDirectory, "nonexistent");

            // Act
            var statistics = _analyzer.AnalyzeDirectory(nonExistentDir);

            // Assert
            Assert.Equal(0, statistics.TotalFiles);
            Assert.Equal(0, statistics.TotalSize);
        }

        #endregion

        #region 重复文件检测测试

        [Fact]
        [Trait("Category", "DuplicateDetection")]
        public void FindDuplicateFiles_WithDuplicateFiles_ShouldDetectDuplicates()
        {
            // Arrange
            var content = "Duplicate content for testing";
            var file1 = Path.Combine(_testDirectory, "duplicate1.txt");
            var file2 = Path.Combine(_testDirectory, "duplicate2.txt");
            var file3 = Path.Combine(_testDirectory, "unique.txt");

            File.WriteAllText(file1, content);
            File.WriteAllText(file2, content); // Same content as file1
            File.WriteAllText(file3, "Different content");

            // Act
            var duplicates = _analyzer.FindDuplicateFiles(_testDirectory);

            // Assert
            Assert.Single(duplicates); // Should find one duplicate group
            var duplicateGroup = duplicates.First();
            Assert.Equal(2, duplicateGroup.FilePaths.Count); // Two files with same content
            Assert.Contains(file1, duplicateGroup.FilePaths);
            Assert.Contains(file2, duplicateGroup.FilePaths);
        }

        [Fact]
        [Trait("Category", "DuplicateDetection")]
        public void FindDuplicateFiles_WithNoDuplicates_ShouldReturnEmpty()
        {
            // Arrange
            var file1 = Path.Combine(_testDirectory, "unique1.txt");
            var file2 = Path.Combine(_testDirectory, "unique2.txt");

            File.WriteAllText(file1, "Content 1");
            File.WriteAllText(file2, "Content 2");

            // Act
            var duplicates = _analyzer.FindDuplicateFiles(_testDirectory);

            // Assert
            Assert.Empty(duplicates);
        }

        #endregion

        #region 相似文件检测测试

        [Fact]
        [Trait("Category", "SimilarFiles")]
        public void FindSimilarFiles_WithSimilarNames_ShouldDetectSimilarity()
        {
            // Arrange
            var file1 = Path.Combine(_testDirectory, "document_v1.txt");
            var file2 = Path.Combine(_testDirectory, "document_v2.txt");
            var file3 = Path.Combine(_testDirectory, "completely_different.txt");

            File.WriteAllText(file1, "Content 1");
            File.WriteAllText(file2, "Content 2");
            File.WriteAllText(file3, "Content 3");

            // Act
            var similarGroups = _analyzer.FindSimilarFiles(_testDirectory, 0.7);

            // Assert
            Assert.True(similarGroups.Count > 0);
            var firstGroup = similarGroups.First();
            Assert.Contains(file1, firstGroup);
            Assert.Contains(file2, firstGroup);
            Assert.DoesNotContain(file3, firstGroup);
        }

        #endregion

        #region 报告生成测试

        [Fact]
        [Trait("Category", "ReportGeneration")]
        public void GenerateAnalysisReport_WithValidStatistics_ShouldGenerateReport()
        {
            // Arrange
            CreateTestFiles();
            var statistics = _analyzer.AnalyzeDirectory(_testDirectory);

            // Act
            var report = _analyzer.GenerateAnalysisReport(statistics);

            // Assert
            Assert.False(string.IsNullOrEmpty(report));
            Assert.Contains("文件分析报告", report);
            Assert.Contains("基础统计", report);
            Assert.Contains("文件类型统计", report);
        }

        #endregion

        #region 辅助方法

        private void CreateTestFiles()
        {
            // 创建不同类型和大小的测试文件
            var files = new (string fileName, string content)[]
            {
                ("small.txt", "Small file content"),
                ("medium.json", "{ \"key\": \"value\", \"number\": 123, \"array\": [1, 2, 3] }"),
                ("large.xml", GenerateLargeXmlContent()),
                ("image.jpg", System.Text.Encoding.UTF8.GetString(GenerateBinaryContent(1024))), // 模拟二进制文件
                ("document.pdf", System.Text.Encoding.UTF8.GetString(GenerateBinaryContent(2048)))
            };

            foreach (var (fileName, content) in files)
            {
                var filePath = Path.Combine(_testDirectory, fileName);
                if (content.Length > 100) // 大文件用二进制写入
                {
                    File.WriteAllBytes(filePath, System.Text.Encoding.UTF8.GetBytes(content));
                }
                else
                {
                    File.WriteAllText(filePath, content);
                }
            }

            // 创建子目录
            var subDir = Path.Combine(_testDirectory, "subdirectory");
            Directory.CreateDirectory(subDir);
            File.WriteAllText(Path.Combine(subDir, "sub_file.txt"), "Subdirectory file content");
        }

        private string GenerateLargeXmlContent()
        {
            var xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<root>\n";
            for (int i = 0; i < 100; i++)
            {
                xml += $"  <item id=\"{i}\">Content {i}</item>\n";
            }
            xml += "</root>";
            return xml;
        }

        private byte[] GenerateBinaryContent(int size)
        {
            var content = new byte[size];
            var random = new Random();
            random.NextBytes(content);
            return content;
        }

        #endregion

        #region 高级分析功能测试

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithTextFile_ShouldDetectTextProperties()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "advanced_text.txt");
            var content = "Hello World!\nThis is a test file.\n这是中文内容。\n\nFourth line here.";
            await File.WriteAllTextAsync(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsTextFile);
            Assert.False(result.IsBinaryFile);
            Assert.False(result.IsCodeFile);
            Assert.Equal(5, result.LineCount);
            Assert.True(result.CharacterCount > 0);
            Assert.True(result.WordCount > 0);
            Assert.NotEmpty(result.Encoding);
            Assert.NotEmpty(result.DetectedLanguage);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithCodeFile_ShouldDetectCodeProperties()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test_code.cs");
            var content = "using System;\n\nnamespace Test\n{\n    // This is a comment\n    public class Program\n    {\n        /// <summary>\n        /// Main method\n        /// </summary>\n        public static void Main()\n        {\n            Console.WriteLine(\"Hello\");\n        }\n    }\n}";
            await File.WriteAllTextAsync(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsCodeFile);
            Assert.False(result.IsTextFile);
            Assert.Equal("C#", result.ProgrammingLanguage);
            Assert.True(result.CodeLines > 0);
            Assert.True(result.CommentLines > 0);
            Assert.True(result.BlankLines > 0);
            Assert.Equal(result.CodeLines + result.CommentLines + result.BlankLines, result.LineCount);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithImageFile_ShouldDetectImageProperties()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test_image.png");
            var pngHeader = new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D };
            await File.WriteAllBytesAsync(testFile, pngHeader);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsImageFile);
            Assert.True(result.IsBinaryFile);
            Assert.False(result.IsTextFile);
            Assert.Equal("PNG", result.ImageFormat);
            Assert.Equal("PNG图像", result.FileType);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithDocumentFile_ShouldDetectDocumentProperties()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test_doc.pdf");
            var pdfHeader = new byte[] { 0x25, 0x50, 0x44, 0x46, 0x2D, 0x31, 0x2E, 0x34 }; // %PDF-1.4
            await File.WriteAllBytesAsync(testFile, pdfHeader);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsDocumentFile);
            Assert.True(result.IsBinaryFile);
            Assert.Equal("PDF文档", result.FileType);
            Assert.Equal("application/pdf", result.MimeType);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithJavaScriptFile_ShouldDetectCorrectLanguage()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test_script.js");
            var content = "// JavaScript test file\nfunction hello() {\n    console.log('Hello World!');\n}\n\n/* Multi-line comment\n   for testing */\nhello();";
            await File.WriteAllTextAsync(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsCodeFile);
            Assert.Equal("JavaScript", result.ProgrammingLanguage);
            Assert.True(result.CommentLines >= 2); // 至少有单行和多行注释
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithPythonFile_ShouldDetectCorrectLanguage()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test_python.py");
            var content = "# Python test file\ndef hello():\n    \"\"\"Print hello message\"\"\"\n    print('Hello World!')\n\nif __name__ == '__main__':\n    hello()";
            await File.WriteAllTextAsync(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.True(result.IsCodeFile);
            Assert.Equal("Python", result.ProgrammingLanguage);
            Assert.True(result.CommentLines > 0);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithEmptyFile_ShouldHandleCorrectly()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "empty_file.txt");
            await File.WriteAllTextAsync(testFile, "");

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.Equal(0, result.FileSize);
            Assert.Equal(0, result.CharacterCount);
            Assert.Equal(0, result.WordCount);
            Assert.True(result.LineCount >= 0);
        }

        [Fact]
        [Trait("Category", "AdvancedAnalysis")]
        public async Task AnalyzeFileAsync_WithChineseContent_ShouldDetectChineseLanguage()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "chinese_content.txt");
            var content = "这是一个中文测试文件。\n包含多行中文内容。\n用于测试语言检测功能。";
            await File.WriteAllTextAsync(testFile, content);

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            Assert.True(result.AnalysisSuccess);
            Assert.Equal("zh", result.DetectedLanguage);
            Assert.True(result.CharacterCount > 0);
            Assert.Equal(3, result.LineCount);
        }

        #endregion

        #region 错误处理测试

        [Fact]
        [Trait("Category", "ErrorHandling")]
        public async Task AnalyzeFileAsync_WithNullPath_ShouldThrowException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _analyzer.AnalyzeFileAsync(null!));
        }

        [Fact]
        [Trait("Category", "ErrorHandling")]
        public async Task AnalyzeFileAsync_WithEmptyPath_ShouldThrowException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _analyzer.AnalyzeFileAsync(""));
        }

        [Fact]
        [Trait("Category", "ErrorHandling")]
        public async Task AnalyzeFileAsync_WithInvalidPath_ShouldHandleGracefully()
        {
            // Act
            var result = await _analyzer.AnalyzeFileAsync("invalid<>path.txt");

            // Assert
            Assert.False(result.AnalysisSuccess);
            Assert.NotEmpty(result.AnalysisError);
        }

        #endregion

        #region 性能测试

        [Fact]
        [Trait("Category", "Performance")]
        public async Task AnalyzeFileAsync_WithLargeFile_ShouldCompleteInReasonableTime()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "large_file.txt");
            var content = string.Join("\n", Enumerable.Range(0, 1000).Select(i => $"Line {i} with some content"));
            await File.WriteAllTextAsync(testFile, content);

            var startTime = DateTime.Now;

            // Act
            var result = await _analyzer.AnalyzeFileAsync(testFile);

            // Assert
            var duration = DateTime.Now - startTime;
            Assert.True(result.AnalysisSuccess);
            Assert.True(duration.TotalSeconds < 5); // 应该在5秒内完成
            Assert.Equal(1000, result.LineCount);
        }

        #endregion


    }
}
