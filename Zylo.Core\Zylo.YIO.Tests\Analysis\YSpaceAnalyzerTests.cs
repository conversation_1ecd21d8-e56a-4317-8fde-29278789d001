using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using Zylo.YIO.Analysis;
using Zylo.YIO.Config;

namespace Zylo.YIO.Tests.Analysis
{
    /// <summary>
    /// YSpaceAnalyzer 测试类
    /// 测试磁盘空间分析、使用率监控、趋势预测等功能
    /// </summary>
    public class YSpaceAnalyzerTests : IDisposable
    {
        private readonly string _testDirectory;
        private readonly YSpaceAnalyzer _analyzer;

        public YSpaceAnalyzerTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "YSpaceAnalyzerTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);
            _analyzer = new YSpaceAnalyzer(new YIOConfig());
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 驱动器空间分析测试

        [Fact]
        [Trait("Category", "DriveAnalysis")]
        public void GetAllDrivesSpaceInfo_ShouldReturnDriveList()
        {
            // Act
            var drives = _analyzer.GetAllDrivesSpaceInfo();

            // Assert
            Assert.NotNull(drives);
            Assert.True(drives.Count > 0);
            
            // 检查至少有一个可用的驱动器
            var readyDrives = drives.Where(d => d.IsReady).ToList();
            Assert.True(readyDrives.Count > 0);

            // 验证驱动器信息的完整性
            foreach (var drive in readyDrives)
            {
                Assert.False(string.IsNullOrEmpty(drive.DriveName));
                Assert.False(string.IsNullOrEmpty(drive.DriveType));
                Assert.True(drive.TotalSize > 0);
                Assert.True(drive.FreeSpace >= 0);
                Assert.True(drive.UsedSpace >= 0);
                Assert.True(drive.UsagePercentage >= 0 && drive.UsagePercentage <= 100);
            }
        }

        [Fact]
        [Trait("Category", "DriveAnalysis")]
        public void GetDriveSpaceInfo_WithValidDrive_ShouldReturnDriveInfo()
        {
            // Arrange
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady).ToArray();
            if (drives.Length == 0)
            {
                // 跳过测试如果没有可用驱动器
                return;
            }

            var testDrive = drives.First();

            // Act
            var driveInfo = _analyzer.GetDriveSpaceInfo(testDrive.Name);

            // Assert
            Assert.NotNull(driveInfo);
            Assert.Equal(testDrive.Name, driveInfo.DriveName);
            Assert.True(driveInfo.IsReady);
            Assert.True(driveInfo.TotalSize > 0);
        }

        [Fact]
        [Trait("Category", "DriveAnalysis")]
        public void GetDriveSpaceInfo_WithInvalidDrive_ShouldReturnNull()
        {
            // Arrange
            var invalidDriveName = "Z:\\"; // 假设Z盘不存在

            // Act
            var driveInfo = _analyzer.GetDriveSpaceInfo(invalidDriveName);

            // Assert
            Assert.Null(driveInfo);
        }

        #endregion

        #region 空间预警测试

        [Fact]
        [Trait("Category", "SpaceAlerts")]
        public void CheckSpaceAlerts_ShouldReturnAlertList()
        {
            // Act
            var alerts = _analyzer.CheckSpaceAlerts();

            // Assert
            Assert.NotNull(alerts);
            
            // 验证每个预警的完整性
            foreach (var alert in alerts)
            {
                Assert.False(string.IsNullOrEmpty(alert.DriveName));
                Assert.False(string.IsNullOrEmpty(alert.Message));
                Assert.True(alert.UsagePercentage >= 0 && alert.UsagePercentage <= 100);
                Assert.True(alert.FreeSpace >= 0);
                Assert.True(Enum.IsDefined(typeof(YSpaceAnalyzer.AlertLevel), alert.Level));
            }
        }

        [Fact]
        [Trait("Category", "SpaceAlerts")]
        public void CheckSpaceAlerts_WithCustomThresholds_ShouldUseCustomValues()
        {
            // Arrange
            var warningThreshold = 70.0;
            var criticalThreshold = 85.0;
            var emergencyThreshold = 95.0;

            // Act
            var alerts = _analyzer.CheckSpaceAlerts(warningThreshold, criticalThreshold, emergencyThreshold);

            // Assert
            Assert.NotNull(alerts);
            
            // 验证预警级别逻辑
            foreach (var alert in alerts)
            {
                if (alert.UsagePercentage >= emergencyThreshold)
                {
                    Assert.Equal(YSpaceAnalyzer.AlertLevel.Emergency, alert.Level);
                }
                else if (alert.UsagePercentage >= criticalThreshold)
                {
                    Assert.Equal(YSpaceAnalyzer.AlertLevel.Critical, alert.Level);
                }
                else if (alert.UsagePercentage >= warningThreshold)
                {
                    Assert.Equal(YSpaceAnalyzer.AlertLevel.Warning, alert.Level);
                }
                else
                {
                    Assert.Equal(YSpaceAnalyzer.AlertLevel.Info, alert.Level);
                }
            }
        }

        #endregion

        #region 目录空间分析测试

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public void AnalyzeDirectorySpace_WithValidDirectory_ShouldReturnDirectoryInfo()
        {
            // Arrange
            CreateTestDirectoryStructure();

            // Act
            var dirInfo = _analyzer.AnalyzeDirectorySpace(_testDirectory);

            // Assert
            Assert.NotNull(dirInfo);
            Assert.Equal(_testDirectory, dirInfo.DirectoryPath);
            Assert.True(dirInfo.FileCount > 0);
            Assert.True(dirInfo.TotalSize > 0);
            Assert.True(dirInfo.LargestFiles.Count > 0);
        }

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public async Task AnalyzeDirectorySpaceAsync_WithValidDirectory_ShouldReturnDirectoryInfo()
        {
            // Arrange
            CreateTestDirectoryStructure();

            // Act
            var dirInfo = await _analyzer.AnalyzeDirectorySpaceAsync(_testDirectory);

            // Assert
            Assert.NotNull(dirInfo);
            Assert.Equal(_testDirectory, dirInfo.DirectoryPath);
            Assert.True(dirInfo.FileCount > 0);
            Assert.True(dirInfo.TotalSize > 0);
        }

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public void AnalyzeDirectorySpace_WithNonExistentDirectory_ShouldReturnEmptyInfo()
        {
            // Arrange
            var nonExistentDir = Path.Combine(_testDirectory, "nonexistent");

            // Act
            var dirInfo = _analyzer.AnalyzeDirectorySpace(nonExistentDir);

            // Assert
            Assert.NotNull(dirInfo);
            Assert.Equal(nonExistentDir, dirInfo.DirectoryPath);
            Assert.Equal(0, dirInfo.FileCount);
            Assert.Equal(0, dirInfo.TotalSize);
        }

        [Fact]
        [Trait("Category", "DirectoryAnalysis")]
        public void AnalyzeDirectorySpace_WithMaxDepth_ShouldRespectDepthLimit()
        {
            // Arrange
            CreateDeepDirectoryStructure();

            // Act
            var dirInfo = _analyzer.AnalyzeDirectorySpace(_testDirectory, maxDepth: 1);

            // Assert
            Assert.NotNull(dirInfo);
            // 应该只分析到深度1，不包含更深层的子目录详情
            Assert.True(dirInfo.SubDirectories.All(sub => sub.SubDirectories.Count == 0));
        }

        #endregion

        #region 空间分析报告测试

        [Fact]
        [Trait("Category", "ReportGeneration")]
        public void GenerateSpaceAnalysisReport_WithValidPath_ShouldGenerateReport()
        {
            // Arrange
            CreateTestDirectoryStructure();

            // Act
            var report = _analyzer.GenerateSpaceAnalysisReport(_testDirectory);

            // Assert
            Assert.NotNull(report);
            Assert.True(report.Drives.Count > 0);
            Assert.True(report.LargestDirectories.Count > 0);
            Assert.True(report.TotalAnalyzedFiles > 0);
            Assert.True(report.TotalAnalyzedSize > 0);
            Assert.True(report.Recommendations.Count >= 0);
        }

        [Fact]
        [Trait("Category", "ReportGeneration")]
        public void GenerateSpaceReportText_WithValidReport_ShouldGenerateText()
        {
            // Arrange
            CreateTestDirectoryStructure();
            var report = _analyzer.GenerateSpaceAnalysisReport(_testDirectory);

            // Act
            var reportText = _analyzer.GenerateSpaceReportText(report);

            // Assert
            Assert.False(string.IsNullOrEmpty(reportText));
            Assert.Contains("磁盘空间分析报告", reportText);
            Assert.Contains("驱动器空间信息", reportText);
            Assert.Contains("分析摘要", reportText);
        }

        #endregion

        #region 趋势预测测试

        [Fact]
        [Trait("Category", "TrendPrediction")]
        public void PredictSpaceTrend_WithValidData_ShouldReturnPrediction()
        {
            // Arrange
            var drives = DriveInfo.GetDrives().Where(d => d.IsReady).ToArray();
            if (drives.Length == 0) return; // 跳过测试如果没有可用驱动器

            var testDrive = drives.First();
            var historicalData = new Dictionary<DateTime, long>
            {
                { DateTime.Now.AddDays(-30), 1000000000L }, // 30天前
                { DateTime.Now.AddDays(-20), 1100000000L }, // 20天前
                { DateTime.Now.AddDays(-10), 1200000000L }, // 10天前
                { DateTime.Now, 1300000000L }               // 现在
            };

            // Act
            var prediction = _analyzer.PredictSpaceTrend(testDrive.Name, historicalData);

            // Assert
            Assert.NotNull(prediction);
            Assert.Equal(testDrive.Name, prediction.DriveName);
            Assert.True(prediction.DailyGrowthRate > 0);
            Assert.True(prediction.FuturePredictions.Count > 0);
        }

        [Fact]
        [Trait("Category", "TrendPrediction")]
        public void PredictSpaceTrend_WithInsufficientData_ShouldReturnError()
        {
            // Arrange
            var historicalData = new Dictionary<DateTime, long>
            {
                { DateTime.Now, 1000000000L } // 只有一个数据点
            };

            // Act
            var prediction = _analyzer.PredictSpaceTrend("C:\\", historicalData);

            // Assert
            Assert.NotNull(prediction);
            Assert.False(string.IsNullOrEmpty(prediction.ErrorMessage));
        }

        #endregion

        #region 辅助方法

        private void CreateTestDirectoryStructure()
        {
            // 创建测试文件
            var files = new[]
            {
                ("small.txt", GenerateContent(100)),
                ("medium.json", GenerateContent(1000)),
                ("large.xml", GenerateContent(10000)),
                ("binary.dat", GenerateContent(5000))
            };

            foreach (var (fileName, content) in files)
            {
                var filePath = Path.Combine(_testDirectory, fileName);
                File.WriteAllText(filePath, content);
            }

            // 创建子目录
            var subDir = Path.Combine(_testDirectory, "subdirectory");
            Directory.CreateDirectory(subDir);
            File.WriteAllText(Path.Combine(subDir, "sub_file.txt"), GenerateContent(2000));
        }

        private void CreateDeepDirectoryStructure()
        {
            var currentDir = _testDirectory;
            
            // 创建3层深的目录结构
            for (int i = 1; i <= 3; i++)
            {
                currentDir = Path.Combine(currentDir, $"level{i}");
                Directory.CreateDirectory(currentDir);
                File.WriteAllText(Path.Combine(currentDir, $"file{i}.txt"), $"Content at level {i}");
            }
        }

        private string GenerateContent(int size)
        {
            var content = new System.Text.StringBuilder();
            var baseText = "This is test content for file size testing. ";
            
            while (content.Length < size)
            {
                content.Append(baseText);
            }
            
            return content.ToString().Substring(0, Math.Min(size, content.Length));
        }

        #endregion
    }
}
