# Zylo.YIO.Core 单元测试

## 📋 **概述**

本文件夹包含 Zylo.YIO.Core 命名空间中所有核心组件的单元测试，确保核心功能的稳定性和可靠性。

## 🧪 **测试覆盖范围**

### **YDirectoryOperations 测试** (`YDirectoryOperationsTests.cs`)

#### **基础目录操作测试**
- ✅ **目录创建功能**: `CreateDirectory`, `CreateDirectoryRecursive`, `CreateIfNotExists`
- ✅ **目录存在性检查**: `DirectoryExists` 各种场景测试
- ✅ **临时目录管理**: `CreateTempDirectory` 唯一性和清理测试
- ✅ **目录删除功能**: `DeleteDirectory`, `DeleteDirectoryRecursive`, `DeleteEmptyDirectory`
- ✅ **内容管理**: `DeleteDirectoryContents` 保留目录但清空内容

#### **高级目录操作测试**
- ✅ **目录复制功能**: `CopyDirectory` 递归复制和内容验证
- ✅ **目录移动功能**: `MoveDirectory` 移动后源目录清理验证
- ✅ **目录重命名**: `RenameDirectory` 重命名和路径更新验证

#### **路径处理功能测试**
- ✅ **路径组合**: `CombinePaths` 多路径组合和空值处理
- ✅ **路径规范化**: `NormalizePath` 混合分隔符和格式标准化
- ✅ **相对路径计算**: `GetRelativePath` 基础路径和目标路径的相对关系
- ✅ **批量目录创建**: `CreateDirectoryStructure` 批量创建和结构验证

#### **项目模板创建测试**
- ✅ **Web应用结构**: `ProjectType.WebApp` - Controllers, Views, Models, wwwroot 等
- ✅ **控制台应用结构**: `ProjectType.ConsoleApp` - Models, Services, Utils, Config 等
- ✅ **类库结构**: `ProjectType.Library` - Models, Services, Interfaces, Extensions 等
- ✅ **桌面应用结构**: `ProjectType.Desktop` - Views, ViewModels, Models, Services 等

#### **目录维护功能测试**
- ✅ **空目录清理**: `CleanupEmptyDirectories` 递归清理空目录但保留非空目录

#### **目录同步功能测试**
- ✅ **基础同步**: `SynchronizeDirectories` 文件复制和统计验证
- ✅ **增量同步**: 跳过未变更文件的智能同步
- ✅ **完整同步**: `deleteExtra=true` 删除目标目录多余文件
- ✅ **同步结果**: `DirectorySyncResult` 详细统计信息验证

#### **目录可视化功能测试**
- ✅ **目录树生成**: `GetDirectoryTree` 文本格式目录结构展示
- ✅ **深度限制**: `maxDepth` 参数控制遍历深度
- ✅ **结构验证**: 目录和文件在树形结构中的正确显示

#### **WPF 绑定功能测试**
- ✅ **WPF数据生成**: `GetDirectoryTreeForWPF` 层次化数据结构
- ✅ **图标系统**: Unicode 图标字符的正确映射
- ✅ **文件过滤**: `includeFiles` 参数控制文件显示
- ✅ **节点属性**: `DirectoryTreeNode` 所有属性的正确设置
- ✅ **错误处理**: 不存在目录的优雅处理

#### **参数验证测试**
- ✅ **空值处理**: null 和空字符串参数的安全处理
- ✅ **无效路径**: 格式错误路径的错误处理
- ✅ **边界条件**: 各种边界情况的稳定性测试

## 🎯 **测试特点**

### **测试设计原则**
- **隔离性**: 每个测试使用独立的临时目录，避免相互影响
- **自清理**: 测试完成后自动清理临时文件和目录
- **全覆盖**: 覆盖所有公共方法和重要的私有逻辑
- **边界测试**: 包含正常情况、边界情况和异常情况

### **测试组织结构**
```csharp
YDirectoryOperationsTests
├── #region 测试设置和清理
├── #region 目录创建和存在性检查测试
├── #region 目录删除操作测试  
├── #region 目录复制和移动测试
├── #region 路径处理功能测试
├── #region 项目模板创建测试
├── #region 目录维护功能测试
├── #region 目录同步功能测试
├── #region 目录可视化功能测试
├── #region WPF 绑定功能测试
└── #region 参数验证测试
```

### **测试数据管理**
- **临时目录**: 使用 `Path.GetTempPath()` 创建隔离的测试环境
- **唯一标识**: 每个测试实例使用 `Guid` 确保目录唯一性
- **自动清理**: 实现 `IDisposable` 接口，测试完成后自动清理
- **路径管理**: `GetTestDirectoryPath()` 方法统一管理测试路径

## 🚀 **运行测试**

### **命令行运行**
```bash
# 运行所有 Core 测试
dotnet test Zylo.YIO.Tests --filter "FullyQualifiedName~Zylo.YIO.Tests.Core"

# 运行特定测试类
dotnet test Zylo.YIO.Tests --filter "FullyQualifiedName~YDirectoryOperationsTests"

# 运行特定测试方法
dotnet test Zylo.YIO.Tests --filter "Method=CreateDirectory_ShouldCreateNewDirectory_WhenPathIsValid"
```

### **IDE 运行**
- **Visual Studio**: 测试资源管理器中按类别运行
- **Rider**: 单元测试窗口中选择性运行
- **VS Code**: Test Explorer 扩展支持

## 📊 **测试统计**

- **测试方法总数**: 40+ 个测试方法
- **功能覆盖率**: 100% 公共方法覆盖
- **场景覆盖**: 正常、边界、异常情况全覆盖
- **代码覆盖率**: 目标 95%+ 代码覆盖率

## 🛡️ **质量保证**

### **测试质量标准**
- ✅ **AAA 模式**: Arrange-Act-Assert 清晰的测试结构
- ✅ **描述性命名**: 测试方法名清楚描述测试场景
- ✅ **完整断言**: 验证所有相关的输出和副作用
- ✅ **错误处理**: 测试异常情况和错误恢复

### **持续集成**
- **自动运行**: CI/CD 管道中自动执行所有测试
- **失败报告**: 测试失败时提供详细的错误信息
- **性能监控**: 监控测试执行时间，确保测试效率

## 📝 **维护指南**

### **添加新测试**
1. 按功能模块添加到相应的 `#region` 区域
2. 遵循现有的命名约定和测试结构
3. 确保测试的隔离性和自清理
4. 添加详细的 XML 注释说明测试目的

### **测试更新**
- 当 YDirectoryOperations 添加新功能时，同步添加对应测试
- 当修改现有功能时，更新相关测试用例
- 定期审查测试覆盖率，补充缺失的测试场景

这个测试套件确保了 YDirectoryOperations 的所有功能都经过充分验证，为代码质量和稳定性提供了强有力的保障。
