using System;
using System.IO;
using System.Collections.Generic;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests.Core;

/// <summary>
/// YDirectory 单元测试
/// 测试目录创建、删除、复制、移动等核心功能
/// 按功能模块分类组织测试用例
/// </summary>
public class YDirectoryTests : IDisposable
{
    #region 测试设置和清理

    private readonly YDirectory _dirOps;
    private readonly string _testBaseDirectory;
    private readonly List<string> _testDirectories;

    public YDirectoryTests()
    {
        _dirOps = new YDirectory();
        _testBaseDirectory = Path.Combine(Path.GetTempPath(), "YIODirTests", Guid.NewGuid().ToString());
        _testDirectories = new List<string>();

        // 创建基础测试目录
        Directory.CreateDirectory(_testBaseDirectory);
    }

    public void Dispose()
    {
        // 清理测试目录
        try
        {
            if (Directory.Exists(_testBaseDirectory))
            {
                Directory.Delete(_testBaseDirectory, true);
            }
        }
        catch
        {
            // 忽略清理错误
        }
    }

    private string GetTestDirectoryPath(string dirName)
    {
        var dirPath = Path.Combine(_testBaseDirectory, dirName);
        _testDirectories.Add(dirPath);
        return dirPath;
    }

    #endregion

    #region 目录创建和存在性检查测试

    /// <summary>
    /// 测试创建新目录功能
    /// </summary>
    [Fact]
    public void CreateDirectory_ShouldCreateNewDirectory_WhenPathIsValid()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("test_create_dir");

        // Act
        var result = _dirOps.CreateDirectory(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeTrue();
    }

    /// <summary>
    /// 测试创建已存在目录的处理
    /// </summary>
    [Fact]
    public void CreateDirectory_ShouldReturnTrue_WhenDirectoryAlreadyExists()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("existing_dir");
        Directory.CreateDirectory(dirPath);

        // Act
        var result = _dirOps.CreateDirectory(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeTrue();
    }

    /// <summary>
    /// 测试递归创建目录功能
    /// </summary>
    [Fact]
    public void CreateDirectoryRecursive_ShouldCreateNestedDirectories()
    {
        // Arrange
        var nestedPath = GetTestDirectoryPath("level1/level2/level3");

        // Act
        var result = _dirOps.CreateDirectoryRecursive(nestedPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(nestedPath).Should().BeTrue();
        Directory.Exists(Path.GetDirectoryName(nestedPath)).Should().BeTrue();
    }

    /// <summary>
    /// 测试目录存在性检查
    /// </summary>
    [Fact]
    public void DirectoryExists_ShouldReturnTrue_WhenDirectoryExists()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("exists_test");
        Directory.CreateDirectory(dirPath);

        // Act
        var result = _dirOps.DirectoryExists(dirPath);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试不存在目录的检查
    /// </summary>
    [Fact]
    public void DirectoryExists_ShouldReturnFalse_WhenDirectoryDoesNotExist()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("non_existing");

        // Act
        var result = _dirOps.DirectoryExists(dirPath);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 测试条件创建目录功能
    /// </summary>
    [Fact]
    public void CreateIfNotExists_ShouldCreateDirectory_WhenNotExists()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("conditional_create");

        // Act
        var result = _dirOps.CreateIfNotExists(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeTrue();
    }

    /// <summary>
    /// 测试条件创建已存在目录的处理
    /// </summary>
    [Fact]
    public void CreateIfNotExists_ShouldReturnTrue_WhenDirectoryExists()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("conditional_existing");
        Directory.CreateDirectory(dirPath);

        // Act
        var result = _dirOps.CreateIfNotExists(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeTrue();
    }

    /// <summary>
    /// 测试创建临时目录功能
    /// </summary>
    [Fact]
    public void CreateTempDirectory_ShouldCreateUniqueDirectory()
    {
        // Act
        var tempDir1 = _dirOps.CreateTempDirectory("test");
        var tempDir2 = _dirOps.CreateTempDirectory("test");

        // Assert
        tempDir1.Should().NotBeEmpty();
        tempDir2.Should().NotBeEmpty();
        tempDir1.Should().NotBe(tempDir2); // 应该是不同的目录
        Directory.Exists(tempDir1).Should().BeTrue();
        Directory.Exists(tempDir2).Should().BeTrue();

        // Cleanup
        Directory.Delete(tempDir1, true);
        Directory.Delete(tempDir2, true);
    }

    #endregion

    #region 目录删除操作测试

    /// <summary>
    /// 测试删除空目录功能
    /// </summary>
    [Fact]
    public void DeleteDirectory_ShouldDeleteEmptyDirectory()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("delete_empty");
        Directory.CreateDirectory(dirPath);

        // Act
        var result = _dirOps.DeleteDirectory(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeFalse();
    }

    /// <summary>
    /// 测试递归删除目录功能
    /// </summary>
    [Fact]
    public void DeleteDirectoryRecursive_ShouldDeleteDirectoryWithContents()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("delete_recursive");
        var subDir = Path.Combine(dirPath, "subdir");
        var testFile = Path.Combine(dirPath, "test.txt");

        Directory.CreateDirectory(dirPath);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(testFile, "test content");

        // Act
        var result = _dirOps.DeleteDirectoryRecursive(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeFalse();
    }

    /// <summary>
    /// 测试删除不存在目录的处理
    /// </summary>
    [Fact]
    public void DeleteDirectory_ShouldReturnTrue_WhenDirectoryDoesNotExist()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("non_existing_delete");

        // Act
        var result = _dirOps.DeleteDirectory(dirPath);

        // Assert
        result.Should().BeTrue(); // 目录不存在视为删除成功
    }

    /// <summary>
    /// 测试删除空目录功能
    /// </summary>
    [Fact]
    public void DeleteEmptyDirectory_ShouldDeleteOnlyEmptyDirectory()
    {
        // Arrange
        var emptyDir = GetTestDirectoryPath("empty_delete");
        var nonEmptyDir = GetTestDirectoryPath("non_empty_delete");

        Directory.CreateDirectory(emptyDir);
        Directory.CreateDirectory(nonEmptyDir);
        File.WriteAllText(Path.Combine(nonEmptyDir, "file.txt"), "content");

        // Act
        var emptyResult = _dirOps.DeleteEmptyDirectory(emptyDir);
        var nonEmptyResult = _dirOps.DeleteEmptyDirectory(nonEmptyDir);

        // Assert
        emptyResult.Should().BeTrue();
        Directory.Exists(emptyDir).Should().BeFalse();

        nonEmptyResult.Should().BeFalse();
        Directory.Exists(nonEmptyDir).Should().BeTrue();
    }

    /// <summary>
    /// 测试删除目录内容但保留目录功能
    /// </summary>
    [Fact]
    public void DeleteDirectoryContents_ShouldDeleteContentsButKeepDirectory()
    {
        // Arrange
        var dirPath = GetTestDirectoryPath("delete_contents");
        var subDir = Path.Combine(dirPath, "subdir");
        var testFile = Path.Combine(dirPath, "test.txt");

        Directory.CreateDirectory(dirPath);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(testFile, "test content");

        // Act
        var result = _dirOps.DeleteDirectoryContents(dirPath);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(dirPath).Should().BeTrue(); // 目录本身应该存在
        Directory.Exists(subDir).Should().BeFalse(); // 子目录应该被删除
        File.Exists(testFile).Should().BeFalse(); // 文件应该被删除
        Directory.GetFileSystemEntries(dirPath).Should().BeEmpty(); // 目录应该为空
    }

    #endregion

    #region 目录复制和移动测试

    /// <summary>
    /// 测试目录复制功能
    /// </summary>
    [Fact]
    public void CopyDirectory_ShouldCopyDirectoryAndContents()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("copy_source");
        var destDir = GetTestDirectoryPath("copy_dest");
        var testFile = Path.Combine(sourceDir, "test.txt");
        var testContent = "test content for copy";

        Directory.CreateDirectory(sourceDir);
        File.WriteAllText(testFile, testContent);

        // Act
        var result = _dirOps.CopyDirectory(sourceDir, destDir);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(destDir).Should().BeTrue();
        File.Exists(Path.Combine(destDir, "test.txt")).Should().BeTrue();
        File.ReadAllText(Path.Combine(destDir, "test.txt")).Should().Be(testContent);
        Directory.Exists(sourceDir).Should().BeTrue(); // 源目录应该仍然存在
    }

    /// <summary>
    /// 测试递归复制目录功能
    /// </summary>
    [Fact]
    public void CopyDirectory_ShouldCopySubdirectoriesRecursively()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("copy_recursive_source");
        var destDir = GetTestDirectoryPath("copy_recursive_dest");
        var subDir = Path.Combine(sourceDir, "subdir");
        var subFile = Path.Combine(subDir, "subfile.txt");

        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(subFile, "sub content");

        // Act
        var result = _dirOps.CopyDirectory(sourceDir, destDir, recursive: true);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.Combine(destDir, "subdir")).Should().BeTrue();
        File.Exists(Path.Combine(destDir, "subdir", "subfile.txt")).Should().BeTrue();
    }

    /// <summary>
    /// 测试目录移动功能
    /// </summary>
    [Fact]
    public void MoveDirectory_ShouldMoveDirectoryToNewLocation()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("move_source");
        var destDir = GetTestDirectoryPath("move_dest");
        var testFile = Path.Combine(sourceDir, "test.txt");
        var testContent = "test content for move";

        Directory.CreateDirectory(sourceDir);
        File.WriteAllText(testFile, testContent);

        // Act
        var result = _dirOps.MoveDirectory(sourceDir, destDir);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(destDir).Should().BeTrue();
        File.Exists(Path.Combine(destDir, "test.txt")).Should().BeTrue();
        File.ReadAllText(Path.Combine(destDir, "test.txt")).Should().Be(testContent);
        Directory.Exists(sourceDir).Should().BeFalse(); // 源目录应该不存在
    }

    /// <summary>
    /// 测试目录重命名功能
    /// </summary>
    [Fact]
    public void RenameDirectory_ShouldRenameDirectorySuccessfully()
    {
        // Arrange
        var originalDir = GetTestDirectoryPath("original_name");
        var newName = "new_name";
        var expectedNewPath = Path.Combine(_testBaseDirectory, newName);
        var testFile = Path.Combine(originalDir, "test.txt");

        Directory.CreateDirectory(originalDir);
        File.WriteAllText(testFile, "test content");

        // Act
        var result = _dirOps.RenameDirectory(originalDir, newName);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(expectedNewPath).Should().BeTrue();
        File.Exists(Path.Combine(expectedNewPath, "test.txt")).Should().BeTrue();
        Directory.Exists(originalDir).Should().BeFalse();
    }

    #endregion

    #region 参数验证测试

    /// <summary>
    /// 测试空路径参数的处理 - 应该抛出异常
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void CreateDirectory_ShouldThrowException_ForInvalidPath(string invalidPath)
    {
        // Act & Assert
        var action = () => _dirOps.CreateDirectory(invalidPath);
        action.Should().Throw<ArgumentException>()
            .WithMessage("目录路径不能为空或仅包含空白字符*");
    }

    /// <summary>
    /// 测试空路径参数的存在性检查
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void DirectoryExists_ShouldReturnFalse_ForInvalidPath(string invalidPath)
    {
        // Act
        var result = _dirOps.DirectoryExists(invalidPath);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 测试null路径参数的处理 - 应该抛出异常
    /// </summary>
    [Fact]
    public void CreateDirectory_ShouldThrowException_ForNullPath()
    {
        // Act & Assert
        var action = () => _dirOps.CreateDirectory(null!);
        action.Should().Throw<ArgumentException>()
            .WithMessage("目录路径不能为空或仅包含空白字符*");
    }

    /// <summary>
    /// 测试null路径参数的存在性检查
    /// </summary>
    [Fact]
    public void DirectoryExists_ShouldReturnFalse_ForNullPath()
    {
        // Act
        var result = _dirOps.DirectoryExists(null!);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region 路径处理功能测试

    /// <summary>
    /// 测试路径组合功能
    /// </summary>
    [Fact]
    public void CombinePaths_ShouldCombinePathsCorrectly()
    {
        // Act
        var result = _dirOps.CombinePaths("C:", "Projects", "MyApp", "src");

        // Assert
        result.Should().Be(Path.Combine("C:", "Projects", "MyApp", "src"));
    }

    /// <summary>
    /// 测试空路径组合的处理
    /// </summary>
    [Fact]
    public void CombinePaths_ShouldHandleEmptyPaths()
    {
        // Act
        var result1 = _dirOps.CombinePaths();
        var result2 = _dirOps.CombinePaths("", "  ", null);
        var result3 = _dirOps.CombinePaths("C:", "", "Projects");

        // Assert
        result1.Should().BeEmpty();
        result2.Should().BeEmpty();
        result3.Should().Be(Path.Combine("C:", "Projects"));
    }

    /// <summary>
    /// 测试路径规范化功能
    /// </summary>
    [Fact]
    public void NormalizePath_ShouldNormalizePathCorrectly()
    {
        // Act
        var result1 = _dirOps.NormalizePath(@"C:/Mixed\Path//Structure");
        var result2 = _dirOps.NormalizePath(@"C:\Normal\Path\");
        var result3 = _dirOps.NormalizePath("");

        // Assert
        result1.Should().Be(@"C:\Mixed\Path\Structure");
        result2.Should().Be(@"C:\Normal\Path");
        result3.Should().BeEmpty();
    }

    /// <summary>
    /// 测试相对路径计算功能
    /// </summary>
    [Fact]
    public void GetRelativePath_ShouldCalculateRelativePathCorrectly()
    {
        // Arrange
        var basePath = @"C:\Base";
        var targetPath = @"C:\Base\Sub\Dir";

        // Act
        var result = _dirOps.GetRelativePath(basePath, targetPath);

        // Assert
        result.Should().Be(@"Sub\Dir");
    }

    /// <summary>
    /// 测试批量目录结构创建
    /// </summary>
    [Fact]
    public void CreateDirectoryStructure_ShouldCreateMultipleDirectories()
    {
        // Arrange
        var baseDir = GetTestDirectoryPath("structure_test");
        var directories = new[] { "src", "docs", "tests", "config" };

        // Act
        _dirOps.CreateDirectoryStructure(baseDir, directories);

        // Assert
        foreach (var dir in directories)
        {
            Directory.Exists(Path.Combine(baseDir, dir)).Should().BeTrue();
        }
    }

    #endregion

    #region 项目模板创建测试

    /// <summary>
    /// 测试Web应用项目结构创建
    /// </summary>
    [Fact]
    public void CreateProjectStructure_ShouldCreateWebAppStructure()
    {
        // Arrange
        var projectDir = GetTestDirectoryPath("webapp_project");

        // Act
        var result = _dirOps.CreateProjectStructure(projectDir, YDirectory.ProjectType.WebApp);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Controllers")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Views")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Models")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "wwwroot")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "wwwroot", "css")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "wwwroot", "js")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Data")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Services")).Should().BeTrue();
    }

    /// <summary>
    /// 测试控制台应用项目结构创建
    /// </summary>
    [Fact]
    public void CreateProjectStructure_ShouldCreateConsoleAppStructure()
    {
        // Arrange
        var projectDir = GetTestDirectoryPath("console_project");

        // Act
        var result = _dirOps.CreateProjectStructure(projectDir, YDirectory.ProjectType.ConsoleApp);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Models")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Services")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Utils")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Config")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Data")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Logs")).Should().BeTrue();
    }

    /// <summary>
    /// 测试类库项目结构创建
    /// </summary>
    [Fact]
    public void CreateProjectStructure_ShouldCreateLibraryStructure()
    {
        // Arrange
        var projectDir = GetTestDirectoryPath("library_project");

        // Act
        var result = _dirOps.CreateProjectStructure(projectDir, YDirectory.ProjectType.Library);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Models")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Services")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Interfaces")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Extensions")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Utils")).Should().BeTrue();
    }

    /// <summary>
    /// 测试桌面应用项目结构创建
    /// </summary>
    [Fact]
    public void CreateProjectStructure_ShouldCreateDesktopStructure()
    {
        // Arrange
        var projectDir = GetTestDirectoryPath("desktop_project");

        // Act
        var result = _dirOps.CreateProjectStructure(projectDir, YDirectory.ProjectType.Desktop);

        // Assert
        result.Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Views")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "ViewModels")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Models")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Services")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Resources")).Should().BeTrue();
        Directory.Exists(Path.Combine(projectDir, "Assets")).Should().BeTrue();
    }

    #endregion

    #region 目录维护功能测试

    /// <summary>
    /// 测试清理空目录功能
    /// </summary>
    [Fact]
    public void CleanupEmptyDirectories_ShouldRemoveEmptyDirectories()
    {
        // Arrange
        var baseDir = GetTestDirectoryPath("cleanup_test");
        var emptyDir1 = Path.Combine(baseDir, "empty1");
        var emptyDir2 = Path.Combine(baseDir, "empty2");
        var nonEmptyDir = Path.Combine(baseDir, "nonempty");
        var nestedEmptyDir = Path.Combine(baseDir, "nested", "empty");

        Directory.CreateDirectory(baseDir);
        Directory.CreateDirectory(emptyDir1);
        Directory.CreateDirectory(emptyDir2);
        Directory.CreateDirectory(nonEmptyDir);
        Directory.CreateDirectory(nestedEmptyDir);

        // 在非空目录中创建文件
        File.WriteAllText(Path.Combine(nonEmptyDir, "file.txt"), "content");

        // Act
        var result = _dirOps.CleanupEmptyDirectories(baseDir);

        // Assert
        result.Should().BeGreaterThan(0); // 应该删除了一些空目录
        Directory.Exists(emptyDir1).Should().BeFalse();
        Directory.Exists(emptyDir2).Should().BeFalse();
        Directory.Exists(nonEmptyDir).Should().BeTrue(); // 非空目录应该保留
        Directory.Exists(baseDir).Should().BeTrue(); // 基础目录应该保留
    }

    #endregion

    #region 目录同步功能测试

    /// <summary>
    /// 测试基础目录同步功能
    /// </summary>
    [Fact]
    public void SynchronizeDirectories_ShouldSyncDirectoriesCorrectly()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("sync_source");
        var targetDir = GetTestDirectoryPath("sync_target");

        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(targetDir);

        // 在源目录创建测试文件
        var sourceFile1 = Path.Combine(sourceDir, "file1.txt");
        var sourceFile2 = Path.Combine(sourceDir, "file2.txt");
        File.WriteAllText(sourceFile1, "content1");
        File.WriteAllText(sourceFile2, "content2");

        // Act
        var result = _dirOps.SynchronizeDirectories(sourceDir, targetDir);

        // Assert
        result.Should().NotBeNull();
        result.CopiedFiles.Should().Be(2);
        result.UpdatedFiles.Should().Be(0);
        result.SkippedFiles.Should().Be(0);

        File.Exists(Path.Combine(targetDir, "file1.txt")).Should().BeTrue();
        File.Exists(Path.Combine(targetDir, "file2.txt")).Should().BeTrue();
        File.ReadAllText(Path.Combine(targetDir, "file1.txt")).Should().Be("content1");
        File.ReadAllText(Path.Combine(targetDir, "file2.txt")).Should().Be("content2");
    }

    /// <summary>
    /// 测试增量同步功能
    /// </summary>
    [Fact]
    public void SynchronizeDirectories_ShouldSkipUnchangedFiles()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("incremental_source");
        var targetDir = GetTestDirectoryPath("incremental_target");

        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(targetDir);

        var sourceFile = Path.Combine(sourceDir, "unchanged.txt");
        var targetFile = Path.Combine(targetDir, "unchanged.txt");

        File.WriteAllText(sourceFile, "same content");
        File.WriteAllText(targetFile, "same content");

        // 确保目标文件的修改时间不早于源文件
        File.SetLastWriteTime(targetFile, File.GetLastWriteTime(sourceFile).AddSeconds(1));

        // Act
        var result = _dirOps.SynchronizeDirectories(sourceDir, targetDir);

        // Assert
        result.Should().NotBeNull();
        result.CopiedFiles.Should().Be(0);
        result.UpdatedFiles.Should().Be(0);
        result.SkippedFiles.Should().Be(1);
    }

    /// <summary>
    /// 测试同步时删除多余文件功能
    /// </summary>
    [Fact]
    public void SynchronizeDirectories_ShouldDeleteExtraFiles_WhenDeleteExtraIsTrue()
    {
        // Arrange
        var sourceDir = GetTestDirectoryPath("delete_extra_source");
        var targetDir = GetTestDirectoryPath("delete_extra_target");

        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(targetDir);

        // 源目录只有一个文件
        File.WriteAllText(Path.Combine(sourceDir, "keep.txt"), "keep this");

        // 目标目录有两个文件
        File.WriteAllText(Path.Combine(targetDir, "keep.txt"), "old content");
        File.WriteAllText(Path.Combine(targetDir, "delete.txt"), "delete this");

        // Act
        var result = _dirOps.SynchronizeDirectories(sourceDir, targetDir, deleteExtra: true);

        // Assert
        result.Should().NotBeNull();
        result.DeletedFiles.Should().Be(1);

        File.Exists(Path.Combine(targetDir, "keep.txt")).Should().BeTrue();
        File.Exists(Path.Combine(targetDir, "delete.txt")).Should().BeFalse();
    }

    #endregion

    #region 目录可视化功能测试

    /// <summary>
    /// 测试目录树生成功能
    /// </summary>
    [Fact]
    public void GetDirectoryTree_ShouldGenerateTreeStructure()
    {
        // Arrange
        var testDir = GetTestDirectoryPath("tree_test");
        var subDir = Path.Combine(testDir, "subdir");

        Directory.CreateDirectory(testDir);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(Path.Combine(testDir, "file1.txt"), "content");
        File.WriteAllText(Path.Combine(subDir, "file2.txt"), "content");

        // Act
        var result = _dirOps.GetDirectoryTree(testDir, maxDepth: 2);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("tree_test");
        result.Should().Contain("subdir");
        result.Should().Contain("file1.txt");
        result.Should().Contain("file2.txt");
    }

    /// <summary>
    /// 测试目录树深度限制
    /// </summary>
    [Fact]
    public void GetDirectoryTree_ShouldRespectMaxDepth()
    {
        // Arrange
        var testDir = GetTestDirectoryPath("depth_test");
        var level1 = Path.Combine(testDir, "level1");
        var level2 = Path.Combine(level1, "level2");
        var level3 = Path.Combine(level2, "level3");

        Directory.CreateDirectory(testDir);
        Directory.CreateDirectory(level1);
        Directory.CreateDirectory(level2);
        Directory.CreateDirectory(level3);

        // Act - 使用 maxDepth=3 确保能看到 level2，但不能看到 level3
        var result = _dirOps.GetDirectoryTree(testDir, maxDepth: 3);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("level1");
        result.Should().Contain("level2");
        result.Should().NotContain("level3"); // 应该被深度限制排除
    }

    #endregion

    #region WPF 绑定功能测试

    /// <summary>
    /// 测试WPF目录树数据生成功能
    /// </summary>
    [Fact]
    public void GetDirectoryTreeForWPF_ShouldGenerateWPFTreeData()
    {
        // Arrange
        var testDir = GetTestDirectoryPath("wpf_test");
        var subDir = Path.Combine(testDir, "subdir");

        Directory.CreateDirectory(testDir);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(Path.Combine(testDir, "test.txt"), "content");
        File.WriteAllText(Path.Combine(testDir, "test.cs"), "code");

        // Act
        var result = _dirOps.GetDirectoryTreeForWPF(testDir, maxDepth: 2, includeFiles: true);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1); // 应该有一个根节点

        var rootNode = result.First();
        rootNode.Name.Should().Be("wpf_test");
        rootNode.IsDirectory.Should().BeTrue();
        rootNode.IconPath.Should().NotBeEmpty();
        rootNode.Children.Should().NotBeEmpty();

        // 检查子节点
        var subdirNode = rootNode.Children.FirstOrDefault(c => c.Name == "subdir");
        subdirNode.Should().NotBeNull();
        subdirNode!.IsDirectory.Should().BeTrue();

        var txtFileNode = rootNode.Children.FirstOrDefault(c => c.Name == "test.txt");
        txtFileNode.Should().NotBeNull();
        txtFileNode!.IsDirectory.Should().BeFalse();
        txtFileNode.IconPath.Should().Be("📝"); // 文本文件图标

        var csFileNode = rootNode.Children.FirstOrDefault(c => c.Name == "test.cs");
        csFileNode.Should().NotBeNull();
        csFileNode!.IsDirectory.Should().BeFalse();
        csFileNode.IconPath.Should().Be("📄"); // C# 文件图标
    }

    /// <summary>
    /// 测试WPF目录树只包含目录的情况
    /// </summary>
    [Fact]
    public void GetDirectoryTreeForWPF_ShouldExcludeFiles_WhenIncludeFilesIsFalse()
    {
        // Arrange
        var testDir = GetTestDirectoryPath("wpf_dirs_only");
        var subDir = Path.Combine(testDir, "subdir");

        Directory.CreateDirectory(testDir);
        Directory.CreateDirectory(subDir);
        File.WriteAllText(Path.Combine(testDir, "file.txt"), "content");

        // Act
        var result = _dirOps.GetDirectoryTreeForWPF(testDir, maxDepth: 2, includeFiles: false);

        // Assert
        result.Should().NotBeNull();
        result.Should().HaveCount(1);

        var rootNode = result.First();
        rootNode.Children.Should().HaveCount(1); // 只有子目录，没有文件
        rootNode.Children.First().Name.Should().Be("subdir");
        rootNode.Children.Should().NotContain(c => c.Name == "file.txt");
    }

    /// <summary>
    /// 测试WPF目录树处理不存在目录的情况
    /// </summary>
    [Fact]
    public void GetDirectoryTreeForWPF_ShouldReturnEmpty_WhenDirectoryDoesNotExist()
    {
        // Arrange
        var nonExistentDir = GetTestDirectoryPath("non_existent");

        // Act
        var result = _dirOps.GetDirectoryTreeForWPF(nonExistentDir);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    /// <summary>
    /// 测试DirectoryTreeNode的属性
    /// </summary>
    [Fact]
    public void DirectoryTreeNode_ShouldHaveCorrectProperties()
    {
        // Arrange
        var testDir = GetTestDirectoryPath("node_props_test");
        Directory.CreateDirectory(testDir);
        File.WriteAllText(Path.Combine(testDir, "large_file.txt"), new string('A', 1024)); // 1KB 文件

        // Act
        var result = _dirOps.GetDirectoryTreeForWPF(testDir, includeFiles: true);

        // Assert
        var rootNode = result.First();
        rootNode.FullPath.Should().Be(testDir);
        // 目录节点的 FormattedFileSize 可能为空，这是正常的
        rootNode.FormattedLastModified.Should().NotBeEmpty();
        rootNode.ToolTip.Should().NotBeEmpty();

        var fileNode = rootNode.Children.FirstOrDefault(c => c.Name == "large_file.txt");
        fileNode.Should().NotBeNull();
        fileNode!.FormattedFileSize.Should().NotBeEmpty(); // 文件节点应该有大小信息
        fileNode.FormattedFileSize.Should().Contain("B"); // 应该显示字节单位
    }

    #endregion
}
