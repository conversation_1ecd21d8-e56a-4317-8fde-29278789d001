using System;
using System.IO;
using System.Linq;
using Xunit;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests
{
    /// <summary>
    /// YFile 路径和文件名处理扩展功能测试
    /// 测试批量重命名、文件名规范化、唯一文件名生成、文件组织等功能
    /// </summary>
    public class YFileOperationsExtensionsTests : IDisposable
    {
        private readonly string _testDirectory;
        private readonly YFile _fileOps;

        public YFileOperationsExtensionsTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileOpsExtTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);
            _fileOps = new YFile();
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 批量重命名测试

        [Fact]
        [Trait("Category", "BatchRename")]
        public void BatchRenameFiles_WithValidPattern_ShouldRenameFiles()
        {
            // Arrange
            var testFiles = new[] { "test1.txt", "test2.txt", "test3.txt" };
            foreach (var file in testFiles)
            {
                File.WriteAllText(Path.Combine(_testDirectory, file), "test content");
            }

            // Act
            var renamedCount = _fileOps.BatchRenameFiles(_testDirectory, "*.txt", "renamed_{index}", 1);

            // Assert
            Assert.Equal(3, renamedCount);
            Assert.True(File.Exists(Path.Combine(_testDirectory, "renamed_001.txt")));
            Assert.True(File.Exists(Path.Combine(_testDirectory, "renamed_002.txt")));
            Assert.True(File.Exists(Path.Combine(_testDirectory, "renamed_003.txt")));
        }

        [Fact]
        [Trait("Category", "BatchRename")]
        public void BatchRenameFiles_WithDatePattern_ShouldIncludeDate()
        {
            // Arrange
            var testFile = "test.txt";
            File.WriteAllText(Path.Combine(_testDirectory, testFile), "test content");
            var currentDate = DateTime.Now.ToString("yyyyMMdd");

            // Act
            var renamedCount = _fileOps.BatchRenameFiles(_testDirectory, "*.txt", "file_{date}_{index}", 1);

            // Assert
            Assert.Equal(1, renamedCount);
            Assert.True(File.Exists(Path.Combine(_testDirectory, $"file_{currentDate}_001.txt")));
        }

        [Fact]
        [Trait("Category", "BatchRename")]
        public void BatchRenameFiles_WithNamePattern_ShouldPreserveOriginalName()
        {
            // Arrange
            var testFile = "original.txt";
            File.WriteAllText(Path.Combine(_testDirectory, testFile), "test content");

            // Act
            var renamedCount = _fileOps.BatchRenameFiles(_testDirectory, "*.txt", "prefix_{name}_suffix", 1);

            // Assert
            Assert.Equal(1, renamedCount);
            Assert.True(File.Exists(Path.Combine(_testDirectory, "prefix_original_suffix.txt")));
        }

        [Fact]
        [Trait("Category", "BatchRename")]
        public void BatchRenameFiles_NonexistentDirectory_ShouldReturnZero()
        {
            // Arrange
            var nonexistentDir = Path.Combine(_testDirectory, "nonexistent");

            // Act
            var renamedCount = _fileOps.BatchRenameFiles(nonexistentDir, "*.txt", "renamed_{index}", 1);

            // Assert
            Assert.Equal(0, renamedCount);
        }

        #endregion

        #region 文件名规范化测试

        [Fact]
        [Trait("Category", "FileNameNormalization")]
        public void NormalizeFileName_WithInvalidChars_ShouldReplaceChars()
        {
            // Arrange
            var invalidFileName = "test<>:\"|?*.txt";

            // Act
            var normalizedName = _fileOps.NormalizeFileName(invalidFileName);

            // Assert
            Assert.NotNull(normalizedName);
            Assert.DoesNotContain("<", normalizedName);
            Assert.DoesNotContain(">", normalizedName);
            Assert.DoesNotContain(":", normalizedName);
            Assert.DoesNotContain("\"", normalizedName);
            Assert.DoesNotContain("|", normalizedName);
            Assert.DoesNotContain("?", normalizedName);
            Assert.DoesNotContain("*", normalizedName);
        }

        [Fact]
        [Trait("Category", "FileNameNormalization")]
        public void NormalizeFileName_WithCustomReplacement_ShouldUseCustomChar()
        {
            // Arrange
            var invalidFileName = "test<file>.txt";
            var replacement = "-";

            // Act
            var normalizedName = _fileOps.NormalizeFileName(invalidFileName, replacement);

            // Assert
            Assert.NotNull(normalizedName);
            Assert.Contains("-", normalizedName);
            Assert.DoesNotContain("<", normalizedName);
            Assert.DoesNotContain(">", normalizedName);
        }

        [Fact]
        [Trait("Category", "FileNameNormalization")]
        public void NormalizeFileName_EmptyString_ShouldReturnEmpty()
        {
            // Act
            var normalizedName = _fileOps.NormalizeFileName("");

            // Assert
            Assert.Equal("", normalizedName);
        }

        [Fact]
        [Trait("Category", "FileNameNormalization")]
        public void NormalizeFileName_VeryLongName_ShouldTruncate()
        {
            // Arrange
            var longName = new string('a', 250) + ".txt";

            // Act
            var normalizedName = _fileOps.NormalizeFileName(longName);

            // Assert
            Assert.NotNull(normalizedName);
            Assert.True(normalizedName.Length <= 200, "文件名应该被截断到200字符以内");
            Assert.EndsWith(".txt", normalizedName);
        }

        #endregion

        #region 唯一文件名生成测试

        [Fact]
        [Trait("Category", "UniqueFileName")]
        public void GenerateUniqueFileName_NonexistentFile_ShouldReturnOriginal()
        {
            // Arrange
            var filePath = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var uniquePath = _fileOps.GenerateUniqueFileName(filePath);

            // Assert
            Assert.Equal(filePath, uniquePath);
        }

        [Fact]
        [Trait("Category", "UniqueFileName")]
        public void GenerateUniqueFileName_ExistingFile_ShouldReturnUniqueVersion()
        {
            // Arrange
            var originalFile = Path.Combine(_testDirectory, "existing.txt");
            File.WriteAllText(originalFile, "test content");

            // Act
            var uniquePath = _fileOps.GenerateUniqueFileName(originalFile);

            // Assert
            Assert.NotEqual(originalFile, uniquePath);
            Assert.Contains("existing (1).txt", uniquePath);
            Assert.False(File.Exists(uniquePath), "唯一文件名不应该已经存在");
        }

        [Fact]
        [Trait("Category", "UniqueFileName")]
        public void GenerateUniqueFileName_MultipleExistingFiles_ShouldIncrementNumber()
        {
            // Arrange
            var originalFile = Path.Combine(_testDirectory, "existing.txt");
            var firstCopy = Path.Combine(_testDirectory, "existing (1).txt");
            File.WriteAllText(originalFile, "test content");
            File.WriteAllText(firstCopy, "test content");

            // Act
            var uniquePath = _fileOps.GenerateUniqueFileName(originalFile);

            // Assert
            Assert.NotEqual(originalFile, uniquePath);
            Assert.Contains("existing (2).txt", uniquePath);
        }

        #endregion

        #region 文件名建议测试

        [Fact]
        [Trait("Category", "SuggestFileName")]
        public void SuggestFileName_ImageFile_ShouldReturnImagePrefix()
        {
            // Arrange
            var imageFile = Path.Combine(_testDirectory, "test.jpg");
            File.WriteAllText(imageFile, "fake image content");

            // Act
            var suggestedName = _fileOps.SuggestFileName(imageFile);

            // Assert
            Assert.NotNull(suggestedName);
            Assert.StartsWith("Image_", suggestedName);
            Assert.EndsWith(".jpg", suggestedName);
        }

        [Fact]
        [Trait("Category", "SuggestFileName")]
        public void SuggestFileName_VideoFile_ShouldReturnVideoPrefix()
        {
            // Arrange
            var videoFile = Path.Combine(_testDirectory, "test.mp4");
            File.WriteAllText(videoFile, "fake video content");

            // Act
            var suggestedName = _fileOps.SuggestFileName(videoFile);

            // Assert
            Assert.NotNull(suggestedName);
            Assert.StartsWith("Video_", suggestedName);
            Assert.EndsWith(".mp4", suggestedName);
        }

        [Fact]
        [Trait("Category", "SuggestFileName")]
        public void SuggestFileName_UnknownFile_ShouldReturnFilePrefix()
        {
            // Arrange
            var unknownFile = Path.Combine(_testDirectory, "test.unknown");
            File.WriteAllText(unknownFile, "unknown content");

            // Act
            var suggestedName = _fileOps.SuggestFileName(unknownFile);

            // Assert
            Assert.NotNull(suggestedName);
            Assert.StartsWith("File_", suggestedName);
            Assert.EndsWith(".unknown", suggestedName);
        }

        [Fact]
        [Trait("Category", "SuggestFileName")]
        public void SuggestFileName_NonexistentFile_ShouldReturnOriginalName()
        {
            // Arrange
            var nonexistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var suggestedName = _fileOps.SuggestFileName(nonexistentFile);

            // Assert
            Assert.Equal("nonexistent.txt", suggestedName);
        }

        #endregion

        #region 文件组织测试

        [Fact]
        [Trait("Category", "FileOrganization")]
        public void OrganizeFilesByType_WithMixedFiles_ShouldOrganizeCorrectly()
        {
            // Arrange
            var testFiles = new[]
            {
                ("image.jpg", "Images"),
                ("video.mp4", "Videos"),
                ("document.pdf", "Documents"),
                ("archive.zip", "Archives"),
                ("code.cs", "Code"),
                ("unknown.xyz", "Others")
            };

            foreach (var (fileName, _) in testFiles)
            {
                File.WriteAllText(Path.Combine(_testDirectory, fileName), "test content");
            }

            // Act
            var organizedCount = _fileOps.OrganizeFilesByType(_testDirectory, true);

            // Assert
            Assert.Equal(testFiles.Length, organizedCount);

            foreach (var (fileName, expectedFolder) in testFiles)
            {
                var expectedPath = Path.Combine(_testDirectory, expectedFolder, fileName);
                Assert.True(File.Exists(expectedPath), $"文件 {fileName} 应该在 {expectedFolder} 文件夹中");
            }
        }

        [Fact]
        [Trait("Category", "FileOrganization")]
        public void OrganizeFilesByType_WithoutSubfolders_ShouldNotMoveFiles()
        {
            // Arrange
            var testFile = "image.jpg";
            File.WriteAllText(Path.Combine(_testDirectory, testFile), "test content");

            // Act
            var organizedCount = _fileOps.OrganizeFilesByType(_testDirectory, false);

            // Assert
            Assert.Equal(0, organizedCount);
            Assert.True(File.Exists(Path.Combine(_testDirectory, testFile)), "文件应该保持在原位置");
        }

        #endregion

        #region 文件名清理测试

        [Fact]
        [Trait("Category", "FileNameCleanup")]
        public void CleanupFileNames_WithInvalidChars_ShouldCleanNames()
        {
            // Arrange - 创建有效的文件名，然后手动重命名来模拟无效字符
            var validFiles = new[] { "test_file_.txt", "another_file_.txt", "file_with_colons_.txt" };
            var invalidFiles = new[] { "test<file>.txt", "another|file.txt", "file:with:colons.txt" };

            // 先创建有效文件
            foreach (var fileName in validFiles)
            {
                File.WriteAllText(Path.Combine(_testDirectory, fileName), "test content");
            }

            // 手动创建一些包含特殊字符的文件名（通过先创建再重命名的方式模拟）
            // 由于Windows不允许创建包含特殊字符的文件，我们直接测试NormalizeFileName方法

            // Act - 测试文件名规范化功能
            var normalizedNames = new List<string>();
            foreach (var invalidName in invalidFiles)
            {
                var normalized = _fileOps.NormalizeFileName(invalidName);
                normalizedNames.Add(normalized);
            }

            // Assert
            Assert.All(normalizedNames, name =>
            {
                Assert.DoesNotContain("<", name);
                Assert.DoesNotContain(">", name);
                Assert.DoesNotContain("|", name);
                Assert.DoesNotContain(":", name);
            });

            // 测试实际的文件清理（使用有效文件名）
            var cleanedCount = _fileOps.CleanupFileNames(_testDirectory, false);
            Assert.Equal(0, cleanedCount); // 有效文件名不需要清理
        }

        [Fact]
        [Trait("Category", "FileNameCleanup")]
        public void CleanupFileNames_ValidNames_ShouldNotChange()
        {
            // Arrange
            var validFiles = new[] { "valid_file.txt", "another-file.txt", "normal.file.txt" };
            foreach (var fileName in validFiles)
            {
                File.WriteAllText(Path.Combine(_testDirectory, fileName), "test content");
            }

            // Act
            var cleanedCount = _fileOps.CleanupFileNames(_testDirectory, false);

            // Assert
            Assert.Equal(0, cleanedCount);

            // 验证所有文件仍然存在
            foreach (var fileName in validFiles)
            {
                Assert.True(File.Exists(Path.Combine(_testDirectory, fileName)), $"有效文件 {fileName} 应该保持不变");
            }
        }

        [Fact]
        [Trait("Category", "FileNameCleanup")]
        public void CleanupFileNames_RecursiveMode_ShouldProcessSubdirectories()
        {
            // Arrange
            var subDir = Path.Combine(_testDirectory, "subdir");
            Directory.CreateDirectory(subDir);

            // 创建有效的文件名进行测试
            var validFile = "valid_file.txt";
            File.WriteAllText(Path.Combine(subDir, validFile), "test content");

            // Act
            var cleanedCount = _fileOps.CleanupFileNames(_testDirectory, true);

            // Assert
            Assert.Equal(0, cleanedCount); // 有效文件名不需要清理
            Assert.True(File.Exists(Path.Combine(subDir, validFile)), "有效文件应该保持不变");
        }

        [Fact]
        [Trait("Category", "FileNameCleanup")]
        public void CleanupFileNames_NonexistentDirectory_ShouldReturnZero()
        {
            // Arrange
            var nonexistentDir = Path.Combine(_testDirectory, "nonexistent");

            // Act
            var cleanedCount = _fileOps.CleanupFileNames(nonexistentDir, false);

            // Assert
            Assert.Equal(0, cleanedCount);
        }

        #endregion
    }
}
