using System;
using System.IO;
using Xunit;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests.Core.YFileOperationsTest
{
    /// <summary>
    /// YFile 高级文件名处理功能测试类
    /// 
    /// 测试范围：
    /// • 查找替换文件名功能
    /// • 正则表达式重命名
    /// • 智能重命名功能
    /// • 大小写转换功能
    /// • 批量高级操作
    /// </summary>
    public class YAdvancedFileNameTests : IDisposable
    {
        private readonly YFile _fileOps;
        private readonly string _testDirectory;
        private readonly string _testFile1;
        private readonly string _testFile2;
        private readonly string _testFile3;

        public YAdvancedFileNameTests()
        {
            _fileOps = new YFile();

            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "YAdvancedTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);

            // 创建测试文件
            _testFile1 = Path.Combine(_testDirectory, "old_document_v1.txt");
            _testFile2 = Path.Combine(_testDirectory, "my_old_file.doc");
            _testFile3 = Path.Combine(_testDirectory, "123_test_file.pdf");

            File.WriteAllText(_testFile1, "Test content 1");
            File.WriteAllText(_testFile2, "Test content 2");
            File.WriteAllText(_testFile3, "Test content 3");
        }

        public void Dispose()
        {
            // 清理测试目录
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 查找替换测试

        [Fact]
        public void FindAndReplaceInFileName_ValidReplace_ShouldReplaceText()
        {
            // Arrange
            var searchText = "old";
            var replaceText = "new";

            // Act
            var result = _fileOps.FindAndReplaceInFileName(_testFile1, searchText, replaceText);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(_testFile1));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Contains("new", newFileName);
            Assert.DoesNotContain("old", newFileName);
        }

        [Fact]
        public void FindAndReplaceInFileName_IgnoreCase_ShouldReplaceIgnoringCase()
        {
            // Arrange
            var searchText = "OLD";
            var replaceText = "new";

            // Act
            var result = _fileOps.FindAndReplaceInFileName(_testFile1, searchText, replaceText, ignoreCase: true);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Contains("new", newFileName);
        }

        [Fact]
        public void FindAndReplaceInFileName_WholeWord_ShouldReplaceOnlyWholeWords()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "folder_test.txt");
            File.WriteAllText(testFile, "test");

            var searchText = "old";
            var replaceText = "new";

            // Act - 应该不匹配 "folder" 中的 "old"
            var result = _fileOps.FindAndReplaceInFileName(testFile, searchText, replaceText, wholeWord: true);

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(testFile));
        }

        [Fact]
        public void FindAndReplaceInFileName_NoMatch_ShouldReturnEmpty()
        {
            // Arrange
            var searchText = "nonexistent";
            var replaceText = "new";

            // Act
            var result = _fileOps.FindAndReplaceInFileName(_testFile1, searchText, replaceText);

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(_testFile1));
        }

        #endregion

        #region 正则表达式重命名测试

        [Fact]
        public void RenameWithRegex_ValidPattern_ShouldRenameCorrectly()
        {
            // Arrange
            var pattern = @"(\d+)_(.+)_(.+)";  // 匹配 "123_test_file"
            var replacement = "$2_$1_$3";      // 重组为 "test_123_file"

            // Act
            var result = _fileOps.RenameWithRegex(_testFile3, pattern, replacement);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(_testFile3));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.StartsWith("test_123_file", newFileName);
        }

        [Fact]
        public void RenameWithRegex_NoMatch_ShouldReturnEmpty()
        {
            // Arrange
            var pattern = @"xyz_(\w+)";  // 不匹配的模式
            var replacement = "new_$1";

            // Act
            var result = _fileOps.RenameWithRegex(_testFile1, pattern, replacement);

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(_testFile1));
        }

        [Fact]
        public void RenameWithRegex_InvalidPattern_ShouldReturnEmpty()
        {
            // Arrange
            var invalidPattern = @"[abc";  // 无效的正则表达式（未闭合的字符类）
            var replacement = "new";

            // Act
            var result = _fileOps.RenameWithRegex(_testFile1, invalidPattern, replacement);

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(_testFile1));
        }

        #endregion

        #region 智能重命名测试

        [Fact]
        public void SmartRename_FileTypeStrategy_ShouldRenameWithTypePrefix()
        {
            // Act
            var result = _fileOps.SmartRename(_testFile1, SmartNamingStrategy.FileType);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(_testFile1));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.StartsWith("Text", newFileName);
        }

        [Fact]
        public void SmartRename_WithCustomPrefix_ShouldIncludePrefix()
        {
            // Act
            var result = _fileOps.SmartRename(_testFile1, SmartNamingStrategy.FileType, customPrefix: "MyFile");

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.StartsWith("MyFile_", newFileName);
        }

        [Fact]
        public void SmartRename_SequentialStrategy_ShouldUseSequentialNumber()
        {
            // Act
            var result = _fileOps.SmartRename(_testFile1, SmartNamingStrategy.Sequential);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Contains("File_", newFileName);
        }

        #endregion

        #region 大小写转换测试

        [Fact]
        public void ConvertFileNameCase_ToLowerCase_ShouldConvertToLower()
        {
            // Arrange
            var upperCaseFile = Path.Combine(_testDirectory, "UPPERCASE_FILE.txt");
            File.WriteAllText(upperCaseFile, "test");

            // Act
            var result = _fileOps.ConvertFileNameCase(upperCaseFile, CaseStyle.LowerCase);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(upperCaseFile));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.StartsWith("uppercase_file", newFileName); // 可能会有 (1) 后缀
        }

        [Fact]
        public void ConvertFileNameCase_ToTitleCase_ShouldConvertToTitle()
        {
            // Arrange
            var lowerCaseFile = Path.Combine(_testDirectory, "my_test_file.txt");
            File.WriteAllText(lowerCaseFile, "test");

            // Act
            var result = _fileOps.ConvertFileNameCase(lowerCaseFile, CaseStyle.TitleCase);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Equal("My Test File", newFileName);
        }

        [Fact]
        public void ConvertFileNameCase_ToCamelCase_ShouldConvertToCamel()
        {
            // Arrange
            var spaceFile = Path.Combine(_testDirectory, "my test file.txt");
            File.WriteAllText(spaceFile, "test");

            // Act
            var result = _fileOps.ConvertFileNameCase(spaceFile, CaseStyle.CamelCase);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Equal("myTestFile", newFileName);
        }

        [Fact]
        public void ConvertFileNameCase_ToKebabCase_ShouldConvertToKebab()
        {
            // Arrange
            var spaceFile = Path.Combine(_testDirectory, "my test file.txt");
            File.WriteAllText(spaceFile, "test");

            // Act
            var result = _fileOps.ConvertFileNameCase(spaceFile, CaseStyle.KebabCase);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.Equal("my-test-file", newFileName);
        }

        #endregion

        #region 批量操作测试

        [Fact]
        public void BatchFindAndReplaceInFileNames_ValidReplace_ShouldProcessAllFiles()
        {
            // Arrange
            var searchText = "test";
            var replaceText = "demo";

            // Act
            var result = _fileOps.BatchFindAndReplaceInFileNames(_testDirectory, searchText, replaceText);

            // Assert
            Assert.Equal(1, result); // 只有一个文件包含 "test"

            var files = Directory.GetFiles(_testDirectory);
            var hasReplacedFile = false;

            foreach (var file in files)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.Contains("demo"))
                {
                    hasReplacedFile = true;
                    break;
                }
            }

            Assert.True(hasReplacedFile);
        }

        #endregion

        #region 边界条件测试

        [Fact]
        public void FindAndReplaceInFileName_FileNotExists_ShouldReturnEmpty()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _fileOps.FindAndReplaceInFileName(nonExistentFile, "old", "new");

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
        }

        [Fact]
        public void ConvertFileNameCase_SameCase_ShouldReturnEmpty()
        {
            // Arrange
            var lowerFile = Path.Combine(_testDirectory, "lowercase.txt");
            File.WriteAllText(lowerFile, "test");

            // Act
            var result = _fileOps.ConvertFileNameCase(lowerFile, CaseStyle.LowerCase);

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(lowerFile));
        }

        #endregion
    }
}
