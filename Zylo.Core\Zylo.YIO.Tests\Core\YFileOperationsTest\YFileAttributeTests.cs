using System;
using System.IO;
using Xunit;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests.Core.YFileOperationsTest
{
    /// <summary>
    /// YFile 文件属性修改功能测试类
    ///
    /// 测试范围：
    /// • 只读属性设置和取消
    /// • 隐藏属性设置和取消
    /// • 系统属性设置和取消
    /// • 存档属性设置和取消
    /// • 文件时间戳修改
    /// • 通用属性修改方法
    /// • 批量属性修改
    /// </summary>
    public class YFileAttributeTests : IDisposable
    {
        private readonly YFile _fileOps;
        private readonly string _testDirectory;
        private readonly string _testFile;

        public YFileAttributeTests()
        {
            _fileOps = new YFile();

            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileAttrTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);

            // 创建测试文件
            _testFile = Path.Combine(_testDirectory, "testfile.txt");
            File.WriteAllText(_testFile, "Test content for attribute testing");

            // 确保文件是正常状态
            File.SetAttributes(_testFile, FileAttributes.Normal);
        }

        public void Dispose()
        {
            // 清理测试目录（先重置文件属性以确保能删除）
            if (Directory.Exists(_testDirectory))
            {
                var files = Directory.GetFiles(_testDirectory, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    try
                    {
                        File.SetAttributes(file, FileAttributes.Normal);
                    }
                    catch { /* 忽略错误 */ }
                }
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 只读属性测试

        [Fact]
        public void SetReadOnlyAttribute_SetTrue_ShouldMakeFileReadOnly()
        {
            // Act
            var result = _fileOps.SetReadOnlyAttribute(_testFile, true);

            // Assert
            Assert.True(result);
            Assert.True(_fileOps.IsReadOnly(_testFile));

            var attributes = File.GetAttributes(_testFile);
            Assert.True((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly);
        }

        [Fact]
        public void SetReadOnlyAttribute_SetFalse_ShouldRemoveReadOnly()
        {
            // Arrange - 先设置为只读
            _fileOps.SetReadOnlyAttribute(_testFile, true);
            Assert.True(_fileOps.IsReadOnly(_testFile));

            // Act
            var result = _fileOps.SetReadOnlyAttribute(_testFile, false);

            // Assert
            Assert.True(result);
            Assert.False(_fileOps.IsReadOnly(_testFile));

            var attributes = File.GetAttributes(_testFile);
            Assert.False((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly);
        }

        #endregion

        #region 隐藏属性测试

        [Fact]
        public void SetHiddenAttribute_SetTrue_ShouldMakeFileHidden()
        {
            // Act
            var result = _fileOps.SetHiddenAttribute(_testFile, true);

            // Assert
            Assert.True(result);
            Assert.True(_fileOps.IsHidden(_testFile));

            var attributes = File.GetAttributes(_testFile);
            Assert.True((attributes & FileAttributes.Hidden) == FileAttributes.Hidden);
        }

        #endregion

        #region 时间戳修改测试

        [Fact]
        public void SetFileTimestamps_ValidDates_ShouldUpdateTimestamps()
        {
            // Arrange
            var newCreationTime = new DateTime(2023, 1, 1, 10, 0, 0);
            var newWriteTime = new DateTime(2023, 6, 15, 14, 30, 0);
            var newAccessTime = new DateTime(2023, 12, 31, 23, 59, 59);

            // Act
            var result = _fileOps.SetFileTimestamps(_testFile, newCreationTime, newWriteTime, newAccessTime);

            // Assert
            Assert.True(result);

            Assert.Equal(newCreationTime, File.GetCreationTime(_testFile));
            Assert.Equal(newWriteTime, File.GetLastWriteTime(_testFile));
            Assert.Equal(newAccessTime, File.GetLastAccessTime(_testFile));
        }

        #endregion

        #region 通用属性修改测试

        [Fact]
        public void ModifyFileAttributes_AddAndRemove_ShouldWorkCorrectly()
        {
            // Arrange - 先设置一些属性
            File.SetAttributes(_testFile, FileAttributes.ReadOnly | FileAttributes.Hidden);

            // Act - 移除只读，添加存档
            var result = _fileOps.ModifyFileAttributes(_testFile,
                FileAttributes.Archive,
                FileAttributes.ReadOnly);

            // Assert
            Assert.True(result);

            var attributes = File.GetAttributes(_testFile);
            Assert.False((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly);
            Assert.True((attributes & FileAttributes.Hidden) == FileAttributes.Hidden);
            Assert.True((attributes & FileAttributes.Archive) == FileAttributes.Archive);
        }

        #endregion

        #region 边界条件测试

        [Fact]
        public void SetReadOnlyAttribute_FileNotExists_ShouldReturnFalse()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _fileOps.SetReadOnlyAttribute(nonExistentFile, true);

            // Assert
            Assert.False(result);
        }

        #endregion
    }
}
