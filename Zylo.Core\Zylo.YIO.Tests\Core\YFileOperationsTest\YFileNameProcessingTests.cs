using System;
using System.IO;
using Xunit;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests.Core.YFileOperationsTest
{
    /// <summary>
    /// YFile 文件名处理功能测试类
    ///
    /// 测试范围：
    /// • 文件名前缀添加和移除
    /// • 文件名后缀添加和移除
    /// • 批量文件名处理
    /// • 边界条件和异常处理
    /// </summary>
    public class YFileNameProcessingTests : IDisposable
    {
        private readonly YFile _fileOps;
        private readonly string _testDirectory;
        private readonly string _testFile1;
        private readonly string _testFile2;

        public YFileNameProcessingTests()
        {
            _fileOps = new YFile();

            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileOpsTest_" + Guid.NewGuid().ToString("N")[..8]);
            Directory.CreateDirectory(_testDirectory);

            // 创建测试文件
            _testFile1 = Path.Combine(_testDirectory, "test1.txt");
            _testFile2 = Path.Combine(_testDirectory, "test2.doc");

            File.WriteAllText(_testFile1, "Test content 1");
            File.WriteAllText(_testFile2, "Test content 2");
        }

        public void Dispose()
        {
            // 清理测试目录
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 前缀处理测试

        [Fact]
        public void AddPrefixToFileName_ValidInput_ShouldAddPrefix()
        {
            // Arrange
            var prefix = "backup";
            var separator = "_";

            // Act
            var result = _fileOps.AddPrefixToFileName(_testFile1, prefix, separator);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(_testFile1));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.True(newFileName.StartsWith($"{prefix}{separator}"));
        }

        [Fact]
        public void AddPrefixToFileName_EmptyPrefix_ShouldReturnEmpty()
        {
            // Act & Assert
            var result = _fileOps.AddPrefixToFileName(_testFile1, "", "_");
            Assert.True(string.IsNullOrEmpty(result));
        }

        [Fact]
        public void RemovePrefixFromFileName_ValidPrefix_ShouldRemovePrefix()
        {
            // Arrange
            var prefix = "backup";
            var separator = "_";

            // 先添加前缀
            var prefixedFile = _fileOps.AddPrefixToFileName(_testFile1, prefix, separator);
            Assert.False(string.IsNullOrEmpty(prefixedFile));

            // Act - 移除前缀
            var result = _fileOps.RemovePrefixFromFileName(prefixedFile, prefix, separator);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(prefixedFile));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.False(newFileName.StartsWith($"{prefix}{separator}"));
        }

        #endregion

        #region 后缀处理测试

        [Fact]
        public void AddSuffixToFileName_ValidInput_ShouldAddSuffix()
        {
            // Arrange
            var suffix = "backup";
            var separator = "_";

            // Act
            var result = _fileOps.AddSuffixToFileName(_testFile1, suffix, separator);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(_testFile1));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.True(newFileName.EndsWith($"{separator}{suffix}"));
        }

        [Fact]
        public void RemoveSuffixFromFileName_ValidSuffix_ShouldRemoveSuffix()
        {
            // Arrange
            var suffix = "backup";
            var separator = "_";

            // 先添加后缀
            var suffixedFile = _fileOps.AddSuffixToFileName(_testFile1, suffix, separator);
            Assert.False(string.IsNullOrEmpty(suffixedFile));

            // Act - 移除后缀
            var result = _fileOps.RemoveSuffixFromFileName(suffixedFile, suffix, separator);

            // Assert
            Assert.False(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(result));
            Assert.False(File.Exists(suffixedFile));

            var newFileName = Path.GetFileNameWithoutExtension(result);
            Assert.False(newFileName.EndsWith($"{separator}{suffix}"));
        }

        #endregion

        #region 批量处理测试

        [Fact]
        public void AddPrefixToFileNames_ValidInput_ShouldProcessAllFiles()
        {
            // Arrange
            var prefix = "batch";
            var separator = "_";

            // Act
            var result = _fileOps.AddPrefixToFileNames(_testDirectory, prefix, "*.*", separator);

            // Assert
            Assert.Equal(2, result);

            // 验证文件是否都被重命名
            var files = Directory.GetFiles(_testDirectory);
            Assert.Equal(2, files.Length);

            foreach (var file in files)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                Assert.True(fileName.StartsWith($"{prefix}{separator}"));
            }
        }

        [Fact]
        public void AddSuffixToFileNames_ValidInput_ShouldProcessAllFiles()
        {
            // Arrange
            var suffix = "processed";
            var separator = "_";

            // Act
            var result = _fileOps.AddSuffixToFileNames(_testDirectory, suffix, "*.*", separator);

            // Assert
            Assert.Equal(2, result);

            // 验证文件是否都被重命名
            var files = Directory.GetFiles(_testDirectory);
            Assert.Equal(2, files.Length);

            foreach (var file in files)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                Assert.True(fileName.EndsWith($"{separator}{suffix}"));
            }
        }

        #endregion

        #region 边界条件测试

        [Fact]
        public void AddPrefixToFileName_FileNotExists_ShouldReturnEmpty()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _fileOps.AddPrefixToFileName(nonExistentFile, "prefix", "_");

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
        }

        [Fact]
        public void RemovePrefixFromFileName_NoPrefixMatch_ShouldReturnEmpty()
        {
            // Act
            var result = _fileOps.RemovePrefixFromFileName(_testFile1, "nonexistent", "_");

            // Assert
            Assert.True(string.IsNullOrEmpty(result));
            Assert.True(File.Exists(_testFile1));
        }

        #endregion
    }
}
