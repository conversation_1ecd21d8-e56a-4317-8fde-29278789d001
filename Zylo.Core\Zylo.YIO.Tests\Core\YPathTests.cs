using System;
using System.IO;
using System.Runtime.InteropServices;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests;

/// <summary>
/// YPath 单元测试
/// 测试路径组合、转换、验证、信息提取等核心功能
/// 按功能模块分类组织测试用例，确保跨平台兼容性
/// </summary>
public class YPathTests
{
    #region 测试设置

    private readonly YPath _pathUtils;

    public YPathTests()
    {
        _pathUtils = new YPath();
    }

    #endregion

    #region 路径组合和构建测试

    /// <summary>
    /// 测试基本路径组合功能
    /// </summary>
    [Fact]
    public void CombinePath_ShouldCombinePathsCorrectly()
    {
        // Arrange
        var parts = new[] { "folder1", "folder2", "file.txt" };

        // Act
        var result = _pathUtils.CombinePath(parts);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("folder1");
        result.Should().Contain("folder2");
        result.Should().Contain("file.txt");
    }

    /// <summary>
    /// 测试空路径组合的处理
    /// </summary>
    [Fact]
    public void CombinePath_ShouldHandleEmptyPaths()
    {
        // Act
        var result1 = _pathUtils.CombinePath();
        var result2 = _pathUtils.CombinePath("");
        var result3 = _pathUtils.CombinePath("", null, "  ");

        // Assert
        result1.Should().BeEmpty();
        result2.Should().BeEmpty();
        result3.Should().BeEmpty();
    }

    /// <summary>
    /// 测试安全路径组合功能
    /// </summary>
    [Fact]
    public void SafeCombinePath_ShouldPreventPathTraversal()
    {
        // Arrange
        var basePath = "/safe/directory";
        var maliciousPath = "../../../etc/passwd";

        // Act
        var result = _pathUtils.SafeCombinePath(basePath, maliciousPath);

        // Assert
        result.Should().Be(basePath); // 应该返回基础路径，防止路径遍历
    }

    /// <summary>
    /// 测试安全路径组合的正常情况
    /// </summary>
    [Fact]
    public void SafeCombinePath_ShouldAllowSafePaths()
    {
        // Arrange
        var basePath = "/safe/directory";
        var safePath = "subfolder/file.txt";

        // Act
        var result = _pathUtils.SafeCombinePath(basePath, safePath);

        // Assert
        result.Should().NotBe(basePath);
        result.Should().Contain("subfolder");
        result.Should().Contain("file.txt");
    }

    /// <summary>
    /// 测试带时间戳的路径构建
    /// </summary>
    [Fact]
    public void BuildTimestampedPath_ShouldIncludeTimestamp()
    {
        // Arrange
        var basePath = "/logs";
        var fileName = "app";
        var extension = "log";

        // Act
        var result = _pathUtils.BuildTimestampedPath(basePath, fileName, extension);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain(basePath);
        result.Should().Contain(fileName);
        result.Should().EndWith(".log");
        result.Should().MatchRegex(@"app_\d{8}_\d{6}\.log"); // 验证时间戳格式
    }

    /// <summary>
    /// 测试唯一路径构建
    /// </summary>
    [Fact]
    public void BuildUniquePath_ShouldReturnOriginalPath_WhenNotExists()
    {
        // Arrange
        var nonExistentPath = "/tmp/unique_file_that_does_not_exist.txt";

        // Act
        var result = _pathUtils.BuildUniquePath(nonExistentPath);

        // Assert
        result.Should().Be(nonExistentPath);
    }

    #endregion

    #region 路径转换和规范化测试

    /// <summary>
    /// 测试路径规范化功能
    /// </summary>
    [Fact]
    public void NormalizePath_ShouldNormalizePathSeparators()
    {
        // Arrange
        var mixedPath = "folder1/folder2\\folder3//file.txt";

        // Act
        var result = _pathUtils.NormalizePath(mixedPath);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().NotContain("//");
        result.Should().NotContain("\\\\");
    }

    /// <summary>
    /// 测试获取绝对路径
    /// </summary>
    [Fact]
    public void GetAbsolutePath_ShouldReturnAbsolutePath()
    {
        // Arrange
        var relativePath = "test.txt";

        // Act
        var result = _pathUtils.GetAbsolutePath(relativePath);

        // Assert
        result.Should().NotBeEmpty();
        _pathUtils.IsAbsolutePath(result).Should().BeTrue();
    }

    /// <summary>
    /// 测试Unix风格路径转换
    /// </summary>
    [Fact]
    public void ToUnixPath_ShouldConvertToUnixStyle()
    {
        // Arrange
        var windowsPath = "C:\\folder\\file.txt";

        // Act
        var result = _pathUtils.ToUnixPath(windowsPath);

        // Assert
        result.Should().Be("C:/folder/file.txt");
    }

    /// <summary>
    /// 测试Windows风格路径转换
    /// </summary>
    [Fact]
    public void ToWindowsPath_ShouldConvertToWindowsStyle()
    {
        // Arrange
        var unixPath = "/home/<USER>/file.txt";

        // Act
        var result = _pathUtils.ToWindowsPath(unixPath);

        // Assert
        result.Should().Be("\\home\\user\\file.txt");
    }

    #endregion

    #region 路径验证和检查测试

    /// <summary>
    /// 测试有效路径验证
    /// </summary>
    [Theory]
    [InlineData("C:\\valid\\path\\file.txt")]
    [InlineData("/valid/unix/path/file.txt")]
    [InlineData("relative/path/file.txt")]
    [InlineData("file.txt")]
    public void IsValidPath_ShouldReturnTrue_ForValidPaths(string validPath)
    {
        // Act
        var result = _pathUtils.IsValidPath(validPath);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试无效路径验证
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("path<with>invalid|chars")]
    // [InlineData("path\"with\"quotes")] // 在某些系统上引号可能被允许，暂时注释
    public void IsValidPath_ShouldReturnFalse_ForInvalidPaths(string invalidPath)
    {
        // Act
        var result = _pathUtils.IsValidPath(invalidPath);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 测试有效文件名验证
    /// </summary>
    [Theory]
    [InlineData("file.txt")]
    [InlineData("document.pdf")]
    [InlineData("image_001.jpg")]
    [InlineData("file-name.ext")]
    public void IsValidFileName_ShouldReturnTrue_ForValidFileNames(string validFileName)
    {
        // Act
        var result = _pathUtils.IsValidFileName(validFileName);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试无效文件名验证
    /// </summary>
    [Theory]
    [InlineData("")]
    [InlineData("file<name>.txt")]
    [InlineData("file|name.txt")]
    [InlineData("CON.txt")] // Windows保留名称
    [InlineData("PRN.log")] // Windows保留名称
    public void IsValidFileName_ShouldReturnFalse_ForInvalidFileNames(string invalidFileName)
    {
        // Act
        var result = _pathUtils.IsValidFileName(invalidFileName);

        // Assert
        result.Should().BeFalse();
    }

    /// <summary>
    /// 测试绝对路径检查
    /// </summary>
    [Fact]
    public void IsAbsolutePath_ShouldDetectAbsolutePaths()
    {
        // Arrange & Act & Assert
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            _pathUtils.IsAbsolutePath("C:\\folder\\file.txt").Should().BeTrue();
            _pathUtils.IsAbsolutePath("\\\\server\\share\\file.txt").Should().BeTrue();
        }
        else
        {
            _pathUtils.IsAbsolutePath("/home/<USER>/file.txt").Should().BeTrue();
        }

        _pathUtils.IsAbsolutePath("relative/path/file.txt").Should().BeFalse();
        _pathUtils.IsAbsolutePath("file.txt").Should().BeFalse();
    }

    /// <summary>
    /// 测试相对路径检查
    /// </summary>
    [Fact]
    public void IsRelativePath_ShouldDetectRelativePaths()
    {
        // Act & Assert
        _pathUtils.IsRelativePath("relative/path/file.txt").Should().BeTrue();
        _pathUtils.IsRelativePath("file.txt").Should().BeTrue();
        _pathUtils.IsRelativePath("../parent/file.txt").Should().BeTrue();

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            _pathUtils.IsRelativePath("C:\\folder\\file.txt").Should().BeFalse();
        }
        else
        {
            _pathUtils.IsRelativePath("/home/<USER>/file.txt").Should().BeFalse();
        }
    }

    /// <summary>
    /// 测试安全路径检查
    /// </summary>
    [Theory]
    [InlineData("safe/path/file.txt", true)]
    [InlineData("file.txt", true)]
    [InlineData("../dangerous/path", false)]
    [InlineData("path/with/../traversal", false)]
    [InlineData("path<with>invalid|chars", false)]
    public void IsSafePath_ShouldDetectUnsafePaths(string path, bool expectedSafe)
    {
        // Act
        var result = _pathUtils.IsSafePath(path);

        // Assert
        result.Should().Be(expectedSafe);
    }

    #endregion

    #region 路径信息提取测试

    /// <summary>
    /// 测试获取目录名
    /// </summary>
    [Fact]
    public void GetDirectoryName_ShouldExtractDirectoryName()
    {
        // Arrange
        var filePath = Path.Combine("folder1", "folder2", "file.txt");

        // Act
        var result = _pathUtils.GetDirectoryName(filePath);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("folder1");
    }

    /// <summary>
    /// 测试获取文件名
    /// </summary>
    [Fact]
    public void GetFileName_ShouldExtractFileName()
    {
        // Arrange
        var filePath = Path.Combine("folder", "file.txt");

        // Act
        var result = _pathUtils.GetFileName(filePath);

        // Assert
        result.Should().Be("file.txt");
    }

    /// <summary>
    /// 测试获取不含扩展名的文件名
    /// </summary>
    [Fact]
    public void GetFileNameWithoutExtension_ShouldExtractFileNameWithoutExtension()
    {
        // Arrange
        var filePath = Path.Combine("folder", "file.txt");

        // Act
        var result = _pathUtils.GetFileNameWithoutExtension(filePath);

        // Assert
        result.Should().Be("file");
    }

    /// <summary>
    /// 测试获取扩展名
    /// </summary>
    [Fact]
    public void GetExtension_ShouldExtractExtension()
    {
        // Arrange
        var filePath = "file.txt";

        // Act
        var result = _pathUtils.GetExtension(filePath);

        // Assert
        result.Should().Be(".txt");
    }

    /// <summary>
    /// 测试路径分解
    /// </summary>
    [Fact]
    public void SplitPath_ShouldSplitPathIntoParts()
    {
        // Arrange
        var path = Path.Combine("folder1", "folder2", "file.txt");

        // Act
        var result = _pathUtils.SplitPath(path);

        // Assert
        result.Should().NotBeEmpty();
        result.Should().Contain("folder1");
        result.Should().Contain("folder2");
        result.Should().Contain("file.txt");
    }

    /// <summary>
    /// 测试路径深度计算
    /// </summary>
    [Fact]
    public void GetPathDepth_ShouldCalculateCorrectDepth()
    {
        // Arrange
        var path = Path.Combine("folder1", "folder2", "folder3", "file.txt");

        // Act
        var result = _pathUtils.GetPathDepth(path);

        // Assert
        result.Should().Be(4); // folder1, folder2, folder3, file.txt
    }

    #endregion

    #region 临时路径测试

    /// <summary>
    /// 测试获取临时路径
    /// </summary>
    [Fact]
    public void GetTempPath_ShouldReturnValidTempPath()
    {
        // Act
        var result = _pathUtils.GetTempPath();

        // Assert
        result.Should().NotBeEmpty();
        Directory.Exists(result).Should().BeTrue();
    }

    /// <summary>
    /// 测试生成临时文件名
    /// </summary>
    [Fact]
    public void GetTempFileName_ShouldGenerateUniqueFileName()
    {
        // Act
        var result1 = _pathUtils.GetTempFileName(".txt");
        var result2 = _pathUtils.GetTempFileName(".txt");

        // Assert
        result1.Should().NotBeEmpty();
        result2.Should().NotBeEmpty();
        result1.Should().NotBe(result2); // 应该生成不同的文件名
        result1.Should().EndWith(".txt");
        result2.Should().EndWith(".txt");
    }

    /// <summary>
    /// 测试生成临时目录路径
    /// </summary>
    [Fact]
    public void GetTempDirectoryPath_ShouldGenerateUniqueDirectoryPath()
    {
        // Act
        var result1 = _pathUtils.GetTempDirectoryPath("test");
        var result2 = _pathUtils.GetTempDirectoryPath("test");

        // Assert
        result1.Should().NotBeEmpty();
        result2.Should().NotBeEmpty();
        result1.Should().NotBe(result2); // 应该生成不同的目录路径
        result1.Should().Contain("test");
        result2.Should().Contain("test");
    }

    #endregion

    #region 路径比较和匹配测试

    /// <summary>
    /// 测试路径相等比较
    /// </summary>
    [Fact]
    public void PathEquals_ShouldComparePathsCorrectly()
    {
        // Arrange
        var path1 = "folder/file.txt";
        var path2 = "folder\\file.txt"; // 不同的分隔符

        // Act
        var result = _pathUtils.PathEquals(path1, path2);

        // Assert
        result.Should().BeTrue(); // 规范化后应该相等
    }

    /// <summary>
    /// 测试通配符模式匹配
    /// </summary>
    [Theory]
    [InlineData("file.txt", "*.txt", true)]
    [InlineData("document.pdf", "*.txt", false)]
    [InlineData("test123.log", "test???.log", true)]
    [InlineData("test12.log", "test???.log", false)]
    public void MatchesPattern_ShouldMatchWildcardPatterns(string path, string pattern, bool expectedMatch)
    {
        // Act
        var result = _pathUtils.MatchesPattern(path, pattern);

        // Assert
        result.Should().Be(expectedMatch);
    }

    /// <summary>
    /// 测试路径包含关系检查
    /// </summary>
    [Fact]
    public void IsPathInDirectory_ShouldDetectPathContainment()
    {
        // Arrange
        var parentDir = "/parent/directory";
        var childPath = "/parent/directory/subfolder/file.txt";
        var outsidePath = "/other/directory/file.txt";

        // Act
        var isInside = _pathUtils.IsPathInDirectory(childPath, parentDir);
        var isOutside = _pathUtils.IsPathInDirectory(outsidePath, parentDir);

        // Assert
        isInside.Should().BeTrue();
        isOutside.Should().BeFalse();
    }

    #endregion

    #region 参数验证测试

    /// <summary>
    /// 测试null和空字符串参数的处理
    /// </summary>
    [Fact]
    public void Methods_ShouldHandleNullAndEmptyInputs()
    {
        // Act & Assert
        _pathUtils.CombinePath(null).Should().BeEmpty();
        _pathUtils.NormalizePath("").Should().BeEmpty();
        _pathUtils.GetAbsolutePath(null).Should().BeEmpty();
        _pathUtils.IsValidPath("").Should().BeFalse();
        _pathUtils.IsValidFileName(null).Should().BeFalse();
        _pathUtils.GetFileName("").Should().BeEmpty();
        _pathUtils.GetExtension(null).Should().BeEmpty();
    }

    #endregion
}
