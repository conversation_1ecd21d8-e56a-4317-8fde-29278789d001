# YFileBackup 测试说明

## 📋 **测试概述**

这是 YFileBackup 的简化单元测试，专为个人项目设计，覆盖核心功能而不过度复杂。

## 🎯 **测试覆盖范围**

### **核心功能测试**
- ✅ **单文件备份** - 验证基本的文件备份功能
- ✅ **目录备份** - 验证整个目录的备份功能  
- ✅ **加密备份** - 验证加密备份和恢复功能

### **增量备份测试**
- ✅ **变更检测** - 验证文件变更的检测和增量备份
- ✅ **无变更处理** - 验证没有变更时的处理逻辑

### **恢复功能测试**
- ✅ **普通恢复** - 验证备份文件的恢复功能
- ✅ **加密恢复** - 验证加密备份的解密恢复

### **实用功能测试**
- ✅ **备份历史** - 验证备份记录的查询功能
- ✅ **清理功能** - 验证过期备份的清理功能
- ✅ **异步操作** - 验证异步备份功能

## 🚀 **运行测试**

### **命令行运行**
```bash
# 运行所有 YFileBackup 测试
dotnet test --filter "YFileBackupTests"

# 运行特定测试
dotnet test --filter "备份单个文件_应该成功"

# 显示详细输出
dotnet test --filter "YFileBackupTests" --verbosity normal
```

### **Visual Studio 运行**
1. 打开 **测试资源管理器**
2. 找到 `YFileBackupTests` 类
3. 右键选择 **运行测试**

## 📊 **测试结果示例**

```
正在运行测试...

✅ 备份单个文件_应该成功
✅ 备份目录_应该成功  
✅ 加密备份_应该成功
✅ 增量备份_有变更时_应该成功
✅ 增量备份_无变更时_应该返回空备份
✅ 恢复备份_应该成功
✅ 恢复加密备份_应该成功
✅ 获取备份历史_应该返回备份列表
✅ 清理过期备份_应该删除旧备份
✅ 异步备份_应该成功

测试运行完毕: 10 个通过, 0 个失败, 0 个跳过
```

## 🔧 **测试配置**

### **测试依赖**
- `xunit` - 测试框架
- `FluentAssertions` - 断言库
- `Zylo.YIO` - 被测试的项目

### **测试数据**
- 测试使用临时目录，自动清理
- 测试文件内容使用中文，验证编码处理
- 每个测试独立运行，互不影响

## 💡 **个人项目测试建议**

### **什么时候运行测试**
- ✅ **修改代码后** - 确保没有破坏现有功能
- ✅ **添加新功能前** - 建立基准线
- ✅ **发布版本前** - 最终验证

### **如何快速验证**
```bash
# 快速验证核心功能
dotnet test --filter "备份单个文件_应该成功"

# 验证加密功能
dotnet test --filter "加密"

# 验证恢复功能  
dotnet test --filter "恢复"
```

### **测试失败时怎么办**
1. **查看错误信息** - 通常会指出具体问题
2. **检查文件权限** - 确保测试目录可写
3. **检查磁盘空间** - 确保有足够空间创建测试文件
4. **重新运行** - 有时是临时问题

## 📝 **测试维护**

### **添加新测试**
当添加新功能时，只需要添加一个简单的测试：

```csharp
[Fact]
public void 新功能_应该成功()
{
    // 准备
    // 执行  
    // 验证
}
```

### **修改现有测试**
- 保持测试简单明了
- 使用中文方法名，便于理解
- 遵循 "准备-执行-验证" 模式

## 🎯 **总结**

这个测试套件专为个人项目设计：
- **简单实用** - 不过度复杂
- **覆盖核心** - 测试最重要的功能
- **易于维护** - 代码清晰，容易修改
- **快速运行** - 几秒钟就能完成

对于个人项目来说，这样的测试覆盖已经足够确保代码质量，同时不会成为开发负担。
