using System;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using Zylo.YIO.Management;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests.Management
{
    /// <summary>
    /// YFileCompressor 单元测试
    /// 测试文件压缩和解压功能
    /// </summary>
    public class YFileCompressorTests : IDisposable
    {
        private readonly string _testDirectory;
        private readonly YFileCompressor _compressor;

        public YFileCompressorTests()
        {
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileCompressorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
            _compressor = new YFileCompressor();
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        [Fact]
        public async Task CompressFileAsync_ShouldCreateZipFile()
        {
            // Arrange
            var sourceFile = Path.Combine(_testDirectory, "test.txt");
            var zipFile = Path.Combine(_testDirectory, "test.zip");
            var testContent = "This is a test file for compression.";

            await File.WriteAllTextAsync(sourceFile, testContent);

            // Act
            var result = await _compressor.CompressFileAsync(sourceFile, zipFile);

            // Assert
            Assert.True(result.Success);
            Assert.True(File.Exists(zipFile));
            Assert.True(result.OriginalSize > 0);
            Assert.True(result.CompressedSize > 0);
            // 注意：小文件压缩后可能比原文件大，这是正常的
        }

        [Fact]
        public async Task DecompressFileAsync_ShouldExtractFile()
        {
            // Arrange
            var sourceFile = Path.Combine(_testDirectory, "test.txt");
            var zipFile = Path.Combine(_testDirectory, "test.zip");
            var extractDir = Path.Combine(_testDirectory, "extracted");
            var testContent = "This is a test file for compression and decompression.";

            await File.WriteAllTextAsync(sourceFile, testContent);
            await _compressor.CompressFileAsync(sourceFile, zipFile);

            // Act
            var result = await _compressor.ExtractZipAsync(zipFile, extractDir);

            // Assert
            Assert.True(result.Success);
            Assert.True(Directory.Exists(extractDir));

            var extractedFile = Path.Combine(extractDir, "test.txt");
            Assert.True(File.Exists(extractedFile));

            var extractedContent = await File.ReadAllTextAsync(extractedFile);
            Assert.Equal(testContent, extractedContent);
        }

        [Fact]
        public async Task CompressDirectoryAsync_ShouldCreateZipWithMultipleFiles()
        {
            // Arrange
            var sourceDir = Path.Combine(_testDirectory, "source");
            var zipFile = Path.Combine(_testDirectory, "directory.zip");
            Directory.CreateDirectory(sourceDir);

            await File.WriteAllTextAsync(Path.Combine(sourceDir, "file1.txt"), "Content 1");
            await File.WriteAllTextAsync(Path.Combine(sourceDir, "file2.txt"), "Content 2");

            var subDir = Path.Combine(sourceDir, "subdir");
            Directory.CreateDirectory(subDir);
            await File.WriteAllTextAsync(Path.Combine(subDir, "file3.txt"), "Content 3");

            // Act
            var result = await _compressor.CompressDirectoryAsync(sourceDir, zipFile);

            // Assert
            Assert.True(result.Success);
            Assert.True(File.Exists(zipFile));
            Assert.True(result.OriginalSize > 0);
            Assert.True(result.CompressedSize > 0);
        }

        [Fact]
        public async Task GetZipInfo_ShouldReturnCorrectInformation()
        {
            // Arrange
            var sourceFile = Path.Combine(_testDirectory, "test.txt");
            var zipFile = Path.Combine(_testDirectory, "test.zip");
            var testContent = "This is a test file for getting zip information.";

            await File.WriteAllTextAsync(sourceFile, testContent);
            await _compressor.CompressFileAsync(sourceFile, zipFile);

            // Act
            var zipInfo = _compressor.GetZipInfo(zipFile);

            // Assert
            Assert.NotNull(zipInfo);
            Assert.True(zipInfo.FileCount > 0);
            Assert.True(zipInfo.UncompressedSize > 0);
            Assert.True(zipInfo.CompressedSize > 0);
            // 注意：压缩率可能大于1（即压缩后更大），这对小文件是正常的
        }

        [Fact]
        public async Task CompressFileAsync_WithInvalidPath_ShouldReturnFailure()
        {
            // Arrange
            var invalidSourceFile = Path.Combine(_testDirectory, "nonexistent.txt");
            var zipFile = Path.Combine(_testDirectory, "test.zip");

            // Act
            var result = await _compressor.CompressFileAsync(invalidSourceFile, zipFile);

            // Assert
            Assert.False(result.Success);
            Assert.NotNull(result.ErrorMessage);
        }

        [Fact]
        public async Task ExtractZipAsync_WithInvalidZip_ShouldReturnFailure()
        {
            // Arrange
            var invalidZipFile = Path.Combine(_testDirectory, "nonexistent.zip");
            var extractDir = Path.Combine(_testDirectory, "extracted");

            // Act
            var result = await _compressor.ExtractZipAsync(invalidZipFile, extractDir);

            // Assert
            Assert.False(result.Success);
            Assert.NotNull(result.ErrorMessage);
        }
    }
}
