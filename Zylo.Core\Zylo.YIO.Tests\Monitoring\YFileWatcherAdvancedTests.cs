using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using FluentAssertions;
using Zylo.YIO.Monitoring;

namespace Zylo.YIO.Tests.Monitoring
{
    /// <summary>
    /// YFileWatcher 高级功能测试类
    /// 测试优化后的 YFileWatcher 新增功能：
    /// • 批量变更检测
    /// • 统计信息收集
    /// • 错误处理和恢复
    /// • 性能监控
    /// • 高级配置选项
    /// </summary>
    public class YFileWatcherAdvancedTests : IDisposable
    {
        private readonly YFileWatcher _fileWatcher;
        private readonly string _testDirectory;
        private readonly List<string> _testDirectories;

        public YFileWatcherAdvancedTests()
        {
            _fileWatcher = new YFileWatcher();
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileWatcherAdvancedTests", Guid.NewGuid().ToString());
            _testDirectories = new List<string>();

            Directory.CreateDirectory(_testDirectory);
            _testDirectories.Add(_testDirectory);
        }

        public void Dispose()
        {
            _fileWatcher?.Dispose();

            foreach (var dir in _testDirectories)
            {
                if (Directory.Exists(dir))
                {
                    try
                    {
                        Directory.Delete(dir, true);
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }
            }
        }

        private string CreateTestDirectory(string name)
        {
            var dir = Path.Combine(_testDirectory, name);
            Directory.CreateDirectory(dir);
            _testDirectories.Add(dir);
            return dir;
        }

        #region 批量变更检测测试

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public async Task BatchChanges_ShouldDetectMultipleChanges()
        {
            // Arrange
            var batchChanges = new List<BatchChangeEventArgs>();
            var changeCount = 0;

            _fileWatcher.BatchChanges += (sender, e) =>
            {
                batchChanges.Add(e);
                changeCount += e.TotalChanges;
            };

            var config = new WatcherConfig
            {
                EnableDebouncing = true,
                DebounceDelayMs = 500,
                Filter = "*.txt"
            };

            _fileWatcher.StartWatching(_testDirectory, config);
            await Task.Delay(100); // 等待监控启动

            // Act - 快速创建多个文件
            var files = new List<string>();
            for (int i = 0; i < 5; i++)
            {
                var file = Path.Combine(_testDirectory, $"batch_test_{i}.txt");
                await File.WriteAllTextAsync(file, $"Content {i}");
                files.Add(file);
            }

            // 等待批量处理
            await Task.Delay(1000);

            // Assert
            batchChanges.Should().NotBeEmpty("应该检测到批量变更");
            changeCount.Should().BeGreaterOrEqualTo(5, "应该检测到至少5个变更");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void WatcherConfig_HighPerformance_ShouldHaveCorrectSettings()
        {
            // Act
            var config = WatcherConfig.HighPerformance;

            // Assert
            config.BufferSize.Should().BeGreaterThan(8192, "高性能配置应该有更大的缓冲区");
            config.EnableDebouncing.Should().BeTrue("高性能配置应该启用防抖");
            config.DebounceDelayMs.Should().BeLessOrEqualTo(1000, "高性能配置的防抖延迟应该合理");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void WatcherConfig_LowLatency_ShouldHaveCorrectSettings()
        {
            // Act
            var config = WatcherConfig.LowLatency;

            // Assert
            config.BufferSize.Should().BeGreaterThan(8192, "低延迟配置应该有较大的缓冲区");
            config.EnableDebouncing.Should().BeFalse("低延迟配置应该禁用防抖");
        }

        #endregion

        #region 统计信息测试

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public async Task GetStatistics_ShouldTrackEvents()
        {
            // Arrange
            _fileWatcher.StartWatching(_testDirectory);
            await Task.Delay(100);

            // Act - 创建一些文件操作
            var testFile = Path.Combine(_testDirectory, "stats_test.txt");
            await File.WriteAllTextAsync(testFile, "initial content");
            await Task.Delay(100);

            await File.AppendAllTextAsync(testFile, " - modified");
            await Task.Delay(100);

            File.Delete(testFile);
            await Task.Delay(500); // 等待事件处理

            // Assert
            var statistics = _fileWatcher.GetStatistics();
            statistics.Should().ContainKey(_testDirectory, "应该包含监控路径的统计信息");

            var pathStats = statistics[_testDirectory];
            pathStats.TotalEvents.Should().BeGreaterThan(0, "应该记录事件总数");
            pathStats.TotalRunTime.Should().BeGreaterThan(TimeSpan.Zero, "应该记录运行时间");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void GetStatistics_ShouldReturnValidData()
        {
            // Arrange & Act
            _fileWatcher.StartWatching(_testDirectory);
            var statistics = _fileWatcher.GetStatistics();

            // Assert
            statistics.Should().NotBeNull("应该返回统计信息");
            statistics.Should().ContainKey(_testDirectory, "应该包含监控路径的统计信息");

            var pathStats = statistics[_testDirectory];
            pathStats.Should().NotBeNull("路径统计信息应该存在");
            pathStats.TotalRunTime.Should().BeGreaterOrEqualTo(TimeSpan.Zero, "运行时间应该大于等于0");
        }

        #endregion

        #region 错误处理测试

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void WatcherError_ShouldHandleInvalidPath()
        {
            // Arrange
            var errorOccurred = false;
            WatcherErrorEventArgs lastError = null;

            _fileWatcher.WatcherError += (sender, e) =>
            {
                errorOccurred = true;
                lastError = e;
            };

            // Act
            var invalidPath = Path.Combine(_testDirectory, "NonExistentDirectory");
            var result = _fileWatcher.StartWatching(invalidPath);

            // Assert
            result.Should().BeFalse("监控无效路径应该失败");
            errorOccurred.Should().BeTrue("应该触发错误事件");
            lastError.Should().NotBeNull("应该提供错误信息");
            lastError.Level.Should().Be(ErrorLevel.Error, "无效路径应该是错误级别");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void StatusChanged_ShouldFireOnStartStop()
        {
            // Arrange
            var statusChanges = new List<WatcherStatusEventArgs>();

            _fileWatcher.StatusChanged += (sender, e) =>
            {
                statusChanges.Add(e);
            };

            // Act
            _fileWatcher.StartWatching(_testDirectory);
            _fileWatcher.StopWatching(_testDirectory);

            // Assert
            statusChanges.Should().HaveCountGreaterOrEqualTo(2, "应该有启动和停止状态变更");
            statusChanges.Should().Contain(s => s.Status == WatcherStatus.Started, "应该有启动状态");
            statusChanges.Should().Contain(s => s.Status == WatcherStatus.Stopped, "应该有停止状态");
        }

        #endregion

        #region 高级配置测试

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void ExcludedExtensions_ShouldFilterFiles()
        {
            // Arrange
            var config = new WatcherConfig();
            config.ExcludedExtensions.Add(".tmp");
            config.ExcludedExtensions.Add(".log");

            // Act & Assert
            config.ExcludedExtensions.Should().Contain(".tmp", "应该排除.tmp文件");
            config.ExcludedExtensions.Should().Contain(".log", "应该排除.log文件");
            config.ExcludedExtensions.Should().HaveCount(2, "应该有2个排除扩展名");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void ExcludedPaths_ShouldFilterDirectories()
        {
            // Arrange
            var config = new WatcherConfig();
            config.ExcludedPaths.Add("temp");
            config.ExcludedPaths.Add("cache");

            // Act & Assert
            config.ExcludedPaths.Should().Contain("temp", "应该排除temp目录");
            config.ExcludedPaths.Should().Contain("cache", "应该排除cache目录");
            config.ExcludedPaths.Should().HaveCount(2, "应该有2个排除路径");
        }

        #endregion

        #region 多路径监控测试

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void MultiPath_ShouldMonitorAllPaths()
        {
            // Arrange
            var path1 = CreateTestDirectory("path1");
            var path2 = CreateTestDirectory("path2");
            var path3 = CreateTestDirectory("path3");

            // Act
            var result1 = _fileWatcher.StartWatching(path1);
            var result2 = _fileWatcher.StartWatching(path2);
            var result3 = _fileWatcher.StartWatching(path3);

            // Assert
            result1.Should().BeTrue("路径1应该监控成功");
            result2.Should().BeTrue("路径2应该监控成功");
            result3.Should().BeTrue("路径3应该监控成功");

            var watchedPaths = _fileWatcher.GetWatchedPaths();
            watchedPaths.Should().HaveCount(3, "应该监控3个路径");
            watchedPaths.Should().Contain(path1, "应该包含路径1");
            watchedPaths.Should().Contain(path2, "应该包含路径2");
            watchedPaths.Should().Contain(path3, "应该包含路径3");
        }

        [Fact]
        [Trait("Category", "FileWatcher.Advanced")]
        public void StopAllWatching_ShouldStopAllMonitoring()
        {
            // Arrange
            var path1 = CreateTestDirectory("stop_path1");
            var path2 = CreateTestDirectory("stop_path2");

            _fileWatcher.StartWatching(path1);
            _fileWatcher.StartWatching(path2);

            // Act
            _fileWatcher.StopAllWatching();

            // Assert
            var watchedPaths = _fileWatcher.GetWatchedPaths();
            watchedPaths.Should().BeEmpty("停止所有监控后应该没有监控路径");

            _fileWatcher.IsWatching(path1).Should().BeFalse("路径1应该停止监控");
            _fileWatcher.IsWatching(path2).Should().BeFalse("路径2应该停止监控");
        }

        #endregion

        #region 性能测试

        [Fact]
        [Trait("Category", "FileWatcher.Performance")]
        public async Task Performance_ShouldHandleManyFiles()
        {
            // Arrange
            var config = WatcherConfig.HighPerformance;
            var eventCount = 0;
            var allEvents = new ConcurrentBag<string>();

            _fileWatcher.FileCreated += (sender, e) =>
            {
                Interlocked.Increment(ref eventCount);
                allEvents.Add($"Created: {e.Name}");
            };

            _fileWatcher.FileChanged += (sender, e) =>
            {
                allEvents.Add($"Changed: {e.Name}");
            };

            _fileWatcher.StartWatching(_testDirectory, config);
            await Task.Delay(500); // 等待监控启动

            // Act - 创建文件
            var createdFiles = new List<string>();
            for (int i = 0; i < 10; i++) // 进一步减少文件数量
            {
                var file = Path.Combine(_testDirectory, $"perf_test_{i}.txt");
                await File.WriteAllTextAsync(file, $"Performance test {i}");
                createdFiles.Add(file);
                await Task.Delay(50); // 增加延迟确保文件系统事件被触发
            }

            await Task.Delay(2000); // 等待所有事件处理完成

            // Assert
            // 检查统计信息而不是事件计数器，因为统计信息更可靠
            var statistics = _fileWatcher.GetStatistics();
            statistics.Should().ContainKey(_testDirectory, "应该包含测试目录的统计信息");

            var pathStats = statistics[_testDirectory];
            pathStats.TotalEvents.Should().BeGreaterThan(0, "统计信息应该显示有事件被处理");

            // 验证文件确实被创建了
            foreach (var file in createdFiles)
            {
                File.Exists(file).Should().BeTrue($"文件 {Path.GetFileName(file)} 应该存在");
            }
        }

        #endregion
    }
}
