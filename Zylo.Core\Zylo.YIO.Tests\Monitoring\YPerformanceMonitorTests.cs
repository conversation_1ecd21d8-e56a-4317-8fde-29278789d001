using System;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using Zylo.YIO.Monitoring;

namespace Zylo.YIO.Tests.Monitoring
{
    /// <summary>
    /// YPerformanceMonitor 性能监控测试类
    /// 测试文件操作性能监控、统计分析、性能报告等功能
    /// </summary>
    public class YPerformanceMonitorTests : IDisposable
    {
        private readonly YPerformanceMonitor _performanceMonitor;
        private readonly string _testDirectory;

        public YPerformanceMonitorTests()
        {
            _performanceMonitor = new YPerformanceMonitor();
            _testDirectory = Path.Combine(Path.GetTempPath(), "YPerformanceMonitorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 基础监控测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void StartMonitoring_ShouldReturnValidContext()
        {
            // Act
            var context = _performanceMonitor.StartMonitoring("TestOperation", "test.txt", 1024);

            // Assert
            Assert.NotNull(context);
            Assert.Equal("TestOperation", context.OperationType);
            Assert.Equal("test.txt", context.FilePath);
            Assert.Equal(1024, context.FileSize);
            Assert.NotNull(context.Stopwatch);
            Assert.True(context.Stopwatch.IsRunning);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void EndMonitoring_ShouldStopStopwatch()
        {
            // Arrange
            var context = _performanceMonitor.StartMonitoring("TestOperation", "test.txt");

            // Act
            _performanceMonitor.EndMonitoring(context, true);

            // Assert
            Assert.False(context.Stopwatch.IsRunning);
            Assert.True(context.Duration > TimeSpan.Zero);
            Assert.True(context.Success);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void EndMonitoring_WithError_ShouldRecordFailure()
        {
            // Arrange
            var context = _performanceMonitor.StartMonitoring("TestOperation", "test.txt");

            // Act
            _performanceMonitor.EndMonitoring(context, false, "Test error message");

            // Assert
            Assert.False(context.Success);
            Assert.Equal("Test error message", context.ErrorMessage);
        }

        #endregion

        #region 异步监控测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public async Task MonitorAsync_SuccessfulOperation_ShouldReturnResult()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test.txt");

            // Act
            var result = await _performanceMonitor.MonitorAsync(
                "FileWrite",
                testFile,
                async () =>
                {
                    await File.WriteAllTextAsync(testFile, "test content");
                    return "success";
                },
                12);

            // Assert
            Assert.Equal("success", result);
            Assert.True(File.Exists(testFile));
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public async Task MonitorAsync_FailedOperation_ShouldThrowException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(async () =>
            {
                await _performanceMonitor.MonitorAsync<string>(
                    "FailedOperation",
                    "test.txt",
                    async () =>
                    {
                        await Task.Delay(10);
                        throw new InvalidOperationException("Test exception");
                    });
            });
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void Monitor_SynchronousOperation_ShouldWork()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "sync_test.txt");

            // Act
            var result = _performanceMonitor.Monitor(
                "SyncFileWrite",
                testFile,
                () =>
                {
                    File.WriteAllText(testFile, "sync content");
                    return "sync_success";
                },
                12);

            // Assert
            Assert.Equal("sync_success", result);
            Assert.True(File.Exists(testFile));
        }

        #endregion

        #region 统计信息测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GetOperationStatistics_AfterOperations_ShouldReturnCorrectStats()
        {
            // Arrange
            var operationType = "TestStats";

            // 执行几个操作
            for (int i = 0; i < 3; i++)
            {
                var context = _performanceMonitor.StartMonitoring(operationType, $"file{i}.txt", 100);
                System.Threading.Thread.Sleep(10); // 模拟操作时间
                _performanceMonitor.EndMonitoring(context, i < 2); // 前两个成功，最后一个失败
            }

            // Act
            var stats = _performanceMonitor.GetOperationStatistics(operationType);

            // Assert
            Assert.NotNull(stats);
            Assert.Equal(operationType, stats.OperationType);
            Assert.Equal(3, stats.TotalOperations);
            Assert.Equal(2, stats.SuccessfulOperations);
            Assert.Equal(1, stats.FailedOperations);
            Assert.True(stats.SuccessRate > 60 && stats.SuccessRate < 70); // 约66.67%
            Assert.True(stats.AverageDuration > TimeSpan.Zero);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GetOperationStatistics_NonExistentOperation_ShouldReturnNull()
        {
            // Act
            var stats = _performanceMonitor.GetOperationStatistics("NonExistentOperation");

            // Assert
            Assert.Null(stats);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GetAllStatistics_ShouldReturnAllOperations()
        {
            // Arrange
            var context1 = _performanceMonitor.StartMonitoring("Operation1", "file1.txt");
            _performanceMonitor.EndMonitoring(context1, true);

            var context2 = _performanceMonitor.StartMonitoring("Operation2", "file2.txt");
            _performanceMonitor.EndMonitoring(context2, true);

            // Act
            var allStats = _performanceMonitor.GetAllStatistics();

            // Assert
            Assert.Equal(2, allStats.Count);
            Assert.Contains(allStats, s => s.OperationType == "Operation1");
            Assert.Contains(allStats, s => s.OperationType == "Operation2");
        }

        #endregion

        #region 性能摘要测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GetPerformanceSummary_ShouldReturnValidSummary()
        {
            // Arrange
            var context1 = _performanceMonitor.StartMonitoring("FastOperation", "file1.txt", 500);
            System.Threading.Thread.Sleep(5);
            _performanceMonitor.EndMonitoring(context1, true);

            var context2 = _performanceMonitor.StartMonitoring("SlowOperation", "file2.txt", 1000);
            System.Threading.Thread.Sleep(20);
            _performanceMonitor.EndMonitoring(context2, true);

            // Act
            var summary = _performanceMonitor.GetPerformanceSummary();

            // Assert
            Assert.True(summary.TotalOperations >= 2);
            Assert.True(summary.TotalBytesProcessed >= 1500);
            Assert.Equal(2, summary.TotalOperationTypes);
            Assert.True(summary.OverallSuccessRate > 0);
            Assert.True(summary.AverageOperationDuration > TimeSpan.Zero);
            Assert.NotEmpty(summary.FastestOperation);
            Assert.NotEmpty(summary.SlowestOperation);
            Assert.NotEmpty(summary.MostFrequentOperation);
        }

        #endregion

        #region 性能报告测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GenerateReport_ShouldReturnCompleteReport()
        {
            // Arrange
            var context = _performanceMonitor.StartMonitoring("ReportTest", "report.txt", 256);
            _performanceMonitor.EndMonitoring(context, true);

            // Act
            var report = _performanceMonitor.GenerateReport(true);

            // Assert
            Assert.NotNull(report);
            Assert.NotNull(report.Summary);
            Assert.NotNull(report.OperationStatistics);
            Assert.NotNull(report.RecentHistory);
            Assert.NotNull(report.Recommendations);
            Assert.True(report.GeneratedAt <= DateTime.Now);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void GetRecentHistory_ShouldReturnLimitedRecords()
        {
            // Arrange
            for (int i = 0; i < 10; i++)
            {
                var context = _performanceMonitor.StartMonitoring($"HistoryTest{i}", $"file{i}.txt");
                _performanceMonitor.EndMonitoring(context, true);
            }

            // Act
            var history = _performanceMonitor.GetRecentHistory(5);

            // Assert
            Assert.True(history.Count <= 5);
        }

        #endregion

        #region 数据清理测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void ClearPerformanceData_ShouldResetAllData()
        {
            // Arrange
            var context = _performanceMonitor.StartMonitoring("ClearTest", "clear.txt");
            _performanceMonitor.EndMonitoring(context, true);

            // Act
            _performanceMonitor.ClearPerformanceData();

            // Assert
            var summary = _performanceMonitor.GetPerformanceSummary();
            Assert.Equal(0, summary.TotalOperations);
            Assert.Equal(0, summary.TotalBytesProcessed);
            Assert.Equal(0, summary.TotalOperationTypes);

            var allStats = _performanceMonitor.GetAllStatistics();
            Assert.Empty(allStats);

            var history = _performanceMonitor.GetRecentHistory();
            Assert.Empty(history);
        }

        #endregion

        #region 数据模型测试

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void PerformanceContext_ShouldHaveCorrectProperties()
        {
            // Act
            var context = new PerformanceContext
            {
                OperationType = "TestOp",
                FilePath = "test.txt",
                FileSize = 1024,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddSeconds(1),
                Duration = TimeSpan.FromSeconds(1),
                Success = true,
                ErrorMessage = null
            };

            // Assert
            Assert.Equal("TestOp", context.OperationType);
            Assert.Equal("test.txt", context.FilePath);
            Assert.Equal(1024, context.FileSize);
            Assert.True(context.Success);
            Assert.Null(context.ErrorMessage);
        }

        [Fact]
        [Trait("Category", "PerformanceMonitor")]
        public void OperationStatistics_ShouldCalculateCorrectly()
        {
            // Act
            var stats = new OperationStatistics
            {
                OperationType = "TestStats",
                TotalOperations = 10,
                SuccessfulOperations = 8,
                FailedOperations = 2,
                SuccessRate = 80.0,
                AverageDuration = TimeSpan.FromMilliseconds(100),
                MinDuration = TimeSpan.FromMilliseconds(50),
                MaxDuration = TimeSpan.FromMilliseconds(200),
                TotalBytesProcessed = 10240,
                AverageSpeed = 1024.0
            };

            // Assert
            Assert.Equal("TestStats", stats.OperationType);
            Assert.Equal(10, stats.TotalOperations);
            Assert.Equal(8, stats.SuccessfulOperations);
            Assert.Equal(2, stats.FailedOperations);
            Assert.Equal(80.0, stats.SuccessRate);
            Assert.Equal(10240, stats.TotalBytesProcessed);
            Assert.Equal(1024.0, stats.AverageSpeed);
        }

        #endregion
    }
}
