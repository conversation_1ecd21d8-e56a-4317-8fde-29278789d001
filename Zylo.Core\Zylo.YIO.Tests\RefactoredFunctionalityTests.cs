using System;
using System.IO;
using System.Collections.Generic;
using Zylo.YIO.Formats;
using Zylo.YIO.Extensions;
using Zylo.YIO.Config;

namespace Zylo.YIO.Tests
{
    /// <summary>
    /// 重构后功能测试 - 按文件分类系统性测试
    /// </summary>
    public class RefactoredFunctionalityTests
    {
        // 测试数据类
        public class TestConfig
        {
            public string Name { get; set; } = "测试配置";
            public int Version { get; set; } = 1;
            public bool IsActive { get; set; } = true;
            public DateTime CreatedAt { get; set; } = DateTime.Now;
            public List<string> Tags { get; set; } = new List<string> { "test", "config" };
        }

        public static void RunAllTests()
        {
            Console.WriteLine("🧪 重构后功能测试 - 系统性验证");
            Console.WriteLine(new string('=', 60));

            var results = new Dictionary<string, bool>();

            // 1. JSON 处理器测试
            results["JSON处理器"] = TestJsonProcessor();

            // 2. XML 处理器测试
            results["XML处理器"] = TestXmlProcessor();

            // 3. INI 处理器测试
            results["INI处理器"] = TestIniProcessor();

            // 4. 配置管理器测试
            results["配置管理器"] = TestConfigManager();

            // 5. 扩展方法测试
            results["扩展方法"] = TestExtensionMethods();

            // 6. 集成测试
            results["集成测试"] = TestIntegration();

            // 输出测试结果
            Console.WriteLine("\n📊 测试结果汇总:");
            Console.WriteLine(new string('-', 40));

            int passed = 0, total = 0;
            foreach (var result in results)
            {
                total++;
                if (result.Value) passed++;

                var status = result.Value ? "✅ 通过" : "❌ 失败";
                Console.WriteLine($"   {result.Key}: {status}");
            }

            Console.WriteLine(new string('-', 40));
            Console.WriteLine($"📈 总体结果: {passed}/{total} 通过 ({(double)passed / total * 100:F1}%)");

            if (passed == total)
            {
                Console.WriteLine("🎉 所有测试通过！重构成功！");
            }
            else
            {
                Console.WriteLine("⚠️ 部分测试失败，需要修复");
            }
        }

        #region 1. JSON 处理器测试

        private static bool TestJsonProcessor()
        {
            Console.WriteLine("\n1️⃣ JSON 处理器测试 (YJsonProcessor)");
            Console.WriteLine("   测试范围: 读写、验证、动态对象");

            try
            {
                var processor = new YJsonProcessor();
                var testConfig = new TestConfig
                {
                    Name = "JSON测试配置",
                    Version = 2,
                    Tags = new List<string> { "json", "test", "processor" }
                };

                // 测试写入
                var writeResult = processor.WriteJson("./test-results/json-test.json", testConfig);
                if (!writeResult)
                {
                    Console.WriteLine("   ❌ JSON 写入失败");
                    return false;
                }
                Console.WriteLine("   ✅ JSON 写入成功");

                // 测试读取
                var readResult = processor.ReadJson<TestConfig>("./test-results/json-test.json");
                if (readResult == null || readResult.Name != testConfig.Name)
                {
                    Console.WriteLine("   ❌ JSON 读取失败");
                    return false;
                }
                Console.WriteLine("   ✅ JSON 读取成功");

                // 测试验证
                var validateResult = processor.ValidateJson("./test-results/json-test.json");
                if (!validateResult)
                {
                    Console.WriteLine("   ❌ JSON 验证失败");
                    return false;
                }
                Console.WriteLine("   ✅ JSON 验证成功");

                // 测试动态读取
                var dynamicResult = processor.ReadJsonDynamic("./test-results/json-test.json");
                if (dynamicResult == null)
                {
                    Console.WriteLine("   ❌ JSON 动态读取失败");
                    return false;
                }
                Console.WriteLine("   ✅ JSON 动态读取成功");

                Console.WriteLine("   🎯 JSON 处理器测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ JSON 处理器测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 2. XML 处理器测试

        private static bool TestXmlProcessor()
        {
            Console.WriteLine("\n2️⃣ XML 处理器测试 (YXmlProcessor)");
            Console.WriteLine("   测试范围: 读写、验证、字典转换");

            try
            {
                var processor = new YXmlProcessor();

                // 创建测试 XML 文档
                var xmlDoc = new System.Xml.Linq.XDocument(
                    new System.Xml.Linq.XElement("TestConfig",
                        new System.Xml.Linq.XElement("Name", "XML测试配置"),
                        new System.Xml.Linq.XElement("Version", "3"),
                        new System.Xml.Linq.XElement("IsActive", "true")
                    )
                );

                // 测试写入
                var writeResult = processor.WriteXml("./test-results/xml-test.xml", xmlDoc);
                if (!writeResult)
                {
                    Console.WriteLine("   ❌ XML 写入失败");
                    return false;
                }
                Console.WriteLine("   ✅ XML 写入成功");

                // 测试读取
                var readResult = processor.ReadXml("./test-results/xml-test.xml");
                if (readResult?.Root == null)
                {
                    Console.WriteLine("   ❌ XML 读取失败");
                    return false;
                }
                Console.WriteLine("   ✅ XML 读取成功");

                // 测试验证
                var validateResult = processor.ValidateXml("./test-results/xml-test.xml");
                if (!validateResult)
                {
                    Console.WriteLine("   ❌ XML 验证失败");
                    return false;
                }
                Console.WriteLine("   ✅ XML 验证成功");

                // 测试字典转换
                var dictResult = processor.ReadXmlAsDictionary("./test-results/xml-test.xml");
                if (dictResult == null)
                {
                    Console.WriteLine("   ❌ XML 字典转换失败");
                    return false;
                }
                Console.WriteLine("   ✅ XML 字典转换成功");

                Console.WriteLine("   🎯 XML 处理器测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ XML 处理器测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 3. INI 处理器测试

        private static bool TestIniProcessor()
        {
            Console.WriteLine("\n3️⃣ INI 处理器测试 (YIniProcessor)");
            Console.WriteLine("   测试范围: 读写、字典转换");

            try
            {
                var processor = new YIniProcessor();

                // 创建测试 INI 数据
                var iniData = new Dictionary<string, Dictionary<string, string>>
                {
                    ["TestSection"] = new Dictionary<string, string>
                    {
                        ["Name"] = "INI测试配置",
                        ["Version"] = "4",
                        ["IsActive"] = "true"
                    },
                    [""] = new Dictionary<string, string>  // 全局节
                    {
                        ["GlobalKey"] = "GlobalValue"
                    }
                };

                // 测试写入
                var writeResult = processor.WriteIni("./test-results/ini-test.ini", iniData);
                if (!writeResult)
                {
                    Console.WriteLine("   ❌ INI 写入失败");
                    return false;
                }
                Console.WriteLine("   ✅ INI 写入成功");

                // 测试读取
                var readResult = processor.ReadIni("./test-results/ini-test.ini");
                if (readResult == null || !readResult.ContainsKey("TestSection"))
                {
                    Console.WriteLine("   ❌ INI 读取失败");
                    return false;
                }
                Console.WriteLine("   ✅ INI 读取成功");

                // 测试字典转换
                var dictResult = processor.ReadIniFileAsDictionary("./test-results/ini-test.ini");
                if (dictResult == null)
                {
                    Console.WriteLine("   ❌ INI 字典转换失败");
                    return false;
                }
                Console.WriteLine("   ✅ INI 字典转换成功");

                Console.WriteLine("   🎯 INI 处理器测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ INI 处理器测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 4. 配置管理器测试

        private static bool TestConfigManager()
        {
            Console.WriteLine("\n4️⃣ 配置管理器测试 (YConfigManager)");
            Console.WriteLine("   测试范围: 统一接口、格式检测、动态加载");

            try
            {
                var manager = new YConfigManager(
                    new YJsonProcessor(),
                    new YXmlProcessor(),
                    new YIniProcessor()
                );

                var testConfig = new TestConfig
                {
                    Name = "管理器测试配置",
                    Version = 5
                };

                // 测试保存
                var saveResult = manager.Save("./test-results/manager-test.json", testConfig);
                if (!saveResult)
                {
                    Console.WriteLine("   ❌ 管理器保存失败");
                    return false;
                }
                Console.WriteLine("   ✅ 管理器保存成功");

                // 测试加载
                var loadResult = manager.Load<TestConfig>("./test-results/manager-test.json");
                if (loadResult == null || loadResult.Name != testConfig.Name)
                {
                    Console.WriteLine("   ❌ 管理器加载失败");
                    return false;
                }
                Console.WriteLine("   ✅ 管理器加载成功");

                // 测试格式检测
                var formatResult = manager.DetectFormat("./test-results/manager-test.json");
                if (formatResult != ConfigFileFormat.Json)
                {
                    Console.WriteLine("   ❌ 格式检测失败");
                    return false;
                }
                Console.WriteLine("   ✅ 格式检测成功");

                // 测试验证
                var validateResult = manager.Validate("./test-results/manager-test.json");
                if (!validateResult)
                {
                    Console.WriteLine("   ❌ 管理器验证失败");
                    return false;
                }
                Console.WriteLine("   ✅ 管理器验证成功");

                // 测试动态加载
                var dynamicResult = manager.LoadDynamic("./test-results/manager-test.json");
                if (dynamicResult == null)
                {
                    Console.WriteLine("   ❌ 动态加载失败");
                    return false;
                }
                Console.WriteLine("   ✅ 动态加载成功");

                Console.WriteLine("   🎯 配置管理器测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 配置管理器测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 5. 扩展方法测试

        private static bool TestExtensionMethods()
        {
            Console.WriteLine("\n5️⃣ 扩展方法测试 (YConfigExtensions)");
            Console.WriteLine("   测试范围: YSave、YLoad、静态方法、路径处理");

            try
            {
                var testConfig = new TestConfig
                {
                    Name = "扩展方法测试配置",
                    Version = 6
                };

                // 测试 YSave 扩展方法
                var saveResult = testConfig.YSave("./test-results/extension-test.json");
                if (!saveResult)
                {
                    Console.WriteLine("   ❌ YSave 扩展方法失败");
                    return false;
                }
                Console.WriteLine("   ✅ YSave 扩展方法成功");

                // 测试 YLoad 扩展方法
                var loadResult = testConfig.YLoad("./test-results/extension-test.json");
                if (loadResult == null || loadResult.Name != testConfig.Name)
                {
                    Console.WriteLine("   ❌ YLoad 扩展方法失败");
                    return false;
                }
                Console.WriteLine("   ✅ YLoad 扩展方法成功");

                // 测试静态方法
                var staticSaveResult = YConfigHelper.Save(testConfig, "./test-results/static-test.json");
                if (!staticSaveResult)
                {
                    Console.WriteLine("   ❌ 静态保存方法失败");
                    return false;
                }
                Console.WriteLine("   ✅ 静态保存方法成功");

                var staticLoadResult = YConfigHelper.Load<TestConfig>("./test-results/static-test.json");
                if (staticLoadResult == null || staticLoadResult.Name != testConfig.Name)
                {
                    Console.WriteLine("   ❌ 静态加载方法失败");
                    return false;
                }
                Console.WriteLine("   ✅ 静态加载方法成功");

                // 测试目录参数
                var dirSaveResult = testConfig.YSave(null, "./test-results/custom-dir");
                if (!dirSaveResult)
                {
                    Console.WriteLine("   ❌ 目录参数保存失败");
                    return false;
                }
                Console.WriteLine("   ✅ 目录参数保存成功");

                // 测试存在检查
                var existsResult = testConfig.YExists("./test-results/extension-test.json");
                if (!existsResult)
                {
                    Console.WriteLine("   ❌ 存在检查失败");
                    return false;
                }
                Console.WriteLine("   ✅ 存在检查成功");

                Console.WriteLine("   🎯 扩展方法测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 扩展方法测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 6. 集成测试

        private static bool TestIntegration()
        {
            Console.WriteLine("\n6️⃣ 集成测试 (整体功能)");
            Console.WriteLine("   测试范围: 多格式协作、错误处理、性能验证");

            try
            {
                var testConfig = new TestConfig
                {
                    Name = "集成测试配置",
                    Version = 7,
                    Tags = new List<string> { "integration", "test", "final" }
                };

                // 测试多格式保存
                var jsonSave = testConfig.YSave("./test-results/integration.json");
                var xmlSave = new YXmlProcessor().WriteXmlFromDictionary(
                    "./test-results/integration.xml",
                    new Dictionary<string, object>
                    {
                        ["Name"] = testConfig.Name,
                        ["Version"] = testConfig.Version.ToString(),
                        ["IsActive"] = testConfig.IsActive.ToString()
                    }
                );

                if (!jsonSave || !xmlSave)
                {
                    Console.WriteLine("   ❌ 多格式保存失败");
                    return false;
                }
                Console.WriteLine("   ✅ 多格式保存成功");

                // 测试错误处理
                var invalidLoad = testConfig.YLoad("./non-existent-file.json");
                if (invalidLoad != null)
                {
                    Console.WriteLine("   ❌ 错误处理失败");
                    return false;
                }
                Console.WriteLine("   ✅ 错误处理正确");

                // 测试配置管理器的多格式检测
                var manager = new YConfigManager(
                    new YJsonProcessor(),
                    new YXmlProcessor(),
                    new YIniProcessor()
                );

                var jsonFormat = manager.DetectFormat("./test-results/integration.json");
                var xmlFormat = manager.DetectFormat("./test-results/integration.xml");

                if (jsonFormat != ConfigFileFormat.Json || xmlFormat != ConfigFileFormat.Xml)
                {
                    Console.WriteLine("   ❌ 多格式检测失败");
                    return false;
                }
                Console.WriteLine("   ✅ 多格式检测成功");

                Console.WriteLine("   🎯 集成测试: 全部通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 集成测试异常: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
