using System;
using System.IO;
using System.Threading.Tasks;
using Xunit;
using Zylo.YIO.Security;

namespace Zylo.YIO.Tests.Security
{
    /// <summary>
    /// YFileEncryption 文件加密测试类
    /// 测试文件加密、解密、哈希计算等安全功能
    /// </summary>
    public class YFileEncryptionTests : IDisposable
    {
        private readonly YFileEncryption _encryption;
        private readonly string _testDirectory;
        private readonly string _testFile;
        private readonly string _encryptedFile;
        private readonly string _decryptedFile;
        private const string TestPassword = "TestSecureKey2024!@#";
        private const string TestContent = "This is a test file for encryption and decryption testing.";

        public YFileEncryptionTests()
        {
            _encryption = new YFileEncryption();
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileEncryptionTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            _testFile = Path.Combine(_testDirectory, "test.txt");
            _encryptedFile = Path.Combine(_testDirectory, "test.enc");
            _decryptedFile = Path.Combine(_testDirectory, "test_decrypted.txt");

            // 创建测试文件
            File.WriteAllText(_testFile, TestContent);
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 基础加密解密测试

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void EncryptFile_ValidInput_ShouldReturnTrue()
        {
            // Act
            var result = _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);

            // Assert
            Assert.True(result);
            Assert.True(File.Exists(_encryptedFile));
            Assert.True(new FileInfo(_encryptedFile).Length > 0);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void DecryptFile_ValidInput_ShouldReturnTrue()
        {
            // Arrange
            _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);

            // Act
            var result = _encryption.DecryptFile(_encryptedFile, _decryptedFile, TestPassword);

            // Assert
            Assert.True(result);
            Assert.True(File.Exists(_decryptedFile));

            var decryptedContent = File.ReadAllText(_decryptedFile);
            Assert.Equal(TestContent, decryptedContent);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void EncryptDecrypt_RoundTrip_ShouldPreserveContent()
        {
            // Act
            var encryptResult = _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);
            var decryptResult = _encryption.DecryptFile(_encryptedFile, _decryptedFile, TestPassword);

            // Assert
            Assert.True(encryptResult);
            Assert.True(decryptResult);

            var originalContent = File.ReadAllText(_testFile);
            var decryptedContent = File.ReadAllText(_decryptedFile);
            Assert.Equal(originalContent, decryptedContent);
        }

        #endregion

        #region 异步加密解密测试

        [Fact]
        [Trait("Category", "FileEncryption")]
        public async Task EncryptFileAsync_ValidInput_ShouldReturnTrue()
        {
            // Act
            var result = await _encryption.EncryptFileAsync(_testFile, _encryptedFile, TestPassword);

            // Assert
            Assert.True(result);
            Assert.True(File.Exists(_encryptedFile));
            Assert.True(new FileInfo(_encryptedFile).Length > 0);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public async Task DecryptFileAsync_ValidInput_ShouldReturnTrue()
        {
            // Arrange
            await _encryption.EncryptFileAsync(_testFile, _encryptedFile, TestPassword);

            // Act
            var result = await _encryption.DecryptFileAsync(_encryptedFile, _decryptedFile, TestPassword);

            // Assert
            Assert.True(result);
            Assert.True(File.Exists(_decryptedFile));

            var decryptedContent = File.ReadAllText(_decryptedFile);
            Assert.Equal(TestContent, decryptedContent);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public async Task EncryptDecryptAsync_RoundTrip_ShouldPreserveContent()
        {
            // Act
            var encryptResult = await _encryption.EncryptFileAsync(_testFile, _encryptedFile, TestPassword);
            var decryptResult = await _encryption.DecryptFileAsync(_encryptedFile, _decryptedFile, TestPassword);

            // Assert
            Assert.True(encryptResult);
            Assert.True(decryptResult);

            var originalContent = File.ReadAllText(_testFile);
            var decryptedContent = File.ReadAllText(_decryptedFile);
            Assert.Equal(originalContent, decryptedContent);
        }

        #endregion

        #region 哈希计算测试

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void CalculateMD5_ValidFile_ShouldReturnCorrectHash()
        {
            // Act
            var hash = _encryption.CalculateMD5(_testFile);

            // Assert
            Assert.NotNull(hash);
            Assert.Equal(32, hash.Length); // MD5 hash is 32 characters
            Assert.Matches("^[a-f0-9]+$", hash); // Should be lowercase hex
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void CalculateSHA256_ValidFile_ShouldReturnCorrectHash()
        {
            // Act
            var hash = _encryption.CalculateSHA256(_testFile);

            // Assert
            Assert.NotNull(hash);
            Assert.Equal(64, hash.Length); // SHA256 hash is 64 characters
            Assert.Matches("^[a-f0-9]+$", hash); // Should be lowercase hex
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void CalculateSHA512_ValidFile_ShouldReturnCorrectHash()
        {
            // Act
            var hash = _encryption.CalculateSHA512(_testFile);

            // Assert
            Assert.NotNull(hash);
            Assert.Equal(128, hash.Length); // SHA512 hash is 128 characters
            Assert.Matches("^[a-f0-9]+$", hash); // Should be lowercase hex
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void VerifyFileHash_CorrectHash_ShouldReturnTrue()
        {
            // Arrange
            var expectedHash = _encryption.CalculateSHA256(_testFile);

            // Act
            var result = _encryption.VerifyFileHash(_testFile, expectedHash, HashAlgorithmType.SHA256);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void VerifyFileHash_IncorrectHash_ShouldReturnFalse()
        {
            // Arrange
            var incorrectHash = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef";

            // Act
            var result = _encryption.VerifyFileHash(_testFile, incorrectHash, HashAlgorithmType.SHA256);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 错误处理测试

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void EncryptFile_NonExistentFile_ShouldReturnFalse()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _encryption.EncryptFile(nonExistentFile, _encryptedFile, TestPassword);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void DecryptFile_WrongPassword_ShouldReturnFalse()
        {
            // Arrange
            _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);

            // Act
            var result = _encryption.DecryptFile(_encryptedFile, _decryptedFile, "WrongPassword");

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FileEncryption")]
        public void CalculateMD5_NonExistentFile_ShouldThrowException()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act & Assert
            Assert.Throws<ArgumentException>(() => _encryption.CalculateMD5(nonExistentFile));
        }

        #endregion

        #region 大文件测试

        [Fact]
        [Trait("Category", "FileEncryption")]
        public async Task EncryptDecryptLargeFile_ShouldWork()
        {
            // Arrange
            var largeFile = Path.Combine(_testDirectory, "large.txt");
            var largeEncrypted = Path.Combine(_testDirectory, "large.enc");
            var largeDecrypted = Path.Combine(_testDirectory, "large_decrypted.txt");

            // Create a larger test file (1MB)
            var largeContent = new string('A', 1024 * 1024);
            File.WriteAllText(largeFile, largeContent);

            // Act
            var encryptResult = await _encryption.EncryptFileAsync(largeFile, largeEncrypted, TestPassword);
            var decryptResult = await _encryption.DecryptFileAsync(largeEncrypted, largeDecrypted, TestPassword);

            // Assert
            Assert.True(encryptResult);
            Assert.True(decryptResult);

            var decryptedContent = File.ReadAllText(largeDecrypted);
            Assert.Equal(largeContent, decryptedContent);
        }

        #endregion

        #region 新增功能测试

        [Fact]
        [Trait("Category", "PasswordValidation")]
        public void ValidatePasswordStrength_WeakPassword_ShouldReturnInvalid()
        {
            // Arrange
            var weakPassword = "123";

            // Act
            var result = _encryption.ValidatePasswordStrength(weakPassword);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(PasswordStrength.VeryWeak, result.Strength);
            Assert.Contains("密码长度至少需要8位", result.Issues);
        }

        [Fact]
        [Trait("Category", "PasswordValidation")]
        public void ValidatePasswordStrength_StrongPassword_ShouldReturnValid()
        {
            // Arrange
            var strongPassword = "MySecureKey2024!@#";

            // Act
            var result = _encryption.ValidatePasswordStrength(strongPassword);

            // Assert
            Assert.True(result.IsValid);
            Assert.True(result.Strength >= PasswordStrength.Medium);
            Assert.Empty(result.Issues);
        }

        [Fact]
        [Trait("Category", "PasswordGeneration")]
        public void GenerateStrongPassword_DefaultLength_ShouldReturnValidPassword()
        {
            // Act
            var password = _encryption.GenerateStrongPassword();

            // Assert
            Assert.Equal(16, password.Length);

            var validation = _encryption.ValidatePasswordStrength(password);
            Assert.True(validation.IsValid);
            Assert.True(validation.Strength >= PasswordStrength.Strong);
        }

        [Fact]
        [Trait("Category", "PasswordGeneration")]
        public void GenerateStrongPassword_CustomLength_ShouldReturnCorrectLength()
        {
            // Arrange
            var customLength = 24;

            // Act
            var password = _encryption.GenerateStrongPassword(customLength);

            // Assert
            Assert.Equal(customLength, password.Length);

            var validation = _encryption.ValidatePasswordStrength(password);
            Assert.True(validation.IsValid);
        }

        [Fact]
        [Trait("Category", "FileValidation")]
        public void IsEncryptedFile_ValidEncryptedFile_ShouldReturnTrue()
        {
            // Arrange
            _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);

            // Act
            var result = _encryption.IsEncryptedFile(_encryptedFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FileValidation")]
        public void IsEncryptedFile_RegularFile_ShouldReturnFalse()
        {
            // Act
            var result = _encryption.IsEncryptedFile(_testFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FileValidation")]
        public void GetEncryptedFileInfo_ValidFile_ShouldReturnCorrectInfo()
        {
            // Arrange
            _encryption.EncryptFile(_testFile, _encryptedFile, TestPassword);

            // Act
            var info = _encryption.GetEncryptedFileInfo(_encryptedFile);

            // Assert
            Assert.NotNull(info);
            Assert.Equal(_encryptedFile, info.FilePath);
            Assert.True(info.IsValid);
            Assert.True(info.HasMagicNumber);
            Assert.True(info.FileSize > 0);
            Assert.True(info.EstimatedOriginalSize > 0);
        }

        [Fact]
        [Trait("Category", "BatchOperations")]
        public void BatchEncryptDirectory_MultipleFiles_ShouldEncryptAll()
        {
            // Arrange
            var sourceDir = Path.Combine(_testDirectory, "source");
            var targetDir = Path.Combine(_testDirectory, "encrypted");
            Directory.CreateDirectory(sourceDir);

            // 创建多个测试文件
            for (int i = 0; i < 3; i++)
            {
                var filePath = Path.Combine(sourceDir, $"test{i}.txt");
                File.WriteAllText(filePath, $"Test content {i}");
            }

            // Act
            var result = _encryption.BatchEncryptDirectory(sourceDir, targetDir, TestPassword);

            // Assert
            Assert.Equal(3, result.TotalFiles);
            Assert.Equal(3, result.SuccessCount);
            Assert.Equal(0, result.FailureCount);
            Assert.Equal(100.0, result.SuccessRate);
            Assert.True(result.Duration.TotalMilliseconds > 0);
        }

        [Fact]
        [Trait("Category", "BatchOperations")]
        public void BatchDecryptDirectory_EncryptedFiles_ShouldDecryptAll()
        {
            // Arrange
            var sourceDir = Path.Combine(_testDirectory, "source");
            var encryptedDir = Path.Combine(_testDirectory, "encrypted");
            var decryptedDir = Path.Combine(_testDirectory, "decrypted");
            Directory.CreateDirectory(sourceDir);

            // 创建并加密多个文件
            var testContents = new[] { "Content 1", "Content 2", "Content 3" };
            for (int i = 0; i < testContents.Length; i++)
            {
                var filePath = Path.Combine(sourceDir, $"test{i}.txt");
                File.WriteAllText(filePath, testContents[i]);
            }

            _encryption.BatchEncryptDirectory(sourceDir, encryptedDir, TestPassword);

            // Act
            var result = _encryption.BatchDecryptDirectory(encryptedDir, decryptedDir, TestPassword);

            // Assert
            Assert.Equal(testContents.Length, result.TotalFiles);
            Assert.Equal(testContents.Length, result.SuccessCount);
            Assert.Equal(0, result.FailureCount);

            // 验证解密后的内容
            for (int i = 0; i < testContents.Length; i++)
            {
                var decryptedFile = Path.Combine(decryptedDir, $"test{i}.txt");
                Assert.True(File.Exists(decryptedFile));
                var content = File.ReadAllText(decryptedFile);
                Assert.Equal(testContents[i], content);
            }
        }

        [Fact]
        [Trait("Category", "SecureDelete")]
        public void SecureDeleteFile_ExistingFile_ShouldDeleteSuccessfully()
        {
            // Arrange
            var tempFile = Path.Combine(_testDirectory, "temp_delete.txt");
            File.WriteAllText(tempFile, "This file will be securely deleted");

            // Act
            var result = _encryption.SecureDeleteFile(tempFile, 2);

            // Assert
            Assert.True(result);
            Assert.False(File.Exists(tempFile));
        }

        [Fact]
        [Trait("Category", "PasswordValidation")]
        public void ValidatePasswordStrength_CommonPassword_ShouldDetectWeakness()
        {
            // Arrange
            var commonPassword = "password123";

            // Act
            var result = _encryption.ValidatePasswordStrength(commonPassword);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("密码包含常见弱密码模式", result.Issues);
        }

        [Fact]
        [Trait("Category", "PasswordValidation")]
        public void ValidatePasswordStrength_RepeatedCharacters_ShouldDetectWeakness()
        {
            // Arrange
            var repeatedPassword = "Aaaaa123!";

            // Act
            var result = _encryption.ValidatePasswordStrength(repeatedPassword);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("密码包含过多重复字符", result.Issues);
        }

        #endregion
    }
}
