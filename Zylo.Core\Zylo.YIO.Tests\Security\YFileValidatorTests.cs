using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using Xunit;
using Zylo.YIO.Security;
using Zylo.YIO.Config;

namespace Zylo.YIO.Tests.Security
{
    /// <summary>
    /// YFileValidator 文件验证测试类
    /// 测试文件安全性检查、格式验证、批量扫描等功能
    /// </summary>
    public class YFileValidatorTests : IDisposable
    {
        private readonly YFileValidator _validator;
        private readonly string _testDirectory;
        private readonly YIOConfig _config;

        public YFileValidatorTests()
        {
            // 创建测试目录
            _testDirectory = Path.Combine(Path.GetTempPath(), "YFileValidatorTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDirectory);

            // 创建测试配置
            _config = new YIOConfig
            {
                MaxSingleFileSize = 10 * 1024 * 1024, // 10MB for testing
                BlockedExtensions = new[] { ".exe", ".bat", ".cmd" },
                EnablePathValidation = true,
                DefaultWorkingDirectory = _testDirectory
            };

            _validator = new YFileValidator(_config);
        }

        public void Dispose()
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }

        #region 基础安全检查测试

        [Fact]
        [Trait("Category", "BasicSecurity")]
        public void IsSafeFile_ValidTextFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "safe.txt");
            File.WriteAllText(testFile, "This is a safe text file.");

            // Act
            var result = _validator.IsSafeFile(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "BasicSecurity")]
        public void IsSafeFile_DangerousExtension_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "malware.exe");
            File.WriteAllText(testFile, "fake exe content");

            // Act
            var result = _validator.IsSafeFile(testFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "BasicSecurity")]
        public void IsSafeFile_NonExistentFile_ShouldReturnFalse()
        {
            // Arrange
            var nonExistentFile = Path.Combine(_testDirectory, "nonexistent.txt");

            // Act
            var result = _validator.IsSafeFile(nonExistentFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "BasicSecurity")]
        public void IsSafeFile_EmptyPath_ShouldReturnFalse()
        {
            // Act
            var result = _validator.IsSafeFile("");

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "BasicSecurity")]
        public void IsSafeFile_NullPath_ShouldReturnFalse()
        {
            // Act
            var result = _validator.IsSafeFile(null);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 扩展名安全检查测试

        [Fact]
        [Trait("Category", "ExtensionSafety")]
        public void IsExtensionSafe_SafeExtensions_ShouldReturnTrue()
        {
            // Arrange
            var safeFiles = new[]
            {
                "document.pdf", "image.jpg", "text.txt", "data.json", "config.xml"
            };

            // Act & Assert
            foreach (var file in safeFiles)
            {
                var result = _validator.IsExtensionSafe(file);
                Assert.True(result, $"File {file} should be considered safe");
            }
        }

        [Fact]
        [Trait("Category", "ExtensionSafety")]
        public void IsExtensionSafe_DangerousExtensions_ShouldReturnFalse()
        {
            // Arrange
            var dangerousFiles = new[]
            {
                "malware.exe", "script.bat", "virus.scr", "trojan.com", "backdoor.dll"
            };

            // Act & Assert
            foreach (var file in dangerousFiles)
            {
                var result = _validator.IsExtensionSafe(file);
                Assert.False(result, $"File {file} should be considered dangerous");
            }
        }

        [Fact]
        [Trait("Category", "ExtensionSafety")]
        public void IsExtensionSafe_DoubleExtension_ShouldDetectThreat()
        {
            // Arrange
            var doubleExtensionFile = "document.txt.exe";

            // Act
            var result = _validator.IsExtensionSafe(doubleExtensionFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "ExtensionSafety")]
        public void IsExtensionSafe_NoExtension_ShouldHandleCorrectly()
        {
            // Arrange
            var noExtensionFile = "README";

            // Act
            var result = _validator.IsExtensionSafe(noExtensionFile);

            // Assert
            // 根据配置，启用严格验证时应该拒绝无扩展名文件
            Assert.False(result);
        }

        #endregion

        #region 文件大小验证测试

        [Fact]
        [Trait("Category", "FileSizeValidation")]
        public void IsFileSizeValid_NormalSizeFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "normal.txt");
            var content = new string('A', 1024); // 1KB
            File.WriteAllText(testFile, content);

            // Act
            var result = _validator.IsFileSizeValid(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FileSizeValidation")]
        public void IsFileSizeValid_EmptyFile_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "empty.txt");
            File.WriteAllText(testFile, "");

            // Act
            var result = _validator.IsFileSizeValid(testFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FileSizeValidation")]
        public void IsFileSizeValid_OversizedFile_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "oversized.txt");
            // 创建一个超过配置限制的文件（11MB > 10MB limit）
            var content = new string('A', 11 * 1024 * 1024);
            File.WriteAllText(testFile, content);

            // Act
            var result = _validator.IsFileSizeValid(testFile);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 文件签名验证测试

        [Fact]
        [Trait("Category", "FileSignature")]
        public void IsFileSignatureValid_ValidPdfFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test.pdf");
            var pdfHeader = new byte[] { 0x25, 0x50, 0x44, 0x46 }; // %PDF
            File.WriteAllBytes(testFile, pdfHeader.Concat(new byte[100]).ToArray());

            // Act
            var result = _validator.IsFileSignatureValid(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FileSignature")]
        public void IsFileSignatureValid_InvalidPdfFile_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "fake.pdf");
            File.WriteAllText(testFile, "This is not a real PDF file");

            // Act
            var result = _validator.IsFileSignatureValid(testFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FileSignature")]
        public void IsFileSignatureValid_ValidJpegFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "test.jpg");
            var jpegHeader = new byte[] { 0xFF, 0xD8, 0xFF };
            File.WriteAllBytes(testFile, jpegHeader.Concat(new byte[100]).ToArray());

            // Act
            var result = _validator.IsFileSignatureValid(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FileSignature")]
        public void IsFileSignatureValid_UnknownFormat_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "unknown.xyz");
            File.WriteAllText(testFile, "Unknown format file");

            // Act
            var result = _validator.IsFileSignatureValid(testFile);

            // Assert
            Assert.True(result); // 未知格式跳过签名检查
        }

        #endregion

        #region 路径安全检查测试

        [Fact]
        [Trait("Category", "PathSafety")]
        public void IsPathSafe_NormalPath_ShouldReturnTrue()
        {
            // Arrange
            var normalPath = Path.Combine(_testDirectory, "normal", "file.txt");

            // Act
            var result = _validator.IsPathSafe(normalPath);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "PathSafety")]
        public void IsPathSafe_PathTraversalAttack_ShouldReturnFalse()
        {
            // Arrange
            var maliciousPath = Path.Combine(_testDirectory, "..", "..", "system32", "evil.exe");

            // Act
            var result = _validator.IsPathSafe(maliciousPath);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "PathSafety")]
        public void IsPathSafe_IllegalCharacters_ShouldReturnFalse()
        {
            // Arrange
            var illegalPath = Path.Combine(_testDirectory, "file<>name.txt");

            // Act
            var result = _validator.IsPathSafe(illegalPath);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "PathSafety")]
        public void IsPathSafe_TooLongPath_ShouldReturnFalse()
        {
            // Arrange
            var longPath = Path.Combine(_testDirectory, new string('a', 300)); // 超过260字符限制

            // Act
            var result = _validator.IsPathSafe(longPath);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 格式验证测试

        [Fact]
        [Trait("Category", "FormatValidation")]
        public void IsValidImageFile_ValidJpegFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "image.jpg");
            var jpegHeader = new byte[] { 0xFF, 0xD8, 0xFF };
            File.WriteAllBytes(testFile, jpegHeader.Concat(new byte[100]).ToArray());

            // Act
            var result = _validator.IsValidImageFile(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FormatValidation")]
        public void IsValidImageFile_InvalidExtension_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "notimage.txt");
            File.WriteAllText(testFile, "This is not an image");

            // Act
            var result = _validator.IsValidImageFile(testFile);

            // Assert
            Assert.False(result);
        }

        [Fact]
        [Trait("Category", "FormatValidation")]
        public void IsValidDocumentFile_ValidPdfFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "document.pdf");
            var pdfHeader = new byte[] { 0x25, 0x50, 0x44, 0x46 };
            File.WriteAllBytes(testFile, pdfHeader.Concat(new byte[100]).ToArray());

            // Act
            var result = _validator.IsValidDocumentFile(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "FormatValidation")]
        public void IsValidArchiveFile_ValidZipFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "archive.zip");
            var zipHeader = new byte[] { 0x50, 0x4B, 0x03, 0x04 };
            File.WriteAllBytes(testFile, zipHeader.Concat(new byte[100]).ToArray());

            // Act
            var result = _validator.IsValidArchiveFile(testFile);

            // Assert
            Assert.True(result);
        }

        #endregion

        #region 批量验证测试

        [Fact]
        [Trait("Category", "BatchValidation")]
        public void ValidateFiles_MixedFiles_ShouldReturnCorrectResults()
        {
            // Arrange
            var safeFile = Path.Combine(_testDirectory, "safe.txt");
            var dangerousFile = Path.Combine(_testDirectory, "dangerous.exe");

            File.WriteAllText(safeFile, "Safe content");
            File.WriteAllText(dangerousFile, "Dangerous content");

            var files = new[] { safeFile, dangerousFile };

            // Act
            var results = _validator.ValidateFiles(files);

            // Assert
            Assert.Equal(2, results.Count);
            Assert.True(results[safeFile]);
            Assert.False(results[dangerousFile]);
        }

        [Fact]
        [Trait("Category", "BatchValidation")]
        public void GetDangerousFiles_DirectoryWithMixedFiles_ShouldReturnOnlyDangerous()
        {
            // Arrange
            var safeFile = Path.Combine(_testDirectory, "safe.txt");
            var dangerousFile = Path.Combine(_testDirectory, "dangerous.exe");

            File.WriteAllText(safeFile, "Safe content");
            File.WriteAllText(dangerousFile, "Dangerous content");

            // Act
            var dangerousFiles = _validator.GetDangerousFiles(_testDirectory, false);

            // Assert
            Assert.Single(dangerousFiles);
            Assert.Contains(dangerousFile, dangerousFiles);
            Assert.DoesNotContain(safeFile, dangerousFiles);
        }

        [Fact]
        [Trait("Category", "BatchValidation")]
        public void ValidateFiles_EmptyList_ShouldReturnEmptyResults()
        {
            // Arrange
            var emptyList = new string[0];

            // Act
            var results = _validator.ValidateFiles(emptyList);

            // Assert
            Assert.Empty(results);
        }

        #endregion

        #region MIME类型测试

        [Fact]
        [Trait("Category", "MimeType")]
        public void GetMimeType_CommonExtensions_ShouldReturnCorrectTypes()
        {
            // Arrange & Act & Assert
            Assert.Equal("text/plain", _validator.GetMimeType("file.txt"));
            Assert.Equal("application/pdf", _validator.GetMimeType("document.pdf"));
            Assert.Equal("image/jpeg", _validator.GetMimeType("photo.jpg"));
            Assert.Equal("image/png", _validator.GetMimeType("image.png"));
            Assert.Equal("application/zip", _validator.GetMimeType("archive.zip"));
            Assert.Equal("application/json", _validator.GetMimeType("data.json"));
        }

        [Fact]
        [Trait("Category", "MimeType")]
        public void GetMimeType_UnknownExtension_ShouldReturnOctetStream()
        {
            // Act
            var result = _validator.GetMimeType("file.unknown");

            // Assert
            Assert.Equal("application/octet-stream", result);
        }

        [Fact]
        [Trait("Category", "MimeType")]
        public void GetMimeType_NoExtension_ShouldReturnOctetStream()
        {
            // Act
            var result = _validator.GetMimeType("README");

            // Assert
            Assert.Equal("application/octet-stream", result);
        }

        #endregion

        #region 文本文件检测测试

        [Fact]
        [Trait("Category", "TextFileDetection")]
        public void IsTextFile_PlainTextFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "text.txt");
            File.WriteAllText(testFile, "This is plain text content.");

            // Act
            var result = _validator.IsTextFile(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "TextFileDetection")]
        public void IsTextFile_JsonFile_ShouldReturnTrue()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "data.json");
            File.WriteAllText(testFile, "{\"key\": \"value\"}");

            // Act
            var result = _validator.IsTextFile(testFile);

            // Assert
            Assert.True(result);
        }

        [Fact]
        [Trait("Category", "TextFileDetection")]
        public void IsTextFile_BinaryFile_ShouldReturnFalse()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "binary.bin");
            var binaryData = new byte[] { 0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD };
            File.WriteAllBytes(testFile, binaryData);

            // Act
            var result = _validator.IsTextFile(testFile);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region 错误处理测试

        [Fact]
        [Trait("Category", "ErrorHandling")]
        public void IsSafeFile_InaccessibleFile_ShouldHandleGracefully()
        {
            // Arrange
            var testFile = Path.Combine(_testDirectory, "readonly.txt");
            File.WriteAllText(testFile, "content");

            // 设置为只读（模拟访问权限问题）
            File.SetAttributes(testFile, FileAttributes.ReadOnly);

            try
            {
                // Act
                var result = _validator.IsSafeFile(testFile);

                // Assert
                // 应该能够处理而不抛出异常
                Assert.True(result || !result); // 只要不抛异常就行
            }
            finally
            {
                // 清理
                File.SetAttributes(testFile, FileAttributes.Normal);
            }
        }

        [Fact]
        [Trait("Category", "ErrorHandling")]
        public void GetDangerousFiles_NonExistentDirectory_ShouldReturnEmptyList()
        {
            // Arrange
            var nonExistentDir = Path.Combine(_testDirectory, "nonexistent");

            // Act
            var result = _validator.GetDangerousFiles(nonExistentDir);

            // Assert
            Assert.Empty(result);
        }

        #endregion

        #region 性能测试

        [Fact]
        [Trait("Category", "Performance")]
        public void ValidateFiles_LargeFileList_ShouldCompleteInReasonableTime()
        {
            // Arrange
            var files = new List<string>();
            for (int i = 0; i < 100; i++)
            {
                var testFile = Path.Combine(_testDirectory, $"test{i}.txt");
                File.WriteAllText(testFile, $"Content {i}");
                files.Add(testFile);
            }

            var startTime = DateTime.Now;

            // Act
            var results = _validator.ValidateFiles(files);

            // Assert
            var duration = DateTime.Now - startTime;
            Assert.Equal(100, results.Count);
            Assert.True(duration.TotalSeconds < 10); // 应该在10秒内完成
        }

        #endregion
    }
}
