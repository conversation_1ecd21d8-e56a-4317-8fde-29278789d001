﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests;

/// <summary>
/// YFile 单元测试
/// 测试文件创建、读写、存在性检查等核心功能
/// </summary>
public class YFileTests : IDisposable
{
    #region 测试设置和清理

    private readonly YFile _fileOps;
    private readonly string _testDirectory;
    private readonly List<string> _testFiles;

    public YFileTests()
    {
        _fileOps = new YFile();
        _testDirectory = Path.Combine(Path.GetTempPath(), "YIOTests", Guid.NewGuid().ToString());
        _testFiles = new List<string>();

        // 创建测试目录
        Directory.CreateDirectory(_testDirectory);
    }

    public void Dispose()
    {
        // 清理测试文件和目录
        try
        {
            if (Directory.Exists(_testDirectory))
            {
                Directory.Delete(_testDirectory, true);
            }
        }
        catch
        {
            // 忽略清理错误
        }
    }

    private string GetTestFilePath(string fileName)
    {
        var filePath = Path.Combine(_testDirectory, fileName);
        _testFiles.Add(filePath);
        return filePath;
    }

    #endregion

    #region 文件创建和存在性检查测试

    [Fact]
    public void CreateFile_ShouldCreateNewFile_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("test_create.txt");

        // Act
        var result = _fileOps.CreateFile(filePath);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
    }

    [Fact]
    public void CreateFile_ShouldReturnFalse_WhenFileExistsAndOverwriteIsFalse()
    {
        // Arrange
        var filePath = GetTestFilePath("test_existing.txt");
        File.WriteAllText(filePath, "existing content");

        // Act
        var result = _fileOps.CreateFile(filePath, overwrite: false);

        // Assert
        result.Should().BeFalse();
        File.ReadAllText(filePath).Should().Be("existing content");
    }

    [Fact]
    public void CreateFile_ShouldOverwriteFile_WhenFileExistsAndOverwriteIsTrue()
    {
        // Arrange
        var filePath = GetTestFilePath("test_overwrite.txt");
        File.WriteAllText(filePath, "existing content");

        // Act
        var result = _fileOps.CreateFile(filePath, overwrite: true);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
        File.ReadAllText(filePath).Should().BeEmpty();
    }

    [Fact]
    public void FileExists_ShouldReturnTrue_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_exists.txt");
        File.WriteAllText(filePath, "test content");

        // Act
        var result = _fileOps.FileExists(filePath);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void FileExists_ShouldReturnFalse_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("non_existing.txt");

        // Act
        var result = _fileOps.FileExists(filePath);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CreateFileIfNotExists_ShouldCreateFile_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("test_conditional.txt");

        // Act
        var result = _fileOps.CreateFileIfNotExists(filePath);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
    }

    [Fact]
    public void CreateFileIfNotExists_ShouldReturnTrue_WhenFileAlreadyExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_conditional_existing.txt");
        File.WriteAllText(filePath, "existing content");

        // Act
        var result = _fileOps.CreateFileIfNotExists(filePath);

        // Assert
        result.Should().BeTrue();
        File.ReadAllText(filePath).Should().Be("existing content");
    }

    #endregion

    #region Touch文件测试

    [Fact]
    public void TouchFile_ShouldCreateFile_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("test_touch_new.txt");

        // Act
        var result = _fileOps.TouchFile(filePath);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
    }

    [Fact]
    public void TouchFile_ShouldUpdateTimestamp_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_touch_existing.txt");
        File.WriteAllText(filePath, "test content");
        var originalTime = File.GetLastWriteTime(filePath);

        // 等待一小段时间确保时间戳不同
        Thread.Sleep(10);

        // Act
        var result = _fileOps.TouchFile(filePath);

        // Assert
        result.Should().BeTrue();
        var newTime = File.GetLastWriteTime(filePath);
        newTime.Should().BeAfter(originalTime);
    }

    #endregion

    #region 唯一文件名生成测试

    [Fact]
    public void GetUniqueFileName_ShouldReturnBaseName_WhenFileDoesNotExist()
    {
        // Arrange
        var baseName = "unique_test";
        var extension = "txt";

        // Act
        var result = _fileOps.GetUniqueFileName(_testDirectory, baseName, extension);

        // Assert
        result.Should().Be($"{baseName}.{extension}");
    }

    [Fact]
    public void GetUniqueFileName_ShouldReturnNumberedName_WhenFileExists()
    {
        // Arrange
        var baseName = "unique_test";
        var extension = "txt";
        var existingFile = Path.Combine(_testDirectory, $"{baseName}.{extension}");
        File.WriteAllText(existingFile, "existing");

        // Act
        var result = _fileOps.GetUniqueFileName(_testDirectory, baseName, extension);

        // Assert
        result.Should().Be($"{baseName}_1.{extension}");
    }

    #endregion

    #region 文件读取测试

    [Fact]
    public void ReadAllText_ShouldReturnContent_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_read.txt");
        var expectedContent = "Hello, YIO Framework!\n测试中文内容。";
        File.WriteAllText(filePath, expectedContent, Encoding.UTF8);

        // Act
        var result = _fileOps.ReadAllText(filePath);

        // Assert
        result.Should().Be(expectedContent);
    }

    [Fact]
    public void ReadAllText_ShouldReturnEmpty_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("non_existing_read.txt");

        // Act
        var result = _fileOps.ReadAllText(filePath);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ReadAllBytes_ShouldReturnBytes_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_read_bytes.txt");
        var expectedBytes = Encoding.UTF8.GetBytes("Test content");
        File.WriteAllBytes(filePath, expectedBytes);

        // Act
        var result = _fileOps.ReadAllBytes(filePath);

        // Assert
        result.Should().Equal(expectedBytes);
    }

    [Fact]
    public void ReadAllLines_ShouldReturnLines_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_read_lines.txt");
        var expectedLines = new[] { "Line 1", "Line 2", "Line 3" };
        File.WriteAllLines(filePath, expectedLines, Encoding.UTF8);

        // Act
        var result = _fileOps.ReadAllLines(filePath);

        // Assert
        result.Should().Equal(expectedLines);
    }

    [Fact]
    public void ReadLines_ShouldReturnEnumerable_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_read_lines_enum.txt");
        var expectedLines = new[] { "Line 1", "Line 2", "Line 3" };
        File.WriteAllLines(filePath, expectedLines, Encoding.UTF8);

        // Act
        var result = _fileOps.ReadLines(filePath).ToArray();

        // Assert
        result.Should().Equal(expectedLines);
    }

    #endregion

    #region 参数验证测试

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void CreateFile_ShouldHandleInvalidPath_Gracefully(string invalidPath)
    {
        // Act
        var result = _fileOps.CreateFile(invalidPath);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData("   ")]
    public void FileExists_ShouldReturnFalse_ForInvalidPath(string invalidPath)
    {
        // Act
        var result = _fileOps.FileExists(invalidPath);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CreateFile_ShouldHandleNullPath_Gracefully()
    {
        // Act
        var result = _fileOps.CreateFile(null!);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void FileExists_ShouldReturnFalse_ForNullPath()
    {
        // Act
        var result = _fileOps.FileExists(null!);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region 文件写入操作测试

    [Fact]
    public void WriteAllText_ShouldCreateFileWithContent_WhenPathIsValid()
    {
        // Arrange
        var filePath = GetTestFilePath("test_write_text.txt");
        var content = "Hello, YIO Framework!\n测试中文内容。";

        // Act
        var result = _fileOps.WriteAllText(filePath, content);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
        File.ReadAllText(filePath, Encoding.UTF8).Should().Be(content);
    }

    [Fact]
    public void WriteAllText_ShouldAppendContent_WhenAppendIsTrue()
    {
        // Arrange
        var filePath = GetTestFilePath("test_append_text.txt");
        var initialContent = "Initial content\n";
        var appendContent = "Appended content\n";
        File.WriteAllText(filePath, initialContent);

        // Act
        var result = _fileOps.WriteAllText(filePath, appendContent, append: true);

        // Assert
        result.Should().BeTrue();
        var finalContent = File.ReadAllText(filePath);
        finalContent.Should().Be(initialContent + appendContent);
    }

    [Fact]
    public void WriteAllBytes_ShouldCreateFileWithBytes_WhenPathIsValid()
    {
        // Arrange
        var filePath = GetTestFilePath("test_write_bytes.bin");
        var bytes = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F }; // "Hello"

        // Act
        var result = _fileOps.WriteAllBytes(filePath, bytes);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
        File.ReadAllBytes(filePath).Should().Equal(bytes);
    }

    [Fact]
    public void WriteAllLines_ShouldCreateFileWithLines_WhenPathIsValid()
    {
        // Arrange
        var filePath = GetTestFilePath("test_write_lines.txt");
        var lines = new[] { "Line 1", "Line 2", "Line 3" };

        // Act
        var result = _fileOps.WriteAllLines(filePath, lines);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeTrue();
        File.ReadAllLines(filePath).Should().Equal(lines);
    }

    [Fact]
    public void WriteAllText_ShouldReturnFalse_ForInvalidPath()
    {
        // Act
        var result = _fileOps.WriteAllText("", "content");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void WriteAllBytes_ShouldReturnFalse_ForNullBytes()
    {
        // Arrange
        var filePath = GetTestFilePath("test_null_bytes.bin");

        // Act
        var result = _fileOps.WriteAllBytes(filePath, null!);

        // Assert
        result.Should().BeFalse();
    }

    #endregion

    #region 文件复制和移动操作测试

    [Fact]
    public void CopyFile_ShouldCopyFileSuccessfully_WhenSourceExists()
    {
        // Arrange
        var sourceFile = GetTestFilePath("source.txt");
        var destinationFile = GetTestFilePath("destination.txt");
        var content = "Test content for copy";
        File.WriteAllText(sourceFile, content);

        // Act
        var result = _fileOps.CopyFile(sourceFile, destinationFile);

        // Assert
        result.Should().BeTrue();
        File.Exists(destinationFile).Should().BeTrue();
        File.ReadAllText(destinationFile).Should().Be(content);
        File.Exists(sourceFile).Should().BeTrue(); // 源文件应该仍然存在
    }

    [Fact]
    public void CopyFile_ShouldReturnFalse_WhenSourceDoesNotExist()
    {
        // Arrange
        var sourceFile = GetTestFilePath("non_existing_source.txt");
        var destinationFile = GetTestFilePath("destination.txt");

        // Act
        var result = _fileOps.CopyFile(sourceFile, destinationFile);

        // Assert
        result.Should().BeFalse();
        File.Exists(destinationFile).Should().BeFalse();
    }

    [Fact]
    public void MoveFile_ShouldMoveFileSuccessfully_WhenSourceExists()
    {
        // Arrange
        var sourceFile = GetTestFilePath("source_move.txt");
        var destinationFile = GetTestFilePath("destination_move.txt");
        var content = "Test content for move";
        File.WriteAllText(sourceFile, content);

        // Act
        var result = _fileOps.MoveFile(sourceFile, destinationFile);

        // Assert
        result.Should().BeTrue();
        File.Exists(destinationFile).Should().BeTrue();
        File.ReadAllText(destinationFile).Should().Be(content);
        File.Exists(sourceFile).Should().BeFalse(); // 源文件应该不存在
    }

    [Fact]
    public void MoveFile_ShouldReturnFalse_WhenDestinationExistsAndOverwriteIsFalse()
    {
        // Arrange
        var sourceFile = GetTestFilePath("source_move2.txt");
        var destinationFile = GetTestFilePath("destination_move2.txt");
        File.WriteAllText(sourceFile, "source content");
        File.WriteAllText(destinationFile, "destination content");

        // Act
        var result = _fileOps.MoveFile(sourceFile, destinationFile, overwrite: false);

        // Assert
        result.Should().BeFalse();
        File.Exists(sourceFile).Should().BeTrue(); // 源文件应该仍然存在
        File.ReadAllText(destinationFile).Should().Be("destination content"); // 目标文件内容不变
    }

    #endregion

    #region 文件删除操作测试

    [Fact]
    public void DeleteFile_ShouldDeleteFileSuccessfully_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_delete.txt");
        File.WriteAllText(filePath, "content to delete");

        // Act
        var result = _fileOps.DeleteFile(filePath);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeFalse();
    }

    [Fact]
    public void DeleteFile_ShouldReturnTrue_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("non_existing_delete.txt");

        // Act
        var result = _fileOps.DeleteFile(filePath);

        // Assert
        result.Should().BeTrue(); // 文件不存在视为删除成功
    }

    [Fact]
    public void DeleteFile_ShouldDeleteReadOnlyFile_WhenForceIsTrue()
    {
        // Arrange
        var filePath = GetTestFilePath("readonly_delete.txt");
        File.WriteAllText(filePath, "readonly content");
        File.SetAttributes(filePath, FileAttributes.ReadOnly);

        // Act
        var result = _fileOps.DeleteFile(filePath, force: true);

        // Assert
        result.Should().BeTrue();
        File.Exists(filePath).Should().BeFalse();
    }

    #endregion

    #region 文件属性和信息测试

    [Fact]
    public void GetFileSize_ShouldReturnCorrectSize_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_size.txt");
        var content = "Hello World!"; // 12 字节
        File.WriteAllText(filePath, content, Encoding.UTF8);

        // Act
        var size = _fileOps.GetFileSize(filePath);

        // Assert
        size.Should().BeGreaterThan(0);
        // 实际文件大小可能包含 BOM，所以只检查大小合理性
        var expectedSize = Encoding.UTF8.GetBytes(content).Length;
        size.Should().BeGreaterOrEqualTo(expectedSize);
        size.Should().BeLessOrEqualTo(expectedSize + 10); // 允许一些额外字节
    }

    [Fact]
    public void GetFileSize_ShouldReturnMinusOne_WhenFileDoesNotExist()
    {
        // Arrange
        var filePath = GetTestFilePath("non_existing_size.txt");

        // Act
        var size = _fileOps.GetFileSize(filePath);

        // Assert
        size.Should().Be(-1);
    }

    [Fact]
    public void GetCreationTime_ShouldReturnValidTime_WhenFileExists()
    {
        // Arrange
        var filePath = GetTestFilePath("test_creation_time.txt");
        var beforeCreation = DateTime.Now.AddSeconds(-1);
        File.WriteAllText(filePath, "test");
        var afterCreation = DateTime.Now.AddSeconds(1);

        // Act
        var creationTime = _fileOps.GetCreationTime(filePath);

        // Assert
        creationTime.Should().BeAfter(beforeCreation);
        creationTime.Should().BeBefore(afterCreation);
    }

    [Fact]
    public void GetFileExtension_ShouldReturnCorrectExtension()
    {
        // Arrange
        var filePath = "test.txt";

        // Act
        var extension = _fileOps.GetFileExtension(filePath);

        // Assert
        extension.Should().Be(".txt");
    }

    [Fact]
    public void GetFileNameWithoutExtension_ShouldReturnCorrectName()
    {
        // Arrange
        var filePath = "path/to/test.txt";

        // Act
        var name = _fileOps.GetFileNameWithoutExtension(filePath);

        // Assert
        name.Should().Be("test");
    }

    [Fact]
    public void IsReadOnly_ShouldReturnTrue_WhenFileIsReadOnly()
    {
        // Arrange
        var filePath = GetTestFilePath("readonly_test.txt");
        File.WriteAllText(filePath, "readonly content");
        File.SetAttributes(filePath, FileAttributes.ReadOnly);

        // Act
        var isReadOnly = _fileOps.IsReadOnly(filePath);

        // Assert
        isReadOnly.Should().BeTrue();

        // Cleanup - remove readonly attribute for deletion
        File.SetAttributes(filePath, FileAttributes.Normal);
    }

    [Fact]
    public void SetFileAttributes_ShouldSetAttributesSuccessfully()
    {
        // Arrange
        var filePath = GetTestFilePath("attributes_test.txt");
        File.WriteAllText(filePath, "test content");

        // Act
        var result = _fileOps.SetFileAttributes(filePath, FileAttributes.ReadOnly);

        // Assert
        result.Should().BeTrue();
        _fileOps.IsReadOnly(filePath).Should().BeTrue();

        // Cleanup
        File.SetAttributes(filePath, FileAttributes.Normal);
    }

    #endregion
}
