using System;
using System.IO;
using System.Linq;
using Xunit;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests
{
    /// <summary>
    /// YCommonPaths 静态路径类测试
    /// 测试系统路径、用户路径、临时路径、应用路径等功能
    /// </summary>
    public class YCommonPathsTests
    {
        #region 系统路径测试

        [Fact]
        [Trait("Category", "SystemPaths")]
        public void SystemRoot_ShouldReturnValidPath()
        {
            // Act
            var systemRoot = YCommonPaths.SystemRoot;

            // Assert
            Assert.NotNull(systemRoot);
            Assert.NotEmpty(systemRoot);
            Assert.True(Directory.Exists(systemRoot), "系统根目录应该存在");
        }

        [Fact]
        [Trait("Category", "SystemPaths")]
        public void SystemDirectory_ShouldReturnValidPath()
        {
            // Act
            var systemDir = YCommonPaths.SystemDirectory;

            // Assert
            Assert.NotNull(systemDir);
            Assert.NotEmpty(systemDir);
            Assert.True(Directory.Exists(systemDir), "系统目录应该存在");
        }

        [Fact]
        [Trait("Category", "SystemPaths")]
        public void WindowsDirectory_ShouldReturnValidPath()
        {
            // Act
            var windowsDir = YCommonPaths.WindowsDirectory;

            // Assert
            Assert.NotNull(windowsDir);
            // 在非Windows系统上可能为空，这是正常的
            if (!string.IsNullOrEmpty(windowsDir))
            {
                Assert.True(Directory.Exists(windowsDir), "Windows目录应该存在");
            }
        }

        [Fact]
        [Trait("Category", "SystemPaths")]
        public void ProgramFiles_ShouldReturnValidPath()
        {
            // Act
            var programFiles = YCommonPaths.ProgramFiles;

            // Assert
            Assert.NotNull(programFiles);
            // 在非Windows系统上可能为空，这是正常的
            if (!string.IsNullOrEmpty(programFiles))
            {
                Assert.True(Directory.Exists(programFiles), "Program Files目录应该存在");
            }
        }

        #endregion

        #region 用户路径测试

        [Fact]
        [Trait("Category", "UserPaths")]
        public void UserProfile_ShouldReturnValidPath()
        {
            // Act
            var userProfile = YCommonPaths.UserProfile;

            // Assert
            Assert.NotNull(userProfile);
            Assert.NotEmpty(userProfile);
            Assert.True(Directory.Exists(userProfile), "用户主目录应该存在");
        }

        [Fact]
        [Trait("Category", "UserPaths")]
        public void Desktop_ShouldReturnValidPath()
        {
            // Act
            var desktop = YCommonPaths.Desktop;

            // Assert
            Assert.NotNull(desktop);
            Assert.NotEmpty(desktop);
            Assert.True(Directory.Exists(desktop), "桌面目录应该存在");
        }

        [Fact]
        [Trait("Category", "UserPaths")]
        public void Documents_ShouldReturnValidPath()
        {
            // Act
            var documents = YCommonPaths.Documents;

            // Assert
            Assert.NotNull(documents);
            Assert.NotEmpty(documents);
            Assert.True(Directory.Exists(documents), "文档目录应该存在");
        }

        [Fact]
        [Trait("Category", "UserPaths")]
        public void Downloads_ShouldReturnValidPath()
        {
            // Act
            var downloads = YCommonPaths.Downloads;

            // Assert
            Assert.NotNull(downloads);
            Assert.NotEmpty(downloads);
            Assert.Contains("Downloads", downloads);
        }

        #endregion

        #region 临时路径测试

        [Fact]
        [Trait("Category", "TempPaths")]
        public void TempDirectory_ShouldReturnValidPath()
        {
            // Act
            var tempDir = YCommonPaths.TempDirectory;

            // Assert
            Assert.NotNull(tempDir);
            Assert.NotEmpty(tempDir);
            Assert.True(Directory.Exists(tempDir), "临时目录应该存在");
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void GetTempFilePath_ShouldReturnValidPath()
        {
            // Act
            var tempFile = YCommonPaths.GetTempFilePath();

            // Assert
            Assert.NotNull(tempFile);
            Assert.NotEmpty(tempFile);
            Assert.True(Path.IsPathFullyQualified(tempFile), "应该返回完整路径");
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void GetTempFilePath_WithExtension_ShouldReturnCorrectExtension()
        {
            // Arrange
            var extension = ".txt";

            // Act
            var tempFile = YCommonPaths.GetTempFilePath(extension);

            // Assert
            Assert.NotNull(tempFile);
            Assert.NotEmpty(tempFile);
            Assert.Equal(extension, Path.GetExtension(tempFile));
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void GetTempDirectoryPath_ShouldReturnValidPath()
        {
            // Act
            var tempDir = YCommonPaths.GetTempDirectoryPath();

            // Assert
            Assert.NotNull(tempDir);
            Assert.NotEmpty(tempDir);
            Assert.True(Path.IsPathFullyQualified(tempDir), "应该返回完整路径");
            Assert.Contains("temp_", Path.GetFileName(tempDir));
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void GetTempDirectoryPath_WithPrefix_ShouldContainPrefix()
        {
            // Arrange
            var prefix = "test";

            // Act
            var tempDir = YCommonPaths.GetTempDirectoryPath(prefix);

            // Assert
            Assert.NotNull(tempDir);
            Assert.NotEmpty(tempDir);
            Assert.Contains(prefix, Path.GetFileName(tempDir));
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void CreateTempDirectory_ShouldCreateAndReturnPath()
        {
            // Act
            var tempDir = YCommonPaths.CreateTempDirectory();

            try
            {
                // Assert
                Assert.NotNull(tempDir);
                Assert.NotEmpty(tempDir);
                Assert.True(Directory.Exists(tempDir), "临时目录应该被创建");
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(tempDir))
                {
                    Directory.Delete(tempDir, true);
                }
            }
        }

        [Fact]
        [Trait("Category", "TempPaths")]
        public void CreateTempDirectory_WithPrefix_ShouldCreateWithPrefix()
        {
            // Arrange
            var prefix = "unittest";

            // Act
            var tempDir = YCommonPaths.CreateTempDirectory(prefix);

            try
            {
                // Assert
                Assert.NotNull(tempDir);
                Assert.NotEmpty(tempDir);
                Assert.True(Directory.Exists(tempDir), "临时目录应该被创建");
                Assert.Contains(prefix, Path.GetFileName(tempDir));
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(tempDir))
                {
                    Directory.Delete(tempDir, true);
                }
            }
        }

        #endregion

        #region 应用程序路径测试

        [Fact]
        [Trait("Category", "ApplicationPaths")]
        public void ApplicationDirectory_ShouldReturnValidPath()
        {
            // Act
            var appDir = YCommonPaths.ApplicationDirectory;

            // Assert
            Assert.NotNull(appDir);
            Assert.NotEmpty(appDir);
            Assert.True(Directory.Exists(appDir), "应用程序目录应该存在");
        }

        [Fact]
        [Trait("Category", "ApplicationPaths")]
        public void EnsureDirectory_ShouldCreateDirectory()
        {
            // Arrange
            var testDir = Path.Combine(Path.GetTempPath(), "YCommonPathsTest_" + Guid.NewGuid().ToString("N")[..8]);

            try
            {
                // Act
                var result = YCommonPaths.EnsureDirectory(testDir);

                // Assert
                Assert.Equal(testDir, result);
                Assert.True(Directory.Exists(testDir), "目录应该被创建");
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(testDir))
                {
                    Directory.Delete(testDir, true);
                }
            }
        }

        [Fact]
        [Trait("Category", "ApplicationPaths")]
        public void EnsureDirectory_ExistingDirectory_ShouldReturnPath()
        {
            // Arrange
            var existingDir = Path.GetTempPath();

            // Act
            var result = YCommonPaths.EnsureDirectory(existingDir);

            // Assert
            Assert.Equal(existingDir, result);
            Assert.True(Directory.Exists(result), "现有目录应该仍然存在");
        }

        #endregion

        #region 路径信息测试

        [Fact]
        [Trait("Category", "PathInfo")]
        public void GetLogicalDrives_ShouldReturnDrives()
        {
            // Act
            var drives = YCommonPaths.GetLogicalDrives();

            // Assert
            Assert.NotNull(drives);
            Assert.NotEmpty(drives);
            Assert.All(drives, drive => Assert.NotNull(drive));
        }

        [Fact]
        [Trait("Category", "PathInfo")]
        public void GetDriveInfos_ShouldReturnDriveInfos()
        {
            // Act
            var driveInfos = YCommonPaths.GetDriveInfos();

            // Assert
            Assert.NotNull(driveInfos);
            Assert.NotEmpty(driveInfos);
            Assert.All(driveInfos, info => Assert.NotNull(info));
        }

        [Fact]
        [Trait("Category", "PathInfo")]
        public void GetAvailableDrives_ShouldReturnReadyDrives()
        {
            // Act
            var availableDrives = YCommonPaths.GetAvailableDrives();

            // Assert
            Assert.NotNull(availableDrives);
            Assert.All(availableDrives, drive => Assert.True(drive.IsReady, "所有返回的驱动器都应该是可用的"));
        }

        #endregion

        #region 路径常量测试

        [Fact]
        [Trait("Category", "PathConstants")]
        public void PathSeparator_ShouldBeValidCharacter()
        {
            // Act
            var separator = YCommonPaths.PathSeparator;

            // Assert
            Assert.True(separator == '\\' || separator == '/', "路径分隔符应该是 \\ 或 /");
        }

        [Fact]
        [Trait("Category", "PathConstants")]
        public void InvalidFileNameChars_ShouldNotBeEmpty()
        {
            // Act
            var invalidChars = YCommonPaths.InvalidFileNameChars;

            // Assert
            Assert.NotNull(invalidChars);
            Assert.NotEmpty(invalidChars);
        }

        [Fact]
        [Trait("Category", "PathConstants")]
        public void InvalidPathChars_ShouldNotBeEmpty()
        {
            // Act
            var invalidChars = YCommonPaths.InvalidPathChars;

            // Assert
            Assert.NotNull(invalidChars);
            Assert.NotEmpty(invalidChars);
        }

        #endregion

        #region 环境变量路径测试

        [Fact]
        [Trait("Category", "EnvironmentPaths")]
        public void GetEnvironmentPaths_ShouldReturnPaths()
        {
            // Act
            var paths = YCommonPaths.GetEnvironmentPaths();

            // Assert
            Assert.NotNull(paths);
            // PATH环境变量通常不为空
            if (paths.Any())
            {
                Assert.All(paths, path => Assert.True(Directory.Exists(path), $"PATH中的路径应该存在: {path}"));
            }
        }

        [Fact]
        [Trait("Category", "EnvironmentPaths")]
        public void GetEnvironmentPath_WithValidVariable_ShouldReturnValue()
        {
            // Arrange
            var variableName = "PATH";

            // Act
            var value = YCommonPaths.GetEnvironmentPath(variableName);

            // Assert
            Assert.NotNull(value);
            // PATH环境变量通常不为空
            Assert.NotEmpty(value);
        }

        [Fact]
        [Trait("Category", "EnvironmentPaths")]
        public void GetEnvironmentPath_WithInvalidVariable_ShouldReturnEmpty()
        {
            // Arrange
            var variableName = "NONEXISTENT_VARIABLE_" + Guid.NewGuid().ToString("N");

            // Act
            var value = YCommonPaths.GetEnvironmentPath(variableName);

            // Assert
            Assert.NotNull(value);
            Assert.Empty(value);
        }

        #endregion
    }
}
