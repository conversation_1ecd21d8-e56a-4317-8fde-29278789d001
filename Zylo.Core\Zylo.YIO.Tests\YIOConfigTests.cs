using System;
using System.IO;
using Zylo.YIO.Config;

namespace Zylo.YIO.Tests;

/// <summary>
/// YIOConfig 单元测试
/// 测试配置类的默认值、属性设置等功能
/// </summary>
public class YIOConfigTests
{
    #region 基础配置测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectDefaultValues()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.DefaultWorkingDirectory.Should().Be("./");
        config.TempDirectory.Should().Be(Path.GetTempPath());
        config.BackupDirectory.Should().Be("./Backups");
        config.LogDirectory.Should().Be("./Logs");
    }

    [Fact]
    public void YIOConfig_ShouldAllowPropertyModification()
    {
        // Arrange
        var config = new YIOConfig();
        var newWorkingDir = "/custom/path";

        // Act
        config.DefaultWorkingDirectory = newWorkingDir;

        // Assert
        config.DefaultWorkingDirectory.Should().Be(newWorkingDir);
    }

    #endregion

    #region 文件大小控制测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectFileSizeDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.MaxSingleFileSize.Should().Be(100 * 1024 * 1024); // 100MB
        config.MaxDirectorySize.Should().Be(1024 * 1024 * 1024); // 1GB
        config.LargeFileThreshold.Should().Be(10 * 1024 * 1024); // 10MB
        config.EnableSizeWarnings.Should().BeTrue();
    }

    [Fact]
    public void YIOConfig_ShouldAllowFileSizeCustomization()
    {
        // Arrange
        var config = new YIOConfig();
        var customSize = 50 * 1024 * 1024; // 50MB

        // Act
        config.MaxSingleFileSize = customSize;

        // Assert
        config.MaxSingleFileSize.Should().Be(customSize);
    }

    #endregion

    #region 保存天数管理测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectRetentionDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.DefaultRetentionDays.Should().Be(30);
        config.BackupRetentionDays.Should().Be(90);
        config.TempFileRetentionHours.Should().Be(24);
        config.LogRetentionDays.Should().Be(7);
    }

    [Fact]
    public void YIOConfig_ShouldAllowRetentionCustomization()
    {
        // Arrange
        var config = new YIOConfig();

        // Act
        config.DefaultRetentionDays = 60;
        config.BackupRetentionDays = 180;

        // Assert
        config.DefaultRetentionDays.Should().Be(60);
        config.BackupRetentionDays.Should().Be(180);
    }

    #endregion

    #region 清理策略测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectCleanupDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.AutoCleanup.Should().BeTrue();
        config.CleanupFilePatterns.Should().Contain("*.tmp");
        config.CleanupFilePatterns.Should().Contain("*.log");
        config.CleanupFilePatterns.Should().Contain("*.bak");
        config.ConfirmBeforeCleanup.Should().BeTrue();
        config.CleanupIntervalHours.Should().Be(24);
    }

    [Fact]
    public void YIOConfig_ShouldAllowCleanupCustomization()
    {
        // Arrange
        var config = new YIOConfig();
        var customPatterns = new[] { "*.temp", "*.cache" };

        // Act
        config.CleanupFilePatterns = customPatterns;
        config.AutoCleanup = false;

        // Assert
        config.CleanupFilePatterns.Should().Equal(customPatterns);
        config.AutoCleanup.Should().BeFalse();
    }

    #endregion

    #region 异步和性能配置测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectPerformanceDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.MaxConcurrentOperations.Should().Be(4);
        config.AsyncBufferSize.Should().Be(64 * 1024); // 64KB
        config.EnableProgressReporting.Should().BeTrue();
        config.ProgressUpdateIntervalMs.Should().Be(100);
    }

    [Fact]
    public void YIOConfig_ShouldAllowPerformanceCustomization()
    {
        // Arrange
        var config = new YIOConfig();

        // Act
        config.MaxConcurrentOperations = 8;
        config.AsyncBufferSize = 128 * 1024; // 128KB

        // Assert
        config.MaxConcurrentOperations.Should().Be(8);
        config.AsyncBufferSize.Should().Be(128 * 1024);
    }

    #endregion

    #region 安全配置测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectSecurityDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.EnableSecureDelete.Should().BeFalse();
        config.SecureDeletePasses.Should().Be(3);
        config.EnableAccessLogging.Should().BeTrue();
        config.EnablePathValidation.Should().BeTrue();
        config.BlockedExtensions.Should().Contain(".exe");
        config.BlockedExtensions.Should().Contain(".bat");
        config.BlockedExtensions.Should().Contain(".cmd");
        config.BlockedExtensions.Should().Contain(".scr");
    }

    [Fact]
    public void YIOConfig_ShouldAllowSecurityCustomization()
    {
        // Arrange
        var config = new YIOConfig();
        var customBlockedExtensions = new[] { ".exe", ".dll", ".sys" };

        // Act
        config.EnableSecureDelete = true;
        config.SecureDeletePasses = 7;
        config.BlockedExtensions = customBlockedExtensions;

        // Assert
        config.EnableSecureDelete.Should().BeTrue();
        config.SecureDeletePasses.Should().Be(7);
        config.BlockedExtensions.Should().Equal(customBlockedExtensions);
    }

    #endregion

    #region 格式设置测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectFormatDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.JsonSettings.Should().NotBeNull();
        config.JsonSettings.PrettyPrint.Should().BeTrue();
        config.JsonSettings.IgnoreComments.Should().BeTrue();
        config.JsonSettings.AllowTrailingCommas.Should().BeTrue();
        config.JsonSettings.MaxDepth.Should().Be(64);

        config.XmlSettings.Should().NotBeNull();
        config.XmlSettings.PreserveWhitespace.Should().BeFalse();
        config.XmlSettings.ValidateOnParse.Should().BeTrue();
        config.XmlSettings.DefaultEncoding.Should().Be("UTF-8");
        config.XmlSettings.IndentOutput.Should().BeTrue();
        config.XmlSettings.IndentChars.Should().Be("  ");

        config.IniSettings.Should().NotBeNull();
        config.IniSettings.CommentChar.Should().Be('#');
        config.IniSettings.SectionStart.Should().Be('[');
        config.IniSettings.SectionEnd.Should().Be(']');
        config.IniSettings.KeyValueSeparator.Should().Be('=');
        config.IniSettings.TrimValues.Should().BeTrue();
        config.IniSettings.CaseSensitive.Should().BeFalse();

        config.CsvSettings.Should().NotBeNull();
        config.CsvSettings.Delimiter.Should().Be(',');
        config.CsvSettings.Quote.Should().Be('"');
        config.CsvSettings.Escape.Should().Be('"');
        config.CsvSettings.HasHeader.Should().BeTrue();
        config.CsvSettings.TrimFields.Should().BeTrue();
    }

    [Fact]
    public void YIOConfig_ShouldAllowFormatCustomization()
    {
        // Arrange
        var config = new YIOConfig();

        // Act
        config.JsonSettings.PrettyPrint = false;
        config.JsonSettings.MaxDepth = 32;
        
        config.XmlSettings.IndentChars = "\t";
        config.XmlSettings.DefaultEncoding = "UTF-16";
        
        config.IniSettings.CommentChar = ';';
        config.IniSettings.CaseSensitive = true;
        
        config.CsvSettings.Delimiter = ';';
        config.CsvSettings.HasHeader = false;

        // Assert
        config.JsonSettings.PrettyPrint.Should().BeFalse();
        config.JsonSettings.MaxDepth.Should().Be(32);
        
        config.XmlSettings.IndentChars.Should().Be("\t");
        config.XmlSettings.DefaultEncoding.Should().Be("UTF-16");
        
        config.IniSettings.CommentChar.Should().Be(';');
        config.IniSettings.CaseSensitive.Should().BeTrue();
        
        config.CsvSettings.Delimiter.Should().Be(';');
        config.CsvSettings.HasHeader.Should().BeFalse();
    }

    #endregion

    #region 备份和同步配置测试

    [Fact]
    public void YIOConfig_ShouldHaveCorrectBackupDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.EnableIncrementalBackup.Should().BeTrue();
        config.CompressBackups.Should().BeFalse();
        config.VerifyBackups.Should().BeTrue();
        config.MaxBackupVersions.Should().Be(10);
    }

    [Fact]
    public void YIOConfig_ShouldHaveCorrectSyncDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.EnableRealTimeSync.Should().BeFalse();
        config.SyncIntervalMinutes.Should().Be(60);
        config.HandleSyncConflicts.Should().BeTrue();
        config.ConflictResolutionStrategy.Should().Be("Timestamp");
    }

    [Fact]
    public void YIOConfig_ShouldHaveCorrectMonitoringDefaults()
    {
        // Act
        var config = new YIOConfig();

        // Assert
        config.EnableFileWatching.Should().BeFalse();
        config.WatchFilter.Should().Be("*.*");
        config.IncludeSubdirectories.Should().BeTrue();
        config.WatchBufferSize.Should().Be(8192);
    }

    #endregion
}
