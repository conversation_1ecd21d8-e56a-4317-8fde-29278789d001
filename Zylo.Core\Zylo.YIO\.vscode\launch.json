{"version": "0.2.0", "configurations": [{"name": "Debug Zylo.YIO", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net8.0/Zylo.YIO.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "justMyCode": false, "enableStepFiltering": false, "suppressJITOptimizations": true, "symbolOptions": {"searchPaths": ["${workspaceFolder}/obj/Generated"], "searchMicrosoftSymbolServer": true, "searchNuGetOrgSymbolServer": true}}, {"name": "Debug Zylo.YIO (net6.0)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/bin/Debug/net6.0/Zylo.YIO.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "justMyCode": false, "enableStepFiltering": false, "suppressJITOptimizations": true}, {"name": "Attach to Process", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}", "justMyCode": false}]}