{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Zylo.YIO.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/Zylo.YIO.csproj"], "problemMatcher": "$msCompile"}, {"label": "rebuild", "dependsOrder": "sequence", "dependsOn": ["clean", "build"]}, {"label": "build-verbose", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Zylo.YIO.csproj", "--verbosity", "detailed", "/property:GenerateFullPaths=true"], "problemMatcher": "$msCompile"}, {"label": "view-generated-code", "command": "explorer", "type": "process", "args": ["${workspaceFolder}/obj/Generated"], "windows": {"command": "explorer", "args": ["${workspaceFolder}\\obj\\Generated"]}, "linux": {"command": "xdg-open", "args": ["${workspaceFolder}/obj/Generated"]}, "osx": {"command": "open", "args": ["${workspaceFolder}/obj/Generated"]}}]}