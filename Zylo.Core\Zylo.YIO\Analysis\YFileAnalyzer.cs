using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using Zylo.Toolkit.Attributes;
using Zylo.YIO.Config;

namespace Zylo.YIO.Analysis
{
    /// <summary>
    /// YFileAnalyzer - 高性能文件分析工具类
    ///
    /// 功能特性：
    /// - 🔍 单文件/批量文件分析：支持同步和异步分析模式
    /// - 📊 文件统计分析：扩展名统计、大小分布、时间分析
    /// - 🔄 重复文件检测：基于SHA256哈希的精确重复检测
    /// - 🔗 相似文件检测：基于Levenshtein距离的文件名相似度分析
    /// - 📝 高级内容分析：文本、代码、图像、文档内容深度分析
    /// - ⚡ 多线程处理：并行分析提升性能
    /// - 📈 进度报告：实时进度回调和时间估算
    /// - 📋 详细报告：生成格式化的分析报告
    ///
    /// 支持的文件类型：
    /// - 文本文件：.txt, .json, .xml, .csv, .md, .log 等
    /// - 代码文件：.cs, .js, .py, .java, .cpp, .html, .css 等
    /// - 图像文件：.jpg, .png, .gif, .bmp, .tiff 等
    /// - 文档文件：.pdf, .doc, .docx, .xls, .xlsx, .ppt 等
    /// - 压缩文件：.zip, .rar, .7z 等
    /// - 可执行文件：.exe, .dll 等
    ///
    /// 使用示例：
    /// <code>
    /// var analyzer = new YFileAnalyzer();
    ///
    /// // 分析单个文件
    /// var result = await analyzer.AnalyzeFileAsync("path/to/file.txt");
    ///
    /// // 分析整个目录
    /// var statistics = await analyzer.AnalyzeDirectoryAsync("path/to/directory");
    ///
    /// // 检测重复文件
    /// var duplicates = analyzer.FindDuplicateFiles("path/to/directory");
    ///
    /// // 生成分析报告
    /// var report = analyzer.GenerateAnalysisReport(statistics);
    /// </code>
    /// </summary>
[YStaticExtension]
 [YServiceScoped] 
    public partial class YFileAnalyzer
    {
        #region 私有字段和常量

        /// <summary>
        /// YIO配置实例，用于控制分析行为和参数
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// 支持的文本文件扩展名集合
        /// 这些文件将进行文本内容分析，包括编码检测、语言识别等
        /// </summary>
        private static readonly HashSet<string> TextExtensions = new()
        {
            ".txt", ".json", ".xml", ".csv", ".md", ".log", ".ini", ".cfg", ".sql", ".yaml", ".yml"
        };

        /// <summary>
        /// 支持的图像文件扩展名集合
        /// 这些文件将进行图像属性分析，如格式、尺寸等
        /// </summary>
        private static readonly HashSet<string> ImageExtensions = new()
        {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".ico", ".webp", ".svg"
        };

        /// <summary>
        /// 支持的代码文件扩展名集合
        /// 这些文件将进行代码分析，包括行数统计、注释检测等
        /// </summary>
        private static readonly HashSet<string> CodeExtensions = new()
        {
            ".cs", ".js", ".html", ".css", ".py", ".java", ".cpp", ".c", ".h", ".php",
            ".rb", ".go", ".rs", ".ts", ".vue", ".jsx", ".tsx", ".kt", ".swift", ".dart"
        };

        /// <summary>
        /// 支持的文档文件扩展名集合
        /// 这些文件将进行文档属性分析，如页数、作者等
        /// </summary>
        private static readonly HashSet<string> DocumentExtensions = new()
        {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".rtf", ".odt", ".ods", ".odp"
        };

        #endregion

        #region 构造函数

        /// <summary>
        /// 使用指定配置初始化YFileAnalyzer实例
        /// </summary>
        /// <param name="config">YIO配置实例，包含分析参数和行为设置。如果为null，将使用默认配置</param>
        /// <remarks>
        /// 配置参数影响分析行为，包括：
        /// - 并发处理线程数
        /// - 文件大小限制
        /// - 超时设置
        /// - 缓存策略等
        /// </remarks>
        public YFileAnalyzer(YIOConfig config)
        {
            _config = config ?? new YIOConfig(); // 确保配置不为null，提供默认配置作为后备
        }

        /// <summary>
        /// 使用默认配置初始化YFileAnalyzer实例
        /// </summary>
        /// <remarks>
        /// 此构造函数适用于：
        /// - 静态方法调用场景
        /// - 快速原型开发
        /// - 不需要特殊配置的简单使用场景
        ///
        /// 默认配置提供合理的性能和兼容性平衡
        /// </remarks>
        public YFileAnalyzer() : this(new YIOConfig())
        {
        }

        #endregion

        #region 数据模型

        /// <summary>
        /// 文件分析结果数据模型
        ///
        /// 包含文件的完整分析信息，从基础属性到高级内容分析结果。
        /// 该类设计为不可变数据传输对象，确保分析结果的一致性和线程安全性。
        /// </summary>
        /// <remarks>
        /// 分析结果分为以下几个层次：
        /// 1. 基础文件系统信息：路径、大小、时间戳、属性等
        /// 2. 文件类型识别：MIME类型、文件类别标志
        /// 3. 内容分析：根据文件类型进行的深度分析
        /// 4. 分析元数据：分析时间、状态、错误信息
        /// </remarks>
        public class FileAnalysisResult
        {
            #region 基础文件系统信息

            /// <summary>
            /// 文件的完整路径
            /// </summary>
            /// <value>文件在文件系统中的绝对路径或相对路径</value>
            public string FilePath { get; set; } = "";

            /// <summary>
            /// 文件大小（字节）
            /// </summary>
            /// <value>文件占用的磁盘空间大小，以字节为单位</value>
            public long FileSize { get; set; }

            /// <summary>
            /// 文件创建时间
            /// </summary>
            /// <value>文件在文件系统中的创建时间戳</value>
            public DateTime CreationTime { get; set; }

            /// <summary>
            /// 文件最后修改时间
            /// </summary>
            /// <value>文件内容最后一次被修改的时间戳</value>
            public DateTime LastWriteTime { get; set; }

            /// <summary>
            /// 文件最后访问时间
            /// </summary>
            /// <value>文件最后一次被读取或执行的时间戳</value>
            public DateTime LastAccessTime { get; set; }

            /// <summary>
            /// 文件扩展名（小写）
            /// </summary>
            /// <value>文件的扩展名，已转换为小写，包含前导点（如：".txt"）</value>
            public string Extension { get; set; } = "";

            /// <summary>
            /// 文件内容的SHA256哈希值
            /// </summary>
            /// <value>用于文件完整性验证和重复检测的十六进制哈希字符串</value>
            public string Hash { get; set; } = "";

            /// <summary>
            /// 文件的MIME类型
            /// </summary>
            /// <value>标准MIME类型字符串，用于标识文件内容类型（如："text/plain"）</value>
            public string MimeType { get; set; } = "";

            /// <summary>
            /// 文件是否为只读
            /// </summary>
            /// <value>true表示文件具有只读属性，不能被修改</value>
            public bool IsReadOnly { get; set; }

            /// <summary>
            /// 文件是否为隐藏文件
            /// </summary>
            /// <value>true表示文件具有隐藏属性，在默认情况下不显示</value>
            public bool IsHidden { get; set; }

            /// <summary>
            /// 文件是否为系统文件
            /// </summary>
            /// <value>true表示文件是操作系统的重要组成部分</value>
            public bool IsSystemFile { get; set; }

            /// <summary>
            /// 文件的完整属性标志
            /// </summary>
            /// <value>包含所有文件属性的枚举标志组合</value>
            public FileAttributes Attributes { get; set; }

            #endregion

            #region 文件类型识别和分类

            /// <summary>
            /// 文件类型的中文描述
            /// </summary>
            /// <value>用户友好的文件类型描述（如："C#源代码"、"PNG图像"）</value>
            public string FileType { get; set; } = "";

            /// <summary>
            /// 文件是否为文本文件
            /// </summary>
            /// <value>true表示文件包含可读文本内容</value>
            public bool IsTextFile { get; set; }

            /// <summary>
            /// 文件是否为二进制文件
            /// </summary>
            /// <value>true表示文件包含二进制数据，不适合文本编辑器打开</value>
            public bool IsBinaryFile { get; set; }

            /// <summary>
            /// 文件是否为图像文件
            /// </summary>
            /// <value>true表示文件是图像格式，可以显示为图片</value>
            public bool IsImageFile { get; set; }

            /// <summary>
            /// 文件是否为代码文件
            /// </summary>
            /// <value>true表示文件包含程序源代码</value>
            public bool IsCodeFile { get; set; }

            /// <summary>
            /// 文件是否为文档文件
            /// </summary>
            /// <value>true表示文件是办公文档或PDF等格式</value>
            public bool IsDocumentFile { get; set; }

            #endregion

            #region 文本内容分析结果

            /// <summary>
            /// 文件编码格式
            /// </summary>
            /// <value>检测到的文件编码（如："UTF-8"、"GBK"、"ASCII"）</value>
            public string Encoding { get; set; } = "";

            /// <summary>
            /// 文件行数
            /// </summary>
            /// <value>文件中的总行数，包括空行</value>
            public int LineCount { get; set; }

            /// <summary>
            /// 字符总数
            /// </summary>
            /// <value>文件中的字符总数，包括空格和特殊字符</value>
            public int CharacterCount { get; set; }

            /// <summary>
            /// 单词总数
            /// </summary>
            /// <value>文件中的单词总数，以空格、制表符、换行符分隔</value>
            public int WordCount { get; set; }

            /// <summary>
            /// 检测到的文本语言
            /// </summary>
            /// <value>语言代码（如："zh"表示中文，"en"表示英文）</value>
            public string DetectedLanguage { get; set; } = "";

            #endregion

            #region 图像文件专用分析结果

            /// <summary>
            /// 图像宽度（像素）
            /// </summary>
            /// <value>图像的宽度，以像素为单位。非图像文件为0。⚠️ TODO: 需要实现图像尺寸检测</value>
            public int ImageWidth { get; set; }

            /// <summary>
            /// 图像高度（像素）
            /// </summary>
            /// <value>图像的高度，以像素为单位。非图像文件为0。⚠️ TODO: 需要实现图像尺寸检测</value>
            public int ImageHeight { get; set; }

            /// <summary>
            /// 图像格式
            /// </summary>
            /// <value>图像的格式类型（如："PNG"、"JPEG"、"GIF"）</value>
            public string ImageFormat { get; set; } = "";

            #endregion

            #region 代码文件专用分析结果

            /// <summary>
            /// 代码行数
            /// </summary>
            /// <value>不包括注释和空行的纯代码行数</value>
            public int CodeLines { get; set; }

            /// <summary>
            /// 注释行数
            /// </summary>
            /// <value>包含注释的行数，包括单行和多行注释</value>
            public int CommentLines { get; set; }

            /// <summary>
            /// 空行数
            /// </summary>
            /// <value>完全空白或只包含空格/制表符的行数</value>
            public int BlankLines { get; set; }

            /// <summary>
            /// 编程语言
            /// </summary>
            /// <value>检测到的编程语言名称（如："C#"、"JavaScript"、"Python"）</value>
            public string ProgrammingLanguage { get; set; } = "";

            #endregion

            #region 文档文件专用分析结果

            /// <summary>
            /// 文档页数
            /// </summary>
            /// <value>文档的总页数。⚠️ TODO: 需要专门的文档解析库支持</value>
            public int PageCount { get; set; }

            /// <summary>
            /// 文档作者
            /// </summary>
            /// <value>从文档元数据中提取的作者信息。⚠️ TODO: 需要实现元数据提取</value>
            public string Author { get; set; } = "";

            /// <summary>
            /// 文档标题
            /// </summary>
            /// <value>从文档元数据中提取的标题信息。⚠️ TODO: 需要实现元数据提取</value>
            public string Title { get; set; } = "";

            /// <summary>
            /// 文档主题
            /// </summary>
            /// <value>从文档元数据中提取的主题或摘要信息。⚠️ TODO: 需要实现元数据提取</value>
            public string Subject { get; set; } = "";

            #endregion

            #region 分析元数据

            /// <summary>
            /// 分析执行时间
            /// </summary>
            /// <value>执行文件分析的时间戳</value>
            public DateTime AnalysisTime { get; set; } = DateTime.Now;

            /// <summary>
            /// 分析是否成功
            /// </summary>
            /// <value>true表示分析成功完成，false表示分析过程中出现错误</value>
            public bool AnalysisSuccess { get; set; } = true;

            /// <summary>
            /// 分析错误信息
            /// </summary>
            /// <value>当分析失败时，包含具体的错误描述信息</value>
            public string AnalysisError { get; set; } = "";

            #endregion
        }

        /// <summary>
        /// 重复文件信息数据模型
        ///
        /// 用于表示一组具有相同内容的文件，通过SHA256哈希值进行精确匹配。
        /// 提供重复文件的详细信息和空间浪费计算。
        /// </summary>
        /// <remarks>
        /// 重复文件检测基于文件内容的SHA256哈希值，确保100%准确性。
        /// 即使文件名不同，只要内容相同就会被识别为重复文件。
        /// </remarks>
        public class DuplicateFileInfo
        {
            /// <summary>
            /// 文件内容的SHA256哈希值
            /// </summary>
            /// <value>用于唯一标识文件内容的十六进制哈希字符串</value>
            public string Hash { get; set; } = "";

            /// <summary>
            /// 文件大小（字节）
            /// </summary>
            /// <value>重复文件组中每个文件的大小，所有文件大小相同</value>
            public long FileSize { get; set; }

            /// <summary>
            /// 重复文件路径列表
            /// </summary>
            /// <value>包含所有具有相同内容的文件路径</value>
            public List<string> FilePaths { get; set; } = new();

            /// <summary>
            /// 计算浪费的磁盘空间
            /// </summary>
            /// <value>除了保留一个文件外，其他重复文件占用的总空间</value>
            /// <remarks>
            /// 计算公式：(重复文件数量 - 1) × 单个文件大小
            /// 这表示如果删除重复文件，可以节省的磁盘空间
            /// </remarks>
            public long TotalWastedSpace => (FilePaths.Count - 1) * FileSize;
        }

        /// <summary>
        /// 文件统计信息数据模型
        ///
        /// 包含目录分析的完整统计结果，提供文件分布、大小统计、
        /// 重复文件信息等综合数据，用于生成详细的分析报告。
        /// </summary>
        /// <remarks>
        /// 统计信息涵盖：
        /// - 基础统计：文件数量、总大小
        /// - 类型分布：按扩展名分组的统计
        /// - 排行榜：最大、最新、最旧文件
        /// - 重复分析：重复文件检测结果
        /// </remarks>
        public class FileStatistics
        {
            /// <summary>
            /// 分析的文件总数
            /// </summary>
            /// <value>成功分析的文件数量</value>
            public int TotalFiles { get; set; }

            /// <summary>
            /// 文件总大小（字节）
            /// </summary>
            /// <value>所有分析文件的大小总和</value>
            public long TotalSize { get; set; }

            /// <summary>
            /// 按扩展名统计的文件数量
            /// </summary>
            /// <value>键为文件扩展名，值为该类型文件的数量</value>
            public Dictionary<string, int> ExtensionCounts { get; set; } = new();

            /// <summary>
            /// 按扩展名统计的文件大小
            /// </summary>
            /// <value>键为文件扩展名，值为该类型文件的总大小</value>
            public Dictionary<string, long> ExtensionSizes { get; set; } = new();

            /// <summary>
            /// 最大文件列表（按大小排序）
            /// </summary>
            /// <value>包含前N个最大文件的分析结果</value>
            public List<FileAnalysisResult> LargestFiles { get; set; } = new();

            /// <summary>
            /// 最旧文件列表（按创建时间排序）
            /// </summary>
            /// <value>包含前N个最旧文件的分析结果</value>
            public List<FileAnalysisResult> OldestFiles { get; set; } = new();

            /// <summary>
            /// 最新文件列表（按创建时间排序）
            /// </summary>
            /// <value>包含前N个最新文件的分析结果</value>
            public List<FileAnalysisResult> NewestFiles { get; set; } = new();

            /// <summary>
            /// 重复文件信息列表
            /// </summary>
            /// <value>包含所有检测到的重复文件组</value>
            public List<DuplicateFileInfo> DuplicateFiles { get; set; } = new();

            /// <summary>
            /// 重复文件浪费的总空间
            /// </summary>
            /// <value>所有重复文件浪费空间的总和</value>
            public long TotalDuplicateSize { get; set; }

            /// <summary>
            /// 统计分析的执行时间
            /// </summary>
            /// <value>执行统计分析的时间戳</value>
            public DateTime AnalysisTime { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// 分析进度信息数据模型
        ///
        /// 用于实时报告文件分析的进度状态，包括已处理文件数、
        /// 当前处理文件、进度百分比和时间估算等信息。
        /// </summary>
        /// <remarks>
        /// 进度信息支持：
        /// - 实时进度更新
        /// - 剩余时间估算
        /// - 当前处理状态
        /// - 性能监控数据
        /// </remarks>
        public class AnalysisProgress
        {
            /// <summary>
            /// 已处理的文件数量
            /// </summary>
            /// <value>当前已完成分析的文件数</value>
            public int ProcessedFiles { get; set; }

            /// <summary>
            /// 需要处理的文件总数
            /// </summary>
            /// <value>本次分析任务的文件总数</value>
            public int TotalFiles { get; set; }

            /// <summary>
            /// 当前正在处理的文件名
            /// </summary>
            /// <value>正在分析的文件的文件名（不包含路径）</value>
            public string CurrentFile { get; set; } = "";

            /// <summary>
            /// 进度百分比
            /// </summary>
            /// <value>分析进度的百分比（0-100）</value>
            /// <remarks>
            /// 计算公式：(已处理文件数 / 总文件数) × 100
            /// 当总文件数为0时返回0，避免除零错误
            /// </remarks>
            public double ProgressPercentage => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;

            /// <summary>
            /// 已消耗的时间
            /// </summary>
            /// <value>从开始分析到当前时刻的时间间隔</value>
            public TimeSpan ElapsedTime { get; set; }

            /// <summary>
            /// 预估剩余时间
            /// </summary>
            /// <value>基于当前处理速度估算的剩余完成时间</value>
            /// <remarks>
            /// 估算基于平均处理时间：
            /// 剩余时间 = (总文件数 - 已处理文件数) × 平均每文件处理时间
            /// </remarks>
            public TimeSpan EstimatedTimeRemaining { get; set; }
        }

        #endregion

        #region 核心文件分析方法

        /// <summary>
        /// 分析单个文件的完整信息
        ///
        /// 执行文件的全面分析，包括基础属性、内容分析、类型识别等。
        /// 该方法是文件分析的核心入口点，提供同步分析功能。
        /// </summary>
        /// <param name="filePath">要分析的文件路径，支持绝对路径和相对路径</param>
        /// <returns>包含完整分析结果的FileAnalysisResult对象</returns>
        /// <exception cref="ArgumentNullException">当filePath为null时抛出</exception>
        /// <exception cref="ArgumentException">当filePath为空字符串时抛出</exception>
        /// <remarks>
        /// 分析过程包括以下步骤：
        /// 1. 文件存在性验证
        /// 2. 基础文件系统信息提取
        /// 3. 文件哈希计算（SHA256）
        /// 4. MIME类型和文件类型识别
        /// 5. 文件分类标志设置
        /// 6. 高级内容分析（根据文件类型）
        ///
        /// 性能考虑：
        /// - 大文件的哈希计算可能耗时较长
        /// - 文本文件的内容分析需要完整读取文件
        /// - 建议对大批量文件使用异步方法
        /// </remarks>
        /// <example>
        /// <code>
        /// var analyzer = new YFileAnalyzer();
        /// var result = analyzer.AnalyzeFile(@"C:\temp\document.txt");
        ///
        /// if (result.AnalysisSuccess)
        /// {
        ///     Console.WriteLine($"文件大小: {result.FileSize} 字节");
        ///     Console.WriteLine($"文件类型: {result.FileType}");
        ///     Console.WriteLine($"是否为文本文件: {result.IsTextFile}");
        /// }
        /// else
        /// {
        ///     Console.WriteLine($"分析失败: {result.AnalysisError}");
        /// }
        /// </code>
        /// </example>
        public FileAnalysisResult AnalyzeFile(string filePath)
        {
            // 参数验证
            if (filePath == null)
                throw new ArgumentNullException(nameof(filePath), "文件路径不能为null");

            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空", nameof(filePath));

            var result = new FileAnalysisResult { FilePath = filePath };

            try
            {
                // 验证文件存在性
                if (!File.Exists(filePath))
                {
                    result.AnalysisSuccess = false;
                    result.AnalysisError = $"文件不存在: {filePath}";
                    return result;
                }

                var fileInfo = new FileInfo(filePath);

                // 第一步：提取基础文件系统信息
                ExtractBasicFileInfo(fileInfo, result);

                // 第二步：计算文件内容哈希值（用于重复检测和完整性验证）
                result.Hash = CalculateFileHash(filePath);

                // 第三步：识别文件类型和MIME类型
                result.MimeType = GetMimeType(filePath);
                result.FileType = GetFileType(filePath);

                // 第四步：设置文件分类标志
                SetFileTypeFlags(result);

                // 第五步：执行高级内容分析
                PerformAdvancedAnalysis(result);

                result.AnalysisSuccess = true;
                return result;
            }
            catch (UnauthorizedAccessException ex)
            {
                // 处理权限不足的情况
                result.AnalysisSuccess = false;
                result.AnalysisError = $"访问被拒绝: {ex.Message}";
                return result;
            }
            catch (IOException ex)
            {
                // 处理IO异常（文件被锁定、磁盘错误等）
                result.AnalysisSuccess = false;
                result.AnalysisError = $"IO错误: {ex.Message}";
                return result;
            }
            catch (Exception ex)
            {
                // 处理其他未预期的异常
                Console.WriteLine($"分析文件时发生未预期错误 {filePath}: {ex.Message}");
                result.AnalysisSuccess = false;
                result.AnalysisError = $"分析失败: {ex.Message}";
                return result;
            }
        }

        
        
        
        public static FileAnalysisResult Analyze(string filePath)
        {
            // 使用Task.Run将同步方法包装为异步执行
            // 这样可以避免阻塞调用线程，特别是在UI应用程序中
            return   new FileAnalysisResult();
        }
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        /// <summary>
        /// 提取文件的基础系统信息
        ///
        /// 从FileInfo对象中提取文件的基本属性，包括大小、时间戳、
        /// 扩展名和各种文件属性标志。
        /// </summary>
        /// <param name="fileInfo">文件信息对象</param>
        /// <param name="result">要填充的分析结果对象</param>
        /// <remarks>
        /// 提取的信息包括：
        /// - 文件大小（字节）
        /// - 创建时间、修改时间、访问时间
        /// - 文件扩展名（转换为小写）
        /// - 只读、隐藏、系统文件等属性标志
        /// </remarks>
        private void ExtractBasicFileInfo(FileInfo fileInfo, FileAnalysisResult result)
        {
            // 基础大小和时间信息
            result.FileSize = fileInfo.Length;
            result.CreationTime = fileInfo.CreationTime;
            result.LastWriteTime = fileInfo.LastWriteTime;
            result.LastAccessTime = fileInfo.LastAccessTime;

            // 扩展名标准化（转换为小写，便于后续比较）
            result.Extension = fileInfo.Extension.ToLowerInvariant();

            // 基础属性
            result.IsReadOnly = fileInfo.IsReadOnly;
            result.Attributes = fileInfo.Attributes;

            // 解析复合属性标志
            result.IsHidden = (result.Attributes & FileAttributes.Hidden) == FileAttributes.Hidden;
            result.IsSystemFile = (result.Attributes & FileAttributes.System) == FileAttributes.System;
        }

        /// <summary>
        /// 异步分析单个文件的完整信息
        ///
        /// 提供文件分析的异步版本，适用于UI应用程序或需要并发处理的场景。
        /// 在后台线程中执行分析，避免阻塞调用线程。
        /// </summary>
        /// <param name="filePath">要分析的文件路径，支持绝对路径和相对路径</param>
        /// <returns>包含完整分析结果的Task&lt;FileAnalysisResult&gt;</returns>
        /// <exception cref="ArgumentNullException">当filePath为null时抛出</exception>
        /// <exception cref="ArgumentException">当filePath为空字符串时抛出</exception>
        /// <remarks>
        /// 异步分析的优势：
        /// - 不阻塞UI线程，保持界面响应性
        /// - 支持取消操作（通过CancellationToken）
        /// - 可以并发处理多个文件
        /// - 适合大文件或网络文件的分析
        ///
        /// 性能提示：
        /// - 对于小文件，同步方法可能更高效
        /// - 大批量文件建议使用批量异步方法
        /// - 可以配合Task.WhenAll实现并发分析
        /// </remarks>
        /// <example>
        /// <code>
        /// var analyzer = new YFileAnalyzer();
        ///
        /// // 单文件异步分析
        /// var result = await analyzer.AnalyzeFileAsync(@"C:\temp\document.txt");
        ///
        /// // 多文件并发分析
        /// var files = new[] { "file1.txt", "file2.txt", "file3.txt" };
        /// var tasks = files.Select(f => analyzer.AnalyzeFileAsync(f));
        /// var results = await Task.WhenAll(tasks);
        /// </code>
        /// </example>
        public async Task<FileAnalysisResult> AnalyzeFileAsync(string filePath)
        {
            // 使用Task.Run将同步方法包装为异步执行
            // 这样可以避免阻塞调用线程，特别是在UI应用程序中
            return await Task.Run(() => AnalyzeFile(filePath)).ConfigureAwait(false);
        }
        
        

        #endregion

        #region 批量文件分析方法

        /// <summary>
        /// 分析指定目录中的所有文件
        ///
        /// 递归扫描目录，分析所有匹配的文件，生成详细的统计信息。
        /// 支持进度报告和灵活的文件过滤条件。
        /// </summary>
        /// <param name="directoryPath">要分析的目录路径</param>
        /// <param name="searchPattern">文件搜索模式，支持通配符（如："*.txt", "*.cs"）</param>
        /// <param name="includeSubdirectories">是否递归包含子目录</param>
        /// <param name="progressCallback">进度报告回调，用于实时更新分析进度</param>
        /// <returns>包含完整统计信息的FileStatistics对象</returns>
        /// <exception cref="ArgumentNullException">当directoryPath为null时抛出</exception>
        /// <exception cref="ArgumentException">当directoryPath为空字符串时抛出</exception>
        /// <exception cref="DirectoryNotFoundException">当指定目录不存在时抛出</exception>
        /// <remarks>
        /// 分析过程包括：
        /// 1. 目录扫描和文件枚举
        /// 2. 并行文件分析（提升性能）
        /// 3. 统计信息汇总
        /// 4. 重复文件检测
        /// 5. 排行榜生成（最大、最新、最旧文件）
        ///
        /// 性能优化：
        /// - 使用并行处理提升分析速度
        /// - 支持进度报告，便于长时间操作的监控
        /// - 内存友好的流式处理
        ///
        /// 搜索模式示例：
        /// - "*.*" : 所有文件
        /// - "*.txt" : 只分析文本文件
        /// - "*.cs" : 只分析C#源代码文件
        /// - "test*.*" : 文件名以"test"开头的所有文件
        /// </remarks>
        /// <example>
        /// <code>
        /// var analyzer = new YFileAnalyzer();
        ///
        /// // 分析整个目录
        /// var stats = analyzer.AnalyzeDirectory(@"C:\MyProject");
        ///
        /// // 只分析C#文件，包含子目录
        /// var csharpStats = analyzer.AnalyzeDirectory(@"C:\MyProject", "*.cs", true);
        ///
        /// // 带进度报告的分析
        /// var progress = new Progress&lt;AnalysisProgress&gt;(p =>
        /// {
        ///     Console.WriteLine($"进度: {p.ProgressPercentage:F1}% - {p.CurrentFile}");
        /// });
        /// var statsWithProgress = analyzer.AnalyzeDirectory(@"C:\MyProject", "*.*", true, progress);
        /// </code>
        /// </example>
        public FileStatistics AnalyzeDirectory(string directoryPath, string searchPattern = "*.*",
            bool includeSubdirectories = true, IProgress<AnalysisProgress>? progressCallback = null)
        {
            // 参数验证
            if (directoryPath == null)
                throw new ArgumentNullException(nameof(directoryPath), "目录路径不能为null");

            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

            try
            {
                // 验证目录存在性
                if (!Directory.Exists(directoryPath))
                {
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");
                }

                // 设置搜索选项：是否包含子目录
                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                // 枚举匹配的文件
                // 使用EnumerateFiles而不是GetFiles，以支持大目录的内存友好处理
                string[] files;
                try
                {
                    files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                }
                catch (UnauthorizedAccessException ex)
                {
                    Console.WriteLine($"访问目录被拒绝 {directoryPath}: {ex.Message}");
                    return new FileStatistics();
                }
                catch (PathTooLongException ex)
                {
                    Console.WriteLine($"路径过长 {directoryPath}: {ex.Message}");
                    return new FileStatistics();
                }

                // 调用核心文件分析方法
                return AnalyzeFiles(files, progressCallback);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析目录时发生错误 {directoryPath}: {ex.Message}");
                return new FileStatistics();
            }
        }

        /// <summary>
        /// 异步分析指定目录中的所有文件
        ///
        /// 提供目录分析的异步版本，适用于大型目录或需要保持UI响应性的场景。
        /// 在后台线程中执行分析，支持进度报告和取消操作。
        /// </summary>
        /// <param name="directoryPath">要分析的目录路径</param>
        /// <param name="searchPattern">文件搜索模式，支持通配符（默认为"*.*"）</param>
        /// <param name="includeSubdirectories">是否递归包含子目录（默认为true）</param>
        /// <param name="progressCallback">进度报告回调，用于实时更新分析进度</param>
        /// <returns>包含完整统计信息的Task&lt;FileStatistics&gt;</returns>
        /// <exception cref="ArgumentNullException">当directoryPath为null时抛出</exception>
        /// <exception cref="ArgumentException">当directoryPath为空字符串时抛出</exception>
        /// <remarks>
        /// 异步目录分析的优势：
        /// - 不阻塞UI线程，保持应用程序响应性
        /// - 支持大型目录的长时间分析
        /// - 可以与其他异步操作并发执行
        /// - 支持实时进度更新
        ///
        /// 适用场景：
        /// - 桌面应用程序的文件管理功能
        /// - Web应用程序的后台文件分析
        /// - 批处理工具的并发处理
        /// - 需要用户交互的长时间操作
        ///
        /// 性能考虑：
        /// - 大目录分析可能消耗大量内存和CPU
        /// - 建议在分析前评估目录大小
        /// - 可以通过searchPattern限制分析范围
        /// </remarks>
        /// <example>
        /// <code>
        /// var analyzer = new YFileAnalyzer();
        ///
        /// // 异步分析整个目录
        /// var stats = await analyzer.AnalyzeDirectoryAsync(@"C:\MyProject");
        ///
        /// // 带进度报告的异步分析
        /// var progress = new Progress&lt;AnalysisProgress&gt;(p =>
        /// {
        ///     Console.WriteLine($"进度: {p.ProgressPercentage:F1}%");
        ///     Console.WriteLine($"当前文件: {p.CurrentFile}");
        ///     Console.WriteLine($"剩余时间: {p.EstimatedTimeRemaining}");
        /// });
        ///
        /// var statsWithProgress = await analyzer.AnalyzeDirectoryAsync(
        ///     @"C:\MyProject", "*.*", true, progress);
        ///
        /// // 并发分析多个目录
        /// var directories = new[] { @"C:\Dir1", @"C:\Dir2", @"C:\Dir3" };
        /// var tasks = directories.Select(dir => analyzer.AnalyzeDirectoryAsync(dir));
        /// var allStats = await Task.WhenAll(tasks);
        /// </code>
        /// </example>
        public async Task<FileStatistics> AnalyzeDirectoryAsync(string directoryPath, string searchPattern = "*.*",
            bool includeSubdirectories = true, IProgress<AnalysisProgress>? progressCallback = null)
        {
            // 使用Task.Run将同步方法包装为异步执行
            // ConfigureAwait(false)避免死锁，提升性能
            return await Task.Run(() =>
                AnalyzeDirectory(directoryPath, searchPattern, includeSubdirectories, progressCallback))
                .ConfigureAwait(false);
        }

        #endregion

        #region 重复文件检测方法

        /// <summary>
        /// 在指定目录中检测重复文件
        ///
        /// 基于SHA256哈希值进行精确的重复文件检测，确保100%准确性。
        /// 支持递归目录扫描和实时进度报告。
        /// </summary>
        /// <param name="directoryPath">要扫描的目录路径</param>
        /// <param name="includeSubdirectories">是否递归包含子目录（默认为true）</param>
        /// <param name="progressCallback">进度报告回调，用于监控检测进度</param>
        /// <returns>重复文件组列表，按浪费空间大小降序排列</returns>
        /// <exception cref="ArgumentNullException">当directoryPath为null时抛出</exception>
        /// <exception cref="ArgumentException">当directoryPath为空字符串时抛出</exception>
        /// <remarks>
        /// 重复检测算法：
        /// 1. 扫描目录获取所有文件
        /// 2. 并行计算每个文件的SHA256哈希值
        /// 3. 按哈希值分组，找出重复文件
        /// 4. 计算每组的空间浪费量
        /// 5. 按浪费空间排序返回结果
        ///
        /// 性能特点：
        /// - 使用并行处理提升计算速度
        /// - 内存使用优化，适合大量文件
        /// - 支持进度报告，便于长时间操作监控
        /// - 异常处理确保单个文件错误不影响整体检测
        ///
        /// 准确性保证：
        /// - SHA256哈希冲突概率极低（2^-256）
        /// - 即使文件名不同，内容相同也能检测
        /// - 忽略文件时间戳和属性，只关注内容
        /// </remarks>
        /// <example>
        /// <code>
        /// var analyzer = new YFileAnalyzer();
        ///
        /// // 检测目录中的重复文件
        /// var duplicates = analyzer.FindDuplicateFiles(@"C:\MyDocuments");
        ///
        /// // 显示检测结果
        /// foreach (var duplicate in duplicates)
        /// {
        ///     Console.WriteLine($"重复文件组 (浪费 {duplicate.TotalWastedSpace} 字节):");
        ///     foreach (var file in duplicate.FilePaths)
        ///     {
        ///         Console.WriteLine($"  - {file}");
        ///     }
        /// }
        ///
        /// // 带进度报告的检测
        /// var progress = new Progress&lt;AnalysisProgress&gt;(p =>
        /// {
        ///     Console.WriteLine($"检测进度: {p.ProgressPercentage:F1}%");
        /// });
        /// var duplicatesWithProgress = analyzer.FindDuplicateFiles(
        ///     @"C:\MyDocuments", true, progress);
        /// </code>
        /// </example>
        public List<DuplicateFileInfo> FindDuplicateFiles(string directoryPath, bool includeSubdirectories = true,
            IProgress<AnalysisProgress>? progressCallback = null)
        {
            // 参数验证
            if (directoryPath == null)
                throw new ArgumentNullException(nameof(directoryPath), "目录路径不能为null");

            if (string.IsNullOrWhiteSpace(directoryPath))
                throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

            try
            {
                // 验证目录存在性
                if (!Directory.Exists(directoryPath))
                {
                    Console.WriteLine($"目录不存在: {directoryPath}");
                    return new List<DuplicateFileInfo>();
                }

                // 设置搜索选项
                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                // 获取所有文件
                string[] files;
                try
                {
                    files = Directory.GetFiles(directoryPath, "*.*", searchOption);
                }
                catch (UnauthorizedAccessException ex)
                {
                    Console.WriteLine($"访问目录被拒绝 {directoryPath}: {ex.Message}");
                    return new List<DuplicateFileInfo>();
                }
                catch (PathTooLongException ex)
                {
                    Console.WriteLine($"路径过长 {directoryPath}: {ex.Message}");
                    return new List<DuplicateFileInfo>();
                }

                // 调用核心重复检测方法
                return FindDuplicateFiles(files, progressCallback);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检测重复文件时发生错误: {ex.Message}");
                return new List<DuplicateFileInfo>();
            }
        }

        /// <summary>
        /// 检测文件列表中的重复文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>重复文件列表</returns>
        public List<DuplicateFileInfo> FindDuplicateFiles(string[] filePaths, IProgress<AnalysisProgress>? progressCallback = null)
        {
            var duplicates = new List<DuplicateFileInfo>();
            var hashGroups = new ConcurrentDictionary<string, ConcurrentBag<string>>();
            var startTime = DateTime.Now;

            try
            {
                var progress = new AnalysisProgress { TotalFiles = filePaths.Length };

                // 并行计算文件哈希
                Parallel.ForEach(filePaths, filePath =>
                {
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            var hash = CalculateFileHash(filePath);
                            if (!string.IsNullOrEmpty(hash))
                            {
                                hashGroups.AddOrUpdate(hash,
                                    new ConcurrentBag<string> { filePath },
                                    (key, existing) => { existing.Add(filePath); return existing; });
                            }
                        }

                        // 更新进度
                        lock (progress)
                        {
                            progress.ProcessedFiles++;
                            progress.CurrentFile = Path.GetFileName(filePath);
                            progress.ElapsedTime = DateTime.Now - startTime;

                            if (progress.ProcessedFiles > 0)
                            {
                                var avgTimePerFile = progress.ElapsedTime.TotalMilliseconds / progress.ProcessedFiles;
                                var remainingFiles = progress.TotalFiles - progress.ProcessedFiles;
                                progress.EstimatedTimeRemaining = TimeSpan.FromMilliseconds(avgTimePerFile * remainingFiles);
                            }

                            progressCallback?.Report(progress);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {filePath}: {ex.Message}");
                    }
                });

                // 找出重复的文件组
                foreach (var group in hashGroups.Where(g => g.Value.Count > 1))
                {
                    var firstFile = group.Value.First();
                    if (File.Exists(firstFile))
                    {
                        var fileInfo = new FileInfo(firstFile);
                        duplicates.Add(new DuplicateFileInfo
                        {
                            Hash = group.Key,
                            FileSize = fileInfo.Length,
                            FilePaths = group.Value.ToList()
                        });
                    }
                }

                return duplicates.OrderByDescending(d => d.TotalWastedSpace).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检测重复文件失败: {ex.Message}");
                return duplicates;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 计算文件哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>SHA256哈希值</returns>
        private string CalculateFileHash(string filePath)
        {
            try
            {
                using var sha256 = SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hash = sha256.ComputeHash(stream);
                return Convert.ToHexString(hash);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"计算文件哈希失败 {filePath}: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 获取文件MIME类型
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MIME类型</returns>
        private string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".txt" => "text/plain",
                ".json" => "application/json",
                ".xml" => "application/xml",
                ".csv" => "text/csv",
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" => "image/tiff",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                ".exe" => "application/x-msdownload",
                ".dll" => "application/x-msdownload",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".mp3" => "audio/mpeg",
                ".wav" => "audio/wav",
                ".mp4" => "video/mp4",
                ".avi" => "video/x-msvideo",
                ".cs" => "text/x-csharp",
                ".js" => "text/javascript",
                ".html" => "text/html",
                ".css" => "text/css",
                ".py" => "text/x-python",
                ".java" => "text/x-java-source",
                ".cpp" => "text/x-c++src",
                ".c" => "text/x-csrc",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 获取文件类型描述
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件类型描述</returns>
        private string GetFileType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".txt" => "纯文本文件",
                ".json" => "JSON数据文件",
                ".xml" => "XML文档",
                ".csv" => "CSV数据文件",
                ".pdf" => "PDF文档",
                ".doc" or ".docx" => "Word文档",
                ".xls" or ".xlsx" => "Excel电子表格",
                ".ppt" or ".pptx" => "PowerPoint演示文稿",
                ".jpg" or ".jpeg" => "JPEG图像",
                ".png" => "PNG图像",
                ".gif" => "GIF动画",
                ".bmp" => "位图图像",
                ".tiff" => "TIFF图像",
                ".zip" => "ZIP压缩包",
                ".rar" => "RAR压缩包",
                ".7z" => "7-Zip压缩包",
                ".exe" => "可执行程序",
                ".dll" => "动态链接库",
                ".mp3" => "MP3音频",
                ".wav" => "WAV音频",
                ".mp4" => "MP4视频",
                ".avi" => "AVI视频",
                ".cs" => "C#源代码",
                ".js" => "JavaScript脚本",
                ".html" => "HTML网页",
                ".css" => "CSS样式表",
                ".py" => "Python脚本",
                ".java" => "Java源代码",
                ".cpp" => "C++源代码",
                ".c" => "C源代码",
                ".h" => "C/C++头文件",
                ".sql" => "SQL脚本",
                ".md" => "Markdown文档",
                ".log" => "日志文件",
                ".ini" => "配置文件",
                ".cfg" => "配置文件",
                _ => "未知文件类型"
            };
        }

        #endregion

        #region 文件统计分析

        /// <summary>
        /// 分析文件列表
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>文件统计信息</returns>
        private FileStatistics AnalyzeFiles(string[] filePaths, IProgress<AnalysisProgress>? progressCallback = null)
        {
            var statistics = new FileStatistics();
            var startTime = DateTime.Now;
            var progress = new AnalysisProgress { TotalFiles = filePaths.Length };
            var fileResults = new ConcurrentBag<FileAnalysisResult>();

            try
            {
                // 并行分析文件
                Parallel.ForEach(filePaths, filePath =>
                {
                    var result = AnalyzeFile(filePath);
                    if (!string.IsNullOrEmpty(result.Hash))
                    {
                        fileResults.Add(result);
                    }

                    // 更新进度
                    lock (progress)
                    {
                        progress.ProcessedFiles++;
                        progress.CurrentFile = Path.GetFileName(filePath);
                        progress.ElapsedTime = DateTime.Now - startTime;

                        if (progress.ProcessedFiles > 0)
                        {
                            var avgTimePerFile = progress.ElapsedTime.TotalMilliseconds / progress.ProcessedFiles;
                            var remainingFiles = progress.TotalFiles - progress.ProcessedFiles;
                            progress.EstimatedTimeRemaining = TimeSpan.FromMilliseconds(avgTimePerFile * remainingFiles);
                        }

                        progressCallback?.Report(progress);
                    }
                });

                var allResults = fileResults.ToList();

                // 基础统计
                statistics.TotalFiles = allResults.Count;
                statistics.TotalSize = allResults.Sum(f => f.FileSize);

                // 扩展名统计
                var extensionGroups = allResults.GroupBy(f => f.Extension);
                foreach (var group in extensionGroups)
                {
                    statistics.ExtensionCounts[group.Key] = group.Count();
                    statistics.ExtensionSizes[group.Key] = group.Sum(f => f.FileSize);
                }

                // 最大文件（前10个）
                statistics.LargestFiles = allResults
                    .OrderByDescending(f => f.FileSize)
                    .Take(10)
                    .ToList();

                // 最旧文件（前10个）
                statistics.OldestFiles = allResults
                    .OrderBy(f => f.CreationTime)
                    .Take(10)
                    .ToList();

                // 最新文件（前10个）
                statistics.NewestFiles = allResults
                    .OrderByDescending(f => f.CreationTime)
                    .Take(10)
                    .ToList();

                // 重复文件检测
                var hashGroups = allResults.GroupBy(f => f.Hash).Where(g => g.Count() > 1);
                foreach (var group in hashGroups)
                {
                    var firstFile = group.First();
                    var duplicate = new DuplicateFileInfo
                    {
                        Hash = group.Key,
                        FileSize = firstFile.FileSize,
                        FilePaths = group.Select(f => f.FilePath).ToList()
                    };
                    statistics.DuplicateFiles.Add(duplicate);
                    statistics.TotalDuplicateSize += duplicate.TotalWastedSpace;
                }

                statistics.DuplicateFiles = statistics.DuplicateFiles
                    .OrderByDescending(d => d.TotalWastedSpace)
                    .ToList();

                return statistics;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析文件失败: {ex.Message}");
                return statistics;
            }
        }

        /// <summary>
        /// 生成分析报告
        /// </summary>
        /// <param name="statistics">文件统计信息</param>
        /// <returns>分析报告文本</returns>
        public string GenerateAnalysisReport(FileStatistics statistics)
        {
            var report = new StringBuilder();

            report.AppendLine("📊 文件分析报告");
            report.AppendLine("=" + new string('=', 50));
            report.AppendLine($"分析时间: {statistics.AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // 基础统计
            report.AppendLine("📈 基础统计");
            report.AppendLine("-" + new string('-', 30));
            report.AppendLine($"总文件数: {statistics.TotalFiles:N0}");
            report.AppendLine($"总大小: {FormatFileSize(statistics.TotalSize)}");
            report.AppendLine($"平均文件大小: {FormatFileSize(statistics.TotalFiles > 0 ? statistics.TotalSize / statistics.TotalFiles : 0)}");
            report.AppendLine();

            // 扩展名统计
            if (statistics.ExtensionCounts.Any())
            {
                report.AppendLine("📁 文件类型统计 (前10名)");
                report.AppendLine("-" + new string('-', 30));
                var topExtensions = statistics.ExtensionCounts
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10);

                foreach (var ext in topExtensions)
                {
                    var size = statistics.ExtensionSizes.GetValueOrDefault(ext.Key, 0);
                    var extName = string.IsNullOrEmpty(ext.Key) ? "(无扩展名)" : ext.Key;
                    report.AppendLine($"  {extName}: {ext.Value:N0} 个文件, {FormatFileSize(size)}");
                }
                report.AppendLine();
            }

            // 最大文件
            if (statistics.LargestFiles.Any())
            {
                report.AppendLine("📦 最大文件 (前5名)");
                report.AppendLine("-" + new string('-', 30));
                foreach (var file in statistics.LargestFiles.Take(5))
                {
                    report.AppendLine($"  {FormatFileSize(file.FileSize)} - {Path.GetFileName(file.FilePath)}");
                }
                report.AppendLine();
            }

            // 重复文件
            if (statistics.DuplicateFiles.Any())
            {
                report.AppendLine("🔄 重复文件统计");
                report.AppendLine("-" + new string('-', 30));
                report.AppendLine($"重复文件组数: {statistics.DuplicateFiles.Count:N0}");
                report.AppendLine($"浪费空间: {FormatFileSize(statistics.TotalDuplicateSize)}");
                report.AppendLine();

                report.AppendLine("🔄 最大重复文件组 (前5名)");
                foreach (var duplicate in statistics.DuplicateFiles.Take(5))
                {
                    report.AppendLine($"  {FormatFileSize(duplicate.FileSize)} x {duplicate.FilePaths.Count} = {FormatFileSize(duplicate.TotalWastedSpace)} 浪费");
                    foreach (var path in duplicate.FilePaths.Take(3))
                    {
                        report.AppendLine($"    - {path}");
                    }
                    if (duplicate.FilePaths.Count > 3)
                    {
                        report.AppendLine($"    ... 还有 {duplicate.FilePaths.Count - 3} 个文件");
                    }
                    report.AppendLine();
                }
            }

            return report.ToString();
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的大小字符串</returns>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        #endregion

        #region 文件相似度分析

        /// <summary>
        /// 查找相似文件（基于文件名）
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="similarityThreshold">相似度阈值（0-1）</param>
        /// <param name="includeSubdirectories">是否包含子目录</param>
        /// <returns>相似文件组列表</returns>
        public List<List<string>> FindSimilarFiles(string directoryPath, double similarityThreshold = 0.8,
            bool includeSubdirectories = true)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return new List<List<string>>();

                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, "*.*", searchOption);

                var similarGroups = new List<List<string>>();
                var processed = new HashSet<string>();

                foreach (var file1 in files)
                {
                    if (processed.Contains(file1)) continue;

                    var similarFiles = new List<string> { file1 };
                    processed.Add(file1);

                    var fileName1 = Path.GetFileNameWithoutExtension(file1);

                    foreach (var file2 in files)
                    {
                        if (processed.Contains(file2) || file1 == file2) continue;

                        var fileName2 = Path.GetFileNameWithoutExtension(file2);
                        var similarity = CalculateStringSimilarity(fileName1, fileName2);

                        if (similarity >= similarityThreshold)
                        {
                            similarFiles.Add(file2);
                            processed.Add(file2);
                        }
                    }

                    if (similarFiles.Count > 1)
                    {
                        similarGroups.Add(similarFiles);
                    }
                }

                return similarGroups.OrderByDescending(g => g.Count).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"查找相似文件失败: {ex.Message}");
                return new List<List<string>>();
            }
        }

        /// <summary>
        /// 计算字符串相似度（Levenshtein距离）
        /// </summary>
        /// <param name="s1">字符串1</param>
        /// <param name="s2">字符串2</param>
        /// <returns>相似度（0-1）</returns>
        private double CalculateStringSimilarity(string s1, string s2)
        {
            if (string.IsNullOrEmpty(s1) && string.IsNullOrEmpty(s2))
                return 1.0;

            if (string.IsNullOrEmpty(s1) || string.IsNullOrEmpty(s2))
                return 0.0;

            var maxLength = Math.Max(s1.Length, s2.Length);
            var distance = CalculateLevenshteinDistance(s1.ToLowerInvariant(), s2.ToLowerInvariant());

            return 1.0 - (double)distance / maxLength;
        }

        /// <summary>
        /// 计算Levenshtein距离
        /// </summary>
        /// <param name="s1">字符串1</param>
        /// <param name="s2">字符串2</param>
        /// <returns>编辑距离</returns>
        private int CalculateLevenshteinDistance(string s1, string s2)
        {
            var matrix = new int[s1.Length + 1, s2.Length + 1];

            for (int i = 0; i <= s1.Length; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= s2.Length; j++)
                matrix[0, j] = j;

            for (int i = 1; i <= s1.Length; i++)
            {
                for (int j = 1; j <= s2.Length; j++)
                {
                    var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[s1.Length, s2.Length];
        }

        /// <summary>
        /// 设置文件类型标志
        /// </summary>
        /// <param name="result">文件分析结果</param>
        private void SetFileTypeFlags(FileAnalysisResult result)
        {
            var extension = result.Extension;

            // 文本文件
            var textExtensions = new[] { ".txt", ".json", ".xml", ".csv", ".md", ".log", ".ini", ".cfg", ".sql" };
            result.IsTextFile = textExtensions.Contains(extension);

            // 图像文件
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".ico", ".webp" };
            result.IsImageFile = imageExtensions.Contains(extension);

            // 代码文件
            var codeExtensions = new[] { ".cs", ".js", ".html", ".css", ".py", ".java", ".cpp", ".c", ".h", ".php", ".rb", ".go", ".rs", ".ts", ".vue", ".jsx", ".tsx" };
            result.IsCodeFile = codeExtensions.Contains(extension);

            // 文档文件
            var documentExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".rtf", ".odt", ".ods", ".odp" };
            result.IsDocumentFile = documentExtensions.Contains(extension);

            // 二进制文件（非文本文件）
            result.IsBinaryFile = !result.IsTextFile && !result.IsCodeFile;
        }

        /// <summary>
        /// 执行高级文件分析
        /// </summary>
        /// <param name="result">文件分析结果</param>
        private void PerformAdvancedAnalysis(FileAnalysisResult result)
        {
            try
            {
                // 如果是文本文件或代码文件，进行文本分析
                if (result.IsTextFile || result.IsCodeFile)
                {
                    AnalyzeTextContent(result);
                }

                // 如果是图像文件，进行图像分析
                if (result.IsImageFile)
                {
                    AnalyzeImageContent(result);
                }

                // 如果是代码文件，进行代码分析
                if (result.IsCodeFile)
                {
                    AnalyzeCodeContent(result);
                }

                // 如果是文档文件，进行文档分析
                if (result.IsDocumentFile)
                {
                    AnalyzeDocumentContent(result);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"高级分析失败 {result.FilePath}: {ex.Message}");
            }
        }

        /// <summary>
        /// 分析文本内容
        /// </summary>
        /// <param name="result">文件分析结果</param>
        private void AnalyzeTextContent(FileAnalysisResult result)
        {
            try
            {
                // 检测文件编码
                result.Encoding = DetectFileEncoding(result.FilePath);

                // 读取文件内容
                string content;
                if (result.Encoding == "UTF-8")
                {
                    content = File.ReadAllText(result.FilePath, System.Text.Encoding.UTF8);
                }
                else if (result.Encoding == "GBK")
                {
                    content = File.ReadAllText(result.FilePath, System.Text.Encoding.GetEncoding("GBK"));
                }
                else
                {
                    content = File.ReadAllText(result.FilePath);
                }

                // 基础统计
                result.CharacterCount = content.Length;
                result.LineCount = content.Split('\n').Length;
                result.WordCount = content.Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;

                // 语言检测
                result.DetectedLanguage = DetectTextLanguage(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文本分析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 分析图像文件的格式、尺寸和元数据信息
        ///
        /// 提取图像的宽度、高度、格式、颜色深度等基本信息。
        /// 支持常见的图像格式如JPEG、PNG、GIF、BMP、TIFF等。
        /// </summary>
        /// <param name="result">文件分析结果对象，将在此对象中设置图像相关属性</param>
        /// <remarks>
        /// ⚠️ TODO: 实现完整的图像分析功能
        ///
        /// 当前状态：基础实现，只设置图像格式，尺寸设为0
        ///
        /// 待实现功能：
        /// 1. 图像尺寸检测
        ///    - 使用 ImageSharp 库（推荐，跨平台）
        ///    - 或使用 System.Drawing（Windows限定）
        ///    - 提取宽度、高度信息
        ///
        /// 2. 图像元数据提取
        ///    - EXIF数据（拍摄时间、相机信息、GPS位置等）
        ///    - 颜色深度、压缩质量
        ///    - 图像创建软件信息
        ///
        /// 3. 支持的格式扩展
        ///    - 常见格式：JPEG, PNG, GIF, BMP, TIFF, WEBP
        ///    - RAW格式：CR2, NEF, ARW等（需专门库）
        ///    - 矢量格式：SVG（需XML解析）
        ///
        /// 实现优先级：
        /// - 高优先级: 基础尺寸检测 (JPEG, PNG, GIF, BMP)
        /// - 中优先级: EXIF元数据提取
        /// - 低优先级: RAW格式支持
        ///
        /// 性能考虑：
        /// - 大图像文件的处理可能耗时较长
        /// - 只读取图像头部信息，避免加载完整图像数据
        /// - 添加文件大小限制，避免处理超大文件
        ///
        /// 依赖库建议：
        /// - ImageSharp (跨平台，现代化API)
        /// - MetadataExtractor (EXIF数据提取)
        /// - System.Drawing (Windows平台，简单场景)
        /// </remarks>
        private void AnalyzeImageContent(FileAnalysisResult result)
        {
            try
            {
                // 设置图像格式（基于文件扩展名）
                result.ImageFormat = result.Extension.TrimStart('.').ToUpperInvariant();

                // ⚠️ TODO: 实现真实的图像分析逻辑
                // 当前为基础实现，只设置格式信息

                // 根据文件扩展名和大小决定分析策略
                if (result.FileSize > 50 * 1024 * 1024) // 50MB以上的大文件
                {
                    // 大文件只进行基础分析，避免性能问题
                    Console.WriteLine($"图像文件过大 ({result.FileSize / 1024 / 1024}MB)，跳过详细分析");
                    SetDefaultImageProperties(result);
                    return;
                }

                switch (result.Extension.ToLowerInvariant())
                {
                    case ".jpg":
                    case ".jpeg":
                        // TODO: 实现JPEG图像分析
                        // 建议使用 ImageSharp 或 MetadataExtractor
                        AnalyzeJpegImage(result);
                        break;

                    case ".png":
                        // TODO: 实现PNG图像分析
                        AnalyzePngImage(result);
                        break;

                    case ".gif":
                        // TODO: 实现GIF图像分析（包括动画帧数）
                        AnalyzeGifImage(result);
                        break;

                    case ".bmp":
                        // TODO: 实现BMP图像分析
                        AnalyzeBmpImage(result);
                        break;

                    case ".tiff":
                    case ".tif":
                        // TODO: 实现TIFF图像分析
                        AnalyzeTiffImage(result);
                        break;

                    case ".webp":
                        // TODO: 实现WebP图像分析
                        AnalyzeWebpImage(result);
                        break;

                    case ".svg":
                        // TODO: 实现SVG矢量图分析（需XML解析）
                        AnalyzeSvgImage(result);
                        break;

                    default:
                        // 不支持的图像格式，设置默认值
                        SetDefaultImageProperties(result);
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"图像分析失败 {result.FilePath}: {ex.Message}");
                // 分析失败时设置默认值，确保程序继续运行
                SetDefaultImageProperties(result);
            }
        }

        /// <summary>
        /// 分析JPEG图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现JPEG图像的尺寸和EXIF数据提取</remarks>
        private void AnalyzeJpegImage(FileAnalysisResult result)
        {
            // TODO: 使用图像处理库实现
            // 示例伪代码：
            // using (var image = Image.Load(result.FilePath))
            // {
            //     result.ImageWidth = image.Width;
            //     result.ImageHeight = image.Height;
            //     // 提取EXIF数据
            //     var exif = image.Metadata.ExifProfile;
            //     if (exif != null)
            //     {
            //         // 提取拍摄时间、相机信息等
            //     }
            // }

            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析PNG图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现PNG图像的尺寸和元数据提取</remarks>
        private void AnalyzePngImage(FileAnalysisResult result)
        {
            // TODO: 使用图像处理库实现
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析GIF图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现GIF图像的尺寸和动画帧数分析</remarks>
        private void AnalyzeGifImage(FileAnalysisResult result)
        {
            // TODO: 使用图像处理库实现，包括动画帧数检测
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析BMP图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现BMP图像的尺寸分析</remarks>
        private void AnalyzeBmpImage(FileAnalysisResult result)
        {
            // TODO: 使用图像处理库实现
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析TIFF图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现TIFF图像的尺寸和多页分析</remarks>
        private void AnalyzeTiffImage(FileAnalysisResult result)
        {
            // TODO: 使用图像处理库实现，包括多页TIFF支持
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析WebP图像
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现WebP图像的尺寸分析</remarks>
        private void AnalyzeWebpImage(FileAnalysisResult result)
        {
            // TODO: 使用支持WebP的图像处理库实现
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 分析SVG矢量图
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现SVG的viewBox尺寸解析</remarks>
        private void AnalyzeSvgImage(FileAnalysisResult result)
        {
            // TODO: 使用XML解析器提取SVG的viewBox信息
            // SVG是矢量格式，尺寸可能是相对的
            SetDefaultImageProperties(result);
        }

        /// <summary>
        /// 设置图像属性的默认值
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>
        /// 当图像分析失败或不支持的格式时，设置默认值确保程序稳定运行
        /// </remarks>
        private void SetDefaultImageProperties(FileAnalysisResult result)
        {
            result.ImageWidth = 0;       // 未知宽度
            result.ImageHeight = 0;      // 未知高度
            // ImageFormat 已在主方法中设置
        }

        /// <summary>
        /// 分析代码内容
        /// </summary>
        /// <param name="result">文件分析结果</param>
        private void AnalyzeCodeContent(FileAnalysisResult result)
        {
            try
            {
                // 检测编程语言
                result.ProgrammingLanguage = DetectProgrammingLanguage(result.Extension);

                // 读取文件内容进行代码分析
                var lines = File.ReadAllLines(result.FilePath);
                result.CodeLines = 0;
                result.CommentLines = 0;
                result.BlankLines = 0;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine))
                    {
                        result.BlankLines++;
                    }
                    else if (IsCommentLine(trimmedLine, result.ProgrammingLanguage))
                    {
                        result.CommentLines++;
                    }
                    else
                    {
                        result.CodeLines++;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"代码分析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 分析文档文件的元数据和内容信息
        ///
        /// 提取文档的页数、作者、标题、主题等元数据信息。
        /// 支持PDF、Word、Excel、PowerPoint等常见办公文档格式。
        /// </summary>
        /// <param name="result">文件分析结果对象，将在此对象中设置文档相关属性</param>
        /// <remarks>
        /// ⚠️ TODO: 实现完整的文档分析功能
        ///
        /// 当前状态：占位符实现，所有文档属性都设置为默认值
        ///
        /// 待实现功能：
        /// 1. PDF文档分析
        ///    - 使用 iTextSharp 或 PdfSharp 库
        ///    - 提取页数、作者、标题、创建日期等元数据
        ///    - 支持加密PDF的处理
        ///
        /// 2. Microsoft Office文档分析
        ///    - 使用 DocumentFormat.OpenXml 库处理 .docx, .xlsx, .pptx
        ///    - 使用 Microsoft.Office.Interop 处理旧格式 .doc, .xls, .ppt
        ///    - 提取文档属性、页数/工作表数/幻灯片数
        ///
        /// 3. 其他文档格式
        ///    - RTF: 使用 RTF 解析库
        ///    - OpenDocument: 使用 ODF 工具包
        ///
        /// 实现优先级：
        /// - 高优先级: PDF (.pdf)
        /// - 中优先级: Word (.docx, .doc)
        /// - 低优先级: Excel (.xlsx, .xls), PowerPoint (.pptx, .ppt)
        ///
        /// 性能考虑：
        /// - 大文档的元数据提取可能耗时较长
        /// - 建议添加超时机制和进度报告
        /// - 考虑缓存机制避免重复分析
        ///
        /// 依赖库建议：
        /// - iText7 (PDF处理，免费版)
        /// - DocumentFormat.OpenXml (Office文档，微软官方)
        /// - EPPlus (Excel处理，开源)
        /// </remarks>
        private void AnalyzeDocumentContent(FileAnalysisResult result)
        {
            try
            {
                // ⚠️ TODO: 实现真实的文档分析逻辑
                // 当前为占位符实现，返回默认值

                // 根据文件扩展名选择相应的分析策略
                switch (result.Extension.ToLowerInvariant())
                {
                    case ".pdf":
                        // TODO: 实现PDF文档分析
                        // 建议使用 iText7 或 PdfSharp
                        AnalyzePdfDocument(result);
                        break;

                    case ".docx":
                    case ".doc":
                        // TODO: 实现Word文档分析
                        // 建议使用 DocumentFormat.OpenXml
                        AnalyzeWordDocument(result);
                        break;

                    case ".xlsx":
                    case ".xls":
                        // TODO: 实现Excel文档分析
                        // 建议使用 EPPlus 或 DocumentFormat.OpenXml
                        AnalyzeExcelDocument(result);
                        break;

                    case ".pptx":
                    case ".ppt":
                        // TODO: 实现PowerPoint文档分析
                        // 建议使用 DocumentFormat.OpenXml
                        AnalyzePowerPointDocument(result);
                        break;

                    default:
                        // 不支持的文档格式，设置默认值
                        SetDefaultDocumentProperties(result);
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文档分析失败 {result.FilePath}: {ex.Message}");
                // 分析失败时设置默认值，确保程序继续运行
                SetDefaultDocumentProperties(result);
            }
        }

        /// <summary>
        /// 分析PDF文档
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现PDF文档的元数据提取</remarks>
        private void AnalyzePdfDocument(FileAnalysisResult result)
        {
            // TODO: 使用PDF处理库实现
            // 示例伪代码：
            // using (var reader = new PdfReader(result.FilePath))
            // {
            //     result.PageCount = reader.NumberOfPages;
            //     var info = reader.Info;
            //     result.Author = info.GetValueOrDefault("Author", "");
            //     result.Title = info.GetValueOrDefault("Title", "");
            //     result.Subject = info.GetValueOrDefault("Subject", "");
            // }

            SetDefaultDocumentProperties(result);
        }

        /// <summary>
        /// 分析Word文档
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现Word文档的元数据提取</remarks>
        private void AnalyzeWordDocument(FileAnalysisResult result)
        {
            // TODO: 使用Office处理库实现
            SetDefaultDocumentProperties(result);
        }

        /// <summary>
        /// 分析Excel文档
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现Excel文档的元数据提取</remarks>
        private void AnalyzeExcelDocument(FileAnalysisResult result)
        {
            // TODO: 使用Excel处理库实现
            SetDefaultDocumentProperties(result);
        }

        /// <summary>
        /// 分析PowerPoint文档
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>TODO: 实现PowerPoint文档的元数据提取</remarks>
        private void AnalyzePowerPointDocument(FileAnalysisResult result)
        {
            // TODO: 使用PowerPoint处理库实现
            SetDefaultDocumentProperties(result);
        }

        /// <summary>
        /// 设置文档属性的默认值
        /// </summary>
        /// <param name="result">文件分析结果</param>
        /// <remarks>
        /// 当文档分析失败或不支持的格式时，设置默认值确保程序稳定运行
        /// </remarks>
        private void SetDefaultDocumentProperties(FileAnalysisResult result)
        {
            result.PageCount = 0;        // 未知页数
            result.Author = "";          // 未知作者
            result.Title = "";           // 未知标题
            result.Subject = "";         // 未知主题
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 检测文件编码
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>编码名称</returns>
        private string DetectFileEncoding(string filePath)
        {
            try
            {
                using var reader = new StreamReader(filePath, true);
                reader.ReadToEnd();
                return reader.CurrentEncoding.EncodingName switch
                {
                    "Unicode (UTF-8)" => "UTF-8",
                    "Unicode (UTF-16)" => "UTF-16",
                    "Unicode (UTF-32)" => "UTF-32",
                    _ => "ASCII"
                };
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// 检测文本语言
        /// </summary>
        /// <param name="content">文本内容</param>
        /// <returns>语言代码</returns>
        private string DetectTextLanguage(string content)
        {
            if (string.IsNullOrEmpty(content))
                return "unknown";

            // 简单的中文检测
            var chineseCharCount = content.Count(c => c >= 0x4e00 && c <= 0x9fff);
            var totalChars = content.Length;

            if (totalChars > 0 && (double)chineseCharCount / totalChars > 0.1)
                return "zh";

            return "en";
        }

        /// <summary>
        /// 检测编程语言
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>编程语言名称</returns>
        private string DetectProgrammingLanguage(string extension)
        {
            return extension switch
            {
                ".cs" => "C#",
                ".js" => "JavaScript",
                ".ts" => "TypeScript",
                ".py" => "Python",
                ".java" => "Java",
                ".cpp" or ".cc" => "C++",
                ".c" => "C",
                ".h" => "C/C++ Header",
                ".php" => "PHP",
                ".rb" => "Ruby",
                ".go" => "Go",
                ".rs" => "Rust",
                ".html" => "HTML",
                ".css" => "CSS",
                ".sql" => "SQL",
                ".xml" => "XML",
                ".json" => "JSON",
                ".vue" => "Vue.js",
                ".jsx" => "React JSX",
                ".tsx" => "React TSX",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// 判断是否为注释行
        /// </summary>
        /// <param name="line">代码行</param>
        /// <param name="language">编程语言</param>
        /// <returns>是否为注释行</returns>
        private bool IsCommentLine(string line, string language)
        {
            return language switch
            {
                "C#" or "JavaScript" or "TypeScript" or "Java" or "C++" or "C" =>
                    line.StartsWith("//") || line.StartsWith("/*") || line.StartsWith("*"),
                "Python" or "Ruby" => line.StartsWith("#"),
                "HTML" or "XML" => line.StartsWith("<!--"),
                "CSS" => line.StartsWith("/*"),
                "SQL" => line.StartsWith("--") || line.StartsWith("/*"),
                _ => false
            };
        }

        #endregion
    }
}
