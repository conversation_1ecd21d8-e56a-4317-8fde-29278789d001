using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text;

using Zylo.YIO.Config;

namespace Zylo.YIO.Analysis
{
    /// <summary>
    /// YSpaceAnalyzer - 磁盘空间分析工具类
    /// 提供磁盘使用率监控、空间预警、增长趋势分析、空间优化建议等功能
    /// 支持多驱动器分析、详细报告生成、可视化数据输出
    /// </summary>
 
    [YServiceScoped] 
    public partial class YSpaceAnalyzer
    {
        #region 私有字段

        private readonly YIOConfig _config;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化YSpaceAnalyzer
        /// </summary>
        /// <param name="config">YIO配置</param>
        public YSpaceAnalyzer(YIOConfig config)
        {
            _config = config ?? new YIOConfig();
        }

        /// <summary>
        /// 无参构造函数（用于静态调用）
        /// </summary>
        public YSpaceAnalyzer() : this(new YIOConfig())
        {
        }

        #endregion

        #region 数据模型

        /// <summary>
        /// 磁盘空间信息
        /// </summary>
        public class DriveSpaceInfo
        {
            public string DriveName { get; set; } = "";
            public string DriveType { get; set; } = "";
            public string FileSystem { get; set; } = "";
            public long TotalSize { get; set; }
            public long UsedSpace { get; set; }
            public long FreeSpace { get; set; }
            public double UsagePercentage => TotalSize > 0 ? (double)UsedSpace / TotalSize * 100 : 0;
            public bool IsReady { get; set; }
            public string VolumeLabel { get; set; } = "";
        }

        /// <summary>
        /// 目录空间信息
        /// </summary>
        public class DirectorySpaceInfo
        {
            public string DirectoryPath { get; set; } = "";
            public long TotalSize { get; set; }
            public int FileCount { get; set; }
            public int SubDirectoryCount { get; set; }
            public List<DirectorySpaceInfo> SubDirectories { get; set; } = new();
            public List<FileSpaceInfo> LargestFiles { get; set; } = new();
            public DateTime LastAnalyzed { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// 文件空间信息
        /// </summary>
        public class FileSpaceInfo
        {
            public string FilePath { get; set; } = "";
            public long FileSize { get; set; }
            public string Extension { get; set; } = "";
            public DateTime LastModified { get; set; }
            public double PercentageOfParent { get; set; }
        }

        /// <summary>
        /// 空间分析报告
        /// </summary>
        public class SpaceAnalysisReport
        {
            public DateTime AnalysisTime { get; set; } = DateTime.Now;
            public List<DriveSpaceInfo> Drives { get; set; } = new();
            public List<DirectorySpaceInfo> LargestDirectories { get; set; } = new();
            public List<FileSpaceInfo> LargestFiles { get; set; } = new();
            public Dictionary<string, long> ExtensionSizes { get; set; } = new();
            public List<string> Recommendations { get; set; } = new();
            public long TotalAnalyzedSize { get; set; }
            public int TotalAnalyzedFiles { get; set; }
        }

        /// <summary>
        /// 空间预警信息
        /// </summary>
        public class SpaceAlert
        {
            public string DriveName { get; set; } = "";
            public AlertLevel Level { get; set; }
            public double UsagePercentage { get; set; }
            public long FreeSpace { get; set; }
            public string Message { get; set; } = "";
            public DateTime AlertTime { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// 预警级别
        /// </summary>
        public enum AlertLevel
        {
            Info,       // 信息
            Warning,    // 警告
            Critical,   // 严重
            Emergency   // 紧急
        }

        #endregion

        #region 磁盘空间分析

        /// <summary>
        /// 获取所有驱动器的空间信息
        /// </summary>
        /// <returns>驱动器空间信息列表</returns>
        public List<DriveSpaceInfo> GetAllDrivesSpaceInfo()
        {
            var drives = new List<DriveSpaceInfo>();

            try
            {
                foreach (var drive in DriveInfo.GetDrives())
                {
                    var driveInfo = new DriveSpaceInfo
                    {
                        DriveName = drive.Name,
                        DriveType = drive.DriveType.ToString(),
                        IsReady = drive.IsReady
                    };

                    if (drive.IsReady)
                    {
                        try
                        {
                            driveInfo.FileSystem = drive.DriveFormat;
                            driveInfo.TotalSize = drive.TotalSize;
                            driveInfo.FreeSpace = drive.TotalFreeSpace;
                            driveInfo.UsedSpace = driveInfo.TotalSize - driveInfo.FreeSpace;
                            driveInfo.VolumeLabel = drive.VolumeLabel ?? "";
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"获取驱动器信息失败 {drive.Name}: {ex.Message}");
                        }
                    }

                    drives.Add(driveInfo);
                }

                return drives.OrderBy(d => d.DriveName).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取驱动器列表失败: {ex.Message}");
                return drives;
            }
        }

        /// <summary>
        /// 获取指定驱动器的空间信息
        /// </summary>
        /// <param name="driveName">驱动器名称</param>
        /// <returns>驱动器空间信息</returns>
        public DriveSpaceInfo? GetDriveSpaceInfo(string driveName)
        {
            try
            {
                var drive = new DriveInfo(driveName);
                if (!drive.IsReady)
                    return null;

                return new DriveSpaceInfo
                {
                    DriveName = drive.Name,
                    DriveType = drive.DriveType.ToString(),
                    FileSystem = drive.DriveFormat,
                    TotalSize = drive.TotalSize,
                    FreeSpace = drive.TotalFreeSpace,
                    UsedSpace = drive.TotalSize - drive.TotalFreeSpace,
                    VolumeLabel = drive.VolumeLabel ?? "",
                    IsReady = true
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取驱动器信息失败 {driveName}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查空间预警
        /// </summary>
        /// <param name="warningThreshold">警告阈值（百分比）</param>
        /// <param name="criticalThreshold">严重阈值（百分比）</param>
        /// <param name="emergencyThreshold">紧急阈值（百分比）</param>
        /// <returns>预警信息列表</returns>
        public List<SpaceAlert> CheckSpaceAlerts(double warningThreshold = 80,
            double criticalThreshold = 90, double emergencyThreshold = 95)
        {
            var alerts = new List<SpaceAlert>();

            try
            {
                var drives = GetAllDrivesSpaceInfo();

                foreach (var drive in drives.Where(d => d.IsReady))
                {
                    var alert = new SpaceAlert
                    {
                        DriveName = drive.DriveName,
                        UsagePercentage = drive.UsagePercentage,
                        FreeSpace = drive.FreeSpace
                    };

                    if (drive.UsagePercentage >= emergencyThreshold)
                    {
                        alert.Level = AlertLevel.Emergency;
                        alert.Message = $"磁盘空间严重不足！使用率 {drive.UsagePercentage:F1}%，剩余 {FormatFileSize(drive.FreeSpace)}";
                    }
                    else if (drive.UsagePercentage >= criticalThreshold)
                    {
                        alert.Level = AlertLevel.Critical;
                        alert.Message = $"磁盘空间不足，使用率 {drive.UsagePercentage:F1}%，剩余 {FormatFileSize(drive.FreeSpace)}";
                    }
                    else if (drive.UsagePercentage >= warningThreshold)
                    {
                        alert.Level = AlertLevel.Warning;
                        alert.Message = $"磁盘空间偏高，使用率 {drive.UsagePercentage:F1}%，剩余 {FormatFileSize(drive.FreeSpace)}";
                    }
                    else
                    {
                        alert.Level = AlertLevel.Info;
                        alert.Message = $"磁盘空间正常，使用率 {drive.UsagePercentage:F1}%，剩余 {FormatFileSize(drive.FreeSpace)}";
                    }

                    alerts.Add(alert);
                }

                return alerts.OrderByDescending(a => a.Level).ThenByDescending(a => a.UsagePercentage).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查空间预警失败: {ex.Message}");
                return alerts;
            }
        }

        #endregion

        #region 目录空间分析

        /// <summary>
        /// 分析目录空间使用情况
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="includeFiles">是否包含文件详情</param>
        /// <returns>目录空间信息</returns>
        public DirectorySpaceInfo AnalyzeDirectorySpace(string directoryPath, int maxDepth = 3, bool includeFiles = true)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                return AnalyzeDirectorySpaceRecursive(directoryPath, 0, maxDepth, includeFiles);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析目录空间失败 {directoryPath}: {ex.Message}");
                return new DirectorySpaceInfo { DirectoryPath = directoryPath };
            }
        }

        /// <summary>
        /// 异步分析目录空间使用情况
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="includeFiles">是否包含文件详情</param>
        /// <returns>目录空间信息</returns>
        public async Task<DirectorySpaceInfo> AnalyzeDirectorySpaceAsync(string directoryPath, int maxDepth = 3, bool includeFiles = true)
        {
            return await Task.Run(() => AnalyzeDirectorySpace(directoryPath, maxDepth, includeFiles));
        }

        /// <summary>
        /// 递归分析目录空间
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="currentDepth">当前深度</param>
        /// <param name="maxDepth">最大深度</param>
        /// <param name="includeFiles">是否包含文件详情</param>
        /// <returns>目录空间信息</returns>
        private DirectorySpaceInfo AnalyzeDirectorySpaceRecursive(string directoryPath, int currentDepth, int maxDepth, bool includeFiles)
        {
            var dirInfo = new DirectorySpaceInfo
            {
                DirectoryPath = directoryPath
            };

            try
            {
                var directory = new DirectoryInfo(directoryPath);

                // 分析文件
                var files = directory.GetFiles();
                dirInfo.FileCount = files.Length;
                dirInfo.TotalSize = files.Sum(f => f.Length);

                if (includeFiles)
                {
                    dirInfo.LargestFiles = files
                        .OrderByDescending(f => f.Length)
                        .Take(10)
                        .Select(f => new FileSpaceInfo
                        {
                            FilePath = f.FullName,
                            FileSize = f.Length,
                            Extension = f.Extension.ToLowerInvariant(),
                            LastModified = f.LastWriteTime,
                            PercentageOfParent = dirInfo.TotalSize > 0 ? (double)f.Length / dirInfo.TotalSize * 100 : 0
                        })
                        .ToList();
                }

                // 分析子目录
                if (currentDepth < maxDepth)
                {
                    var subDirectories = directory.GetDirectories();
                    dirInfo.SubDirectoryCount = subDirectories.Length;

                    foreach (var subDir in subDirectories)
                    {
                        try
                        {
                            var subDirInfo = AnalyzeDirectorySpaceRecursive(subDir.FullName, currentDepth + 1, maxDepth, false);
                            dirInfo.SubDirectories.Add(subDirInfo);
                            dirInfo.TotalSize += subDirInfo.TotalSize;
                            dirInfo.FileCount += subDirInfo.FileCount;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"分析子目录失败 {subDir.FullName}: {ex.Message}");
                        }
                    }

                    // 排序子目录（按大小）
                    dirInfo.SubDirectories = dirInfo.SubDirectories
                        .OrderByDescending(d => d.TotalSize)
                        .ToList();
                }

                return dirInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析目录失败 {directoryPath}: {ex.Message}");
                return dirInfo;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的大小字符串</returns>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        #endregion

        #region 空间分析报告

        /// <summary>
        /// 生成完整的空间分析报告
        /// </summary>
        /// <param name="rootPath">根路径</param>
        /// <param name="includeSystemDrives">是否包含系统驱动器</param>
        /// <returns>空间分析报告</returns>
        public SpaceAnalysisReport GenerateSpaceAnalysisReport(string? rootPath = null, bool includeSystemDrives = true)
        {
            var report = new SpaceAnalysisReport();

            try
            {
                // 分析所有驱动器
                if (includeSystemDrives)
                {
                    report.Drives = GetAllDrivesSpaceInfo();
                }

                // 分析指定路径
                if (!string.IsNullOrEmpty(rootPath) && Directory.Exists(rootPath))
                {
                    var rootDirInfo = AnalyzeDirectorySpace(rootPath, 2, true);
                    report.LargestDirectories.Add(rootDirInfo);
                    report.TotalAnalyzedSize = rootDirInfo.TotalSize;
                    report.TotalAnalyzedFiles = rootDirInfo.FileCount;

                    // 收集最大文件
                    CollectLargestFiles(rootDirInfo, report.LargestFiles, 20);

                    // 统计扩展名大小
                    CalculateExtensionSizes(rootDirInfo, report.ExtensionSizes);
                }

                // 生成建议
                report.Recommendations = GenerateRecommendations(report);

                return report;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成空间分析报告失败: {ex.Message}");
                return report;
            }
        }

        /// <summary>
        /// 收集最大文件
        /// </summary>
        /// <param name="dirInfo">目录信息</param>
        /// <param name="largestFiles">最大文件列表</param>
        /// <param name="maxCount">最大数量</param>
        private void CollectLargestFiles(DirectorySpaceInfo dirInfo, List<FileSpaceInfo> largestFiles, int maxCount)
        {
            // 添加当前目录的大文件
            largestFiles.AddRange(dirInfo.LargestFiles);

            // 递归处理子目录
            foreach (var subDir in dirInfo.SubDirectories)
            {
                CollectLargestFiles(subDir, largestFiles, maxCount);
            }

            // 保持列表大小
            if (largestFiles.Count > maxCount)
            {
                largestFiles.Sort((a, b) => b.FileSize.CompareTo(a.FileSize));
                largestFiles.RemoveRange(maxCount, largestFiles.Count - maxCount);
            }
        }

        /// <summary>
        /// 计算扩展名大小统计
        /// </summary>
        /// <param name="dirInfo">目录信息</param>
        /// <param name="extensionSizes">扩展名大小字典</param>
        private void CalculateExtensionSizes(DirectorySpaceInfo dirInfo, Dictionary<string, long> extensionSizes)
        {
            // 统计当前目录的文件
            foreach (var file in dirInfo.LargestFiles)
            {
                var ext = string.IsNullOrEmpty(file.Extension) ? "(无扩展名)" : file.Extension;
                extensionSizes[ext] = extensionSizes.GetValueOrDefault(ext, 0) + file.FileSize;
            }

            // 递归处理子目录
            foreach (var subDir in dirInfo.SubDirectories)
            {
                CalculateExtensionSizes(subDir, extensionSizes);
            }
        }

        /// <summary>
        /// 生成优化建议
        /// </summary>
        /// <param name="report">分析报告</param>
        /// <returns>建议列表</returns>
        private List<string> GenerateRecommendations(SpaceAnalysisReport report)
        {
            var recommendations = new List<string>();

            try
            {
                // 驱动器空间建议
                foreach (var drive in report.Drives.Where(d => d.IsReady))
                {
                    if (drive.UsagePercentage > 90)
                    {
                        recommendations.Add($"🚨 {drive.DriveName} 驱动器空间严重不足（{drive.UsagePercentage:F1}%），建议立即清理");
                    }
                    else if (drive.UsagePercentage > 80)
                    {
                        recommendations.Add($"⚠️ {drive.DriveName} 驱动器空间偏高（{drive.UsagePercentage:F1}%），建议定期清理");
                    }
                }

                // 大文件建议
                var veryLargeFiles = report.LargestFiles.Where(f => f.FileSize > 100 * 1024 * 1024).ToList(); // > 100MB
                if (veryLargeFiles.Any())
                {
                    recommendations.Add($"📦 发现 {veryLargeFiles.Count} 个大文件（>100MB），总计 {FormatFileSize(veryLargeFiles.Sum(f => f.FileSize))}");
                }

                // 扩展名建议
                var topExtensions = report.ExtensionSizes.OrderByDescending(kvp => kvp.Value).Take(5);
                foreach (var ext in topExtensions)
                {
                    if (ext.Value > 500 * 1024 * 1024) // > 500MB
                    {
                        recommendations.Add($"📁 {ext.Key} 文件占用空间较大：{FormatFileSize(ext.Value)}，可考虑压缩或清理");
                    }
                }

                // 临时文件建议
                var tempExtensions = new[] { ".tmp", ".temp", ".log", ".bak", ".old" };
                var tempSize = report.ExtensionSizes
                    .Where(kvp => tempExtensions.Contains(kvp.Key.ToLowerInvariant()))
                    .Sum(kvp => kvp.Value);

                if (tempSize > 50 * 1024 * 1024) // > 50MB
                {
                    recommendations.Add($"🗑️ 临时文件占用 {FormatFileSize(tempSize)}，建议定期清理");
                }

                // 重复文件建议（如果有分析数据）
                if (report.TotalAnalyzedFiles > 1000)
                {
                    recommendations.Add("🔍 建议运行重复文件检测，可能存在重复文件浪费空间");
                }

                return recommendations;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成建议失败: {ex.Message}");
                return recommendations;
            }
        }

        /// <summary>
        /// 生成空间分析报告文本
        /// </summary>
        /// <param name="report">分析报告</param>
        /// <returns>报告文本</returns>
        public string GenerateSpaceReportText(SpaceAnalysisReport report)
        {
            var reportText = new StringBuilder();

            reportText.AppendLine("💾 磁盘空间分析报告");
            reportText.AppendLine("=" + new string('=', 50));
            reportText.AppendLine($"分析时间: {report.AnalysisTime:yyyy-MM-dd HH:mm:ss}");
            reportText.AppendLine();

            // 驱动器信息
            if (report.Drives.Any())
            {
                reportText.AppendLine("🖥️ 驱动器空间信息");
                reportText.AppendLine("-" + new string('-', 30));
                foreach (var drive in report.Drives.Where(d => d.IsReady))
                {
                    var statusIcon = drive.UsagePercentage > 90 ? "🚨" : drive.UsagePercentage > 80 ? "⚠️" : "✅";
                    reportText.AppendLine($"{statusIcon} {drive.DriveName} ({drive.VolumeLabel})");
                    reportText.AppendLine($"   类型: {drive.DriveType} | 文件系统: {drive.FileSystem}");
                    reportText.AppendLine($"   总容量: {FormatFileSize(drive.TotalSize)}");
                    reportText.AppendLine($"   已使用: {FormatFileSize(drive.UsedSpace)} ({drive.UsagePercentage:F1}%)");
                    reportText.AppendLine($"   可用空间: {FormatFileSize(drive.FreeSpace)}");
                    reportText.AppendLine();
                }
            }

            // 最大文件
            if (report.LargestFiles.Any())
            {
                reportText.AppendLine("📦 最大文件 (前10名)");
                reportText.AppendLine("-" + new string('-', 30));
                foreach (var file in report.LargestFiles.Take(10))
                {
                    reportText.AppendLine($"  {FormatFileSize(file.FileSize)} - {Path.GetFileName(file.FilePath)}");
                    reportText.AppendLine($"    路径: {file.FilePath}");
                    reportText.AppendLine($"    修改时间: {file.LastModified:yyyy-MM-dd HH:mm:ss}");
                    reportText.AppendLine();
                }
            }

            // 扩展名统计
            if (report.ExtensionSizes.Any())
            {
                reportText.AppendLine("📁 文件类型空间占用 (前10名)");
                reportText.AppendLine("-" + new string('-', 30));
                var topExtensions = report.ExtensionSizes
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(10);

                foreach (var ext in topExtensions)
                {
                    var percentage = report.TotalAnalyzedSize > 0 ? (double)ext.Value / report.TotalAnalyzedSize * 100 : 0;
                    reportText.AppendLine($"  {ext.Key}: {FormatFileSize(ext.Value)} ({percentage:F1}%)");
                }
                reportText.AppendLine();
            }

            // 优化建议
            if (report.Recommendations.Any())
            {
                reportText.AppendLine("💡 优化建议");
                reportText.AppendLine("-" + new string('-', 30));
                foreach (var recommendation in report.Recommendations)
                {
                    reportText.AppendLine($"  {recommendation}");
                }
                reportText.AppendLine();
            }

            // 统计摘要
            reportText.AppendLine("📊 分析摘要");
            reportText.AppendLine("-" + new string('-', 30));
            reportText.AppendLine($"分析文件数: {report.TotalAnalyzedFiles:N0}");
            reportText.AppendLine($"分析总大小: {FormatFileSize(report.TotalAnalyzedSize)}");
            if (report.Drives.Any(d => d.IsReady))
            {
                var totalDriveSpace = report.Drives.Where(d => d.IsReady).Sum(d => d.TotalSize);
                var totalUsedSpace = report.Drives.Where(d => d.IsReady).Sum(d => d.UsedSpace);
                reportText.AppendLine($"系统总容量: {FormatFileSize(totalDriveSpace)}");
                reportText.AppendLine($"系统已使用: {FormatFileSize(totalUsedSpace)} ({(totalDriveSpace > 0 ? (double)totalUsedSpace / totalDriveSpace * 100 : 0):F1}%)");
            }

            return reportText.ToString();
        }

        #endregion

        #region 空间趋势分析

        /// <summary>
        /// 预测空间使用趋势
        /// </summary>
        /// <param name="driveName">驱动器名称</param>
        /// <param name="historicalData">历史数据（日期和使用量）</param>
        /// <returns>预测结果</returns>
        public SpaceTrendPrediction PredictSpaceTrend(string driveName, Dictionary<DateTime, long> historicalData)
        {
            var prediction = new SpaceTrendPrediction { DriveName = driveName };

            try
            {
                if (historicalData.Count < 2)
                {
                    prediction.ErrorMessage = "历史数据不足，无法进行趋势预测";
                    return prediction;
                }

                var sortedData = historicalData.OrderBy(kvp => kvp.Key).ToList();
                var dataPoints = sortedData.Count;

                // 简单线性回归计算增长率
                var avgGrowthPerDay = CalculateAverageGrowthRate(sortedData);
                prediction.DailyGrowthRate = avgGrowthPerDay;

                // 获取当前驱动器信息
                var currentDriveInfo = GetDriveSpaceInfo(driveName);
                if (currentDriveInfo != null)
                {
                    prediction.CurrentUsedSpace = currentDriveInfo.UsedSpace;
                    prediction.TotalCapacity = currentDriveInfo.TotalSize;

                    // 预测未来使用量
                    var remainingSpace = currentDriveInfo.FreeSpace;
                    if (avgGrowthPerDay > 0)
                    {
                        var daysToFull = remainingSpace / avgGrowthPerDay;
                        prediction.DaysUntilFull = (int)Math.Ceiling(daysToFull);
                        prediction.EstimatedFullDate = DateTime.Now.AddDays(daysToFull);
                    }

                    // 生成未来30天的预测
                    for (int i = 1; i <= 30; i++)
                    {
                        var futureDate = DateTime.Now.AddDays(i);
                        var predictedUsage = currentDriveInfo.UsedSpace + (long)(avgGrowthPerDay * i);
                        prediction.FuturePredictions[futureDate] = Math.Min(predictedUsage, currentDriveInfo.TotalSize);
                    }
                }

                return prediction;
            }
            catch (Exception ex)
            {
                prediction.ErrorMessage = $"预测失败: {ex.Message}";
                return prediction;
            }
        }

        /// <summary>
        /// 计算平均增长率
        /// </summary>
        /// <param name="sortedData">排序的历史数据</param>
        /// <returns>每日平均增长字节数</returns>
        private double CalculateAverageGrowthRate(List<KeyValuePair<DateTime, long>> sortedData)
        {
            if (sortedData.Count < 2) return 0;

            var totalGrowth = 0L;
            var totalDays = 0.0;

            for (int i = 1; i < sortedData.Count; i++)
            {
                var growth = sortedData[i].Value - sortedData[i - 1].Value;
                var days = (sortedData[i].Key - sortedData[i - 1].Key).TotalDays;

                if (days > 0)
                {
                    totalGrowth += growth;
                    totalDays += days;
                }
            }

            return totalDays > 0 ? totalGrowth / totalDays : 0;
        }

        /// <summary>
        /// 空间趋势预测结果
        /// </summary>
        public class SpaceTrendPrediction
        {
            public string DriveName { get; set; } = "";
            public double DailyGrowthRate { get; set; }
            public long CurrentUsedSpace { get; set; }
            public long TotalCapacity { get; set; }
            public int DaysUntilFull { get; set; }
            public DateTime? EstimatedFullDate { get; set; }
            public Dictionary<DateTime, long> FuturePredictions { get; set; } = new();
            public string ErrorMessage { get; set; } = "";
        }

        #endregion
    }
}
