using System;
using System.Collections.Generic;
using System.IO;
// using Zylo.Toolkit.Attributes; // 暂时注释，等框架创建后启用


namespace Zylo.YIO.Config
{
    /// <summary>
    /// YIOConfig - YIO框架的统一配置类
    /// 包含文件操作、格式处理、安全设置等所有配置项
    /// 支持自动加载和保存配置
    /// </summary>
 
    public partial class YIOConfig
    {
        #region 基础配置

        /// <summary>
        /// 默认工作目录
        /// </summary>
        public string DefaultWorkingDirectory { get; set; } = "./";

        /// <summary>
        /// 临时文件目录
        /// </summary>
        public string TempDirectory { get; set; } = Path.GetTempPath();

        /// <summary>
        /// 备份文件目录
        /// </summary>
        public string BackupDirectory { get; set; } = "./Backups";

        /// <summary>
        /// 日志文件目录
        /// </summary>
        public string LogDirectory { get; set; } = "./Logs";

        #endregion

        #region 文件大小控制

        /// <summary>
        /// 单个文件最大大小（字节）
        /// </summary>
        public long MaxSingleFileSize { get; set; } = 100 * 1024 * 1024; // 100MB

        /// <summary>
        /// 目录最大大小（字节）
        /// </summary>
        public long MaxDirectorySize { get; set; } = 1024 * 1024 * 1024; // 1GB

        /// <summary>
        /// 大文件阈值（字节）
        /// </summary>
        public int LargeFileThreshold { get; set; } = 10 * 1024 * 1024; // 10MB

        /// <summary>
        /// 是否启用大小警告
        /// </summary>
        public bool EnableSizeWarnings { get; set; } = true;

        #endregion

        #region 保存天数管理

        /// <summary>
        /// 默认文件保留天数
        /// </summary>
        public int DefaultRetentionDays { get; set; } = 30;

        /// <summary>
        /// 备份文件保留天数
        /// </summary>
        public int BackupRetentionDays { get; set; } = 90;

        /// <summary>
        /// 临时文件保留小时数
        /// </summary>
        public int TempFileRetentionHours { get; set; } = 24;

        /// <summary>
        /// 日志文件保留天数
        /// </summary>
        public int LogRetentionDays { get; set; } = 7;

        #endregion

        #region 清理策略

        /// <summary>
        /// 是否启用自动清理
        /// </summary>
        public bool AutoCleanup { get; set; } = true;

        /// <summary>
        /// 清理文件模式列表
        /// </summary>
        public string[] CleanupFilePatterns { get; set; } = { "*.tmp", "*.log", "*.bak" };

        /// <summary>
        /// 清理前是否需要确认
        /// </summary>
        public bool ConfirmBeforeCleanup { get; set; } = true;

        /// <summary>
        /// 清理间隔（小时）
        /// </summary>
        public int CleanupIntervalHours { get; set; } = 24;

        #endregion

        #region 异步和性能配置

        /// <summary>
        /// 最大并发操作数
        /// </summary>
        public int MaxConcurrentOperations { get; set; } = 4;

        /// <summary>
        /// 异步操作缓冲区大小（字节）
        /// </summary>
        public int AsyncBufferSize { get; set; } = 64 * 1024; // 64KB

        /// <summary>
        /// 是否启用进度报告
        /// </summary>
        public bool EnableProgressReporting { get; set; } = true;

        /// <summary>
        /// 进度更新间隔（毫秒）
        /// </summary>
        public int ProgressUpdateIntervalMs { get; set; } = 100;

        #endregion

        #region 安全配置

        /// <summary>
        /// 是否启用安全删除
        /// </summary>
        public bool EnableSecureDelete { get; set; } = false;

        /// <summary>
        /// 安全删除覆写次数
        /// </summary>
        public int SecureDeletePasses { get; set; } = 3;

        /// <summary>
        /// 是否启用访问日志
        /// </summary>
        public bool EnableAccessLogging { get; set; } = true;

        /// <summary>
        /// 是否启用路径验证
        /// </summary>
        public bool EnablePathValidation { get; set; } = true;

        /// <summary>
        /// 被阻止的文件扩展名列表
        /// </summary>
        public string[] BlockedExtensions { get; set; } = { ".exe", ".bat", ".cmd", ".scr" };

        #endregion

        #region 格式处理配置

        /// <summary>
        /// JSON格式配置
        /// </summary>
        public JsonSettings JsonSettings { get; set; } = new JsonSettings();

        /// <summary>
        /// XML格式配置
        /// </summary>
        public XmlSettings XmlSettings { get; set; } = new XmlSettings();

        /// <summary>
        /// INI格式配置
        /// </summary>
        public IniSettings IniSettings { get; set; } = new IniSettings();

        /// <summary>
        /// CSV格式配置
        /// </summary>
        public CsvSettings CsvSettings { get; set; } = new CsvSettings();

        #endregion

        #region 备份配置

        /// <summary>
        /// 是否启用增量备份
        /// </summary>
        public bool EnableIncrementalBackup { get; set; } = true;

        /// <summary>
        /// 是否压缩备份文件
        /// </summary>
        public bool CompressBackups { get; set; } = false;

        /// <summary>
        /// 是否验证备份完整性
        /// </summary>
        public bool VerifyBackups { get; set; } = true;

        /// <summary>
        /// 最大备份版本数
        /// </summary>
        public int MaxBackupVersions { get; set; } = 10;

        #endregion

        #region 同步配置

        /// <summary>
        /// 是否启用实时同步
        /// </summary>
        public bool EnableRealTimeSync { get; set; } = false;

        /// <summary>
        /// 同步间隔（分钟）
        /// </summary>
        public int SyncIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// 是否处理同步冲突
        /// </summary>
        public bool HandleSyncConflicts { get; set; } = true;

        /// <summary>
        /// 冲突解决策略：Timestamp, Size, Manual
        /// </summary>
        public string ConflictResolutionStrategy { get; set; } = "Timestamp";

        #endregion

        #region 监控配置

        /// <summary>
        /// 是否启用文件监控
        /// </summary>
        public bool EnableFileWatching { get; set; } = false;

        /// <summary>
        /// 监控文件过滤器
        /// </summary>
        public string WatchFilter { get; set; } = "*.*";

        /// <summary>
        /// 是否包含子目录
        /// </summary>
        public bool IncludeSubdirectories { get; set; } = true;

        /// <summary>
        /// 监控缓冲区大小
        /// </summary>
        public int WatchBufferSize { get; set; } = 8192;

        #endregion
    }

    #region 格式设置类

    /// <summary>
    /// JSON格式设置
    /// </summary>
    public class JsonSettings
    {
        /// <summary>
        /// 是否格式化输出
        /// </summary>
        public bool PrettyPrint { get; set; } = true;

        /// <summary>
        /// 是否忽略注释
        /// </summary>
        public bool IgnoreComments { get; set; } = true;

        /// <summary>
        /// 是否允许尾随逗号
        /// </summary>
        public bool AllowTrailingCommas { get; set; } = true;

        /// <summary>
        /// 最大解析深度
        /// </summary>
        public int MaxDepth { get; set; } = 64;
    }

    /// <summary>
    /// XML格式设置
    /// </summary>
    public class XmlSettings
    {
        /// <summary>
        /// 是否保留空白字符
        /// </summary>
        public bool PreserveWhitespace { get; set; } = false;

        /// <summary>
        /// 解析时是否验证
        /// </summary>
        public bool ValidateOnParse { get; set; } = true;

        /// <summary>
        /// 默认编码
        /// </summary>
        public string DefaultEncoding { get; set; } = "UTF-8";

        /// <summary>
        /// 是否缩进输出
        /// </summary>
        public bool IndentOutput { get; set; } = true;

        /// <summary>
        /// 缩进字符
        /// </summary>
        public string IndentChars { get; set; } = "  ";
    }

    /// <summary>
    /// INI格式设置
    /// </summary>
    public class IniSettings
    {
        /// <summary>
        /// 注释字符
        /// </summary>
        public char CommentChar { get; set; } = '#';

        /// <summary>
        /// 节开始字符
        /// </summary>
        public char SectionStart { get; set; } = '[';

        /// <summary>
        /// 节结束字符
        /// </summary>
        public char SectionEnd { get; set; } = ']';

        /// <summary>
        /// 键值分隔符
        /// </summary>
        public char KeyValueSeparator { get; set; } = '=';

        /// <summary>
        /// 是否去除值的空白字符
        /// </summary>
        public bool TrimValues { get; set; } = true;

        /// <summary>
        /// 是否大小写敏感
        /// </summary>
        public bool CaseSensitive { get; set; } = false;
    }

    /// <summary>
    /// CSV格式设置
    /// </summary>
    public class CsvSettings
    {
        /// <summary>
        /// 字段分隔符
        /// </summary>
        public char Delimiter { get; set; } = ',';

        /// <summary>
        /// 引号字符
        /// </summary>
        public char Quote { get; set; } = '"';

        /// <summary>
        /// 转义字符
        /// </summary>
        public char Escape { get; set; } = '"';

        /// <summary>
        /// 是否包含标题行
        /// </summary>
        public bool HasHeader { get; set; } = true;

        /// <summary>
        /// 是否去除字段空白
        /// </summary>
        public bool TrimFields { get; set; } = true;
    }

    #endregion
}
