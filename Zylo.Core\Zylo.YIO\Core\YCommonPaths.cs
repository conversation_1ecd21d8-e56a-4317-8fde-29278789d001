using System;
using System.IO;
using System.Collections.Generic;
using System.Reflection;

namespace Zylo.YIO.Core
{
    /// <summary>
    /// YCommonPaths - 静态常用路径类
    /// 提供系统常用路径、应用路径、临时路径等静态访问
    /// 包含桌面、文档、下载、图片等用户目录的快速访问
    /// </summary>

    public static class YCommonPaths
    {
        #region 系统路径

        /// <summary>
        /// 获取系统根目录 (C:\ 或 /)
        /// </summary>
        public static string SystemRoot => Path.GetPathRoot(Environment.SystemDirectory) ?? "";

        /// <summary>
        /// 获取系统目录 (C:\Windows\System32)
        /// </summary>
        public static string SystemDirectory => Environment.SystemDirectory;

        /// <summary>
        /// 获取Windows目录 (C:\Windows)
        /// </summary>
        public static string WindowsDirectory => Environment.GetFolderPath(Environment.SpecialFolder.Windows);

        /// <summary>
        /// 获取程序文件目录 (C:\Program Files)
        /// </summary>
        public static string ProgramFiles => Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles);

        /// <summary>
        /// 获取程序文件目录 (x86) (C:\Program Files (x86))
        /// </summary>
        public static string ProgramFilesX86 => Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86);

        #endregion

        #region 用户路径

        /// <summary>
        /// 获取用户主目录 (C:\Users\<USER>\Users\Username\AppData\Roaming)
        /// </summary>
        public static string AppData => Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

        /// <summary>
        /// 获取本地应用数据路径 (C:\Users\<USER>\AppData\Local)
        /// </summary>
        public static string LocalAppData => Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);

        /// <summary>
        /// 获取公共应用数据路径 (C:\ProgramData)
        /// </summary>
        public static string CommonAppData => Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);

        #endregion

        #region 临时路径

        /// <summary>
        /// 获取系统临时目录
        /// </summary>
        public static string TempDirectory => Path.GetTempPath();

        /// <summary>
        /// 生成临时文件路径
        /// </summary>
        /// <param name="extension">文件扩展名（可选）</param>
        /// <returns>临时文件完整路径</returns>
        public static string GetTempFilePath(string? extension = null)
        {
            var tempFile = Path.GetTempFileName();
            if (!string.IsNullOrEmpty(extension))
            {
                var newPath = Path.ChangeExtension(tempFile, extension);
                if (File.Exists(tempFile))
                {
                    File.Delete(tempFile);
                }
                return newPath;
            }
            return tempFile;
        }

        /// <summary>
        /// 生成临时目录路径
        /// </summary>
        /// <param name="prefix">目录名前缀（可选）</param>
        /// <returns>临时目录完整路径</returns>
        public static string GetTempDirectoryPath(string? prefix = null)
        {
            var tempDir = Path.Combine(TempDirectory, (prefix ?? "temp") + "_" + Guid.NewGuid().ToString("N")[..8]);
            return tempDir;
        }

        /// <summary>
        /// 创建临时目录
        /// </summary>
        /// <param name="prefix">目录名前缀（可选）</param>
        /// <returns>创建的临时目录路径</returns>
        public static string CreateTempDirectory(string? prefix = null)
        {
            var tempDir = GetTempDirectoryPath(prefix);
            Directory.CreateDirectory(tempDir);
            return tempDir;
        }

        #endregion

        #region 应用程序路径

        /// <summary>
        /// 获取当前应用程序目录
        /// </summary>
        public static string ApplicationDirectory => AppDomain.CurrentDomain.BaseDirectory;

        /// <summary>
        /// 获取当前应用程序可执行文件路径
        /// </summary>
        public static string ApplicationExecutable => Environment.ProcessPath ?? "";

        /// <summary>
        /// 获取当前应用程序配置目录
        /// </summary>
        public static string ApplicationConfigDirectory => Path.Combine(ApplicationDirectory, "Config");

        /// <summary>
        /// 获取当前应用程序日志目录
        /// </summary>
        public static string ApplicationLogDirectory => Path.Combine(ApplicationDirectory, "Logs");

        /// <summary>
        /// 获取当前应用程序数据目录
        /// </summary>
        public static string ApplicationDataDirectory => Path.Combine(ApplicationDirectory, "Data");

        /// <summary>
        /// 获取当前应用程序缓存目录
        /// </summary>
        public static string ApplicationCacheDirectory => Path.Combine(ApplicationDirectory, "Cache");

        #endregion

        #region 路径检查和创建

        /// <summary>
        /// 确保目录存在，如果不存在则创建
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>目录路径</returns>
        public static string EnsureDirectory(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
            return directoryPath;
        }

        /// <summary>
        /// 确保应用程序标准目录存在
        /// </summary>
        public static void EnsureApplicationDirectories()
        {
            EnsureDirectory(ApplicationConfigDirectory);
            EnsureDirectory(ApplicationLogDirectory);
            EnsureDirectory(ApplicationDataDirectory);
            EnsureDirectory(ApplicationCacheDirectory);
        }

        #endregion

        #region 路径信息获取

        /// <summary>
        /// 获取所有逻辑驱动器
        /// </summary>
        /// <returns>驱动器列表</returns>
        public static string[] GetLogicalDrives()
        {
            return Environment.GetLogicalDrives();
        }

        /// <summary>
        /// 获取驱动器信息
        /// </summary>
        /// <returns>驱动器信息列表</returns>
        public static List<DriveInfo> GetDriveInfos()
        {
            return new List<DriveInfo>(DriveInfo.GetDrives());
        }

        /// <summary>
        /// 获取可用的驱动器信息
        /// </summary>
        /// <returns>可用驱动器信息列表</returns>
        public static List<DriveInfo> GetAvailableDrives()
        {
            var drives = new List<DriveInfo>();
            foreach (var drive in DriveInfo.GetDrives())
            {
                if (drive.IsReady)
                {
                    drives.Add(drive);
                }
            }
            return drives;
        }

        #endregion

        #region 特殊路径

        /// <summary>
        /// 获取回收站路径 (Windows)
        /// </summary>
        public static string RecycleBin => @"C:\$Recycle.Bin";

        /// <summary>
        /// 获取启动目录
        /// </summary>
        public static string Startup => Environment.GetFolderPath(Environment.SpecialFolder.Startup);

        /// <summary>
        /// 获取开始菜单程序目录
        /// </summary>
        public static string StartMenuPrograms => Environment.GetFolderPath(Environment.SpecialFolder.Programs);

        /// <summary>
        /// 获取公共桌面目录
        /// </summary>
        public static string CommonDesktop => Environment.GetFolderPath(Environment.SpecialFolder.CommonDesktopDirectory);

        /// <summary>
        /// 获取公共文档目录
        /// </summary>
        public static string CommonDocuments => Environment.GetFolderPath(Environment.SpecialFolder.CommonDocuments);

        #endregion

        #region 路径常量

        /// <summary>
        /// 路径分隔符
        /// </summary>
        public static char PathSeparator => Path.DirectorySeparatorChar;

        /// <summary>
        /// 备用路径分隔符
        /// </summary>
        public static char AltPathSeparator => Path.AltDirectorySeparatorChar;

        /// <summary>
        /// 卷分隔符
        /// </summary>
        public static char VolumeSeparator => Path.VolumeSeparatorChar;

        /// <summary>
        /// 路径分隔符字符串
        /// </summary>
        public static string PathSeparatorString => Path.DirectorySeparatorChar.ToString();

        /// <summary>
        /// 非法文件名字符
        /// </summary>
        public static char[] InvalidFileNameChars => Path.GetInvalidFileNameChars();

        /// <summary>
        /// 非法路径字符
        /// </summary>
        public static char[] InvalidPathChars => Path.GetInvalidPathChars();

        #endregion

        #region 环境变量路径

        /// <summary>
        /// 获取PATH环境变量中的所有路径
        /// </summary>
        /// <returns>PATH路径列表</returns>
        public static List<string> GetEnvironmentPaths()
        {
            var pathVar = Environment.GetEnvironmentVariable("PATH") ?? "";
            var paths = new List<string>();

            foreach (var path in pathVar.Split(Path.PathSeparator))
            {
                if (!string.IsNullOrWhiteSpace(path) && Directory.Exists(path))
                {
                    paths.Add(path.Trim());
                }
            }

            return paths;
        }

        /// <summary>
        /// 获取指定环境变量的路径值
        /// </summary>
        /// <param name="variableName">环境变量名</param>
        /// <returns>环境变量路径值</returns>
        public static string GetEnvironmentPath(string variableName)
        {
            return Environment.GetEnvironmentVariable(variableName) ?? "";
        }

        #endregion
    }
}
