# YDirectoryOperations - 企业级目录操作工具类

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🔧 **全功能目录操作解决方案** - 提供完整的目录创建、删除、复制、移动、同步和管理功能

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [项目模板](#-项目模板)
- [高级功能](#-高级功能)
- [WPF 绑定](#-wpf-绑定)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **🔧 基础目录操作**

- ✅ **目录创建**: 单个/批量创建，递归创建，临时目录管理
- ✅ **目录删除**: 安全删除，递归删除，内容清理
- ✅ **目录检查**: 存在性验证，权限检查，状态监控

### **📋 高级目录管理**

- ✅ **目录复制**: 完整复制，增量复制，结构复制
- ✅ **目录移动**: 安全移动，重命名，路径更新
- ✅ **目录同步**: 单向同步，双向同步，增量同步

### **🛤️ 智能路径处理**

- ✅ **路径组合**: 多路径组合，智能分隔符处理
- ✅ **路径规范化**: 格式标准化，分隔符统一
- ✅ **相对路径**: 智能计算，跨平台兼容

### **🎯 项目模板创建**

- ✅ **Web 应用**: Controllers, Views, Models, wwwroot
- ✅ **控制台应用**: Models, Services, Utils, Config
- ✅ **类库项目**: Models, Services, Interfaces, Extensions
- ✅ **桌面应用**: Views, ViewModels, Models, Resources

### **🔄 目录同步功能**

- ✅ **增量同步**: 智能文件比较，时间戳检查
- ✅ **完整同步**: 删除多余文件，保持一致性
- ✅ **统计报告**: 详细的同步结果统计

### **👁️ 可视化功能**

- ✅ **目录树**: 文本格式树形结构展示
- ✅ **深度控制**: 可配置的遍历深度限制
- ✅ **图标支持**: Unicode 图标，无外部依赖

### **🎨 WPF 绑定支持**

- ✅ **数据绑定**: TreeView 层次化数据结构
- ✅ **属性通知**: INotifyPropertyChanged 接口
- ✅ **智能图标**: 文件类型自动识别图标
- ✅ **UI 集成**: 完美支持 MVVM 模式

### **🧹 维护功能**

- ✅ **空目录清理**: 递归清理空目录
- ✅ **结构优化**: 目录结构整理和优化

### **🛡️ 安全特性**

- ✅ **参数验证**: 严格的输入验证
- ✅ **异常处理**: 优雅的错误处理
- ✅ **权限检查**: 文件系统权限验证
- ✅ **跨平台**: Windows、Linux、macOS 兼容

## 🚀 **快速开始**

### **安装**

```csharp
// 通过 NuGet 包管理器安装
Install-Package Zylo.YIO

// 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Core;

var dirOps = new YDirectoryOperations();

// 创建目录
dirOps.CreateDirectory(@"C:\MyProject\src");

// 复制目录
dirOps.CopyDirectory(@"C:\Source", @"C:\Backup", recursive: true);

// 同步目录
var result = dirOps.SynchronizeDirectories(@"C:\Source", @"C:\Target");
Console.WriteLine($"复制: {result.CopiedFiles}, 更新: {result.UpdatedFiles}");

// 生成目录树
var tree = dirOps.GetDirectoryTree(@"C:\MyProject", maxDepth: 3);
Console.WriteLine(tree);
```

## 🔧 **核心功能**

### **目录创建和管理**

```csharp
var dirOps = new YDirectoryOperations();

// 基础目录创建
bool created = dirOps.CreateDirectory(@"C:\NewFolder");

// 递归创建多级目录
bool success = dirOps.CreateDirectoryRecursive(@"C:\Deep\Nested\Structure");

// 批量创建目录结构
int count = dirOps.CreateDirectoryStructure(@"C:\Project", 
    new[] { "src", "docs", "tests", "config" });

// 创建临时目录
string tempDir = dirOps.CreateTemporaryDirectory();
```

### **目录复制和移动**

```csharp
// 完整目录复制
bool copied = dirOps.CopyDirectory(@"C:\Source", @"C:\Destination", 
    recursive: true, overwrite: true);

// 目录移动
bool moved = dirOps.MoveDirectory(@"C:\OldLocation", @"C:\NewLocation");

// 目录重命名
bool renamed = dirOps.RenameDirectory(@"C:\OldName", "NewName");
```

### **路径处理**

```csharp
// 路径组合
string combined = dirOps.CombinePaths("C:", "Projects", "MyApp", "src");

// 路径规范化
string normalized = dirOps.NormalizePath(@"C:/Mixed\Path//Structure");

// 相对路径计算
string relative = dirOps.GetRelativePath(@"C:\Base", @"C:\Base\Sub\Dir");
// 结果: "Sub\Dir"
```

## 🎯 **项目模板**

### **支持的项目类型**

```csharp
// Web 应用项目
dirOps.CreateProjectStructure(@"C:\WebApp", ProjectType.WebApp);
// 创建: Controllers, Views, Models, wwwroot, Data, Services

// 控制台应用项目  
dirOps.CreateProjectStructure(@"C:\ConsoleApp", ProjectType.ConsoleApp);
// 创建: Models, Services, Utils, Config, Data, Logs

// 类库项目
dirOps.CreateProjectStructure(@"C:\Library", ProjectType.Library);
// 创建: Models, Services, Interfaces, Extensions, Utils

// 桌面应用项目
dirOps.CreateProjectStructure(@"C:\DesktopApp", ProjectType.Desktop);
// 创建: Views, ViewModels, Models, Services, Resources, Assets
```

### **自定义项目结构**

```csharp
// 自定义目录结构
var customDirs = new[] { 
    "Core", "Infrastructure", "Application", 
    "Domain", "Presentation", "Tests" 
};
int created = dirOps.CreateDirectoryStructure(@"C:\CleanArchitecture", customDirs);
```

## 🔄 **高级功能**

### **目录同步**

```csharp
// 基础同步
var result = dirOps.SynchronizeDirectories(@"C:\Source", @"C:\Target");

// 完整同步（删除多余文件）
var fullSync = dirOps.SynchronizeDirectories(@"C:\Source", @"C:\Target", 
    deleteExtra: true);

// 检查同步结果
Console.WriteLine($"""
同步完成:
- 复制文件: {result.CopiedFiles}
- 更新文件: {result.UpdatedFiles}  
- 跳过文件: {result.SkippedFiles}
- 删除文件: {result.DeletedFiles}
- 总耗时: {result.ElapsedTime}
""");
```

### **目录维护**

```csharp
// 清理空目录
int cleaned = dirOps.CleanupEmptyDirectories(@"C:\ProjectRoot");
Console.WriteLine($"清理了 {cleaned} 个空目录");

// 目录树可视化
string tree = dirOps.GetDirectoryTree(@"C:\Project", maxDepth: 3);
Console.WriteLine(tree);
/*
输出示例:
📁 Project
  📁 src
    📄 Program.cs
    📄 Config.json
  📁 docs
    📝 README.md
  📚 tests
    🧪 UnitTests.cs
*/
```

## 🎨 **WPF 绑定**

### **TreeView 数据绑定**

```csharp
// 获取 WPF 绑定数据
var treeData = dirOps.GetDirectoryTreeForWPF(@"C:\Project", 
    maxDepth: 3, includeFiles: true);

// 绑定到 TreeView
myTreeView.ItemsSource = treeData;
```

### **XAML 绑定示例**

```xml
<TreeView ItemsSource="{Binding DirectoryNodes}">
    <TreeView.ItemTemplate>
        <HierarchicalDataTemplate ItemsSource="{Binding Children}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding IconPath}" FontSize="16" Margin="0,0,5,0"/>
                <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
            </StackPanel>
        </HierarchicalDataTemplate>
    </TreeView.ItemTemplate>
</TreeView>
```

### **DirectoryTreeNode 属性**

```csharp
public class DirectoryTreeNode : INotifyPropertyChanged
{
    public string Name { get; set; }              // 显示名称
    public string FullPath { get; set; }          // 完整路径
    public bool IsDirectory { get; set; }         // 是否为目录
    public string IconPath { get; set; }          // Unicode 图标
    public List<DirectoryTreeNode> Children { get; set; }  // 子节点
    public bool IsExpanded { get; set; }          // 展开状态
    public bool IsSelected { get; set; }          // 选中状态
    
    // 计算属性
    public string FormattedFileSize { get; }      // 格式化文件大小
    public string FormattedLastModified { get; }  // 格式化修改时间
    public string ToolTip { get; }                // 工具提示
}
```

## 📊 **完整功能函数汇总表格**

### **🔧 基础目录操作方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CreateDirectory` | `string directoryPath` | `bool` | 创建单个目录，如果已存在则返回 true |
| `CreateDirectoryRecursive` | `string directoryPath` | `bool` | 递归创建多级目录结构 |
| `CreateIfNotExists` | `string directoryPath` | `bool` | 仅在目录不存在时创建 |
| `CreateTemporaryDirectory` | 无 | `string` | 创建唯一的临时目录并返回路径 |
| `DirectoryExists` | `string directoryPath` | `bool` | 检查目录是否存在 |
| `DeleteDirectory` | `string directoryPath` | `bool` | 删除空目录 |
| `DeleteDirectoryRecursive` | `string directoryPath` | `bool` | 递归删除目录及其所有内容 |
| `DeleteEmptyDirectory` | `string directoryPath` | `bool` | 仅删除空目录，非空则失败 |
| `DeleteDirectoryContents` | `string directoryPath` | `bool` | 删除目录内容但保留目录本身 |
| `SafeDeleteDirectory` | `string directoryPath` | `bool` | 安全删除，包含错误处理 |

### **📋 高级目录管理方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CopyDirectory` | `string source, string destination, bool recursive, bool overwrite` | `bool` | 复制目录，支持递归和覆盖选项 |
| `MoveDirectory` | `string sourcePath, string destinationPath` | `bool` | 移动目录到新位置 |
| `RenameDirectory` | `string directoryPath, string newName` | `bool` | 重命名目录 |
| `CreateDirectoryStructure` | `string basePath, string[] directoryNames` | `int` | 批量创建目录结构，返回创建数量 |

### **🛤️ 路径处理方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CombinePaths` | `params string[] paths` | `string` | 智能组合多个路径片段 |
| `NormalizePath` | `string path` | `string` | 规范化路径格式，统一分隔符 |
| `GetRelativePath` | `string fromPath, string toPath` | `string` | 计算从一个路径到另一个路径的相对路径 |

### **🎯 项目模板创建方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CreateProjectStructure` | `string projectPath, ProjectType projectType` | `bool` | 根据项目类型创建标准目录结构 |

**支持的项目类型 (ProjectType 枚举):**

- `WebApp` - Web 应用项目 (Controllers, Views, Models, wwwroot, Data, Services)
- `ConsoleApp` - 控制台应用 (Models, Services, Utils, Config, Data, Logs)
- `Library` - 类库项目 (Models, Services, Interfaces, Extensions, Utils)
- `Desktop` - 桌面应用 (Views, ViewModels, Models, Services, Resources, Assets)

### **🔄 目录同步方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `SynchronizeDirectories` | `string sourceDir, string targetDir, bool deleteExtra = false` | `DirectorySyncResult` | 智能目录同步，支持增量和完整同步 |

**DirectorySyncResult 属性:**

- `bool Success` - 同步是否成功
- `int CopiedFiles` - 复制的文件数量
- `int UpdatedFiles` - 更新的文件数量
- `int SkippedFiles` - 跳过的文件数量
- `int DeletedFiles` - 删除的文件数量
- `TimeSpan ElapsedTime` - 同步耗时
- `string ErrorMessage` - 错误信息（如果有）

### **🧹 目录维护方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CleanupEmptyDirectories` | `string directoryPath` | `int` | 递归清理空目录，返回清理数量 |

### **👁️ 可视化方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `GetDirectoryTree` | `string directoryPath, int maxDepth = 5` | `string` | 生成文本格式的目录树结构 |
| `BuildDirectoryTree` | `string path, int currentDepth, int maxDepth, string prefix` | `string` | 递归构建目录树的辅助方法 |

### **🎨 WPF 绑定方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `GetDirectoryTreeForWPF` | `string directoryPath, int maxDepth = 5, bool includeFiles = true` | `List<DirectoryTreeNode>` | 生成适用于 WPF TreeView 绑定的数据结构 |
| `BuildWPFTreeNode` | `DirectoryInfo dirInfo, int currentDepth, int maxDepth, bool includeFiles` | `DirectoryTreeNode` | 构建 WPF 树节点的辅助方法 |
| `GetFileIcon` | `string extension, bool useUnicodeAsFallback = true` | `string` | 根据文件扩展名获取图标 |
| `GetDirectoryIcon` | `string directoryName, bool hasError = false, bool useUnicodeAsFallback = true` | `string` | 根据目录名称获取图标 |

**DirectoryTreeNode 类属性:**

- `string Name` - 显示名称
- `string FullPath` - 完整路径
- `bool IsDirectory` - 是否为目录
- `string IconPath` - 图标路径/Unicode 字符
- `List<DirectoryTreeNode> Children` - 子节点集合
- `bool IsExpanded` - 是否展开
- `bool IsSelected` - 是否选中
- `string FormattedFileSize` - 格式化的文件大小
- `string FormattedLastModified` - 格式化的修改时间
- `string ToolTip` - 工具提示信息

## 🎨 **智能图标系统详解**

### **文件类型图标映射**

| 文件扩展名 | Unicode 图标 | FontAwesome 类名 | 描述 |
|------------|--------------|------------------|------|
| `.cs` | 📄 | `fa-file-code` | C# 代码文件 |
| `.xaml` | 🎨 | `fa-file-code` | XAML 界面文件 |
| `.xml` | 📄 | `fa-file-code` | XML 文档 |
| `.json` | 📄 | `fa-file-code` | JSON 数据文件 |
| `.js` | 📄 | `fa-file-code` | JavaScript 文件 |
| `.ts` | 📄 | `fa-file-code` | TypeScript 文件 |
| `.html` | 🌐 | `fa-file-code` | HTML 网页 |
| `.css` | 🎨 | `fa-file-code` | CSS 样式文件 |
| `.sql` | 🗄️ | `fa-database` | SQL 脚本 |
| `.txt` | 📝 | `fa-file-text` | 文本文件 |
| `.md` | 📝 | `fa-file-text` | Markdown 文档 |
| `.pdf` | 📕 | `fa-file-pdf` | PDF 文档 |
| `.doc/.docx` | 📘 | `fa-file-word` | Word 文档 |
| `.xls/.xlsx` | 📊 | `fa-file-excel` | Excel 表格 |
| `.ppt/.pptx` | 📊 | `fa-file-powerpoint` | PowerPoint 演示 |
| `.jpg/.png/.gif` | 🖼️ | `fa-file-image` | 图片文件 |
| `.mp3/.wav/.aac` | 🎵 | `fa-file-audio` | 音频文件 |
| `.mp4/.avi/.mkv` | 🎬 | `fa-file-video` | 视频文件 |
| `.zip/.rar/.7z` | 📦 | `fa-file-archive` | 压缩文件 |
| `.exe/.msi` | ⚙️ | `fa-cog` | 可执行文件 |
| `.dll/.so` | 🔧 | `fa-cogs` | 动态库文件 |
| `.bat/.cmd/.sh` | ⚡ | `fa-terminal` | 脚本文件 |

### **目录类型图标映射**

| 目录名称 | Unicode 图标 | FontAwesome 类名 | 描述 |
|----------|--------------|------------------|------|
| `src/source` | 📁 | `fa-folder-open` | 源代码目录 |
| `bin/obj` | 🔧 | `fa-cogs` | 编译输出目录 |
| `docs/documentation` | 📚 | `fa-book` | 文档目录 |
| `tests/test` | 🧪 | `fa-flask` | 测试目录 |
| `images/img` | 🖼️ | `fa-images` | 图片目录 |
| `css/styles` | 🎨 | `fa-paint-brush` | 样式目录 |
| `js/javascript` | 📄 | `fa-code` | JavaScript 目录 |
| `config/configuration` | ⚙️ | `fa-cog` | 配置目录 |
| `logs/log` | 📋 | `fa-list-alt` | 日志目录 |
| `temp/tmp` | 🗂️ | `fa-folder` | 临时目录 |
| `backup/bak` | 💾 | `fa-save` | 备份目录 |
| `wwwroot/public` | 🌐 | `fa-globe` | Web 根目录 |
| `controllers` | 🎮 | `fa-gamepad` | 控制器目录 |
| `models` | 📊 | `fa-cubes` | 模型目录 |
| `views` | 👁️ | `fa-eye` | 视图目录 |
| `services` | 🔧 | `fa-cogs` | 服务目录 |
| `data` | 🗄️ | `fa-database` | 数据目录 |
| `assets/resources` | 📦 | `fa-archive` | 资源目录 |
| `lib/libraries` | 📚 | `fa-book` | 库目录 |
| `tools/utils` | 🔧 | `fa-wrench` | 工具目录 |
| `默认目录` | 📁 | `fa-folder` | 普通目录 |
