using System;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Linq;
using System.Threading;

namespace Zylo.YIO.Core
{
    /// <summary>
    /// 智能命名策略枚举
    /// </summary>
    public enum SmartNamingStrategy
    {
        /// <summary>基于文件内容的哈希值</summary>
        ContentHash,
        /// <summary>基于文件类型</summary>
        FileType,
        /// <summary>基于文件时间戳</summary>
        DateTime,
        /// <summary>基于文件大小</summary>
        Size,
        /// <summary>顺序编号</summary>
        Sequential
    }

    /// <summary>
    /// 大小写样式枚举
    /// </summary>
    public enum CaseStyle
    {
        /// <summary>小写</summary>
        LowerCase,
        /// <summary>大写</summary>
        UpperCase,
        /// <summary>标题格式（每个单词首字母大写）</summary>
        TitleCase,
        /// <summary>驼峰格式（首字母小写）</summary>
        CamelCase,
        /// <summary>帕斯卡格式（首字母大写）</summary>
        PascalCase,
        /// <summary>短横线格式（kebab-case）</summary>
        KebabCase,
        /// <summary>下划线格式（snake_case）</summary>
        SnakeCase
    }

    /// <summary>
    /// YFile - 企业级文件操作工具类
    ///
    /// 🚀 核心功能特性：
    /// • 基础文件操作：创建、读取、写入、复制、移动、删除
    /// • 文件名处理：快速添加/移除前缀后缀，批量重命名
    /// • 文件属性管理：只读、隐藏、系统、存档属性的设置和修改
    /// • 异步操作支持：现代应用的基础需求，提升性能
    /// • 流式操作：大文件处理，避免内存溢出
    /// • 文件锁定：并发控制和安全访问
    /// • 临时文件管理：避免资源泄漏，自动清理
    /// • 备份和版本控制：数据安全保障，时间戳备份
    /// • 完整性验证：SHA256哈希计算和校验和管理
    /// • 路径处理：文件名规范化、唯一性生成、类型组织
    ///
    /// 💡 设计原则：
    /// • 安全性：严格的参数验证和权限检查
    /// • 性能：优化的缓冲区和流式处理
    /// • 易用性：简洁的API和丰富的重载方法
    /// • 可靠性：完善的异常处理和恢复机制
    /// • 扩展性：支持批量操作和递归处理
    ///
    /// 📋 方法分组：
    /// • 基础操作：文件创建、读写、复制、移动、删除
    /// • 文件名处理：前缀后缀添加/移除、批量重命名
    /// • 属性管理：文件属性和时间戳的设置修改
    /// • 高级功能：异步操作、流式处理、文件锁定
    /// • 企业功能：临时文件、备份版本、完整性验证
    /// </summary>

    [YStatic]
    public partial class YFile
    {
        // ==========================================
        // 🔧 基础文件操作 - 核心功能
        // ==========================================

        #region 1. 文件创建和存在性检查

        /// <summary>
        /// 创建新文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>创建成功返回true，否则返回false</returns>
        public bool CreateFile(string filePath, bool overwrite = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (File.Exists(filePath) && !overwrite)
                    return false;

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 创建文件
                using (File.Create(filePath))
                {
                }
                return true;
            }
            catch (Exception ex)
            {
                // 这里可以添加日志记录
                Console.WriteLine($"创建文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件存在返回true，否则返回false</returns>
        public bool FileExists(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            return File.Exists(filePath);
        }

        /// <summary>
        /// 条件创建文件（仅当文件不存在时创建）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>创建成功或文件已存在返回true</returns>
        public bool CreateFileIfNotExists(string filePath)
        {
            if (FileExists(filePath))
                return true;

            return CreateFile(filePath, false);
        }

        /// <summary>
        /// 更新文件访问时间或创建文件（类似Unix的touch命令）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作成功返回true</returns>
        public bool TouchFile(string filePath)
        {
            try
            {
                if (FileExists(filePath))
                {
                    File.SetLastAccessTime(filePath, DateTime.Now);
                    File.SetLastWriteTime(filePath, DateTime.Now);
                }
                else
                {
                    return CreateFile(filePath);
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Touch文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成唯一文件名（在指定目录中不重复）
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="baseName">基础文件名</param>
        /// <param name="extension">文件扩展名</param>
        /// <returns>唯一的文件名</returns>
        public string GetUniqueFileName(string directory, string baseName, string extension)
        {
            if (string.IsNullOrWhiteSpace(directory))
                directory = Directory.GetCurrentDirectory();

            if (string.IsNullOrWhiteSpace(baseName))
                baseName = "file";

            if (!extension.StartsWith("."))
                extension = "." + extension;

            var fileName = baseName + extension;
            var fullPath = Path.Combine(directory, fileName);

            if (!File.Exists(fullPath))
                return fileName;

            // 生成带数字后缀的唯一文件名
            int counter = 1;
            do
            {
                fileName = $"{baseName}_{counter}{extension}";
                fullPath = Path.Combine(directory, fileName);
                counter++;
            } while (File.Exists(fullPath));

            return fileName;
        }

        #endregion

        #region 2. 文件读取操作

        /// <summary>
        /// 读取文件全部文本内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="encoding">文本编码，null时自动检测</param>
        /// <returns>文件内容</returns>
        public string ReadAllText(string filePath, Encoding? encoding = null)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                if (encoding == null)
                    encoding = DetectEncoding(filePath);

                return File.ReadAllText(filePath, encoding);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 读取文件全部字节内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件字节数组</returns>
        public byte[] ReadAllBytes(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                return File.ReadAllBytes(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件字节失败: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// 按行读取文件到数组
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="encoding">文本编码</param>
        /// <returns>文件行数组</returns>
        public string[] ReadAllLines(string filePath, Encoding? encoding = null)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                if (encoding == null)
                    encoding = DetectEncoding(filePath);

                return File.ReadAllLines(filePath, encoding);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按行读取文件失败: {ex.Message}");
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 流式按行读取文件（节省内存，适合大文件）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="encoding">文本编码</param>
        /// <returns>行的枚举器</returns>
        public IEnumerable<string> ReadLines(string filePath, Encoding? encoding = null)
        {
            if (!FileExists(filePath))
                yield break;

            if (encoding == null)
                encoding = DetectEncoding(filePath);

            using var reader = new StreamReader(filePath, encoding);
            string? line;
            while ((line = reader.ReadLine()) != null)
            {
                yield return line;
            }
        }

        #endregion

        #region 3. 文件写入操作

        /// <summary>
        /// 写入文本内容到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">要写入的内容</param>
        /// <param name="encoding">文本编码，null时使用UTF-8</param>
        /// <param name="append">是否追加模式</param>
        /// <returns>写入成功返回true</returns>
        public bool WriteAllText(string filePath, string content, Encoding? encoding = null, bool append = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (content == null)
                    content = string.Empty;

                encoding ??= Encoding.UTF8;

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                if (append)
                {
                    File.AppendAllText(filePath, content, encoding);
                }
                else
                {
                    File.WriteAllText(filePath, content, encoding);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 写入字节数组到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="bytes">要写入的字节数组</param>
        /// <param name="append">是否追加模式</param>
        /// <returns>写入成功返回true</returns>
        public bool WriteAllBytes(string filePath, byte[] bytes, bool append = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (bytes == null)
                    throw new ArgumentNullException(nameof(bytes));

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                if (append)
                {
                    using var stream = new FileStream(filePath, FileMode.Append, FileAccess.Write);
                    stream.Write(bytes, 0, bytes.Length);
                }
                else
                {
                    File.WriteAllBytes(filePath, bytes);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入文件字节失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 写入行数组到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="lines">要写入的行数组</param>
        /// <param name="encoding">文本编码</param>
        /// <param name="append">是否追加模式</param>
        /// <returns>写入成功返回true</returns>
        public bool WriteAllLines(string filePath, string[] lines, Encoding? encoding = null, bool append = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (lines == null)
                    throw new ArgumentNullException(nameof(lines));

                encoding ??= Encoding.UTF8;

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                if (append)
                {
                    using var writer = new StreamWriter(filePath, true, encoding);
                    foreach (var line in lines)
                    {
                        writer.WriteLine(line);
                    }
                }
                else
                {
                    File.WriteAllLines(filePath, lines, encoding);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"按行写入文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 4. 文件复制和移动操作

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>复制成功返回true</returns>
        public bool CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sourceFilePath))
                    throw new ArgumentException("源文件路径不能为空", nameof(sourceFilePath));

                if (string.IsNullOrWhiteSpace(destinationFilePath))
                    throw new ArgumentException("目标文件路径不能为空", nameof(destinationFilePath));

                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException($"源文件不存在: {sourceFilePath}");

                // 确保目标目录存在
                var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
                if (!string.IsNullOrEmpty(destinationDirectory) && !Directory.Exists(destinationDirectory))
                {
                    Directory.CreateDirectory(destinationDirectory);
                }

                File.Copy(sourceFilePath, destinationFilePath, overwrite);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"复制文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>移动成功返回true</returns>
        public bool MoveFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sourceFilePath))
                    throw new ArgumentException("源文件路径不能为空", nameof(sourceFilePath));

                if (string.IsNullOrWhiteSpace(destinationFilePath))
                    throw new ArgumentException("目标文件路径不能为空", nameof(destinationFilePath));

                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException($"源文件不存在: {sourceFilePath}");

                // 确保目标目录存在
                var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
                if (!string.IsNullOrEmpty(destinationDirectory) && !Directory.Exists(destinationDirectory))
                {
                    Directory.CreateDirectory(destinationDirectory);
                }

                // 如果目标文件存在且不允许覆盖，返回失败
                if (File.Exists(destinationFilePath) && !overwrite)
                    return false;

                // 如果目标文件存在且允许覆盖，先删除
                if (File.Exists(destinationFilePath) && overwrite)
                {
                    File.Delete(destinationFilePath);
                }

                File.Move(sourceFilePath, destinationFilePath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移动文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 5. 文件删除操作

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="force">是否强制删除（忽略只读属性）</param>
        /// <returns>删除成功返回true</returns>
        public bool DeleteFile(string filePath, bool force = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    return true; // 文件不存在，视为删除成功

                // 如果是只读文件且需要强制删除
                if (force)
                {
                    var attributes = File.GetAttributes(filePath);
                    if ((attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
                    {
                        File.SetAttributes(filePath, attributes & ~FileAttributes.ReadOnly);
                    }
                }

                File.Delete(filePath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 6. 文件属性和信息

        /// <summary>
        /// 获取文件大小（字节）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小，文件不存在返回-1</returns>
        public long GetFileSize(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return -1;

                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件大小失败: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取文件创建时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>创建时间，文件不存在返回DateTime.MinValue</returns>
        public DateTime GetCreationTime(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return DateTime.MinValue;

                return File.GetCreationTime(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件创建时间失败: {ex.Message}");
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// 获取文件最后写入时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>最后写入时间，文件不存在返回DateTime.MinValue</returns>
        public DateTime GetLastWriteTime(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return DateTime.MinValue;

                return File.GetLastWriteTime(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件最后写入时间失败: {ex.Message}");
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// 获取文件最后访问时间
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>最后访问时间，文件不存在返回DateTime.MinValue</returns>
        public DateTime GetLastAccessTime(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return DateTime.MinValue;

                return File.GetLastAccessTime(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件最后访问时间失败: {ex.Message}");
                return DateTime.MinValue;
            }
        }

        /// <summary>
        /// 获取文件属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件属性，文件不存在返回None</returns>
        public FileAttributes GetFileAttributes(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return FileAttributes.Normal;

                return File.GetAttributes(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件属性失败: {ex.Message}");
                return FileAttributes.Normal;
            }
        }

        /// <summary>
        /// 设置文件属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="attributes">要设置的属性</param>
        /// <returns>设置成功返回true</returns>
        public bool SetFileAttributes(string filePath, FileAttributes attributes)
        {
            try
            {
                if (!FileExists(filePath))
                    return false;

                File.SetAttributes(filePath, attributes);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置文件属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件扩展名（包含点号）</returns>
        public string GetFileExtension(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return string.Empty;

                return Path.GetExtension(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件扩展名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取不带扩展名的文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>不带扩展名的文件名</returns>
        public string GetFileNameWithoutExtension(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return string.Empty;

                return Path.GetFileNameWithoutExtension(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查文件是否为只读
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是只读文件返回true</returns>
        public bool IsReadOnly(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return false;

                var attributes = File.GetAttributes(filePath);
                return (attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查只读属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否为隐藏文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是隐藏文件返回true</returns>
        public bool IsHidden(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return false;

                var attributes = File.GetAttributes(filePath);
                return (attributes & FileAttributes.Hidden) == FileAttributes.Hidden;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查隐藏属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否为系统文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是系统文件返回true</returns>
        public bool IsSystemFile(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    return false;

                var attributes = File.GetAttributes(filePath);
                return (attributes & FileAttributes.System) == FileAttributes.System;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查系统文件属性失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        // ==========================================
        // 🔧 辅助功能 - 工具和扩展
        // ==========================================

        #region 14. 私有辅助方法

        /// <summary>
        /// 检测文件编码
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>检测到的编码</returns>
        public Encoding DetectEncoding(string filePath)
        {
            try
            {
                // 读取文件前几个字节检测BOM
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var bom = new byte[4];
                var bytesRead = stream.Read(bom, 0, 4);

                // 检测UTF-8 BOM
                if (bytesRead >= 3 && bom[0] == 0xEF && bom[1] == 0xBB && bom[2] == 0xBF)
                    return Encoding.UTF8;

                // 检测UTF-16 LE BOM
                if (bytesRead >= 2 && bom[0] == 0xFF && bom[1] == 0xFE)
                    return Encoding.Unicode;

                // 检测UTF-16 BE BOM
                if (bytesRead >= 2 && bom[0] == 0xFE && bom[1] == 0xFF)
                    return Encoding.BigEndianUnicode;

                // 检测UTF-32 LE BOM
                if (bytesRead >= 4 && bom[0] == 0xFF && bom[1] == 0xFE && bom[2] == 0x00 && bom[3] == 0x00)
                    return Encoding.UTF32;

                // 默认返回UTF-8
                return Encoding.UTF8;
            }
            catch
            {
                return Encoding.UTF8;
            }
        }

        #endregion

        #region 15. 文件名快速处理功能

        /// <summary>
        /// 为文件名添加前缀
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="prefix">要添加的前缀</param>
        /// <param name="separator">前缀与原文件名之间的分隔符，默认为空</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        /// <remarks>
        /// 此方法会实际重命名文件，不仅仅是返回新的文件名
        /// 如果目标文件已存在，会自动生成唯一的文件名
        /// </remarks>
        public string AddPrefixToFileName(string filePath, string prefix, string separator = "")
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(prefix))
                    throw new ArgumentException("前缀不能为空", nameof(prefix));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 构建新文件名：前缀 + 分隔符 + 原文件名 + 扩展名
                var newFileName = $"{prefix}{separator}{fileName}{extension}";
                var newFilePath = Path.Combine(directory, newFileName);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加文件名前缀失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 为文件名添加后缀
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="suffix">要添加的后缀</param>
        /// <param name="separator">原文件名与后缀之间的分隔符，默认为空</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        /// <remarks>
        /// 此方法会实际重命名文件，后缀添加在文件名和扩展名之间
        /// 例如：file.txt + "_backup" = file_backup.txt
        /// </remarks>
        public string AddSuffixToFileName(string filePath, string suffix, string separator = "")
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(suffix))
                    throw new ArgumentException("后缀不能为空", nameof(suffix));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 构建新文件名：原文件名 + 分隔符 + 后缀 + 扩展名
                var newFileName = $"{fileName}{separator}{suffix}{extension}";
                var newFilePath = Path.Combine(directory, newFileName);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"添加文件名后缀失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 批量为目录中的文件名添加前缀
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="prefix">要添加的前缀</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="separator">前缀与原文件名之间的分隔符，默认为空</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        public int AddPrefixToFileNames(string directoryPath, string prefix, string searchPattern = "*.*",
            string separator = "", bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                if (string.IsNullOrWhiteSpace(prefix))
                    throw new ArgumentException("前缀不能为空", nameof(prefix));

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        var result = AddPrefixToFileName(file, prefix, separator);
                        if (!string.IsNullOrEmpty(result))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量添加文件名前缀失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 批量为目录中的文件名添加后缀
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="suffix">要添加的后缀</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="separator">原文件名与后缀之间的分隔符，默认为空</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        public int AddSuffixToFileNames(string directoryPath, string suffix, string searchPattern = "*.*",
            string separator = "", bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                if (string.IsNullOrWhiteSpace(suffix))
                    throw new ArgumentException("后缀不能为空", nameof(suffix));

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        var result = AddSuffixToFileName(file, suffix, separator);
                        if (!string.IsNullOrEmpty(result))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量添加文件名后缀失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 从文件名中移除指定的前缀
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="prefix">要移除的前缀</param>
        /// <param name="separator">前缀与文件名之间的分隔符，默认为空</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <returns>操作成功返回新文件路径，失败或前缀不匹配返回空字符串</returns>
        /// <remarks>
        /// 只有当文件名确实以指定前缀开头时才会执行重命名操作
        /// 支持大小写敏感和不敏感的匹配模式
        /// </remarks>
        public string RemovePrefixFromFileName(string filePath, string prefix, string separator = "", bool ignoreCase = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(prefix))
                    throw new ArgumentException("前缀不能为空", nameof(prefix));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 构建要查找的前缀模式
                var prefixPattern = prefix + separator;

                // 检查文件名是否以指定前缀开头
                var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
                if (!fileName.StartsWith(prefixPattern, comparison))
                {
                    // 前缀不匹配，返回空字符串表示未处理
                    return string.Empty;
                }

                // 移除前缀，构建新文件名
                var newFileName = fileName.Substring(prefixPattern.Length);

                // 如果移除前缀后文件名为空，则不执行操作
                if (string.IsNullOrWhiteSpace(newFileName))
                {
                    Console.WriteLine("移除前缀后文件名为空，操作取消");
                    return string.Empty;
                }

                var newFileNameWithExt = newFileName + extension;
                var newFilePath = Path.Combine(directory, newFileNameWithExt);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移除文件名前缀失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 从文件名中移除指定的后缀
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="suffix">要移除的后缀</param>
        /// <param name="separator">文件名与后缀之间的分隔符，默认为空</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <returns>操作成功返回新文件路径，失败或后缀不匹配返回空字符串</returns>
        /// <remarks>
        /// 只有当文件名确实以指定后缀结尾时才会执行重命名操作
        /// 后缀检查在文件扩展名之前进行
        /// </remarks>
        public string RemoveSuffixFromFileName(string filePath, string suffix, string separator = "", bool ignoreCase = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(suffix))
                    throw new ArgumentException("后缀不能为空", nameof(suffix));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 构建要查找的后缀模式
                var suffixPattern = separator + suffix;

                // 检查文件名是否以指定后缀结尾
                var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
                if (!fileName.EndsWith(suffixPattern, comparison))
                {
                    // 后缀不匹配，返回空字符串表示未处理
                    return string.Empty;
                }

                // 移除后缀，构建新文件名
                var newFileName = fileName.Substring(0, fileName.Length - suffixPattern.Length);

                // 如果移除后缀后文件名为空，则不执行操作
                if (string.IsNullOrWhiteSpace(newFileName))
                {
                    Console.WriteLine("移除后缀后文件名为空，操作取消");
                    return string.Empty;
                }

                var newFileNameWithExt = newFileName + extension;
                var newFilePath = Path.Combine(directory, newFileNameWithExt);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"移除文件名后缀失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 批量移除目录中文件名的前缀
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="prefix">要移除的前缀</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="separator">前缀与文件名之间的分隔符，默认为空</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        public int RemovePrefixFromFileNames(string directoryPath, string prefix, string searchPattern = "*.*",
            string separator = "", bool ignoreCase = false, bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                if (string.IsNullOrWhiteSpace(prefix))
                    throw new ArgumentException("前缀不能为空", nameof(prefix));

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        var result = RemovePrefixFromFileName(file, prefix, separator, ignoreCase);
                        if (!string.IsNullOrEmpty(result))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量移除文件名前缀失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 批量移除目录中文件名的后缀
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="suffix">要移除的后缀</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="separator">文件名与后缀之间的分隔符，默认为空</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        public int RemoveSuffixFromFileNames(string directoryPath, string suffix, string searchPattern = "*.*",
            string separator = "", bool ignoreCase = false, bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                if (string.IsNullOrWhiteSpace(suffix))
                    throw new ArgumentException("后缀不能为空", nameof(suffix));

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        var result = RemoveSuffixFromFileName(file, suffix, separator, ignoreCase);
                        if (!string.IsNullOrEmpty(result))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量移除文件名后缀失败: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region 16. 路径和文件名处理扩展

        /// <summary>
        /// 批量重命名文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="searchPattern">搜索模式</param>
        /// <param name="renamePattern">重命名模式 (支持 {name}, {ext}, {index}, {date} 占位符)</param>
        /// <param name="startIndex">起始索引</param>
        /// <returns>重命名的文件数量</returns>
        /// <remarks>
        /// 支持的占位符：
        /// • {name} - 原文件名（不含扩展名）
        /// • {ext} - 文件扩展名（不含点号）
        /// • {index} - 序号（3位数字，如001、002）
        /// • {date} - 当前日期（yyyyMMdd格式）
        /// 示例："{date}_{name}_{index}" 会生成 "20231201_document_001.txt"
        /// </remarks>
        public int BatchRenameFiles(string directoryPath, string searchPattern, string renamePattern, int startIndex = 1)
        {
            try
            {
                // 参数验证：检查目录路径是否有效
                if (string.IsNullOrWhiteSpace(directoryPath) || !Directory.Exists(directoryPath))
                    throw new ArgumentException("目录不存在", nameof(directoryPath));

                // 获取符合搜索模式的所有文件
                var files = Directory.GetFiles(directoryPath, searchPattern);
                var renamedCount = 0;

                // 生成当前日期字符串，用于 {date} 占位符
                var currentDate = DateTime.Now.ToString("yyyyMMdd");

                // 遍历所有文件进行重命名
                for (int i = 0; i < files.Length; i++)
                {
                    var oldPath = files[i];

                    // 解析原文件的各个组成部分
                    var oldName = Path.GetFileNameWithoutExtension(oldPath);  // 获取不含扩展名的文件名
                    var extension = Path.GetExtension(oldPath);               // 获取文件扩展名
                    var currentIndex = startIndex + i;                        // 计算当前文件的序号

                    // 替换重命名模式中的所有占位符
                    var newName = renamePattern
                       .Replace("{name}", oldName)                           // 替换原文件名
                       .Replace("{ext}", extension.TrimStart('.'))           // 替换扩展名（移除点号）
                       .Replace("{index}", currentIndex.ToString("D3"))     // 替换序号（3位数字格式）
                       .Replace("{date}", currentDate);                     // 替换日期

                    // 构建新的完整文件路径
                    var newPath = Path.Combine(directoryPath, newName + extension);

                    // 检查是否需要重命名且目标文件不存在
                    if (oldPath != newPath && !File.Exists(newPath))
                    {
                        File.Move(oldPath, newPath);  // 执行重命名操作
                        renamedCount++;               // 增加成功计数
                    }
                    // 如果目标文件已存在，跳过此文件（可以考虑添加日志记录）
                }

                return renamedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量重命名失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 规范化文件名（移除非法字符）
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="replacement">替换字符</param>
        /// <returns>规范化后的文件名</returns>
        public string NormalizeFileName(string fileName, string replacement = "_")
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "";

            var invalidChars = Path.GetInvalidFileNameChars();
            var normalizedName = fileName;

            foreach (var invalidChar in invalidChars)
            {
                normalizedName = normalizedName.Replace(invalidChar.ToString(), replacement);
            }

            // 移除多余的空格和点
            normalizedName = normalizedName.Trim().Trim('.');

            // 限制长度
            if (normalizedName.Length > 200)
            {
                var extension = Path.GetExtension(normalizedName);
                var nameWithoutExt = Path.GetFileNameWithoutExtension(normalizedName);
                normalizedName = nameWithoutExt[..(200 - extension.Length)] + extension;
            }

            return normalizedName;
        }

        /// <summary>
        /// 生成唯一文件名（如果文件已存在）
        /// </summary>
        /// <param name="filePath">原始文件路径</param>
        /// <returns>唯一的文件路径</returns>
        public string GenerateUniqueFileName(string filePath)
        {
            if (!File.Exists(filePath))
                return filePath;

            var directory = Path.GetDirectoryName(filePath) ?? "";
            var nameWithoutExt = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);

            var counter = 1;
            string newPath;

            do
            {
                var newName = $"{nameWithoutExt} ({counter}){extension}";
                newPath = Path.Combine(directory, newName);
                counter++;
            } while (File.Exists(newPath));

            return newPath;
        }

        /// <summary>
        /// 根据文件内容生成建议的文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>建议的文件名</returns>
        public string SuggestFileName(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return Path.GetFileName(filePath);

                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileInfo = new FileInfo(filePath);
                var dateStr = fileInfo.CreationTime.ToString("yyyyMMdd");

                return extension switch
                {
                    ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" => $"Image_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}",
                    ".mp4" or ".avi" or ".mov" or ".wmv" => $"Video_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}",
                    ".mp3" or ".wav" or ".flac" => $"Audio_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}",
                    ".pdf" => $"Document_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}",
                    ".txt" => $"Text_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}",
                    _ => $"File_{dateStr}_{Guid.NewGuid().ToString("N")[..8]}{extension}"
                };
            }
            catch
            {
                return Path.GetFileName(filePath);
            }
        }

        /// <summary>
        /// 按文件类型组织文件到子目录
        /// </summary>
        /// <param name="sourceDirectory">源目录</param>
        /// <param name="createSubfolders">是否创建子文件夹</param>
        /// <returns>组织的文件数量</returns>
        /// <remarks>
        /// 文件类型分类规则：
        /// • Images: 图片文件（jpg, png, gif, bmp等）
        /// • Videos: 视频文件（mp4, avi, mov等）
        /// • Audio: 音频文件（mp3, wav, flac等）
        /// • Documents: 文档文件（pdf, doc, txt等）
        /// • Archives: 压缩文件（zip, rar, 7z等）
        /// • Code: 代码文件（cs, js, html等）
        /// • Others: 其他未分类文件
        /// </remarks>
        public int OrganizeFilesByType(string sourceDirectory, bool createSubfolders = true)
        {
            try
            {
                // 验证源目录是否存在
                if (!Directory.Exists(sourceDirectory))
                    return 0;

                // 获取源目录中的所有文件
                var files = Directory.GetFiles(sourceDirectory);
                var organizedCount = 0;

                // 定义文件类型映射表：文件夹名 -> 支持的扩展名列表
                var typeMapping = new Dictionary<string, string>
                {
                    {
                        "Images", ".jpg,.jpeg,.png,.gif,.bmp,.tiff,.webp"      // 图片文件类型
                    },
                    {
                        "Videos", ".mp4,.avi,.mov,.wmv,.flv,.mkv"             // 视频文件类型
                    },
                    {
                        "Audio", ".mp3,.wav,.flac,.aac,.ogg"                  // 音频文件类型
                    },
                    {
                        "Documents", ".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"  // 文档文件类型
                    },
                    {
                        "Archives", ".zip,.rar,.7z,.tar,.gz"                  // 压缩文件类型
                    },
                    {
                        "Code", ".cs,.js,.html,.css,.cpp,.java,.py"           // 代码文件类型
                    }
                };

                // 遍历每个文件进行分类处理
                foreach (var file in files)
                {
                    // 获取文件扩展名并转换为小写（用于不区分大小写的匹配）
                    var extension = Path.GetExtension(file).ToLowerInvariant();
                    var targetFolder = "Others";  // 默认分类为"其他"

                    // 在类型映射表中查找匹配的文件类型
                    foreach (var mapping in typeMapping)
                    {
                        // 检查当前扩展名是否在该类型的扩展名列表中
                        if (mapping.Value.Contains(extension))
                        {
                            targetFolder = mapping.Key;  // 找到匹配的类型，设置目标文件夹
                            break;                        // 找到匹配后立即退出循环
                        }
                    }

                    // 如果启用了子文件夹创建功能
                    if (createSubfolders)
                    {
                        // 构建目标目录路径
                        var targetDirectory = Path.Combine(sourceDirectory, targetFolder);

                        // 确保目标目录存在（如果不存在则创建）
                        Directory.CreateDirectory(targetDirectory);

                        // 构建目标文件的完整路径
                        var targetPath = Path.Combine(targetDirectory, Path.GetFileName(file));

                        // 如果目标文件已存在，生成唯一的文件名
                        targetPath = GenerateUniqueFileName(targetPath);

                        // 移动文件到目标位置
                        File.Move(file, targetPath);
                        organizedCount++;  // 增加成功处理的文件计数
                    }
                }

                return organizedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件组织失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 清理文件名中的特殊字符和空格
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="recursive">是否递归处理</param>
        /// <returns>清理的文件数量</returns>
        public int CleanupFileNames(string directoryPath, bool recursive = false)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                    return 0;

                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, "*", searchOption);
                var cleanedCount = 0;

                foreach (var file in files)
                {
                    var directory = Path.GetDirectoryName(file) ?? "";
                    var originalName = Path.GetFileName(file);
                    var cleanedName = NormalizeFileName(originalName);

                    if (originalName != cleanedName)
                    {
                        var newPath = Path.Combine(directory, cleanedName);
                        newPath = GenerateUniqueFileName(newPath);

                        File.Move(file, newPath);
                        cleanedCount++;
                    }
                }

                return cleanedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件名清理失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 在文件名中查找并替换指定字符串
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="searchText">要查找的文本</param>
        /// <param name="replaceText">替换的文本</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <param name="wholeWord">是否只匹配完整单词，默认false</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        /// <remarks>
        /// 支持在文件名中进行精确的文本查找和替换
        /// 可以选择大小写敏感或不敏感的匹配模式
        /// 支持完整单词匹配，避免部分匹配的误替换
        /// </remarks>
        public string FindAndReplaceInFileName(string filePath, string searchText, string replaceText,
            bool ignoreCase = false, bool wholeWord = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(searchText))
                    throw new ArgumentException("查找文本不能为空", nameof(searchText));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 执行查找替换操作
                var newFileName = fileName;

                if (wholeWord)
                {
                    // 完整单词匹配：使用单词边界
                    var pattern = $@"\b{Regex.Escape(searchText)}\b";
                    var options = ignoreCase ? RegexOptions.IgnoreCase : RegexOptions.None;
                    newFileName = Regex.Replace(fileName, pattern, replaceText ?? "", options);
                }
                else
                {
                    // 普通字符串替换
                    var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

                    // 检查是否包含要查找的文本
                    if (fileName.IndexOf(searchText, comparison) == -1)
                    {
                        // 没有找到匹配的文本，返回空字符串表示未处理
                        return string.Empty;
                    }

                    // 执行替换（处理大小写不敏感的情况）
                    if (ignoreCase)
                    {
                        var regex = new Regex(Regex.Escape(searchText), RegexOptions.IgnoreCase);
                        newFileName = regex.Replace(fileName, replaceText ?? "");
                    }
                    else
                    {
                        newFileName = fileName.Replace(searchText, replaceText ?? "");
                    }
                }

                // 如果文件名没有变化，返回空字符串
                if (newFileName == fileName)
                {
                    return string.Empty;
                }

                // 验证新文件名的有效性
                newFileName = NormalizeFileName(newFileName + extension);
                if (string.IsNullOrWhiteSpace(Path.GetFileNameWithoutExtension(newFileName)))
                {
                    Console.WriteLine("替换后的文件名无效，操作取消");
                    return string.Empty;
                }

                // 构建新的文件路径
                var newFilePath = Path.Combine(directory, newFileName);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件名查找替换失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 使用正则表达式重命名文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="pattern">正则表达式模式</param>
        /// <param name="replacement">替换模式（支持捕获组引用如$1, $2等）</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        /// <remarks>
        /// 支持强大的正则表达式模式匹配和替换
        /// 可以使用捕获组进行复杂的文件名重组
        /// 示例：pattern="(\d+)_(.+)" replacement="$2_$1" 可以交换数字和文本的位置
        /// </remarks>
        public string RenameWithRegex(string filePath, string pattern, string replacement, bool ignoreCase = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (string.IsNullOrWhiteSpace(pattern))
                    throw new ArgumentException("正则表达式模式不能为空", nameof(pattern));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 设置正则表达式选项
                var options = RegexOptions.None;
                if (ignoreCase)
                    options |= RegexOptions.IgnoreCase;

                // 执行正则表达式替换
                var regex = new Regex(pattern, options);
                var newFileName = regex.Replace(fileName, replacement ?? "");

                // 如果文件名没有变化，返回空字符串
                if (newFileName == fileName)
                {
                    return string.Empty;
                }

                // 验证新文件名的有效性
                newFileName = NormalizeFileName(newFileName + extension);
                if (string.IsNullOrWhiteSpace(Path.GetFileNameWithoutExtension(newFileName)))
                {
                    Console.WriteLine("正则替换后的文件名无效，操作取消");
                    return string.Empty;
                }

                // 构建新的文件路径
                var newFilePath = Path.Combine(directory, newFileName);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (RegexMatchTimeoutException ex)
            {
                Console.WriteLine($"正则表达式超时: {ex.Message}");
                return string.Empty;
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"正则表达式无效: {ex.Message}");
                return string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"正则表达式重命名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 批量查找替换文件名
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="searchText">要查找的文本</param>
        /// <param name="replaceText">替换的文本</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="ignoreCase">是否忽略大小写，默认false</param>
        /// <param name="wholeWord">是否只匹配完整单词，默认false</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        public int BatchFindAndReplaceInFileNames(string directoryPath, string searchText, string replaceText,
            string searchPattern = "*.*", bool ignoreCase = false, bool wholeWord = false, bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                if (string.IsNullOrWhiteSpace(searchText))
                    throw new ArgumentException("查找文本不能为空", nameof(searchText));

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        var result = FindAndReplaceInFileName(file, searchText, replaceText, ignoreCase, wholeWord);
                        if (!string.IsNullOrEmpty(result))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量查找替换失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 智能重命名文件（基于文件内容或元数据）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="namingStrategy">命名策略</param>
        /// <param name="includeDate">是否包含日期</param>
        /// <param name="includeSize">是否包含文件大小</param>
        /// <param name="customPrefix">自定义前缀</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        /// <remarks>
        /// 支持多种智能命名策略：
        /// • ContentHash: 基于文件内容的哈希值
        /// • FileType: 基于文件类型
        /// • DateTime: 基于文件时间戳
        /// • Size: 基于文件大小
        /// • Sequential: 顺序编号
        /// </remarks>
        public string SmartRename(string filePath, SmartNamingStrategy namingStrategy = SmartNamingStrategy.FileType,
            bool includeDate = true, bool includeSize = false, string customPrefix = "")
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取文件信息
                var fileInfo = new FileInfo(filePath);
                var directory = fileInfo.DirectoryName ?? "";
                var extension = fileInfo.Extension;

                // 构建新文件名
                var newFileName = "";

                // 添加自定义前缀
                if (!string.IsNullOrWhiteSpace(customPrefix))
                {
                    newFileName += customPrefix + "_";
                }

                // 根据命名策略生成文件名
                switch (namingStrategy)
                {
                    case SmartNamingStrategy.ContentHash:
                        var hash = CalculateFileHash(filePath);
                        newFileName += $"Hash_{hash[..8]}";
                        break;

                    case SmartNamingStrategy.FileType:
                        var typePrefix = GetFileTypePrefix(extension);
                        newFileName += typePrefix;
                        break;

                    case SmartNamingStrategy.DateTime:
                        var dateTime = fileInfo.CreationTime.ToString("yyyyMMdd_HHmmss");
                        newFileName += $"File_{dateTime}";
                        break;

                    case SmartNamingStrategy.Size:
                        var sizeCategory = GetSizeCategory(fileInfo.Length);
                        newFileName += $"{sizeCategory}_File";
                        break;

                    case SmartNamingStrategy.Sequential:
                        var counter = GetNextSequentialNumber(directory, extension);
                        newFileName += $"File_{counter:D4}";
                        break;

                    default:
                        newFileName += "File";
                        break;
                }

                // 添加日期信息
                if (includeDate)
                {
                    var dateStr = fileInfo.CreationTime.ToString("yyyyMMdd");
                    newFileName += $"_{dateStr}";
                }

                // 添加大小信息
                if (includeSize)
                {
                    var sizeStr = FormatFileSize(fileInfo.Length);
                    newFileName += $"_{sizeStr}";
                }

                // 添加随机标识符以确保唯一性
                newFileName += $"_{Guid.NewGuid().ToString("N")[..6]}";

                // 构建完整的新文件路径
                var newFilePath = Path.Combine(directory, newFileName + extension);

                // 确保文件名的有效性
                newFilePath = Path.Combine(directory, NormalizeFileName(Path.GetFileName(newFilePath)));

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"智能重命名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 转换文件名大小写
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="caseStyle">大小写样式</param>
        /// <returns>操作成功返回新文件路径，失败返回空字符串</returns>
        public string ConvertFileNameCase(string filePath, CaseStyle caseStyle)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 解析文件路径组件
                var directory = Path.GetDirectoryName(filePath) ?? "";
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 转换文件名大小写
                var newFileName = caseStyle switch
                {
                    CaseStyle.LowerCase => fileName.ToLowerInvariant(),
                    CaseStyle.UpperCase => fileName.ToUpperInvariant(),
                    CaseStyle.TitleCase => ConvertToTitleCase(fileName),
                    CaseStyle.CamelCase => ConvertToCamelCase(fileName),
                    CaseStyle.PascalCase => ConvertToPascalCase(fileName),
                    CaseStyle.KebabCase => ConvertToKebabCase(fileName),
                    CaseStyle.SnakeCase => ConvertToSnakeCase(fileName),
                    _ => fileName
                };

                // 如果文件名没有变化，返回空字符串
                if (newFileName == fileName)
                {
                    return string.Empty;
                }

                // 构建新的文件路径
                var newFilePath = Path.Combine(directory, newFileName + extension);

                // 如果目标文件已存在，生成唯一文件名
                newFilePath = GenerateUniqueFileName(newFilePath);

                // 执行重命名操作
                File.Move(filePath, newFilePath);

                return newFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换文件名大小写失败: {ex.Message}");
                return string.Empty;
            }
        }

        #endregion

        #region 18. 文件属性修改方法

        /// <summary>
        /// 设置或取消文件的只读属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="isReadOnly">true设置为只读，false取消只读</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 只读属性可以防止文件被意外修改或删除
        /// 取消只读属性后，文件可以正常编辑和删除
        /// </remarks>
        public bool SetReadOnlyAttribute(string filePath, bool isReadOnly)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取当前文件属性
                var currentAttributes = File.GetAttributes(filePath);

                // 根据参数设置或移除只读属性
                if (isReadOnly)
                {
                    // 添加只读属性（使用位运算OR操作）
                    File.SetAttributes(filePath, currentAttributes | FileAttributes.ReadOnly);
                }
                else
                {
                    // 移除只读属性（使用位运算AND NOT操作）
                    File.SetAttributes(filePath, currentAttributes & ~FileAttributes.ReadOnly);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置只读属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置或取消文件的隐藏属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="isHidden">true设置为隐藏，false取消隐藏</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 隐藏属性使文件在文件资源管理器中默认不可见
        /// 需要显示隐藏文件选项才能看到隐藏文件
        /// </remarks>
        public bool SetHiddenAttribute(string filePath, bool isHidden)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取当前文件属性
                var currentAttributes = File.GetAttributes(filePath);

                // 根据参数设置或移除隐藏属性
                if (isHidden)
                {
                    // 添加隐藏属性
                    File.SetAttributes(filePath, currentAttributes | FileAttributes.Hidden);
                }
                else
                {
                    // 移除隐藏属性
                    File.SetAttributes(filePath, currentAttributes & ~FileAttributes.Hidden);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置隐藏属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置或取消文件的系统属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="isSystem">true设置为系统文件，false取消系统文件</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 系统文件属性通常用于操作系统重要文件
        /// 设置系统属性需要管理员权限，请谨慎使用
        /// </remarks>
        public bool SetSystemAttribute(string filePath, bool isSystem)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取当前文件属性
                var currentAttributes = File.GetAttributes(filePath);

                // 根据参数设置或移除系统属性
                if (isSystem)
                {
                    // 添加系统属性
                    File.SetAttributes(filePath, currentAttributes | FileAttributes.System);
                }
                else
                {
                    // 移除系统属性
                    File.SetAttributes(filePath, currentAttributes & ~FileAttributes.System);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置系统属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置或取消文件的存档属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="isArchive">true设置存档属性，false取消存档属性</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 存档属性通常用于标记文件是否需要备份
        /// 备份软件会检查此属性来决定是否备份文件
        /// </remarks>
        public bool SetArchiveAttribute(string filePath, bool isArchive)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取当前文件属性
                var currentAttributes = File.GetAttributes(filePath);

                // 根据参数设置或移除存档属性
                if (isArchive)
                {
                    // 添加存档属性
                    File.SetAttributes(filePath, currentAttributes | FileAttributes.Archive);
                }
                else
                {
                    // 移除存档属性
                    File.SetAttributes(filePath, currentAttributes & ~FileAttributes.Archive);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置存档属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置文件的时间戳（创建时间、修改时间、访问时间）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="creationTime">创建时间，null表示不修改</param>
        /// <param name="lastWriteTime">最后写入时间，null表示不修改</param>
        /// <param name="lastAccessTime">最后访问时间，null表示不修改</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 可以单独设置某个时间戳，也可以同时设置多个时间戳
        /// 时间戳的修改可能会影响文件的备份和同步行为
        /// </remarks>
        public bool SetFileTimestamps(string filePath, DateTime? creationTime = null,
            DateTime? lastWriteTime = null, DateTime? lastAccessTime = null)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 设置创建时间
                if (creationTime.HasValue)
                {
                    File.SetCreationTime(filePath, creationTime.Value);
                }

                // 设置最后写入时间
                if (lastWriteTime.HasValue)
                {
                    File.SetLastWriteTime(filePath, lastWriteTime.Value);
                }

                // 设置最后访问时间
                if (lastAccessTime.HasValue)
                {
                    File.SetLastAccessTime(filePath, lastAccessTime.Value);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置文件时间戳失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 通用文件属性修改方法
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="attributesToAdd">要添加的属性</param>
        /// <param name="attributesToRemove">要移除的属性</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 这是一个通用的属性修改方法，可以同时添加和移除多个属性
        /// 使用位运算来高效处理文件属性的组合
        /// 先移除指定属性，再添加指定属性，确保操作的准确性
        /// </remarks>
        public bool ModifyFileAttributes(string filePath, FileAttributes attributesToAdd = FileAttributes.Normal,
            FileAttributes attributesToRemove = FileAttributes.Normal)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 获取当前文件属性
                var currentAttributes = File.GetAttributes(filePath);

                // 先移除指定的属性（使用位运算AND NOT操作）
                var newAttributes = currentAttributes & ~attributesToRemove;

                // 再添加指定的属性（使用位运算OR操作）
                newAttributes |= attributesToAdd;

                // 应用新的属性设置
                File.SetAttributes(filePath, newAttributes);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"修改文件属性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量修改目录中文件的属性
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="attributesToAdd">要添加的属性</param>
        /// <param name="attributesToRemove">要移除的属性</param>
        /// <param name="searchPattern">文件搜索模式，默认为所有文件</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <returns>成功处理的文件数量</returns>
        /// <remarks>
        /// 批量处理可以提高效率，但需要注意权限问题
        /// 建议先在小范围测试，确认效果后再大规模应用
        /// </remarks>
        public int BatchModifyFileAttributes(string directoryPath, FileAttributes attributesToAdd = FileAttributes.Normal,
            FileAttributes attributesToRemove = FileAttributes.Normal, string searchPattern = "*.*", bool recursive = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在: {directoryPath}");

                // 获取文件列表
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var files = Directory.GetFiles(directoryPath, searchPattern, searchOption);
                var processedCount = 0;

                // 逐个处理文件
                foreach (var file in files)
                {
                    try
                    {
                        if (ModifyFileAttributes(file, attributesToAdd, attributesToRemove))
                        {
                            processedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"处理文件属性失败 {file}: {ex.Message}");
                        // 继续处理其他文件
                    }
                }

                return processedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"批量修改文件属性失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 重置文件属性为正常状态
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>操作成功返回true</returns>
        /// <remarks>
        /// 移除所有特殊属性（只读、隐藏、系统等），将文件恢复为正常状态
        /// 这在处理从其他系统复制的文件时特别有用
        /// </remarks>
        public bool ResetFileAttributes(string filePath)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                // 设置为正常属性，这会移除所有特殊属性
                File.SetAttributes(filePath, FileAttributes.Normal);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"重置文件属性失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        // ==========================================
        // 🚀 高级文件操作 - 现代化功能
        // ==========================================

        #region 7. 异步文件操作

        /// <summary>
        /// 异步读取文件全部文本内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="encoding">文本编码，null时自动检测</param>
        /// <returns>文件内容</returns>
        public async Task<string> ReadAllTextAsync(string filePath, Encoding? encoding = null)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                if (encoding == null)
                    encoding = DetectEncoding(filePath);

                return await File.ReadAllTextAsync(filePath, encoding);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步读取文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 异步写入文本内容到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">要写入的内容</param>
        /// <param name="encoding">文本编码，null时使用UTF-8</param>
        /// <param name="append">是否追加模式</param>
        /// <returns>写入成功返回true</returns>
        public async Task<bool> WriteAllTextAsync(string filePath, string content, Encoding? encoding = null, bool append = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                content ??= string.Empty;
                encoding ??= Encoding.UTF8;

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                if (append)
                {
                    await File.AppendAllTextAsync(filePath, content, encoding);
                }
                else
                {
                    await File.WriteAllTextAsync(filePath, content, encoding);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步写入文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步读取文件全部字节内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件字节数组</returns>
        public async Task<byte[]> ReadAllBytesAsync(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                return await File.ReadAllBytesAsync(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步读取文件字节失败: {ex.Message}");
                return Array.Empty<byte>();
            }
        }

        /// <summary>
        /// 异步写入字节数组到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="bytes">要写入的字节数组</param>
        /// <param name="append">是否追加模式</param>
        /// <returns>写入成功返回true</returns>
        public async Task<bool> WriteAllBytesAsync(string filePath, byte[] bytes, bool append = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空", nameof(filePath));

                if (bytes == null)
                    throw new ArgumentNullException(nameof(bytes));

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                if (append)
                {
                    using var stream = new FileStream(filePath, FileMode.Append, FileAccess.Write);
                    await stream.WriteAsync(bytes, 0, bytes.Length);
                }
                else
                {
                    await File.WriteAllBytesAsync(filePath, bytes);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步写入文件字节失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步复制文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <returns>复制成功返回true</returns>
        public async Task<bool> CopyFileAsync(string sourceFilePath, string destinationFilePath, bool overwrite = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(sourceFilePath))
                    throw new ArgumentException("源文件路径不能为空", nameof(sourceFilePath));

                if (string.IsNullOrWhiteSpace(destinationFilePath))
                    throw new ArgumentException("目标文件路径不能为空", nameof(destinationFilePath));

                if (!File.Exists(sourceFilePath))
                    throw new FileNotFoundException($"源文件不存在: {sourceFilePath}");

                // 确保目标目录存在
                var destinationDirectory = Path.GetDirectoryName(destinationFilePath);
                if (!string.IsNullOrEmpty(destinationDirectory) && !Directory.Exists(destinationDirectory))
                {
                    Directory.CreateDirectory(destinationDirectory);
                }

                using var sourceStream = new FileStream(sourceFilePath, FileMode.Open, FileAccess.Read);
                using var destinationStream = new FileStream(destinationFilePath,
                    overwrite ? FileMode.Create : FileMode.CreateNew,
                    FileAccess.Write);

                await sourceStream.CopyToAsync(destinationStream);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"异步复制文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 12. 文件完整性验证和比较

        /// <summary>
        /// 比较两个文件是否相同（通过内容比较）
        /// </summary>
        /// <param name="file1Path">第一个文件路径</param>
        /// <param name="file2Path">第二个文件路径</param>
        /// <returns>文件相同返回true</returns>
        public bool CompareFiles(string file1Path, string file2Path)
        {
            try
            {
                if (!FileExists(file1Path) || !FileExists(file2Path))
                    return false;

                var file1Info = new FileInfo(file1Path);
                var file2Info = new FileInfo(file2Path);

                // 首先比较文件大小
                if (file1Info.Length != file2Info.Length)
                    return false;

                // 如果是同一个文件
                if (string.Equals(file1Info.FullName, file2Info.FullName, StringComparison.OrdinalIgnoreCase))
                    return true;

                // 逐字节比较
                using var stream1 = new FileStream(file1Path, FileMode.Open, FileAccess.Read);
                using var stream2 = new FileStream(file2Path, FileMode.Open, FileAccess.Read);

                const int bufferSize = 8192;
                var buffer1 = new byte[bufferSize];
                var buffer2 = new byte[bufferSize];

                int bytesRead1, bytesRead2;
                do
                {
                    bytesRead1 = stream1.Read(buffer1, 0, bufferSize);
                    bytesRead2 = stream2.Read(buffer2, 0, bufferSize);

                    if (bytesRead1 != bytesRead2)
                        return false;

                    for (int i = 0; i < bytesRead1; i++)
                    {
                        if (buffer1[i] != buffer2[i])
                            return false;
                    }
                } while (bytesRead1 > 0);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件比较失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 计算文件的SHA256哈希值
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>哈希值的十六进制字符串</returns>
        public string CalculateFileHash(string filePath)
        {
            try
            {
                if (!FileExists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                using var sha256 = SHA256.Create();

                var hashBytes = sha256.ComputeHash(stream);
                return Convert.ToHexString(hashBytes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"计算文件哈希失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 通过哈希值比较两个文件
        /// </summary>
        /// <param name="file1Path">第一个文件路径</param>
        /// <param name="file2Path">第二个文件路径</param>
        /// <returns>文件哈希相同返回true</returns>
        public bool CompareFilesByHash(string file1Path, string file2Path)
        {
            try
            {
                var hash1 = CalculateFileHash(file1Path);
                var hash2 = CalculateFileHash(file2Path);

                return !string.IsNullOrEmpty(hash1) &&
                       !string.IsNullOrEmpty(hash2) &&
                       string.Equals(hash1, hash2, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"通过哈希比较文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedHash">期望的哈希值</param>
        /// <returns>文件完整性验证通过返回true</returns>
        public bool VerifyFileIntegrity(string filePath, string expectedHash)
        {
            try
            {
                var actualHash = CalculateFileHash(filePath);
                return string.Equals(actualHash, expectedHash, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件完整性验证失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 8. 流式操作支持

        /// <summary>
        /// 以块的形式读取大文件，避免内存溢出
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="chunkSize">块大小，默认8KB</param>
        /// <returns>字节块的枚举</returns>
        public IEnumerable<byte[]> ReadFileInChunks(string filePath, int chunkSize = 8192)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

            if (!File.Exists(filePath))
                throw new FileNotFoundException($"文件不存在: {filePath}");

            if (chunkSize <= 0)
                throw new ArgumentException("块大小必须大于0", nameof(chunkSize));

            using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            var buffer = new byte[chunkSize];
            int bytesRead;

            while ((bytesRead = stream.Read(buffer, 0, chunkSize)) > 0)
            {
                if (bytesRead == chunkSize)
                {
                    yield return buffer;
                }
                else
                {
                    // 最后一块可能不满
                    var lastChunk = new byte[bytesRead];
                    Array.Copy(buffer, lastChunk, bytesRead);
                    yield return lastChunk;
                }
            }
        }

        /// <summary>
        /// 从流写入文件
        /// </summary>
        /// <param name="filePath">目标文件路径</param>
        /// <param name="sourceStream">源数据流</param>
        /// <param name="bufferSize">缓冲区大小，默认64KB</param>
        /// <returns>写入成功返回true</returns>
        public bool WriteFileFromStream(string filePath, Stream sourceStream, int bufferSize = 65536)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                if (sourceStream == null)
                    throw new ArgumentNullException(nameof(sourceStream));

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, bufferSize);
                sourceStream.CopyTo(fileStream, bufferSize);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从流写入文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从流追加到文件
        /// </summary>
        /// <param name="filePath">目标文件路径</param>
        /// <param name="sourceStream">源数据流</param>
        /// <param name="bufferSize">缓冲区大小，默认64KB</param>
        /// <returns>追加成功返回true</returns>
        public bool AppendFromStream(string filePath, Stream sourceStream, int bufferSize = 65536)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                if (sourceStream == null)
                    throw new ArgumentNullException(nameof(sourceStream));

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                using var fileStream = new FileStream(filePath, FileMode.Append, FileAccess.Write, FileShare.None, bufferSize);
                sourceStream.CopyTo(fileStream, bufferSize);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从流追加到文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 打开文件流
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="mode">文件模式</param>
        /// <param name="access">访问权限</param>
        /// <param name="share">共享模式</param>
        /// <param name="bufferSize">缓冲区大小，默认4KB</param>
        /// <returns>文件流，使用完毕后需要释放</returns>
        public Stream? OpenFileStream(string filePath, FileMode mode, FileAccess access, FileShare share = FileShare.Read, int bufferSize = 4096)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                // 如果是创建模式，确保目录存在
                if (mode == FileMode.Create || mode == FileMode.CreateNew || mode == FileMode.OpenOrCreate)
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                }

                return new FileStream(filePath, mode, access, share, bufferSize);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"打开文件流失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 9. 文件锁定和并发控制

        /// <summary>
        /// 检查文件是否被锁定（被其他进程占用）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件被锁定返回true</returns>
        public bool IsFileLocked(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                if (!File.Exists(filePath))
                    return false;

                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
                return false; // 如果能打开，说明没有被锁定
            }
            catch (IOException)
            {
                return true; // IO异常通常表示文件被锁定
            }
            catch (UnauthorizedAccessException)
            {
                return true; // 权限异常也可能表示文件被锁定
            }
            catch
            {
                return false; // 其他异常不认为是锁定
            }
        }

        /// <summary>
        /// 打开文件并加锁
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="mode">文件模式</param>
        /// <param name="access">访问权限</param>
        /// <param name="share">共享模式</param>
        /// <returns>带锁的文件流，使用完毕后需要释放</returns>
        public FileStream? OpenFileWithLock(string filePath, FileMode mode, FileAccess access, FileShare share = FileShare.None)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                // 如果是创建模式，确保目录存在
                if (mode == FileMode.Create || mode == FileMode.CreateNew || mode == FileMode.OpenOrCreate)
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                }

                return new FileStream(filePath, mode, access, share);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"打开文件并加锁失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 等待文件解锁
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="timeout">超时时间</param>
        /// <param name="checkInterval">检查间隔，默认100毫秒</param>
        /// <returns>文件解锁返回true，超时返回false</returns>
        public bool WaitForFileUnlock(string filePath, TimeSpan timeout, TimeSpan? checkInterval = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                if (!File.Exists(filePath))
                    return true; // 文件不存在，认为是解锁状态

                checkInterval ??= TimeSpan.FromMilliseconds(100);
                var endTime = DateTime.Now.Add(timeout);

                while (DateTime.Now < endTime)
                {
                    if (!IsFileLocked(filePath))
                        return true;

                    Thread.Sleep(checkInterval.Value);
                }

                return false; // 超时
            }
            catch (Exception ex)
            {
                Console.WriteLine($"等待文件解锁失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 尝试锁定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="timeout">超时时间</param>
        /// <param name="lockedStream">成功时返回锁定的文件流</param>
        /// <param name="mode">文件模式</param>
        /// <param name="access">访问权限</param>
        /// <returns>锁定成功返回true</returns>
        public bool TryLockFile(string filePath, TimeSpan timeout, FileStream? lockedStream,
            FileMode mode, FileAccess access)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                var endTime = DateTime.Now.Add(timeout);

                while (DateTime.Now < endTime)
                {
                    try
                    {
                        lockedStream = OpenFileWithLock(filePath, mode, access, FileShare.None);
                        return lockedStream != null;
                    }
                    catch (IOException)
                    {
                        // 文件被锁定，继续等待
                        Thread.Sleep(100);
                    }
                }

                return false; // 超时
            }
            catch (Exception ex)
            {
                Console.WriteLine($"尝试锁定文件失败: {ex.Message}");
                lockedStream?.Dispose();
                return false;
            }
        }

        /// <summary>
        /// 尝试锁定文件（返回元组）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="timeout">超时时间</param>
        /// <param name="mode">文件模式，默认OpenOrCreate</param>
        /// <param name="access">访问权限，默认ReadWrite</param>
        /// <returns>锁定结果和文件流的元组</returns>
        public (bool Success, FileStream? Stream) TryLockFileWithResult(string filePath, TimeSpan timeout,
            FileMode mode = FileMode.OpenOrCreate, FileAccess access = FileAccess.ReadWrite)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return (false, null);

                var endTime = DateTime.Now.Add(timeout);

                while (DateTime.Now < endTime)
                {
                    try
                    {
                        var stream = OpenFileWithLock(filePath, mode, access, FileShare.None);
                        return (stream != null, stream);
                    }
                    catch (IOException)
                    {
                        // 文件被锁定，继续等待
                        Thread.Sleep(100);
                    }
                }

                return (false, null); // 超时
            }
            catch (Exception ex)
            {
                Console.WriteLine($"尝试锁定文件失败: {ex.Message}");
                return (false, null);
            }
        }

        #endregion

        // ==========================================
        // 🛡️ 企业级功能 - 安全和管理
        // ==========================================

        #region 10. 临时文件管理

        /// <summary>
        /// 创建临时文件
        /// </summary>
        /// <param name="extension">文件扩展名，默认.tmp</param>
        /// <param name="prefix">文件名前缀，默认temp</param>
        /// <returns>临时文件路径</returns>
        public string CreateTemporaryFile(string extension = ".tmp", string prefix = "temp")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(extension))
                    extension = ".tmp";

                if (!extension.StartsWith("."))
                    extension = "." + extension;

                if (string.IsNullOrWhiteSpace(prefix))
                    prefix = "temp";

                var tempDir = Path.GetTempPath();
                var fileName = $"{prefix}_{Guid.NewGuid():N}{extension}";
                var tempFilePath = Path.Combine(tempDir, fileName);

                // 创建空文件
                File.WriteAllText(tempFilePath, string.Empty);

                return tempFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建临时文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 创建包含内容的临时文件
        /// </summary>
        /// <param name="content">文件内容</param>
        /// <param name="extension">文件扩展名，默认.tmp</param>
        /// <param name="prefix">文件名前缀，默认temp</param>
        /// <param name="encoding">文本编码，默认UTF-8</param>
        /// <returns>临时文件路径</returns>
        public string CreateTemporaryFileWithContent(string content, string extension = ".tmp",
            string prefix = "temp", Encoding? encoding = null)
        {
            try
            {
                var tempFilePath = CreateTemporaryFile(extension, prefix);
                if (string.IsNullOrEmpty(tempFilePath))
                    return string.Empty;

                encoding ??= Encoding.UTF8;
                File.WriteAllText(tempFilePath, content ?? string.Empty, encoding);

                return tempFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建包含内容的临时文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 在指定目录创建临时文件
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="prefix">文件名前缀，默认temp</param>
        /// <param name="extension">文件扩展名，默认.tmp</param>
        /// <returns>临时文件路径</returns>
        public string CreateTemporaryFileInDirectory(string directory, string prefix = "temp", string extension = ".tmp")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(directory))
                    directory = Path.GetTempPath();

                if (!Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                if (string.IsNullOrWhiteSpace(extension))
                    extension = ".tmp";

                if (!extension.StartsWith("."))
                    extension = "." + extension;

                if (string.IsNullOrWhiteSpace(prefix))
                    prefix = "temp";

                var fileName = $"{prefix}_{Guid.NewGuid():N}{extension}";
                var tempFilePath = Path.Combine(directory, fileName);

                // 创建空文件
                File.WriteAllText(tempFilePath, string.Empty);

                return tempFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"在指定目录创建临时文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 清理临时文件
        /// </summary>
        /// <param name="olderThan">清理早于指定时间的文件</param>
        /// <param name="directory">目录路径，null时使用系统临时目录</param>
        /// <param name="pattern">文件模式，默认temp_*.tmp</param>
        /// <returns>清理的文件数量</returns>
        public int CleanupTemporaryFiles(TimeSpan olderThan, string? directory = null, string pattern = "temp_*.tmp")
        {
            try
            {
                directory ??= Path.GetTempPath();

                if (!Directory.Exists(directory))
                    return 0;

                var cutoffTime = DateTime.Now.Subtract(olderThan);
                var files = Directory.GetFiles(directory, pattern);
                var deletedCount = 0;

                foreach (var file in files)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.LastWriteTime < cutoffTime)
                        {
                            File.Delete(file);
                            deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"删除临时文件失败 {file}: {ex.Message}");
                    }
                }

                return deletedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理临时文件失败: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region 11. 文件备份和版本控制

        /// <summary>
        /// 创建文件备份
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="backupSuffix">备份文件后缀，默认.bak</param>
        /// <returns>备份成功返回true</returns>
        public bool CreateBackup(string filePath, string backupSuffix = ".bak")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                if (string.IsNullOrWhiteSpace(backupSuffix))
                    backupSuffix = ".bak";

                if (!backupSuffix.StartsWith("."))
                    backupSuffix = "." + backupSuffix;

                var backupPath = filePath + backupSuffix;
                File.Copy(filePath, backupPath, overwrite: true);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建文件备份失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从备份恢复文件
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="backupSuffix">备份文件后缀，默认.bak</param>
        /// <returns>恢复成功返回true</returns>
        public bool RestoreFromBackup(string filePath, string backupSuffix = ".bak")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                if (string.IsNullOrWhiteSpace(backupSuffix))
                    backupSuffix = ".bak";

                if (!backupSuffix.StartsWith("."))
                    backupSuffix = "." + backupSuffix;

                var backupPath = filePath + backupSuffix;

                if (!File.Exists(backupPath))
                    throw new FileNotFoundException($"备份文件不存在: {backupPath}");

                File.Copy(backupPath, filePath, overwrite: true);

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从备份恢复文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建带时间戳的备份
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="timestampFormat">时间戳格式，默认yyyyMMdd_HHmmss</param>
        /// <returns>备份文件路径，失败返回空字符串</returns>
        public string CreateTimestampedBackup(string filePath, string timestampFormat = "yyyyMMdd_HHmmss")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空或仅包含空白字符", nameof(filePath));

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在: {filePath}");

                var directory = Path.GetDirectoryName(filePath) ?? string.Empty;
                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);
                var timestamp = DateTime.Now.ToString(timestampFormat);

                var backupFileName = $"{fileNameWithoutExt}_{timestamp}{extension}";
                var backupPath = Path.Combine(directory, backupFileName);

                File.Copy(filePath, backupPath, overwrite: true);

                return backupPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建带时间戳的备份失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文件的所有版本（备份文件）
        /// </summary>
        /// <param name="filePath">原文件路径</param>
        /// <param name="backupPattern">备份文件模式，默认查找同名文件的所有版本</param>
        /// <returns>版本文件路径数组</returns>
        public string[] GetFileVersions(string filePath, string? backupPattern = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return Array.Empty<string>();

                var directory = Path.GetDirectoryName(filePath);
                if (string.IsNullOrEmpty(directory) || !Directory.Exists(directory))
                    return Array.Empty<string>();

                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);

                // 默认模式：查找 filename_*.ext 和 filename.ext.bak 格式的文件
                backupPattern ??= $"{fileNameWithoutExt}_*{extension}";

                var backupFiles = Directory.GetFiles(directory, backupPattern)
                    .Concat(Directory.GetFiles(directory, $"{Path.GetFileName(filePath)}.*"))
                    .Where(f => !string.Equals(f, filePath, StringComparison.OrdinalIgnoreCase))
                    .OrderByDescending(f => new FileInfo(f).LastWriteTime)
                    .ToArray();

                return backupFiles;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件版本失败: {ex.Message}");
                return Array.Empty<string>();
            }
        }

        #endregion

        #region 13. 文件校验和管理

        /// <summary>
        /// 创建文件的校验和文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="checksumFilePath">校验和文件路径，null时自动生成</param>
        /// <returns>校验和文件路径，失败返回空字符串</returns>
        public string CreateChecksumFile(string filePath, string? checksumFilePath = null)
        {
            try
            {
                var hash = CalculateFileHash(filePath);
                if (string.IsNullOrEmpty(hash))
                    return string.Empty;

                checksumFilePath ??= filePath + ".sha256";

                var fileName = Path.GetFileName(filePath);
                var checksumContent = $"{hash} *{fileName}";

                File.WriteAllText(checksumFilePath, checksumContent);

                return checksumFilePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建校验和文件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 验证文件的校验和文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="checksumFilePath">校验和文件路径，null时自动查找</param>
        /// <returns>校验和验证通过返回true</returns>
        public bool VerifyChecksumFile(string filePath, string? checksumFilePath = null)
        {
            try
            {
                checksumFilePath ??= filePath + ".sha256";

                if (!File.Exists(checksumFilePath))
                    return false;

                var checksumContent = File.ReadAllText(checksumFilePath).Trim();
                var parts = checksumContent.Split(' ', 2);

                if (parts.Length != 2)
                    return false;

                var expectedHash = parts[0];
                return VerifyFileIntegrity(filePath, expectedHash);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"验证校验和文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 19. 高级文件名处理辅助方法

        /// <summary>
        /// 获取文件类型前缀
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>文件类型前缀</returns>
        private string GetFileTypePrefix(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" or ".webp" => "Image",
                ".mp4" or ".avi" or ".mov" or ".wmv" or ".flv" or ".mkv" => "Video",
                ".mp3" or ".wav" or ".flac" or ".aac" or ".ogg" => "Audio",
                ".pdf" or ".doc" or ".docx" or ".xls" or ".xlsx" or ".ppt" or ".pptx" => "Document",
                ".txt" or ".md" or ".rtf" => "Text",
                ".zip" or ".rar" or ".7z" or ".tar" or ".gz" => "Archive",
                ".cs" or ".js" or ".html" or ".css" or ".cpp" or ".java" or ".py" => "Code",
                _ => "File"
            };
        }

        /// <summary>
        /// 获取文件大小分类
        /// </summary>
        /// <param name="sizeInBytes">文件大小（字节）</param>
        /// <returns>大小分类</returns>
        private string GetSizeCategory(long sizeInBytes)
        {
            return sizeInBytes switch
            {
                < 1024 => "Tiny",                    // < 1KB
                < 1024 * 1024 => "Small",            // < 1MB
                < 10 * 1024 * 1024 => "Medium",      // < 10MB
                < 100 * 1024 * 1024 => "Large",      // < 100MB
                _ => "Huge"                           // >= 100MB
            };
        }

        /// <summary>
        /// 获取下一个顺序编号
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="extension">文件扩展名</param>
        /// <returns>下一个可用的编号</returns>
        private int GetNextSequentialNumber(string directory, string extension)
        {
            try
            {
                var files = Directory.GetFiles(directory, $"File_*{extension}");
                var maxNumber = 0;

                foreach (var file in files)
                {
                    var fileName = Path.GetFileNameWithoutExtension(file);
                    var parts = fileName.Split('_');

                    if (parts.Length >= 2 && parts[0] == "File" && int.TryParse(parts[1], out var number))
                    {
                        maxNumber = Math.Max(maxNumber, number);
                    }
                }

                return maxNumber + 1;
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// 格式化文件大小为可读字符串
        /// </summary>
        /// <param name="sizeInBytes">文件大小（字节）</param>
        /// <returns>格式化的大小字符串</returns>
        private string FormatFileSize(long sizeInBytes)
        {
            return sizeInBytes switch
            {
                < 1024 => $"{sizeInBytes}B",
                < 1024 * 1024 => $"{sizeInBytes / 1024}KB",
                < 1024 * 1024 * 1024 => $"{sizeInBytes / (1024 * 1024)}MB",
                _ => $"{sizeInBytes / (1024 * 1024 * 1024)}GB"
            };
        }

        /// <summary>
        /// 转换为标题格式（每个单词首字母大写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>标题格式字符串</returns>
        private string ConvertToTitleCase(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            var words = input.Split(new[] { ' ', '_', '-' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    if (result.Length > 0)
                        result.Append(' ');

                    result.Append(char.ToUpperInvariant(word[0]));
                    if (word.Length > 1)
                        result.Append(word[1..].ToLowerInvariant());
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 转换为驼峰格式（首字母小写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>驼峰格式字符串</returns>
        private string ConvertToCamelCase(string input)
        {
            var titleCase = ConvertToTitleCase(input);
            if (string.IsNullOrEmpty(titleCase))
                return titleCase;

            var result = titleCase.Replace(" ", "");
            return char.ToLowerInvariant(result[0]) + result[1..];
        }

        /// <summary>
        /// 转换为帕斯卡格式（首字母大写）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>帕斯卡格式字符串</returns>
        private string ConvertToPascalCase(string input)
        {
            var titleCase = ConvertToTitleCase(input);
            return titleCase.Replace(" ", "");
        }

        /// <summary>
        /// 转换为短横线格式（kebab-case）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>短横线格式字符串</returns>
        private string ConvertToKebabCase(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            var words = input.Split(new[] { ' ', '_' }, StringSplitOptions.RemoveEmptyEntries);
            return string.Join("-", words.Select(w => w.ToLowerInvariant()));
        }

        /// <summary>
        /// 转换为下划线格式（snake_case）
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>下划线格式字符串</returns>
        private string ConvertToSnakeCase(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return input;

            var words = input.Split(new[] { ' ', '-' }, StringSplitOptions.RemoveEmptyEntries);
            return string.Join("_", words.Select(w => w.ToLowerInvariant()));
        }

        #endregion
    }
}
