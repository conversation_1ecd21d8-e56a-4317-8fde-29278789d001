using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Zylo.YIO.Core
{
    /// <summary>
    /// YPath - 企业级路径处理工具类
    ///
    /// 功能概述：
    /// • 路径组合与构建 - 安全的路径拼接，防止路径遍历攻击
    /// • 路径转换与规范化 - 跨平台路径格式转换，环境变量处理
    /// • 路径验证与安全检查 - 全面的路径有效性和安全性验证
    /// • 路径信息提取 - 详细的路径组成部分分析
    /// • 路径比较与匹配 - 高级的路径比较和模式匹配
    /// • 临时路径管理 - 安全的临时文件和目录路径生成
    /// • 特殊路径处理 - 网络路径、UNC路径、长路径支持
    ///
    /// 设计特点：
    /// • 跨平台兼容 - 支持 Windows、Linux、macOS 路径格式
    /// • 安全优先 - 内置路径遍历攻击防护和安全验证
    /// • 性能优化 - 缓存机制和高效算法
    /// • 错误友好 - 详细的错误信息和优雅的错误处理
    ///
    /// 使用示例：
    /// <code>
    /// // 安全路径组合
    /// var safePath = pathUtils.SafeCombinePath(@"C:\Base", "sub\file.txt");
    ///
    /// // 跨平台路径转换
    /// var unixPath = pathUtils.ToUnixPath(@"C:\Windows\System32");
    ///
    /// // 路径验证
    /// var isValid = pathUtils.IsValidPath(userInput);
    /// var isSafe = pathUtils.IsSafePath(userInput);
    /// </code>
    /// </summary>
 
    [YStatic]
    public partial class YPath
    {
        #region 路径组合和构建

        /// <summary>
        /// 组合多个路径片段为完整路径
        /// </summary>
        /// <param name="paths">路径片段数组</param>
        /// <returns>组合后的完整路径</returns>
        public string CombinePath(params string[] paths)
        {
            try
            {
                if (paths == null || paths.Length == 0)
                    return string.Empty;

                // 过滤空值和空字符串
                var validPaths = paths.Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();
                if (validPaths.Length == 0)
                    return string.Empty;

                return Path.Combine(validPaths);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"路径组合失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 安全组合路径（防止路径遍历攻击）
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="relativePath">相对路径</param>
        /// <returns>安全的组合路径</returns>
        public string SafeCombinePath(string basePath, string relativePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(basePath))
                    throw new ArgumentException("基础路径不能为空", nameof(basePath));

                if (string.IsNullOrWhiteSpace(relativePath))
                    return basePath;

                // 检查相对路径是否包含危险字符
                if (relativePath.Contains("..") || relativePath.Contains("~"))
                {
                    Console.WriteLine("检测到潜在的路径遍历攻击");
                    return basePath;
                }

                var combined = Path.Combine(basePath, relativePath);
                var fullBasePath = Path.GetFullPath(basePath);
                var fullCombinedPath = Path.GetFullPath(combined);

                // 确保组合后的路径仍在基础路径内
                if (!fullCombinedPath.StartsWith(fullBasePath, StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine("路径超出了基础目录范围");
                    return basePath;
                }

                return combined;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"安全路径组合失败: {ex.Message}");
                return basePath;
            }
        }

        /// <summary>
        /// 构建带时间戳的路径
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <param name="fileName">文件名</param>
        /// <param name="extension">扩展名</param>
        /// <param name="timestampFormat">时间戳格式</param>
        /// <returns>带时间戳的完整路径</returns>
        public string BuildTimestampedPath(string basePath, string fileName, string extension, string timestampFormat = "yyyyMMdd_HHmmss")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    fileName = "file";

                if (!string.IsNullOrWhiteSpace(extension) && !extension.StartsWith("."))
                    extension = "." + extension;

                var timestamp = DateTime.Now.ToString(timestampFormat);
                var timestampedFileName = $"{fileName}_{timestamp}{extension}";

                return string.IsNullOrWhiteSpace(basePath)
                    ? timestampedFileName
                    : Path.Combine(basePath, timestampedFileName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"构建时间戳路径失败: {ex.Message}");
                return Path.Combine(basePath ?? "", $"{fileName ?? "file"}{extension ?? ""}");
            }
        }

        /// <summary>
        /// 构建唯一路径（避免重复）
        /// </summary>
        /// <param name="desiredPath">期望的路径</param>
        /// <returns>唯一的路径</returns>
        public string BuildUniquePath(string desiredPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(desiredPath))
                    return string.Empty;

                if (!File.Exists(desiredPath) && !Directory.Exists(desiredPath))
                    return desiredPath;

                var directory = Path.GetDirectoryName(desiredPath) ?? "";
                var fileNameWithoutExt = Path.GetFileNameWithoutExtension(desiredPath);
                var extension = Path.GetExtension(desiredPath);

                int counter = 1;
                string uniquePath;
                do
                {
                    var uniqueFileName = $"{fileNameWithoutExt}_{counter}{extension}";
                    uniquePath = Path.Combine(directory, uniqueFileName);
                    counter++;
                }
                while (File.Exists(uniquePath) || Directory.Exists(uniquePath));

                return uniquePath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"构建唯一路径失败: {ex.Message}");
                return desiredPath;
            }
        }

        #endregion

        #region 路径转换和规范化

        /// <summary>
        /// 规范化路径（统一分隔符、移除多余字符）
        /// </summary>
        /// <param name="path">要规范化的路径</param>
        /// <returns>规范化后的路径</returns>
        public string NormalizePath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                // 替换路径分隔符为当前系统的标准分隔符
                var normalized = path.Replace('/', Path.DirectorySeparatorChar)
                                    .Replace('\\', Path.DirectorySeparatorChar);

                // 移除重复的分隔符
                while (normalized.Contains($"{Path.DirectorySeparatorChar}{Path.DirectorySeparatorChar}"))
                {
                    normalized = normalized.Replace(
                        $"{Path.DirectorySeparatorChar}{Path.DirectorySeparatorChar}",
                        Path.DirectorySeparatorChar.ToString());
                }

                // 移除末尾的分隔符（除非是根目录）
                if (normalized.Length > 1 && normalized.EndsWith(Path.DirectorySeparatorChar))
                {
                    normalized = normalized.TrimEnd(Path.DirectorySeparatorChar);
                }

                return normalized;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"路径规范化失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 获取绝对路径
        /// </summary>
        /// <param name="path">相对或绝对路径</param>
        /// <returns>绝对路径</returns>
        public string GetAbsolutePath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetFullPath(path);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取绝对路径失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 获取相对路径
        /// </summary>
        /// <param name="fromPath">起始路径</param>
        /// <param name="toPath">目标路径</param>
        /// <returns>从起始路径到目标路径的相对路径</returns>
        public string GetRelativePath(string fromPath, string toPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fromPath) || string.IsNullOrWhiteSpace(toPath))
                    return string.Empty;

                var fromUri = new Uri(Path.GetFullPath(fromPath));
                var toUri = new Uri(Path.GetFullPath(toPath));

                if (fromUri.Scheme != toUri.Scheme)
                    return toPath; // 不同驱动器，返回绝对路径

                var relativeUri = fromUri.MakeRelativeUri(toUri);
                var relativePath = Uri.UnescapeDataString(relativeUri.ToString());

                return relativePath.Replace('/', Path.DirectorySeparatorChar);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取相对路径失败: {ex.Message}");
                return toPath;
            }
        }



        /// <summary>
        /// 转换路径为 Unix 格式（使用正斜杠分隔符）
        /// 将 Windows 风格的路径转换为 Unix/Linux 风格
        /// </summary>
        /// <param name="path">要转换的路径</param>
        /// <returns>Unix 格式的路径</returns>
        /// <example>
        /// <code>
        /// var unixPath = ToUnixPath(@"C:\Windows\System32\file.txt");
        /// // 结果: "C:/Windows/System32/file.txt"
        /// </code>
        /// </example>
        public string ToUnixPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                // 将反斜杠替换为正斜杠
                var unixPath = path.Replace('\\', '/');

                // 处理多个连续的斜杠，替换为单个斜杠
                while (unixPath.Contains("//"))
                {
                    unixPath = unixPath.Replace("//", "/");
                }

                return unixPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换为Unix路径失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 转换路径为 Windows 格式（使用反斜杠分隔符）
        /// 将 Unix/Linux 风格的路径转换为 Windows 风格
        /// </summary>
        /// <param name="path">要转换的路径</param>
        /// <returns>Windows 格式的路径</returns>
        /// <example>
        /// <code>
        /// var winPath = ToWindowsPath("C:/Program Files/App/file.txt");
        /// // 结果: @"C:\Program Files\App\file.txt"
        /// </code>
        /// </example>
        public string ToWindowsPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                // 将正斜杠替换为反斜杠
                var windowsPath = path.Replace('/', '\\');

                // 处理多个连续的反斜杠，替换为单个反斜杠
                while (windowsPath.Contains("\\\\"))
                {
                    windowsPath = windowsPath.Replace("\\\\", "\\");
                }

                return windowsPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换为Windows路径失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 智能转换路径分隔符
        /// 根据指定的分隔符类型转换路径中的所有分隔符
        /// </summary>
        /// <param name="path">要转换的路径</param>
        /// <param name="targetSeparator">目标分隔符（'/' 或 '\'）</param>
        /// <returns>转换后的路径</returns>
        /// <example>
        /// <code>
        /// var converted = ConvertPathSeparators(@"C:\path/mixed\separators", '/');
        /// // 结果: "C:/path/mixed/separators"
        /// </code>
        /// </example>
        public string ConvertPathSeparators(string path, char targetSeparator)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                if (targetSeparator != '/' && targetSeparator != '\\')
                    throw new ArgumentException("分隔符只能是 '/' 或 '\\'", nameof(targetSeparator));

                // 确定源分隔符
                char sourceSeparator = targetSeparator == '/' ? '\\' : '/';

                // 执行替换
                var result = path.Replace(sourceSeparator, targetSeparator);

                // 清理多个连续的分隔符
                var doubleSeparator = new string(targetSeparator, 2);
                while (result.Contains(doubleSeparator))
                {
                    result = result.Replace(doubleSeparator, targetSeparator.ToString());
                }

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换路径分隔符失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 展开路径中的环境变量
        /// 将形如 %VARIABLE% 或 $VARIABLE 的环境变量展开为实际值
        /// </summary>
        /// <param name="path">包含环境变量的路径</param>
        /// <returns>展开环境变量后的路径</returns>
        /// <example>
        /// <code>
        /// var expanded = ExpandEnvironmentVariables("%TEMP%\\myfile.txt");
        /// // 结果: "C:\Users\<USER>\AppData\Local\Temp\myfile.txt"
        /// </code>
        /// </example>
        public string ExpandEnvironmentVariables(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                // 使用 .NET 内置方法展开 Windows 风格的环境变量 (%VAR%)
                var expanded = Environment.ExpandEnvironmentVariables(path);

                // 处理 Unix 风格的环境变量 ($VAR)
                var unixVarPattern = @"\$([A-Za-z_][A-Za-z0-9_]*)";
                expanded = Regex.Replace(expanded, unixVarPattern, match =>
                {
                    var varName = match.Groups[1].Value;
                    var varValue = Environment.GetEnvironmentVariable(varName);
                    return varValue ?? match.Value; // 如果变量不存在，保持原样
                });

                return expanded;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"展开环境变量失败: {ex.Message}");
                return path;
            }
        }

        /// <summary>
        /// 折叠路径中的相对路径部分（如 ../ 和 ./）
        /// 解析并简化路径中的相对引用
        /// </summary>
        /// <param name="path">包含相对路径的路径</param>
        /// <returns>折叠后的路径</returns>
        /// <example>
        /// <code>
        /// var collapsed = CollapseRelativePath(@"C:\folder\subfolder\..\file.txt");
        /// // 结果: @"C:\folder\file.txt"
        /// </code>
        /// </example>
        public string CollapseRelativePath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                // 规范化路径以获得绝对路径，这会自动处理相对路径
                var fullPath = Path.GetFullPath(path);
                return fullPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"折叠相对路径失败: {ex.Message}");

                // 如果 GetFullPath 失败，尝试手动处理
                try
                {
                    return ManualCollapseRelativePath(path);
                }
                catch
                {
                    return path;
                }
            }
        }

        /// <summary>
        /// 手动折叠相对路径（当 Path.GetFullPath 失败时的备用方法）
        /// 这是一个内部辅助方法，用于处理特殊情况
        /// </summary>
        /// <param name="path">要处理的路径</param>
        /// <returns>处理后的路径</returns>
        private string ManualCollapseRelativePath(string path)
        {
            // 分割路径为各个部分
            var parts = path.Split(new[] { '/', '\\' }, StringSplitOptions.RemoveEmptyEntries);
            var stack = new List<string>();

            foreach (var part in parts)
            {
                if (part == ".")
                {
                    // 当前目录，跳过
                    continue;
                }
                else if (part == "..")
                {
                    // 父目录，弹出上一级（如果存在）
                    if (stack.Count > 0 && stack[stack.Count - 1] != "..")
                    {
                        stack.RemoveAt(stack.Count - 1);
                    }
                    else
                    {
                        stack.Add(part);
                    }
                }
                else
                {
                    // 普通目录或文件名
                    stack.Add(part);
                }
            }

            // 重新组合路径
            var separator = path.Contains('/') ? "/" : "\\";
            var result = string.Join(separator, stack);

            // 保持原始路径的根部分（如驱动器字母）
            if (Path.IsPathRooted(path) && !Path.IsPathRooted(result))
            {
                var root = Path.GetPathRoot(path);
                result = root + result;
            }

            return result;
        }

        #endregion

        #region 路径验证和检查

        /// <summary>
        /// 验证路径是否有效
        /// </summary>
        /// <param name="path">要验证的路径</param>
        /// <returns>路径有效返回true</returns>
        public bool IsValidPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // 检查是否包含无效字符
                var invalidChars = Path.GetInvalidPathChars();
                if (path.IndexOfAny(invalidChars) >= 0)
                    return false;

                // 尝试获取完整路径
                Path.GetFullPath(path);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证文件名是否有效
        /// </summary>
        /// <param name="fileName">要验证的文件名</param>
        /// <returns>文件名有效返回true</returns>
        public bool IsValidFileName(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                // 检查是否包含无效字符
                var invalidChars = Path.GetInvalidFileNameChars();
                if (fileName.IndexOfAny(invalidChars) >= 0)
                    return false;

                // 检查是否为保留名称（Windows）
                var reservedNames = new[] { "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };
                var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName).ToUpperInvariant();
                if (reservedNames.Contains(nameWithoutExt))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否为绝对路径
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是绝对路径返回true</returns>
        public bool IsAbsolutePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return false;

            try
            {
                return Path.IsPathRooted(path);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否为相对路径
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是相对路径返回true</returns>
        public bool IsRelativePath(string path)
        {
            return !IsAbsolutePath(path);
        }

        /// <summary>
        /// 检查路径是否安全（不包含危险字符）
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>路径安全返回true</returns>
        public bool IsSafePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return false;

            // 检查路径遍历攻击
            if (path.Contains("..") || path.Contains("~"))
                return false;

            // 检查危险字符
            var dangerousPatterns = new[] { "<", ">", ":", "\"", "|", "?", "*" };
            if (dangerousPatterns.Any(pattern => path.Contains(pattern)))
                return false;

            return IsValidPath(path);
        }

        /// <summary>
        /// 检查是否为网络路径（UNC路径）
        /// 识别形如 \\server\share 或 //server/share 的网络路径
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是网络路径返回true</returns>
        /// <example>
        /// <code>
        /// var isNetwork1 = IsNetworkPath(@"\\server\share\file.txt"); // true
        /// var isNetwork2 = IsNetworkPath("//server/share/file.txt");  // true
        /// var isNetwork3 = IsNetworkPath(@"C:\local\file.txt");       // false
        /// </code>
        /// </example>
        public bool IsNetworkPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // 检查 UNC 路径格式：\\server\share 或 //server/share
                return path.StartsWith(@"\\") || path.StartsWith("//");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查网络路径失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否为 UNC 路径（更严格的网络路径检查）
        /// 验证路径是否符合标准的 UNC 路径格式
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是UNC路径返回true</returns>
        /// <example>
        /// <code>
        /// var isUNC = IsUncPath(@"\\server\share\folder\file.txt"); // true
        /// var notUNC = IsUncPath(@"C:\folder\file.txt");            // false
        /// </code>
        /// </example>
        public bool IsUncPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // UNC 路径必须以 \\ 开头，并且至少包含服务器名和共享名
                if (!path.StartsWith(@"\\"))
                    return false;

                // 移除开头的 \\，然后检查格式
                var pathWithoutPrefix = path.Substring(2);
                var parts = pathWithoutPrefix.Split('\\');

                // 至少需要服务器名和共享名两部分
                return parts.Length >= 2 &&
                       !string.IsNullOrWhiteSpace(parts[0]) &&
                       !string.IsNullOrWhiteSpace(parts[1]);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查UNC路径失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查是否为根路径
        /// 识别各种根路径格式：驱动器根、网络根、Unix根等
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>是根路径返回true</returns>
        /// <example>
        /// <code>
        /// var isRoot1 = IsRootPath(@"C:\");     // true
        /// var isRoot2 = IsRootPath("/");        // true
        /// var isRoot3 = IsRootPath(@"\\server\share\"); // true
        /// var isRoot4 = IsRootPath(@"C:\folder"); // false
        /// </code>
        /// </example>
        public bool IsRootPath(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // 规范化路径
                var normalizedPath = NormalizePath(path);

                // 获取路径根部分
                var root = Path.GetPathRoot(normalizedPath);

                // 比较路径是否等于其根部分
                return string.Equals(normalizedPath, root, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查根路径失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径长度是否超过系统限制
        /// Windows 系统默认路径长度限制为 260 字符
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="maxLength">最大长度限制（默认260字符）</param>
        /// <returns>路径过长返回true</returns>
        /// <example>
        /// <code>
        /// var tooLong = IsPathTooLong(veryLongPath);        // 使用默认260字符限制
        /// var tooLong2 = IsPathTooLong(path, 100);          // 使用自定义100字符限制
        /// </code>
        /// </example>
        public bool IsPathTooLong(string path, int maxLength = 260)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // 检查路径长度
                return path.Length > maxLength;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查路径长度失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否包含非法字符
        /// 检查文件名和路径中的非法字符
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <returns>包含非法字符返回true</returns>
        /// <example>
        /// <code>
        /// var hasInvalid = ContainsInvalidChars("file&lt;name&gt;.txt");  // true
        /// var isValid = ContainsInvalidChars("filename.txt");       // false
        /// </code>
        /// </example>
        public bool ContainsInvalidChars(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return false;

                // 获取路径非法字符
                var invalidPathChars = Path.GetInvalidPathChars();
                var invalidFileNameChars = Path.GetInvalidFileNameChars();

                // 检查路径非法字符
                if (path.IndexOfAny(invalidPathChars) >= 0)
                    return true;

                // 检查文件名部分的非法字符
                var fileName = Path.GetFileName(path);
                if (!string.IsNullOrEmpty(fileName) && fileName.IndexOfAny(invalidFileNameChars) >= 0)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查非法字符失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否在指定目录内（防止目录遍历攻击）
        /// 验证目标路径是否在允许的基础目录范围内
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="baseDirectory">基础目录</param>
        /// <returns>在指定目录内返回true</returns>
        /// <example>
        /// <code>
        /// var isWithin = IsWithinDirectory(@"C:\base\sub\file.txt", @"C:\base"); // true
        /// var isOutside = IsWithinDirectory(@"C:\other\file.txt", @"C:\base");   // false
        /// </code>
        /// </example>
        public bool IsWithinDirectory(string path, string baseDirectory)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(baseDirectory))
                    return false;

                // 获取绝对路径
                var fullPath = Path.GetFullPath(path);
                var fullBaseDirectory = Path.GetFullPath(baseDirectory);

                // 确保基础目录以分隔符结尾
                if (!fullBaseDirectory.EndsWith(Path.DirectorySeparatorChar.ToString()))
                {
                    fullBaseDirectory += Path.DirectorySeparatorChar;
                }

                // 检查路径是否以基础目录开头
                return fullPath.StartsWith(fullBaseDirectory, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查目录包含关系失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 路径信息提取

        /// <summary>
        /// 获取路径的目录部分
        /// </summary>
        /// <param name="path">文件或目录路径</param>
        /// <returns>目录路径</returns>
        public string GetDirectoryName(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetDirectoryName(path) ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取目录名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文件名（包含扩展名）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件名</returns>
        public string GetFileName(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetFileName(path);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文件名（不包含扩展名）
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>不含扩展名的文件名</returns>
        public string GetFileNameWithoutExtension(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetFileNameWithoutExtension(path);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取文件名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns>文件扩展名（包含点号）</returns>
        public string GetExtension(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetExtension(path);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取扩展名失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取路径的根目录
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>根目录路径</returns>
        public string GetPathRoot(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                return Path.GetPathRoot(path) ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取根目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文件的多重扩展名
        /// 处理形如 .tar.gz、.min.js 等多重扩展名的情况
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="maxExtensions">最大扩展名数量（默认3个）</param>
        /// <returns>扩展名数组，按从右到左的顺序</returns>
        /// <example>
        /// <code>
        /// var extensions = GetExtensions("file.tar.gz");
        /// // 结果: [".gz", ".tar"]
        ///
        /// var jsExtensions = GetExtensions("script.min.js");
        /// // 结果: [".js", ".min"]
        /// </code>
        /// </example>
        public string[] GetExtensions(string path, int maxExtensions = 3)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return Array.Empty<string>();

                var fileName = Path.GetFileName(path);
                if (string.IsNullOrWhiteSpace(fileName))
                    return Array.Empty<string>();

                var extensions = new List<string>();
                var currentName = fileName;

                // 从右到左提取扩展名
                for (int i = 0; i < maxExtensions; i++)
                {
                    var extension = Path.GetExtension(currentName);
                    if (string.IsNullOrEmpty(extension))
                        break;

                    extensions.Add(extension);

                    // 移除当前扩展名，继续查找
                    currentName = Path.GetFileNameWithoutExtension(currentName);
                    if (string.IsNullOrEmpty(currentName))
                        break;
                }

                return extensions.ToArray();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取多重扩展名失败: {ex.Message}");
                return Array.Empty<string>();
            }
        }

        /// <summary>
        /// 分解路径为各个组成部分
        /// 将路径分解为驱动器、目录、文件名、扩展名等部分
        /// </summary>
        /// <param name="path">要分解的路径</param>
        /// <returns>路径各部分的详细信息</returns>
        /// <example>
        /// <code>
        /// var parts = GetPathParts(@"C:\Users\<USER>\Documents\file.txt");
        /// // 结果包含: Root="C:\", Directory="Users\Name\Documents", FileName="file.txt", Extension=".txt"
        /// </code>
        /// </example>
        public PathParts GetPathParts(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return new PathParts();

                var fullPath = Path.GetFullPath(path);

                return new PathParts
                {
                    OriginalPath = path,
                    FullPath = fullPath,
                    Root = Path.GetPathRoot(fullPath) ?? string.Empty,
                    Directory = Path.GetDirectoryName(fullPath) ?? string.Empty,
                    FileName = Path.GetFileName(fullPath),
                    FileNameWithoutExtension = Path.GetFileNameWithoutExtension(fullPath),
                    Extension = Path.GetExtension(fullPath),
                    Extensions = GetExtensions(fullPath),
                    IsAbsolute = Path.IsPathRooted(path),
                    IsNetworkPath = IsNetworkPath(path),
                    Segments = SplitPath(fullPath)
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分解路径失败: {ex.Message}");
                return new PathParts { OriginalPath = path };
            }
        }

        /// <summary>
        /// 获取父目录路径
        /// 返回指定路径的上一级目录
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>父目录路径</returns>
        /// <example>
        /// <code>
        /// var parent = GetParentDirectory(@"C:\Users\<USER>\Documents\file.txt");
        /// // 结果: @"C:\Users\<USER>\Documents"
        /// </code>
        /// </example>
        public string GetParentDirectory(string path)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path))
                    return string.Empty;

                var directoryInfo = Directory.GetParent(path);
                return directoryInfo?.FullName ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取父目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取指定层级的祖先目录
        /// 向上追溯指定层数的目录
        /// </summary>
        /// <param name="path">起始路径</param>
        /// <param name="levels">向上层数</param>
        /// <returns>祖先目录路径</returns>
        /// <example>
        /// <code>
        /// var ancestor = GetAncestorDirectory(@"C:\A\B\C\D\file.txt", 2);
        /// // 结果: @"C:\A\B"
        /// </code>
        /// </example>
        public string GetAncestorDirectory(string path, int levels)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || levels < 0)
                    return string.Empty;

                var currentPath = Path.GetFullPath(path);

                // 如果是文件，先获取其目录
                if (File.Exists(currentPath))
                {
                    currentPath = Path.GetDirectoryName(currentPath) ?? string.Empty;
                }

                // 向上追溯指定层数
                for (int i = 0; i < levels; i++)
                {
                    var parent = Directory.GetParent(currentPath);
                    if (parent == null)
                        break;
                    currentPath = parent.FullName;
                }

                return currentPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取祖先目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取多个路径的公共路径
        /// 找到所有路径共同的最长前缀路径
        /// </summary>
        /// <param name="paths">路径数组</param>
        /// <returns>公共路径</returns>
        /// <example>
        /// <code>
        /// var common = GetCommonPath(new[] {
        ///     @"C:\Users\<USER>\Documents\file1.txt",
        ///     @"C:\Users\<USER>\Documents\folder\file2.txt",
        ///     @"C:\Users\<USER>\Pictures\image.jpg"
        /// });
        /// // 结果: @"C:\Users\<USER>\temp\file.txt");
        /// // 如果文件存在，可能返回: @"C:\temp\file_1.txt"
        ///
        /// var uniqueDir = GetUniquePath(@"C:\temp\folder", true);
        /// // 如果目录存在，可能返回: @"C:\temp\folder_1"
        /// </code>
        /// </example>
        public string GetUniquePath(string desiredPath, bool isDirectory = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(desiredPath))
                    return string.Empty;

                // 检查路径是否已存在
                var exists = isDirectory ? Directory.Exists(desiredPath) : File.Exists(desiredPath);
                if (!exists)
                    return desiredPath;

                // 生成唯一路径
                if (isDirectory)
                {
                    return GenerateUniqueDirectoryPath(desiredPath);
                }
                else
                {
                    return GenerateUniqueFilePath(desiredPath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成唯一路径失败: {ex.Message}");
                return desiredPath;
            }
        }

        /// <summary>
        /// 生成唯一文件路径
        /// 内部辅助方法，用于生成不重复的文件路径
        /// </summary>
        /// <param name="originalPath">原始文件路径</param>
        /// <returns>唯一的文件路径</returns>
        private static string GenerateUniqueFilePath(string originalPath)
        {
            var directory = Path.GetDirectoryName(originalPath) ?? "";
            var fileNameWithoutExt = Path.GetFileNameWithoutExtension(originalPath);
            var extension = Path.GetExtension(originalPath);

            int counter = 1;
            string uniquePath;

            do
            {
                var uniqueFileName = $"{fileNameWithoutExt}_{counter}{extension}";
                uniquePath = Path.Combine(directory, uniqueFileName);
                counter++;
            }
            while (File.Exists(uniquePath) && counter < 10000); // 防止无限循环

            return uniquePath;
        }

        /// <summary>
        /// 生成唯一目录路径
        /// 内部辅助方法，用于生成不重复的目录路径
        /// </summary>
        /// <param name="originalPath">原始目录路径</param>
        /// <returns>唯一的目录路径</returns>
        private static string GenerateUniqueDirectoryPath(string originalPath)
        {
            var parentDirectory = Path.GetDirectoryName(originalPath) ?? "";
            var directoryName = Path.GetFileName(originalPath);

            int counter = 1;
            string uniquePath;

            do
            {
                var uniqueDirectoryName = $"{directoryName}_{counter}";
                uniquePath = Path.Combine(parentDirectory, uniqueDirectoryName);
                counter++;
            }
            while (Directory.Exists(uniquePath) && counter < 10000); // 防止无限循环

            return uniquePath;
        }

        /// <summary>
        /// 生成唯一文件名（仅文件名部分）
        /// 在指定目录中生成不重复的文件名
        /// </summary>
        /// <param name="directory">目录路径</param>
        /// <param name="baseFileName">基础文件名</param>
        /// <param name="extension">文件扩展名</param>
        /// <returns>唯一的文件名</returns>
        /// <example>
        /// <code>
        /// var uniqueName = GetUniqueFileName(@"C:\temp", "document", ".txt");
        /// // 可能返回: "document_1.txt"
        /// </code>
        /// </example>
        public string GetUniqueFileName(string directory, string baseFileName, string extension = "")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(directory) || string.IsNullOrWhiteSpace(baseFileName))
                    return string.Empty;

                // 确保扩展名格式正确
                if (!string.IsNullOrEmpty(extension) && !extension.StartsWith("."))
                    extension = "." + extension;

                var originalPath = Path.Combine(directory, baseFileName + extension);

                if (!File.Exists(originalPath))
                    return baseFileName + extension;

                // 生成唯一文件名
                var uniquePath = GenerateUniqueFilePath(originalPath);
                return Path.GetFileName(uniquePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成唯一文件名失败: {ex.Message}");
                return baseFileName + extension;
            }
        }

        /// <summary>
        /// 生成唯一目录名（仅目录名部分）
        /// 在指定父目录中生成不重复的目录名
        /// </summary>
        /// <param name="parentDirectory">父目录路径</param>
        /// <param name="baseDirectoryName">基础目录名</param>
        /// <returns>唯一的目录名</returns>
        /// <example>
        /// <code>
        /// var uniqueName = GetUniqueDirectoryName(@"C:\temp", "folder");
        /// // 可能返回: "folder_1"
        /// </code>
        /// </example>
        public string GetUniqueDirectoryName(string parentDirectory, string baseDirectoryName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(parentDirectory) || string.IsNullOrWhiteSpace(baseDirectoryName))
                    return string.Empty;

                var originalPath = Path.Combine(parentDirectory, baseDirectoryName);

                if (!Directory.Exists(originalPath))
                    return baseDirectoryName;

                // 生成唯一目录名
                var uniquePath = GenerateUniqueDirectoryPath(originalPath);
                return Path.GetFileName(uniquePath) ?? baseDirectoryName;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成唯一目录名失败: {ex.Message}");
                return baseDirectoryName;
            }
        }

        /// <summary>
        /// 生成随机路径
        /// 使用 GUID 生成完全随机的路径名
        /// </summary>
        /// <param name="baseDirectory">基础目录</param>
        /// <param name="extension">文件扩展名（可选）</param>
        /// <param name="prefix">前缀（可选）</param>
        /// <returns>随机路径</returns>
        /// <example>
        /// <code>
        /// var randomFile = GenerateRandomPath(@"C:\temp", ".txt", "data");
        /// // 可能返回: @"C:\temp\data_a1b2c3d4e5f6.txt"
        /// </code>
        /// </example>
        public string GenerateRandomPath(string baseDirectory, string extension = "", string prefix = "")
        {
            try
            {
                if (string.IsNullOrWhiteSpace(baseDirectory))
                    return string.Empty;

                // 生成随机名称
                var randomName = Guid.NewGuid().ToString("N")[..12]; // 取前12位

                // 组合前缀和随机名称
                var fileName = string.IsNullOrEmpty(prefix) ? randomName : $"{prefix}_{randomName}";

                // 添加扩展名
                if (!string.IsNullOrEmpty(extension) && !extension.StartsWith("."))
                    extension = "." + extension;

                fileName += extension;

                return Path.Combine(baseDirectory, fileName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成随机路径失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 创建临时路径（文件或目录）
        /// 不仅生成路径，还会实际创建文件或目录
        /// </summary>
        /// <param name="isDirectory">是否创建目录</param>
        /// <param name="prefix">前缀</param>
        /// <param name="extension">文件扩展名（仅当isDirectory为false时有效）</param>
        /// <returns>创建的临时路径</returns>
        /// <example>
        /// <code>
        /// var tempFile = CreateTempPath(false, "work", ".tmp");
        /// var tempDir = CreateTempPath(true, "workspace");
        /// </code>
        /// </example>
        public string CreateTempPath(bool isDirectory, string prefix = "temp", string extension = ".tmp")
        {
            try
            {
                var tempBasePath = Path.GetTempPath();
                string tempPath;

                if (isDirectory)
                {
                    tempPath = GetTempDirectoryPath(prefix);
                    Directory.CreateDirectory(tempPath);
                }
                else
                {
                    tempPath = GetTempFileName(extension);
                    // 创建空文件
                    File.WriteAllText(tempPath, string.Empty);
                }

                return tempPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建临时路径失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取当前工作目录
        /// </summary>
        /// <returns>当前工作目录路径</returns>
        public string GetCurrentDirectory()
        {
            try
            {
                return Directory.GetCurrentDirectory();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取当前目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取应用程序目录
        /// </summary>
        /// <returns>应用程序目录路径</returns>
        public string GetApplicationDirectory()
        {
            try
            {
                return AppDomain.CurrentDomain.BaseDirectory;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取应用程序目录失败: {ex.Message}");
                return string.Empty;
            }
        }

        #endregion

        #region 路径比较和匹配

        /// <summary>
        /// 比较两个路径是否相等
        /// </summary>
        /// <param name="path1">路径1</param>
        /// <param name="path2">路径2</param>
        /// <param name="ignoreCase">是否忽略大小写</param>
        /// <returns>路径相等返回true</returns>
        public bool PathEquals(string path1, string path2, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path1) && string.IsNullOrWhiteSpace(path2))
                    return true;

                if (string.IsNullOrWhiteSpace(path1) || string.IsNullOrWhiteSpace(path2))
                    return false;

                var normalizedPath1 = NormalizePath(GetAbsolutePath(path1));
                var normalizedPath2 = NormalizePath(GetAbsolutePath(path2));

                return string.Equals(normalizedPath1, normalizedPath2,
                    ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"路径比较失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否匹配通配符模式
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="pattern">通配符模式（支持 * 和 ?）</param>
        /// <param name="ignoreCase">是否忽略大小写</param>
        /// <returns>匹配返回true</returns>
        public bool MatchesPattern(string path, string pattern, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(pattern))
                    return false;

                // 转换通配符模式为正则表达式
                var regexPattern = "^" + Regex.Escape(pattern)
                    .Replace("\\*", ".*")
                    .Replace("\\?", ".") + "$";

                var options = ignoreCase ? RegexOptions.IgnoreCase : RegexOptions.None;
                return Regex.IsMatch(path, regexPattern, options);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"模式匹配失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否在指定目录内
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="parentDirectory">父目录</param>
        /// <returns>在父目录内返回true</returns>
        public bool IsPathInDirectory(string path, string parentDirectory)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(parentDirectory))
                    return false;

                var fullPath = GetAbsolutePath(path);
                var fullParentPath = GetAbsolutePath(parentDirectory);

                return fullPath.StartsWith(fullParentPath, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查路径包含关系失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否以指定前缀开始
        /// 支持大小写敏感和不敏感的比较
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="prefix">路径前缀</param>
        /// <param name="ignoreCase">是否忽略大小写（默认true）</param>
        /// <returns>以指定前缀开始返回true</returns>
        /// <example>
        /// <code>
        /// var starts = PathStartsWith(@"C:\Users\<USER>\file.txt", @"C:\Users"); // true
        /// var notStarts = PathStartsWith(@"D:\file.txt", @"C:\Users");         // false
        /// </code>
        /// </example>
        public bool PathStartsWith(string path, string prefix, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(prefix))
                    return false;

                // 规范化路径
                var normalizedPath = NormalizePath(GetAbsolutePath(path));
                var normalizedPrefix = NormalizePath(GetAbsolutePath(prefix));

                // 确保前缀以分隔符结尾（避免部分匹配）
                if (!normalizedPrefix.EndsWith(Path.DirectorySeparatorChar.ToString()))
                {
                    normalizedPrefix += Path.DirectorySeparatorChar;
                }

                var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
                return normalizedPath.StartsWith(normalizedPrefix, comparison);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查路径前缀失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否以指定后缀结束
        /// 通常用于检查文件扩展名或路径模式
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="suffix">路径后缀</param>
        /// <param name="ignoreCase">是否忽略大小写（默认true）</param>
        /// <returns>以指定后缀结束返回true</returns>
        /// <example>
        /// <code>
        /// var endsWithTxt = PathEndsWith("document.txt", ".txt");     // true
        /// var endsWithBin = PathEndsWith(@"C:\app\bin", "bin");       // true
        /// </code>
        /// </example>
        public bool PathEndsWith(string path, string suffix, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(suffix))
                    return false;

                var normalizedPath = NormalizePath(path);
                var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

                return normalizedPath.EndsWith(suffix, comparison);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查路径后缀失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否包含指定的子字符串
        /// 可用于检查路径中是否包含特定的目录或文件名
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="substring">要查找的子字符串</param>
        /// <param name="ignoreCase">是否忽略大小写（默认true）</param>
        /// <returns>包含指定子字符串返回true</returns>
        /// <example>
        /// <code>
        /// var contains = PathContains(@"C:\Users\<USER>\Documents", "Users");  // true
        /// var notContains = PathContains(@"C:\Program Files", "Users");      // false
        /// </code>
        /// </example>
        public bool PathContains(string path, string substring, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(substring))
                    return false;

                var normalizedPath = NormalizePath(path);
                var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;

                return normalizedPath.Contains(substring, comparison);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查路径包含失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 高级通配符匹配（支持 ** 递归匹配）
        /// 支持更复杂的通配符模式，如 **/*.txt, src/**/*.cs 等
        /// </summary>
        /// <param name="path">要检查的路径</param>
        /// <param name="pattern">通配符模式</param>
        /// <param name="ignoreCase">是否忽略大小写（默认true）</param>
        /// <returns>匹配返回true</returns>
        /// <example>
        /// <code>
        /// var matches1 = MatchesAdvancedPattern(@"src\utils\helper.cs", "**/*.cs");        // true
        /// var matches2 = MatchesAdvancedPattern(@"docs\readme.txt", "docs/**");            // true
        /// var matches3 = MatchesAdvancedPattern(@"test\unit\*.test.js", "test/**/*.js");   // true
        /// </code>
        /// </example>
        public bool MatchesAdvancedPattern(string path, string pattern, bool ignoreCase = true)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path) || string.IsNullOrWhiteSpace(pattern))
                    return false;

                // 规范化路径和模式
                var normalizedPath = NormalizePath(path).Replace('\\', '/');
                var normalizedPattern = pattern.Replace('\\', '/');

                // 转换高级通配符为正则表达式
                var regexPattern = ConvertAdvancedPatternToRegex(normalizedPattern);

                var options = RegexOptions.None;
                if (ignoreCase)
                    options |= RegexOptions.IgnoreCase;

                return Regex.IsMatch(normalizedPath, regexPattern, options);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"高级模式匹配失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 将高级通配符模式转换为正则表达式
        /// 这是一个内部辅助方法，用于处理复杂的通配符模式
        /// </summary>
        /// <param name="pattern">通配符模式</param>
        /// <returns>对应的正则表达式</returns>
        private static string ConvertAdvancedPatternToRegex(string pattern)
        {
            // 转义正则表达式特殊字符（除了我们要处理的通配符）
            var escaped = Regex.Escape(pattern);

            // 恢复通配符并转换为正则表达式
            escaped = escaped
                .Replace("\\*\\*", "DOUBLE_STAR_PLACEHOLDER")  // 临时占位符
                .Replace("\\*", "[^/]*")                       // * 匹配除路径分隔符外的任意字符
                .Replace("DOUBLE_STAR_PLACEHOLDER", ".*")      // ** 匹配任意字符包括路径分隔符
                .Replace("\\?", "[^/]");                       // ? 匹配除路径分隔符外的单个字符

            return "^" + escaped + "$";
        }

        /// <summary>
        /// 比较两个路径的深度
        /// 返回路径深度的比较结果
        /// </summary>
        /// <param name="path1">第一个路径</param>
        /// <param name="path2">第二个路径</param>
        /// <returns>负数表示path1较浅，0表示相等，正数表示path1较深</returns>
        /// <example>
        /// <code>
        /// var result = ComparePathDepth(@"C:\A\B", @"C:\A\B\C\D");
        /// // 结果: -2 (第一个路径比第二个浅2层)
        /// </code>
        /// </example>
        public int ComparePathDepth(string path1, string path2)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(path1) && string.IsNullOrWhiteSpace(path2))
                    return 0;

                if (string.IsNullOrWhiteSpace(path1))
                    return -1;

                if (string.IsNullOrWhiteSpace(path2))
                    return 1;

                var depth1 = GetPathDepth(path1);
                var depth2 = GetPathDepth(path2);

                return depth1.CompareTo(depth2);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"比较路径深度失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 查找多个路径的公共祖先目录
        /// 这是 GetCommonPath 的别名方法，提供更直观的命名
        /// </summary>
        /// <param name="paths">路径数组</param>
        /// <returns>公共祖先目录</returns>
        /// <example>
        /// <code>
        /// var ancestor = FindCommonAncestor(
        ///     @"C:\Projects\App1\src\file1.cs",
        ///     @"C:\Projects\App1\tests\test1.cs",
        ///     @"C:\Projects\App1\docs\readme.md"
        /// );
        /// // 结果: @"C:\Projects\App1"
        /// </code>
        /// </example>
        public string FindCommonAncestor(params string[] paths)
        {
            return GetCommonPath(paths);
        }

        #endregion
    }

    /// <summary>
    /// 路径组成部分的详细信息
    /// 包含路径的各个组成部分和相关属性
    /// </summary>
    public class PathParts
    {
        /// <summary>
        /// 原始路径
        /// </summary>
        public string OriginalPath { get; set; } = string.Empty;

        /// <summary>
        /// 完整绝对路径
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 路径根部分（如 "C:\" 或 "/"）
        /// </summary>
        public string Root { get; set; } = string.Empty;

        /// <summary>
        /// 目录部分
        /// </summary>
        public string Directory { get; set; } = string.Empty;

        /// <summary>
        /// 完整文件名（包含扩展名）
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 不含扩展名的文件名
        /// </summary>
        public string FileNameWithoutExtension { get; set; } = string.Empty;

        /// <summary>
        /// 主扩展名
        /// </summary>
        public string Extension { get; set; } = string.Empty;

        /// <summary>
        /// 所有扩展名（多重扩展名）
        /// </summary>
        public string[] Extensions { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 是否为绝对路径
        /// </summary>
        public bool IsAbsolute { get; set; }

        /// <summary>
        /// 是否为网络路径
        /// </summary>
        public bool IsNetworkPath { get; set; }

        /// <summary>
        /// 路径分段
        /// </summary>
        public string[] Segments { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 路径深度（分段数量）
        /// </summary>
        public int Depth => Segments?.Length ?? 0;

        /// <summary>
        /// 是否为文件路径（有扩展名）
        /// </summary>
        public bool IsFile => !string.IsNullOrEmpty(Extension);

        /// <summary>
        /// 是否为目录路径
        /// </summary>
        public bool IsDirectory => !IsFile;

        /// <summary>
        /// 返回路径信息的字符串表示
        /// </summary>
        /// <returns>格式化的路径信息</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"Original: {OriginalPath}");
            sb.AppendLine($"Full: {FullPath}");
            sb.AppendLine($"Root: {Root}");
            sb.AppendLine($"Directory: {Directory}");
            sb.AppendLine($"FileName: {FileName}");
            sb.AppendLine($"Extension: {Extension}");
            sb.AppendLine($"IsAbsolute: {IsAbsolute}");
            sb.AppendLine($"IsNetwork: {IsNetworkPath}");
            sb.AppendLine($"Depth: {Depth}");

            if (Extensions.Length > 1)
            {
                sb.AppendLine($"Extensions: [{string.Join(", ", Extensions)}]");
            }

            return sb.ToString();
        }
    }
}
