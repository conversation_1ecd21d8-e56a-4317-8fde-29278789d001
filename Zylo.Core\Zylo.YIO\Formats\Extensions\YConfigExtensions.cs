using System;
using System.IO;
using Zylo.YIO.Formats;

namespace Zylo.YIO.Extensions
{
    /// <summary>
    /// 配置扩展方法 - 重构版本
    ///
    /// 🎯 功能特性：
    /// • 简洁的链式调用语法
    /// • 自动路径推断和处理
    /// • 类型安全的配置操作
    /// • 灵活的文件路径配置
    /// • 完善的错误处理
    ///
    /// 🔧 使用场景：
    /// • 快速配置文件操作
    /// • 原型开发和测试
    /// • 简化的配置管理
    /// • 链式调用风格编程
    ///
    /// 📖 使用示例：
    /// <code>
    /// var config = new AppConfig { Name = "MyApp", Version = "1.0" };
    ///
    /// // 扩展方法调用
    /// config.YSave();                              // 保存到默认位置
    /// config.YSave("custom.json");                 // 保存到指定文件
    /// config.YSave(null, "./configs");             // 保存到指定目录
    ///
    /// // 加载配置
    /// var loaded = config.YLoad();                 // 从默认位置加载
    /// var loaded2 = config.YLoad("custom.json");   // 从指定文件加载
    ///
    /// // 静态方法
    /// YConfigHelper.Save(config, "backup.json");
    /// var restored = YConfigHelper.Load&lt;AppConfig&gt;("backup.json");
    /// </code>
    /// </summary>
    public static class YConfigExtensions
    {
        #region 实例扩展方法

        /// <summary>
        /// 保存对象为 JSON 文件 (YSave 扩展方法)
        ///
        /// 这是最常用的配置保存方法，提供简洁的链式调用语法。
        /// 支持自动路径推断和灵活的文件路径配置。
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要保存的对象</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>保存是否成功</returns>
        public static bool YSave<T>(this T obj, string? filePath = null, string? directory = null) where T : class
        {
            try
            {
                // 第一步：智能确定最终的文件保存路径
                // 支持三种模式：完整路径、目录+自动文件名、默认路径
                var finalPath = DetermineFilePath<T>(filePath, directory);

                // 第二步：创建 JSON 处理器实例并执行保存操作
                // 使用 JSON 格式确保跨平台兼容性和易读性
                var jsonProcessor = new YJsonProcessor();
                return jsonProcessor.WriteJson(finalPath, obj);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ YSave 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从 JSON 文件加载对象
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">对象实例（用于类型推断）</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>加载的对象，失败时返回 null</returns>
        public static T? YLoad<T>(this T obj, string? filePath = null, string? directory = null) where T : class
        {
            try
            {
                // 第一步：智能确定要加载的文件路径
                // 使用与 YSave 相同的路径确定逻辑，确保一致性
                var finalPath = DetermineFilePath<T>(filePath, directory);

                // 第二步：创建 JSON 处理器并执行加载操作
                // 返回强类型对象，保持类型安全
                var jsonProcessor = new YJsonProcessor();
                return jsonProcessor.ReadJson<T>(finalPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ YLoad 失败: {ex.Message}");
                return null; // 加载失败时返回 null，调用方需要检查
            }
        }

        /// <summary>
        /// 静态保存方法
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要保存的对象</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>保存是否成功</returns>
        public static bool Save<T>(T obj, string? filePath = null, string? directory = null) where T : class
        {
            return obj.YSave(filePath, directory);
        }

        /// <summary>
        /// 静态加载方法
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>加载的对象，失败时返回 null</returns>
        public static T? Load<T>(string? filePath = null, string? directory = null) where T : class, new()
        {
            try
            {
                // 确定文件路径
                var finalPath = DetermineFilePath<T>(filePath, directory);

                // 使用 JSON 处理器加载
                var jsonProcessor = new YJsonProcessor();
                return jsonProcessor.ReadJson<T>(finalPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Load 失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 确定最终的文件路径
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="filePath">指定的文件路径</param>
        /// <param name="directory">指定的目录</param>
        /// <returns>最终的文件路径</returns>
        private static string DetermineFilePath<T>(string? filePath, string? directory)
        {
            // 优先级1：如果提供了完整的文件路径，直接使用（最高优先级）
            if (!string.IsNullOrEmpty(filePath))
            {
                return filePath;
            }

            // 优先级2：确定目标目录，如果未指定则使用默认的 ./configs 目录
            var targetDirectory = directory ?? "./configs";

            // 优先级3：自动生成文件名，基于类型名称转换为小写并添加 .json 扩展名
            // 例如：AppConfig → appconfig.json, UserSettings → usersettings.json
            var typeName = typeof(T).Name;
            var fileName = $"{typeName.ToLowerInvariant()}.json";

            // 最终步骤：组合目录和文件名，生成完整路径
            return Path.Combine(targetDirectory, fileName);
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">对象实例</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>文件是否存在</returns>
        public static bool YExists<T>(this T obj, string? filePath = null, string? directory = null) where T : class
        {
            try
            {
                var finalPath = DetermineFilePath<T>(filePath, directory);
                return File.Exists(finalPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ YExists 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除配置文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">对象实例</param>
        /// <param name="filePath">文件路径（可选）</param>
        /// <param name="directory">目录路径（可选）</param>
        /// <returns>删除是否成功</returns>
        public static bool YDelete<T>(this T obj, string? filePath = null, string? directory = null) where T : class
        {
            try
            {
                var finalPath = DetermineFilePath<T>(filePath, directory);

                if (File.Exists(finalPath))
                {
                    File.Delete(finalPath);
                    Console.WriteLine($"✅ 配置文件已删除: {finalPath}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"⚠️ 配置文件不存在: {finalPath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ YDelete 失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// 静态配置助手类
    ///
    /// 提供静态方法形式的配置操作接口，
    /// 适用于不使用扩展方法的场景
    /// </summary>
    public static class YConfigHelper
    {
        #region 静态方法
        /// <summary>
        /// 静态保存方法
        /// </summary>
        public static bool Save<T>(T obj, string? filePath = null, string? directory = null) where T : class
        {
            return obj.YSave(filePath, directory);
        }

        /// <summary>
        /// 静态加载方法
        /// </summary>
        public static T? Load<T>(string? filePath = null, string? directory = null) where T : class, new()
        {
            return YConfigExtensions.Load<T>(filePath, directory);
        }

        /// <summary>
        /// 静态存在检查方法
        /// </summary>
        public static bool Exists<T>(string? filePath = null, string? directory = null) where T : class, new()
        {
            try
            {
                var obj = new T();
                return obj.YExists(filePath, directory);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exists 失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 静态删除方法
        /// </summary>
        public static bool Delete<T>(string? filePath = null, string? directory = null) where T : class, new()
        {
            try
            {
                var obj = new T();
                return obj.YDelete(filePath, directory);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Delete 失败: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
