using System;
using System.IO;
using System.Collections.Generic;
using System.Xml.Linq;
using Zylo.YIO.Config;

namespace Zylo.YIO.Formats
{
    /// <summary>
    /// 统一配置管理器
    ///
    /// 🎯 功能特性：
    /// • 多格式统一接口（JSON、XML、INI）
    /// • 自动格式检测和处理
    /// • 动态配置加载
    /// • 格式验证和错误处理
    /// • 处理器组合和协调
    ///
    /// 🔧 使用场景：
    /// • 多格式配置文件管理
    /// • 配置文件格式转换
    /// • 统一配置访问接口
    /// • 企业级配置管理
    ///
    /// 📖 使用示例：
    /// <code>
    /// var manager = new YConfigManager(
    ///     new YJsonProcessor(),
    ///     new YXmlProcessor(),
    ///     new YIniProcessor()
    /// );
    ///
    /// // 自动检测格式并加载
    /// var config = manager.Load&lt;AppConfig&gt;("config.json");
    ///
    /// // 动态加载
    /// dynamic dynConfig = manager.LoadDynamic("config.xml");
    ///
    /// // 格式检测
    /// var format = manager.DetectFormat("unknown.config");
    /// </code>
    /// </summary>
    [YServiceScoped]
    public class YConfigManager
    {
        #region 私有字段

        /// <summary>
        /// JSON 处理器实例
        /// </summary>
        private readonly YJsonProcessor _jsonProcessor;

        /// <summary>
        /// XML 处理器实例
        /// </summary>
        private readonly YXmlProcessor _xmlProcessor;

        /// <summary>
        /// INI 处理器实例
        /// </summary>
        private readonly YIniProcessor _iniProcessor;

        /// <summary>
        /// YIO 配置实例
        /// </summary>
        private readonly YIOConfig _config;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化统一配置管理器
        ///
        /// 通过依赖注入的方式组合各个专用处理器，
        /// 提供统一的配置文件管理接口
        /// </summary>
        /// <param name="jsonProcessor">JSON 处理器实例</param>
        /// <param name="xmlProcessor">XML 处理器实例</param>
        /// <param name="iniProcessor">INI 处理器实例</param>
        /// <param name="config">YIO 配置，为 null 时使用默认配置</param>
        /// <remarks>
        /// 构造器采用依赖注入模式，确保：
        /// • 各处理器的独立性和可测试性
        /// • 配置的一致性管理
        /// • 扩展性和可维护性
        /// </remarks>
        /// <example>
        /// <code>
        /// // 手动创建
        /// var manager = new YConfigManager(
        ///     new YJsonProcessor(),
        ///     new YXmlProcessor(),
        ///     new YIniProcessor()
        /// );
        ///
        /// // 或通过 DI 容器注入
        /// services.AddScoped&lt;YConfigManager&gt;();
        /// </code>
        /// </example>
        public YConfigManager(
            YJsonProcessor jsonProcessor,
            YXmlProcessor xmlProcessor,
            YIniProcessor iniProcessor,
            YIOConfig? config = null)
        {
            _jsonProcessor = jsonProcessor;
            _xmlProcessor = xmlProcessor;
            _iniProcessor = iniProcessor;
            _config = config ?? new YIOConfig();
        }

        #endregion

        #region 配置加载方法

        /// <summary>
        /// 自动检测格式并加载配置对象
        ///
        /// 根据文件扩展名和内容自动选择合适的处理器进行加载，
        /// 提供统一的配置加载接口
        /// </summary>
        /// <typeparam name="T">配置对象类型，必须是引用类型</typeparam>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>加载的配置对象实例，失败时返回 null</returns>
        /// <remarks>
        /// 支持的格式：
        /// • .json - 使用 YJsonProcessor 进行强类型反序列化
        /// • .xml/.config - 使用 YXmlProcessor（需要自定义实现）
        /// • .ini/.cfg - 使用 YIniProcessor（需要自定义实现）
        ///
        /// 注意：目前只有 JSON 格式支持强类型对象的直接反序列化
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        ///
        /// // 自动检测格式并加载
        /// var config = manager.Load&lt;AppConfig&gt;("config.json");
        /// if (config != null)
        /// {
        ///     Console.WriteLine($"应用名称: {config.Name}");
        /// }
        /// </code>
        /// </example>
        public T? Load<T>(string filePath) where T : class
        {
            try
            {
                // 第一步：自动检测配置文件格式（JSON/XML/INI）
                var format = DetectFormat(filePath);

                // 第二步：根据检测到的格式选择相应的处理器进行加载
                return format switch
                {
                    // JSON 格式：使用 JSON 处理器进行强类型反序列化
                    ConfigFileFormat.Json => _jsonProcessor.ReadJson<T>(filePath),

                    // XML 格式：目前不支持直接转换为强类型对象，需要自定义实现
                    ConfigFileFormat.Xml => throw new NotSupportedException("XML 到强类型对象的转换需要自定义实现"),

                    // INI 格式：目前不支持直接转换为强类型对象，需要自定义实现
                    ConfigFileFormat.Ini => throw new NotSupportedException("INI 到强类型对象的转换需要自定义实现"),

                    // 未知格式：抛出不支持异常
                    _ => throw new NotSupportedException($"不支持的文件格式: {format}")
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 配置加载失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 配置保存方法

        /// <summary>
        /// 自动检测格式并保存配置对象
        ///
        /// 根据文件扩展名自动选择合适的处理器进行保存，
        /// 提供统一的配置保存接口
        /// </summary>
        /// <typeparam name="T">配置对象类型，必须是引用类型</typeparam>
        /// <param name="filePath">目标文件路径</param>
        /// <param name="data">要保存的配置对象</param>
        /// <returns>保存成功返回 true，失败返回 false</returns>
        /// <remarks>
        /// 支持的格式：
        /// • .json - 使用 YJsonProcessor
        /// • .xml/.config - 使用 YXmlProcessor（需要自定义实现）
        /// • .ini/.cfg - 使用 YIniProcessor（需要自定义实现）
        ///
        /// 注意：目前只有 JSON 格式支持强类型对象的直接序列化
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        /// var config = new AppConfig { Name = "MyApp", Version = "1.0" };
        ///
        /// // 自动检测为 JSON 格式并保存
        /// bool success = manager.Save("config.json", config);
        /// </code>
        /// </example>
        public bool Save<T>(string filePath, T data) where T : class
        {
            try
            {
                // 根据文件扩展名确定格式，而不是检测文件内容
                var format = DetectFormatByExtension(filePath);

                return format switch
                {
                    ConfigFileFormat.Json => _jsonProcessor.WriteJson(filePath, data),
                    ConfigFileFormat.Xml => throw new NotSupportedException("强类型对象到 XML 的转换需要自定义实现"),
                    ConfigFileFormat.Ini => throw new NotSupportedException("强类型对象到 INI 的转换需要自定义实现"),
                    _ => throw new NotSupportedException($"不支持的文件格式: {format}")
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 配置保存失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 动态配置加载方法

        /// <summary>
        /// 读取动态配置对象
        ///
        /// 将配置文件内容转换为动态对象，无需预定义类型，
        /// 适用于配置结构不固定或需要灵活访问的场景
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>动态配置对象，失败时返回 null</returns>
        /// <remarks>
        /// 返回类型说明：
        /// • JSON 格式 → ExpandoObject（支持动态属性访问）
        /// • XML 格式 → Dictionary&lt;string, object&gt;（嵌套字典结构）
        /// • INI 格式 → Dictionary&lt;string, object&gt;（扁平化字典结构）
        ///
        /// 使用建议：
        /// • 适用于配置结构经常变化的场景
        /// • 适用于原型开发和调试
        /// • 不适用于性能敏感的生产环境
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        ///
        /// // 动态加载 JSON 配置
        /// dynamic config = manager.LoadDynamic("config.json");
        /// if (config != null)
        /// {
        ///     Console.WriteLine($"应用名称: {config.Name}");
        ///     Console.WriteLine($"版本: {config.Version}");
        /// }
        ///
        /// // 动态加载 XML 配置
        /// var xmlConfig = manager.LoadDynamic("config.xml") as Dictionary&lt;string, object&gt;;
        /// if (xmlConfig != null && xmlConfig.ContainsKey("AppName"))
        /// {
        ///     Console.WriteLine($"应用名称: {xmlConfig["AppName"]}");
        /// }
        /// </code>
        /// </example>
        public dynamic? LoadDynamic(string filePath)
        {
            try
            {
                var format = DetectFormat(filePath);

                return format switch
                {
                    ConfigFileFormat.Json => _jsonProcessor.ReadJsonDynamic(filePath),
                    ConfigFileFormat.Xml => _xmlProcessor.ReadXmlAsDictionary(filePath),
                    ConfigFileFormat.Ini => _iniProcessor.ReadIniFileAsDictionary(filePath),
                    _ => throw new NotSupportedException($"不支持的文件格式: {format}")
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 动态配置加载失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 配置验证方法

        /// <summary>
        /// 验证配置文件格式是否正确
        ///
        /// 根据文件格式自动选择相应的验证器进行格式验证，
        /// 确保配置文件可以被正确解析
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>验证通过返回 true，失败返回 false</returns>
        /// <remarks>
        /// 验证过程：
        /// • 自动检测文件格式
        /// • 调用相应处理器的验证方法
        /// • 返回验证结果
        ///
        /// 验证内容：
        /// • JSON：语法正确性、结构完整性
        /// • XML：格式正确性、标签匹配
        /// • INI：节和键值对格式
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        ///
        /// if (manager.Validate("config.json"))
        /// {
        ///     var config = manager.Load&lt;AppConfig&gt;("config.json");
        ///     // 安全地使用配置
        /// }
        /// else
        /// {
        ///     Console.WriteLine("配置文件格式无效");
        /// }
        /// </code>
        /// </example>
        public bool Validate(string filePath)
        {
            try
            {
                var format = DetectFormat(filePath);

                return format switch
                {
                    ConfigFileFormat.Json => _jsonProcessor.ValidateJson(filePath),
                    ConfigFileFormat.Xml => _xmlProcessor.ValidateXml(filePath),
                    ConfigFileFormat.Ini => File.Exists(filePath), // INI 格式相对宽松
                    _ => false
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 配置验证失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 格式检测方法

        /// <summary>
        /// 检测配置文件格式
        ///
        /// 通过文件扩展名和内容分析自动检测配置文件的格式类型，
        /// 为后续的处理器选择提供依据
        /// </summary>
        /// <param name="filePath">配置文件路径</param>
        /// <returns>检测到的文件格式枚举值</returns>
        /// <remarks>
        /// 检测规则：
        /// 1. 首先检查文件是否存在
        /// 2. 根据文件扩展名进行初步判断
        /// 3. 如果扩展名不明确，尝试解析文件内容
        /// 4. 返回最匹配的格式类型
        ///
        /// 支持的格式：
        /// • JSON：.json 扩展名或 JSON 语法内容
        /// • XML：.xml/.config 扩展名或 XML 标签内容
        /// • INI：.ini/.cfg 扩展名或节/键值对内容
        /// • Unknown：无法识别的格式
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        ///
        /// var format = manager.DetectFormat("config.json");
        /// switch (format)
        /// {
        ///     case ConfigFileFormat.Json:
        ///         Console.WriteLine("这是一个 JSON 配置文件");
        ///         break;
        ///     case ConfigFileFormat.Xml:
        ///         Console.WriteLine("这是一个 XML 配置文件");
        ///         break;
        ///     case ConfigFileFormat.Ini:
        ///         Console.WriteLine("这是一个 INI 配置文件");
        ///         break;
        ///     default:
        ///         Console.WriteLine("未知的配置文件格式");
        ///         break;
        /// }
        /// </code>
        /// </example>
        public ConfigFileFormat DetectFormat(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ 文件不存在，无法检测格式: {filePath}");
                    return ConfigFileFormat.Unknown;
                }

                // 首先根据扩展名判断
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                switch (extension)
                {
                    case ".json":
                        return ConfigFileFormat.Json;
                    case ".xml":
                    case ".config":
                        return ConfigFileFormat.Xml;
                    case ".ini":
                    case ".cfg":
                        return ConfigFileFormat.Ini;
                }

                // 如果扩展名无法确定，则读取文件内容进行判断
                var content = File.ReadAllText(filePath, System.Text.Encoding.UTF8).Trim();
                if (string.IsNullOrEmpty(content))
                {
                    Console.WriteLine($"⚠️ 文件为空，无法检测格式: {filePath}");
                    return ConfigFileFormat.Unknown;
                }

                // 检测 JSON 格式
                if (IsJsonFormat(content))
                {
                    Console.WriteLine($"✅ 检测为 JSON 格式: {filePath}");
                    return ConfigFileFormat.Json;
                }

                // 检测 XML 格式
                if (IsXmlFormat(content))
                {
                    Console.WriteLine($"✅ 检测为 XML 格式: {filePath}");
                    return ConfigFileFormat.Xml;
                }

                // 检测 INI 格式
                if (IsIniFormat(content))
                {
                    Console.WriteLine($"✅ 检测为 INI 格式: {filePath}");
                    return ConfigFileFormat.Ini;
                }

                Console.WriteLine($"⚠️ 无法识别文件格式: {filePath}");
                return ConfigFileFormat.Unknown;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 格式检测失败 {filePath}: {ex.Message}");
                return ConfigFileFormat.Unknown;
            }
        }

        #endregion

        #region 处理器访问属性

        /// <summary>
        /// 获取 JSON 处理器实例
        ///
        /// 提供对内部 JSON 处理器的直接访问，
        /// 用于需要使用特定 JSON 功能的场景
        /// </summary>
        /// <value>YJsonProcessor 实例</value>
        /// <remarks>
        /// 使用场景：
        /// • 需要使用 JSON 特有的功能
        /// • 需要自定义 JSON 序列化设置
        /// • 需要直接操作 JSON 处理器
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new YConfigManager(...);
        ///
        /// // 直接使用 JSON 处理器
        /// var customSettings = new JsonSettings { PrettyPrint = false };
        /// manager.Json.WriteJson("config.json", data, customSettings);
        /// </code>
        /// </example>
        public YJsonProcessor Json => _jsonProcessor;

        /// <summary>
        /// 获取 XML 处理器实例
        ///
        /// 提供对内部 XML 处理器的直接访问，
        /// 用于需要使用特定 XML 功能的场景
        /// </summary>
        /// <value>YXmlProcessor 实例</value>
        public YXmlProcessor Xml => _xmlProcessor;

        /// <summary>
        /// 获取 INI 处理器实例
        ///
        /// 提供对内部 INI 处理器的直接访问，
        /// 用于需要使用特定 INI 功能的场景
        /// </summary>
        /// <value>YIniProcessor 实例</value>
        public YIniProcessor Ini => _iniProcessor;

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 根据文件扩展名检测格式
        ///
        /// 通过分析文件扩展名来推断配置文件的格式类型，
        /// 这是格式检测的第一步
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>推断的文件格式</returns>
        /// <remarks>
        /// 扩展名映射：
        /// • .json → JSON 格式
        /// • .xml, .config → XML 格式
        /// • .ini, .cfg → INI 格式
        /// • 其他 → Unknown 格式
        /// </remarks>
        private ConfigFileFormat DetectFormatByExtension(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".json" => ConfigFileFormat.Json,
                ".xml" or ".config" => ConfigFileFormat.Xml,
                ".ini" or ".cfg" => ConfigFileFormat.Ini,
                _ => ConfigFileFormat.Unknown
            };
        }

        private bool IsJsonFormat(string content)
        {
            try
            {
                content = content.Trim();
                if ((content.StartsWith("{") && content.EndsWith("}")) ||
                    (content.StartsWith("[") && content.EndsWith("]")))
                {
                    System.Text.Json.JsonDocument.Parse(content);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool IsXmlFormat(string content)
        {
            try
            {
                content = content.Trim();
                if (content.StartsWith("<?xml") || content.StartsWith("<"))
                {
                    XDocument.Parse(content);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private bool IsIniFormat(string content)
        {
            try
            {
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                var hasKeyValue = false;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                        continue;

                    // 检查节标题 [SectionName] - 这是可选的
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        continue;
                    }

                    // 检查键值对 Key=Value
                    if (trimmedLine.Contains("="))
                    {
                        hasKeyValue = true;
                    }
                }

                // INI 格式至少要有键值对，节是可选的
                return hasKeyValue;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// 配置文件格式枚举
    /// </summary>
    public enum ConfigFileFormat
    {
        /// <summary>
        /// 未知格式
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// JSON 格式
        /// </summary>
        Json = 1,

        /// <summary>
        /// XML 格式
        /// </summary>
        Xml = 2,

        /// <summary>
        /// INI 格式
        /// </summary>
        Ini = 3
    }
}
