using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using Zylo.YIO.Config;

// 类型别名 - 简化复杂泛型
using IniData = System.Collections.Generic.Dictionary<string, System.Collections.Generic.Dictionary<string, string>>;

namespace Zylo.YIO.Formats
{
    /// <summary>
    /// INI 配置处理器
    ///
    /// 🎯 功能特性：
    /// • INI 文件读写和解析
    /// • 节（Section）和键值对处理
    /// • 注释保留和处理
    /// • 自定义 INI 设置
    /// • 字典格式双向转换
    ///
    /// 🔧 使用场景：
    /// • 传统 Windows 配置文件
    /// • 简单的配置存储
    /// • 跨平台配置文件
    /// • 配置文件格式转换
    ///
    /// 📖 使用示例：
    /// <code>
    /// var processor = new YIniProcessor();
    ///
    /// // 读取 INI 文件
    /// var iniData = processor.ReadIni("config.ini");
    /// if (iniData != null && iniData.ContainsKey("Database"))
    /// {
    ///     var connStr = iniData["Database"]["ConnectionString"];
    ///     Console.WriteLine($"连接字符串: {connStr}");
    /// }
    ///
    /// // 转换为字典
    /// var dict = processor.ReadIniFileAsDictionary("config.ini");
    /// </code>
    /// </summary>
    [YServiceScoped]
    public class YIniProcessor
    {
        #region 私有字段

        /// <summary>
        /// YIO 配置实例
        /// </summary>
        private readonly YIOConfig _config;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 INI 处理器
        /// </summary>
        /// <param name="config">YIO 配置，为 null 时使用默认配置</param>
        public YIniProcessor(YIOConfig? config = null)
        {
            _config = config ?? new YIOConfig();
        }

        #endregion

        #region INI 读取方法

        /// <summary>
        /// 读取 INI 配置文件并解析为嵌套字典结构
        ///
        /// 此方法提供完整的 INI 文件读取和解析功能，
        /// 支持节（Section）、键值对、注释处理
        /// </summary>
        /// <param name="filePath">INI 文件的完整路径</param>
        /// <param name="settings">INI 处理设置，为 null 时使用默认配置</param>
        /// <returns>解析后的嵌套字典，外层键为节名，内层为键值对；失败时返回 null</returns>
        /// <exception cref="FileNotFoundException">当指定的 INI 文件不存在时抛出</exception>
        /// <remarks>
        /// 支持的 INI 特性：
        /// • 节（Section）处理：[SectionName]
        /// • 键值对：Key=Value
        /// • 注释支持：; 或 # 开头的行
        /// • 空行忽略
        /// • 全局键值对（无节名）
        ///
        /// 返回结构示例：
        /// {
        ///   "": { "GlobalKey": "GlobalValue" },        // 全局键值对
        ///   "Database": { "Host": "localhost" },       // 节内键值对
        ///   "Settings": { "Debug": "true" }
        /// }
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YIniProcessor();
        ///
        /// // 读取 INI 文件
        /// var iniData = processor.ReadIni("config.ini");
        /// if (iniData != null)
        /// {
        ///     // 访问全局键值对
        ///     if (iniData.ContainsKey("") && iniData[""].ContainsKey("AppName"))
        ///     {
        ///         var appName = iniData[""]["AppName"];
        ///     }
        ///
        ///     // 访问节内键值对
        ///     if (iniData.ContainsKey("Database"))
        ///     {
        ///         var host = iniData["Database"]["Host"];
        ///         var port = iniData["Database"]["Port"];
        ///     }
        /// }
        /// </code>
        /// </example>
        public IniData? ReadIni(string filePath, IniSettings? settings = null)
        {
            try
            {
                // 第一步：检查 INI 文件是否存在
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ INI 文件不存在: {filePath}");
                    return null;
                }

                // 第二步：按行读取文件内容，使用 UTF-8 编码
                var lines = File.ReadAllLines(filePath, Encoding.UTF8);
                if (lines.Length == 0)
                {
                    Console.WriteLine($"⚠️ INI 文件为空: {filePath}");
                    return new IniData(); // 返回空的 INI 数据结构
                }

                // 第三步：解析 INI 内容，处理节、键值对和注释
                var result = ParseIniContent(lines, settings);
                Console.WriteLine($"✅ INI 读取成功: {filePath}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ INI 读取失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region INI 写入方法

        /// <summary>
        /// 将嵌套字典数据写入 INI 配置文件
        ///
        /// 此方法提供完整的 INI 文件写入功能，
        /// 支持节、键值对、注释的格式化输出
        /// </summary>
        /// <param name="filePath">目标 INI 文件的完整路径</param>
        /// <param name="data">要写入的嵌套字典数据</param>
        /// <param name="settings">INI 处理设置，为 null 时使用默认配置</param>
        /// <returns>写入成功返回 true，失败返回 false</returns>
        /// <remarks>
        /// 写入特性：
        /// • 自动创建目录结构
        /// • UTF-8 编码输出
        /// • 格式化节和键值对
        /// • 异常安全处理
        ///
        /// 输出格式：
        /// ; 全局键值对（无节名）
        /// GlobalKey=GlobalValue
        ///
        /// [SectionName]
        /// Key1=Value1
        /// Key2=Value2
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YIniProcessor();
        /// var iniData = new Dictionary&lt;string, Dictionary&lt;string, string&gt;&gt;
        /// {
        ///     [""] = new Dictionary&lt;string, string&gt; { ["AppName"] = "MyApp" },
        ///     ["Database"] = new Dictionary&lt;string, string&gt;
        ///     {
        ///         ["Host"] = "localhost",
        ///         ["Port"] = "5432"
        ///     }
        /// };
        ///
        /// bool success = processor.WriteIni("config.ini", iniData);
        /// </code>
        /// </example>
        public bool WriteIni(string filePath, IniData data, IniSettings? settings = null)
        {
            try
            {
                if (data == null)
                {
                    Console.WriteLine($"⚠️ INI 数据为空，无法写入: {filePath}");
                    return false;
                }

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var content = GenerateIniContent(data, settings);
                File.WriteAllText(filePath, content, Encoding.UTF8);

                Console.WriteLine($"✅ INI 写入成功: {filePath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ INI 写入失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region INI 字典转换方法

        /// <summary>
        /// 读取 INI 文件并转换为扁平化字典格式
        ///
        /// 此方法将 INI 文件转换为扁平化的字典结构，
        /// 使用 "节名.键名" 作为字典键，便于快速访问
        /// </summary>
        /// <param name="filePath">INI 文件路径</param>
        /// <param name="settings">INI 处理设置</param>
        /// <returns>转换后的扁平化字典，失败时返回 null</returns>
        /// <remarks>
        /// 转换规则：
        /// • 全局键值对：直接使用键名
        /// • 节内键值对：使用 "节名.键名" 格式
        /// • 值类型自动推断（字符串、数字、布尔值）
        ///
        /// 示例转换：
        /// INI 内容：
        /// AppName=MyApp
        /// [Database]
        /// Host=localhost
        /// Port=5432
        ///
        /// 转换结果：
        /// {
        ///   "AppName": "MyApp",
        ///   "Database.Host": "localhost",
        ///   "Database.Port": 5432
        /// }
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YIniProcessor();
        /// var dict = processor.ReadIniFileAsDictionary("config.ini");
        /// if (dict != null)
        /// {
        ///     var appName = dict["AppName"]?.ToString();
        ///     var dbHost = dict["Database.Host"]?.ToString();
        ///     var dbPort = Convert.ToInt32(dict["Database.Port"]);
        /// }
        /// </code>
        /// </example>
        public Dictionary<string, object>? ReadIniFileAsDictionary(string filePath, IniSettings? settings = null)
        {
            try
            {
                var iniData = ReadIni(filePath, settings);
                if (iniData == null)
                {
                    return null;
                }

                var result = new Dictionary<string, object>();
                foreach (var section in iniData)
                {
                    foreach (var kvp in section.Value)
                    {
                        var key = string.IsNullOrEmpty(section.Key) ? kvp.Key : $"{section.Key}.{kvp.Key}";
                        result[key] = kvp.Value;
                    }
                }

                Console.WriteLine($"✅ INI 字典转换成功: {filePath}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ INI 字典转换失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将字典转换为 INI 并写入文件
        /// </summary>
        public bool WriteIniFromDictionary(string filePath, Dictionary<string, object> data, IniSettings? settings = null)
        {
            try
            {
                if (data == null)
                {
                    Console.WriteLine($"⚠️ 字典数据为空，无法写入 INI: {filePath}");
                    return false;
                }

                var iniData = ConvertDictionaryToIniData(data);
                return WriteIni(filePath, iniData, settings);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ INI 字典写入失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        private IniData ParseIniContent(string[] lines, IniSettings? settings)
        {
            settings ??= _config.IniSettings;

            var result = new IniData();
            var currentSection = "";
            var commentChar = settings.CommentChar;
            var keyValueSeparator = settings.KeyValueSeparator;

            // 添加默认空节
            result[""] = new Dictionary<string, string>();

            foreach (var line in lines)
            {
                var trimmedLine = line.Trim();

                // 跳过空行和注释行
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(commentChar))
                {
                    continue;
                }

                // 处理节标题 [SectionName]
                if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    currentSection = trimmedLine.Substring(1, trimmedLine.Length - 2).Trim();
                    if (!result.ContainsKey(currentSection))
                    {
                        result[currentSection] = new Dictionary<string, string>();
                    }
                    continue;
                }

                // 处理键值对 Key=Value
                var separatorIndex = trimmedLine.IndexOf(keyValueSeparator);
                if (separatorIndex > 0)
                {
                    var key = trimmedLine.Substring(0, separatorIndex).Trim();
                    var value = trimmedLine.Substring(separatorIndex + 1).Trim();

                    // 移除值两端的引号
                    if ((value.StartsWith("\"") && value.EndsWith("\"")) ||
                        (value.StartsWith("'") && value.EndsWith("'")))
                    {
                        value = value.Substring(1, value.Length - 2);
                    }

                    if (!result.ContainsKey(currentSection))
                    {
                        result[currentSection] = new Dictionary<string, string>();
                    }

                    result[currentSection][key] = value;
                }
            }

            return result;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 生成 INI 文件内容
        /// </summary>
        /// <param name="data">INI 数据</param>
        /// <param name="settings">INI 设置</param>
        /// <returns>格式化的 INI 文件内容</returns>
        private string GenerateIniContent(IniData data, IniSettings? settings)
        {
            settings ??= _config.IniSettings;

            var sb = new StringBuilder();
            var keyValueSeparator = settings.KeyValueSeparator;
            var addSpacesAroundSeparator = true; // 默认在分隔符周围添加空格
            var separator = addSpacesAroundSeparator ? $" {keyValueSeparator} " : keyValueSeparator.ToString();

            // 首先处理空节（全局键值对）
            if (data.ContainsKey("") && data[""].Any())
            {
                foreach (var kvp in data[""])
                {
                    sb.AppendLine($"{kvp.Key}{separator}{kvp.Value}");
                }
                sb.AppendLine(); // 空行分隔
            }

            // 处理其他节
            foreach (var section in data.Where(s => !string.IsNullOrEmpty(s.Key)))
            {
                sb.AppendLine($"[{section.Key}]");

                foreach (var kvp in section.Value)
                {
                    sb.AppendLine($"{kvp.Key}{separator}{kvp.Value}");
                }

                sb.AppendLine(); // 节之间的空行
            }

            return sb.ToString().TrimEnd();
        }

        /// <summary>
        /// 将字典转换为 INI 数据格式
        /// </summary>
        /// <param name="dict">源字典</param>
        /// <returns>INI 数据格式</returns>
        private static IniData ConvertDictionaryToIniData(Dictionary<string, object> dict)
        {
            var result = new IniData();

            foreach (var kvp in dict)
            {
                var key = kvp.Key;
                var value = kvp.Value?.ToString() ?? "";

                string section;
                string keyName;

                // 解析键名，格式为 "Section.Key" 或直接 "Key"
                var dotIndex = key.IndexOf('.');
                if (dotIndex > 0)
                {
                    section = key.Substring(0, dotIndex);
                    keyName = key.Substring(dotIndex + 1);
                }
                else
                {
                    section = ""; // 全局节
                    keyName = key;
                }

                if (!result.ContainsKey(section))
                {
                    result[section] = new Dictionary<string, string>();
                }

                result[section][keyName] = value;
            }

            return result;
        }

        #endregion
    }
}
