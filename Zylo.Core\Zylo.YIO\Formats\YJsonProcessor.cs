using System;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Collections.Generic;
using System.Dynamic;
using Zylo.YIO.Config;

namespace Zylo.YIO.Formats
{
    /// <summary>
    /// JSON 配置处理器
    ///
    /// 🎯 功能特性：
    /// • 强类型 JSON 序列化/反序列化
    /// • 动态 JSON 对象处理
    /// • JSON 格式验证
    /// • 自定义序列化选项
    /// • 完善的错误处理
    ///
    /// 🔧 使用场景：
    /// • 应用程序配置文件
    /// • API 数据交换
    /// • 配置文件验证
    /// • 动态配置读取
    ///
    /// 📖 使用示例：
    /// <code>
    /// var processor = new YJsonProcessor();
    ///
    /// // 强类型读写
    /// var config = new AppConfig { Name = "MyApp" };
    /// processor.WriteJson("config.json", config);
    /// var loaded = processor.ReadJson&lt;AppConfig&gt;("config.json");
    ///
    /// // 动态读取
    /// dynamic dynConfig = processor.ReadJsonDynamic("config.json");
    /// Console.WriteLine(dynConfig.Name);
    ///
    /// // 格式验证
    /// bool isValid = processor.ValidateJson("config.json");
    /// </code>
    /// </summary>
    [YServiceScoped]
    public class YJsonProcessor
    {
        #region 私有字段

        /// <summary>
        /// YIO 配置实例
        /// </summary>
        private readonly YIOConfig _config;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 JSON 处理器
        /// </summary>
        /// <param name="config">YIO 配置，为 null 时使用默认配置</param>
        public YJsonProcessor(YIOConfig? config = null)
        {
            _config = config ?? new YIOConfig();
        }

        #endregion

        #region JSON 读取方法

        /// <summary>
        /// 读取 JSON 配置文件并反序列化为强类型对象
        ///
        /// 此方法提供完整的 JSON 文件读取和反序列化功能，
        /// 支持自定义序列化选项和完善的错误处理
        /// </summary>
        /// <typeparam name="T">目标对象类型，必须是引用类型</typeparam>
        /// <param name="filePath">JSON 文件的完整路径</param>
        /// <param name="settings">JSON 序列化设置，为 null 时使用默认配置</param>
        /// <returns>反序列化后的对象实例，失败时返回 null</returns>
        /// <exception cref="JsonException">JSON 格式错误时抛出</exception>
        /// <exception cref="FileNotFoundException">文件不存在时抛出</exception>
        /// <remarks>
        /// 支持的 JSON 特性：
        /// • 注释处理（可配置）
        /// • 尾随逗号支持
        /// • 大小写不敏感属性匹配
        /// • 自定义命名策略
        /// • 深度限制保护
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YJsonProcessor();
        ///
        /// // 基本用法
        /// var config = processor.ReadJson&lt;AppConfig&gt;("app.json");
        /// if (config != null)
        /// {
        ///     Console.WriteLine($"应用名称: {config.Name}");
        /// }
        ///
        /// // 使用自定义设置
        /// var settings = new JsonSettings { IgnoreComments = false };
        /// var config2 = processor.ReadJson&lt;AppConfig&gt;("app.json", settings);
        /// </code>
        /// </example>
        public T? ReadJson<T>(string filePath, JsonSettings? settings = null) where T : class
        {
            try
            {
                // 第一步：检查文件是否存在，避免不必要的异常
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ JSON 文件不存在: {filePath}");
                    return null;
                }

                // 第二步：读取文件内容，使用 UTF-8 编码确保中文等字符正确显示
                var jsonContent = File.ReadAllText(filePath, Encoding.UTF8);

                // 第三步：检查文件内容是否为空
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Console.WriteLine($"⚠️ JSON 文件为空: {filePath}");
                    return null;
                }

                // 第四步：创建序列化选项，支持自定义配置（如注释处理、命名策略等）
                var options = CreateJsonSerializerOptions(settings);

                // 第五步：执行反序列化，将 JSON 字符串转换为强类型对象
                var result = JsonSerializer.Deserialize<T>(jsonContent, options);

                Console.WriteLine($"✅ JSON 读取成功: {filePath}");
                return result;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"❌ JSON 格式错误 {filePath}: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON 读取失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region JSON 写入方法

        /// <summary>
        /// 将对象序列化为 JSON 并写入文件
        ///
        /// 此方法提供完整的对象序列化和文件写入功能，
        /// 支持自动目录创建和自定义格式化选项
        /// </summary>
        /// <typeparam name="T">源对象类型，必须是引用类型</typeparam>
        /// <param name="filePath">目标 JSON 文件的完整路径</param>
        /// <param name="data">要序列化的对象实例</param>
        /// <param name="settings">JSON 序列化设置，为 null 时使用默认配置</param>
        /// <returns>写入成功返回 true，失败返回 false</returns>
        /// <exception cref="JsonException">序列化错误时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">文件访问权限不足时抛出</exception>
        /// <remarks>
        /// 写入特性：
        /// • 自动创建目录结构
        /// • UTF-8 编码输出
        /// • 格式化输出（可配置）
        /// • 异常安全处理
        /// • 原子性写入保护
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YJsonProcessor();
        /// var config = new AppConfig
        /// {
        ///     Name = "MyApp",
        ///     Version = "1.0",
        ///     Settings = new Dictionary&lt;string, object&gt;
        ///     {
        ///         ["Debug"] = true,
        ///         ["MaxConnections"] = 100
        ///     }
        /// };
        ///
        /// // 基本写入
        /// bool success = processor.WriteJson("config.json", config);
        ///
        /// // 使用自定义设置（紧凑格式）
        /// var settings = new JsonSettings { PrettyPrint = false };
        /// bool success2 = processor.WriteJson("config.min.json", config, settings);
        /// </code>
        /// </example>
        public bool WriteJson<T>(string filePath, T data, JsonSettings? settings = null) where T : class
        {
            try
            {
                // 第一步：验证输入数据，确保不为空
                if (data == null)
                {
                    Console.WriteLine($"⚠️ 数据为空，无法写入: {filePath}");
                    return false;
                }

                // 第二步：确保目标目录存在，如果不存在则自动创建
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Console.WriteLine($"📁 创建目录: {directory}");
                }

                // 第三步：创建序列化选项，支持自定义格式化设置
                var options = CreateJsonSerializerOptions(settings);

                // 第四步：将对象序列化为 JSON 字符串
                var jsonContent = JsonSerializer.Serialize(data, options);

                // 第五步：将 JSON 内容写入文件，使用 UTF-8 编码
                File.WriteAllText(filePath, jsonContent, Encoding.UTF8);
                Console.WriteLine($"✅ JSON 写入成功: {filePath}");
                return true;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"❌ JSON 序列化错误 {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON 写入失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region JSON 验证方法

        /// <summary>
        /// 验证 JSON 文件格式是否正确
        ///
        /// 此方法通过尝试解析 JSON 文件来验证其格式的正确性，
        /// 是一个轻量级的验证工具，不会加载完整的对象模型
        /// </summary>
        /// <param name="filePath">要验证的 JSON 文件路径</param>
        /// <returns>如果 JSON 格式正确返回 true，否则返回 false</returns>
        /// <remarks>
        /// 验证过程：
        /// • 检查文件是否存在
        /// • 读取文件内容
        /// • 尝试解析 JSON 结构
        /// • 返回验证结果
        ///
        /// 注意：此方法只验证 JSON 语法的正确性，不验证数据的语义正确性。
        /// 对于大文件，此方法比完整反序列化更高效。
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YJsonProcessor();
        ///
        /// if (processor.ValidateJson("config.json"))
        /// {
        ///     var config = processor.ReadJson&lt;AppConfig&gt;("config.json");
        ///     // 安全地使用配置
        /// }
        /// else
        /// {
        ///     Console.WriteLine("JSON 文件格式无效");
        ///     // 处理格式错误
        /// }
        /// </code>
        /// </example>
        public bool ValidateJson(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ JSON 文件不存在: {filePath}");
                    return false;
                }

                var jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Console.WriteLine($"⚠️ JSON 文件为空: {filePath}");
                    return false;
                }

                JsonDocument.Parse(jsonContent);
                Console.WriteLine($"✅ JSON 格式验证通过: {filePath}");
                return true;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"❌ JSON 格式验证失败 {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON 验证过程出错 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region JSON 动态处理方法

        /// <summary>
        /// 读取 JSON 文件并转换为动态对象
        ///
        /// 此方法将 JSON 文件内容转换为 ExpandoObject，
        /// 允许使用动态语法访问 JSON 数据，无需预定义类型
        /// </summary>
        /// <param name="filePath">JSON 文件路径</param>
        /// <param name="settings">JSON 设置，为 null 时使用默认配置</param>
        /// <returns>动态对象实例，失败时返回 null</returns>
        /// <remarks>
        /// 动态对象特性：
        /// • 支持嵌套对象访问
        /// • 支持数组和集合
        /// • 运行时类型推断
        /// • 灵活的属性访问
        ///
        /// 适用场景：
        /// • 配置文件结构不固定
        /// • 需要灵活访问 JSON 数据
        /// • 原型开发和调试
        /// • API 响应数据处理
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YJsonProcessor();
        ///
        /// // 读取动态配置
        /// dynamic config = processor.ReadJsonDynamic("config.json");
        /// if (config != null)
        /// {
        ///     // 动态访问属性
        ///     Console.WriteLine($"应用名称: {config.Name}");
        ///     Console.WriteLine($"版本: {config.Version}");
        ///
        ///     // 访问嵌套对象
        ///     if (config.Database != null)
        ///     {
        ///         Console.WriteLine($"连接字符串: {config.Database.ConnectionString}");
        ///     }
        ///
        ///     // 访问数组
        ///     if (config.Features != null)
        ///     {
        ///         foreach (var feature in config.Features)
        ///         {
        ///             Console.WriteLine($"功能: {feature}");
        ///         }
        ///     }
        /// }
        /// </code>
        /// </example>
        public dynamic? ReadJsonDynamic(string filePath, JsonSettings? settings = null)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ JSON 文件不存在: {filePath}");
                    return null;
                }

                var jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Console.WriteLine($"⚠️ JSON 文件为空: {filePath}");
                    return null;
                }

                using var document = JsonDocument.Parse(jsonContent);
                var result = JsonElementToExpandoObject(document.RootElement);

                Console.WriteLine($"✅ JSON 动态读取成功: {filePath}");
                return result;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"❌ JSON 格式错误 {filePath}: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ JSON 动态读取失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 创建 JSON 序列化选项
        ///
        /// 根据提供的设置创建 JsonSerializerOptions 实例，
        /// 如果设置为 null 则使用默认配置
        /// </summary>
        /// <param name="settings">JSON 设置</param>
        /// <returns>配置好的 JsonSerializerOptions 实例</returns>
        private JsonSerializerOptions CreateJsonSerializerOptions(JsonSettings? settings)
        {
            settings ??= _config.JsonSettings;

            return new JsonSerializerOptions
            {
                WriteIndented = settings.PrettyPrint,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                AllowTrailingCommas = settings.AllowTrailingCommas,
                ReadCommentHandling = settings.IgnoreComments ? JsonCommentHandling.Skip : JsonCommentHandling.Allow,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                PropertyNameCaseInsensitive = true,
                MaxDepth = settings.MaxDepth
            };
        }

        /// <summary>
        /// 将 JsonElement 转换为 ExpandoObject，支持动态类型访问
        ///
        /// 此方法递归地将 JSON 元素转换为动态对象，
        /// 保持原始的 JSON 结构和数据类型
        /// </summary>
        /// <param name="element">要转换的 JSON 元素</param>
        /// <returns>转换后的动态对象</returns>
        /// <remarks>
        /// 转换规则：
        /// • JsonValueKind.Object → ExpandoObject
        /// • JsonValueKind.Array → List&lt;dynamic&gt;
        /// • JsonValueKind.String → string
        /// • JsonValueKind.Number → int/long/double/decimal（自动推断）
        /// • JsonValueKind.True/False → bool
        /// • JsonValueKind.Null → null
        /// </remarks>
        private dynamic JsonElementToExpandoObject(JsonElement element)
        {
            switch (element.ValueKind)
            {
                case JsonValueKind.Object:
                    var expandoObject = new ExpandoObject();
                    var dictionary = (IDictionary<string, object?>)expandoObject;

                    foreach (var property in element.EnumerateObject())
                    {
                        dictionary[property.Name] = JsonElementToExpandoObject(property.Value);
                    }

                    return expandoObject;

                case JsonValueKind.Array:
                    var list = new List<dynamic>();
                    foreach (var item in element.EnumerateArray())
                    {
                        list.Add(JsonElementToExpandoObject(item));
                    }
                    return list;

                case JsonValueKind.String:
                    return element.GetString() ?? "";

                case JsonValueKind.Number:
                    if (element.TryGetInt32(out var intValue))
                        return intValue;
                    if (element.TryGetInt64(out var longValue))
                        return longValue;
                    if (element.TryGetDouble(out var doubleValue))
                        return doubleValue;
                    return element.GetDecimal();

                case JsonValueKind.True:
                    return true;

                case JsonValueKind.False:
                    return false;

                case JsonValueKind.Null:
                    return null!;

                default:
                    return element.ToString();
            }
        }

        #endregion
    }
}
