using System;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using System.Collections.Generic;
using Zylo.YIO.Config;

namespace Zylo.YIO.Formats
{
    /// <summary>
    /// XML 配置处理器
    ///
    /// 🎯 功能特性：
    /// • XML 文档读写和解析
    /// • XML 格式验证
    /// • XML 到字典的双向转换
    /// • 自定义 XML 处理设置
    /// • 完善的错误处理和恢复
    ///
    /// 🔧 使用场景：
    /// • 传统配置文件处理
    /// • Web.config 和 App.config
    /// • XML 数据交换
    /// • 配置文件格式转换
    ///
    /// 📖 使用示例：
    /// <code>
    /// var processor = new YXmlProcessor();
    ///
    /// // 读取 XML 文档
    /// var xmlDoc = processor.ReadXml("config.xml");
    /// if (xmlDoc?.Root != null)
    /// {
    ///     var appName = xmlDoc.Root.Element("AppName")?.Value;
    ///     Console.WriteLine($"应用名称: {appName}");
    /// }
    ///
    /// // 转换为字典
    /// var dict = processor.ReadXmlAsDictionary("config.xml");
    ///
    /// // 验证格式
    /// bool isValid = processor.ValidateXml("config.xml");
    /// </code>
    /// </summary>
    [YServiceScoped]
    public class YXmlProcessor
    {
        #region 私有字段

        /// <summary>
        /// YIO 配置实例
        /// </summary>
        private readonly YIOConfig _config;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 XML 处理器
        /// </summary>
        /// <param name="config">YIO 配置，为 null 时使用默认配置</param>
        public YXmlProcessor(YIOConfig? config = null)
        {
            _config = config ?? new YIOConfig();
        }

        #endregion

        #region XML 读取方法

        /// <summary>
        /// 读取 XML 配置文件并解析为 XDocument 对象
        ///
        /// 此方法提供完整的 XML 文件读取和解析功能，
        /// 支持自定义解析选项和验证设置
        /// </summary>
        /// <param name="filePath">XML 文件的完整路径</param>
        /// <param name="settings">XML 处理设置，为 null 时使用默认配置</param>
        /// <returns>解析后的 XDocument 对象，失败时返回 null</returns>
        /// <exception cref="FileNotFoundException">当指定的 XML 文件不存在时抛出</exception>
        /// <remarks>
        /// 支持的 XML 特性：
        /// • DTD 验证（可选）
        /// • 空白字符处理控制
        /// • 注释保留
        /// • 编码自动检测
        /// </remarks>
        /// <example>
        /// <code>
        /// // 使用默认设置读取
        /// var xmlDoc = processor.ReadXml("config.xml");
        ///
        /// // 使用自定义设置读取
        /// var customSettings = new XmlSettings { ValidateOnParse = false };
        /// var xmlDoc2 = processor.ReadXml("config.xml", customSettings);
        ///
        /// // 访问 XML 内容
        /// if (xmlDoc?.Root != null)
        /// {
        ///     var appName = xmlDoc.Root.Element("AppName")?.Value;
        ///     Console.WriteLine($"应用名称: {appName}");
        /// }
        /// </code>
        /// </example>
        public XDocument? ReadXml(string filePath, XmlSettings? settings = null)
        {
            try
            {
                // 第一步：检查文件是否存在
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ XML 文件不存在: {filePath}");
                    return null;
                }

                // 第二步：读取 XML 文件内容，使用 UTF-8 编码
                var xmlContent = File.ReadAllText(filePath, Encoding.UTF8);
                if (string.IsNullOrWhiteSpace(xmlContent))
                {
                    Console.WriteLine($"⚠️ XML 文件为空: {filePath}");
                    return null;
                }

                // 第三步：设置默认配置，如果未提供则使用全局配置
                settings ??= _config.XmlSettings;

                var xmlSettings = new XmlReaderSettings
                {
                    IgnoreWhitespace = !settings.PreserveWhitespace,
                    IgnoreComments = false,
                    ValidationType = settings.ValidateOnParse ? ValidationType.DTD : ValidationType.None
                };

                using var reader = XmlReader.Create(new StringReader(xmlContent), xmlSettings);
                var result = XDocument.Load(reader);

                Console.WriteLine($"✅ XML 读取成功: {filePath}");
                return result;
            }
            catch (XmlException ex)
            {
                Console.WriteLine($"❌ XML 格式错误 {filePath}: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 读取失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region XML 写入方法

        /// <summary>
        /// 将 XDocument 对象写入 XML 文件
        ///
        /// 此方法提供完整的 XML 文档写入功能，
        /// 支持自定义格式化选项和编码设置
        /// </summary>
        /// <param name="filePath">目标 XML 文件的完整路径</param>
        /// <param name="document">要写入的 XDocument 对象</param>
        /// <param name="settings">XML 处理设置，为 null 时使用默认配置</param>
        /// <returns>写入成功返回 true，失败返回 false</returns>
        /// <remarks>
        /// 写入特性：
        /// • 自动创建目录结构
        /// • UTF-8 编码输出
        /// • 格式化输出（可选）
        /// • 异常安全处理
        /// </remarks>
        /// <example>
        /// <code>
        /// // 创建 XML 文档
        /// var xmlDoc = new XDocument(
        ///     new XElement("Configuration",
        ///         new XElement("AppName", "MyApp"),
        ///         new XElement("Version", "1.0")
        ///     )
        /// );
        ///
        /// // 写入文件
        /// bool success = processor.WriteXml("config.xml", xmlDoc);
        ///
        /// // 使用自定义设置
        /// var customSettings = new XmlSettings { Indent = false };
        /// bool success2 = processor.WriteXml("config.xml", xmlDoc, customSettings);
        /// </code>
        /// </example>
        public bool WriteXml(string filePath, XDocument document, XmlSettings? settings = null)
        {
            try
            {
                if (document == null)
                {
                    Console.WriteLine($"⚠️ XML 文档为空，无法写入: {filePath}");
                    return false;
                }

                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                settings ??= _config.XmlSettings;

                var xmlSettings = new XmlWriterSettings
                {
                    Indent = settings.IndentOutput,
                    IndentChars = settings.IndentChars,
                    Encoding = Encoding.GetEncoding(settings.DefaultEncoding),
                    OmitXmlDeclaration = false
                };

                using var writer = XmlWriter.Create(filePath, xmlSettings);
                document.Save(writer);

                Console.WriteLine($"✅ XML 写入成功: {filePath}");
                return true;
            }
            catch (XmlException ex)
            {
                Console.WriteLine($"❌ XML 写入格式错误 {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 写入失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region XML 验证方法

        /// <summary>
        /// 验证 XML 文件格式是否正确
        ///
        /// 此方法通过尝试解析 XML 文件来验证其格式的正确性，
        /// 是一个轻量级的验证工具
        /// </summary>
        /// <param name="filePath">要验证的 XML 文件路径</param>
        /// <returns>如果 XML 格式正确返回 true，否则返回 false</returns>
        /// <remarks>
        /// 验证过程：
        /// • 检查文件是否存在
        /// • 读取文件内容
        /// • 尝试解析 XML 结构
        /// • 返回验证结果
        /// </remarks>
        /// <example>
        /// <code>
        /// var processor = new YXmlProcessor();
        /// if (processor.ValidateXml("config.xml"))
        /// {
        ///     var xmlDoc = processor.ReadXml("config.xml");
        /// }
        /// else
        /// {
        ///     Console.WriteLine("XML 格式无效");
        /// }
        /// </code>
        /// </example>
        public bool ValidateXml(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"⚠️ XML 文件不存在: {filePath}");
                    return false;
                }

                var xmlContent = File.ReadAllText(filePath, Encoding.UTF8);
                if (string.IsNullOrWhiteSpace(xmlContent))
                {
                    Console.WriteLine($"⚠️ XML 文件为空: {filePath}");
                    return false;
                }

                XDocument.Parse(xmlContent);
                Console.WriteLine($"✅ XML 格式验证通过: {filePath}");
                return true;
            }
            catch (XmlException ex)
            {
                Console.WriteLine($"❌ XML 格式验证失败 {filePath}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 验证过程出错 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region XML 字典转换方法

        /// <summary>
        /// 读取 XML 文件并转换为字典格式
        ///
        /// 此方法将 XML 文档转换为嵌套字典结构，
        /// 便于动态访问和处理 XML 数据
        /// </summary>
        /// <param name="filePath">XML 文件路径</param>
        /// <param name="settings">XML 处理设置</param>
        /// <returns>转换后的字典对象，失败时返回 null</returns>
        /// <remarks>
        /// 转换规则：
        /// • XML 元素 → 字典键值对
        /// • XML 属性 → 以 @ 前缀的键
        /// • 文本内容 → #text 键
        /// • 嵌套元素 → 嵌套字典
        /// </remarks>
        /// <example>
        /// <code>
        /// // XML: &lt;config&gt;&lt;name&gt;MyApp&lt;/name&gt;&lt;/config&gt;
        /// // 转换为: { "config": { "name": "MyApp" } }
        /// var dict = processor.ReadXmlAsDictionary("config.xml");
        /// var appName = dict["config"]["name"];
        /// </code>
        /// </example>
        public Dictionary<string, object>? ReadXmlAsDictionary(string filePath, XmlSettings? settings = null)
        {
            try
            {
                var xmlDoc = ReadXml(filePath, settings);
                if (xmlDoc?.Root == null)
                {
                    Console.WriteLine($"⚠️ XML 根节点为空: {filePath}");
                    return null;
                }

                var result = new Dictionary<string, object>();
                ConvertXmlNodeToDictionary(xmlDoc.Root, result);

                Console.WriteLine($"✅ XML 字典转换成功: {filePath}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 字典转换失败 {filePath}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将字典转换为 XML 并写入文件
        /// </summary>
        public bool WriteXmlFromDictionary(string filePath, Dictionary<string, object> data, string rootElementName = "Configuration", XmlSettings? settings = null)
        {
            try
            {
                if (data == null)
                {
                    Console.WriteLine($"⚠️ 字典数据为空，无法写入 XML: {filePath}");
                    return false;
                }

                var rootElement = new XElement(rootElementName);
                ConvertDictionaryToXmlElement(data, rootElement);

                var xmlDoc = new XDocument(rootElement);
                return WriteXml(filePath, xmlDoc, settings);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 字典写入失败 {filePath}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 将 XML 节点转换为字典
        /// </summary>
        /// <param name="element">XML 元素</param>
        /// <param name="dict">目标字典</param>
        private static void ConvertXmlNodeToDictionary(XElement element, Dictionary<string, object> dict)
        {
            try
            {
                // 处理属性
                foreach (var attr in element.Attributes())
                {
                    dict[$"@{attr.Name}"] = attr.Value;
                }

                // 处理子元素
                var childElements = element.Elements().ToList();
                if (childElements.Any())
                {
                    foreach (var group in childElements.GroupBy(e => e.Name.LocalName))
                    {
                        var elements = group.ToList();
                        if (elements.Count == 1)
                        {
                            var childDict = new Dictionary<string, object>();
                            ConvertXmlNodeToDictionary(elements[0], childDict);
                            dict[group.Key] = childDict.Any() ? childDict : elements[0].Value;
                        }
                        else
                        {
                            var list = new List<object>();
                            foreach (var elem in elements)
                            {
                                var childDict = new Dictionary<string, object>();
                                ConvertXmlNodeToDictionary(elem, childDict);
                                list.Add(childDict.Any() ? childDict : elem.Value);
                            }
                            dict[group.Key] = list;
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(element.Value))
                {
                    dict["#text"] = element.Value;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ XML 节点转换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 将字典转换为 XML 元素
        /// </summary>
        /// <param name="dict">源字典</param>
        /// <param name="parentElement">父 XML 元素</param>
        private static void ConvertDictionaryToXmlElement(Dictionary<string, object> dict, XElement parentElement)
        {
            try
            {
                foreach (var kvp in dict)
                {
                    if (kvp.Key.StartsWith("@"))
                    {
                        var attrName = kvp.Key.Substring(1);
                        parentElement.SetAttributeValue(attrName, kvp.Value?.ToString());
                    }
                    else if (kvp.Key == "#text")
                    {
                        parentElement.Value = kvp.Value?.ToString() ?? "";
                    }
                    else
                    {
                        if (kvp.Value is Dictionary<string, object> childDict)
                        {
                            var childElement = new XElement(kvp.Key);
                            ConvertDictionaryToXmlElement(childDict, childElement);
                            parentElement.Add(childElement);
                        }
                        else if (kvp.Value is List<object> list)
                        {
                            foreach (var item in list)
                            {
                                var childElement = new XElement(kvp.Key);
                                if (item is Dictionary<string, object> itemDict)
                                {
                                    ConvertDictionaryToXmlElement(itemDict, childElement);
                                }
                                else
                                {
                                    childElement.Value = item?.ToString() ?? "";
                                }
                                parentElement.Add(childElement);
                            }
                        }
                        else
                        {
                            var childElement = new XElement(kvp.Key, kvp.Value?.ToString());
                            parentElement.Add(childElement);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 字典转 XML 失败: {ex.Message}");
            }
        }

        #endregion
    }
}
