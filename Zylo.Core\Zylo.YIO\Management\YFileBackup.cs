using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text;
using System.Threading;

using Zylo.YIO.Config;
using Zylo.YIO.Security;

namespace Zylo.YIO.Management
{
    /// <summary>
    /// YFileBackup - 企业级文件备份管理工具类
    ///
    /// 功能概述：
    /// • 完整备份 - 创建源文件/目录的完整副本
    /// • 增量备份 - 仅备份自上次备份以来发生变化的文件
    /// • 差异备份 - 备份自上次完整备份以来的所有变化
    /// • 备份恢复 - 从备份文件恢复到指定位置
    /// • 版本管理 - 维护备份历史记录和版本链
    /// • 自动清理 - 根据保留策略清理过期备份
    /// • 加密支持 - 可选的备份文件加密保护
    /// • 完整性验证 - 使用SHA256哈希验证备份完整性
    ///
    /// 设计特点：
    /// • 支持单文件和目录备份
    /// • 智能变更检测（基于文件修改时间和哈希值）
    /// • 灵活的备份策略配置
    /// • 完善的错误处理和日志记录
    /// • 异步操作支持
    /// • 跨平台兼容性
    ///
    /// 使用场景：
    /// • 重要文件的定期备份
    /// • 项目代码的版本备份
    /// • 配置文件的安全备份
    /// • 数据库文件的增量备份
    /// • 系统文件的灾难恢复备份
    ///
    /// 使用示例：
    /// <code>
    /// var backup = new YFileBackup();
    ///
    /// // 创建完整备份
    /// var fullBackup = backup.CreateFullBackup(@"C:\MyProject", @"D:\Backups", encrypt: true, password: "mypassword");
    ///
    /// // 创建增量备份
    /// var incrementalBackup = backup.CreateIncrementalBackup(@"C:\MyProject", @"D:\Backups", fullBackup);
    ///
    /// // 恢复备份
    /// backup.RestoreBackup(fullBackup, @"C:\Restored", "mypassword");
    ///
    /// // 清理过期备份
    /// backup.CleanupOldBackups(@"D:\Backups", retentionDays: 30, maxVersions: 10);
    /// </code>
    /// </summary>


    public partial class YFileBackup
    {
        #region 私有字段和常量

        /// <summary>
        /// YIO框架配置实例
        /// 包含备份操作的各种配置参数，如缓冲区大小、超时设置等
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// 文件加密服务实例
        /// 提供文件加密/解密功能和哈希计算服务
        /// 用于保护敏感备份数据和验证文件完整性
        /// </summary>
        private readonly YFileEncryption _encryption;

        /// <summary>
        /// 备份信息文件的扩展名
        /// 用于标识备份元数据文件
        /// </summary>
        private const string BackupInfoExtension = ".backup.json";

        /// <summary>
        /// 加密文件的扩展名
        /// 用于标识已加密的备份文件
        /// </summary>
        private const string EncryptedFileExtension = ".enc";

        /// <summary>
        /// 默认的备份保留天数
        /// 超过此天数的备份将被自动清理
        /// </summary>
        private const int DefaultRetentionDays = 30;

        /// <summary>
        /// 默认的最大备份版本数
        /// 超过此数量的备份将被自动清理
        /// </summary>
        private const int DefaultMaxVersions = 10;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化 YFileBackup 实例
        ///
        /// 创建一个配置完整的文件备份管理器实例，支持各种备份操作
        /// </summary>
        /// <param name="config">
        /// YIO框架配置实例，包含：
        /// • 文件操作缓冲区大小
        /// • 加密算法配置
        /// • 超时设置
        /// • 性能优化参数
        /// 如果为null，将使用默认配置
        /// </param>
        /// <param name="encryption">
        /// 文件加密服务实例，提供：
        /// • 文件加密/解密功能
        /// • SHA256哈希计算
        /// • 密钥管理
        /// 如果为null，将使用默认加密服务
        /// </param>
        /// <example>
        /// <code>
        /// // 使用自定义配置创建备份管理器
        /// var config = new YIOConfig { BufferSize = 8192 };
        /// var encryption = new YFileEncryption(config);
        /// var backup = new YFileBackup(config, encryption);
        /// </code>
        /// </example>
        public YFileBackup(YIOConfig config, YFileEncryption encryption)
        {
            // 确保配置不为null，提供默认配置作为后备
            _config = config ?? new YIOConfig();

            // 确保加密服务不为null，使用配置创建默认实例
            _encryption = encryption ?? new YFileEncryption(_config);
        }

        /// <summary>
        /// 无参构造函数 - 使用默认配置初始化
        ///
        /// 适用于快速创建备份管理器实例，使用框架默认配置
        /// 主要用于静态方法调用和简单场景
        /// </summary>
        /// <example>
        /// <code>
        /// // 快速创建备份管理器
        /// var backup = new YFileBackup();
        ///
        /// // 或使用静态方法（由YStatic属性自动生成）
        /// var backupInfo = YFileBackup.CreateFullBackup(sourcePath, backupDir);
        /// </code>
        /// </example>
        public YFileBackup() : this(new YIOConfig(), new YFileEncryption())
        {
            // 调用主构造函数，使用默认配置
            // 这种设计模式确保所有初始化逻辑集中在主构造函数中
        }

        #endregion

        #region 备份信息模型和枚举

        /// <summary>
        /// 备份信息数据模型
        ///
        /// 包含备份操作的完整元数据信息，用于：
        /// • 跟踪备份版本和关系链
        /// • 验证备份完整性
        /// • 支持备份恢复操作
        /// • 管理备份生命周期
        /// </summary>
        public class BackupInfo
        {
            /// <summary>
            /// 备份唯一标识符
            /// 使用GUID确保全局唯一性，用于建立备份版本链和快速查找
            /// </summary>
            public string BackupId { get; set; } = Guid.NewGuid().ToString();

            /// <summary>
            /// 源文件或目录的完整路径
            /// 记录备份的原始位置，用于增量备份时的路径匹配和恢复操作
            /// </summary>
            public string SourcePath { get; set; } = "";

            /// <summary>
            /// 备份文件或目录的完整路径
            /// 指向实际的备份数据存储位置，可能是单个文件或目录结构
            /// </summary>
            public string BackupPath { get; set; } = "";

            /// <summary>
            /// 备份创建时间
            /// 用于备份版本排序、过期清理和增量备份的时间比较
            /// </summary>
            public DateTime BackupTime { get; set; } = DateTime.Now;

            /// <summary>
            /// 备份类型标识
            /// 决定备份和恢复的处理策略
            /// </summary>
            public BackupType Type { get; set; } = BackupType.Full;

            /// <summary>
            /// 原始数据大小（字节）
            /// 源文件或目录的总大小，用于计算压缩率和存储统计
            /// </summary>
            public long OriginalSize { get; set; }

            /// <summary>
            /// 备份数据大小（字节）
            /// 实际备份文件的大小，可能因压缩或加密而与原始大小不同
            /// </summary>
            public long BackupSize { get; set; }

            /// <summary>
            /// 源数据的SHA256哈希值
            /// 用于验证数据完整性和检测文件变更，确保备份的可靠性
            /// </summary>
            public string Hash { get; set; } = "";

            /// <summary>
            /// 是否启用压缩
            /// 标识备份数据是否经过压缩处理，影响恢复时的解压操作
            /// </summary>
            public bool IsCompressed { get; set; }

            /// <summary>
            /// 是否启用加密
            /// 标识备份数据是否经过加密保护，影响恢复时的解密操作
            /// </summary>
            public bool IsEncrypted { get; set; }

            /// <summary>
            /// 父备份标识符
            /// 用于增量和差异备份，建立备份版本链关系
            /// • 完整备份：null
            /// • 增量备份：指向上一个备份（可能是完整或增量）
            /// • 差异备份：指向最近的完整备份
            /// </summary>
            public string? ParentBackupId { get; set; }

            /// <summary>
            /// 备份包含的文件列表
            /// 记录所有被备份的文件路径，用于：
            /// • 增量备份时的变更检测
            /// • 恢复操作的文件定位
            /// • 备份内容的审计和统计
            /// </summary>
            public List<string> Files { get; set; } = new();

            /// <summary>
            /// 获取备份压缩率
            /// 计算备份数据相对于原始数据的压缩比例
            /// </summary>
            /// <returns>压缩率百分比，0-100之间的值</returns>
            public double GetCompressionRatio()
            {
                if (OriginalSize == 0) return 0;
                return Math.Max(0, (1.0 - (double)BackupSize / OriginalSize) * 100);
            }

            /// <summary>
            /// 获取格式化的大小信息
            /// 返回人类可读的大小描述
            /// </summary>
            /// <returns>格式化的大小字符串</returns>
            public string GetFormattedSizes()
            {
                return $"原始: {FormatBytes(OriginalSize)}, 备份: {FormatBytes(BackupSize)}";
            }

            /// <summary>
            /// 格式化字节数为可读字符串
            /// </summary>
            private static string FormatBytes(long bytes)
            {
                string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
                int counter = 0;
                double number = bytes;
                while (Math.Round(number / 1024) >= 1)
                {
                    number /= 1024;
                    counter++;
                }
                return $"{number:N1} {suffixes[counter]}";
            }
        }

        /// <summary>
        /// 备份类型枚举
        ///
        /// 定义支持的备份策略类型，每种类型有不同的特点和适用场景：
        /// </summary>
        public enum BackupType
        {
            /// <summary>
            /// 完整备份 - 备份所有指定的文件和目录
            ///
            /// 特点：
            /// • 包含完整的数据副本
            /// • 恢复速度最快（单一备份文件）
            /// • 占用存储空间最大
            /// • 可独立进行恢复操作
            ///
            /// 适用场景：
            /// • 首次备份
            /// • 定期的基准备份
            /// • 重要数据的完整保护
            /// </summary>
            Full = 0,

            /// <summary>
            /// 增量备份 - 仅备份自上次备份以来发生变化的文件
            ///
            /// 特点：
            /// • 备份速度最快
            /// • 占用存储空间最小
            /// • 需要完整的备份链进行恢复
            /// • 备份链中任一环节损坏都会影响恢复
            ///
            /// 适用场景：
            /// • 频繁的日常备份
            /// • 存储空间有限的环境
            /// • 变化较少的数据集
            /// </summary>
            Incremental = 1,

            /// <summary>
            /// 差异备份 - 备份自上次完整备份以来的所有变化
            ///
            /// 特点：
            /// • 恢复速度中等（需要完整备份+最新差异备份）
            /// • 占用存储空间中等
            /// • 备份时间随距离完整备份的时间增长而增加
            /// • 恢复时只需要完整备份和最新的差异备份
            ///
            /// 适用场景：
            /// • 平衡备份速度和恢复复杂度
            /// • 中等频率的备份需求
            /// • 对恢复速度有一定要求的场景
            /// </summary>
            Differential = 2
        }

        #endregion

        #region 完整备份操作

        /// <summary>
        /// 创建完整备份
        ///
        /// 对指定的文件或目录创建完整的备份副本，包含所有数据
        /// 这是备份链的起点，后续的增量和差异备份都基于完整备份
        /// </summary>
        /// <param name="sourcePath">
        /// 源文件或目录的完整路径
        /// • 支持单个文件备份
        /// • 支持整个目录树备份（包括子目录）
        /// • 路径必须存在且可访问
        /// </param>
        /// <param name="backupDirectory">
        /// 备份文件的存储目录
        /// • 如果目录不存在，将自动创建
        /// • 建议使用专门的备份存储位置
        /// • 确保有足够的磁盘空间
        /// </param>
        /// <param name="compress">
        /// 是否对备份数据进行压缩
        /// • true: 压缩备份数据，节省存储空间但增加CPU开销
        /// • false: 不压缩，备份速度更快但占用更多空间
        /// 注意：当前版本暂未实现压缩功能，参数保留用于未来扩展
        /// </param>
        /// <param name="encrypt">
        /// 是否对备份数据进行加密
        /// • true: 使用AES加密保护备份数据
        /// • false: 不加密，备份文件为明文存储
        /// 加密可保护敏感数据，但需要密码才能恢复
        /// </param>
        /// <param name="password">
        /// 加密密码（当encrypt为true时必需）
        /// • 用于AES加密的密钥生成
        /// • 恢复时需要相同的密码
        /// • 建议使用强密码保护重要数据
        /// </param>
        /// <returns>
        /// 包含备份详细信息的BackupInfo对象
        /// 包括备份ID、路径、大小、哈希值等元数据
        /// </returns>
        /// <exception cref="ArgumentException">当源路径不存在或备份目录为空时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有访问权限时抛出</exception>
        /// <exception cref="IOException">当发生I/O错误时抛出</exception>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        ///
        /// // 创建简单的完整备份
        /// var backupInfo = backup.CreateFullBackup(@"C:\MyProject", @"D:\Backups");
        ///
        /// // 创建加密的完整备份
        /// var encryptedBackup = backup.CreateFullBackup(
        ///     @"C:\SensitiveData",
        ///     @"D:\SecureBackups",
        ///     compress: false,
        ///     encrypt: true,
        ///     password: "MyStrongPassword123"
        /// );
        ///
        /// Console.WriteLine($"备份ID: {backupInfo.BackupId}");
        /// Console.WriteLine($"备份大小: {backupInfo.GetFormattedSizes()}");
        /// </code>
        /// </example>
        public BackupInfo CreateFullBackup(string sourcePath, string backupDirectory,
            bool compress = false, bool encrypt = false, string? password = null)
        {
            try
            {
                // 参数验证 - 确保输入参数的有效性
                ValidateBackupParameters(sourcePath, backupDirectory, encrypt, password);

                // 创建备份目录（如果不存在）
                // 使用递归创建确保整个路径都存在
                Directory.CreateDirectory(backupDirectory);

                // 初始化备份信息对象
                var backupInfo = new BackupInfo
                {
                    SourcePath = Path.GetFullPath(sourcePath), // 获取绝对路径
                    Type = BackupType.Full,
                    IsCompressed = compress,
                    IsEncrypted = encrypt
                };

                // 生成唯一的备份文件名
                // 格式: backup_YYYYMMDD_HHMMSS_GUID
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"backup_{timestamp}_{backupInfo.BackupId[..8]}"; // 使用GUID前8位

                // 根据源路径类型执行不同的备份策略
                if (File.Exists(sourcePath))
                {
                    // 单文件备份处理
                    // 保留原文件扩展名以便识别文件类型
                    var extension = Path.GetExtension(sourcePath);
                    backupInfo.BackupPath = Path.Combine(backupDirectory, backupFileName + extension);

                    // 执行单文件备份操作
                    BackupSingleFile(sourcePath, backupInfo.BackupPath, backupInfo, password);
                    backupInfo.Files.Add(sourcePath);
                }
                else if (Directory.Exists(sourcePath))
                {
                    // 目录备份处理
                    // 创建以备份文件名命名的子目录
                    backupInfo.BackupPath = Path.Combine(backupDirectory, backupFileName);

                    // 执行目录备份操作（递归处理所有子目录和文件）
                    BackupDirectory(sourcePath, backupInfo.BackupPath, backupInfo, password);
                }

                // 保存备份元数据信息到JSON文件
                // 用于后续的备份管理和恢复操作
                SaveBackupInfo(backupInfo, backupDirectory);

                // 输出成功信息
                Console.WriteLine($"✅ 完整备份创建成功");
                Console.WriteLine($"   备份ID: {backupInfo.BackupId}");
                Console.WriteLine($"   源路径: {backupInfo.SourcePath}");
                Console.WriteLine($"   备份路径: {backupInfo.BackupPath}");
                Console.WriteLine($"   文件数量: {backupInfo.Files.Count}");
                Console.WriteLine($"   {backupInfo.GetFormattedSizes()}");

                return backupInfo;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                Console.WriteLine($"❌ 创建完整备份失败");
                Console.WriteLine($"   源路径: {sourcePath}");
                Console.WriteLine($"   备份目录: {backupDirectory}");
                Console.WriteLine($"   错误信息: {ex.Message}");

                // 重新抛出异常，保持调用栈信息
                throw;
            }
        }

        /// <summary>
        /// 异步创建完整备份
        ///
        /// 在后台线程中执行完整备份操作，避免阻塞UI线程
        /// 适用于大文件或大目录的备份，提供更好的用户体验
        /// </summary>
        /// <param name="sourcePath">
        /// 源文件或目录的完整路径
        /// 与同步版本参数含义相同
        /// </param>
        /// <param name="backupDirectory">
        /// 备份文件的存储目录
        /// 与同步版本参数含义相同
        /// </param>
        /// <param name="compress">
        /// 是否对备份数据进行压缩
        /// 与同步版本参数含义相同
        /// </param>
        /// <param name="encrypt">
        /// 是否对备份数据进行加密
        /// 与同步版本参数含义相同
        /// </param>
        /// <param name="password">
        /// 加密密码（当encrypt为true时必需）
        /// 与同步版本参数含义相同
        /// </param>
        /// <param name="cancellationToken">
        /// 取消令牌，用于取消长时间运行的备份操作
        /// 调用者可以通过此令牌请求取消操作
        /// </param>
        /// <param name="progress">
        /// 进度报告接口，用于报告备份进度
        /// 可以用于更新UI进度条或记录备份状态
        /// </param>
        /// <returns>
        /// 包含备份详细信息的BackupInfo对象的异步任务
        /// 任务完成后返回与同步版本相同的备份信息
        /// </returns>
        /// <exception cref="OperationCanceledException">当操作被取消时抛出</exception>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        /// var cts = new CancellationTokenSource();
        /// var progress = new Progress&lt;string&gt;(message => Console.WriteLine(message));
        ///
        /// try
        /// {
        ///     // 异步创建备份，支持取消和进度报告
        ///     var backupInfo = await backup.CreateFullBackupAsync(
        ///         @"C:\LargeProject",
        ///         @"D:\Backups",
        ///         encrypt: true,
        ///         password: "password123",
        ///         cts.Token,
        ///         progress
        ///     );
        ///
        ///     Console.WriteLine($"异步备份完成: {backupInfo.BackupId}");
        /// }
        /// catch (OperationCanceledException)
        /// {
        ///     Console.WriteLine("备份操作已取消");
        /// }
        /// </code>
        /// </example>
        public async Task<BackupInfo> CreateFullBackupAsync(string sourcePath, string backupDirectory,
            bool compress = false, bool encrypt = false, string? password = null,
            CancellationToken cancellationToken = default, IProgress<string>? progress = null)
        {
            // 在Task.Run中执行同步备份操作
            // 这样可以避免阻塞调用线程，同时保持代码的简洁性
            return await Task.Run(() =>
            {
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();

                // 报告开始进度
                progress?.Report($"开始创建完整备份: {sourcePath}");

                try
                {
                    // 执行实际的备份操作
                    var result = CreateFullBackup(sourcePath, backupDirectory, compress, encrypt, password);

                    // 报告完成进度
                    progress?.Report($"完整备份创建完成: {result.BackupId}");

                    return result;
                }
                catch (Exception ex)
                {
                    // 报告错误进度
                    progress?.Report($"备份失败: {ex.Message}");
                    throw;
                }
            }, cancellationToken);
        }

        /// <summary>
        /// 验证备份参数的有效性
        ///
        /// 集中处理所有备份操作的参数验证逻辑
        /// 确保参数的一致性和有效性
        /// </summary>
        /// <param name="sourcePath">源路径</param>
        /// <param name="backupDirectory">备份目录</param>
        /// <param name="encrypt">是否加密</param>
        /// <param name="password">加密密码</param>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        private static void ValidateBackupParameters(string sourcePath, string backupDirectory, bool encrypt, string? password)
        {
            // 验证源路径
            if (string.IsNullOrWhiteSpace(sourcePath))
                throw new ArgumentException("源路径不能为空", nameof(sourcePath));

            if (!File.Exists(sourcePath) && !Directory.Exists(sourcePath))
                throw new ArgumentException($"源路径不存在: {sourcePath}", nameof(sourcePath));

            // 验证备份目录
            if (string.IsNullOrWhiteSpace(backupDirectory))
                throw new ArgumentException("备份目录不能为空", nameof(backupDirectory));

            // 验证加密参数
            if (encrypt && string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("启用加密时必须提供密码", nameof(password));
        }

        #endregion

        #region 增量备份操作

        /// <summary>
        /// 创建增量备份
        ///
        /// 仅备份自上次备份以来发生变化的文件，实现高效的增量备份策略
        /// 增量备份依赖于备份链，需要完整的备份历史才能完全恢复数据
        /// </summary>
        /// <param name="sourcePath">
        /// 源文件或目录的完整路径
        /// 必须与上次备份的源路径一致，用于变更检测
        /// </param>
        /// <param name="backupDirectory">
        /// 备份文件的存储目录
        /// 建议与完整备份使用相同的目录，便于管理备份链
        /// </param>
        /// <param name="lastBackupInfo">
        /// 上次备份的信息对象
        /// • 可以是完整备份或增量备份
        /// • 用于比较文件变更和建立备份链关系
        /// • 必须包含有效的文件列表和时间戳
        /// </param>
        /// <param name="compress">
        /// 是否对备份数据进行压缩
        /// 建议与备份链中的其他备份保持一致
        /// </param>
        /// <param name="encrypt">
        /// 是否对备份数据进行加密
        /// 建议与备份链中的其他备份保持一致
        /// </param>
        /// <param name="password">
        /// 加密密码（当encrypt为true时必需）
        /// 必须与备份链中的其他备份使用相同密码
        /// </param>
        /// <returns>
        /// 包含增量备份详细信息的BackupInfo对象
        /// 如果没有文件变更，返回空的备份信息对象
        /// </returns>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        /// <exception cref="ArgumentNullException">当lastBackupInfo为null时抛出</exception>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        ///
        /// // 首先创建完整备份
        /// var fullBackup = backup.CreateFullBackup(@"C:\MyProject", @"D:\Backups");
        ///
        /// // 等待一段时间，让文件发生变化...
        /// System.Threading.Thread.Sleep(5000);
        ///
        /// // 创建增量备份
        /// var incrementalBackup = backup.CreateIncrementalBackup(
        ///     @"C:\MyProject",
        ///     @"D:\Backups",
        ///     fullBackup,
        ///     encrypt: true,
        ///     password: "password123"
        /// );
        ///
        /// if (incrementalBackup.Files.Count > 0)
        /// {
        ///     Console.WriteLine($"增量备份包含 {incrementalBackup.Files.Count} 个变更文件");
        /// }
        /// else
        /// {
        ///     Console.WriteLine("没有文件变更，跳过备份");
        /// }
        /// </code>
        /// </example>
        public BackupInfo CreateIncrementalBackup(string sourcePath, string backupDirectory,
            BackupInfo lastBackupInfo, bool compress = false, bool encrypt = false, string? password = null)
        {
            try
            {
                // 参数验证
                ValidateIncrementalBackupParameters(sourcePath, backupDirectory, lastBackupInfo, encrypt, password);

                // 确保备份目录存在
                Directory.CreateDirectory(backupDirectory);

                // 初始化增量备份信息
                var backupInfo = new BackupInfo
                {
                    SourcePath = Path.GetFullPath(sourcePath),
                    Type = BackupType.Incremental,
                    ParentBackupId = lastBackupInfo.BackupId, // 建立备份链关系
                    IsCompressed = compress,
                    IsEncrypted = encrypt
                };

                // 执行变更检测 - 找出自上次备份以来发生变化的文件
                Console.WriteLine("🔍 正在检测文件变更...");
                var changedFiles = FindChangedFiles(sourcePath, lastBackupInfo);

                // 检查是否有文件变更
                if (changedFiles.Count == 0)
                {
                    Console.WriteLine("ℹ️  没有检测到文件变更，跳过增量备份");
                    Console.WriteLine($"   检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    Console.WriteLine($"   上次备份: {lastBackupInfo.BackupTime:yyyy-MM-dd HH:mm:ss}");

                    // 返回空的备份信息，但保留基本元数据
                    backupInfo.BackupPath = ""; // 标识为空备份
                    return backupInfo;
                }

                // 生成增量备份文件名
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"incremental_{timestamp}_{backupInfo.BackupId[..8]}";
                backupInfo.BackupPath = Path.Combine(backupDirectory, backupFileName);

                // 执行增量备份操作
                Console.WriteLine($"📦 正在创建增量备份，包含 {changedFiles.Count} 个变更文件...");
                BackupChangedFiles(changedFiles, backupInfo.BackupPath, backupInfo, password);
                backupInfo.Files = changedFiles;

                // 保存备份元数据
                SaveBackupInfo(backupInfo, backupDirectory);

                // 输出成功信息
                Console.WriteLine($"✅ 增量备份创建成功");
                Console.WriteLine($"   备份ID: {backupInfo.BackupId}");
                Console.WriteLine($"   父备份ID: {backupInfo.ParentBackupId}");
                Console.WriteLine($"   变更文件数: {changedFiles.Count}");
                Console.WriteLine($"   备份路径: {backupInfo.BackupPath}");
                Console.WriteLine($"   {backupInfo.GetFormattedSizes()}");

                return backupInfo;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                Console.WriteLine($"❌ 创建增量备份失败");
                Console.WriteLine($"   源路径: {sourcePath}");
                Console.WriteLine($"   备份目录: {backupDirectory}");
                Console.WriteLine($"   上次备份ID: {lastBackupInfo?.BackupId ?? "null"}");
                Console.WriteLine($"   错误信息: {ex.Message}");

                throw;
            }
        }

        /// <summary>
        /// 验证增量备份参数的有效性
        ///
        /// 专门用于增量备份的参数验证，包含额外的备份链验证
        /// </summary>
        /// <param name="sourcePath">源路径</param>
        /// <param name="backupDirectory">备份目录</param>
        /// <param name="lastBackupInfo">上次备份信息</param>
        /// <param name="encrypt">是否加密</param>
        /// <param name="password">加密密码</param>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        /// <exception cref="ArgumentNullException">当必需参数为null时抛出</exception>
        private static void ValidateIncrementalBackupParameters(string sourcePath, string backupDirectory,
            BackupInfo lastBackupInfo, bool encrypt, string? password)
        {
            // 基础参数验证
            ValidateBackupParameters(sourcePath, backupDirectory, encrypt, password);

            // 增量备份特有的验证
            if (lastBackupInfo == null)
                throw new ArgumentNullException(nameof(lastBackupInfo), "增量备份需要上次备份信息");

            if (string.IsNullOrWhiteSpace(lastBackupInfo.BackupId))
                throw new ArgumentException("上次备份信息缺少有效的备份ID", nameof(lastBackupInfo));

            if (lastBackupInfo.BackupTime == default)
                throw new ArgumentException("上次备份信息缺少有效的备份时间", nameof(lastBackupInfo));

            // 验证源路径一致性
            var normalizedSourcePath = Path.GetFullPath(sourcePath);
            var normalizedLastSourcePath = Path.GetFullPath(lastBackupInfo.SourcePath);

            if (!string.Equals(normalizedSourcePath, normalizedLastSourcePath, StringComparison.OrdinalIgnoreCase))
            {
                throw new ArgumentException(
                    $"源路径不一致。当前: {normalizedSourcePath}, 上次备份: {normalizedLastSourcePath}",
                    nameof(sourcePath));
            }
        }

        #endregion

        #region 备份恢复操作

        /// <summary>
        /// 恢复备份数据到指定位置
        ///
        /// 从备份文件中恢复数据到目标位置，支持完整备份的直接恢复
        /// 对于增量和差异备份，需要完整的备份链才能正确恢复
        /// </summary>
        /// <param name="backupInfo">
        /// 要恢复的备份信息对象
        /// • 必须包含有效的备份路径和类型信息
        /// • 对于加密备份，需要正确的密码
        /// • 备份文件必须存在且可访问
        /// </param>
        /// <param name="restorePath">
        /// 数据恢复的目标路径
        /// • 如果目录不存在，将自动创建
        /// • 确保有足够的磁盘空间
        /// • 建议使用空目录避免文件冲突
        /// </param>
        /// <param name="password">
        /// 解密密码（当备份已加密时必需）
        /// • 必须与备份时使用的密码一致
        /// • 对于未加密的备份，此参数被忽略
        /// </param>
        /// <param name="overwriteExisting">
        /// 是否覆盖已存在的文件
        /// • true: 覆盖同名文件
        /// • false: 跳过同名文件
        /// </param>
        /// <returns>
        /// 恢复操作的结果
        /// • true: 恢复成功
        /// • false: 恢复失败或部分失败
        /// </returns>
        /// <exception cref="ArgumentNullException">当backupInfo为null时抛出</exception>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        /// <exception cref="FileNotFoundException">当备份文件不存在时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有访问权限时抛出</exception>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        ///
        /// // 恢复完整备份
        /// bool success = backup.RestoreBackup(
        ///     fullBackupInfo,
        ///     @"C:\Restored",
        ///     password: "password123",
        ///     overwriteExisting: true
        /// );
        ///
        /// if (success)
        /// {
        ///     Console.WriteLine("数据恢复成功");
        /// }
        /// else
        /// {
        ///     Console.WriteLine("数据恢复失败，请检查日志");
        /// }
        /// </code>
        /// </example>
        public bool RestoreBackup(BackupInfo backupInfo, string restorePath, string? password = null,
            bool overwriteExisting = true)
        {
            try
            {
                // 参数验证
                ValidateRestoreParameters(backupInfo, restorePath);

                // 验证备份文件存在性
                if (!File.Exists(backupInfo.BackupPath) && !Directory.Exists(backupInfo.BackupPath))
                {
                    throw new FileNotFoundException($"备份文件不存在: {backupInfo.BackupPath}", backupInfo.BackupPath);
                }

                // 验证加密参数
                if (backupInfo.IsEncrypted && string.IsNullOrWhiteSpace(password))
                {
                    throw new ArgumentException("恢复加密备份需要提供密码", nameof(password));
                }

                // 创建恢复目录（如果不存在）
                Directory.CreateDirectory(restorePath);

                Console.WriteLine($"🔄 开始恢复备份");
                Console.WriteLine($"   备份ID: {backupInfo.BackupId}");
                Console.WriteLine($"   备份类型: {backupInfo.Type}");
                Console.WriteLine($"   源路径: {backupInfo.SourcePath}");
                Console.WriteLine($"   恢复到: {restorePath}");
                Console.WriteLine($"   加密状态: {(backupInfo.IsEncrypted ? "已加密" : "未加密")}");

                bool result;

                // 根据备份类型执行不同的恢复策略
                switch (backupInfo.Type)
                {
                    case BackupType.Full:
                        // 恢复完整备份 - 可以独立恢复
                        result = RestoreFullBackup(backupInfo, restorePath, password, overwriteExisting);
                        break;

                    case BackupType.Incremental:
                    case BackupType.Differential:
                        // 增量和差异备份需要备份链支持
                        Console.WriteLine("⚠️  增量/差异备份恢复需要完整的备份链");
                        Console.WriteLine("   请使用 RestoreBackupChain 方法进行链式恢复");
                        result = false;
                        break;

                    default:
                        Console.WriteLine($"❌ 不支持的备份类型: {backupInfo.Type}");
                        result = false;
                        break;
                }

                // 输出恢复结果
                if (result)
                {
                    Console.WriteLine($"✅ 备份恢复成功");
                    Console.WriteLine($"   恢复位置: {restorePath}");
                    Console.WriteLine($"   文件数量: {backupInfo.Files.Count}");
                }
                else
                {
                    Console.WriteLine($"❌ 备份恢复失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                Console.WriteLine($"❌ 恢复备份时发生错误");
                Console.WriteLine($"   备份ID: {backupInfo?.BackupId ?? "unknown"}");
                Console.WriteLine($"   恢复路径: {restorePath}");
                Console.WriteLine($"   错误信息: {ex.Message}");
                Console.WriteLine($"   错误类型: {ex.GetType().Name}");

                return false;
            }
        }

        /// <summary>
        /// 验证恢复操作参数的有效性
        /// </summary>
        /// <param name="backupInfo">备份信息</param>
        /// <param name="restorePath">恢复路径</param>
        /// <exception cref="ArgumentNullException">当必需参数为null时抛出</exception>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        private static void ValidateRestoreParameters(BackupInfo backupInfo, string restorePath)
        {
            if (backupInfo == null)
                throw new ArgumentNullException(nameof(backupInfo), "备份信息不能为空");

            if (string.IsNullOrWhiteSpace(restorePath))
                throw new ArgumentException("恢复路径不能为空", nameof(restorePath));

            if (string.IsNullOrWhiteSpace(backupInfo.BackupPath))
                throw new ArgumentException("备份信息中缺少备份路径", nameof(backupInfo));

            if (string.IsNullOrWhiteSpace(backupInfo.BackupId))
                throw new ArgumentException("备份信息中缺少备份ID", nameof(backupInfo));
        }

        #endregion

        #region 备份管理和维护

        /// <summary>
        /// 获取备份历史记录
        ///
        /// 扫描指定目录中的所有备份信息文件，构建完整的备份历史列表
        /// 支持备份链分析和版本管理
        /// </summary>
        /// <param name="backupDirectory">
        /// 备份存储目录的完整路径
        /// • 包含备份文件和元数据文件的目录
        /// • 如果目录不存在或为空，返回空列表
        /// </param>
        /// <param name="includeCorrupted">
        /// 是否包含损坏的备份记录
        /// • true: 包含无法解析的备份信息（用于诊断）
        /// • false: 只返回有效的备份记录
        /// </param>
        /// <returns>
        /// 按时间倒序排列的备份信息列表
        /// • 最新的备份排在前面
        /// • 每个备份包含完整的元数据信息
        /// • 空列表表示没有找到有效备份
        /// </returns>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        /// var history = backup.GetBackupHistory(@"D:\Backups");
        ///
        /// Console.WriteLine($"找到 {history.Count} 个备份:");
        /// foreach (var backupInfo in history)
        /// {
        ///     Console.WriteLine($"  {backupInfo.BackupTime:yyyy-MM-dd HH:mm} - {backupInfo.Type} - {backupInfo.GetFormattedSizes()}");
        /// }
        ///
        /// // 分析备份链
        /// var fullBackups = history.Where(b => b.Type == BackupType.Full).ToList();
        /// var incrementalBackups = history.Where(b => b.Type == BackupType.Incremental).ToList();
        /// Console.WriteLine($"完整备份: {fullBackups.Count}, 增量备份: {incrementalBackups.Count}");
        /// </code>
        /// </example>
        public List<BackupInfo> GetBackupHistory(string backupDirectory, bool includeCorrupted = false)
        {
            var backups = new List<BackupInfo>();
            var corruptedFiles = new List<string>();

            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(backupDirectory))
                {
                    Console.WriteLine("⚠️  备份目录路径为空");
                    return backups;
                }

                if (!Directory.Exists(backupDirectory))
                {
                    Console.WriteLine($"⚠️  备份目录不存在: {backupDirectory}");
                    return backups;
                }

                // 查找所有备份信息文件
                var infoFiles = Directory.GetFiles(backupDirectory, $"*{BackupInfoExtension}");

                if (infoFiles.Length == 0)
                {
                    Console.WriteLine($"ℹ️  在目录中未找到备份信息文件: {backupDirectory}");
                    return backups;
                }

                Console.WriteLine($"🔍 正在扫描 {infoFiles.Length} 个备份信息文件...");

                // 逐个解析备份信息文件
                foreach (var infoFile in infoFiles)
                {
                    try
                    {
                        // 读取并解析JSON文件
                        var json = File.ReadAllText(infoFile);
                        var backupInfo = JsonSerializer.Deserialize<BackupInfo>(json);

                        if (backupInfo != null && IsValidBackupInfo(backupInfo))
                        {
                            backups.Add(backupInfo);
                        }
                        else
                        {
                            corruptedFiles.Add(infoFile);
                            Console.WriteLine($"⚠️  备份信息无效: {Path.GetFileName(infoFile)}");
                        }
                    }
                    catch (JsonException ex)
                    {
                        corruptedFiles.Add(infoFile);
                        Console.WriteLine($"❌ JSON解析失败: {Path.GetFileName(infoFile)} - {ex.Message}");
                    }
                    catch (Exception ex)
                    {
                        corruptedFiles.Add(infoFile);
                        Console.WriteLine($"❌ 读取备份信息失败: {Path.GetFileName(infoFile)} - {ex.Message}");
                    }
                }

                // 按备份时间倒序排序（最新的在前）
                backups.Sort((a, b) => b.BackupTime.CompareTo(a.BackupTime));

                // 输出统计信息
                Console.WriteLine($"✅ 成功加载 {backups.Count} 个有效备份");
                if (corruptedFiles.Count > 0)
                {
                    Console.WriteLine($"⚠️  发现 {corruptedFiles.Count} 个损坏的备份信息文件");
                }

                // 分析备份类型分布
                var typeStats = backups.GroupBy(b => b.Type)
                    .ToDictionary(g => g.Key, g => g.Count());

                foreach (var stat in typeStats)
                {
                    Console.WriteLine($"   {stat.Key}: {stat.Value} 个");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取备份历史失败: {ex.Message}");
            }

            return backups;
        }

        /// <summary>
        /// 验证备份信息的有效性
        ///
        /// 检查备份信息对象是否包含必需的字段和有效数据
        /// </summary>
        /// <param name="backupInfo">要验证的备份信息</param>
        /// <returns>true表示备份信息有效，false表示无效</returns>
        private static bool IsValidBackupInfo(BackupInfo backupInfo)
        {
            if (backupInfo == null) return false;
            if (string.IsNullOrWhiteSpace(backupInfo.BackupId)) return false;
            if (string.IsNullOrWhiteSpace(backupInfo.SourcePath)) return false;
            if (backupInfo.BackupTime == default) return false;

            // 对于增量备份，如果没有变更，BackupPath 可以为空
            // 对于完整备份，BackupPath 必须有值
            if (backupInfo.Type == BackupType.Full && string.IsNullOrWhiteSpace(backupInfo.BackupPath))
                return false;

            return true;
        }

        /// <summary>
        /// 清理过期备份
        ///
        /// 根据保留策略自动清理过期的备份文件，释放存储空间
        /// 支持基于时间和数量的双重清理策略，确保备份存储的可持续性
        /// </summary>
        /// <param name="backupDirectory">
        /// 备份存储目录的完整路径
        /// 包含要清理的备份文件和元数据文件
        /// </param>
        /// <param name="retentionDays">
        /// 备份保留天数（默认30天）
        /// • 超过此天数的备份将被标记为过期
        /// • 设置为0表示不基于时间清理
        /// • 建议根据数据重要性和存储容量设置
        /// </param>
        /// <param name="maxVersions">
        /// 最大保留版本数（默认10个）
        /// • 超过此数量的旧备份将被清理
        /// • 设置为0表示不限制版本数量
        /// • 按备份时间排序，保留最新的版本
        /// </param>
        /// <param name="preserveFullBackups">
        /// 是否优先保留完整备份
        /// • true: 在清理时优先保留完整备份，避免破坏备份链
        /// • false: 按时间和数量统一清理，不区分备份类型
        /// </param>
        /// <param name="dryRun">
        /// 是否执行模拟清理（不实际删除文件）
        /// • true: 只显示将要删除的备份，不执行实际删除
        /// • false: 执行实际的删除操作
        /// </param>
        /// <returns>
        /// 清理的备份数量
        /// • 实际删除的备份文件数量
        /// • 模拟模式下返回将要删除的数量
        /// </returns>
        /// <example>
        /// <code>
        /// var backup = new YFileBackup();
        ///
        /// // 标准清理：保留30天内的备份，最多10个版本
        /// int cleaned = backup.CleanupOldBackups(@"D:\Backups");
        ///
        /// // 严格清理：只保留7天内的备份，最多5个版本
        /// int strictCleaned = backup.CleanupOldBackups(@"D:\Backups", 7, 5);
        ///
        /// // 模拟清理：查看将要删除的备份，不实际删除
        /// int wouldDelete = backup.CleanupOldBackups(@"D:\Backups", dryRun: true);
        /// Console.WriteLine($"模拟清理将删除 {wouldDelete} 个备份");
        ///
        /// // 保护完整备份的清理
        /// int safeCleaned = backup.CleanupOldBackups(@"D:\Backups",
        ///     retentionDays: 30, maxVersions: 10, preserveFullBackups: true);
        /// </code>
        /// </example>
        public int CleanupOldBackups(string backupDirectory, int retentionDays = DefaultRetentionDays,
            int maxVersions = DefaultMaxVersions, bool preserveFullBackups = true, bool dryRun = false)
        {
            try
            {
                // 参数验证
                if (string.IsNullOrWhiteSpace(backupDirectory))
                {
                    Console.WriteLine("❌ 备份目录路径不能为空");
                    return 0;
                }

                if (!Directory.Exists(backupDirectory))
                {
                    Console.WriteLine($"❌ 备份目录不存在: {backupDirectory}");
                    return 0;
                }

                // 获取所有备份历史
                var allBackups = GetBackupHistory(backupDirectory);
                if (allBackups.Count == 0)
                {
                    Console.WriteLine("ℹ️  没有找到备份文件，无需清理");
                    return 0;
                }

                Console.WriteLine($"🧹 开始清理过期备份 {(dryRun ? "(模拟模式)" : "")}");
                Console.WriteLine($"   总备份数: {allBackups.Count}");
                Console.WriteLine($"   保留策略: {retentionDays}天内，最多{maxVersions}个版本");
                Console.WriteLine($"   保护完整备份: {(preserveFullBackups ? "是" : "否")}");

                var toDelete = new List<BackupInfo>();

                // 策略1: 基于时间的清理
                if (retentionDays > 0)
                {
                    var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                    var timeBasedOldBackups = allBackups.Where(b => b.BackupTime < cutoffDate).ToList();

                    if (preserveFullBackups)
                    {
                        // 保护完整备份，只删除过期的增量和差异备份
                        timeBasedOldBackups = timeBasedOldBackups
                            .Where(b => b.Type != BackupType.Full)
                            .ToList();
                    }

                    toDelete.AddRange(timeBasedOldBackups);
                    Console.WriteLine($"   基于时间标记删除: {timeBasedOldBackups.Count} 个");
                }

                // 策略2: 基于数量的清理
                if (maxVersions > 0 && allBackups.Count > maxVersions)
                {
                    var excessBackups = allBackups.Skip(maxVersions).ToList();

                    if (preserveFullBackups)
                    {
                        // 在超出数量的备份中，优先删除增量和差异备份
                        var nonFullBackups = excessBackups.Where(b => b.Type != BackupType.Full).ToList();
                        var fullBackupsToDelete = excessBackups.Where(b => b.Type == BackupType.Full)
                            .Skip(Math.Max(1, maxVersions / 3)) // 至少保留1/3的完整备份
                            .ToList();

                        excessBackups = nonFullBackups.Concat(fullBackupsToDelete).ToList();
                    }

                    toDelete.AddRange(excessBackups);
                    Console.WriteLine($"   基于数量标记删除: {excessBackups.Count} 个");
                }

                // 去重（避免重复删除）
                var uniqueToDelete = toDelete.Distinct().ToList();

                if (uniqueToDelete.Count == 0)
                {
                    Console.WriteLine("✅ 没有需要清理的过期备份");
                    return 0;
                }

                Console.WriteLine($"📋 准备删除 {uniqueToDelete.Count} 个过期备份:");

                // 执行删除操作
                var deletedCount = 0;
                foreach (var backup in uniqueToDelete)
                {
                    try
                    {
                        Console.WriteLine($"   🗑️  {backup.BackupTime:yyyy-MM-dd HH:mm} - {backup.Type} - {Path.GetFileName(backup.BackupPath)}");

                        if (!dryRun)
                        {
                            // 删除备份数据文件或目录
                            if (File.Exists(backup.BackupPath))
                            {
                                File.Delete(backup.BackupPath);
                            }
                            else if (Directory.Exists(backup.BackupPath))
                            {
                                Directory.Delete(backup.BackupPath, true);
                            }

                            // 删除备份信息文件
                            var infoFile = Path.Combine(backupDirectory, $"{backup.BackupId}{BackupInfoExtension}");
                            if (File.Exists(infoFile))
                            {
                                File.Delete(infoFile);
                            }
                        }

                        deletedCount++;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ 删除备份失败: {Path.GetFileName(backup.BackupPath)} - {ex.Message}");
                    }
                }

                // 输出清理结果
                if (dryRun)
                {
                    Console.WriteLine($"🔍 模拟清理完成，将删除 {deletedCount} 个过期备份");
                }
                else
                {
                    Console.WriteLine($"✅ 清理完成，成功删除 {deletedCount} 个过期备份");

                    // 显示清理后的统计信息
                    var remainingBackups = GetBackupHistory(backupDirectory);
                    Console.WriteLine($"   剩余备份: {remainingBackups.Count} 个");

                    var typeStats = remainingBackups.GroupBy(b => b.Type)
                        .ToDictionary(g => g.Key, g => g.Count());
                    foreach (var stat in typeStats)
                    {
                        Console.WriteLine($"   {stat.Key}: {stat.Value} 个");
                    }
                }

                return deletedCount;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 清理过期备份失败: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 备份单个文件
        ///
        /// 处理单个文件的备份操作，包括加密、哈希计算和大小统计
        /// 这是文件备份的核心处理逻辑
        /// </summary>
        /// <param name="sourcePath">
        /// 源文件的完整路径
        /// 必须是存在且可读的文件
        /// </param>
        /// <param name="backupPath">
        /// 备份文件的目标路径
        /// 包含完整的文件名和扩展名
        /// </param>
        /// <param name="backupInfo">
        /// 备份信息对象，用于记录备份元数据
        /// 方法会更新其中的大小和哈希信息
        /// </param>
        /// <param name="password">
        /// 加密密码（当需要加密时）
        /// 如果backupInfo.IsEncrypted为true且password不为空，则执行加密备份
        /// </param>
        /// <exception cref="FileNotFoundException">当源文件不存在时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有文件访问权限时抛出</exception>
        /// <exception cref="IOException">当发生I/O错误时抛出</exception>
        private void BackupSingleFile(string sourcePath, string backupPath, BackupInfo backupInfo, string? password)
        {
            try
            {
                // 获取源文件信息
                var sourceFileInfo = new FileInfo(sourcePath);
                if (!sourceFileInfo.Exists)
                {
                    throw new FileNotFoundException($"源文件不存在: {sourcePath}");
                }

                // 记录原始文件大小
                backupInfo.OriginalSize = sourceFileInfo.Length;

                // 确保备份目录存在
                var backupDirectory = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDirectory))
                {
                    Directory.CreateDirectory(backupDirectory);
                }

                // 根据加密设置执行不同的备份策略
                if (backupInfo.IsEncrypted && !string.IsNullOrEmpty(password))
                {
                    // 加密备份：使用加密服务处理文件
                    Console.WriteLine($"🔒 正在加密备份文件: {Path.GetFileName(sourcePath)}");
                    _encryption.EncryptFile(sourcePath, backupPath, password);
                }
                else
                {
                    // 普通备份：直接复制文件
                    Console.WriteLine($"📄 正在备份文件: {Path.GetFileName(sourcePath)}");
                    File.Copy(sourcePath, backupPath, overwrite: true);
                }

                // 获取备份文件信息并更新统计数据
                var backupFileInfo = new FileInfo(backupPath);
                if (!backupFileInfo.Exists)
                {
                    throw new IOException($"备份文件创建失败: {backupPath}");
                }

                backupInfo.BackupSize = backupFileInfo.Length;

                // 计算源文件的SHA256哈希值用于完整性验证
                // 注意：这里计算的是源文件的哈希，不是备份文件的哈希
                // 这样可以在恢复时验证数据完整性
                Console.WriteLine($"🔍 正在计算文件哈希: {Path.GetFileName(sourcePath)}");
                backupInfo.Hash = _encryption.CalculateSHA256(sourcePath);

                // 输出备份统计信息
                var compressionRatio = backupInfo.GetCompressionRatio();
                if (backupInfo.IsEncrypted)
                {
                    Console.WriteLine($"   加密完成，大小变化: {compressionRatio:F1}%");
                }
                else if (compressionRatio != 0)
                {
                    Console.WriteLine($"   备份完成，大小: {backupInfo.GetFormattedSizes()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份单个文件失败: {Path.GetFileName(sourcePath)} - {ex.Message}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 备份目录及其所有内容
        ///
        /// 递归备份指定目录下的所有文件和子目录，保持原有的目录结构
        /// 支持加密备份和进度跟踪
        /// </summary>
        /// <param name="sourcePath">
        /// 源目录的完整路径
        /// 必须是存在且可访问的目录
        /// </param>
        /// <param name="backupPath">
        /// 备份目录的目标路径
        /// 将创建与源目录相同的目录结构
        /// </param>
        /// <param name="backupInfo">
        /// 备份信息对象，用于记录备份元数据
        /// 方法会更新其中的文件列表、大小等信息
        /// </param>
        /// <param name="password">
        /// 加密密码（当需要加密时）
        /// 如果backupInfo.IsEncrypted为true且password不为空，则对所有文件执行加密备份
        /// </param>
        /// <exception cref="DirectoryNotFoundException">当源目录不存在时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有目录访问权限时抛出</exception>
        /// <exception cref="IOException">当发生I/O错误时抛出</exception>
        private void BackupDirectory(string sourcePath, string backupPath, BackupInfo backupInfo, string? password)
        {
            try
            {
                // 验证源目录存在
                if (!Directory.Exists(sourcePath))
                {
                    throw new DirectoryNotFoundException($"源目录不存在: {sourcePath}");
                }

                // 创建备份根目录
                Directory.CreateDirectory(backupPath);

                // 获取所有文件（包括子目录中的文件）
                var allFiles = Directory.GetFiles(sourcePath, "*", SearchOption.AllDirectories);

                Console.WriteLine($"📁 正在备份目录: {Path.GetFileName(sourcePath)}");
                Console.WriteLine($"   发现 {allFiles.Length} 个文件");

                long totalOriginalSize = 0;
                int processedFiles = 0;
                var failedFiles = new List<string>();

                // 逐个处理每个文件
                foreach (var sourceFile in allFiles)
                {
                    try
                    {
                        // 计算相对路径以保持目录结构
                        var relativePath = Path.GetRelativePath(sourcePath, sourceFile);
                        var targetPath = Path.Combine(backupPath, relativePath);

                        // 确保目标目录存在
                        var targetDirectory = Path.GetDirectoryName(targetPath);
                        if (!string.IsNullOrEmpty(targetDirectory))
                        {
                            Directory.CreateDirectory(targetDirectory);
                        }

                        // 获取源文件大小
                        var sourceFileInfo = new FileInfo(sourceFile);
                        totalOriginalSize += sourceFileInfo.Length;

                        // 根据加密设置执行备份
                        if (backupInfo.IsEncrypted && !string.IsNullOrEmpty(password))
                        {
                            // 加密备份：添加.enc扩展名
                            var encryptedTargetPath = targetPath + EncryptedFileExtension;
                            _encryption.EncryptFile(sourceFile, encryptedTargetPath, password);
                        }
                        else
                        {
                            // 普通备份：直接复制
                            File.Copy(sourceFile, targetPath, overwrite: true);
                        }

                        // 记录成功备份的文件
                        backupInfo.Files.Add(sourceFile);
                        processedFiles++;

                        // 显示进度（每处理10个文件显示一次）
                        if (processedFiles % 10 == 0 || processedFiles == allFiles.Length)
                        {
                            var progress = (double)processedFiles / allFiles.Length * 100;
                            Console.WriteLine($"   进度: {progress:F1}% ({processedFiles}/{allFiles.Length})");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录失败的文件，但继续处理其他文件
                        failedFiles.Add(sourceFile);
                        Console.WriteLine($"⚠️  备份文件失败: {Path.GetFileName(sourceFile)} - {ex.Message}");
                    }
                }

                // 更新备份信息统计
                backupInfo.OriginalSize = totalOriginalSize;
                backupInfo.BackupSize = GetDirectorySize(backupPath);

                // 输出备份结果统计
                Console.WriteLine($"✅ 目录备份完成");
                Console.WriteLine($"   成功备份: {processedFiles} 个文件");
                if (failedFiles.Count > 0)
                {
                    Console.WriteLine($"   失败文件: {failedFiles.Count} 个");
                }
                Console.WriteLine($"   {backupInfo.GetFormattedSizes()}");

                var compressionRatio = backupInfo.GetCompressionRatio();
                if (compressionRatio > 0)
                {
                    Console.WriteLine($"   压缩率: {compressionRatio:F1}%");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份目录失败: {Path.GetFileName(sourcePath)} - {ex.Message}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        /// <summary>
        /// 查找自上次备份以来发生变更的文件
        ///
        /// 通过比较文件修改时间、大小和哈希值来检测文件变更
        /// 这是增量备份的核心算法，确保只备份真正发生变化的文件
        /// </summary>
        /// <param name="sourcePath">
        /// 当前要检查的源路径
        /// 可以是单个文件或目录
        /// </param>
        /// <param name="lastBackup">
        /// 上次备份的信息对象
        /// 包含上次备份的文件列表、时间戳等信息
        /// </param>
        /// <returns>
        /// 发生变更的文件路径列表
        /// • 新增的文件
        /// • 修改过的文件
        /// • 不包括删除的文件（增量备份不处理删除）
        /// </returns>
        private List<string> FindChangedFiles(string sourcePath, BackupInfo lastBackup)
        {
            var changedFiles = new List<string>();
            var checkedFiles = 0;
            var newFiles = 0;
            var modifiedFiles = 0;

            try
            {
                Console.WriteLine($"🔍 正在检测文件变更...");
                Console.WriteLine($"   基准时间: {lastBackup.BackupTime:yyyy-MM-dd HH:mm:ss}");

                if (File.Exists(sourcePath))
                {
                    // 单文件变更检测
                    Console.WriteLine($"   检查单个文件: {Path.GetFileName(sourcePath)}");

                    // 使用哈希值进行精确比较
                    var currentHash = _encryption.CalculateSHA256(sourcePath);
                    if (currentHash != lastBackup.Hash)
                    {
                        changedFiles.Add(sourcePath);
                        modifiedFiles++;
                        Console.WriteLine($"   ✓ 文件已变更（哈希不匹配）");
                    }
                    else
                    {
                        Console.WriteLine($"   - 文件未变更");
                    }

                    checkedFiles = 1;
                }
                else if (Directory.Exists(sourcePath))
                {
                    // 目录变更检测
                    var currentFiles = Directory.GetFiles(sourcePath, "*", SearchOption.AllDirectories);
                    Console.WriteLine($"   检查目录，发现 {currentFiles.Length} 个文件");

                    // 创建上次备份文件的快速查找集合
                    var lastBackupFilesSet = new HashSet<string>(lastBackup.Files, StringComparer.OrdinalIgnoreCase);

                    foreach (var currentFile in currentFiles)
                    {
                        checkedFiles++;
                        var fileName = Path.GetFileName(currentFile);

                        try
                        {
                            if (!lastBackupFilesSet.Contains(currentFile))
                            {
                                // 新增文件
                                changedFiles.Add(currentFile);
                                newFiles++;
                                Console.WriteLine($"   + 新文件: {fileName}");
                            }
                            else
                            {
                                // 检查现有文件是否修改
                                var currentFileInfo = new FileInfo(currentFile);

                                // 首先使用修改时间进行快速检查
                                if (currentFileInfo.LastWriteTime > lastBackup.BackupTime)
                                {
                                    // 进一步检查文件大小
                                    var wasModified = true;

                                    // 对于精确的变更检测，可以计算文件哈希进行比较
                                    // 但这会增加检测时间，特别是对于大文件
                                    // 当前实现基于修改时间，这在大多数情况下是可靠的
                                    // 如果需要更精确的检测，可以启用哈希比较（需要额外配置）

                                    if (wasModified)
                                    {
                                        changedFiles.Add(currentFile);
                                        modifiedFiles++;
                                        Console.WriteLine($"   * 已修改: {fileName} ({currentFileInfo.LastWriteTime:MM-dd HH:mm})");
                                    }
                                }
                            }

                            // 显示进度（每检查50个文件显示一次）
                            if (checkedFiles % 50 == 0)
                            {
                                var progress = (double)checkedFiles / currentFiles.Length * 100;
                                Console.WriteLine($"   检查进度: {progress:F1}% ({checkedFiles}/{currentFiles.Length})");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"⚠️  检查文件失败: {fileName} - {ex.Message}");
                            // 对于无法检查的文件，保守地将其标记为变更
                            changedFiles.Add(currentFile);
                        }
                    }
                }

                // 输出检测结果统计
                Console.WriteLine($"✅ 变更检测完成");
                Console.WriteLine($"   检查文件: {checkedFiles} 个");
                Console.WriteLine($"   新增文件: {newFiles} 个");
                Console.WriteLine($"   修改文件: {modifiedFiles} 个");
                Console.WriteLine($"   总变更: {changedFiles.Count} 个");

                return changedFiles;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 文件变更检测失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 备份变更的文件列表
        ///
        /// 专门用于增量备份，将变更的文件备份到指定目录
        /// 与完整备份不同，这里不保持原有的目录结构，而是将所有文件平铺存储
        /// </summary>
        /// <param name="files">
        /// 需要备份的文件路径列表
        /// 通常是通过变更检测得到的文件列表
        /// </param>
        /// <param name="backupPath">
        /// 增量备份的目标目录
        /// 所有变更文件将存储在此目录中
        /// </param>
        /// <param name="backupInfo">
        /// 备份信息对象，用于记录备份统计数据
        /// </param>
        /// <param name="password">
        /// 加密密码（当需要加密时）
        /// </param>
        /// <exception cref="IOException">当发生I/O错误时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有访问权限时抛出</exception>
        private void BackupChangedFiles(List<string> files, string backupPath, BackupInfo backupInfo, string? password)
        {
            try
            {
                // 创建备份目录
                Directory.CreateDirectory(backupPath);

                long totalOriginalSize = 0;
                int processedFiles = 0;
                var failedFiles = new List<string>();

                Console.WriteLine($"📦 正在备份 {files.Count} 个变更文件...");

                // 逐个备份变更的文件
                foreach (var sourceFile in files)
                {
                    try
                    {
                        var fileName = Path.GetFileName(sourceFile);
                        var targetPath = Path.Combine(backupPath, fileName);

                        // 处理文件名冲突（如果有多个同名文件来自不同目录）
                        var counter = 1;
                        var originalTargetPath = targetPath;
                        while (File.Exists(targetPath) || File.Exists(targetPath + EncryptedFileExtension))
                        {
                            var nameWithoutExt = Path.GetFileNameWithoutExtension(originalTargetPath);
                            var extension = Path.GetExtension(originalTargetPath);
                            targetPath = Path.Combine(backupPath, $"{nameWithoutExt}_{counter}{extension}");
                            counter++;
                        }

                        // 获取源文件大小
                        var sourceFileInfo = new FileInfo(sourceFile);
                        totalOriginalSize += sourceFileInfo.Length;

                        // 根据加密设置执行备份
                        if (backupInfo.IsEncrypted && !string.IsNullOrEmpty(password))
                        {
                            // 加密备份
                            var encryptedTargetPath = targetPath + EncryptedFileExtension;
                            _encryption.EncryptFile(sourceFile, encryptedTargetPath, password);
                            Console.WriteLine($"   🔒 {fileName} (已加密)");
                        }
                        else
                        {
                            // 普通备份
                            File.Copy(sourceFile, targetPath, overwrite: true);
                            Console.WriteLine($"   📄 {fileName}");
                        }

                        processedFiles++;
                    }
                    catch (Exception ex)
                    {
                        failedFiles.Add(sourceFile);
                        Console.WriteLine($"⚠️  备份文件失败: {Path.GetFileName(sourceFile)} - {ex.Message}");
                    }
                }

                // 更新备份统计信息
                backupInfo.OriginalSize = totalOriginalSize;
                backupInfo.BackupSize = GetDirectorySize(backupPath);

                // 输出备份结果
                Console.WriteLine($"✅ 变更文件备份完成");
                Console.WriteLine($"   成功备份: {processedFiles} 个文件");
                if (failedFiles.Count > 0)
                {
                    Console.WriteLine($"   失败文件: {failedFiles.Count} 个");
                }
                Console.WriteLine($"   {backupInfo.GetFormattedSizes()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 备份变更文件失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 恢复完整备份的私有实现
        ///
        /// 处理完整备份的恢复逻辑，支持单文件和目录的恢复
        /// 自动处理加密文件的解密和目录结构的重建
        /// </summary>
        /// <param name="backupInfo">
        /// 完整备份的信息对象
        /// 必须是BackupType.Full类型的备份
        /// </param>
        /// <param name="restorePath">
        /// 数据恢复的目标路径
        /// 恢复的文件将放置在此目录中
        /// </param>
        /// <param name="password">
        /// 解密密码（当备份已加密时必需）
        /// </param>
        /// <param name="overwriteExisting">
        /// 是否覆盖已存在的文件
        /// </param>
        /// <returns>
        /// 恢复操作是否成功
        /// true表示完全成功，false表示失败或部分失败
        /// </returns>
        private bool RestoreFullBackup(BackupInfo backupInfo, string restorePath, string? password,
            bool overwriteExisting = true)
        {
            try
            {
                int restoredFiles = 0;
                int failedFiles = 0;
                long totalRestoredSize = 0;

                if (File.Exists(backupInfo.BackupPath))
                {
                    // 单文件恢复处理
                    Console.WriteLine($"🔄 正在恢复单个文件...");

                    var originalFileName = Path.GetFileName(backupInfo.SourcePath);
                    var targetPath = Path.Combine(restorePath, originalFileName);

                    // 检查目标文件是否已存在
                    if (File.Exists(targetPath) && !overwriteExisting)
                    {
                        Console.WriteLine($"⚠️  目标文件已存在，跳过: {originalFileName}");
                        return false;
                    }

                    if (backupInfo.IsEncrypted && !string.IsNullOrEmpty(password))
                    {
                        // 解密恢复
                        Console.WriteLine($"🔓 正在解密文件: {originalFileName}");
                        var success = _encryption.DecryptFile(backupInfo.BackupPath, targetPath, password);

                        if (success)
                        {
                            restoredFiles = 1;
                            totalRestoredSize = new FileInfo(targetPath).Length;
                            Console.WriteLine($"✅ 文件解密恢复成功: {originalFileName}");
                        }
                        else
                        {
                            Console.WriteLine($"❌ 文件解密失败: {originalFileName}");
                            return false;
                        }
                    }
                    else
                    {
                        // 普通文件恢复
                        File.Copy(backupInfo.BackupPath, targetPath, overwrite: overwriteExisting);
                        restoredFiles = 1;
                        totalRestoredSize = new FileInfo(targetPath).Length;
                        Console.WriteLine($"✅ 文件恢复成功: {originalFileName}");
                    }
                }
                else if (Directory.Exists(backupInfo.BackupPath))
                {
                    // 目录恢复处理
                    var allBackupFiles = Directory.GetFiles(backupInfo.BackupPath, "*", SearchOption.AllDirectories);
                    Console.WriteLine($"🔄 正在恢复目录，包含 {allBackupFiles.Length} 个文件...");

                    foreach (var backupFile in allBackupFiles)
                    {
                        try
                        {
                            // 计算相对路径以重建目录结构
                            var relativePath = Path.GetRelativePath(backupInfo.BackupPath, backupFile);
                            var targetPath = Path.Combine(restorePath, relativePath);

                            // 确保目标目录存在
                            var targetDirectory = Path.GetDirectoryName(targetPath);
                            if (!string.IsNullOrEmpty(targetDirectory))
                            {
                                Directory.CreateDirectory(targetDirectory);
                            }

                            // 检查是否为加密文件
                            var isEncryptedFile = backupInfo.IsEncrypted && backupFile.EndsWith(EncryptedFileExtension);

                            if (isEncryptedFile && !string.IsNullOrEmpty(password))
                            {
                                // 解密恢复：移除.enc扩展名
                                var decryptedTargetPath = targetPath.Replace(EncryptedFileExtension, "");

                                // 检查目标文件是否已存在
                                if (File.Exists(decryptedTargetPath) && !overwriteExisting)
                                {
                                    Console.WriteLine($"⚠️  目标文件已存在，跳过: {Path.GetFileName(decryptedTargetPath)}");
                                    continue;
                                }

                                var success = _encryption.DecryptFile(backupFile, decryptedTargetPath, password);
                                if (success)
                                {
                                    restoredFiles++;
                                    totalRestoredSize += new FileInfo(decryptedTargetPath).Length;
                                }
                                else
                                {
                                    failedFiles++;
                                    Console.WriteLine($"❌ 解密失败: {Path.GetFileName(backupFile)}");
                                }
                            }
                            else
                            {
                                // 普通文件恢复
                                if (File.Exists(targetPath) && !overwriteExisting)
                                {
                                    Console.WriteLine($"⚠️  目标文件已存在，跳过: {Path.GetFileName(targetPath)}");
                                    continue;
                                }

                                File.Copy(backupFile, targetPath, overwrite: overwriteExisting);
                                restoredFiles++;
                                totalRestoredSize += new FileInfo(targetPath).Length;
                            }

                            // 显示进度（每恢复10个文件显示一次）
                            if (restoredFiles % 10 == 0)
                            {
                                var progress = (double)(restoredFiles + failedFiles) / allBackupFiles.Length * 100;
                                Console.WriteLine($"   恢复进度: {progress:F1}% ({restoredFiles + failedFiles}/{allBackupFiles.Length})");
                            }
                        }
                        catch (Exception ex)
                        {
                            failedFiles++;
                            Console.WriteLine($"❌ 恢复文件失败: {Path.GetFileName(backupFile)} - {ex.Message}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"❌ 备份路径不存在: {backupInfo.BackupPath}");
                    return false;
                }

                // 输出恢复结果统计
                Console.WriteLine($"✅ 完整备份恢复完成");
                Console.WriteLine($"   成功恢复: {restoredFiles} 个文件");
                if (failedFiles > 0)
                {
                    Console.WriteLine($"   失败文件: {failedFiles} 个");
                }
                Console.WriteLine($"   恢复数据大小: {FormatBytes(totalRestoredSize)}");

                // 如果有失败的文件，返回false表示部分失败
                return failedFiles == 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 恢复完整备份时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 格式化字节数为可读字符串的辅助方法
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的字符串</returns>
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            double number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:N1} {suffixes[counter]}";
        }

        /// <summary>
        /// 保存备份元数据信息到JSON文件
        ///
        /// 将备份信息序列化为JSON格式并保存到备份目录中
        /// 这些元数据文件用于备份管理、恢复操作和历史记录查询
        /// </summary>
        /// <param name="backupInfo">
        /// 要保存的备份信息对象
        /// 包含备份的所有元数据信息
        /// </param>
        /// <param name="backupDirectory">
        /// 备份信息文件的存储目录
        /// 通常与备份数据文件在同一目录
        /// </param>
        /// <remarks>
        /// 备份信息文件命名格式：{BackupId}.backup.json
        /// 使用JSON格式便于人工查看和程序解析
        /// 包含缩进格式化以提高可读性
        /// </remarks>
        private void SaveBackupInfo(BackupInfo backupInfo, string backupDirectory)
        {
            try
            {
                // 构造备份信息文件路径
                var infoFileName = $"{backupInfo.BackupId}{BackupInfoExtension}";
                var infoFilePath = Path.Combine(backupDirectory, infoFileName);

                // 配置JSON序列化选项
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,           // 格式化输出，便于阅读
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,  // 使用驼峰命名
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull  // 忽略null值
                };

                // 序列化备份信息为JSON
                var jsonContent = JsonSerializer.Serialize(backupInfo, jsonOptions);

                // 写入文件
                File.WriteAllText(infoFilePath, jsonContent, Encoding.UTF8);

                Console.WriteLine($"💾 备份信息已保存: {infoFileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 保存备份信息失败: {ex.Message}");
                // 不重新抛出异常，因为备份数据已经成功，元数据保存失败不应该影响整个备份操作
            }
        }

        /// <summary>
        /// 计算目录的总大小
        ///
        /// 递归计算指定目录及其所有子目录中所有文件的总大小
        /// 用于统计备份数据的实际占用空间
        /// </summary>
        /// <param name="directoryPath">
        /// 要计算大小的目录路径
        /// 必须是存在的目录
        /// </param>
        /// <returns>
        /// 目录总大小（字节）
        /// 如果计算失败返回0
        /// </returns>
        /// <remarks>
        /// 此方法会遍历所有子目录和文件，对于包含大量文件的目录可能较慢
        /// 在计算过程中如果遇到访问权限问题，会跳过相关文件并继续计算
        /// </remarks>
        private long GetDirectorySize(string directoryPath)
        {
            try
            {
                // 验证目录存在
                if (!Directory.Exists(directoryPath))
                {
                    return 0;
                }

                // 获取所有文件并计算总大小
                // 使用LINQ的Sum方法进行聚合计算
                var totalSize = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories)
                    .Select(filePath =>
                    {
                        try
                        {
                            // 获取每个文件的大小
                            return new FileInfo(filePath).Length;
                        }
                        catch
                        {
                            // 如果无法访问某个文件，返回0（跳过该文件）
                            return 0L;
                        }
                    })
                    .Sum();

                return totalSize;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️  计算目录大小失败: {directoryPath} - {ex.Message}");
                return 0;
            }
        }

        #endregion
    }
}
