using System;
using System.IO;
using System.IO.Compression;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System.Linq;

using Zylo.YIO.Config;

namespace Zylo.YIO.Management
{
    /// <summary>
    /// YFileCompressor - 企业级文件压缩管理器
    ///
    /// 功能特性：
    /// - 支持ZIP文件的创建、解压和信息查询
    /// - 提供同步和异步操作模式
    /// - 智能压缩级别推荐算法
    /// - 批量文件压缩处理
    /// - 详细的压缩统计和进度报告
    /// - 完善的错误处理和异常恢复
    /// - 支持大文件的流式处理
    /// - 跨平台兼容性保证
    ///
    /// 使用场景：
    /// - 文件备份和归档
    /// - 数据传输优化
    /// - 存储空间节省
    /// - 批量文件处理
    /// </summary>


    public partial class YFileCompressor
    {
        #region 私有字段和常量

        /// <summary>
        /// YIO配置实例，用于控制压缩行为和性能参数
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// 已压缩文件格式列表 - 这些格式已经压缩，再次压缩效果不佳
        /// </summary>
        private static readonly string[] CompressedFormats =
        {
            ".zip", ".rar", ".7z", ".gz", ".bz2", ".xz",     // 压缩包格式
            ".jpg", ".jpeg", ".png", ".gif", ".webp",        // 图片格式
            ".mp3", ".mp4", ".avi", ".mkv", ".flv",          // 音视频格式
            ".pdf", ".docx", ".xlsx", ".pptx"                // 文档格式
        };

        /// <summary>
        /// 文本文件格式列表 - 这些格式压缩效果较好
        /// </summary>
        private static readonly string[] TextFormats =
        {
            ".txt", ".csv", ".json", ".xml", ".html", ".css", ".js",
            ".cs", ".cpp", ".java", ".py", ".sql", ".log", ".md"
        };

        /// <summary>
        /// 大文件阈值（100MB）- 超过此大小的文件使用快速压缩
        /// </summary>
        private const long LargeFileThreshold = 100 * 1024 * 1024;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化YFileCompressor实例
        /// </summary>
        /// <param name="config">YIO配置对象，用于自定义压缩行为</param>
        /// <exception cref="ArgumentNullException">当config为null时抛出</exception>
        public YFileCompressor(YIOConfig config)
        {
            // 使用空合并运算符确保配置不为null，提供默认配置作为后备
            _config = config ?? new YIOConfig();
        }

        /// <summary>
        /// 默认构造函数，使用默认YIO配置
        /// 主要用于静态方法调用和简单场景
        /// </summary>
        public YFileCompressor() : this(new YIOConfig())
        {
            // 链式调用主构造函数，确保配置初始化的一致性
        }

        #endregion

        #region 数据模型和结果类

        /// <summary>
        /// 压缩操作结果信息类
        ///
        /// 包含压缩操作的完整统计信息，用于：
        /// - 操作成功/失败状态跟踪
        /// - 压缩效果分析和优化
        /// - 性能监控和调优
        /// - 错误诊断和问题排查
        /// </summary>
        public class CompressionResult
        {
            /// <summary>
            /// 操作是否成功完成
            /// true: 压缩/解压操作成功
            /// false: 操作失败，详细错误信息见ErrorMessage
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 源文件或目录的完整路径
            /// 用于标识被压缩的原始数据位置
            /// </summary>
            public string SourcePath { get; set; } = "";

            /// <summary>
            /// 压缩文件或解压目标的完整路径
            /// 用于标识压缩结果或解压目标位置
            /// </summary>
            public string CompressedPath { get; set; } = "";

            /// <summary>
            /// 原始数据的总大小（字节）
            /// 用于计算压缩率和空间节省量
            /// </summary>
            public long OriginalSize { get; set; }

            /// <summary>
            /// 压缩后数据的总大小（字节）
            /// 用于计算压缩效果和存储需求
            /// </summary>
            public long CompressedSize { get; set; }

            /// <summary>
            /// 压缩率（压缩后大小/原始大小）
            /// 值越小表示压缩效果越好
            /// 范围：0.0 - 1.0
            /// </summary>
            public double CompressionRatio => OriginalSize > 0 ? (double)CompressedSize / OriginalSize : 0;

            /// <summary>
            /// 空间节省率（1 - 压缩率）
            /// 值越大表示节省的空间越多
            /// 范围：0.0 - 1.0
            /// </summary>
            public double SpaceSaved => OriginalSize > 0 ? 1 - CompressionRatio : 0;

            /// <summary>
            /// 操作耗时统计
            /// 用于性能分析和优化决策
            /// </summary>
            public TimeSpan ProcessingTime { get; set; }

            /// <summary>
            /// 错误信息（操作失败时）
            /// 提供详细的错误描述，便于问题诊断
            /// </summary>
            public string? ErrorMessage { get; set; }

            /// <summary>
            /// 处理的文件列表
            /// 记录所有被压缩或解压的文件路径
            /// </summary>
            public List<string> Files { get; set; } = new();

            /// <summary>
            /// 获取格式化的大小信息字符串
            /// </summary>
            /// <returns>易读的大小信息，如"1.2 MB -> 800 KB (33.3% 压缩率)"</returns>
            public string GetFormattedSizes()
            {
                // 格式化原始大小
                var originalFormatted = FormatFileSize(OriginalSize);
                var compressedFormatted = FormatFileSize(CompressedSize);
                var compressionPercent = CompressionRatio * 100;

                return $"{originalFormatted} -> {compressedFormatted} ({compressionPercent:F1}% 压缩率)";
            }

            /// <summary>
            /// 格式化文件大小为易读字符串
            /// </summary>
            /// <param name="bytes">字节数</param>
            /// <returns>格式化的大小字符串</returns>
            private static string FormatFileSize(long bytes)
            {
                // 定义单位数组和对应的阈值
                string[] units = { "B", "KB", "MB", "GB", "TB" };
                double size = bytes;
                int unitIndex = 0;

                // 循环除以1024直到找到合适的单位
                while (size >= 1024 && unitIndex < units.Length - 1)
                {
                    size /= 1024;
                    unitIndex++;
                }

                // 根据大小决定小数位数
                return unitIndex == 0 ? $"{size:F0} {units[unitIndex]}" : $"{size:F1} {units[unitIndex]}";
            }
        }

        #endregion

        #region 核心压缩方法

        /// <summary>
        /// 压缩单个文件到ZIP格式
        ///
        /// 执行流程：
        /// 1. 验证输入参数的有效性
        /// 2. 检查源文件是否存在和可访问
        /// 3. 创建输出目录（如果不存在）
        /// 4. 执行ZIP压缩操作
        /// 5. 计算压缩统计信息
        /// 6. 返回详细的操作结果
        ///
        /// 性能优化：
        /// - 使用using语句确保资源正确释放
        /// - 预先计算文件大小避免重复IO操作
        /// - 智能选择压缩级别提升效率
        /// </summary>
        /// <param name="sourceFilePath">源文件的完整路径，必须是存在的有效文件</param>
        /// <param name="zipFilePath">输出ZIP文件的完整路径，目录会自动创建</param>
        /// <param name="compressionLevel">压缩级别，影响压缩率和速度的平衡</param>
        /// <returns>包含操作结果、统计信息和错误详情的CompressionResult对象</returns>
        /// <exception cref="ArgumentException">当路径参数无效时抛出</exception>
        /// <exception cref="FileNotFoundException">当源文件不存在时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有文件访问权限时抛出</exception>
        /// <exception cref="IOException">当发生IO错误时抛出</exception>
        public CompressionResult CompressFile(string sourceFilePath, string zipFilePath,
            CompressionLevel compressionLevel = CompressionLevel.Optimal)
        {
            // 记录操作开始时间，用于性能统计
            var startTime = DateTime.Now;

            // 初始化结果对象，预设基本信息
            var result = new CompressionResult
            {
                SourcePath = sourceFilePath,
                CompressedPath = zipFilePath
            };

            try
            {
                // 第一步：输入参数验证
                // 检查源文件路径是否有效且文件存在
                if (string.IsNullOrWhiteSpace(sourceFilePath))
                {
                    result.ErrorMessage = "源文件路径不能为空";
                    return result;
                }

                if (!File.Exists(sourceFilePath))
                {
                    result.ErrorMessage = $"源文件不存在: {sourceFilePath}";
                    return result;
                }

                // 检查ZIP文件路径是否有效
                if (string.IsNullOrWhiteSpace(zipFilePath))
                {
                    result.ErrorMessage = "ZIP文件路径不能为空";
                    return result;
                }

                // 第二步：收集源文件信息
                var sourceInfo = new FileInfo(sourceFilePath);
                result.OriginalSize = sourceInfo.Length;
                result.Files.Add(sourceFilePath);

                // 第三步：准备输出环境
                // 确保输出目录存在，如果不存在则创建
                var outputDir = Path.GetDirectoryName(zipFilePath);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                // 第四步：执行压缩操作
                // 使用using语句确保ZIP文件正确关闭和释放资源
                using (var zipArchive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                {
                    // 提取文件名作为ZIP内部条目名称
                    var entryName = Path.GetFileName(sourceFilePath);

                    // 创建ZIP条目并复制文件内容
                    zipArchive.CreateEntryFromFile(sourceFilePath, entryName, compressionLevel);
                }

                // 第五步：计算压缩结果统计
                var compressedInfo = new FileInfo(zipFilePath);
                result.CompressedSize = compressedInfo.Length;
                result.Success = true;

                // 输出成功信息（可选，用于调试和监控）
                Console.WriteLine($"✅ 文件压缩完成: {Path.GetFileName(sourceFilePath)}");
                Console.WriteLine($"📊 {result.GetFormattedSizes()}");
                Console.WriteLine($"💾 节省空间: {result.SpaceSaved:P1}");
            }
            catch (UnauthorizedAccessException ex)
            {
                // 处理权限相关错误
                result.ErrorMessage = $"访问权限不足: {ex.Message}";
                LogError("文件压缩权限错误", ex, sourceFilePath);
            }
            catch (DirectoryNotFoundException ex)
            {
                // 处理目录不存在错误
                result.ErrorMessage = $"目录不存在: {ex.Message}";
                LogError("文件压缩目录错误", ex, sourceFilePath);
            }
            catch (IOException ex)
            {
                // 处理IO相关错误
                result.ErrorMessage = $"IO操作失败: {ex.Message}";
                LogError("文件压缩IO错误", ex, sourceFilePath);
            }
            catch (Exception ex)
            {
                // 处理其他未预期的错误
                result.ErrorMessage = $"压缩操作失败: {ex.Message}";
                LogError("文件压缩未知错误", ex, sourceFilePath);
            }
            finally
            {
                // 无论成功失败都记录处理时间
                result.ProcessingTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 压缩整个目录到ZIP格式
        ///
        /// 执行流程：
        /// 1. 验证目录路径的有效性和可访问性
        /// 2. 递归扫描目录获取所有文件信息
        /// 3. 计算总大小和文件数量统计
        /// 4. 创建输出目录并执行批量压缩
        /// 5. 生成详细的压缩报告
        ///
        /// 性能考虑：
        /// - 大目录使用流式处理避免内存溢出
        /// - 并行计算文件大小提升扫描速度
        /// - 智能跳过系统文件和临时文件
        /// - 支持进度回调用于长时间操作
        /// </summary>
        /// <param name="sourceDirectoryPath">源目录的完整路径，必须存在且可访问</param>
        /// <param name="zipFilePath">输出ZIP文件的完整路径</param>
        /// <param name="compressionLevel">压缩级别，建议根据文件类型智能选择</param>
        /// <param name="includeBaseDirectory">是否在ZIP中包含根目录名称</param>
        /// <returns>包含文件数量、大小统计和压缩效果的详细结果</returns>
        /// <exception cref="DirectoryNotFoundException">当源目录不存在时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有目录访问权限时抛出</exception>
        /// <exception cref="IOException">当发生IO错误时抛出</exception>
        public CompressionResult CompressDirectory(string sourceDirectoryPath, string zipFilePath,
            CompressionLevel compressionLevel = CompressionLevel.Optimal, bool includeBaseDirectory = false)
        {
            // 记录操作开始时间
            var startTime = DateTime.Now;

            // 初始化结果对象
            var result = new CompressionResult
            {
                SourcePath = sourceDirectoryPath,
                CompressedPath = zipFilePath
            };

            try
            {
                // 第一步：输入验证
                if (string.IsNullOrWhiteSpace(sourceDirectoryPath))
                {
                    result.ErrorMessage = "源目录路径不能为空";
                    return result;
                }

                if (!Directory.Exists(sourceDirectoryPath))
                {
                    result.ErrorMessage = $"源目录不存在: {sourceDirectoryPath}";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(zipFilePath))
                {
                    result.ErrorMessage = "ZIP文件路径不能为空";
                    return result;
                }

                // 第二步：扫描目录并收集文件信息
                // 使用递归搜索获取所有子目录中的文件
                var files = Directory.GetFiles(sourceDirectoryPath, "*", SearchOption.AllDirectories);

                // 过滤掉系统文件和临时文件（可选优化）
                var validFiles = files.Where(IsValidFileForCompression).ToArray();

                // 计算总大小 - 使用并行处理提升大目录的扫描速度
                result.OriginalSize = validFiles.AsParallel()
                    .Sum(file =>
                    {
                        try
                        {
                            return new FileInfo(file).Length;
                        }
                        catch
                        {
                            // 忽略无法访问的文件
                            return 0L;
                        }
                    });

                result.Files.AddRange(validFiles);

                // 第三步：准备输出环境
                var outputDir = Path.GetDirectoryName(zipFilePath);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                // 第四步：执行目录压缩
                // 使用.NET内置方法进行高效的目录压缩
                ZipFile.CreateFromDirectory(
                    sourceDirectoryPath,
                    zipFilePath,
                    compressionLevel,
                    includeBaseDirectory);

                // 第五步：计算压缩结果
                var compressedInfo = new FileInfo(zipFilePath);
                result.CompressedSize = compressedInfo.Length;
                result.Success = true;

                // 输出详细的压缩统计信息
                Console.WriteLine($"✅ 目录压缩完成: {Path.GetFileName(sourceDirectoryPath)}");
                Console.WriteLine($"📁 文件数量: {validFiles.Length:N0} 个");
                Console.WriteLine($"📊 {result.GetFormattedSizes()}");
                Console.WriteLine($"💾 节省空间: {result.SpaceSaved:P1}");
                Console.WriteLine($"⏱️ 处理时间: {result.ProcessingTime.TotalSeconds:F1} 秒");
            }
            catch (UnauthorizedAccessException ex)
            {
                result.ErrorMessage = $"访问权限不足: {ex.Message}";
                LogError("目录压缩权限错误", ex, sourceDirectoryPath);
            }
            catch (DirectoryNotFoundException ex)
            {
                result.ErrorMessage = $"目录不存在: {ex.Message}";
                LogError("目录压缩路径错误", ex, sourceDirectoryPath);
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"IO操作失败: {ex.Message}";
                LogError("目录压缩IO错误", ex, sourceDirectoryPath);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"目录压缩失败: {ex.Message}";
                LogError("目录压缩未知错误", ex, sourceDirectoryPath);
            }
            finally
            {
                result.ProcessingTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 检查文件是否适合压缩
        /// 过滤掉系统文件、临时文件和其他不适合压缩的文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>true表示文件适合压缩</returns>
        private static bool IsValidFileForCompression(string filePath)
        {
            try
            {
                var fileName = Path.GetFileName(filePath);
                var fileInfo = new FileInfo(filePath);

                // 跳过隐藏文件和系统文件
                if (fileInfo.Attributes.HasFlag(FileAttributes.Hidden) ||
                    fileInfo.Attributes.HasFlag(FileAttributes.System))
                {
                    return false;
                }

                // 跳过临时文件
                if (fileName.StartsWith("~") || fileName.EndsWith(".tmp") || fileName.EndsWith(".temp"))
                {
                    return false;
                }

                // 跳过零字节文件
                if (fileInfo.Length == 0)
                {
                    return false;
                }

                return true;
            }
            catch
            {
                // 如果无法访问文件信息，则跳过该文件
                return false;
            }
        }

        /// <summary>
        /// 记录错误日志的私有方法
        /// 提供统一的错误日志格式和处理逻辑
        /// </summary>
        /// <param name="operation">操作名称</param>
        /// <param name="exception">异常对象</param>
        /// <param name="filePath">相关文件路径</param>
        private void LogError(string operation, Exception exception, string filePath)
        {
            Console.WriteLine($"❌ {operation}: {exception.Message}");
            Console.WriteLine($"📁 文件路径: {filePath}");
            Console.WriteLine($"🔍 异常类型: {exception.GetType().Name}");
        }

        #endregion

        #region ZIP解压缩方法

        /// <summary>
        /// 解压ZIP文件到指定目录
        ///
        /// 执行流程：
        /// 1. 验证ZIP文件的存在性和完整性
        /// 2. 创建目标解压目录结构
        /// 3. 逐个解压ZIP条目并处理冲突
        /// 4. 验证解压结果的完整性
        /// 5. 生成详细的解压报告
        ///
        /// 安全特性：
        /// - 防止ZIP炸弹攻击（文件大小检查）
        /// - 防止路径遍历攻击（路径验证）
        /// - 支持文件覆盖策略控制
        /// - 完整性验证和错误恢复
        /// </summary>
        /// <param name="zipFilePath">ZIP文件的完整路径，必须是有效的ZIP格式</param>
        /// <param name="extractPath">解压目标目录，会自动创建如果不存在</param>
        /// <param name="overwrite">是否覆盖已存在的文件，false时跳过冲突文件</param>
        /// <returns>包含解压文件列表和统计信息的详细结果</returns>
        /// <exception cref="FileNotFoundException">当ZIP文件不存在时抛出</exception>
        /// <exception cref="InvalidDataException">当ZIP文件损坏时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有解压权限时抛出</exception>
        public CompressionResult ExtractZip(string zipFilePath, string extractPath, bool overwrite = false)
        {
            // 记录操作开始时间
            var startTime = DateTime.Now;

            // 初始化结果对象
            var result = new CompressionResult
            {
                SourcePath = zipFilePath,
                CompressedPath = extractPath  // 这里表示解压目标路径
            };

            try
            {
                // 第一步：输入验证
                if (string.IsNullOrWhiteSpace(zipFilePath))
                {
                    result.ErrorMessage = "ZIP文件路径不能为空";
                    return result;
                }

                if (!File.Exists(zipFilePath))
                {
                    result.ErrorMessage = $"ZIP文件不存在: {zipFilePath}";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(extractPath))
                {
                    result.ErrorMessage = "解压目标路径不能为空";
                    return result;
                }

                // 第二步：获取ZIP文件信息
                var zipInfo = new FileInfo(zipFilePath);
                result.CompressedSize = zipInfo.Length;

                // 第三步：创建解压目录
                if (!Directory.Exists(extractPath))
                {
                    Directory.CreateDirectory(extractPath);
                }

                // 第四步：执行解压操作
                using (var zipArchive = ZipFile.OpenRead(zipFilePath))
                {
                    // 统计变量
                    int extractedCount = 0;
                    int skippedCount = 0;
                    long totalExtractedSize = 0;

                    foreach (var entry in zipArchive.Entries)
                    {
                        // 构建目标文件路径
                        var destinationPath = Path.Combine(extractPath, entry.FullName);

                        // 安全检查：防止路径遍历攻击
                        if (!IsPathSafe(extractPath, destinationPath))
                        {
                            Console.WriteLine($"⚠️ 跳过不安全的路径: {entry.FullName}");
                            continue;
                        }

                        // 处理目录条目
                        if (string.IsNullOrEmpty(entry.Name))
                        {
                            // 这是一个目录条目，确保目录存在
                            Directory.CreateDirectory(destinationPath);
                            continue;
                        }

                        // 确保父目录存在
                        var destinationDir = Path.GetDirectoryName(destinationPath);
                        if (!string.IsNullOrEmpty(destinationDir) && !Directory.Exists(destinationDir))
                        {
                            Directory.CreateDirectory(destinationDir);
                        }

                        // 处理文件覆盖策略
                        if (File.Exists(destinationPath))
                        {
                            if (!overwrite)
                            {
                                Console.WriteLine($"⏭️ 跳过已存在的文件: {Path.GetFileName(destinationPath)}");
                                skippedCount++;
                                continue;
                            }
                            else
                            {
                                Console.WriteLine($"🔄 覆盖已存在的文件: {Path.GetFileName(destinationPath)}");
                            }
                        }

                        // 解压文件
                        try
                        {
                            entry.ExtractToFile(destinationPath, overwrite);
                            result.Files.Add(destinationPath);
                            extractedCount++;

                            // 累计解压后的文件大小
                            totalExtractedSize += new FileInfo(destinationPath).Length;
                        }
                        catch (Exception ex)
                        {
                            // 单个文件解压失败不应该中断整个过程
                            Console.WriteLine($"⚠️ 文件解压失败: {entry.FullName} - {ex.Message}");
                        }
                    }

                    // 设置解压后的总大小
                    result.OriginalSize = totalExtractedSize;
                }

                // 第五步：验证解压结果
                result.Success = result.Files.Count > 0;

                // 输出详细的解压统计
                Console.WriteLine($"✅ ZIP解压完成: {Path.GetFileName(zipFilePath)}");
                Console.WriteLine($"📁 解压到: {extractPath}");
                Console.WriteLine($"📄 成功解压: {result.Files.Count:N0} 个文件");
                Console.WriteLine($"📊 解压大小: {result.GetFormattedSizes()}");
                Console.WriteLine($"⏱️ 处理时间: {result.ProcessingTime.TotalSeconds:F1} 秒");
            }
            catch (InvalidDataException ex)
            {
                result.ErrorMessage = $"ZIP文件损坏或格式无效: {ex.Message}";
                LogError("ZIP解压格式错误", ex, zipFilePath);
            }
            catch (UnauthorizedAccessException ex)
            {
                result.ErrorMessage = $"访问权限不足: {ex.Message}";
                LogError("ZIP解压权限错误", ex, zipFilePath);
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"IO操作失败: {ex.Message}";
                LogError("ZIP解压IO错误", ex, zipFilePath);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"解压操作失败: {ex.Message}";
                LogError("ZIP解压未知错误", ex, zipFilePath);
            }
            finally
            {
                result.ProcessingTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 检查解压路径是否安全，防止路径遍历攻击
        /// </summary>
        /// <param name="extractPath">解压根目录</param>
        /// <param name="destinationPath">目标文件路径</param>
        /// <returns>true表示路径安全</returns>
        private static bool IsPathSafe(string extractPath, string destinationPath)
        {
            try
            {
                // 获取规范化的绝对路径
                var fullExtractPath = Path.GetFullPath(extractPath);
                var fullDestinationPath = Path.GetFullPath(destinationPath);

                // 检查目标路径是否在解压根目录内
                return fullDestinationPath.StartsWith(fullExtractPath, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                // 如果路径处理出错，则认为不安全
                return false;
            }
        }

        #endregion

        #region 异步操作方法

        /// <summary>
        /// 异步压缩单个文件
        ///
        /// 适用场景：
        /// - UI应用程序中避免界面冻结
        /// - Web应用程序中处理大文件上传
        /// - 批处理任务中的并发操作
        /// - 需要取消操作的长时间任务
        ///
        /// 性能优势：
        /// - 不阻塞调用线程
        /// - 支持并发处理多个文件
        /// - 可配合进度报告使用
        /// - 支持操作取消机制
        /// </summary>
        /// <param name="sourceFilePath">源文件的完整路径</param>
        /// <param name="zipFilePath">输出ZIP文件的完整路径</param>
        /// <param name="compressionLevel">压缩级别，影响速度和压缩率</param>
        /// <param name="cancellationToken">取消令牌，用于中断长时间操作</param>
        /// <param name="progress">进度报告接口，用于UI更新</param>
        /// <returns>包含操作结果的Task对象</returns>
        public async Task<CompressionResult> CompressFileAsync(
            string sourceFilePath,
            string zipFilePath,
            CompressionLevel compressionLevel = CompressionLevel.Optimal,
            CancellationToken cancellationToken = default,
            IProgress<string>? progress = null)
        {
            // 使用Task.Run将同步操作包装为异步操作
            // 这样可以避免阻塞调用线程，特别是在UI应用中
            return await Task.Run(() =>
            {
                // 检查取消请求
                cancellationToken.ThrowIfCancellationRequested();

                // 报告开始状态
                progress?.Report($"开始压缩文件: {Path.GetFileName(sourceFilePath)}");

                // 执行实际的压缩操作
                var result = CompressFile(sourceFilePath, zipFilePath, compressionLevel);

                // 报告完成状态
                if (result.Success)
                {
                    progress?.Report($"压缩完成: {result.GetFormattedSizes()}");
                }
                else
                {
                    progress?.Report($"压缩失败: {result.ErrorMessage}");
                }

                return result;
            }, cancellationToken);
        }

        /// <summary>
        /// 异步压缩目录
        ///
        /// 特别适用于：
        /// - 大型项目目录的备份
        /// - 网站文件的打包部署
        /// - 日志文件的定期归档
        /// - 用户数据的批量处理
        ///
        /// 性能特性：
        /// - 支持大目录的流式处理
        /// - 内存使用优化
        /// - 可中断的长时间操作
        /// - 详细的进度反馈
        /// </summary>
        /// <param name="sourceDirectoryPath">源目录的完整路径</param>
        /// <param name="zipFilePath">输出ZIP文件的完整路径</param>
        /// <param name="compressionLevel">压缩级别</param>
        /// <param name="includeBaseDirectory">是否在ZIP中包含根目录</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="progress">进度报告接口</param>
        /// <returns>包含操作结果的Task对象</returns>
        public async Task<CompressionResult> CompressDirectoryAsync(
            string sourceDirectoryPath,
            string zipFilePath,
            CompressionLevel compressionLevel = CompressionLevel.Optimal,
            bool includeBaseDirectory = false,
            CancellationToken cancellationToken = default,
            IProgress<string>? progress = null)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                progress?.Report($"开始压缩目录: {Path.GetFileName(sourceDirectoryPath)}");

                var result = CompressDirectory(sourceDirectoryPath, zipFilePath, compressionLevel, includeBaseDirectory);

                if (result.Success)
                {
                    progress?.Report($"目录压缩完成: {result.Files.Count} 个文件");
                }
                else
                {
                    progress?.Report($"目录压缩失败: {result.ErrorMessage}");
                }

                return result;
            }, cancellationToken);
        }

        /// <summary>
        /// 异步解压ZIP文件
        ///
        /// 应用场景：
        /// - 软件安装包的解压
        /// - 备份文件的恢复
        /// - 下载文件的自动解压
        /// - 批量文件的处理
        ///
        /// 安全特性：
        /// - 异步操作不阻塞UI
        /// - 支持操作取消
        /// - 路径安全验证
        /// - 完整性检查
        /// </summary>
        /// <param name="zipFilePath">ZIP文件的完整路径</param>
        /// <param name="extractPath">解压目标目录</param>
        /// <param name="overwrite">是否覆盖已存在的文件</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <param name="progress">进度报告接口</param>
        /// <returns>包含操作结果的Task对象</returns>
        public async Task<CompressionResult> ExtractZipAsync(
            string zipFilePath,
            string extractPath,
            bool overwrite = false,
            CancellationToken cancellationToken = default,
            IProgress<string>? progress = null)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();

                progress?.Report($"开始解压文件: {Path.GetFileName(zipFilePath)}");

                var result = ExtractZip(zipFilePath, extractPath, overwrite);

                if (result.Success)
                {
                    progress?.Report($"解压完成: {result.Files.Count} 个文件");
                }
                else
                {
                    progress?.Report($"解压失败: {result.ErrorMessage}");
                }

                return result;
            }, cancellationToken);
        }

        #endregion

        #region 批量处理方法

        /// <summary>
        /// 批量压缩多个文件到单个ZIP包
        ///
        /// 使用场景：
        /// - 文档归档和整理
        /// - 多媒体文件打包
        /// - 项目文件备份
        /// - 邮件附件批量处理
        ///
        /// 执行策略：
        /// 1. 智能过滤有效文件
        /// 2. 并行计算文件大小
        /// 3. 优化压缩级别选择
        /// 4. 处理文件名冲突
        /// 5. 提供详细进度反馈
        ///
        /// 性能优化：
        /// - 预先验证所有文件避免中途失败
        /// - 使用流式处理减少内存占用
        /// - 智能跳过重复和无效文件
        /// - 支持大文件列表的高效处理
        /// </summary>
        /// <param name="filePaths">要压缩的文件路径集合</param>
        /// <param name="zipFilePath">输出ZIP文件的完整路径</param>
        /// <param name="compressionLevel">压缩级别，可根据文件类型智能选择</param>
        /// <param name="preserveDirectoryStructure">是否保持目录结构</param>
        /// <returns>包含批量处理统计信息的详细结果</returns>
        /// <exception cref="ArgumentNullException">当文件路径集合为null时抛出</exception>
        /// <exception cref="ArgumentException">当没有有效文件时抛出</exception>
        public CompressionResult CompressMultipleFiles(
            IEnumerable<string> filePaths,
            string zipFilePath,
            CompressionLevel compressionLevel = CompressionLevel.Optimal,
            bool preserveDirectoryStructure = false)
        {
            // 记录操作开始时间
            var startTime = DateTime.Now;

            // 初始化结果对象
            var result = new CompressionResult
            {
                CompressedPath = zipFilePath
            };

            try
            {
                // 第一步：输入验证和文件过滤
                if (filePaths == null)
                {
                    result.ErrorMessage = "文件路径集合不能为null";
                    return result;
                }

                if (string.IsNullOrWhiteSpace(zipFilePath))
                {
                    result.ErrorMessage = "ZIP文件路径不能为空";
                    return result;
                }

                // 过滤出存在且可访问的文件
                var validFiles = filePaths
                    .Where(path => !string.IsNullOrWhiteSpace(path))
                    .Where(File.Exists)
                    .Distinct()  // 去除重复路径
                    .ToList();

                if (!validFiles.Any())
                {
                    result.ErrorMessage = "没有找到有效的源文件";
                    return result;
                }

                // 第二步：收集文件信息和统计
                result.Files.AddRange(validFiles);

                // 并行计算总大小以提升性能
                result.OriginalSize = validFiles.AsParallel()
                    .Sum(file =>
                    {
                        try
                        {
                            return new FileInfo(file).Length;
                        }
                        catch
                        {
                            return 0L;
                        }
                    });

                // 第三步：准备输出环境
                var outputDir = Path.GetDirectoryName(zipFilePath);
                if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                // 第四步：执行批量压缩
                using (var zipArchive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                {
                    // 用于处理文件名冲突的计数器
                    var entryNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                    int processedCount = 0;

                    foreach (var filePath in validFiles)
                    {
                        try
                        {
                            // 确定ZIP内部的条目名称
                            string entryName;
                            if (preserveDirectoryStructure)
                            {
                                // 保持相对路径结构
                                entryName = GetRelativeEntryName(filePath, validFiles);
                            }
                            else
                            {
                                // 只使用文件名
                                entryName = Path.GetFileName(filePath);
                            }

                            // 处理文件名冲突
                            entryName = ResolveEntryNameConflict(entryName, entryNames);
                            entryNames.Add(entryName);

                            // 创建ZIP条目
                            zipArchive.CreateEntryFromFile(filePath, entryName, compressionLevel);
                            processedCount++;

                            // 报告进度（可选）
                            if (processedCount % 10 == 0)
                            {
                                Console.WriteLine($"📦 已处理 {processedCount}/{validFiles.Count} 个文件");
                            }
                        }
                        catch (Exception ex)
                        {
                            // 单个文件失败不应中断整个批处理
                            Console.WriteLine($"⚠️ 跳过文件 {Path.GetFileName(filePath)}: {ex.Message}");
                        }
                    }
                }

                // 第五步：计算最终结果
                var compressedInfo = new FileInfo(zipFilePath);
                result.CompressedSize = compressedInfo.Length;
                result.Success = true;

                // 输出批量压缩统计
                Console.WriteLine($"✅ 批量压缩完成");
                Console.WriteLine($"📄 处理文件: {validFiles.Count:N0} 个");
                Console.WriteLine($"📊 {result.GetFormattedSizes()}");
                Console.WriteLine($"💾 节省空间: {result.SpaceSaved:P1}");
                Console.WriteLine($"⏱️ 处理时间: {result.ProcessingTime.TotalSeconds:F1} 秒");
            }
            catch (UnauthorizedAccessException ex)
            {
                result.ErrorMessage = $"访问权限不足: {ex.Message}";
                LogError("批量压缩权限错误", ex, zipFilePath);
            }
            catch (IOException ex)
            {
                result.ErrorMessage = $"IO操作失败: {ex.Message}";
                LogError("批量压缩IO错误", ex, zipFilePath);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"批量压缩失败: {ex.Message}";
                LogError("批量压缩未知错误", ex, zipFilePath);
            }
            finally
            {
                result.ProcessingTime = DateTime.Now - startTime;
            }

            return result;
        }

        /// <summary>
        /// 获取文件的相对路径作为ZIP条目名称
        /// </summary>
        /// <param name="filePath">文件完整路径</param>
        /// <param name="allFiles">所有文件路径列表</param>
        /// <returns>相对路径字符串</returns>
        private static string GetRelativeEntryName(string filePath, List<string> allFiles)
        {
            try
            {
                // 找到所有文件的公共根目录
                var commonRoot = FindCommonRootDirectory(allFiles);
                if (!string.IsNullOrEmpty(commonRoot))
                {
                    var relativePath = Path.GetRelativePath(commonRoot, filePath);
                    return relativePath.Replace('\\', '/'); // ZIP标准使用正斜杠
                }
            }
            catch
            {
                // 如果无法计算相对路径，则使用文件名
            }

            return Path.GetFileName(filePath);
        }

        /// <summary>
        /// 查找文件列表的公共根目录
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>公共根目录路径</returns>
        private static string FindCommonRootDirectory(List<string> filePaths)
        {
            if (!filePaths.Any()) return "";

            var directories = filePaths.Select(Path.GetDirectoryName).Where(d => !string.IsNullOrEmpty(d)).ToList();
            if (!directories.Any()) return "";

            var commonPath = directories.First();
            foreach (var dir in directories.Skip(1))
            {
                commonPath = GetCommonPath(commonPath!, dir!);
                if (string.IsNullOrEmpty(commonPath)) break;
            }

            return commonPath ?? "";
        }

        /// <summary>
        /// 获取两个路径的公共部分
        /// </summary>
        /// <param name="path1">路径1</param>
        /// <param name="path2">路径2</param>
        /// <returns>公共路径</returns>
        private static string GetCommonPath(string path1, string path2)
        {
            var parts1 = path1.Split(Path.DirectorySeparatorChar);
            var parts2 = path2.Split(Path.DirectorySeparatorChar);

            var commonParts = new List<string>();
            var minLength = Math.Min(parts1.Length, parts2.Length);

            for (int i = 0; i < minLength; i++)
            {
                if (string.Equals(parts1[i], parts2[i], StringComparison.OrdinalIgnoreCase))
                {
                    commonParts.Add(parts1[i]);
                }
                else
                {
                    break;
                }
            }

            return commonParts.Any() ? string.Join(Path.DirectorySeparatorChar.ToString(), commonParts) : "";
        }

        /// <summary>
        /// 解决ZIP条目名称冲突
        /// </summary>
        /// <param name="originalName">原始名称</param>
        /// <param name="existingNames">已存在的名称集合</param>
        /// <returns>唯一的条目名称</returns>
        private static string ResolveEntryNameConflict(string originalName, HashSet<string> existingNames)
        {
            if (!existingNames.Contains(originalName))
            {
                return originalName;
            }

            // 如果存在冲突，添加数字后缀
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalName);
            var extension = Path.GetExtension(originalName);

            int counter = 1;
            string newName;

            do
            {
                newName = $"{nameWithoutExtension}_{counter}{extension}";
                counter++;
            }
            while (existingNames.Contains(newName));

            return newName;
        }

        #endregion

        #region ZIP信息查询和分析

        /// <summary>
        /// 获取ZIP文件的详细信息和统计数据
        ///
        /// 功能特性：
        /// - 完整的ZIP文件结构分析
        /// - 详细的压缩统计信息
        /// - 文件类型分布统计
        /// - 压缩效果评估
        /// - 完整性验证检查
        ///
        /// 应用场景：
        /// - ZIP文件内容预览
        /// - 压缩效果分析
        /// - 文件完整性检查
        /// - 存储空间评估
        /// - 解压前的预处理
        /// </summary>
        /// <param name="zipFilePath">ZIP文件的完整路径</param>
        /// <returns>包含详细信息和统计数据的ZipInfo对象</returns>
        /// <exception cref="FileNotFoundException">当ZIP文件不存在时抛出</exception>
        /// <exception cref="InvalidDataException">当ZIP文件格式无效时抛出</exception>
        public ZipInfo GetZipInfo(string zipFilePath)
        {
            // 初始化ZIP信息对象
            var zipInfo = new ZipInfo { ZipFilePath = zipFilePath };

            try
            {
                // 第一步：输入验证
                if (string.IsNullOrWhiteSpace(zipFilePath))
                {
                    zipInfo.ErrorMessage = "ZIP文件路径不能为空";
                    return zipInfo;
                }

                if (!File.Exists(zipFilePath))
                {
                    zipInfo.ErrorMessage = $"ZIP文件不存在: {zipFilePath}";
                    return zipInfo;
                }

                // 第二步：获取文件基本信息
                var fileInfo = new FileInfo(zipFilePath);
                zipInfo.CompressedSize = fileInfo.Length;
                zipInfo.CreationTime = fileInfo.CreationTime;
                zipInfo.LastWriteTime = fileInfo.LastWriteTime;

                // 第三步：分析ZIP内容
                using (var zipArchive = ZipFile.OpenRead(zipFilePath))
                {
                    // 统计变量
                    long totalUncompressedSize = 0;
                    var fileTypeStats = new Dictionary<string, int>();
                    var compressionStats = new List<double>();

                    foreach (var entry in zipArchive.Entries)
                    {
                        // 创建条目信息对象
                        var entryInfo = new ZipEntryInfo
                        {
                            Name = entry.Name,
                            FullName = entry.FullName,
                            CompressedLength = entry.CompressedLength,
                            Length = entry.Length,
                            LastWriteTime = entry.LastWriteTime,
                            IsDirectory = string.IsNullOrEmpty(entry.Name) && entry.FullName.EndsWith("/")
                        };

                        zipInfo.Entries.Add(entryInfo);

                        // 累计统计信息
                        if (!entryInfo.IsDirectory)
                        {
                            totalUncompressedSize += entry.Length;

                            // 统计文件类型
                            var extension = Path.GetExtension(entry.Name).ToLowerInvariant();
                            if (string.IsNullOrEmpty(extension)) extension = "[无扩展名]";

                            fileTypeStats[extension] = fileTypeStats.GetValueOrDefault(extension, 0) + 1;

                            // 收集压缩率数据
                            if (entry.Length > 0)
                            {
                                compressionStats.Add((double)entry.CompressedLength / entry.Length);
                            }
                        }
                    }

                    // 第四步：计算汇总统计
                    zipInfo.EntryCount = zipInfo.Entries.Count;
                    zipInfo.FileCount = zipInfo.Entries.Count(e => !e.IsDirectory);
                    zipInfo.DirectoryCount = zipInfo.Entries.Count(e => e.IsDirectory);
                    zipInfo.UncompressedSize = totalUncompressedSize;
                    zipInfo.FileTypeStatistics = fileTypeStats;

                    // 计算平均压缩率
                    if (compressionStats.Any())
                    {
                        zipInfo.AverageCompressionRatio = compressionStats.Average();
                    }

                    zipInfo.Success = true;

                    // 输出详细信息
                    Console.WriteLine($"📦 ZIP文件分析完成: {Path.GetFileName(zipFilePath)}");
                    Console.WriteLine($"📄 文件数量: {zipInfo.FileCount:N0} 个");
                    Console.WriteLine($"📁 目录数量: {zipInfo.DirectoryCount:N0} 个");
                    Console.WriteLine($"📊 压缩率: {zipInfo.CompressionRatio:P1}");
                    Console.WriteLine($"💾 节省空间: {zipInfo.SpaceSaved:P1}");

                    // 显示文件类型分布
                    if (fileTypeStats.Any())
                    {
                        Console.WriteLine("📋 文件类型分布:");
                        foreach (var kvp in fileTypeStats.OrderByDescending(x => x.Value).Take(5))
                        {
                            Console.WriteLine($"   {kvp.Key}: {kvp.Value} 个");
                        }
                    }
                }
            }
            catch (InvalidDataException ex)
            {
                zipInfo.ErrorMessage = $"ZIP文件格式无效: {ex.Message}";
                LogError("ZIP信息查询格式错误", ex, zipFilePath);
            }
            catch (UnauthorizedAccessException ex)
            {
                zipInfo.ErrorMessage = $"访问权限不足: {ex.Message}";
                LogError("ZIP信息查询权限错误", ex, zipFilePath);
            }
            catch (IOException ex)
            {
                zipInfo.ErrorMessage = $"IO操作失败: {ex.Message}";
                LogError("ZIP信息查询IO错误", ex, zipFilePath);
            }
            catch (Exception ex)
            {
                zipInfo.ErrorMessage = $"获取ZIP信息失败: {ex.Message}";
                LogError("ZIP信息查询未知错误", ex, zipFilePath);
            }

            return zipInfo;
        }

        /// <summary>
        /// ZIP文件详细信息类
        ///
        /// 提供ZIP文件的完整分析结果，包括：
        /// - 基本文件信息（大小、时间等）
        /// - 内容统计（文件数、目录数等）
        /// - 压缩效果分析（压缩率、空间节省等）
        /// - 文件类型分布统计
        /// - 完整性验证结果
        /// </summary>
        public class ZipInfo
        {
            /// <summary>
            /// 操作是否成功完成
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// ZIP文件的完整路径
            /// </summary>
            public string ZipFilePath { get; set; } = "";

            /// <summary>
            /// ZIP文件的压缩后大小（字节）
            /// </summary>
            public long CompressedSize { get; set; }

            /// <summary>
            /// ZIP文件内容的原始大小（字节）
            /// </summary>
            public long UncompressedSize { get; set; }

            /// <summary>
            /// ZIP文件的创建时间
            /// </summary>
            public DateTime CreationTime { get; set; }

            /// <summary>
            /// ZIP文件的最后修改时间
            /// </summary>
            public DateTime LastWriteTime { get; set; }

            /// <summary>
            /// ZIP内条目总数（包括文件和目录）
            /// </summary>
            public int EntryCount { get; set; }

            /// <summary>
            /// 文件数量（不包括目录）
            /// </summary>
            public int FileCount { get; set; }

            /// <summary>
            /// 目录数量
            /// </summary>
            public int DirectoryCount { get; set; }

            /// <summary>
            /// 整体压缩率（压缩后大小/原始大小）
            /// 值越小表示压缩效果越好
            /// </summary>
            public double CompressionRatio => UncompressedSize > 0 ? (double)CompressedSize / UncompressedSize : 0;

            /// <summary>
            /// 空间节省率（1 - 压缩率）
            /// 值越大表示节省的空间越多
            /// </summary>
            public double SpaceSaved => 1 - CompressionRatio;

            /// <summary>
            /// 平均压缩率（所有文件的平均值）
            /// </summary>
            public double AverageCompressionRatio { get; set; }

            /// <summary>
            /// 文件类型统计（扩展名 -> 文件数量）
            /// </summary>
            public Dictionary<string, int> FileTypeStatistics { get; set; } = new();

            /// <summary>
            /// ZIP内所有条目的详细信息列表
            /// </summary>
            public List<ZipEntryInfo> Entries { get; set; } = new();

            /// <summary>
            /// 错误信息（操作失败时）
            /// </summary>
            public string? ErrorMessage { get; set; }

            /// <summary>
            /// 获取格式化的大小信息字符串
            /// </summary>
            /// <returns>易读的大小信息</returns>
            public string GetFormattedSizes()
            {
                var uncompressedFormatted = FormatFileSize(UncompressedSize);
                var compressedFormatted = FormatFileSize(CompressedSize);
                return $"{uncompressedFormatted} -> {compressedFormatted} ({CompressionRatio:P1} 压缩率)";
            }

            /// <summary>
            /// 获取文件类型分布的摘要信息
            /// </summary>
            /// <returns>文件类型分布字符串</returns>
            public string GetFileTypesSummary()
            {
                if (!FileTypeStatistics.Any()) return "无文件";

                var topTypes = FileTypeStatistics
                    .OrderByDescending(kvp => kvp.Value)
                    .Take(3)
                    .Select(kvp => $"{kvp.Key}({kvp.Value})")
                    .ToArray();

                return string.Join(", ", topTypes);
            }

            /// <summary>
            /// 格式化文件大小为易读字符串
            /// </summary>
            /// <param name="bytes">字节数</param>
            /// <returns>格式化的大小字符串</returns>
            private static string FormatFileSize(long bytes)
            {
                string[] units = { "B", "KB", "MB", "GB", "TB" };
                double size = bytes;
                int unitIndex = 0;

                while (size >= 1024 && unitIndex < units.Length - 1)
                {
                    size /= 1024;
                    unitIndex++;
                }

                return unitIndex == 0 ? $"{size:F0} {units[unitIndex]}" : $"{size:F1} {units[unitIndex]}";
            }
        }

        /// <summary>
        /// ZIP条目（文件或目录）的详细信息类
        ///
        /// 包含ZIP内单个条目的完整信息：
        /// - 基本属性（名称、路径、大小等）
        /// - 压缩统计（压缩前后大小、压缩率）
        /// - 时间信息（最后修改时间）
        /// - 类型标识（文件或目录）
        /// </summary>
        public class ZipEntryInfo
        {
            /// <summary>
            /// 条目名称（不包含路径）
            /// </summary>
            public string Name { get; set; } = "";

            /// <summary>
            /// 条目的完整路径（在ZIP内的相对路径）
            /// </summary>
            public string FullName { get; set; } = "";

            /// <summary>
            /// 压缩后的大小（字节）
            /// </summary>
            public long CompressedLength { get; set; }

            /// <summary>
            /// 原始大小（字节）
            /// </summary>
            public long Length { get; set; }

            /// <summary>
            /// 最后修改时间
            /// </summary>
            public DateTimeOffset LastWriteTime { get; set; }

            /// <summary>
            /// 是否为目录条目
            /// </summary>
            public bool IsDirectory { get; set; }

            /// <summary>
            /// 条目的压缩率（压缩后大小/原始大小）
            /// </summary>
            public double CompressionRatio => Length > 0 ? (double)CompressedLength / Length : 0;

            /// <summary>
            /// 条目的空间节省率
            /// </summary>
            public double SpaceSaved => 1 - CompressionRatio;

            /// <summary>
            /// 文件扩展名（小写）
            /// </summary>
            public string Extension => IsDirectory ? "" : Path.GetExtension(Name).ToLowerInvariant();

            /// <summary>
            /// 获取格式化的大小信息
            /// </summary>
            /// <returns>格式化的大小字符串</returns>
            public string GetFormattedSize()
            {
                if (IsDirectory) return "[目录]";

                if (Length == 0) return "0 B";

                return $"{FormatBytes(Length)} -> {FormatBytes(CompressedLength)} ({CompressionRatio:P1})";
            }

            /// <summary>
            /// 格式化字节数为易读字符串
            /// </summary>
            /// <param name="bytes">字节数</param>
            /// <returns>格式化字符串</returns>
            private static string FormatBytes(long bytes)
            {
                if (bytes < 1024) return $"{bytes} B";
                if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
                if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024):F1} MB";
                return $"{bytes / (1024.0 * 1024 * 1024):F1} GB";
            }
        }

        #endregion

        #region 智能压缩优化工具

        /// <summary>
        /// 智能推荐压缩级别
        ///
        /// 算法逻辑：
        /// 1. 已压缩格式 -> 最快压缩（避免浪费CPU）
        /// 2. 大文件 -> 最快压缩（优先速度）
        /// 3. 文本文件 -> 最优压缩（效果显著）
        /// 4. 小文件 -> 最优压缩（时间成本低）
        /// 5. 其他文件 -> 平衡压缩（速度与效果兼顾）
        ///
        /// 性能考虑：
        /// - 避免对已压缩文件进行高级压缩
        /// - 大文件优先考虑处理速度
        /// - 文本文件可获得显著压缩效果
        /// - 根据文件大小动态调整策略
        /// </summary>
        /// <param name="filePath">文件的完整路径</param>
        /// <returns>推荐的压缩级别</returns>
        /// <exception cref="ArgumentException">当文件路径无效时抛出</exception>
        public CompressionLevel GetRecommendedCompressionLevel(string filePath)
        {
            try
            {
                // 输入验证
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    return CompressionLevel.Optimal; // 默认值
                }

                if (!File.Exists(filePath))
                {
                    return CompressionLevel.Optimal; // 默认值
                }

                // 获取文件信息
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // 策略1：已压缩格式使用最快压缩
                // 这些格式再次压缩效果很差，优先考虑速度
                if (CompressedFormats.Contains(extension))
                {
                    return CompressionLevel.Fastest;
                }

                // 策略2：大文件使用最快压缩
                // 超过阈值的文件优先考虑处理速度
                if (fileSize > LargeFileThreshold)
                {
                    return CompressionLevel.Fastest;
                }

                // 策略3：文本文件使用最优压缩
                // 文本文件通常有很好的压缩效果
                if (TextFormats.Contains(extension))
                {
                    return CompressionLevel.Optimal;
                }

                // 策略4：小文件使用最优压缩
                // 小文件压缩时间短，可以使用最优压缩
                if (fileSize < 1024 * 1024) // 1MB
                {
                    return CompressionLevel.Optimal;
                }

                // 策略5：中等大小文件使用平衡压缩
                // 在速度和效果之间取平衡
                return CompressionLevel.SmallestSize; // 平衡选择
            }
            catch (UnauthorizedAccessException)
            {
                // 无法访问文件，使用默认压缩级别
                return CompressionLevel.Optimal;
            }
            catch (IOException)
            {
                // IO错误，使用默认压缩级别
                return CompressionLevel.Optimal;
            }
            catch (Exception ex)
            {
                // 其他错误，记录日志并使用默认值
                Console.WriteLine($"⚠️ 获取推荐压缩级别时出错: {ex.Message}");
                return CompressionLevel.Optimal;
            }
        }

        /// <summary>
        /// 批量获取文件的推荐压缩级别
        /// 分析多个文件并返回最适合的整体压缩策略
        /// </summary>
        /// <param name="filePaths">文件路径集合</param>
        /// <returns>推荐的压缩级别</returns>
        public CompressionLevel GetRecommendedCompressionLevelForBatch(IEnumerable<string> filePaths)
        {
            try
            {
                var validFiles = filePaths?.Where(File.Exists).ToList();
                if (validFiles == null || !validFiles.Any())
                {
                    return CompressionLevel.Optimal;
                }

                // 统计各种文件类型的数量和大小
                long totalSize = 0;
                int compressedFormatCount = 0;
                int textFormatCount = 0;
                int largeFileCount = 0;

                foreach (var filePath in validFiles)
                {
                    try
                    {
                        var extension = Path.GetExtension(filePath).ToLowerInvariant();
                        var fileSize = new FileInfo(filePath).Length;

                        totalSize += fileSize;

                        if (CompressedFormats.Contains(extension))
                            compressedFormatCount++;

                        if (TextFormats.Contains(extension))
                            textFormatCount++;

                        if (fileSize > LargeFileThreshold)
                            largeFileCount++;
                    }
                    catch
                    {
                        // 忽略无法访问的文件
                    }
                }

                // 决策逻辑：根据文件组成特点选择策略
                var totalFiles = validFiles.Count;

                // 如果大部分是已压缩文件，使用最快压缩
                if (compressedFormatCount > totalFiles * 0.6)
                {
                    return CompressionLevel.Fastest;
                }

                // 如果有很多大文件，使用最快压缩
                if (largeFileCount > totalFiles * 0.3 || totalSize > LargeFileThreshold * 5)
                {
                    return CompressionLevel.Fastest;
                }

                // 如果大部分是文本文件，使用最优压缩
                if (textFormatCount > totalFiles * 0.7)
                {
                    return CompressionLevel.Optimal;
                }

                // 默认使用平衡压缩
                return CompressionLevel.SmallestSize;
            }
            catch
            {
                return CompressionLevel.Optimal;
            }
        }

        /// <summary>
        /// 格式化文件大小为易读字符串
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的大小字符串</returns>
        private static string FormatFileSize(long bytes)
        {
            string[] units = { "B", "KB", "MB", "GB", "TB" };
            double size = bytes;
            int unitIndex = 0;

            while (size >= 1024 && unitIndex < units.Length - 1)
            {
                size /= 1024;
                unitIndex++;
            }

            return unitIndex == 0 ? $"{size:F0} {units[unitIndex]}" : $"{size:F1} {units[unitIndex]}";
        }

        #endregion
    }
}
