# YFileCompressor - 企业级文件压缩管理器

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🗜️ **全功能文件压缩解决方案** - 支持ZIP压缩、解压、批量处理的智能压缩管理

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [超简化API](#-超简化api)
- [高级功能](#-高级功能)
- [压缩策略](#-压缩策略)
- [函数汇总](#-函数汇总)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **📦 多种压缩模式**

- ✅ **单文件压缩**: 将单个文件压缩为ZIP格式，保持文件结构
- ✅ **目录压缩**: 递归压缩整个目录，支持子目录和文件层次
- ✅ **批量压缩**: 同时处理多个文件，提升批处理效率
- ✅ **智能压缩**: 基于文件类型自动选择最优压缩级别

### **🔧 压缩控制**

- ✅ **压缩级别**: 支持最快、最优、最小、无压缩四种级别
- ✅ **智能推荐**: 根据文件大小和类型自动推荐压缩级别
- ✅ **性能优化**: 大文件使用流式处理，避免内存溢出
- ✅ **进度监控**: 实时报告压缩进度和统计信息

### **📊 解压功能**

- ✅ **ZIP解压**: 完整解压ZIP文件到指定目录
- ✅ **选择性解压**: 支持解压特定文件或目录
- ✅ **覆盖控制**: 灵活的文件覆盖策略配置
- ✅ **完整性验证**: 解压后自动验证文件完整性

### **🔍 信息查询**

- ✅ **ZIP信息**: 查询ZIP文件的详细信息和统计数据
- ✅ **文件列表**: 获取ZIP内部文件和目录结构
- ✅ **压缩统计**: 计算压缩率、节省空间等指标
- ✅ **类型分析**: 分析ZIP中文件类型分布

### **⚡ 异步支持**

- ✅ **异步操作**: 所有主要操作支持异步模式
- ✅ **取消支持**: 支持CancellationToken取消长时间操作
- ✅ **进度回调**: 提供IProgress接口用于UI更新
- ✅ **并发安全**: 支持多线程并发操作

### **🛡️ 安全特性**

- ✅ **错误处理**: 完善的异常处理和错误恢复机制
- ✅ **资源管理**: 自动释放文件句柄和内存资源
- ✅ **路径验证**: 防止路径遍历和安全漏洞
- ✅ **权限检查**: 智能检测文件访问权限

## 🚀 **快速开始**

### **安装**

```bash
# 通过 NuGet 包管理器
Install-Package Zylo.YIO

# 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Management;

var compressor = new YFileCompressor();

// 压缩单个文件
var result = compressor.CompressFile(@"C:\MyFile.txt", @"D:\Compressed\MyFile.zip");

// 压缩整个目录
var dirResult = compressor.CompressDirectory(@"C:\MyProject", @"D:\Compressed\Project.zip");

// 解压ZIP文件
var extractResult = compressor.ExtractZip(@"D:\Compressed\Project.zip", @"C:\Extracted");
```

### **超简化 API**

```csharp
// 最简单的使用方式 - 静态方法
var result = YFileCompressor.CompressFile(@"C:\MyFile.txt", @"D:\MyFile.zip");
bool success = YFileCompressor.ExtractZip(@"D:\MyFile.zip", @"C:\Extracted");

// 快速批量压缩
var batchResult = YFileCompressor.CompressMultipleFiles(
    new[] { @"C:\File1.txt", @"C:\File2.txt" }, 
    @"D:\Batch.zip"
);
```

## 🔧 **核心功能**

### **单文件压缩**

```csharp
var compressor = new YFileCompressor();

// 基础文件压缩
var result = compressor.CompressFile(@"C:\Document.pdf", @"D:\Archive\Document.zip");

// 指定压缩级别
var optimizedResult = compressor.CompressFile(
    @"C:\LargeFile.dat", 
    @"D:\Archive\LargeFile.zip",
    CompressionLevel.Optimal
);

// 异步文件压缩
var asyncResult = await compressor.CompressFileAsync(
    @"C:\BigFile.iso", 
    @"D:\Archive\BigFile.zip",
    CompressionLevel.Fastest,
    cancellationToken: cts.Token,
    progress: new Progress<string>(Console.WriteLine)
);

Console.WriteLine($"压缩完成: {result.GetFormattedSizes()}");
Console.WriteLine($"压缩率: {result.CompressionRatio:P1}");
Console.WriteLine($"节省空间: {result.SpaceSaved:P1}");
```

### **目录压缩**

```csharp
// 压缩整个项目目录
var projectResult = compressor.CompressDirectory(
    @"C:\MyProject", 
    @"D:\Backups\Project_v1.0.zip"
);

// 包含根目录名称
var withRootResult = compressor.CompressDirectory(
    @"C:\WebSite", 
    @"D:\Deploy\WebSite.zip",
    CompressionLevel.SmallestSize,
    includeBaseDirectory: true
);

// 异步目录压缩（适合大目录）
var largeProjectResult = await compressor.CompressDirectoryAsync(
    @"C:\LargeProject", 
    @"D:\Archive\LargeProject.zip",
    CompressionLevel.Optimal,
    includeBaseDirectory: false,
    cancellationToken: cts.Token,
    progress: new Progress<string>(msg => Console.WriteLine($"进度: {msg}"))
);

Console.WriteLine($"压缩了 {projectResult.FileCount} 个文件");
Console.WriteLine($"原始大小: {projectResult.GetFormattedSizes()}");
Console.WriteLine($"处理时间: {projectResult.ProcessingTime.TotalSeconds:F1} 秒");
```

### **ZIP解压**

```csharp
// 解压到指定目录
var extractResult = compressor.ExtractZip(@"D:\Archive\Project.zip", @"C:\Restored");

// 覆盖已存在文件
var overwriteResult = compressor.ExtractZip(
    @"D:\Archive\Update.zip", 
    @"C:\Application",
    overwrite: true
);

// 异步解压（适合大文件）
var asyncExtractResult = await compressor.ExtractZipAsync(
    @"D:\Archive\LargeArchive.zip", 
    @"C:\Extracted",
    overwrite: false,
    cancellationToken: cts.Token,
    progress: new Progress<string>(Console.WriteLine)
);

if (extractResult.Success)
{
    Console.WriteLine($"解压成功: {extractResult.Files.Count} 个文件");
    Console.WriteLine($"解压大小: {extractResult.GetFormattedSizes()}");
}
else
{
    Console.WriteLine($"解压失败: {extractResult.ErrorMessage}");
}
```

## 🎯 **超简化API**

### **静态方法快速操作**

```csharp
// 由 [YStatic] 属性自动生成的静态扩展方法

// 快速文件压缩
var quickCompress = YFileCompressor.CompressFile(@"C:\Data.txt", @"D:\Data.zip");

// 快速目录压缩
var quickDirCompress = YFileCompressor.CompressDirectory(@"C:\Folder", @"D:\Folder.zip");

// 快速解压
bool quickExtract = YFileCompressor.ExtractZip(@"D:\Archive.zip", @"C:\Extracted");

// 快速信息查询
var quickInfo = YFileCompressor.GetZipInfo(@"D:\Archive.zip");
```

### **一行代码操作**

```csharp
// 最简单的压缩操作
var result = YFileCompressor.CompressFile(@"C:\MyFile.txt", @"D:\MyFile.zip");

// 最简单的解压操作
YFileCompressor.ExtractZip(@"D:\MyFile.zip", @"C:\Extracted");

// 最简单的信息查询
var info = YFileCompressor.GetZipInfo(@"D:\Archive.zip");
```

## 🔧 **高级功能**

### **批量文件处理**

```csharp
var compressor = new YFileCompressor();

// 批量压缩多个文件
var files = new[]
{
    @"C:\Document1.pdf",
    @"C:\Document2.docx", 
    @"C:\Spreadsheet.xlsx"
};

var batchResult = compressor.CompressMultipleFiles(files, @"D:\Documents.zip");

// 异步批量处理
var asyncBatchResult = await compressor.CompressMultipleFilesAsync(
    files,
    @"D:\AsyncDocuments.zip",
    CompressionLevel.Optimal,
    cancellationToken: cts.Token,
    progress: new Progress<string>(Console.WriteLine)
);

Console.WriteLine($"批量压缩完成: {batchResult.FileCount} 个文件");
Console.WriteLine($"总大小: {batchResult.GetFormattedSizes()}");
Console.WriteLine($"压缩率: {batchResult.CompressionRatio:P1}");
```

### **ZIP信息查询**

```csharp
// 获取ZIP文件详细信息
var zipInfo = compressor.GetZipInfo(@"D:\Archive.zip");

Console.WriteLine($"ZIP文件信息:");
Console.WriteLine($"文件数量: {zipInfo.FileCount:N0} 个");
Console.WriteLine($"目录数量: {zipInfo.DirectoryCount:N0} 个");
Console.WriteLine($"未压缩大小: {FormatFileSize(zipInfo.UncompressedSize)}");
Console.WriteLine($"压缩后大小: {FormatFileSize(zipInfo.CompressedSize)}");
Console.WriteLine($"压缩率: {zipInfo.CompressionRatio:P1}");
Console.WriteLine($"节省空间: {zipInfo.SpaceSaved:P1}");

// 获取文件列表
Console.WriteLine("\n文件列表:");
foreach (var file in zipInfo.Files.Take(10))
{
    Console.WriteLine($"  📄 {file}");
}

// 获取文件类型分布
var typeStats = zipInfo.GetFileTypeStatistics();
Console.WriteLine("\n文件类型分布:");
foreach (var kvp in typeStats.OrderByDescending(x => x.Value).Take(5))
{
    Console.WriteLine($"  {kvp.Key}: {kvp.Value} 个");
}
```

### **智能压缩级别推荐**

```csharp
// 获取推荐的压缩级别
var recommendedLevel = compressor.GetRecommendedCompressionLevel(@"C:\MyFile.txt");

// 使用推荐级别压缩
var smartResult = compressor.CompressFile(
    @"C:\MyFile.txt", 
    @"D:\MyFile.zip", 
    recommendedLevel
);

Console.WriteLine($"推荐压缩级别: {recommendedLevel}");
Console.WriteLine($"压缩效果: {smartResult.GetFormattedSizes()}");
```

### **进度监控和取消**

```csharp
// 创建取消令牌
var cts = new CancellationTokenSource();

// 创建进度报告器
var progress = new Progress<string>(message =>
{
    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
});

// 异步压缩大目录
var task = compressor.CompressDirectoryAsync(
    @"C:\LargeProject",
    @"D:\LargeProject.zip",
    CompressionLevel.Optimal,
    includeBaseDirectory: true,
    cancellationToken: cts.Token,
    progress: progress
);

// 在另一个线程中可以取消操作
// cts.Cancel();

try
{
    var result = await task;
    Console.WriteLine($"压缩完成: {result.GetFormattedSizes()}");
}
catch (OperationCanceledException)
{
    Console.WriteLine("压缩操作已取消");
}
```

## 📊 **压缩策略**

### **智能压缩级别选择**

```csharp
// 系统会根据文件特征自动选择最优压缩级别

var compressor = new YFileCompressor();

// 文本文件 - 使用最优压缩（压缩效果好）
var textResult = compressor.CompressFile(@"C:\Document.txt", @"D:\Document.zip");

// 图片文件 - 使用最快压缩（已经压缩过）
var imageResult = compressor.CompressFile(@"C:\Photo.jpg", @"D:\Photo.zip");

// 大文件 - 使用最快压缩（优先速度）
var largeResult = compressor.CompressFile(@"C:\BigFile.iso", @"D:\BigFile.zip");

// 小文件 - 使用最优压缩（时间短，可以追求最佳效果）
var smallResult = compressor.CompressFile(@"C:\Config.xml", @"D:\Config.zip");
```

### **批量处理策略**

```csharp
// 实现高效的批量文件处理策略

var compressor = new YFileCompressor();

// 策略1：按文件类型分组压缩
var documentFiles = Directory.GetFiles(@"C:\Documents", "*.pdf");
var imageFiles = Directory.GetFiles(@"C:\Images", "*.jpg");

var docResult = compressor.CompressMultipleFiles(documentFiles, @"D:\Documents.zip");
var imgResult = compressor.CompressMultipleFiles(imageFiles, @"D:\Images.zip");

// 策略2：按大小分组处理
var allFiles = Directory.GetFiles(@"C:\Data", "*", SearchOption.AllDirectories);
var smallFiles = allFiles.Where(f => new FileInfo(f).Length < 10 * 1024 * 1024); // < 10MB
var largeFiles = allFiles.Where(f => new FileInfo(f).Length >= 10 * 1024 * 1024); // >= 10MB

var smallResult = compressor.CompressMultipleFiles(smallFiles.ToArray(), @"D:\SmallFiles.zip");
var largeResult = compressor.CompressMultipleFiles(largeFiles.ToArray(), @"D:\LargeFiles.zip");

Console.WriteLine("批量处理完成");
```

### **存储优化策略**

```csharp
// 实现存储空间优化的压缩策略

// 策略1：定期压缩日志文件
var logFiles = Directory.GetFiles(@"C:\Logs", "*.log")
    .Where(f => File.GetLastWriteTime(f) < DateTime.Now.AddDays(-7)) // 7天前的日志
    .ToArray();

if (logFiles.Any())
{
    var logResult = compressor.CompressMultipleFiles(
        logFiles, 
        $@"D:\Archive\Logs_{DateTime.Now:yyyyMMdd}.zip"
    );
    
    // 压缩成功后删除原文件
    if (logResult.Success)
    {
        foreach (var logFile in logFiles)
        {
            File.Delete(logFile);
        }
        Console.WriteLine($"日志归档完成，节省空间: {logResult.SpaceSaved:P1}");
    }
}

// 策略2：临时文件清理压缩
var tempFiles = Directory.GetFiles(Path.GetTempPath(), "*", SearchOption.TopDirectoryOnly)
    .Where(f => File.GetLastAccessTime(f) < DateTime.Now.AddHours(-24)) // 24小时未访问
    .ToArray();

if (tempFiles.Any())
{
    var tempResult = compressor.CompressMultipleFiles(
        tempFiles, 
        $@"D:\Cleanup\Temp_{DateTime.Now:yyyyMMdd_HHmmss}.zip"
    );
    Console.WriteLine($"临时文件清理完成: {tempResult.FileCount} 个文件");
}
```

## 📋 **函数汇总**

### **🏗️ 构造函数**

| 构造函数 | 描述 | 参数 |
|----------|------|------|
| `YFileCompressor()` | 默认构造函数，使用默认YIO配置 | 无 |
| `YFileCompressor(YIOConfig config)` | 使用自定义配置初始化 | `config`: YIO配置对象 |

### **🗜️ 压缩方法**

#### **同步压缩方法**

| 方法名 | 返回类型 | 描述 | 主要参数 |
|--------|----------|------|----------|
| `CompressFile` | `CompressionResult` | 压缩单个文件到ZIP格式 | `sourceFilePath`, `zipFilePath`, `compressionLevel` |
| `CompressDirectory` | `CompressionResult` | 压缩整个目录到ZIP格式 | `sourceDirectoryPath`, `zipFilePath`, `compressionLevel`, `includeBaseDirectory` |
| `CompressMultipleFiles` | `CompressionResult` | 批量压缩多个文件到单个ZIP包 | `filePaths[]`, `zipFilePath`, `compressionLevel`, `preserveDirectoryStructure` |

#### **异步压缩方法**

| 方法名 | 返回类型 | 描述 | 主要参数 |
|--------|----------|------|----------|
| `CompressFileAsync` | `Task<CompressionResult>` | 异步压缩单个文件 | `sourceFilePath`, `zipFilePath`, `compressionLevel`, `cancellationToken`, `progress` |
| `CompressDirectoryAsync` | `Task<CompressionResult>` | 异步压缩目录 | `sourceDirectoryPath`, `zipFilePath`, `compressionLevel`, `includeBaseDirectory`, `cancellationToken`, `progress` |
| `CompressMultipleFilesAsync` | `Task<CompressionResult>` | 异步批量压缩文件 | `filePaths[]`, `zipFilePath`, `compressionLevel`, `cancellationToken`, `progress` |

### **📦 解压方法**

| 方法名 | 返回类型 | 描述 | 主要参数 |
|--------|----------|------|----------|
| `ExtractZip` | `CompressionResult` | 解压ZIP文件到指定目录 | `zipFilePath`, `extractPath`, `overwrite` |
| `ExtractZipAsync` | `Task<CompressionResult>` | 异步解压ZIP文件 | `zipFilePath`, `extractPath`, `overwrite`, `cancellationToken`, `progress` |

### **🔍 信息查询方法**

| 方法名 | 返回类型 | 描述 | 主要参数 |
|--------|----------|------|----------|
| `GetZipInfo` | `ZipInfo` | 获取ZIP文件的详细信息和统计数据 | `zipFilePath` |
| `GetRecommendedCompressionLevel` | `CompressionLevel` | 智能推荐单个文件的压缩级别 | `filePath` |
| `GetRecommendedCompressionLevelForBatch` | `CompressionLevel` | 批量文件的推荐压缩级别 | `filePaths[]` |

### **🛠️ 辅助方法**

#### **私有辅助方法**

| 方法名 | 返回类型 | 描述 | 主要参数 |
|--------|----------|------|----------|
| `IsValidFileForCompression` | `bool` | 检查文件是否适合压缩 | `filePath` |
| `FormatFileSize` | `string` | 格式化文件大小为易读字符串 | `bytes` |
| `LogError` | `void` | 记录错误日志 | `message`, `exception`, `context` |

### **📊 常量和静态字段**

#### **文件格式常量**

| 常量名 | 类型 | 描述 | 值 |
|--------|------|------|-----|
| `CompressedFormats` | `string[]` | 已压缩文件格式列表 | `.zip`, `.rar`, `.7z`, `.jpg`, `.mp4`, `.pdf` 等 |
| `TextFormats` | `string[]` | 文本文件格式列表 | `.txt`, `.csv`, `.json`, `.xml`, `.cs`, `.log` 等 |
| `LargeFileThreshold` | `long` | 大文件阈值（字节） | `100 * 1024 * 1024` (100MB) |

#### **配置字段**

| 字段名 | 类型 | 描述 | 用途 |
|--------|------|------|------|
| `_config` | `YIOConfig` | YIO配置实例 | 控制压缩行为和性能参数 |

### **📝 详细方法签名**

#### **压缩方法完整签名**

```csharp
// 同步压缩方法
public CompressionResult CompressFile(
    string sourceFilePath,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal)

public CompressionResult CompressDirectory(
    string sourceDirectoryPath,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal,
    bool includeBaseDirectory = false)

public CompressionResult CompressMultipleFiles(
    string[] sourceFilePaths,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal,
    bool preserveDirectoryStructure = false)

// 异步压缩方法
public async Task<CompressionResult> CompressFileAsync(
    string sourceFilePath,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal,
    CancellationToken cancellationToken = default,
    IProgress<string>? progress = null)

public async Task<CompressionResult> CompressDirectoryAsync(
    string sourceDirectoryPath,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal,
    bool includeBaseDirectory = false,
    CancellationToken cancellationToken = default,
    IProgress<string>? progress = null)

public async Task<CompressionResult> CompressMultipleFilesAsync(
    string[] sourceFilePaths,
    string zipFilePath,
    CompressionLevel compressionLevel = CompressionLevel.Optimal,
    CancellationToken cancellationToken = default,
    IProgress<string>? progress = null)
```

#### **解压方法完整签名**

```csharp
// 同步解压方法
public CompressionResult ExtractZip(
    string zipFilePath,
    string extractPath,
    bool overwrite = false)

// 异步解压方法
public async Task<CompressionResult> ExtractZipAsync(
    string zipFilePath,
    string extractPath,
    bool overwrite = false,
    CancellationToken cancellationToken = default,
    IProgress<string>? progress = null)
```

#### **信息查询方法完整签名**

```csharp
// ZIP信息查询
public ZipInfo GetZipInfo(string zipFilePath)

// 智能压缩级别推荐
public CompressionLevel GetRecommendedCompressionLevel(string filePath)

public CompressionLevel GetRecommendedCompressionLevelForBatch(
    IEnumerable<string> filePaths)
```

#### **私有辅助方法签名**

```csharp
// 文件验证
private static bool IsValidFileForCompression(string filePath)

// 格式化工具
private static string FormatFileSize(long bytes)

// 错误日志
private void LogError(string message, Exception exception, string context)
```

#### **解压方法完整签名**

```csharp
// 同步解压方法
public CompressionResult ExtractZip(
    string zipFilePath,
    string extractPath,
    bool overwrite = false)

// 异步解压方法
public async Task<CompressionResult> ExtractZipAsync(
    string zipFilePath,
    string extractPath,
    bool overwrite = false,
    CancellationToken cancellationToken = default,
    IProgress<string>? progress = null)
```

#### **信息查询方法完整签名**

```csharp
// ZIP信息查询
public ZipInfo GetZipInfo(string zipFilePath)

// 智能压缩级别推荐
public CompressionLevel GetRecommendedCompressionLevel(string filePath)

public CompressionLevel GetRecommendedCompressionLevelForBatch(
    IEnumerable<string> filePaths)
```

#### **私有辅助方法签名**

```csharp
// 文件验证
private static bool IsValidFileForCompression(string filePath)

// 格式化工具
private static string FormatFileSize(long bytes)

// 错误日志
private void LogError(string message, Exception exception, string context)
```

### **�📊 结果类型详解**

#### **CompressionResult 类**

```csharp
public class CompressionResult
{
    // 基本状态
    public bool Success { get; set; }                    // 操作是否成功
    public string? ErrorMessage { get; set; }           // 错误信息
    public TimeSpan ProcessingTime { get; set; }        // 处理耗时

    // 路径信息
    public string SourcePath { get; set; }              // 源路径
    public string CompressedPath { get; set; }          // 压缩文件路径

    // 大小统计
    public long OriginalSize { get; set; }              // 原始大小（字节）
    public long CompressedSize { get; set; }            // 压缩后大小（字节）
    public int FileCount { get; set; }                  // 文件数量

    // 计算属性
    public double CompressionRatio { get; }             // 压缩率（压缩后/原始）
    public double SpaceSaved { get; }                   // 空间节省率（1-压缩率）

    // 格式化方法
    public string GetFormattedSizes()                   // 获取格式化的大小信息
    public string GetCompressionSummary()               // 获取压缩摘要信息
}
```

#### **ZipInfo 类**

```csharp
public class ZipInfo
{
    // 基本状态
    public bool Success { get; set; }                   // 操作是否成功
    public string? ErrorMessage { get; set; }          // 错误信息
    public string ZipFilePath { get; set; }            // ZIP文件路径

    // 时间信息
    public DateTime CreationTime { get; set; }         // 创建时间
    public DateTime LastWriteTime { get; set; }        // 最后修改时间

    // 大小统计
    public long CompressedSize { get; set; }           // 压缩后大小
    public long UncompressedSize { get; set; }         // 原始大小

    // 内容统计
    public int EntryCount { get; set; }                // 条目总数
    public int FileCount { get; set; }                 // 文件数量
    public int DirectoryCount { get; set; }            // 目录数量
    public List<string> Files { get; set; }            // 文件列表

    // 压缩统计
    public double CompressionRatio { get; }            // 整体压缩率
    public double SpaceSaved { get; }                  // 空间节省率
    public double AverageCompressionRatio { get; set; } // 平均压缩率

    // 文件类型统计
    public Dictionary<string, int> FileTypeStatistics { get; set; }  // 文件类型分布

    // 方法
    public Dictionary<string, int> GetFileTypeStatistics()           // 获取文件类型统计
    public string GetFormattedSizes()                               // 获取格式化大小信息
    public List<string> GetLargestFiles(int count = 10)            // 获取最大的文件列表
}
```

### **🎛️ 枚举类型**

#### **CompressionLevel 枚举**

```csharp
public enum CompressionLevel
{
    Optimal,        // 最优压缩 - 最佳压缩率，较慢速度，适合文本文件
    Fastest,        // 最快压缩 - 最快速度，较低压缩率，适合大文件和已压缩文件
    NoCompression,  // 无压缩 - 仅打包，不压缩，适合已压缩格式
    SmallestSize    // 最小体积 - 最高压缩率，最慢速度，适合存储优化
}
```

### **📈 性能特征**

| 操作类型 | 时间复杂度 | 空间复杂度 | 适用场景 |
|----------|------------|------------|----------|
| 单文件压缩 | O(n) | O(1) | 小到中等文件 |
| 目录压缩 | O(n×m) | O(m) | 项目备份、批量归档 |
| 批量压缩 | O(n×k) | O(k) | 多文件打包 |
| ZIP解压 | O(n) | O(m) | 文件恢复、安装包 |
| 信息查询 | O(m) | O(m) | 预览、分析 |

*注：n=文件大小，m=文件数量，k=批量文件数*

### **🔧 配置选项**

#### **YIOConfig 相关配置**

```csharp
// 缓冲区配置
_config.BufferSize                    // 读写缓冲区大小（默认64KB）
_config.MaxConcurrentOperations       // 最大并发操作数
_config.OperationTimeoutMs           // 操作超时时间（毫秒）

// 文件处理配置
_config.MaxSingleFileSize            // 单文件最大大小限制
_config.MaxTotalFileSize             // 总文件大小限制
_config.EnableProgressReporting      // 是否启用进度报告

// 错误处理配置
_config.RetryCount                   // 重试次数
_config.RetryDelayMs                 // 重试延迟
_config.EnableDetailedErrorLogging  // 详细错误日志
```

### **🎯 使用建议**

#### **方法选择指南**

| 场景 | 推荐方法 | 理由 |
|------|----------|------|
| 单个小文件 | `CompressFile` | 简单快速，同步操作 |
| 单个大文件 | `CompressFileAsync` | 异步处理，不阻塞UI |
| 整个项目目录 | `CompressDirectoryAsync` | 异步处理，支持进度监控 |
| 多个独立文件 | `CompressMultipleFiles` | 批量处理，统一管理 |
| 需要取消的长操作 | 异步方法 + `CancellationToken` | 支持用户取消 |
| 需要进度显示 | 异步方法 + `IProgress<string>` | 实时进度反馈 |

#### **压缩级别选择**

| 文件类型 | 推荐级别 | 原因 |
|----------|----------|------|
| 文本文件(.txt, .log, .xml) | `Optimal` | 压缩效果显著 |
| 图片文件(.jpg, .png, .gif) | `Fastest` | 已压缩格式 |
| 视频文件(.mp4, .avi, .mkv) | `Fastest` | 已压缩格式 |
| 大文件(>100MB) | `Fastest` | 优先处理速度 |
| 小文件(<1MB) | `Optimal` | 时间成本低 |
| 混合文件 | `SmallestSize` | 平衡选择 |

## 📚 **API 参考**

### **核心类和方法**

#### **YFileCompressor 类**

```csharp
public partial class YFileCompressor
{
    // 构造函数
    public YFileCompressor()
    public YFileCompressor(YIOConfig config)

    // 同步压缩方法
    public CompressionResult CompressFile(string sourceFilePath, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal)
    public CompressionResult CompressDirectory(string sourceDirectoryPath, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal, bool includeBaseDirectory = false)
    public CompressionResult CompressMultipleFiles(string[] sourceFilePaths, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal)

    // 异步压缩方法
    public Task<CompressionResult> CompressFileAsync(string sourceFilePath, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal, CancellationToken cancellationToken = default, IProgress<string>? progress = null)
    public Task<CompressionResult> CompressDirectoryAsync(string sourceDirectoryPath, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal, bool includeBaseDirectory = false, CancellationToken cancellationToken = default, IProgress<string>? progress = null)
    public Task<CompressionResult> CompressMultipleFilesAsync(string[] sourceFilePaths, string zipFilePath, CompressionLevel compressionLevel = CompressionLevel.Optimal, CancellationToken cancellationToken = default, IProgress<string>? progress = null)

    // 解压方法
    public ExtractionResult ExtractZip(string zipFilePath, string extractPath, bool overwrite = false)
    public Task<ExtractionResult> ExtractZipAsync(string zipFilePath, string extractPath, bool overwrite = false, CancellationToken cancellationToken = default, IProgress<string>? progress = null)

    // 信息查询方法
    public ZipInfo GetZipInfo(string zipFilePath)
    public CompressionLevel GetRecommendedCompressionLevel(string filePath)
}
```

#### **结果类型**

```csharp
// 压缩结果
public class CompressionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public long OriginalSize { get; set; }
    public long CompressedSize { get; set; }
    public int FileCount { get; set; }
    public TimeSpan ProcessingTime { get; set; }

    // 计算属性
    public double CompressionRatio => OriginalSize > 0 ? (double)CompressedSize / OriginalSize : 0;
    public double SpaceSaved => 1.0 - CompressionRatio;

    // 格式化方法
    public string GetFormattedSizes() => $"{FormatFileSize(OriginalSize)} -> {FormatFileSize(CompressedSize)} ({CompressionRatio:P1} 压缩率)";
}

// 解压结果
public class ExtractionResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<string> Files { get; set; } = new();
    public long TotalSize { get; set; }
    public TimeSpan ProcessingTime { get; set; }

    public string GetFormattedSizes() => $"{FormatFileSize(TotalSize)} ({Files.Count:N0} 个文件)";
}

// ZIP信息
public class ZipInfo
{
    public int FileCount { get; set; }
    public int DirectoryCount { get; set; }
    public long UncompressedSize { get; set; }
    public long CompressedSize { get; set; }
    public List<string> Files { get; set; } = new();

    public double CompressionRatio => UncompressedSize > 0 ? (double)CompressedSize / UncompressedSize : 0;
    public double SpaceSaved => 1.0 - CompressionRatio;

    public Dictionary<string, int> GetFileTypeStatistics();
}
```

### **压缩级别枚举**

```csharp
public enum CompressionLevel
{
    Optimal,        // 最优压缩 - 最佳压缩率，较慢速度
    Fastest,        // 最快压缩 - 最快速度，较低压缩率
    NoCompression,  // 无压缩 - 仅打包，不压缩
    SmallestSize    // 最小体积 - 最高压缩率，最慢速度
}
```

## 🧪 **测试覆盖**

### **单元测试示例**

```csharp
[Test]
public void CompressFile_ValidInput_ShouldSucceed()
{
    // Arrange
    var compressor = new YFileCompressor();
    var sourceFile = CreateTestFile("test.txt", "Hello World");
    var zipFile = Path.GetTempFileName() + ".zip";

    // Act
    var result = compressor.CompressFile(sourceFile, zipFile);

    // Assert
    Assert.IsTrue(result.Success);
    Assert.IsTrue(File.Exists(zipFile));
    Assert.Greater(result.OriginalSize, 0);
    Assert.Greater(result.CompressedSize, 0);

    // Cleanup
    File.Delete(sourceFile);
    File.Delete(zipFile);
}

[Test]
public async Task CompressDirectoryAsync_LargeDirectory_ShouldHandleProgress()
{
    // Arrange
    var compressor = new YFileCompressor();
    var sourceDir = CreateTestDirectory(1000); // 1000个文件
    var zipFile = Path.GetTempFileName() + ".zip";
    var progressReports = new List<string>();
    var progress = new Progress<string>(msg => progressReports.Add(msg));

    // Act
    var result = await compressor.CompressDirectoryAsync(
        sourceDir, zipFile,
        progress: progress
    );

    // Assert
    Assert.IsTrue(result.Success);
    Assert.Greater(progressReports.Count, 0);
    Assert.AreEqual(1000, result.FileCount);

    // Cleanup
    Directory.Delete(sourceDir, true);
    File.Delete(zipFile);
}

[Test]
public void GetZipInfo_ValidZipFile_ShouldReturnCorrectInfo()
{
    // Arrange
    var compressor = new YFileCompressor();
    var testFiles = CreateTestFiles(5);
    var zipFile = Path.GetTempFileName() + ".zip";

    compressor.CompressMultipleFiles(testFiles, zipFile);

    // Act
    var zipInfo = compressor.GetZipInfo(zipFile);

    // Assert
    Assert.AreEqual(5, zipInfo.FileCount);
    Assert.Greater(zipInfo.UncompressedSize, 0);
    Assert.Greater(zipInfo.CompressedSize, 0);
    Assert.LessOrEqual(zipInfo.CompressionRatio, 1.0);

    // Cleanup
    foreach (var file in testFiles) File.Delete(file);
    File.Delete(zipFile);
}
```

### **性能测试**

```csharp
[Test]
public void CompressLargeFile_Performance_ShouldCompleteInReasonableTime()
{
    // Arrange
    var compressor = new YFileCompressor();
    var largeFile = CreateLargeTestFile(100 * 1024 * 1024); // 100MB
    var zipFile = Path.GetTempFileName() + ".zip";
    var stopwatch = Stopwatch.StartNew();

    // Act
    var result = compressor.CompressFile(largeFile, zipFile, CompressionLevel.Fastest);
    stopwatch.Stop();

    // Assert
    Assert.IsTrue(result.Success);
    Assert.Less(stopwatch.ElapsedMilliseconds, 30000); // 应在30秒内完成
    Assert.Greater(result.SpaceSaved, 0); // 应该有一定的压缩效果

    Console.WriteLine($"100MB文件压缩耗时: {stopwatch.ElapsedMilliseconds}ms");
    Console.WriteLine($"压缩率: {result.CompressionRatio:P1}");

    // Cleanup
    File.Delete(largeFile);
    File.Delete(zipFile);
}

[Test]
public async Task CompressManySmallFiles_Performance_ShouldHandleEfficiently()
{
    // Arrange
    var compressor = new YFileCompressor();
    var smallFiles = CreateManySmallFiles(10000); // 10000个小文件
    var zipFile = Path.GetTempFileName() + ".zip";
    var stopwatch = Stopwatch.StartNew();

    // Act
    var result = await compressor.CompressMultipleFilesAsync(smallFiles, zipFile);
    stopwatch.Stop();

    // Assert
    Assert.IsTrue(result.Success);
    Assert.AreEqual(10000, result.FileCount);
    Assert.Less(stopwatch.ElapsedMilliseconds, 60000); // 应在60秒内完成

    Console.WriteLine($"10000个小文件压缩耗时: {stopwatch.ElapsedMilliseconds}ms");
    Console.WriteLine($"平均每文件: {(double)stopwatch.ElapsedMilliseconds / 10000:F2}ms");

    // Cleanup
    foreach (var file in smallFiles) File.Delete(file);
    File.Delete(zipFile);
}
```

## 💡 **最佳实践**

### **1. 压缩策略选择**

```csharp
// ✅ 推荐：根据文件类型选择压缩级别
public CompressionLevel GetOptimalLevel(string filePath)
{
    var extension = Path.GetExtension(filePath).ToLower();
    var fileSize = new FileInfo(filePath).Length;

    // 已压缩格式使用最快压缩
    if (new[] { ".jpg", ".png", ".mp4", ".zip", ".rar" }.Contains(extension))
        return CompressionLevel.Fastest;

    // 大文件优先速度
    if (fileSize > 100 * 1024 * 1024) // > 100MB
        return CompressionLevel.Fastest;

    // 文本文件使用最优压缩
    if (new[] { ".txt", ".log", ".xml", ".json", ".csv" }.Contains(extension))
        return CompressionLevel.Optimal;

    // 默认使用平衡模式
    return CompressionLevel.SmallestSize;
}

// ❌ 避免：对所有文件使用相同压缩级别
// var result = compressor.CompressFile(anyFile, zipFile, CompressionLevel.Optimal); // 可能很慢
```

### **2. 内存管理**

```csharp
// ✅ 推荐：大文件使用异步操作
public async Task CompressLargeFilesAsync()
{
    var compressor = new YFileCompressor();
    var largeFiles = GetLargeFiles();

    foreach (var file in largeFiles)
    {
        var result = await compressor.CompressFileAsync(
            file,
            file + ".zip",
            CompressionLevel.Fastest, // 大文件优先速度
            cancellationToken: cts.Token
        );

        // 及时释放资源
        GC.Collect();
        GC.WaitForPendingFinalizers();
    }
}

// ✅ 推荐：批量处理时分批进行
public async Task CompressManyFilesInBatches(string[] files)
{
    var compressor = new YFileCompressor();
    const int batchSize = 100;

    for (int i = 0; i < files.Length; i += batchSize)
    {
        var batch = files.Skip(i).Take(batchSize).ToArray();
        var batchZip = $"batch_{i / batchSize + 1}.zip";

        var result = await compressor.CompressMultipleFilesAsync(batch, batchZip);
        Console.WriteLine($"批次 {i / batchSize + 1} 完成: {result.FileCount} 个文件");
    }
}
```

### **3. 错误处理**

```csharp
// ✅ 推荐：完善的错误处理
public async Task<bool> SafeCompressWithRetry(string sourceFile, string zipFile, int maxRetries = 3)
{
    var compressor = new YFileCompressor();

    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            var result = await compressor.CompressFileAsync(sourceFile, zipFile);

            if (result.Success)
            {
                // 验证压缩结果
                var zipInfo = compressor.GetZipInfo(zipFile);
                if (zipInfo.FileCount > 0)
                {
                    Console.WriteLine($"压缩成功: {result.GetFormattedSizes()}");
                    return true;
                }
            }

            Console.WriteLine($"压缩失败: {result.ErrorMessage}");
        }
        catch (UnauthorizedAccessException ex)
        {
            Console.WriteLine($"权限不足: {ex.Message}");
            return false; // 权限问题不重试
        }
        catch (DirectoryNotFoundException ex)
        {
            Console.WriteLine($"目录不存在: {ex.Message}");
            return false; // 路径问题不重试
        }
        catch (Exception ex)
        {
            Console.WriteLine($"尝试 {attempt}/{maxRetries} 失败: {ex.Message}");

            if (attempt < maxRetries)
            {
                await Task.Delay(1000 * attempt); // 递增延迟重试
            }
        }
    }

    return false;
}
```

### **4. 性能优化**

```csharp
// ✅ 推荐：并行处理独立文件
public async Task CompressFilesInParallel(string[] files)
{
    var compressor = new YFileCompressor();
    var semaphore = new SemaphoreSlim(Environment.ProcessorCount); // 限制并发数

    var tasks = files.Select(async file =>
    {
        await semaphore.WaitAsync();
        try
        {
            var zipFile = file + ".zip";
            var result = await compressor.CompressFileAsync(file, zipFile);
            Console.WriteLine($"完成: {Path.GetFileName(file)} -> {result.CompressionRatio:P1}");
            return result;
        }
        finally
        {
            semaphore.Release();
        }
    });

    var results = await Task.WhenAll(tasks);
    Console.WriteLine($"并行压缩完成: {results.Length} 个文件");
}

// ✅ 推荐：使用进度监控改善用户体验
public async Task CompressWithProgress(string sourceDir, string zipFile)
{
    var compressor = new YFileCompressor();
    var progress = new Progress<string>(message =>
    {
        // 更新UI或控制台显示
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
    });

    var cts = new CancellationTokenSource();

    try
    {
        var result = await compressor.CompressDirectoryAsync(
            sourceDir, zipFile,
            CompressionLevel.Optimal,
            includeBaseDirectory: true,
            cancellationToken: cts.Token,
            progress: progress
        );

        Console.WriteLine($"压缩完成: {result.GetFormattedSizes()}");
    }
    catch (OperationCanceledException)
    {
        Console.WriteLine("用户取消了压缩操作");
    }
}
```

### **5. 监控和日志**

```csharp
// ✅ 推荐：记录压缩操作日志
public void LogCompressionOperation(CompressionResult result, string operation)
{
    var logEntry = new
    {
        Timestamp = DateTime.Now,
        Operation = operation,
        Success = result.Success,
        OriginalSize = result.OriginalSize,
        CompressedSize = result.CompressedSize,
        CompressionRatio = result.CompressionRatio,
        SpaceSaved = result.SpaceSaved,
        FileCount = result.FileCount,
        ProcessingTime = result.ProcessingTime.TotalMilliseconds,
        ErrorMessage = result.ErrorMessage
    };

    // 写入日志文件或数据库
    var logJson = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions { WriteIndented = true });
    File.AppendAllText("compression.log", logJson + Environment.NewLine);

    // 控制台输出
    if (result.Success)
    {
        Console.WriteLine($"✅ {operation} 成功: {result.GetFormattedSizes()}, 耗时: {result.ProcessingTime.TotalSeconds:F1}s");
    }
    else
    {
        Console.WriteLine($"❌ {operation} 失败: {result.ErrorMessage}");
    }
}

// 使用示例
var result = compressor.CompressDirectory(@"C:\Project", @"D:\Project.zip");
LogCompressionOperation(result, "目录压缩");
```

---

## 📞 **技术支持**

- 📧 **邮箱**: <<EMAIL>>
- 🌐 **官网**: <https://zylo.dev>
- 📚 **文档**: <https://docs.zylo.dev/yio/filecompressor>
- 🐛 **问题反馈**: <https://github.com/zylo/yio/issues>

---

*YFileCompressor 是 Zylo.YIO 框架的核心组件，为企业级应用提供可靠的文件压缩解决方案。*
