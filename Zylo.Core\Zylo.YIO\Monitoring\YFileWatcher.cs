using System;
using System.IO;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;


namespace Zylo.YIO.Monitoring
{
    /// <summary>
    /// YFileWatcher - 企业级文件系统监控工具类
    /// 
    /// 🚀 核心功能特性：
    /// • 实时文件系统监控：支持文件创建、修改、删除、重命名事件
    /// • 多路径监控：同时监控多个目录，独立配置
    /// • 智能事件聚合：防抖处理，批量事件处理，减少事件风暴
    /// • 高级过滤器：文件扩展名、路径模式、正则表达式过滤
    /// • 性能优化：异步处理、内存优化、缓冲区管理
    /// • 错误恢复：自动重连、错误统计、监控状态管理
    /// • 统计分析：事件统计、性能监控、监控报告
    /// 
    /// 💡 设计原则：
    /// • 高性能：异步事件处理，最小化资源占用
    /// • 可靠性：完善的错误处理和自动恢复机制
    /// • 灵活性：丰富的配置选项和过滤器
    /// • 易用性：简洁的API和事件驱动模式
    /// • 可扩展性：支持自定义事件处理器和过滤器
    /// 
    /// 📋 使用场景：
    /// • 文件同步和备份系统
    /// • 开发工具的文件变更检测
    /// • 日志文件监控和分析
    /// • 配置文件热重载
    /// • 文件系统安全监控
    /// </summary>
 
 
    public partial class YFileWatcher : IDisposable
    {
        // ==========================================
        // 🔧 私有字段和常量定义
        // ==========================================

        #region 私有字段

        /// <summary>
        /// 文件系统监控器字典 - 存储每个路径对应的监控器实例
        /// Key: 监控路径, Value: FileSystemWatcher实例
        /// </summary>
        private readonly ConcurrentDictionary<string, FileSystemWatcher> _watchers;

        /// <summary>
        /// 监控配置字典 - 存储每个路径对应的配置信息
        /// Key: 监控路径, Value: 监控配置
        /// </summary>
        private readonly ConcurrentDictionary<string, WatcherConfig> _configs;

        /// <summary>
        /// 防抖定时器 - 用于延迟处理事件，避免事件风暴
        /// </summary>
        private readonly Timer _debounceTimer;

        /// <summary>
        /// 待处理事件队列 - 存储需要防抖处理的文件系统事件
        /// 使用线程安全的队列确保并发访问安全
        /// </summary>
        private readonly ConcurrentQueue<FileSystemEventArgs> _pendingEvents;

        /// <summary>
        /// 事件统计信息 - 记录各类事件的发生次数和性能数据
        /// </summary>
        private readonly ConcurrentDictionary<string, WatcherStatistics> _statistics;

        /// <summary>
        /// 线程同步锁对象 - 用于保护关键代码段的线程安全
        /// </summary>
        private readonly object _lockObject = new();

        /// <summary>
        /// 资源释放标志 - 标记对象是否已被释放
        /// </summary>
        private volatile bool _disposed = false;

        /// <summary>
        /// 监控器启动时间 - 记录监控器的启动时间
        /// </summary>
        private readonly DateTime _startTime;

        #endregion

        #region 常量定义

        /// <summary>
        /// 默认防抖延迟时间（毫秒）
        /// </summary>
        private const int DEFAULT_DEBOUNCE_DELAY = 300;

        /// <summary>
        /// 默认缓冲区大小（字节）
        /// </summary>
        private const int DEFAULT_BUFFER_SIZE = 8192;

        /// <summary>
        /// 最大重试次数
        /// </summary>
        private const int MAX_RETRY_COUNT = 3;

        /// <summary>
        /// 统计信息更新间隔（毫秒）
        /// </summary>
        private const int STATISTICS_UPDATE_INTERVAL = 1000;

        #endregion

        // ==========================================
        // 🏗️ 构造函数和初始化
        // ==========================================

        #region 构造函数

        /// <summary>
        /// 初始化 YFileWatcher 实例
        /// 创建必要的数据结构和定时器
        /// </summary>
        public YFileWatcher()
        {
            // 初始化线程安全的数据结构
            _watchers = new ConcurrentDictionary<string, FileSystemWatcher>();
            _configs = new ConcurrentDictionary<string, WatcherConfig>();
            _pendingEvents = new ConcurrentQueue<FileSystemEventArgs>();
            _statistics = new ConcurrentDictionary<string, WatcherStatistics>();

            // 初始化防抖定时器，初始状态为禁用
            _debounceTimer = new Timer(ProcessPendingEvents, null, Timeout.Infinite, Timeout.Infinite);

            // 记录启动时间
            _startTime = DateTime.Now;

            // 输出初始化日志
            Console.WriteLine($"YFileWatcher 初始化完成 - {_startTime:yyyy-MM-dd HH:mm:ss}");
        }

        #endregion

        // ==========================================
        // 📢 事件定义
        // ==========================================

        #region 事件定义

        /// <summary>
        /// 文件创建事件
        /// 当监控目录中有新文件被创建时触发
        /// </summary>
        /// <remarks>
        /// 事件参数包含：文件完整路径、文件名、创建时间、文件大小等信息
        /// </remarks>
        public event EventHandler<FileChangeEventArgs>? FileCreated;

        /// <summary>
        /// 文件修改事件
        /// 当监控目录中的文件内容或属性被修改时触发
        /// </summary>
        /// <remarks>
        /// 注意：某些操作可能会触发多次修改事件，建议使用防抖处理
        /// </remarks>
        public event EventHandler<FileChangeEventArgs>? FileChanged;

        /// <summary>
        /// 文件删除事件
        /// 当监控目录中的文件被删除时触发
        /// </summary>
        /// <remarks>
        /// 删除事件触发时，文件已不存在，无法获取文件信息
        /// </remarks>
        public event EventHandler<FileChangeEventArgs>? FileDeleted;

        /// <summary>
        /// 文件重命名事件
        /// 当监控目录中的文件被重命名或移动时触发
        /// </summary>
        /// <remarks>
        /// 事件参数包含原文件名和新文件名信息
        /// </remarks>
        public event EventHandler<FileRenamedEventArgs>? FileRenamed;

        /// <summary>
        /// 批量变更事件
        /// 当启用防抖处理时，多个文件变更会被聚合为批量事件
        /// </summary>
        /// <remarks>
        /// 批量事件可以减少事件处理的频率，提高性能
        /// </remarks>
        public event EventHandler<BatchChangeEventArgs>? BatchChanges;

        /// <summary>
        /// 监控错误事件
        /// 当文件系统监控过程中发生错误时触发
        /// </summary>
        /// <remarks>
        /// 包括权限不足、路径不存在、系统资源不足等错误
        /// </remarks>
        public event EventHandler<WatcherErrorEventArgs>? WatcherError;

        /// <summary>
        /// 监控状态变更事件
        /// 当监控器的状态发生变化时触发（启动、停止、暂停等）
        /// </summary>
        public event EventHandler<WatcherStatusEventArgs>? StatusChanged;

        /// <summary>
        /// 统计信息更新事件
        /// 定期触发，提供监控统计信息
        /// </summary>
        public event EventHandler<WatcherStatisticsEventArgs>? StatisticsUpdated;

        #endregion

        // ==========================================
        // 🎯 公共API - 监控管理
        // ==========================================

        #region 监控管理

        /// <summary>
        /// 开始监控指定路径
        /// 创建并启动文件系统监控器，支持自定义配置
        /// </summary>
        /// <param name="path">要监控的目录路径，必须是有效的现有目录</param>
        /// <param name="config">监控配置，如果为null则使用默认配置</param>
        /// <returns>
        /// 监控启动成功返回true，失败返回false
        /// 如果路径已在监控中，返回true
        /// </returns>
        /// <exception cref="ArgumentException">路径为空或无效时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">没有访问权限时抛出</exception>
        /// <exception cref="DirectoryNotFoundException">目录不存在时抛出</exception>
        /// <remarks>
        /// 📋 功能说明：
        /// • 验证路径有效性和访问权限
        /// • 检查是否已在监控中，避免重复监控
        /// • 创建并配置FileSystemWatcher实例
        /// • 绑定事件处理器和错误处理
        /// • 启动监控并更新统计信息
        /// 
        /// 💡 使用建议：
        /// • 确保目录存在且有读取权限
        /// • 合理配置缓冲区大小，避免事件丢失
        /// • 对于高频变更的目录，建议启用防抖处理
        /// </remarks>
        /// <example>
        /// <code>
        /// var watcher = new YFileWatcher();
        /// var config = new WatcherConfig
        /// {
        ///     Filter = "*.txt",
        ///     IncludeSubdirectories = true,
        ///     EnableDebouncing = true
        /// };
        /// 
        /// bool success = watcher.StartWatching(@"C:\MyFolder", config);
        /// if (success)
        /// {
        ///     Console.WriteLine("监控启动成功");
        /// }
        /// </code>
        /// </example>
        public bool StartWatching(string path, WatcherConfig? config = null)
        {
            // 参数验证 - 检查路径是否有效
            if (string.IsNullOrWhiteSpace(path))
            {
                var error = "监控路径不能为空或空白字符";
                Console.WriteLine($"❌ {error}");
                OnWatcherError(new WatcherErrorEventArgs(path ?? "", new ArgumentException(error)));
                return false;
            }

            // 标准化路径格式，确保路径一致性
            path = Path.GetFullPath(path);

            try
            {
                // 验证目录是否存在
                if (!Directory.Exists(path))
                {
                    var error = $"监控目录不存在: {path}";
                    Console.WriteLine($"❌ {error}");
                    OnWatcherError(new WatcherErrorEventArgs(path, new DirectoryNotFoundException(error)));
                    return false;
                }

                // 检查是否已在监控中
                if (_watchers.ContainsKey(path))
                {
                    Console.WriteLine($"⚠️ 路径已在监控中: {path}");
                    return true; // 已在监控中，返回成功
                }

                // 使用默认配置（如果未提供）
                config ??= new WatcherConfig();

                // 创建文件系统监控器
                var watcher = CreateFileSystemWatcher(path, config);
                if (watcher == null)
                {
                    var error = "创建文件系统监控器失败";
                    Console.WriteLine($"❌ {error}");
                    OnWatcherError(new WatcherErrorEventArgs(path, new InvalidOperationException(error)));
                    return false;
                }

                // 原子性操作：同时添加监控器和配置
                if (_watchers.TryAdd(path, watcher) && _configs.TryAdd(path, config))
                {
                    // 初始化统计信息
                    _statistics.TryAdd(path, new WatcherStatistics(path));

                    // 启动监控
                    watcher.EnableRaisingEvents = true;

                    // 触发状态变更事件
                    OnStatusChanged(new WatcherStatusEventArgs(path, WatcherStatus.Started));

                    Console.WriteLine($"✅ 开始监控路径: {path}");
                    Console.WriteLine($"   📁 包含子目录: {config.IncludeSubdirectories}");
                    Console.WriteLine($"   🔍 文件过滤器: {config.Filter}");
                    Console.WriteLine($"   ⏱️ 防抖处理: {(config.EnableDebouncing ? "启用" : "禁用")}");

                    return true;
                }
                else
                {
                    // 添加失败，清理资源
                    watcher.Dispose();
                    _watchers.TryRemove(path, out _);
                    _configs.TryRemove(path, out _);

                    var error = "添加监控器到集合失败";
                    Console.WriteLine($"❌ {error}");
                    OnWatcherError(new WatcherErrorEventArgs(path, new InvalidOperationException(error)));
                    return false;
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                var error = $"没有访问路径的权限: {path}";
                Console.WriteLine($"❌ {error} - {ex.Message}");
                OnWatcherError(new WatcherErrorEventArgs(path, ex));
                return false;
            }
            catch (Exception ex)
            {
                var error = $"启动监控失败: {path}";
                Console.WriteLine($"❌ {error} - {ex.Message}");
                OnWatcherError(new WatcherErrorEventArgs(path, ex));
                return false;
            }
        }

        /// <summary>
        /// 停止监控指定路径
        /// 安全地停止并释放指定路径的文件系统监控器
        /// </summary>
        /// <param name="path">要停止监控的目录路径</param>
        /// <returns>
        /// 停止成功返回true，失败或路径未在监控中返回false
        /// </returns>
        public bool StopWatching(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
            {
                Console.WriteLine("❌ 停止监控失败: 路径不能为空");
                return false;
            }

            path = Path.GetFullPath(path);

            try
            {
                if (_watchers.TryRemove(path, out var watcher))
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                    _configs.TryRemove(path, out _);
                    _statistics.TryRemove(path, out _);

                    OnStatusChanged(new WatcherStatusEventArgs(path, WatcherStatus.Stopped));
                    Console.WriteLine($"✅ 停止监控路径: {path}");
                    return true;
                }
                else
                {
                    Console.WriteLine($"⚠️ 路径未在监控中: {path}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                var error = $"停止监控失败: {path}";
                Console.WriteLine($"❌ {error} - {ex.Message}");
                OnWatcherError(new WatcherErrorEventArgs(path, ex));
                return false;
            }
        }

        /// <summary>
        /// 停止所有监控
        /// </summary>
        public void StopAllWatching()
        {
            var paths = new List<string>(_watchers.Keys);
            Console.WriteLine($"🛑 开始停止所有监控，共 {paths.Count} 个路径");

            int successCount = 0;
            foreach (var path in paths)
            {
                if (StopWatching(path))
                {
                    successCount++;
                }
            }

            Console.WriteLine($"✅ 停止监控完成: {successCount}/{paths.Count} 个路径成功停止");
        }

        /// <summary>
        /// 获取当前监控的路径列表
        /// </summary>
        /// <returns>监控路径列表</returns>
        public List<string> GetWatchedPaths()
        {
            return new List<string>(_watchers.Keys);
        }

        /// <summary>
        /// 检查指定路径是否在监控中
        /// </summary>
        /// <param name="path">要检查的目录路径</param>
        /// <returns>在监控中返回true，否则返回false</returns>
        public bool IsWatching(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return false;

            try
            {
                path = Path.GetFullPath(path);
                return _watchers.ContainsKey(path);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取监控统计信息
        /// </summary>
        /// <returns>监控统计信息字典</returns>
        public Dictionary<string, WatcherStatistics> GetStatistics()
        {
            return new Dictionary<string, WatcherStatistics>(_statistics);
        }

        #endregion

        // ==========================================
        // 🔧 私有方法 - 内部实现
        // ==========================================

        #region 私有方法

        /// <summary>
        /// 创建文件系统监控器
        /// 根据配置创建并初始化FileSystemWatcher实例
        /// </summary>
        /// <param name="path">监控路径</param>
        /// <param name="config">监控配置</param>
        /// <returns>配置好的FileSystemWatcher实例，失败时返回null</returns>
        private FileSystemWatcher? CreateFileSystemWatcher(string path, WatcherConfig config)
        {
            try
            {
                var watcher = new FileSystemWatcher(path)
                {
                    Filter = config.Filter,
                    IncludeSubdirectories = config.IncludeSubdirectories,
                    NotifyFilter = config.NotifyFilter,
                    InternalBufferSize = config.BufferSize
                };

                // 绑定事件处理器
                watcher.Created += (sender, e) => HandleFileSystemEvent(e, FileChangeType.Created);
                watcher.Changed += (sender, e) => HandleFileSystemEvent(e, FileChangeType.Changed);
                watcher.Deleted += (sender, e) => HandleFileSystemEvent(e, FileChangeType.Deleted);
                watcher.Renamed += (sender, e) => HandleRenamedEvent(e);
                watcher.Error += (sender, e) => HandleErrorEvent(e);

                Console.WriteLine($"📁 创建文件系统监控器成功: {path}");
                return watcher;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建文件系统监控器失败: {path} - {ex.Message}");
                OnWatcherError(new WatcherErrorEventArgs(path, ex));
                return null;
            }
        }

        /// <summary>
        /// 处理文件系统事件
        /// 统一的文件系统事件处理入口，负责过滤、防抖和事件分发
        /// </summary>
        /// <param name="e">文件系统事件参数</param>
        /// <param name="changeType">变更类型</param>
        private void HandleFileSystemEvent(FileSystemEventArgs e, FileChangeType changeType)
        {
            try
            {
                // 应用事件过滤器
                if (!ShouldProcessEvent(e.FullPath, changeType))
                    return;

                // 更新统计信息
                UpdateStatistics(e.FullPath, changeType);

                // 检查防抖处理配置
                if (_configs.Values.Any(c => c.EnableDebouncing))
                {
                    // 防抖处理 - 将事件加入队列
                    _pendingEvents.Enqueue(e);
                    var debounceDelay = _configs.Values.FirstOrDefault()?.DebounceDelayMs ?? DEFAULT_DEBOUNCE_DELAY;
                    _debounceTimer.Change(debounceDelay, Timeout.Infinite);
                    Console.WriteLine($"🔄 事件加入防抖队列: {e.Name} ({changeType})");
                }
                else
                {
                    // 立即处理事件
                    ProcessEvent(e, changeType);
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"处理文件系统事件时发生错误: {e.FullPath}";
                Console.WriteLine($"❌ {errorMsg} - {ex.Message}");
                OnWatcherError(new WatcherErrorEventArgs(e.FullPath, ex));
            }
        }

        /// <summary>
        /// 处理文件重命名事件
        /// 专门处理文件重命名和移动操作
        /// </summary>
        /// <param name="e">重命名事件参数</param>
        private void HandleRenamedEvent(RenamedEventArgs e)
        {
            try
            {
                if (!ShouldProcessEvent(e.FullPath, FileChangeType.Renamed))
                    return;

                UpdateStatistics(e.FullPath, FileChangeType.Renamed);

                var args = new FileRenamedEventArgs
                {
                    FullPath = e.FullPath,
                    OldFullPath = e.OldFullPath,
                    Name = e.Name,
                    OldName = e.OldName,
                    ChangeType = FileChangeType.Renamed,
                    Timestamp = DateTime.Now
                };

                OnFileRenamed(args);
                Console.WriteLine($"📝 文件重命名: {e.OldName} → {e.Name}");
            }
            catch (Exception ex)
            {
                OnWatcherError(new WatcherErrorEventArgs(e.FullPath, ex));
            }
        }

        /// <summary>
        /// 处理错误事件
        /// 处理文件系统监控过程中的错误
        /// </summary>
        /// <param name="e">错误事件参数</param>
        private void HandleErrorEvent(ErrorEventArgs e)
        {
            var exception = e.GetException();
            Console.WriteLine($"❌ 文件系统监控错误: {exception.Message}");
            OnWatcherError(new WatcherErrorEventArgs("", exception));
        }

        /// <summary>
        /// 检查是否应该处理事件
        /// 应用各种过滤器判断事件是否需要处理
        /// </summary>
        /// <param name="fullPath">文件完整路径</param>
        /// <param name="changeType">变更类型</param>
        /// <returns>应该处理返回true，否则返回false</returns>
        private bool ShouldProcessEvent(string fullPath, FileChangeType changeType)
        {
            // 获取对应的配置
            var config = _configs.Values.FirstOrDefault();
            if (config == null) return true;

            // 检查文件扩展名过滤
            if (config.ExcludedExtensions.Any())
            {
                var extension = Path.GetExtension(fullPath).ToLowerInvariant();
                if (config.ExcludedExtensions.Contains(extension))
                {
                    Console.WriteLine($"🚫 跳过排除的扩展名: {extension}");
                    return false;
                }
            }

            // 检查路径过滤
            if (config.ExcludedPaths.Any())
            {
                if (config.ExcludedPaths.Any(excluded => fullPath.Contains(excluded)))
                {
                    Console.WriteLine($"🚫 跳过排除的路径: {fullPath}");
                    return false;
                }
            }

            // 检查变更类型过滤
            if (!config.MonitoredChangeTypes.HasFlag(changeType))
            {
                Console.WriteLine($"🚫 跳过未监控的变更类型: {changeType}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 处理单个事件
        /// 创建事件参数并触发相应的事件
        /// </summary>
        /// <param name="e">文件系统事件参数</param>
        /// <param name="changeType">变更类型</param>
        private void ProcessEvent(FileSystemEventArgs e, FileChangeType changeType)
        {
            var args = new FileChangeEventArgs
            {
                FullPath = e.FullPath,
                Name = e.Name,
                ChangeType = changeType,
                Timestamp = DateTime.Now
            };

            // 尝试获取文件信息
            try
            {
                if (File.Exists(e.FullPath))
                {
                    var fileInfo = new FileInfo(e.FullPath);
                    args.FileSize = fileInfo.Length;
                    args.FileExtension = fileInfo.Extension;
                }
            }
            catch
            {
                // 忽略文件信息获取失败（文件可能已被删除）
            }

            // 根据变更类型触发相应事件
            switch (changeType)
            {
                case FileChangeType.Created:
                    OnFileCreated(args);
                    Console.WriteLine($"📄 文件创建: {args.Name}");
                    break;
                case FileChangeType.Changed:
                    OnFileChanged(args);
                    Console.WriteLine($"✏️ 文件修改: {args.Name}");
                    break;
                case FileChangeType.Deleted:
                    OnFileDeleted(args);
                    Console.WriteLine($"🗑️ 文件删除: {args.Name}");
                    break;
            }
        }

        /// <summary>
        /// 处理待处理事件队列
        /// 防抖定时器回调方法，处理队列中的事件
        /// </summary>
        /// <param name="state">定时器状态</param>
        private void ProcessPendingEvents(object? state)
        {
            var events = new List<FileSystemEventArgs>();

            // 收集所有待处理事件
            while (_pendingEvents.TryDequeue(out var evt))
            {
                events.Add(evt);
            }

            if (events.Count == 0) return;

            Console.WriteLine($"🔄 处理防抖队列中的 {events.Count} 个事件");

            // 去重和聚合 - 同一文件的多个事件只保留最后一个
            var groupedEvents = events
                .GroupBy(e => e.FullPath)
                .Select(g => g.Last()) // 取最后一个事件
                .ToList();

            Console.WriteLine($"📊 去重后剩余 {groupedEvents.Count} 个事件");

            // 触发批量变更事件
            if (groupedEvents.Count > 1)
            {
                var batchArgs = new BatchChangeEventArgs
                {
                    Changes = groupedEvents.Select(e => new FileChangeEventArgs
                    {
                        FullPath = e.FullPath,
                        Name = e.Name,
                        ChangeType = GetChangeType(e),
                        Timestamp = DateTime.Now
                    }).ToList(),
                    Timestamp = DateTime.Now
                };

                OnBatchChanges(batchArgs);
                Console.WriteLine($"📦 触发批量变更事件: {batchArgs.TotalChanges} 个文件");
            }
            else if (groupedEvents.Count == 1)
            {
                // 单个事件直接处理
                var evt = groupedEvents[0];
                ProcessEvent(evt, GetChangeType(evt));
            }
        }

        /// <summary>
        /// 获取变更类型
        /// 将FileSystemWatcher的变更类型转换为自定义的变更类型
        /// </summary>
        /// <param name="e">文件系统事件参数</param>
        /// <returns>文件变更类型</returns>
        private FileChangeType GetChangeType(FileSystemEventArgs e)
        {
            return e.ChangeType switch
            {
                WatcherChangeTypes.Created => FileChangeType.Created,
                WatcherChangeTypes.Changed => FileChangeType.Changed,
                WatcherChangeTypes.Deleted => FileChangeType.Deleted,
                WatcherChangeTypes.Renamed => FileChangeType.Renamed,
                _ => FileChangeType.Changed
            };
        }

        /// <summary>
        /// 更新统计信息
        /// 更新指定路径的事件统计数据
        /// </summary>
        /// <param name="fullPath">文件完整路径</param>
        /// <param name="changeType">变更类型</param>
        private void UpdateStatistics(string fullPath, FileChangeType changeType)
        {
            try
            {
                // 找到对应的监控路径
                var monitorPath = _statistics.Keys.FirstOrDefault(path => fullPath.StartsWith(path));
                if (monitorPath != null && _statistics.TryGetValue(monitorPath, out var stats))
                {
                    // 更新统计计数
                    stats.TotalEvents++;
                    switch (changeType)
                    {
                        case FileChangeType.Created:
                            stats.CreatedEvents++;
                            break;
                        case FileChangeType.Changed:
                            stats.ChangedEvents++;
                            break;
                        case FileChangeType.Deleted:
                            stats.DeletedEvents++;
                            break;
                        case FileChangeType.Renamed:
                            stats.RenamedEvents++;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 更新统计信息失败: {ex.Message}");
            }
        }

        #endregion

        // ==========================================
        // 📢 事件触发方法
        // ==========================================

        #region 事件触发方法

        /// <summary>
        /// 触发文件创建事件
        /// </summary>
        /// <param name="e">文件变更事件参数</param>
        protected virtual void OnFileCreated(FileChangeEventArgs e)
        {
            FileCreated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发文件修改事件
        /// </summary>
        /// <param name="e">文件变更事件参数</param>
        protected virtual void OnFileChanged(FileChangeEventArgs e)
        {
            FileChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发文件删除事件
        /// </summary>
        /// <param name="e">文件变更事件参数</param>
        protected virtual void OnFileDeleted(FileChangeEventArgs e)
        {
            FileDeleted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发文件重命名事件
        /// </summary>
        /// <param name="e">文件重命名事件参数</param>
        protected virtual void OnFileRenamed(FileRenamedEventArgs e)
        {
            FileRenamed?.Invoke(this, e);
        }

        /// <summary>
        /// 触发批量变更事件
        /// </summary>
        /// <param name="e">批量变更事件参数</param>
        protected virtual void OnBatchChanges(BatchChangeEventArgs e)
        {
            BatchChanges?.Invoke(this, e);
        }

        /// <summary>
        /// 触发监控错误事件
        /// </summary>
        /// <param name="e">监控错误事件参数</param>
        protected virtual void OnWatcherError(WatcherErrorEventArgs e)
        {
            WatcherError?.Invoke(this, e);
        }

        /// <summary>
        /// 触发监控状态变更事件
        /// </summary>
        /// <param name="e">监控状态事件参数</param>
        protected virtual void OnStatusChanged(WatcherStatusEventArgs e)
        {
            StatusChanged?.Invoke(this, e);
        }

        /// <summary>
        /// 触发统计信息更新事件
        /// </summary>
        /// <param name="e">统计信息事件参数</param>
        protected virtual void OnStatisticsUpdated(WatcherStatisticsEventArgs e)
        {
            StatisticsUpdated?.Invoke(this, e);
        }

        #endregion

        // ==========================================
        // 🧹 资源释放
        // ==========================================

        #region 资源释放

        /// <summary>
        /// 释放所有资源
        /// 实现IDisposable接口，确保资源正确释放
        /// </summary>
        /// <remarks>
        /// 📋 功能说明：
        /// • 停止所有活动的文件监控
        /// • 释放定时器和其他托管资源
        /// • 清理内存和句柄
        /// • 防止内存泄漏
        ///
        /// 💡 使用建议：
        /// • 在应用程序关闭时调用
        /// • 使用using语句自动释放
        /// • 不要在释放后继续使用对象
        /// </remarks>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 受保护的资源释放方法
        /// 实际执行资源释放的逻辑
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        /// <remarks>
        /// 📋 释放流程：
        /// • 检查是否已释放，避免重复释放
        /// • 停止所有文件监控器
        /// • 释放定时器资源
        /// • 标记为已释放状态
        ///
        /// 💡 设计模式：
        /// • 遵循标准的Dispose模式
        /// • 支持继承类的资源释放
        /// • 线程安全的释放操作
        /// </remarks>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                try
                {
                    // 停止所有监控
                    StopAllWatching();

                    // 释放定时器
                    _debounceTimer?.Dispose();

                    // 清理事件队列
                    while (_pendingEvents.TryDequeue(out _)) { }

                    Console.WriteLine("🧹 YFileWatcher 资源释放完成");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 资源释放时发生错误: {ex.Message}");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        #endregion
    }

    // ==========================================
    // 📊 数据模型和配置类
    // ==========================================

    #region 数据模型和配置类

    /// <summary>
    /// 监控配置类
    /// 定义文件系统监控的各种配置选项
    /// </summary>
    public class WatcherConfig
    {
        /// <summary>
        /// 文件过滤器
        /// 支持通配符模式，如 "*.txt", "*.log", "*.*"
        /// </summary>
        public string Filter { get; set; } = "*.*";

        /// <summary>
        /// 是否包含子目录
        /// true: 监控所有子目录, false: 仅监控指定目录
        /// </summary>
        public bool IncludeSubdirectories { get; set; } = true;

        /// <summary>
        /// 通知过滤器
        /// 指定要监控的文件系统变更类型
        /// </summary>
        public NotifyFilters NotifyFilter { get; set; } =
            NotifyFilters.FileName |
            NotifyFilters.DirectoryName |
            NotifyFilters.LastWrite;

        /// <summary>
        /// 缓冲区大小（字节）
        /// 内部缓冲区大小，影响能处理的事件数量
        /// </summary>
        public int BufferSize { get; set; } = 8192;

        /// <summary>
        /// 启用防抖处理
        /// true: 启用防抖，减少事件风暴
        /// false: 立即处理所有事件
        /// </summary>
        public bool EnableDebouncing { get; set; } = true;

        /// <summary>
        /// 防抖延迟（毫秒）
        /// 事件防抖的延迟时间，默认300ms
        /// </summary>
        public int DebounceDelayMs { get; set; } = 300;

        /// <summary>
        /// 排除的文件扩展名
        /// 不监控这些扩展名的文件变更
        /// </summary>
        public HashSet<string> ExcludedExtensions { get; set; } = new();

        /// <summary>
        /// 排除的路径
        /// 不监控包含这些路径的文件变更
        /// </summary>
        public HashSet<string> ExcludedPaths { get; set; } = new();

        /// <summary>
        /// 监控的变更类型
        /// 指定要监控的文件变更类型组合
        /// </summary>
        public FileChangeType MonitoredChangeTypes { get; set; } = FileChangeType.All;

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns>默认配置实例</returns>
        public static WatcherConfig Default => new();

        /// <summary>
        /// 创建高性能配置
        /// 适用于高频变更场景
        /// </summary>
        /// <returns>高性能配置实例</returns>
        public static WatcherConfig HighPerformance => new()
        {
            BufferSize = 65536,
            EnableDebouncing = true,
            DebounceDelayMs = 500,
            NotifyFilter = NotifyFilters.FileName | NotifyFilters.LastWrite
        };

        /// <summary>
        /// 创建低延迟配置
        /// 适用于需要即时响应的场景
        /// </summary>
        /// <returns>低延迟配置实例</returns>
        public static WatcherConfig LowLatency => new()
        {
            EnableDebouncing = false,
            BufferSize = 16384,
            NotifyFilter = NotifyFilters.FileName | NotifyFilters.DirectoryName | NotifyFilters.LastWrite
        };
    }

    /// <summary>
    /// 文件变更类型枚举
    /// 定义文件系统中可能发生的变更类型
    /// </summary>
    [Flags]
    public enum FileChangeType
    {
        /// <summary>
        /// 无变更
        /// </summary>
        None = 0,

        /// <summary>
        /// 文件创建
        /// 新文件被创建时触发
        /// </summary>
        Created = 1,

        /// <summary>
        /// 文件修改
        /// 文件内容或属性被修改时触发
        /// </summary>
        Changed = 2,

        /// <summary>
        /// 文件删除
        /// 文件被删除时触发
        /// </summary>
        Deleted = 4,

        /// <summary>
        /// 文件重命名
        /// 文件被重命名或移动时触发
        /// </summary>
        Renamed = 8,

        /// <summary>
        /// 所有变更类型
        /// 包含所有可能的变更类型
        /// </summary>
        All = Created | Changed | Deleted | Renamed
    }

    /// <summary>
    /// 文件变更事件参数
    /// 包含文件变更的详细信息
    /// </summary>
    public class FileChangeEventArgs : EventArgs
    {
        /// <summary>
        /// 文件完整路径
        /// </summary>
        public string FullPath { get; set; } = "";

        /// <summary>
        /// 文件名（不包含路径）
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// 变更类型
        /// </summary>
        public FileChangeType ChangeType { get; set; }

        /// <summary>
        /// 事件发生时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// 仅在文件存在时有效
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件扩展名
        /// 包含点号，如 ".txt", ".log"
        /// </summary>
        public string? FileExtension { get; set; }
    }

    /// <summary>
    /// 文件重命名事件参数
    /// 继承自FileChangeEventArgs，包含重命名的额外信息
    /// </summary>
    public class FileRenamedEventArgs : FileChangeEventArgs
    {
        /// <summary>
        /// 原文件完整路径
        /// </summary>
        public string OldFullPath { get; set; } = "";

        /// <summary>
        /// 原文件名（不包含路径）
        /// </summary>
        public string? OldName { get; set; }
    }

    /// <summary>
    /// 批量变更事件参数
    /// 当启用防抖处理时，多个文件变更会被聚合为批量事件
    /// </summary>
    public class BatchChangeEventArgs : EventArgs
    {
        /// <summary>
        /// 变更列表
        /// 包含所有聚合的文件变更
        /// </summary>
        public List<FileChangeEventArgs> Changes { get; set; } = new();

        /// <summary>
        /// 批量事件时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 总变更数量
        /// </summary>
        public int TotalChanges => Changes.Count;

        /// <summary>
        /// 按变更类型分组的统计
        /// </summary>
        public Dictionary<FileChangeType, int> ChangesByType
        {
            get
            {
                return Changes
                    .GroupBy(c => c.ChangeType)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
        }
    }

    /// <summary>
    /// 监控错误事件参数
    /// 当文件系统监控过程中发生错误时使用
    /// </summary>
    public class WatcherErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 发生错误的路径
        /// </summary>
        public string Path { get; set; } = "";

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 错误发生时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 错误级别
        /// </summary>
        public ErrorLevel Level { get; set; } = ErrorLevel.Error;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="exception">异常</param>
        /// <param name="level">错误级别</param>
        public WatcherErrorEventArgs(string path, Exception exception, ErrorLevel level = ErrorLevel.Error)
        {
            Path = path;
            Exception = exception;
            Level = level;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 错误级别枚举
    /// </summary>
    public enum ErrorLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }

    /// <summary>
    /// 监控状态枚举
    /// </summary>
    public enum WatcherStatus
    {
        /// <summary>
        /// 未启动
        /// </summary>
        NotStarted,

        /// <summary>
        /// 已启动
        /// </summary>
        Started,

        /// <summary>
        /// 已停止
        /// </summary>
        Stopped,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused,

        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }

    /// <summary>
    /// 监控状态变更事件参数
    /// </summary>
    public class WatcherStatusEventArgs : EventArgs
    {
        /// <summary>
        /// 监控路径
        /// </summary>
        public string Path { get; set; } = "";

        /// <summary>
        /// 新状态
        /// </summary>
        public WatcherStatus Status { get; set; }

        /// <summary>
        /// 状态变更时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 状态变更原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="status">状态</param>
        /// <param name="reason">变更原因</param>
        public WatcherStatusEventArgs(string path, WatcherStatus status, string? reason = null)
        {
            Path = path;
            Status = status;
            Reason = reason;
            Timestamp = DateTime.Now;
        }
    }

    /// <summary>
    /// 监控统计信息
    /// 记录文件系统监控的各种统计数据
    /// </summary>
    public class WatcherStatistics
    {
        /// <summary>
        /// 监控路径
        /// </summary>
        public string Path { get; set; } = "";

        /// <summary>
        /// 监控开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 总事件数
        /// </summary>
        public long TotalEvents { get; set; }

        /// <summary>
        /// 创建事件数
        /// </summary>
        public long CreatedEvents { get; set; }

        /// <summary>
        /// 修改事件数
        /// </summary>
        public long ChangedEvents { get; set; }

        /// <summary>
        /// 删除事件数
        /// </summary>
        public long DeletedEvents { get; set; }

        /// <summary>
        /// 重命名事件数
        /// </summary>
        public long RenamedEvents { get; set; }

        /// <summary>
        /// 错误事件数
        /// </summary>
        public long ErrorEvents { get; set; }

        /// <summary>
        /// 总运行时间
        /// </summary>
        public TimeSpan TotalRunTime => DateTime.Now - StartTime;

        /// <summary>
        /// 平均每分钟事件数
        /// </summary>
        public double EventsPerMinute
        {
            get
            {
                var minutes = TotalRunTime.TotalMinutes;
                return minutes > 0 ? TotalEvents / minutes : 0;
            }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="path">监控路径</param>
        public WatcherStatistics(string path)
        {
            Path = path;
            StartTime = DateTime.Now;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            StartTime = DateTime.Now;
            TotalEvents = 0;
            CreatedEvents = 0;
            ChangedEvents = 0;
            DeletedEvents = 0;
            RenamedEvents = 0;
            ErrorEvents = 0;
        }

        /// <summary>
        /// 获取统计摘要
        /// </summary>
        /// <returns>统计摘要字符串</returns>
        public string GetSummary()
        {
            return $"路径: {Path}, 运行时间: {TotalRunTime:hh\\:mm\\:ss}, " +
                   $"总事件: {TotalEvents}, 创建: {CreatedEvents}, 修改: {ChangedEvents}, " +
                   $"删除: {DeletedEvents}, 重命名: {RenamedEvents}, 错误: {ErrorEvents}";
        }
    }

    /// <summary>
    /// 监控统计信息事件参数
    /// </summary>
    public class WatcherStatisticsEventArgs : EventArgs
    {
        /// <summary>
        /// 统计信息字典
        /// Key: 监控路径, Value: 统计信息
        /// </summary>
        public Dictionary<string, WatcherStatistics> Statistics { get; set; } = new();

        /// <summary>
        /// 统计更新时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 总监控路径数
        /// </summary>
        public int TotalPaths => Statistics.Count;

        /// <summary>
        /// 总事件数
        /// </summary>
        public long TotalEvents => Statistics.Values.Sum(s => s.TotalEvents);

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="statistics">统计信息字典</param>
        public WatcherStatisticsEventArgs(Dictionary<string, WatcherStatistics> statistics)
        {
            Statistics = statistics;
            Timestamp = DateTime.Now;
        }
    }

    #endregion
}
