# YFileWatcher - 企业级文件系统监控工具类

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🔍 **实时文件系统监控解决方案** - 提供完整的文件变更监控、事件处理、统计分析和性能优化功能

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [配置选项](#-配置选项)
- [事件处理](#-事件处理)
- [统计分析](#-统计分析)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **🔍 实时文件系统监控**

- ✅ **文件事件监控**: 创建、修改、删除、重命名事件实时捕获
- ✅ **多路径监控**: 同时监控多个目录，独立配置管理
- ✅ **子目录支持**: 递归监控子目录，深度可配置

### **⚡ 智能事件处理**

- ✅ **防抖处理**: 智能事件聚合，避免事件风暴
- ✅ **批量处理**: 批量事件处理，提高性能
- ✅ **事件过滤**: 文件扩展名、路径模式、正则表达式过滤

### **🎯 高级过滤器**

- ✅ **文件类型过滤**: 支持通配符模式 (*.txt,*.log)
- ✅ **路径排除**: 排除特定路径和目录
- ✅ **扩展名排除**: 排除特定文件类型
- ✅ **变更类型过滤**: 选择性监控特定变更类型

### **📊 统计分析系统**

- ✅ **实时统计**: 事件数量、类型分布、性能指标
- ✅ **性能监控**: 事件处理速度、内存使用情况
- ✅ **错误统计**: 错误分级、错误计数、错误恢复

### **🔧 配置管理**

- ✅ **预设配置**: 高性能、低延迟、默认配置
- ✅ **自定义配置**: 灵活的配置选项组合
- ✅ **动态配置**: 运行时配置更新

### **🛡️ 安全特性**

- ✅ **参数验证**: 严格的输入验证和错误处理
- ✅ **资源管理**: 自动资源释放和内存管理
- ✅ **异常处理**: 优雅的错误处理和恢复机制
- ✅ **线程安全**: 并发安全的操作和数据访问

## 🚀 **快速开始**

### **安装**

```csharp
// 通过 NuGet 包管理器安装
Install-Package Zylo.YIO

// 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Monitoring;

var fileWatcher = new YFileWatcher();

// 订阅事件
fileWatcher.FileCreated += (sender, e) => 
    Console.WriteLine($"文件创建: {e.FullPath}");
fileWatcher.FileChanged += (sender, e) => 
    Console.WriteLine($"文件修改: {e.FullPath}");
fileWatcher.FileDeleted += (sender, e) => 
    Console.WriteLine($"文件删除: {e.FullPath}");

// 开始监控
bool success = fileWatcher.StartWatching(@"C:\MyProject");
if (success)
{
    Console.WriteLine("文件监控启动成功");
}

// 停止监控
fileWatcher.StopWatching(@"C:\MyProject");
```

## 🔧 **核心功能**

### **基础监控操作**

```csharp
var fileWatcher = new YFileWatcher();

// 启动监控（使用默认配置）
bool started = fileWatcher.StartWatching(@"C:\MyFolder");

// 启动监控（使用自定义配置）
var config = new WatcherConfig
{
    Filter = "*.txt",
    IncludeSubdirectories = true,
    EnableDebouncing = true,
    DebounceDelayMs = 500
};
bool success = fileWatcher.StartWatching(@"C:\MyFolder", config);

// 停止特定路径监控
bool stopped = fileWatcher.StopWatching(@"C:\MyFolder");

// 停止所有监控
fileWatcher.StopAllWatching();

// 检查监控状态
bool isWatching = fileWatcher.IsWatching(@"C:\MyFolder");
```

### **多路径监控**

```csharp
var fileWatcher = new YFileWatcher();

// 同时监控多个路径
var paths = new[]
{
    @"C:\Project\src",
    @"C:\Project\docs", 
    @"C:\Project\config"
};

foreach (var path in paths)
{
    var config = new WatcherConfig
    {
        Filter = path.Contains("src") ? "*.cs" : "*.*",
        IncludeSubdirectories = true
    };
    fileWatcher.StartWatching(path, config);
}

// 获取所有监控路径
var watchedPaths = fileWatcher.GetWatchedPaths();
Console.WriteLine($"正在监控 {watchedPaths.Count} 个路径");
```

### **高级配置使用**

```csharp
// 高性能配置（适用于高频变更场景）
var highPerfConfig = WatcherConfig.HighPerformance;
fileWatcher.StartWatching(@"C:\HighActivity", highPerfConfig);

// 低延迟配置（适用于即时响应场景）
var lowLatencyConfig = WatcherConfig.LowLatency;
fileWatcher.StartWatching(@"C:\RealTime", lowLatencyConfig);

// 自定义配置
var customConfig = new WatcherConfig
{
    Filter = "*.log",
    IncludeSubdirectories = false,
    EnableDebouncing = true,
    DebounceDelayMs = 1000,
    ExcludedExtensions = { ".tmp", ".bak" },
    ExcludedPaths = { @"C:\Temp", @"C:\Cache" },
    MonitoredChangeTypes = FileChangeType.Created | FileChangeType.Changed
};
fileWatcher.StartWatching(@"C:\Logs", customConfig);
```

## ⚙️ **配置选项**

### **WatcherConfig 配置类**

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `Filter` | `string` | `"*.*"` | 文件过滤器，支持通配符 |
| `IncludeSubdirectories` | `bool` | `true` | 是否包含子目录 |
| `NotifyFilter` | `NotifyFilters` | `FileName \| DirectoryName \| LastWrite` | 通知过滤器 |
| `BufferSize` | `int` | `8192` | 缓冲区大小（字节） |
| `EnableDebouncing` | `bool` | `true` | 启用防抖处理 |
| `DebounceDelayMs` | `int` | `300` | 防抖延迟（毫秒） |
| `ExcludedExtensions` | `HashSet<string>` | `空集合` | 排除的文件扩展名 |
| `ExcludedPaths` | `HashSet<string>` | `空集合` | 排除的路径 |
| `MonitoredChangeTypes` | `FileChangeType` | `All` | 监控的变更类型 |

### **预设配置**

```csharp
// 默认配置
var defaultConfig = WatcherConfig.Default;

// 高性能配置
var highPerfConfig = WatcherConfig.HighPerformance;
// - BufferSize: 65536
// - EnableDebouncing: true
// - DebounceDelayMs: 500

// 低延迟配置  
var lowLatencyConfig = WatcherConfig.LowLatency;
// - EnableDebouncing: false
// - BufferSize: 16384
```

## 📢 **事件处理**

### **基础事件**

```csharp
var fileWatcher = new YFileWatcher();

// 文件创建事件
fileWatcher.FileCreated += (sender, e) =>
{
    Console.WriteLine($"文件创建: {e.FullPath}");
    Console.WriteLine($"文件大小: {e.FileSize} 字节");
    Console.WriteLine($"时间戳: {e.Timestamp}");
};

// 文件修改事件
fileWatcher.FileChanged += (sender, e) =>
{
    Console.WriteLine($"文件修改: {e.Name}");
    Console.WriteLine($"扩展名: {e.FileExtension}");
};

// 文件删除事件
fileWatcher.FileDeleted += (sender, e) =>
{
    Console.WriteLine($"文件删除: {e.FullPath}");
};

// 文件重命名事件
fileWatcher.FileRenamed += (sender, e) =>
{
    Console.WriteLine($"文件重命名: {e.OldFullPath} -> {e.FullPath}");
};
```

### **批量事件处理**

```csharp
// 批量变更事件
fileWatcher.BatchChanges += (sender, e) =>
{
    Console.WriteLine($"批量变更完成:");
    Console.WriteLine($"- 总事件数: {e.TotalEvents}");
    Console.WriteLine($"- 创建: {e.CreatedCount}");
    Console.WriteLine($"- 修改: {e.ChangedCount}");
    Console.WriteLine($"- 删除: {e.DeletedCount}");
    Console.WriteLine($"- 重命名: {e.RenamedCount}");
    Console.WriteLine($"- 处理时间: {e.ProcessingTime}ms");
};
```

### **错误事件处理**

```csharp
// 错误事件
fileWatcher.WatcherError += (sender, e) =>
{
    Console.WriteLine($"监控错误:");
    Console.WriteLine($"- 路径: {e.Path}");
    Console.WriteLine($"- 级别: {e.Level}");
    Console.WriteLine($"- 消息: {e.Exception.Message}");
    Console.WriteLine($"- 时间: {e.Timestamp}");
};

// 统计更新事件
fileWatcher.StatisticsUpdated += (sender, e) =>
{
    Console.WriteLine($"统计更新:");
    Console.WriteLine($"- 总路径数: {e.TotalPaths}");
    Console.WriteLine($"- 活跃路径数: {e.ActivePaths}");
    Console.WriteLine($"- 总事件数: {e.TotalEvents}");
    Console.WriteLine($"- 错误数: {e.ErrorCount}");
};
```

## 📊 **统计分析**

### **获取统计信息**

```csharp
// 获取所有路径的统计信息
var allStatistics = fileWatcher.GetStatistics();

foreach (var kvp in allStatistics)
{
    var path = kvp.Key;
    var stats = kvp.Value;
    
    Console.WriteLine($"路径: {path}");
    Console.WriteLine($"- 总事件数: {stats.TotalEvents}");
    Console.WriteLine($"- 创建事件: {stats.CreatedEvents}");
    Console.WriteLine($"- 修改事件: {stats.ChangedEvents}");
    Console.WriteLine($"- 删除事件: {stats.DeletedEvents}");
    Console.WriteLine($"- 重命名事件: {stats.RenamedEvents}");
    Console.WriteLine($"- 错误事件: {stats.ErrorEvents}");
    Console.WriteLine($"- 运行时间: {stats.TotalRunTime}");
    Console.WriteLine($"- 每分钟事件数: {stats.EventsPerMinute:F2}");
}
```

### **重置统计信息**

```csharp
// 重置特定路径的统计信息
fileWatcher.ResetStatistics(@"C:\MyFolder");

// 重置所有统计信息
fileWatcher.ResetAllStatistics();
```

## 📚 **API 参考**

### **🎯 监控管理方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `StartWatching` | `string path, WatcherConfig? config = null` | `bool` | 启动指定路径的文件监控 |
| `StopWatching` | `string path` | `bool` | 停止指定路径的文件监控 |
| `StopAllWatching` | 无 | `void` | 停止所有路径的文件监控 |
| `IsWatching` | `string path` | `bool` | 检查指定路径是否正在监控 |
| `GetWatchedPaths` | 无 | `List<string>` | 获取所有正在监控的路径列表 |

### **📊 统计信息方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `GetStatistics` | 无 | `Dictionary<string, WatcherStatistics>` | 获取所有路径的统计信息 |
| `GetStatistics` | `string path` | `WatcherStatistics?` | 获取指定路径的统计信息 |
| `ResetStatistics` | `string path` | `void` | 重置指定路径的统计信息 |
| `ResetAllStatistics` | 无 | `void` | 重置所有路径的统计信息 |

### **📢 事件定义**

| 事件名 | 事件参数类型 | 触发条件 |
|--------|-------------|----------|
| `FileCreated` | `FileChangeEventArgs` | 文件创建时触发 |
| `FileChanged` | `FileChangeEventArgs` | 文件修改时触发 |
| `FileDeleted` | `FileChangeEventArgs` | 文件删除时触发 |
| `FileRenamed` | `FileRenamedEventArgs` | 文件重命名时触发 |
| `BatchChanges` | `BatchChangeEventArgs` | 批量变更处理完成时触发 |
| `WatcherError` | `WatcherErrorEventArgs` | 监控错误时触发 |
| `StatisticsUpdated` | `WatcherStatisticsEventArgs` | 统计信息更新时触发 |

### **📊 数据模型**

#### **FileChangeEventArgs 属性**

- `string FullPath` - 文件完整路径
- `string? Name` - 文件名
- `FileChangeType ChangeType` - 变更类型
- `DateTime Timestamp` - 时间戳
- `long? FileSize` - 文件大小（字节）
- `string? FileExtension` - 文件扩展名

#### **FileRenamedEventArgs 属性**

- `string FullPath` - 新文件路径
- `string? Name` - 新文件名
- `string OldFullPath` - 原文件路径
- `string? OldName` - 原文件名
- `DateTime Timestamp` - 时间戳

#### **BatchChangeEventArgs 属性**

- `int TotalEvents` - 总事件数
- `int CreatedCount` - 创建事件数
- `int ChangedCount` - 修改事件数
- `int DeletedCount` - 删除事件数
- `int RenamedCount` - 重命名事件数
- `long ProcessingTime` - 处理时间（毫秒）

#### **WatcherStatistics 属性**

- `string Path` - 监控路径
- `DateTime StartTime` - 开始时间
- `int TotalEvents` - 总事件数
- `int CreatedEvents` - 创建事件数
- `int ChangedEvents` - 修改事件数
- `int DeletedEvents` - 删除事件数
- `int RenamedEvents` - 重命名事件数
- `int ErrorEvents` - 错误事件数
- `TimeSpan TotalRunTime` - 总运行时间
- `double EventsPerMinute` - 每分钟事件数

### **🔧 枚举定义**

#### **FileChangeType 枚举**

```csharp
[Flags]
public enum FileChangeType
{
    Created = 1,
    Changed = 2,
    Deleted = 4,
    Renamed = 8,
    All = Created | Changed | Deleted | Renamed
}
```

#### **ErrorLevel 枚举**

```csharp
public enum ErrorLevel
{
    Information,
    Warning,
    Error,
    Critical
}
```

## 🧪 **测试覆盖**

### **基础功能测试**

- ✅ **监控启动/停止**: 路径验证、状态管理、资源释放
- ✅ **多路径监控**: 并发监控、独立配置、状态查询
- ✅ **配置管理**: 默认配置、预设配置、自定义配置

### **高级功能测试**

- ✅ **事件过滤**: 文件类型过滤、路径排除、扩展名排除
- ✅ **防抖处理**: 事件聚合、批量处理、延迟配置
- ✅ **错误处理**: 无效路径、权限错误、异常恢复

### **性能测试**

- ✅ **大量文件处理**: 高频事件、内存使用、处理速度
- ✅ **并发安全**: 多线程访问、数据一致性、资源竞争
- ✅ **统计准确性**: 事件计数、时间统计、性能指标

### **边界条件测试**

- ✅ **参数验证**: 空值处理、无效路径、边界值
- ✅ **资源管理**: 内存泄漏、文件句柄、定时器清理
- ✅ **异常处理**: 网络中断、磁盘满、权限变更

## 💡 **最佳实践**

### **🎯 监控配置建议**

```csharp
// 1. 根据场景选择合适的配置
// 开发环境 - 即时响应
var devConfig = WatcherConfig.LowLatency;

// 生产环境 - 高性能
var prodConfig = WatcherConfig.HighPerformance;

// 日志监控 - 自定义配置
var logConfig = new WatcherConfig
{
    Filter = "*.log",
    EnableDebouncing = true,
    DebounceDelayMs = 1000,
    ExcludedExtensions = { ".tmp", ".lock" }
};
```

### **🔧 性能优化建议**

```csharp
// 1. 合理设置缓冲区大小
var config = new WatcherConfig
{
    BufferSize = 32768, // 高频场景使用更大缓冲区
    EnableDebouncing = true,
    DebounceDelayMs = 500 // 根据业务需求调整防抖延迟
};

// 2. 使用路径排除减少不必要的监控
config.ExcludedPaths.Add(@"C:\Windows\Temp");
config.ExcludedPaths.Add(@"C:\$Recycle.Bin");

// 3. 限制监控的变更类型
config.MonitoredChangeTypes = FileChangeType.Created | FileChangeType.Changed;
```

### **📊 错误处理建议**

```csharp
// 1. 实现完整的错误处理
fileWatcher.WatcherError += (sender, e) =>
{
    switch (e.Level)
    {
        case ErrorLevel.Warning:
            _logger.LogWarning($"文件监控警告: {e.Exception.Message}");
            break;
        case ErrorLevel.Error:
            _logger.LogError($"文件监控错误: {e.Exception.Message}");
            // 可以考虑重启监控
            break;
        case ErrorLevel.Critical:
            _logger.LogCritical($"文件监控严重错误: {e.Exception.Message}");
            // 停止监控并通知管理员
            break;
    }
};

// 2. 定期检查监控状态
var timer = new Timer(_ =>
{
    var paths = fileWatcher.GetWatchedPaths();
    foreach (var path in paths)
    {
        if (!fileWatcher.IsWatching(path))
        {
            _logger.LogWarning($"路径 {path} 监控已停止，尝试重启");
            fileWatcher.StartWatching(path);
        }
    }
}, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
```

### **🧹 资源管理建议**

```csharp
// 1. 正确释放资源
public class FileMonitorService : IDisposable
{
    private readonly YFileWatcher _fileWatcher;

    public FileMonitorService()
    {
        _fileWatcher = new YFileWatcher();
    }

    public void Dispose()
    {
        _fileWatcher?.StopAllWatching();
        _fileWatcher?.Dispose();
    }
}

// 2. 使用 using 语句确保资源释放
using var fileWatcher = new YFileWatcher();
fileWatcher.StartWatching(@"C:\MyProject");
// 自动释放资源
```

### **📈 监控策略建议**

```csharp
// 1. 分层监控策略
// 核心业务文件 - 低延迟监控
fileWatcher.StartWatching(@"C:\Business\Core", WatcherConfig.LowLatency);

// 日志文件 - 批量处理
var logConfig = new WatcherConfig
{
    Filter = "*.log",
    EnableDebouncing = true,
    DebounceDelayMs = 2000
};
fileWatcher.StartWatching(@"C:\Logs", logConfig);

// 临时文件 - 排除监控
var mainConfig = new WatcherConfig();
mainConfig.ExcludedPaths.Add(@"C:\Temp");
mainConfig.ExcludedExtensions.Add(".tmp");
fileWatcher.StartWatching(@"C:\Project", mainConfig);
```

---

## 🎉 **总结**

**YFileWatcher** 是一个功能完整、性能优异的企业级文件系统监控解决方案，具有以下特点：

- **🚀 高性能**: 异步事件处理、智能防抖、批量处理
- **🛡️ 高可靠性**: 完善的错误处理、资源管理、异常恢复
- **🔧 高灵活性**: 丰富的配置选项、多种预设配置、自定义过滤器
- **📊 可观测性**: 详细的统计信息、性能监控、事件分析
- **💡 易用性**: 简洁的API、事件驱动、完整的文档

适用于文件同步、开发工具、日志监控、配置热重载等多种企业级应用场景。
