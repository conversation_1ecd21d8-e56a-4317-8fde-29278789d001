using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace Zylo.YIO.Monitoring
{
    /// <summary>
    /// YPerformanceMonitor - 企业级文件操作性能监控工具类
    ///
    /// 🚀 核心功能特性：
    /// • 实时性能监控：支持同步和异步操作的性能监控
    /// • 统计分析系统：提供详细的操作统计、成功率分析、性能指标
    /// • 智能报告生成：自动生成性能报告和优化建议
    /// • 历史数据管理：维护操作历史记录，支持趋势分析
    /// • 多操作类型支持：支持文件读写、复制、压缩等各种操作类型
    /// • 性能优化建议：基于统计数据提供智能优化建议
    /// • 定时报告机制：支持定时生成和输出性能报告
    /// ///
    /// 💡 设计原则：
    /// • 高性能：使用并发安全的数据结构，最小化性能开销
    /// • 线程安全：完全支持多线程环境下的并发监控
    /// • 内存优化：自动限制历史记录数量，防止内存泄漏
    /// • 易用性：简洁的API设计，支持链式调用和异步操作
    /// • 可扩展性：支持自定义操作类型和统计指标
    ///
    /// 📋 使用场景：
    /// • 文件操作性能分析和优化
    /// • 系统性能监控和诊断
    /// • 批量操作性能评估
    /// • 性能回归测试和基准测试
    /// • 生产环境性能监控和告警
    /// </summary>


    public partial class YPerformanceMonitor
    {
        // ==========================================
        // 🔧 私有字段和常量定义
        // ==========================================

        #region 私有字段

        /// <summary>
        /// 操作指标存储 - 按操作类型分组的性能指标
        /// 使用 ConcurrentDictionary 确保线程安全
        /// </summary>
        private readonly ConcurrentDictionary<string, OperationMetrics> _operationMetrics;

        /// <summary>
        /// 性能历史记录队列 - 存储最近的操作记录
        /// 使用 ConcurrentQueue 支持高并发场景
        /// </summary>
        private readonly ConcurrentQueue<PerformanceRecord> _performanceHistory;

        /// <summary>
        /// 定时报告生成器 - 定期生成性能报告
        /// 默认每5分钟生成一次报告
        /// </summary>
        private readonly Timer _reportTimer;

        /// <summary>
        /// 同步锁对象 - 用于保护关键代码段
        /// 虽然使用了并发集合，但某些复合操作仍需要同步
        /// </summary>
        private readonly object _lockObject = new();

        /// <summary>
        /// 总操作计数器 - 记录所有操作的总数
        /// 使用 Interlocked 操作确保原子性
        /// </summary>
        private long _totalOperations = 0;

        /// <summary>
        /// 总处理字节数 - 记录所有成功操作处理的字节总数
        /// 用于计算整体吞吐量
        /// </summary>
        private long _totalBytesProcessed = 0;

        /// <summary>
        /// 历史记录最大数量限制
        /// 防止内存无限增长
        /// </summary>
        private const int MAX_HISTORY_RECORDS = 1000;

        /// <summary>
        /// 定时报告间隔（分钟）
        /// 可根据需要调整报告频率
        /// </summary>
        private const int REPORT_INTERVAL_MINUTES = 5;

        #endregion

        // ==========================================
        // 🏗️ 构造函数和初始化
        // ==========================================

        #region 构造函数

        /// <summary>
        /// 初始化 YPerformanceMonitor 实例
        /// 创建必要的数据结构和定时器
        /// </summary>
        public YPerformanceMonitor()
        {
            // 初始化线程安全的数据结构
            _operationMetrics = new ConcurrentDictionary<string, OperationMetrics>();
            _performanceHistory = new ConcurrentQueue<PerformanceRecord>();

            // 启动定时报告生成器
            // 首次延迟5分钟，之后每5分钟执行一次
            _reportTimer = new Timer(
                GeneratePeriodicReport,
                null,
                TimeSpan.FromMinutes(REPORT_INTERVAL_MINUTES),
                TimeSpan.FromMinutes(REPORT_INTERVAL_MINUTES)
            );

            // 输出初始化日志
            Console.WriteLine($"YPerformanceMonitor 初始化完成 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        #endregion

        // ==========================================
        // 🎯 公共API - 性能监控管理
        // ==========================================

        #region 性能监控核心方法

        /// <summary>
        /// 开始监控文件操作性能
        /// 创建性能监控上下文，开始计时和数据收集
        /// </summary>
        /// <param name="operationType">操作类型标识符（如：FileRead、FileWrite、FileCopy等）</param>
        /// <param name="filePath">被操作的文件路径，用于日志和统计</param>
        /// <param name="fileSize">文件大小（字节），用于计算吞吐量，默认为0</param>
        /// <returns>性能监控上下文对象，包含计时器和操作信息</returns>
        /// <example>
        /// <code>
        /// var context = monitor.StartMonitoring("FileRead", @"C:\data\file.txt", 1024);
        /// // 执行文件操作...
        /// monitor.EndMonitoring(context, true);
        /// </code>
        /// </example>
        public PerformanceContext StartMonitoring(string operationType, string filePath, long fileSize = 0)
        {
            // 创建性能监控上下文对象
            var context = new PerformanceContext
            {
                OperationType = operationType,      // 设置操作类型标识
                FilePath = filePath,                // 设置文件路径
                FileSize = fileSize,                // 设置文件大小
                StartTime = DateTime.Now,           // 记录开始时间
                Stopwatch = Stopwatch.StartNew()   // 启动高精度计时器
            };

            // 原子性地增加总操作计数器
            // 使用 Interlocked 确保在多线程环境下的线程安全
            Interlocked.Increment(ref _totalOperations);

            return context;
        }

        /// <summary>
        /// 结束监控操作并记录性能数据
        /// 停止计时，更新统计信息，记录历史数据
        /// </summary>
        /// <param name="context">由 StartMonitoring 返回的性能监控上下文</param>
        /// <param name="success">操作是否成功完成，影响成功率统计</param>
        /// <param name="errorMessage">操作失败时的错误信息，可选</param>
        /// <example>
        /// <code>
        /// try
        /// {
        ///     // 执行文件操作
        ///     monitor.EndMonitoring(context, true);
        /// }
        /// catch (Exception ex)
        /// {
        ///     monitor.EndMonitoring(context, false, ex.Message);
        /// }
        /// </code>
        /// </example>
        public void EndMonitoring(PerformanceContext context, bool success = true, string? errorMessage = null)
        {
            // 参数验证 - 确保上下文和计时器有效
            if (context?.Stopwatch == null) return;

            // 停止高精度计时器
            context.Stopwatch.Stop();

            // 记录结束时间和计算持续时间
            context.EndTime = DateTime.Now;
            context.Duration = context.Stopwatch.Elapsed;

            // 设置操作结果状态
            context.Success = success;
            context.ErrorMessage = errorMessage;

            // 更新操作类型的统计指标
            // 包括计数、成功率、平均时间等
            UpdateOperationMetrics(context);

            // 将操作记录添加到历史数据中
            // 用于趋势分析和详细报告
            RecordPerformance(context);

            // 更新总处理字节数（仅成功操作）
            // 用于计算整体吞吐量
            if (success && context.FileSize > 0)
            {
                Interlocked.Add(ref _totalBytesProcessed, context.FileSize);
            }
        }

        /// <summary>
        /// 监控异步操作的性能
        /// 自动处理性能监控的开始、结束和异常情况
        /// </summary>
        /// <typeparam name="T">异步操作的返回类型</typeparam>
        /// <param name="operationType">操作类型标识符</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="operation">要监控的异步操作委托</param>
        /// <param name="fileSize">文件大小（字节），用于吞吐量计算</param>
        /// <returns>异步操作的结果</returns>
        /// <exception cref="Exception">重新抛出原始操作中的异常</exception>
        /// <example>
        /// <code>
        /// var result = await monitor.MonitorAsync("FileReadAsync", filePath, async () =>
        /// {
        ///     return await File.ReadAllTextAsync(filePath);
        /// }, fileInfo.Length);
        /// </code>
        /// </example>
        public async Task<T> MonitorAsync<T>(string operationType, string filePath, Func<Task<T>> operation, long fileSize = 0)
        {
            // 开始性能监控，创建监控上下文
            var context = StartMonitoring(operationType, filePath, fileSize);

            try
            {
                // 执行异步操作并等待完成
                var result = await operation();

                // 操作成功完成，记录成功状态
                EndMonitoring(context, true);

                return result;
            }
            catch (Exception ex)
            {
                // 操作失败，记录失败状态和错误信息
                EndMonitoring(context, false, ex.Message);

                // 重新抛出异常，保持原有的异常处理流程
                throw;
            }
        }

        /// <summary>
        /// 监控同步操作的性能
        /// 自动处理性能监控的开始、结束和异常情况
        /// </summary>
        /// <typeparam name="T">同步操作的返回类型</typeparam>
        /// <param name="operationType">操作类型标识符</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="operation">要监控的同步操作委托</param>
        /// <param name="fileSize">文件大小（字节），用于吞吐量计算</param>
        /// <returns>同步操作的结果</returns>
        /// <exception cref="Exception">重新抛出原始操作中的异常</exception>
        /// <example>
        /// <code>
        /// var content = monitor.Monitor("FileRead", filePath, () =>
        /// {
        ///     return File.ReadAllText(filePath);
        /// }, fileInfo.Length);
        /// </code>
        /// </example>
        public T Monitor<T>(string operationType, string filePath, Func<T> operation, long fileSize = 0)
        {
            // 开始性能监控，创建监控上下文
            var context = StartMonitoring(operationType, filePath, fileSize);

            try
            {
                // 执行同步操作
                var result = operation();

                // 操作成功完成，记录成功状态
                EndMonitoring(context, true);

                return result;
            }
            catch (Exception ex)
            {
                // 操作失败，记录失败状态和错误信息
                EndMonitoring(context, false, ex.Message);

                // 重新抛出异常，保持原有的异常处理流程
                throw;
            }
        }

        #endregion

        // ==========================================
        // 📊 性能统计和分析方法
        // ==========================================

        #region 性能统计方法

        /// <summary>
        /// 获取指定操作类型的详细统计信息
        /// 包括操作次数、成功率、平均耗时、吞吐量等关键指标
        /// </summary>
        /// <param name="operationType">操作类型标识符（如：FileRead、FileWrite等）</param>
        /// <returns>操作统计信息对象，如果操作类型不存在则返回null</returns>
        /// <example>
        /// <code>
        /// var stats = monitor.GetOperationStatistics("FileRead");
        /// if (stats != null)
        /// {
        ///     Console.WriteLine($"成功率: {stats.SuccessRate:F2}%");
        ///     Console.WriteLine($"平均耗时: {stats.AverageDuration.TotalMilliseconds}ms");
        /// }
        /// </code>
        /// </example>
        public OperationStatistics? GetOperationStatistics(string operationType)
        {
            // 尝试从并发字典中获取指定操作类型的指标数据
            if (!_operationMetrics.TryGetValue(operationType, out var metrics))
                return null;

            // 使用锁确保在读取指标数据时的一致性
            // 虽然使用了并发集合，但复合操作仍需要同步
            lock (metrics)
            {
                // 创建统计信息对象，计算各种性能指标
                return new OperationStatistics
                {
                    OperationType = operationType,
                    TotalOperations = metrics.TotalCount,
                    SuccessfulOperations = metrics.SuccessCount,
                    FailedOperations = metrics.FailureCount,

                    // 计算成功率百分比，避免除零错误
                    SuccessRate = metrics.TotalCount > 0
                        ? (double)metrics.SuccessCount / metrics.TotalCount * 100
                        : 0,

                    // 计算平均持续时间，避免除零错误
                    AverageDuration = metrics.TotalCount > 0
                        ? TimeSpan.FromTicks(metrics.TotalDuration.Ticks / metrics.TotalCount)
                        : TimeSpan.Zero,

                    MinDuration = metrics.MinDuration,
                    MaxDuration = metrics.MaxDuration,
                    TotalBytesProcessed = metrics.TotalBytes,

                    // 计算平均处理速度（字节/秒）
                    AverageSpeed = CalculateAverageSpeed(metrics)
                };
            }
        }

        /// <summary>
        /// 获取所有操作类型的统计信息列表
        /// 按操作总数降序排列，便于识别最频繁的操作
        /// </summary>
        /// <returns>所有操作统计信息的列表，按操作频率降序排列</returns>
        /// <example>
        /// <code>
        /// var allStats = monitor.GetAllStatistics();
        /// foreach (var stat in allStats)
        /// {
        ///     Console.WriteLine($"{stat.OperationType}: {stat.TotalOperations} 次操作");
        /// }
        /// </code>
        /// </example>
        public List<OperationStatistics> GetAllStatistics()
        {
            var statistics = new List<OperationStatistics>();

            // 遍历所有已记录的操作类型
            foreach (var kvp in _operationMetrics)
            {
                // 获取每个操作类型的详细统计信息
                var stat = GetOperationStatistics(kvp.Key);
                if (stat != null)
                {
                    statistics.Add(stat);
                }
            }

            // 按操作总数降序排列，最频繁的操作排在前面
            return statistics.OrderByDescending(s => s.TotalOperations).ToList();
        }

        /// <summary>
        /// 获取系统整体性能摘要
        /// 汇总所有操作类型的关键性能指标，提供全局视图
        /// 这是一个复杂的计算方法，涉及多个统计指标的聚合
        /// </summary>
        /// <returns>包含全局性能指标的摘要对象</returns>
        /// <example>
        /// <code>
        /// var summary = monitor.GetPerformanceSummary();
        /// Console.WriteLine($"总体成功率: {summary.OverallSuccessRate:F2}%");
        /// Console.WriteLine($"最快操作: {summary.FastestOperation}");
        /// </code>
        /// </example>
        public PerformanceSummary GetPerformanceSummary()
        {
            // 获取所有操作的统计信息作为计算基础
            var allStats = GetAllStatistics();

            return new PerformanceSummary
            {
                // 直接从原子计数器获取总操作数
                TotalOperations = _totalOperations,

                // 直接从原子计数器获取总处理字节数
                TotalBytesProcessed = _totalBytesProcessed,

                // 操作类型总数
                TotalOperationTypes = _operationMetrics.Count,

                // 计算所有操作类型的平均成功率
                // 使用加权平均可能更准确，但简单平均更易理解
                OverallSuccessRate = allStats.Count > 0
                    ? allStats.Average(s => s.SuccessRate)
                    : 0,

                // 计算所有操作类型的平均持续时间
                // 将 Ticks 转换为 TimeSpan，避免精度损失
                AverageOperationDuration = allStats.Count > 0
                    ? TimeSpan.FromTicks((long)allStats.Average(s => s.AverageDuration.Ticks))
                    : TimeSpan.Zero,

                // 找出平均耗时最短的操作类型
                FastestOperation = allStats
                    .OrderBy(s => s.AverageDuration)
                    .FirstOrDefault()?.OperationType ?? "",

                // 找出平均耗时最长的操作类型
                SlowestOperation = allStats
                    .OrderByDescending(s => s.AverageDuration)
                    .FirstOrDefault()?.OperationType ?? "",

                // 找出执行次数最多的操作类型
                MostFrequentOperation = allStats
                    .OrderByDescending(s => s.TotalOperations)
                    .FirstOrDefault()?.OperationType ?? "",

                // 记录摘要生成时间
                GeneratedAt = DateTime.Now
            };
        }

        #endregion

        #region 性能报告

        /// <summary>
        /// 生成性能报告
        /// </summary>
        /// <param name="includeHistory">是否包含历史数据</param>
        /// <returns>性能报告</returns>
        public PerformanceReport GenerateReport(bool includeHistory = false)
        {
            var summary = GetPerformanceSummary();
            var statistics = GetAllStatistics();
            var history = includeHistory ? GetRecentHistory(100) : new List<PerformanceRecord>();

            return new PerformanceReport
            {
                Summary = summary,
                OperationStatistics = statistics,
                RecentHistory = history,
                Recommendations = GenerateRecommendations(statistics),
                GeneratedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 获取最近的性能历史记录
        /// </summary>
        /// <param name="count">记录数量</param>
        /// <returns>性能历史记录</returns>
        public List<PerformanceRecord> GetRecentHistory(int count = 50)
        {
            return _performanceHistory.TakeLast(count).ToList();
        }

        /// <summary>
        /// 清除性能数据
        /// </summary>
        public void ClearPerformanceData()
        {
            _operationMetrics.Clear();

            while (_performanceHistory.TryDequeue(out _))
            {
                // 清空队列
            }

            Interlocked.Exchange(ref _totalOperations, 0);
            Interlocked.Exchange(ref _totalBytesProcessed, 0);
        }

        #endregion

        // ==========================================
        // 🔧 私有辅助方法
        // ==========================================

        #region 私有方法

        /// <summary>
        /// 更新指定操作类型的性能指标
        /// 这是一个复杂的方法，需要处理并发更新和多种统计指标
        /// </summary>
        /// <param name="context">包含操作结果的性能上下文</param>
        private void UpdateOperationMetrics(PerformanceContext context)
        {
            // 获取或创建操作类型对应的指标对象
            // GetOrAdd 方法是线程安全的，确保并发场景下的正确性
            var metrics = _operationMetrics.GetOrAdd(context.OperationType, _ => new OperationMetrics());

            // 使用锁保护指标更新操作
            // 虽然 ConcurrentDictionary 是线程安全的，但指标对象内部的更新需要同步
            lock (metrics)
            {
                // 增加总操作计数
                metrics.TotalCount++;

                // 根据操作结果更新相应的计数器
                if (context.Success)
                {
                    // 操作成功：增加成功计数
                    metrics.SuccessCount++;
                }
                else
                {
                    // 操作失败：增加失败计数
                    metrics.FailureCount++;
                }

                // 累加总持续时间，用于计算平均时间
                metrics.TotalDuration = metrics.TotalDuration.Add(context.Duration);

                // 更新最小持续时间
                // 首次记录或当前时间更短时更新
                if (metrics.MinDuration == TimeSpan.Zero || context.Duration < metrics.MinDuration)
                {
                    metrics.MinDuration = context.Duration;
                }

                // 更新最大持续时间
                // 当前时间更长时更新
                if (context.Duration > metrics.MaxDuration)
                {
                    metrics.MaxDuration = context.Duration;
                }

                // 累加处理字节数（仅当文件大小有效时）
                // 用于计算吞吐量统计
                if (context.FileSize > 0)
                {
                    metrics.TotalBytes += context.FileSize;
                }
            }
        }

        /// <summary>
        /// 记录性能历史数据
        /// 将操作上下文转换为历史记录并管理队列大小
        /// </summary>
        /// <param name="context">性能监控上下文</param>
        private void RecordPerformance(PerformanceContext context)
        {
            // 创建性能记录对象，包含所有关键信息
            var record = new PerformanceRecord
            {
                OperationType = context.OperationType,
                FilePath = context.FilePath,
                FileSize = context.FileSize,
                Duration = context.Duration,
                Success = context.Success,
                ErrorMessage = context.ErrorMessage,
                Timestamp = context.StartTime,

                // 计算处理速度（字节/秒）
                // 需要同时检查文件大小和持续时间的有效性
                Speed = context.FileSize > 0 && context.Duration.TotalSeconds > 0
                    ? context.FileSize / context.Duration.TotalSeconds
                    : 0
            };

            // 将记录添加到历史队列中
            // ConcurrentQueue 是线程安全的，支持并发操作
            _performanceHistory.Enqueue(record);

            // 限制历史记录数量，防止内存无限增长
            // 使用常量 MAX_HISTORY_RECORDS 提高可维护性
            while (_performanceHistory.Count > MAX_HISTORY_RECORDS)
            {
                // 移除最旧的记录，TryDequeue 是线程安全的
                _performanceHistory.TryDequeue(out _);
            }
        }

        private double CalculateAverageSpeed(OperationMetrics metrics)
        {
            if (metrics.TotalBytes == 0 || metrics.TotalDuration.TotalSeconds == 0)
                return 0;

            return metrics.TotalBytes / metrics.TotalDuration.TotalSeconds;
        }

        /// <summary>
        /// 生成智能性能优化建议
        /// 这是一个复杂的分析方法，基于多维度统计数据生成优化建议
        /// 分析维度包括：成功率、性能耗时、操作频率、吞吐量等
        /// </summary>
        /// <param name="statistics">所有操作类型的统计信息列表</param>
        /// <returns>性能优化建议列表</returns>
        private List<string> GenerateRecommendations(List<OperationStatistics> statistics)
        {
            var recommendations = new List<string>();

            // === 第一维度分析：成功率分析 ===
            // 识别成功率低于90%的操作，这些操作可能存在稳定性问题
            var lowSuccessRateOps = statistics.Where(s => s.SuccessRate < 90).ToList();
            if (lowSuccessRateOps.Any())
            {
                // 按成功率升序排列，最需要关注的操作排在前面
                var sortedLowSuccessOps = lowSuccessRateOps.OrderBy(s => s.SuccessRate);
                var operationNames = string.Join(", ", sortedLowSuccessOps.Select(s => $"{s.OperationType}({s.SuccessRate:F1}%)"));
                recommendations.Add($"🚨 成功率告警: 以下操作成功率较低，建议检查错误原因并优化: {operationNames}");
            }

            // === 第二维度分析：性能耗时分析 ===
            // 识别平均耗时超过5秒的操作，这些操作可能存在性能瓶颈
            var slowOps = statistics.Where(s => s.AverageDuration.TotalSeconds > 5).ToList();
            if (slowOps.Any())
            {
                // 按平均耗时降序排列，最慢的操作排在前面
                var sortedSlowOps = slowOps.OrderByDescending(s => s.AverageDuration);
                var operationDetails = string.Join(", ", sortedSlowOps.Select(s => $"{s.OperationType}({s.AverageDuration.TotalSeconds:F1}s)"));
                recommendations.Add($"⏱️ 性能优化: 以下操作耗时较长，建议优化算法或增加并行处理: {operationDetails}");
            }

            // === 第三维度分析：操作频率分析 ===
            // 识别执行次数超过100次的高频操作，这些操作适合缓存或批量优化
            var frequentOps = statistics.Where(s => s.TotalOperations > 100).ToList();
            if (frequentOps.Any())
            {
                // 按操作次数降序排列，最频繁的操作排在前面
                var sortedFrequentOps = frequentOps.OrderByDescending(s => s.TotalOperations);
                var operationCounts = string.Join(", ", sortedFrequentOps.Select(s => $"{s.OperationType}({s.TotalOperations}次)"));
                recommendations.Add($"🔄 频率优化: 以下操作频率较高，建议考虑缓存、批量处理或连接池: {operationCounts}");
            }

            // === 第四维度分析：吞吐量分析 ===
            // 识别处理速度较慢的操作（小于1MB/s），这些操作可能存在I/O瓶颈
            var lowThroughputOps = statistics.Where(s => s.AverageSpeed > 0 && s.AverageSpeed < 1024 * 1024).ToList();
            if (lowThroughputOps.Any())
            {
                var throughputDetails = string.Join(", ", lowThroughputOps.Select(s => $"{s.OperationType}({s.AverageSpeed / 1024:F1}KB/s)"));
                recommendations.Add($"📊 吞吐量优化: 以下操作处理速度较慢，建议检查I/O配置或网络状况: {throughputDetails}");
            }

            // === 综合分析：无问题情况 ===
            // 如果所有指标都正常，给出积极反馈
            if (!recommendations.Any())
            {
                recommendations.Add("✅ 性能表现优秀: 当前所有操作的成功率、耗时和频率都在正常范围内，系统运行良好。");
            }

            return recommendations;
        }

        private void GeneratePeriodicReport(object? state)
        {
            try
            {
                var report = GenerateReport(false);
                Console.WriteLine($"[性能报告] 总操作数: {report.Summary.TotalOperations}, 总体成功率: {report.Summary.OverallSuccessRate:F2}%");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成性能报告失败: {ex.Message}");
            }
        }

        #endregion
    }

    #region 性能监控数据模型

    /// <summary>
    /// 性能监控上下文
    /// 用于跟踪单个文件操作的完整生命周期和性能数据
    /// </summary>
    public class PerformanceContext
    {
        /// <summary>
        /// 操作类型标识符
        /// 用于分类和统计不同类型的文件操作（如：FileRead、FileWrite、FileCopy等）
        /// </summary>
        public string OperationType { get; set; } = "";

        /// <summary>
        /// 被操作的文件路径
        /// 用于日志记录和问题追踪
        /// </summary>
        public string FilePath { get; set; } = "";

        /// <summary>
        /// 文件大小（字节）
        /// 用于计算吞吐量和处理速度
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 操作开始时间
        /// 用于计算操作持续时间和生成时间戳
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 操作结束时间
        /// 用于计算操作持续时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 操作持续时间
        /// 从开始到结束的总耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 操作是否成功
        /// 用于计算成功率统计
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// 当操作失败时记录具体的错误原因
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 高精度计时器
        /// 用于精确测量操作耗时
        /// </summary>
        public Stopwatch? Stopwatch { get; set; }
    }

    /// <summary>
    /// 操作指标
    /// 存储特定操作类型的原始统计数据，用于计算各种性能指标
    /// </summary>
    public class OperationMetrics
    {
        /// <summary>
        /// 总操作次数
        /// 包括成功和失败的所有操作
        /// </summary>
        public long TotalCount { get; set; }

        /// <summary>
        /// 成功操作次数
        /// 用于计算成功率
        /// </summary>
        public long SuccessCount { get; set; }

        /// <summary>
        /// 失败操作次数
        /// 用于计算失败率和稳定性分析
        /// </summary>
        public long FailureCount { get; set; }

        /// <summary>
        /// 总持续时间
        /// 所有操作的累计耗时，用于计算平均时间
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// 最小持续时间
        /// 记录最快的操作耗时
        /// </summary>
        public TimeSpan MinDuration { get; set; }

        /// <summary>
        /// 最大持续时间
        /// 记录最慢的操作耗时
        /// </summary>
        public TimeSpan MaxDuration { get; set; }

        /// <summary>
        /// 总处理字节数
        /// 所有成功操作处理的字节总数，用于计算吞吐量
        /// </summary>
        public long TotalBytes { get; set; }
    }

    /// <summary>
    /// 操作统计信息
    /// 基于原始指标计算得出的完整统计数据，提供给外部调用者使用
    /// </summary>
    public class OperationStatistics
    {
        /// <summary>
        /// 操作类型标识符
        /// 用于区分不同类型的文件操作
        /// </summary>
        public string OperationType { get; set; } = "";

        /// <summary>
        /// 总操作次数
        /// 包括成功和失败的所有操作
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// 成功操作次数
        /// 完成无错误的操作数量
        /// </summary>
        public long SuccessfulOperations { get; set; }

        /// <summary>
        /// 失败操作次数
        /// 发生错误或异常的操作数量
        /// </summary>
        public long FailedOperations { get; set; }

        /// <summary>
        /// 成功率（百分比）
        /// 成功操作占总操作的比例，范围0-100
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 平均持续时间
        /// 所有操作的平均耗时
        /// </summary>
        public TimeSpan AverageDuration { get; set; }

        /// <summary>
        /// 最小持续时间
        /// 最快操作的耗时
        /// </summary>
        public TimeSpan MinDuration { get; set; }

        /// <summary>
        /// 最大持续时间
        /// 最慢操作的耗时
        /// </summary>
        public TimeSpan MaxDuration { get; set; }

        /// <summary>
        /// 总处理字节数
        /// 所有成功操作处理的数据总量
        /// </summary>
        public long TotalBytesProcessed { get; set; }

        /// <summary>
        /// 平均处理速度（字节/秒）
        /// 数据处理的平均吞吐量
        /// </summary>
        public double AverageSpeed { get; set; }
    }

    /// <summary>
    /// 性能记录
    /// 单个操作的完整历史记录，用于趋势分析和详细报告
    /// </summary>
    public class PerformanceRecord
    {
        /// <summary>
        /// 操作类型标识符
        /// 标识具体的操作类型
        /// </summary>
        public string OperationType { get; set; } = "";

        /// <summary>
        /// 文件路径
        /// 被操作的文件完整路径
        /// </summary>
        public string FilePath { get; set; } = "";

        /// <summary>
        /// 文件大小（字节）
        /// 操作涉及的数据量
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 操作持续时间
        /// 从开始到结束的总耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 操作是否成功
        /// 标识操作的最终结果
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// 操作失败时的具体错误描述
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 时间戳
        /// 操作发生的具体时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 处理速度（字节/秒）
        /// 数据处理的实时吞吐量
        /// </summary>
        public double Speed { get; set; }
    }

    /// <summary>
    /// 性能摘要
    /// 系统整体性能的高级概览，包含关键指标和趋势信息
    /// </summary>
    public class PerformanceSummary
    {
        /// <summary>
        /// 总操作次数
        /// 系统启动以来的所有操作总数
        /// </summary>
        public long TotalOperations { get; set; }

        /// <summary>
        /// 总处理字节数
        /// 所有成功操作处理的数据总量
        /// </summary>
        public long TotalBytesProcessed { get; set; }

        /// <summary>
        /// 操作类型总数
        /// 系统中不同操作类型的数量
        /// </summary>
        public int TotalOperationTypes { get; set; }

        /// <summary>
        /// 整体成功率（百分比）
        /// 所有操作类型的平均成功率
        /// </summary>
        public double OverallSuccessRate { get; set; }

        /// <summary>
        /// 平均操作持续时间
        /// 所有操作类型的平均耗时
        /// </summary>
        public TimeSpan AverageOperationDuration { get; set; }

        /// <summary>
        /// 最快操作类型
        /// 平均耗时最短的操作类型名称
        /// </summary>
        public string FastestOperation { get; set; } = "";

        /// <summary>
        /// 最慢操作类型
        /// 平均耗时最长的操作类型名称
        /// </summary>
        public string SlowestOperation { get; set; } = "";

        /// <summary>
        /// 最频繁操作类型
        /// 执行次数最多的操作类型名称
        /// </summary>
        public string MostFrequentOperation { get; set; } = "";

        /// <summary>
        /// 摘要生成时间
        /// 此摘要数据的生成时间戳
        /// </summary>
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// 性能报告
    /// 完整的性能分析报告，包含摘要、统计、历史和建议
    /// </summary>
    public class PerformanceReport
    {
        /// <summary>
        /// 性能摘要
        /// 系统整体性能的高级概览
        /// </summary>
        public PerformanceSummary Summary { get; set; } = new();

        /// <summary>
        /// 操作统计信息列表
        /// 每个操作类型的详细统计数据
        /// </summary>
        public List<OperationStatistics> OperationStatistics { get; set; } = new();

        /// <summary>
        /// 最近历史记录
        /// 最近的操作历史数据，用于趋势分析
        /// </summary>
        public List<PerformanceRecord> RecentHistory { get; set; } = new();

        /// <summary>
        /// 优化建议列表
        /// 基于统计数据生成的智能优化建议
        /// </summary>
        public List<string> Recommendations { get; set; } = new();

        /// <summary>
        /// 报告生成时间
        /// 此报告的生成时间戳
        /// </summary>
        public DateTime GeneratedAt { get; set; }
    }

    #endregion
}
