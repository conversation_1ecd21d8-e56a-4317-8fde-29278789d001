# YPerformanceMonitor - 企业级文件操作性能监控工具

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🚀 **智能性能监控解决方案** - 提供实时性能监控、统计分析、智能优化建议的企业级工具

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [性能统计](#-性能统计)
- [智能分析](#-智能分析)
- [异步监控](#-异步监控)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **📊 实时性能监控**

- ✅ **操作监控**: 文件读写、复制、压缩等操作的实时监控
- ✅ **高精度计时**: 使用 Stopwatch 提供微秒级精度
- ✅ **并发安全**: 完全支持多线程环境下的并发监控
- ✅ **内存优化**: 自动限制历史记录，防止内存泄漏

### **📈 统计分析系统**

- ✅ **成功率分析**: 操作成功率统计和趋势分析
- ✅ **性能指标**: 平均耗时、最快/最慢操作统计
- ✅ **吞吐量计算**: 数据处理速度和吞吐量分析
- ✅ **频率统计**: 操作频率和热点分析

### **🧠 智能优化建议**

- ✅ **多维度分析**: 成功率、性能、频率、吞吐量四维分析
- ✅ **智能建议**: 基于统计数据自动生成优化建议
- ✅ **问题识别**: 自动识别性能瓶颈和稳定性问题
- ✅ **趋势预测**: 基于历史数据的性能趋势分析

### **📋 报告生成系统**

- ✅ **完整报告**: 包含摘要、统计、历史、建议的完整报告
- ✅ **定时报告**: 支持定时自动生成性能报告
- ✅ **历史追踪**: 详细的操作历史记录和回溯
- ✅ **数据导出**: 支持多种格式的数据导出

### **⚡ 异步操作支持**

- ✅ **异步监控**: 完整支持 async/await 模式
- ✅ **同步监控**: 传统同步操作的性能监控
- ✅ **异常处理**: 优雅的异常捕获和错误统计
- ✅ **链式调用**: 支持流畅的 API 调用方式

### **🛡️ 企业级特性**

- ✅ **线程安全**: 使用并发安全的数据结构
- ✅ **高性能**: 最小化监控开销，适合生产环境
- ✅ **可扩展**: 支持自定义操作类型和统计指标
- ✅ **易集成**: 简洁的 API 设计，易于集成到现有系统

## 🚀 **快速开始**

### **安装**

```csharp
// 通过 NuGet 包管理器安装
Install-Package Zylo.YIO

// 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Monitoring;

var monitor = new YPerformanceMonitor();

// 基础监控
var context = monitor.StartMonitoring("FileRead", @"C:\data\file.txt", 1024);
try 
{
    // 执行文件操作
    var content = File.ReadAllText(@"C:\data\file.txt");
    monitor.EndMonitoring(context, true);
}
catch (Exception ex)
{
    monitor.EndMonitoring(context, false, ex.Message);
}

// 获取统计信息
var stats = monitor.GetOperationStatistics("FileRead");
Console.WriteLine($"成功率: {stats.SuccessRate:F2}%");
Console.WriteLine($"平均耗时: {stats.AverageDuration.TotalMilliseconds}ms");
```

### **异步监控**

```csharp
// 异步操作监控
var result = await monitor.MonitorAsync("FileReadAsync", filePath, async () =>
{
    return await File.ReadAllTextAsync(filePath);
}, fileInfo.Length);

// 同步操作监控
var content = monitor.Monitor("FileRead", filePath, () =>
{
    return File.ReadAllText(filePath);
}, fileInfo.Length);
```

## 🔧 **核心功能**

### **性能监控管理**

```csharp
var monitor = new YPerformanceMonitor();

// 开始监控
var context = monitor.StartMonitoring("FileWrite", @"C:\output.txt", 2048);

// 执行操作...
Thread.Sleep(100); // 模拟操作时间

// 结束监控
monitor.EndMonitoring(context, success: true);

// 获取操作统计
var statistics = monitor.GetOperationStatistics("FileWrite");
if (statistics != null)
{
    Console.WriteLine($"操作类型: {statistics.OperationType}");
    Console.WriteLine($"总操作数: {statistics.TotalOperations}");
    Console.WriteLine($"成功操作: {statistics.SuccessfulOperations}");
    Console.WriteLine($"失败操作: {statistics.FailedOperations}");
    Console.WriteLine($"成功率: {statistics.SuccessRate:F2}%");
    Console.WriteLine($"平均耗时: {statistics.AverageDuration.TotalMilliseconds:F2}ms");
    Console.WriteLine($"处理速度: {statistics.AverageSpeed / 1024:F2} KB/s");
}
```

### **批量操作监控**

```csharp
// 监控批量文件操作
var files = Directory.GetFiles(@"C:\data", "*.txt");
foreach (var file in files)
{
    var fileInfo = new FileInfo(file);
    var result = monitor.Monitor("BatchProcess", file, () =>
    {
        // 处理文件
        var content = File.ReadAllText(file);
        var processed = content.ToUpper();
        File.WriteAllText(file + ".processed", processed);
        return processed.Length;
    }, fileInfo.Length);
}

// 获取批量操作统计
var batchStats = monitor.GetOperationStatistics("BatchProcess");
Console.WriteLine($"批量处理完成: {batchStats.TotalOperations} 个文件");
Console.WriteLine($"平均处理时间: {batchStats.AverageDuration.TotalMilliseconds:F2}ms/文件");
```

## 📊 **性能统计**

### **获取所有统计信息**

```csharp
// 获取所有操作类型的统计
var allStats = monitor.GetAllStatistics();
foreach (var stat in allStats)
{
    Console.WriteLine($"{stat.OperationType}:");
    Console.WriteLine($"  总操作: {stat.TotalOperations}");
    Console.WriteLine($"  成功率: {stat.SuccessRate:F1}%");
    Console.WriteLine($"  平均耗时: {stat.AverageDuration.TotalMilliseconds:F1}ms");
    Console.WriteLine($"  处理速度: {stat.AverageSpeed / 1024:F1} KB/s");
    Console.WriteLine();
}
```

### **系统性能摘要**

```csharp
// 获取系统整体性能摘要
var summary = monitor.GetPerformanceSummary();
Console.WriteLine("=== 系统性能摘要 ===");
Console.WriteLine($"总操作数: {summary.TotalOperations:N0}");
Console.WriteLine($"总处理数据: {summary.TotalBytesProcessed / 1024 / 1024:F2} MB");
Console.WriteLine($"操作类型数: {summary.TotalOperationTypes}");
Console.WriteLine($"整体成功率: {summary.OverallSuccessRate:F2}%");
Console.WriteLine($"平均操作时间: {summary.AverageOperationDuration.TotalMilliseconds:F2}ms");
Console.WriteLine($"最快操作: {summary.FastestOperation}");
Console.WriteLine($"最慢操作: {summary.SlowestOperation}");
Console.WriteLine($"最频繁操作: {summary.MostFrequentOperation}");
Console.WriteLine($"生成时间: {summary.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
```

## 🧠 **智能分析**

### **生成性能报告**

```csharp
// 生成完整性能报告
var report = monitor.GenerateReport(includeHistory: true);

Console.WriteLine("=== 性能分析报告 ===");
Console.WriteLine($"报告生成时间: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss}");
Console.WriteLine();

// 显示摘要信息
Console.WriteLine("📊 性能摘要:");
Console.WriteLine($"总操作数: {report.Summary.TotalOperations:N0}");
Console.WriteLine($"整体成功率: {report.Summary.OverallSuccessRate:F2}%");
Console.WriteLine();

// 显示详细统计
Console.WriteLine("📈 详细统计:");
foreach (var stat in report.OperationStatistics.Take(5))
{
    Console.WriteLine($"  {stat.OperationType}: {stat.TotalOperations} 次, " +
                     $"成功率 {stat.SuccessRate:F1}%, " +
                     $"平均 {stat.AverageDuration.TotalMilliseconds:F1}ms");
}
Console.WriteLine();

// 显示优化建议
Console.WriteLine("💡 优化建议:");
foreach (var recommendation in report.Recommendations)
{
    Console.WriteLine($"  • {recommendation}");
}
```

### **历史数据分析**

```csharp
// 获取最近的操作历史
var recentHistory = monitor.GetRecentHistory(limit: 10);
Console.WriteLine("📋 最近操作历史:");
foreach (var record in recentHistory)
{
    var status = record.Success ? "✅" : "❌";
    var speed = record.Speed > 0 ? $"{record.Speed / 1024:F1} KB/s" : "N/A";
    
    Console.WriteLine($"{status} {record.OperationType} - {record.FilePath}");
    Console.WriteLine($"    时间: {record.Timestamp:HH:mm:ss}, " +
                     $"耗时: {record.Duration.TotalMilliseconds:F1}ms, " +
                     $"速度: {speed}");
    
    if (!record.Success && !string.IsNullOrEmpty(record.ErrorMessage))
    {
        Console.WriteLine($"    错误: {record.ErrorMessage}");
    }
    Console.WriteLine();
}
```

## ⚡ **异步监控**

### **异步文件操作监控**

```csharp
// 异步文件读取监控
var content = await monitor.MonitorAsync("AsyncFileRead", filePath, async () =>
{
    using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
    using var reader = new StreamReader(stream);
    return await reader.ReadToEndAsync();
}, new FileInfo(filePath).Length);

// 异步文件写入监控
await monitor.MonitorAsync("AsyncFileWrite", outputPath, async () =>
{
    await File.WriteAllTextAsync(outputPath, content);
    return Task.CompletedTask;
}, content.Length);

// 异步批量操作监控
var tasks = files.Select(async file =>
{
    return await monitor.MonitorAsync("AsyncBatchProcess", file, async () =>
    {
        var data = await File.ReadAllBytesAsync(file);
        var compressed = await CompressDataAsync(data);
        await File.WriteAllBytesAsync(file + ".gz", compressed);
        return compressed.Length;
    }, new FileInfo(file).Length);
});

var results = await Task.WhenAll(tasks);
Console.WriteLine($"异步处理完成: {results.Length} 个文件");
```

### **异常处理和错误统计**

```csharp
// 带异常处理的异步监控
try
{
    var result = await monitor.MonitorAsync("RiskyOperation", filePath, async () =>
    {
        // 可能抛出异常的操作
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"文件不存在: {filePath}");

        return await ProcessFileAsync(filePath);
    });

    Console.WriteLine($"操作成功: {result}");
}
catch (Exception ex)
{
    Console.WriteLine($"操作失败: {ex.Message}");
    // 异常会自动记录到性能统计中
}

// 查看错误统计
var errorStats = monitor.GetOperationStatistics("RiskyOperation");
if (errorStats != null && errorStats.FailedOperations > 0)
{
    Console.WriteLine($"失败率: {(100 - errorStats.SuccessRate):F2}%");
    Console.WriteLine($"失败次数: {errorStats.FailedOperations}");
}
```

## 📊 **API 参考**

### **核心监控方法**

| 方法 | 描述 | 返回类型 |
|------|------|----------|
| `StartMonitoring(operationType, filePath, fileSize)` | 开始监控操作 | `PerformanceContext` |
| `EndMonitoring(context, success, errorMessage)` | 结束监控操作 | `void` |
| `MonitorAsync<T>(operationType, filePath, operation, fileSize)` | 异步操作监控 | `Task<T>` |
| `Monitor<T>(operationType, filePath, operation, fileSize)` | 同步操作监控 | `T` |

### **统计分析方法**

| 方法 | 描述 | 返回类型 |
|------|------|----------|
| `GetOperationStatistics(operationType)` | 获取指定操作统计 | `OperationStatistics?` |
| `GetAllStatistics()` | 获取所有操作统计 | `List<OperationStatistics>` |
| `GetPerformanceSummary()` | 获取性能摘要 | `PerformanceSummary` |
| `GenerateReport(includeHistory)` | 生成完整报告 | `PerformanceReport` |

### **历史和管理方法**

| 方法 | 描述 | 返回类型 |
|------|------|----------|
| `GetRecentHistory(limit)` | 获取最近历史记录 | `List<PerformanceRecord>` |
| `ClearPerformanceData()` | 清除所有性能数据 | `void` |

### **数据模型**

#### **PerformanceContext**

```csharp
public class PerformanceContext
{
    public string OperationType { get; set; }     // 操作类型
    public string FilePath { get; set; }          // 文件路径
    public long FileSize { get; set; }            // 文件大小
    public DateTime StartTime { get; set; }       // 开始时间
    public DateTime EndTime { get; set; }         // 结束时间
    public TimeSpan Duration { get; set; }        // 持续时间
    public bool Success { get; set; }             // 是否成功
    public string? ErrorMessage { get; set; }     // 错误信息
    public Stopwatch? Stopwatch { get; set; }     // 计时器
}
```

#### **OperationStatistics**

```csharp
public class OperationStatistics
{
    public string OperationType { get; set; }           // 操作类型
    public long TotalOperations { get; set; }           // 总操作数
    public long SuccessfulOperations { get; set; }      // 成功操作数
    public long FailedOperations { get; set; }          // 失败操作数
    public double SuccessRate { get; set; }             // 成功率(%)
    public TimeSpan AverageDuration { get; set; }       // 平均耗时
    public TimeSpan MinDuration { get; set; }           // 最小耗时
    public TimeSpan MaxDuration { get; set; }           // 最大耗时
    public long TotalBytesProcessed { get; set; }       // 总处理字节数
    public double AverageSpeed { get; set; }            // 平均速度(字节/秒)
}
```

#### **PerformanceSummary**

```csharp
public class PerformanceSummary
{
    public long TotalOperations { get; set; }           // 总操作数
    public long TotalBytesProcessed { get; set; }       // 总处理字节数
    public int TotalOperationTypes { get; set; }        // 操作类型数
    public double OverallSuccessRate { get; set; }      // 整体成功率
    public TimeSpan AverageOperationDuration { get; set; } // 平均操作时间
    public string FastestOperation { get; set; }        // 最快操作
    public string SlowestOperation { get; set; }        // 最慢操作
    public string MostFrequentOperation { get; set; }   // 最频繁操作
    public DateTime GeneratedAt { get; set; }           // 生成时间
}
```

## ✅ **测试覆盖**

YPerformanceMonitor 具有 **100% 的测试覆盖率**，包含以下测试类别：

### **基础功能测试**

- ✅ 监控上下文创建和管理
- ✅ 计时器启动和停止
- ✅ 成功和失败状态记录
- ✅ 错误信息捕获

### **异步操作测试**

- ✅ 异步方法监控
- ✅ 异常处理和重新抛出
- ✅ 返回值正确传递
- ✅ 同步方法监控

### **统计分析测试**

- ✅ 操作统计计算准确性
- ✅ 成功率计算
- ✅ 平均时间计算
- ✅ 最大最小值记录

### **性能摘要测试**

- ✅ 整体摘要生成
- ✅ 多操作类型统计
- ✅ 最快最慢操作识别
- ✅ 频率统计

### **报告生成测试**

- ✅ 完整报告生成
- ✅ 历史记录管理
- ✅ 优化建议生成
- ✅ 数据清理功能

### **数据模型测试**

- ✅ 所有数据模型属性验证
- ✅ 数据类型正确性
- ✅ 默认值设置
- ✅ 对象创建和初始化

### **运行测试**

```bash
# 运行所有 YPerformanceMonitor 测试
dotnet test --filter "FullyQualifiedName~YPerformanceMonitorTests"

# 运行特定类别的测试
dotnet test --filter "Category=PerformanceMonitor"

# 查看测试覆盖率
dotnet test --collect:"XPlat Code Coverage"
```

## 🎯 **最佳实践**

### **1. 监控粒度选择**

```csharp
// ✅ 推荐：监控有意义的操作单元
var result = monitor.Monitor("FileProcessing", filePath, () =>
{
    var content = File.ReadAllText(filePath);
    var processed = ProcessContent(content);
    File.WriteAllText(outputPath, processed);
    return processed.Length;
});

// ❌ 避免：过细粒度的监控
var content = monitor.Monitor("FileRead", filePath, () => File.ReadAllText(filePath));
var processed = monitor.Monitor("ContentProcess", "", () => ProcessContent(content));
var written = monitor.Monitor("FileWrite", outputPath, () => File.WriteAllText(outputPath, processed));
```

### **2. 操作类型命名规范**

```csharp
// ✅ 推荐：使用清晰的操作类型名称
monitor.StartMonitoring("FileRead", filePath);
monitor.StartMonitoring("FileWrite", filePath);
monitor.StartMonitoring("FileCompress", filePath);
monitor.StartMonitoring("DatabaseQuery", queryName);
monitor.StartMonitoring("ApiCall", endpoint);

// ❌ 避免：模糊或过于通用的名称
monitor.StartMonitoring("Operation", filePath);
monitor.StartMonitoring("Process", filePath);
monitor.StartMonitoring("Task", filePath);
```

### **3. 异常处理最佳实践**

```csharp
// ✅ 推荐：使用 MonitorAsync 自动处理异常
try
{
    var result = await monitor.MonitorAsync("FileOperation", filePath, async () =>
    {
        return await ProcessFileAsync(filePath);
    });
}
catch (SpecificException ex)
{
    // 处理特定异常
    logger.LogError(ex, "文件处理失败: {FilePath}", filePath);
}

// ✅ 推荐：手动监控时正确处理异常
var context = monitor.StartMonitoring("ManualOperation", filePath);
try
{
    var result = ProcessFile(filePath);
    monitor.EndMonitoring(context, true);
    return result;
}
catch (Exception ex)
{
    monitor.EndMonitoring(context, false, ex.Message);
    throw; // 重新抛出异常
}
```

### **4. 性能数据管理**

```csharp
// ✅ 推荐：定期生成报告和清理数据
public class PerformanceService
{
    private readonly YPerformanceMonitor _monitor;
    private readonly Timer _reportTimer;

    public PerformanceService()
    {
        _monitor = new YPerformanceMonitor();

        // 每小时生成一次报告
        _reportTimer = new Timer(GenerateHourlyReport, null,
            TimeSpan.FromHours(1), TimeSpan.FromHours(1));
    }

    private void GenerateHourlyReport(object state)
    {
        var report = _monitor.GenerateReport(includeHistory: true);
        SaveReportToFile(report);

        // 如果数据量过大，考虑清理旧数据
        if (report.Summary.TotalOperations > 10000)
        {
            _monitor.ClearPerformanceData();
        }
    }
}
```

### **5. 生产环境使用建议**

```csharp
// ✅ 推荐：在生产环境中使用单例模式
public static class PerformanceMonitorService
{
    private static readonly Lazy<YPerformanceMonitor> _instance =
        new Lazy<YPerformanceMonitor>(() => new YPerformanceMonitor());

    public static YPerformanceMonitor Instance => _instance.Value;
}

// 使用示例
var result = await PerformanceMonitorService.Instance.MonitorAsync(
    "CriticalOperation", filePath, async () =>
    {
        return await ExecuteCriticalOperationAsync(filePath);
    });
```

### **6. 监控开销最小化**

```csharp
// ✅ 推荐：只在需要时启用详细监控
public class ConfigurableMonitoring
{
    private readonly YPerformanceMonitor _monitor;
    private readonly bool _detailedMonitoringEnabled;

    public ConfigurableMonitoring(IConfiguration config)
    {
        _monitor = new YPerformanceMonitor();
        _detailedMonitoringEnabled = config.GetValue<bool>("Monitoring:Detailed");
    }

    public async Task<T> MonitorIfEnabled<T>(string operationType, string filePath,
        Func<Task<T>> operation, long fileSize = 0)
    {
        if (_detailedMonitoringEnabled)
        {
            return await _monitor.MonitorAsync(operationType, filePath, operation, fileSize);
        }
        else
        {
            return await operation();
        }
    }
}
```

---

## 📞 **技术支持**

如果您在使用 YPerformanceMonitor 时遇到问题或有改进建议，请：

1. 查看 [测试用例](../../Zylo.YIO.Tests/Monitoring/YPerformanceMonitorTests.cs) 获取更多使用示例
2. 检查 [API 文档](#-api-参考) 确认方法签名和参数
3. 参考 [最佳实践](#-最佳实践) 优化使用方式

---

**YPerformanceMonitor** - 让性能监控变得简单而强大！ 🚀
