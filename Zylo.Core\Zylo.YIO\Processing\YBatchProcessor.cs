using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using Zylo.YIO.Management;

namespace Zylo.YIO.Processing
{
    /// <summary>
    /// YBatchProcessor - 高性能批量文件处理器
    ///
    /// 核心功能：
    /// - 批量文件操作：复制、移动、删除、压缩、解压
    /// - 并发控制：可配置的最大并发数，基于信号量的线程安全
    /// - 进度监控：实时进度回调和事件通知系统
    /// - 错误处理：独立的任务错误处理，不影响其他任务
    /// - 性能优化：异步处理、内存优化、资源管理
    /// - 取消支持：支持批量操作的优雅取消
    ///
    /// 设计特点：
    /// - 线程安全：使用并发集合和同步原语
    /// - 资源管理：实现IDisposable，正确释放资源
    /// - 事件驱动：提供丰富的事件通知机制
    /// - 可扩展性：支持自定义任务类型和处理逻辑
    ///
    /// 使用示例：
    /// <code>
    /// using var processor = new YBatchProcessor(maxConcurrency: 4);
    /// var filePairs = new[] { new FileCopyPair { SourcePath = "a.txt", TargetPath = "b.txt" } };
    /// var result = await processor.BatchCopyFilesAsync(filePairs);
    /// Console.WriteLine($"处理完成: {result.SuccessfulTasks}/{result.TotalTasks}");
    /// </code>
    /// </summary>
   
    public partial class YBatchProcessor : IDisposable
    {
        #region 私有字段和状态管理

        /// <summary>
        /// 信号量，用于控制最大并发数
        /// </summary>
        private readonly SemaphoreSlim _semaphore;

        /// <summary>
        /// 任务队列，线程安全的并发队列
        /// </summary>
        private readonly ConcurrentQueue<BatchTask> _taskQueue;

        /// <summary>
        /// 任务结果字典，线程安全的并发字典
        /// </summary>
        private readonly ConcurrentDictionary<string, BatchResult> _results;

        /// <summary>
        /// 取消令牌源，用于取消批量操作
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 文件压缩器实例，用于压缩和解压操作
        /// </summary>
        private readonly YFileCompressor _fileCompressor;

        /// <summary>
        /// 最大并发数配置
        /// </summary>
        private int _maxConcurrency;

        /// <summary>
        /// 是否正在处理标志
        /// </summary>
        private bool _isProcessing = false;

        /// <summary>
        /// 是否已释放资源标志
        /// </summary>
        private bool _disposed = false;

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 初始化 YBatchProcessor 实例
        /// </summary>
        /// <param name="maxConcurrency">
        /// 最大并发数。默认值4会自动使用CPU核心数，其他值会使用指定值（最小为1）
        /// </param>
        /// <param name="fileCompressor">
        /// 文件压缩器实例。如果为null，会创建默认的YFileCompressor实例
        /// </param>
        /// <remarks>
        /// 构造函数会初始化所有必要的并发控制组件：
        /// - SemaphoreSlim：控制并发访问
        /// - ConcurrentQueue：任务队列管理
        /// - ConcurrentDictionary：结果存储
        /// - CancellationTokenSource：取消控制
        /// </remarks>
        public YBatchProcessor(int maxConcurrency = 4, YFileCompressor? fileCompressor = null)
        {
            // 智能并发数配置：默认值4表示"自动检测"，使用CPU核心数
            if (maxConcurrency == 4)
            {
                // Math.Max确保至少有1个并发，Environment.ProcessorCount获取CPU核心数
                _maxConcurrency = Math.Max(1, Environment.ProcessorCount);
            }
            else
            {
                // 用户指定了具体值，使用指定值但确保不小于1
                _maxConcurrency = Math.Max(1, maxConcurrency);
            }

            // 初始化信号量：initialCount和maximumCount都设为_maxConcurrency
            // 这样可以同时允许_maxConcurrency个任务并发执行
            _semaphore = new SemaphoreSlim(_maxConcurrency, _maxConcurrency);

            // 初始化线程安全的任务队列，用于存储待处理的任务
            _taskQueue = new ConcurrentQueue<BatchTask>();

            // 初始化线程安全的结果字典，key为任务ID，value为任务结果
            _results = new ConcurrentDictionary<string, BatchResult>();

            // 初始化取消令牌源，用于协调取消所有正在进行的任务
            _cancellationTokenSource = new CancellationTokenSource();

            // 依赖注入模式：如果传入了压缩器则使用，否则创建默认实例
            _fileCompressor = fileCompressor ?? new YFileCompressor();
        }

        #endregion

        #region 事件定义和通知系统

        /// <summary>
        /// 任务开始事件
        /// 当单个批量任务开始执行时触发
        /// </summary>
        /// <remarks>
        /// 事件参数包含：
        /// - Task: 开始执行的任务信息
        ///
        /// 使用示例：
        /// processor.TaskStarted += (sender, e) => Console.WriteLine($"开始处理: {e.Task.SourcePath}");
        /// </remarks>
        public event EventHandler<TaskStartedEventArgs>? TaskStarted;

        /// <summary>
        /// 任务完成事件
        /// 当单个批量任务完成执行时触发（无论成功或失败）
        /// </summary>
        /// <remarks>
        /// 事件参数包含：
        /// - Task: 完成的任务信息
        /// - Result: 任务执行结果（包含成功状态、错误信息等）
        ///
        /// 使用示例：
        /// processor.TaskCompleted += (sender, e) => {
        ///     var status = e.Result.Success ? "成功" : "失败";
        ///     Console.WriteLine($"任务{status}: {e.Task.SourcePath}");
        /// };
        /// </remarks>
        public event EventHandler<TaskCompletedEventArgs>? TaskCompleted;

        /// <summary>
        /// 进度更新事件
        /// 当批量处理进度发生变化时触发
        /// </summary>
        /// <remarks>
        /// 事件参数包含：
        /// - TotalTasks: 总任务数
        /// - CompletedTasks: 已完成任务数
        /// - SuccessfulTasks: 成功任务数
        /// - FailedTasks: 失败任务数
        /// - ProgressPercentage: 进度百分比
        /// - CurrentTask: 当前处理的任务
        ///
        /// 使用示例：
        /// processor.ProgressUpdated += (sender, e) => {
        ///     Console.WriteLine($"进度: {e.ProgressPercentage:F1}% ({e.CompletedTasks}/{e.TotalTasks})");
        /// };
        /// </remarks>
        public event EventHandler<ProgressUpdateEventArgs>? ProgressUpdated;

        /// <summary>
        /// 批处理完成事件
        /// 当整个批量处理操作完成时触发
        /// </summary>
        /// <remarks>
        /// 事件参数包含：
        /// - Result: 完整的批处理结果统计
        ///
        /// 使用示例：
        /// processor.BatchCompleted += (sender, e) => {
        ///     Console.WriteLine($"批处理完成: {e.Result.SuccessfulTasks}/{e.Result.TotalTasks} 成功");
        ///     Console.WriteLine($"总耗时: {e.Result.Duration.TotalMilliseconds} ms");
        /// };
        /// </remarks>
        public event EventHandler<BatchCompletedEventArgs>? BatchCompleted;

        #endregion

        #region 批量文件操作方法

        /// <summary>
        /// 批量复制文件
        /// 高效地将多个文件从源位置复制到目标位置
        /// </summary>
        /// <param name="filePairs">
        /// 文件复制对列表，每个对象包含源路径和目标路径
        /// </param>
        /// <param name="overwrite">
        /// 是否覆盖已存在的目标文件。默认为false，如果目标文件存在则跳过
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 批处理结果，包含成功/失败统计、耗时、详细结果等信息
        /// </returns>
        /// <remarks>
        /// 特性：
        /// - 并发执行：根据配置的最大并发数并行处理
        /// - 错误隔离：单个文件复制失败不影响其他文件
        /// - 进度监控：提供实时进度回调和事件通知
        /// - 性能优化：使用异步I/O和内存优化
        ///
        /// 使用示例：
        /// <code>
        /// var filePairs = new[] {
        ///     new FileCopyPair { SourcePath = "source1.txt", TargetPath = "target1.txt" },
        ///     new FileCopyPair { SourcePath = "source2.txt", TargetPath = "target2.txt" }
        /// };
        ///
        /// var progress = new Progress&lt;BatchProgress&gt;(p =>
        ///     Console.WriteLine($"进度: {p.ProgressPercentage:F1}%"));
        ///
        /// var result = await processor.BatchCopyFilesAsync(filePairs, overwrite: true, progress);
        /// Console.WriteLine($"复制完成: {result.SuccessfulTasks}/{result.TotalTasks}");
        /// </code>
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当filePairs为null时抛出</exception>
        public async Task<BatchProcessResult> BatchCopyFilesAsync(
            IEnumerable<FileCopyPair> filePairs,
            bool overwrite = false,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 将文件复制对转换为批量任务
            var tasks = filePairs.Select(pair => new BatchTask
            {
                Id = Guid.NewGuid().ToString(),
                Type = BatchTaskType.CopyFile,
                SourcePath = pair.SourcePath,
                TargetPath = pair.TargetPath,
                Parameters = new Dictionary<string, object> { { "overwrite", overwrite } }
            }).ToList();

            // 执行批量处理
            return await ProcessBatchAsync(tasks, progressCallback);
        }

        /// <summary>
        /// 批量移动文件
        /// 高效地将多个文件从源位置移动到目标位置（相当于剪切操作）
        /// </summary>
        /// <param name="filePairs">
        /// 文件移动对列表，每个对象包含源路径和目标路径
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 批处理结果，包含成功/失败统计、耗时、详细结果等信息
        /// </returns>
        /// <remarks>
        /// 移动操作特点：
        /// - 原子性：移动操作是原子的，要么成功要么失败
        /// - 跨驱动器：支持跨驱动器移动（内部会先复制再删除）
        /// - 目录创建：自动创建目标目录（如果不存在）
        /// - 错误处理：移动失败时源文件保持不变
        ///
        /// 性能说明：
        /// - 同驱动器：非常快速的重命名操作
        /// - 跨驱动器：需要复制+删除，速度较慢
        ///
        /// 使用示例：
        /// <code>
        /// var filePairs = new[] {
        ///     new FileCopyPair { SourcePath = "temp/file1.txt", TargetPath = "archive/file1.txt" },
        ///     new FileCopyPair { SourcePath = "temp/file2.txt", TargetPath = "archive/file2.txt" }
        /// };
        ///
        /// var result = await processor.BatchMoveFilesAsync(filePairs);
        /// Console.WriteLine($"移动完成: {result.SuccessfulTasks} 个文件");
        /// </code>
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当filePairs为null时抛出</exception>
        public async Task<BatchProcessResult> BatchMoveFilesAsync(
            IEnumerable<FileCopyPair> filePairs,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 将文件移动对转换为批量任务
            var tasks = filePairs.Select(pair => new BatchTask
            {
                Id = Guid.NewGuid().ToString(),
                Type = BatchTaskType.MoveFile,
                SourcePath = pair.SourcePath,
                TargetPath = pair.TargetPath
            }).ToList();

            // 执行批量处理
            return await ProcessBatchAsync(tasks, progressCallback);
        }

        /// <summary>
        /// 批量删除文件
        /// 高效地删除多个文件，支持普通删除和安全删除两种模式
        /// </summary>
        /// <param name="filePaths">
        /// 要删除的文件路径列表
        /// </param>
        /// <param name="secureDelete">
        /// 是否使用安全删除模式。true=多次覆写后删除，false=直接删除
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 批处理结果，包含成功/失败统计、耗时、详细结果等信息
        /// </returns>
        /// <remarks>
        /// 删除模式说明：
        /// - 普通删除：直接调用File.Delete，速度快
        /// - 安全删除：多次随机覆写文件内容后删除，防止数据恢复，速度较慢
        ///
        /// 安全特性：
        /// - 权限检查：删除前检查文件访问权限
        /// - 只读处理：自动移除只读属性后删除
        /// - 错误隔离：单个文件删除失败不影响其他文件
        ///
        /// 使用示例：
        /// <code>
        /// var filesToDelete = new[] { "temp1.txt", "temp2.txt", "temp3.txt" };
        ///
        /// // 普通删除
        /// var result1 = await processor.BatchDeleteFilesAsync(filesToDelete, secureDelete: false);
        ///
        /// // 安全删除（敏感文件）
        /// var sensitiveFiles = new[] { "password.txt", "private_key.pem" };
        /// var result2 = await processor.BatchDeleteFilesAsync(sensitiveFiles, secureDelete: true);
        /// </code>
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当filePaths为null时抛出</exception>
        public async Task<BatchProcessResult> BatchDeleteFilesAsync(
            IEnumerable<string> filePaths,
            bool secureDelete = false,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 将文件路径转换为批量任务
            var tasks = filePaths.Select(path => new BatchTask
            {
                Id = Guid.NewGuid().ToString(),
                Type = BatchTaskType.DeleteFile,
                SourcePath = path,
                Parameters = new Dictionary<string, object> { { "secureDelete", secureDelete } }
            }).ToList();

            // 执行批量处理
            return await ProcessBatchAsync(tasks, progressCallback);
        }

        /// <summary>
        /// 批量压缩文件
        /// 将多个文件或目录压缩为ZIP格式，支持不同压缩级别和选项
        /// </summary>
        /// <param name="compressionTasks">
        /// 压缩任务列表，每个任务包含源路径、目标ZIP路径和压缩选项
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 批处理结果，包含成功/失败统计、耗时、详细结果等信息
        /// </returns>
        /// <remarks>
        /// 压缩特性：
        /// - 多种压缩级别：Optimal（最优）、Fastest（最快）、NoCompression（不压缩）
        /// - 目录支持：可以压缩整个目录结构
        /// - 子目录控制：可选择是否包含子目录
        /// - 格式支持：生成标准ZIP格式文件
        ///
        /// 性能说明：
        /// - Optimal：压缩率最高，速度较慢
        /// - Fastest：速度最快，压缩率较低
        /// - NoCompression：仅打包，不压缩
        ///
        /// 使用示例：
        /// <code>
        /// var compressionTasks = new[] {
        ///     new CompressionTask {
        ///         SourcePath = "documents/",
        ///         TargetPath = "backup/documents.zip",
        ///         CompressionLevel = CompressionLevel.Optimal,
        ///         IncludeSubdirectories = true
        ///     },
        ///     new CompressionTask {
        ///         SourcePath = "report.pdf",
        ///         TargetPath = "archive/report.zip",
        ///         CompressionLevel = CompressionLevel.Fastest
        ///     }
        /// };
        ///
        /// var result = await processor.BatchCompressFilesAsync(compressionTasks);
        /// Console.WriteLine($"压缩完成: {result.SuccessfulTasks} 个文件");
        /// </code>
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当compressionTasks为null时抛出</exception>
        public async Task<BatchProcessResult> BatchCompressFilesAsync(
            IEnumerable<CompressionTask> compressionTasks,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 将压缩任务转换为批量任务
            var tasks = compressionTasks.Select(task => new BatchTask
            {
                Id = Guid.NewGuid().ToString(),
                Type = BatchTaskType.CompressFile,
                SourcePath = task.SourcePath,
                TargetPath = task.TargetPath,
                Parameters = new Dictionary<string, object>
                {
                    { "compressionLevel", task.CompressionLevel },
                    { "includeSubdirectories", task.IncludeSubdirectories }
                }
            }).ToList();

            // 执行批量处理
            return await ProcessBatchAsync(tasks, progressCallback);
        }

        /// <summary>
        /// 批量解压文件
        /// 将多个ZIP文件解压到指定目录，支持覆盖控制和目录结构保持
        /// </summary>
        /// <param name="decompressionTasks">
        /// 解压任务列表，每个任务包含ZIP文件路径、解压目标路径和解压选项
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 批处理结果，包含成功/失败统计、耗时、详细结果等信息
        /// </returns>
        /// <remarks>
        /// 解压特性：
        /// - 目录结构：完整保持ZIP内的目录结构
        /// - 覆盖控制：可选择是否覆盖已存在的文件
        /// - 路径安全：防止ZIP炸弹和路径遍历攻击
        /// - 编码支持：自动处理文件名编码问题
        ///
        /// 安全特性：
        /// - 路径验证：防止解压到系统目录
        /// - 大小限制：防止ZIP炸弹攻击
        /// - 权限检查：确保有足够的磁盘空间和写权限
        ///
        /// 使用示例：
        /// <code>
        /// var decompressionTasks = new[] {
        ///     new DecompressionTask {
        ///         ZipFilePath = "backup/documents.zip",
        ///         ExtractPath = "restored/documents/",
        ///         Overwrite = true
        ///     },
        ///     new DecompressionTask {
        ///         ZipFilePath = "archive/report.zip",
        ///         ExtractPath = "temp/reports/",
        ///         Overwrite = false
        ///     }
        /// };
        ///
        /// var result = await processor.BatchDecompressFilesAsync(decompressionTasks);
        /// Console.WriteLine($"解压完成: {result.SuccessfulTasks} 个文件");
        /// </code>
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当decompressionTasks为null时抛出</exception>
        public async Task<BatchProcessResult> BatchDecompressFilesAsync(
            IEnumerable<DecompressionTask> decompressionTasks,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 将解压任务转换为批量任务
            var tasks = decompressionTasks.Select(task => new BatchTask
            {
                Id = Guid.NewGuid().ToString(),
                Type = BatchTaskType.DecompressFile,
                SourcePath = task.ZipFilePath,
                TargetPath = task.ExtractPath,
                Parameters = new Dictionary<string, object>
                {
                    { "overwrite", task.Overwrite }
                }
            }).ToList();

            // 执行批量处理
            return await ProcessBatchAsync(tasks, progressCallback);
        }

        #endregion

        #region 核心处理引擎

        /// <summary>
        /// 自定义批量处理核心引擎
        /// 这是所有批量操作的核心方法，负责任务调度、并发控制、进度监控和结果收集
        /// </summary>
        /// <param name="tasks">
        /// 要处理的批量任务列表，每个任务包含类型、路径、参数等信息
        /// </param>
        /// <param name="progressCallback">
        /// 进度回调接口，用于接收实时进度更新。可以为null
        /// </param>
        /// <returns>
        /// 完整的批处理结果，包含所有统计信息和详细结果
        /// </returns>
        /// <remarks>
        /// 核心处理流程：
        /// 1. 参数验证和状态检查
        /// 2. 初始化处理状态和统计信息
        /// 3. 创建并发任务队列
        /// 4. 使用信号量控制并发数
        /// 5. 异步执行所有任务
        /// 6. 收集结果和统计信息
        /// 7. 触发完成事件
        ///
        /// 并发控制：
        /// - 使用SemaphoreSlim限制最大并发数
        /// - 每个任务独立执行，互不影响
        /// - 支持取消操作和超时控制
        ///
        /// 错误处理：
        /// - 单个任务失败不影响其他任务
        /// - 详细记录每个任务的执行结果
        /// - 提供完整的错误信息和堆栈跟踪
        ///
        /// 性能优化：
        /// - 异步I/O操作，避免线程阻塞
        /// - 内存高效的结果收集
        /// - 实时进度更新，减少UI冻结
        /// </remarks>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        /// <exception cref="ArgumentNullException">当tasks为null时抛出</exception>
        public async Task<BatchProcessResult> ProcessBatchAsync(
            IEnumerable<BatchTask> tasks,
            IProgress<BatchProgress>? progressCallback = null)
        {
            // 检查对象是否已被释放
            ThrowIfDisposed();

            // 转换为列表以便多次枚举
            var taskList = tasks.ToList();
            if (!taskList.Any())
            {
                return new BatchProcessResult
                {
                    TotalTasks = 0,
                    CompletedTasks = 0,
                    SuccessfulTasks = 0,
                    FailedTasks = 0,
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now,
                    Duration = TimeSpan.Zero
                };
            }

            _isProcessing = true;
            var startTime = DateTime.Now;
            var completedTasks = 0;
            var successfulTasks = 0;
            var failedTasks = 0;

            try
            {
                // 清空之前的结果
                _results.Clear();

                // 为每个任务创建异步处理函数，使用LINQ的Select转换为Task集合
                var concurrentTasks = taskList.Select(async task =>
                {
                    // 等待信号量许可，限制并发数量。如果取消令牌被触发会抛出OperationCanceledException
                    await _semaphore.WaitAsync(_cancellationTokenSource.Token);
                    try
                    {
                        // 触发任务开始事件，通知外部监听器
                        OnTaskStarted(new TaskStartedEventArgs { Task = task });

                        // 执行单个任务的具体处理逻辑
                        var result = await ProcessSingleTaskAsync(task);

                        // 线程安全地将结果添加到结果字典中，TryAdd在key已存在时返回false但不抛异常
                        _results.TryAdd(task.Id, result);

                        // 使用Interlocked.Increment确保多线程环境下的原子递增操作
                        var completed = Interlocked.Increment(ref completedTasks);
                        if (result.Success)
                        {
                            // 原子递增成功任务计数器
                            Interlocked.Increment(ref successfulTasks);
                        }
                        else
                        {
                            // 原子递增失败任务计数器
                            Interlocked.Increment(ref failedTasks);
                        }

                        // 构建当前进度信息对象
                        var progress = new BatchProgress
                        {
                            TotalTasks = taskList.Count,
                            CompletedTasks = completed,
                            SuccessfulTasks = successfulTasks,
                            FailedTasks = failedTasks,
                            CurrentTask = task,
                            // 计算百分比进度，转换为double避免整数除法
                            ProgressPercentage = (double)completed / taskList.Count * 100
                        };

                        // 如果提供了进度回调，则报告当前进度（?. 空条件运算符）
                        progressCallback?.Report(progress);

                        // 触发进度更新事件
                        OnProgressUpdated(new ProgressUpdateEventArgs { Progress = progress });

                        // 触发任务完成事件
                        OnTaskCompleted(new TaskCompletedEventArgs { Task = task, Result = result });

                        return result;
                    }
                    finally
                    {
                        // 无论任务成功还是失败，都要释放信号量许可，确保其他等待的任务可以继续
                        _semaphore.Release();
                    }
                });

                // Task.WhenAll等待所有并发任务完成，返回所有任务的结果数组
                var results = await Task.WhenAll(concurrentTasks);

                // 记录批处理结束时间
                var endTime = DateTime.Now;

                // 构建完整的批处理结果对象
                var batchResult = new BatchProcessResult
                {
                    TotalTasks = taskList.Count,
                    CompletedTasks = completedTasks,
                    SuccessfulTasks = successfulTasks,
                    FailedTasks = failedTasks,
                    StartTime = startTime,
                    EndTime = endTime,
                    // 计算总耗时
                    Duration = endTime - startTime,
                    // 将Task数组转换为List，便于后续操作
                    TaskResults = results.ToList(),
                    // 计算成功率：防止除零错误，使用三元运算符检查
                    SuccessRate = taskList.Count > 0 ? (double)successfulTasks / taskList.Count * 100 : 0
                };

                // 触发批处理完成事件，通知外部监听器
                OnBatchCompleted(new BatchCompletedEventArgs { Result = batchResult });
                return batchResult;
            }
            catch (OperationCanceledException)
            {
                // 捕获取消操作异常，返回部分完成的结果
                // 这种情况下不抛出异常，而是返回包含已完成任务的结果
                return new BatchProcessResult
                {
                    TotalTasks = taskList.Count,
                    CompletedTasks = completedTasks,
                    SuccessfulTasks = successfulTasks,
                    FailedTasks = failedTasks,
                    StartTime = startTime,
                    // 使用当前时间作为结束时间
                    EndTime = DateTime.Now,
                    // 计算到取消时为止的耗时
                    Duration = DateTime.Now - startTime,
                    // 标记为已取消状态
                    IsCancelled = true
                };
            }
            finally
            {
                // 无论成功、失败还是取消，都要重置处理状态标志
                _isProcessing = false;
            }
        }

        #endregion

        #region 任务控制和状态管理

        /// <summary>
        /// 取消当前批处理操作
        /// 发送取消信号给所有正在执行和等待执行的任务
        /// </summary>
        /// <remarks>
        /// 取消特性：
        /// - 优雅取消：正在执行的任务会在安全点停止
        /// - 立即响应：等待中的任务会立即取消
        /// - 状态保持：已完成的任务结果会保留
        /// - 线程安全：可以从任何线程安全调用
        ///
        /// 注意事项：
        /// - 取消是异步的，可能需要一些时间才能完全停止
        /// - 某些I/O操作可能无法立即取消
        /// - 取消后的批处理结果仍然有效，包含已完成的任务
        ///
        /// 使用示例：
        /// <code>
        /// // 启动长时间运行的批处理
        /// var processingTask = processor.ProcessBatchAsync(largeBatch);
        ///
        /// // 在另一个线程或UI事件中取消
        /// processor.CancelBatch();
        ///
        /// // 等待处理完成（会返回部分结果）
        /// var result = await processingTask;
        /// Console.WriteLine($"取消前完成: {result.CompletedTasks}/{result.TotalTasks}");
        /// </code>
        /// </remarks>
        public void CancelBatch()
        {
            _cancellationTokenSource.Cancel();
        }

        /// <summary>
        /// 检查是否正在处理批量任务
        /// </summary>
        /// <returns>
        /// 如果当前有批量任务正在执行返回true，否则返回false
        /// </returns>
        /// <remarks>
        /// 状态说明：
        /// - true：有批量任务正在执行中
        /// - false：当前空闲，可以开始新的批量任务
        ///
        /// 线程安全：此方法是线程安全的，可以从任何线程调用
        ///
        /// 使用场景：
        /// - UI状态更新：显示处理状态指示器
        /// - 任务调度：避免重复启动批量任务
        /// - 资源管理：确定是否可以安全释放资源
        /// </remarks>
        public bool IsProcessing()
        {
            return _isProcessing;
        }

        /// <summary>
        /// 设置最大并发数
        /// 动态调整批量处理的并发级别
        /// </summary>
        /// <param name="maxConcurrency">
        /// 新的最大并发数，必须大于0。如果小于1会自动调整为1
        /// </param>
        /// <remarks>
        /// 并发控制说明：
        /// - 立即生效：设置后立即影响新的批量任务
        /// - 不影响当前：不会影响正在执行的批量任务
        /// - 最小值限制：最小值为1，确保至少有一个并发任务
        /// - 性能影响：过高的并发数可能导致资源竞争，过低则影响效率
        ///
        /// 推荐设置：
        /// - CPU密集型任务：CPU核心数
        /// - I/O密集型任务：CPU核心数的2-4倍
        /// - 网络任务：根据网络带宽和延迟调整
        ///
        /// 使用示例：
        /// <code>
        /// // 根据系统负载动态调整
        /// if (systemLoad > 80)
        ///     processor.SetMaxConcurrency(2);  // 降低并发
        /// else
        ///     processor.SetMaxConcurrency(Environment.ProcessorCount);  // 恢复默认
        /// </code>
        /// </remarks>
        public void SetMaxConcurrency(int maxConcurrency)
        {
            _maxConcurrency = Math.Max(1, maxConcurrency);
        }

        /// <summary>
        /// 获取当前配置的最大并发数
        /// </summary>
        /// <returns>
        /// 当前配置的最大并发数
        /// </returns>
        /// <remarks>
        /// 返回值说明：
        /// - 返回通过构造函数或SetMaxConcurrency设置的并发数
        /// - 最小值为1
        /// - 默认值为CPU核心数（当构造函数参数为4时）
        ///
        /// 使用场景：
        /// - 性能监控：了解当前并发配置
        /// - 动态调整：根据当前配置进行优化
        /// - 调试诊断：排查性能问题
        /// </remarks>
        public int GetMaxConcurrency()
        {
            return _maxConcurrency;
        }

        #endregion

        #region 私有方法和任务处理

        /// <summary>
        /// 处理单个批量任务
        /// 这是每个任务的执行入口点，负责任务分发和结果收集
        /// </summary>
        /// <param name="task">要处理的批量任务</param>
        /// <returns>任务执行结果</returns>
        /// <remarks>
        /// 处理流程：
        /// 1. 创建结果对象并记录开始时间
        /// 2. 根据任务类型分发到具体处理方法
        /// 3. 处理任务执行过程中的异常
        /// 4. 记录结束时间和执行结果
        /// 5. 计算任务执行耗时
        ///
        /// 支持的任务类型：
        /// - CopyFile：文件复制
        /// - MoveFile：文件移动
        /// - DeleteFile：文件删除
        /// - CompressFile：文件压缩
        /// - DecompressFile：文件解压
        ///
        /// 错误处理：
        /// - 捕获所有异常并记录到结果中
        /// - 不会因为单个任务失败而影响其他任务
        /// - 提供详细的错误信息和堆栈跟踪
        /// </remarks>
        private async Task<BatchResult> ProcessSingleTaskAsync(BatchTask task)
        {
            // 初始化任务结果对象
            var result = new BatchResult
            {
                TaskId = task.Id,
                TaskType = task.Type,
                SourcePath = task.SourcePath,
                TargetPath = task.TargetPath,
                StartTime = DateTime.Now
            };

            try
            {
                // 使用switch表达式根据任务类型分发到对应的处理方法
                // 每个case都是异步调用，await确保等待完成
                switch (task.Type)
                {
                    case BatchTaskType.CopyFile:
                        // 处理文件复制任务，包含覆盖选项处理
                        await ProcessCopyFileTask(task, result);
                        break;
                    case BatchTaskType.MoveFile:
                        // 处理文件移动任务，支持跨驱动器移动
                        await ProcessMoveFileTask(task, result);
                        break;
                    case BatchTaskType.DeleteFile:
                        // 处理文件删除任务，支持安全删除选项
                        await ProcessDeleteFileTask(task, result);
                        break;
                    case BatchTaskType.CompressFile:
                        // 处理文件压缩任务，支持不同压缩级别
                        await ProcessCompressFileTask(task, result);
                        break;
                    case BatchTaskType.DecompressFile:
                        // 处理文件解压任务，支持覆盖控制
                        await ProcessDecompressFileTask(task, result);
                        break;
                    default:
                        // 遇到未知任务类型时抛出异常，这会被下面的catch捕获
                        throw new NotSupportedException($"不支持的任务类型: {task.Type}");
                }

                // 如果执行到这里说明任务成功完成，没有抛出异常
                result.Success = true;
            }
            catch (Exception ex)
            {
                // 捕获任务执行过程中的所有异常
                result.Success = false;
                // 记录异常消息，用于错误诊断和用户反馈
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                // 无论成功还是失败都要记录结束时间和计算耗时
                result.EndTime = DateTime.Now;
                // 计算任务执行的总耗时
                result.Duration = result.EndTime - result.StartTime;
            }

            return result;
        }

        private async Task ProcessCopyFileTask(BatchTask task, BatchResult result)
        {
            // 从任务参数中提取覆盖选项，使用空条件运算符和类型转换
            // 如果Parameters为null或不包含overwrite键，则默认为false
            var overwrite = task.Parameters?.ContainsKey("overwrite") == true && (bool)task.Parameters["overwrite"];

            // 验证源文件是否存在，不存在则抛出FileNotFoundException
            if (!File.Exists(task.SourcePath))
            {
                throw new FileNotFoundException($"源文件不存在: {task.SourcePath}");
            }

            // 检查目标文件是否已存在，如果存在且不允许覆盖则抛出异常
            if (File.Exists(task.TargetPath) && !overwrite)
            {
                throw new InvalidOperationException($"目标文件已存在: {task.TargetPath}");
            }

            // 确保目标目录存在，Path.GetDirectoryName获取目录路径
            var targetDir = Path.GetDirectoryName(task.TargetPath);
            if (!string.IsNullOrEmpty(targetDir))
            {
                // Directory.CreateDirectory是幂等的，如果目录已存在不会抛出异常
                Directory.CreateDirectory(targetDir);
            }

            // 使用异步文件流进行文件复制，using语句确保资源正确释放
            using var sourceStream = new FileStream(task.SourcePath, FileMode.Open, FileAccess.Read);
            using var targetStream = new FileStream(task.TargetPath, FileMode.Create, FileAccess.Write);

            // CopyToAsync异步复制流内容，避免阻塞线程
            await sourceStream.CopyToAsync(targetStream);

            // 记录处理的字节数，用于性能统计
            result.BytesProcessed = sourceStream.Length;
        }

        private async Task ProcessMoveFileTask(BatchTask task, BatchResult result)
        {
            // 验证源文件是否存在
            if (!File.Exists(task.SourcePath))
            {
                throw new FileNotFoundException($"源文件不存在: {task.SourcePath}");
            }

            // 确保目标目录存在，移动操作需要目标目录预先存在
            var targetDir = Path.GetDirectoryName(task.TargetPath);
            if (!string.IsNullOrEmpty(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }

            // 获取文件信息以记录处理的字节数
            var fileInfo = new FileInfo(task.SourcePath);
            result.BytesProcessed = fileInfo.Length;

            // 执行文件移动操作
            // File.Move是原子操作，在同一驱动器上是重命名，跨驱动器是复制+删除
            File.Move(task.SourcePath, task.TargetPath);

            // 返回已完成的Task以保持异步方法签名的一致性
            // 虽然File.Move是同步的，但这样可以保持接口统一
            await Task.CompletedTask;
        }

        private async Task ProcessDeleteFileTask(BatchTask task, BatchResult result)
        {
            // 检查文件是否存在
            if (!File.Exists(task.SourcePath))
            {
                // 文件不存在时认为删除操作成功（幂等性）
                // 直接返回，不抛出异常
                return;
            }

            // 获取文件大小信息，用于统计处理的字节数
            var fileInfo = new FileInfo(task.SourcePath);
            result.BytesProcessed = fileInfo.Length;

            // 从任务参数中提取安全删除选项
            // 安全删除会多次覆写文件内容后再删除，防止数据恢复
            var secureDelete = task.Parameters?.ContainsKey("secureDelete") == true && (bool)task.Parameters["secureDelete"];

            if (secureDelete)
            {
                // 执行安全删除：多次随机覆写文件内容后删除
                // 这样可以防止通过磁盘恢复工具恢复文件内容
                await SecureDeleteFileAsync(task.SourcePath);
            }
            else
            {
                // 执行普通删除：直接调用系统API删除文件
                // 速度快但文件内容可能被恢复
                File.Delete(task.SourcePath);
            }
        }

        private async Task ProcessCompressFileTask(BatchTask task, BatchResult result)
        {
            // 从任务参数中提取压缩级别，使用三元运算符提供默认值
            // 如果参数不存在或类型不匹配，使用Optimal作为默认值
            var compressionLevel = task.Parameters?.ContainsKey("compressionLevel") == true
                ? (CompressionLevel)task.Parameters["compressionLevel"]
                : CompressionLevel.Optimal;

            // 提取是否包含子目录的选项，使用逻辑与运算符确保类型安全
            var includeSubdirectories = task.Parameters?.ContainsKey("includeSubdirectories") == true
                && (bool)task.Parameters["includeSubdirectories"];

            // 声明压缩结果变量，稍后根据源路径类型进行不同的处理
            YFileCompressor.CompressionResult compressionResult;

            // 判断源路径是文件还是目录，采用不同的压缩策略
            if (File.Exists(task.SourcePath))
            {
                // 源路径是文件：压缩单个文件到ZIP
                compressionResult = await _fileCompressor.CompressFileAsync(
                    task.SourcePath,
                    task.TargetPath,
                    compressionLevel);
            }
            else if (Directory.Exists(task.SourcePath))
            {
                // 压缩目录
                compressionResult = await _fileCompressor.CompressDirectoryAsync(
                    task.SourcePath,
                    task.TargetPath,
                    compressionLevel,
                    includeSubdirectories);
            }
            else
            {
                throw new FileNotFoundException($"源路径不存在: {task.SourcePath}");
            }

            if (!compressionResult.Success)
            {
                throw new InvalidOperationException($"压缩失败: {compressionResult.ErrorMessage}");
            }

            result.BytesProcessed = compressionResult.OriginalSize;
        }

        private async Task ProcessDecompressFileTask(BatchTask task, BatchResult result)
        {
            var overwrite = task.Parameters?.ContainsKey("overwrite") == true && (bool)task.Parameters["overwrite"];

            if (!File.Exists(task.SourcePath))
            {
                throw new FileNotFoundException($"ZIP文件不存在: {task.SourcePath}");
            }

            // 解压ZIP文件
            var decompressionResult = await _fileCompressor.ExtractZipAsync(
                task.SourcePath,
                task.TargetPath,
                overwrite);

            if (!decompressionResult.Success)
            {
                throw new InvalidOperationException($"解压失败: {decompressionResult.ErrorMessage}");
            }

            result.BytesProcessed = decompressionResult.CompressedSize;
        }

        private async Task SecureDeleteFileAsync(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var fileSize = fileInfo.Length;

            // 多次覆写文件内容
            var random = new Random();
            var buffer = new byte[4096];

            for (int pass = 0; pass < 3; pass++)
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Write);
                stream.Seek(0, SeekOrigin.Begin);

                for (long written = 0; written < fileSize; written += buffer.Length)
                {
                    var bytesToWrite = (int)Math.Min(buffer.Length, fileSize - written);
                    random.NextBytes(buffer);
                    await stream.WriteAsync(buffer, 0, bytesToWrite);
                }

                await stream.FlushAsync();
            }

            // 最后删除文件
            File.Delete(filePath);
        }

        #endregion

        #region 事件触发方法

        /// <summary>
        /// 触发任务开始事件
        /// 当单个批量任务开始执行时调用此方法
        /// </summary>
        /// <param name="e">任务开始事件参数，包含任务信息</param>
        /// <remarks>
        /// 事件触发时机：
        /// - 任务获得执行权限（通过信号量）后立即触发
        /// - 在实际任务处理开始之前触发
        /// - 每个任务都会触发一次此事件
        ///
        /// 线程安全：
        /// - 此方法在任务执行线程中调用
        /// - 事件处理程序应该是线程安全的
        /// - 避免在事件处理程序中执行耗时操作
        ///
        /// 继承说明：
        /// - 子类可以重写此方法来自定义事件触发逻辑
        /// - 重写时应该调用基类方法以确保事件正常触发
        /// </remarks>
        protected virtual void OnTaskStarted(TaskStartedEventArgs e)
        {
            TaskStarted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发任务完成事件
        /// 当单个批量任务完成执行时调用此方法（无论成功或失败）
        /// </summary>
        /// <param name="e">任务完成事件参数，包含任务信息和执行结果</param>
        /// <remarks>
        /// 事件触发时机：
        /// - 任务执行完成后立即触发（成功或失败都会触发）
        /// - 在释放信号量之前触发
        /// - 包含完整的任务执行结果信息
        ///
        /// 结果信息：
        /// - 任务执行状态（成功/失败）
        /// - 执行耗时
        /// - 处理的字节数
        /// - 错误信息（如果失败）
        ///
        /// 线程安全：
        /// - 此方法在任务执行线程中调用
        /// - 事件处理程序应该是线程安全的
        /// - 避免在事件处理程序中执行耗时操作
        /// </remarks>
        protected virtual void OnTaskCompleted(TaskCompletedEventArgs e)
        {
            TaskCompleted?.Invoke(this, e);
        }

        /// <summary>
        /// 触发进度更新事件
        /// 当批量处理进度发生变化时调用此方法
        /// </summary>
        /// <param name="e">进度更新事件参数，包含当前进度信息</param>
        /// <remarks>
        /// 事件触发时机：
        /// - 每个任务完成后触发
        /// - 包含实时的进度统计信息
        /// - 提供百分比和绝对数量两种进度表示
        ///
        /// 进度信息：
        /// - 总任务数和已完成任务数
        /// - 成功任务数和失败任务数
        /// - 进度百分比
        /// - 当前正在处理的任务信息
        ///
        /// 性能考虑：
        /// - 事件处理程序应该尽可能轻量
        /// - 避免在事件处理程序中执行耗时的UI更新
        /// - 考虑使用防抖动机制避免过于频繁的更新
        /// </remarks>
        protected virtual void OnProgressUpdated(ProgressUpdateEventArgs e)
        {
            ProgressUpdated?.Invoke(this, e);
        }

        /// <summary>
        /// 触发批处理完成事件
        /// 当整个批量处理操作完成时调用此方法
        /// </summary>
        /// <param name="e">批处理完成事件参数，包含完整的处理结果</param>
        /// <remarks>
        /// 事件触发时机：
        /// - 所有任务执行完成后触发
        /// - 包含完整的批处理统计结果
        /// - 无论成功、失败或取消都会触发
        ///
        /// 结果信息：
        /// - 完整的任务执行统计
        /// - 总耗时和性能指标
        /// - 成功率和错误汇总
        /// - 处理的总字节数
        ///
        /// 使用场景：
        /// - 显示最终处理结果
        /// - 记录操作日志
        /// - 触发后续处理流程
        /// - 更新UI状态
        /// </remarks>
        protected virtual void OnBatchCompleted(BatchCompletedEventArgs e)
        {
            BatchCompleted?.Invoke(this, e);
        }

        #endregion

        #region IDisposable 实现和资源管理

        /// <summary>
        /// 释放YBatchProcessor使用的所有资源
        /// 实现IDisposable接口，确保资源得到正确释放
        /// </summary>
        /// <remarks>
        /// 释放的资源包括：
        /// - SemaphoreSlim：并发控制信号量
        /// - CancellationTokenSource：取消令牌源
        /// - YFileCompressor：文件压缩器（如果是内部创建的）
        ///
        /// 使用模式：
        /// <code>
        /// // 推荐使用using语句
        /// using var processor = new YBatchProcessor();
        /// await processor.BatchCopyFilesAsync(filePairs);
        /// // 自动释放资源
        ///
        /// // 或者手动释放
        /// var processor = new YBatchProcessor();
        /// try
        /// {
        ///     await processor.BatchCopyFilesAsync(filePairs);
        /// }
        /// finally
        /// {
        ///     processor.Dispose();
        /// }
        /// </code>
        ///
        /// 注意事项：
        /// - 释放后不能再使用此实例
        /// - 多次调用Dispose是安全的
        /// - 释放会取消所有正在进行的操作
        /// </remarks>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// 遵循标准的Dispose模式实现
        /// </summary>
        /// <param name="disposing">
        /// true表示正在释放托管资源，false表示从终结器调用
        /// </param>
        /// <remarks>
        /// 释放顺序：
        /// 1. 取消所有正在进行的操作
        /// 2. 释放信号量资源
        /// 3. 释放取消令牌源
        /// 4. 释放文件压缩器（如果适用）
        /// 5. 标记为已释放状态
        ///
        /// 线程安全：
        /// - 此方法是线程安全的
        /// - 多次调用是安全的
        /// - 使用_disposed标志防止重复释放
        ///
        /// 继承支持：
        /// - 子类可以重写此方法来释放额外资源
        /// - 重写时应该调用基类方法
        /// </remarks>
        protected virtual void Dispose(bool disposing)
        {
            // 双重检查：确保只释放一次且只在释放托管资源时执行
            // !_disposed 防止重复释放，disposing 确保是从Dispose()调用而非终结器
            if (!_disposed && disposing)
            {
                // 第一步：取消所有正在进行的操作
                // 使用空条件运算符(?.)防止空引用异常
                _cancellationTokenSource?.Cancel();

                // 第二步：释放信号量资源
                // SemaphoreSlim实现了IDisposable，需要显式释放
                _semaphore?.Dispose();

                // 第三步：释放取消令牌源
                // CancellationTokenSource也需要释放以清理内部资源
                _cancellationTokenSource?.Dispose();

                // 第四步：释放文件压缩器（如果它实现了IDisposable）
                // 使用is模式匹配检查类型并转换，避免强制转换异常
                if (_fileCompressor is IDisposable disposableCompressor)
                {
                    disposableCompressor.Dispose();
                }

                // 第五步：标记为已释放状态，防止后续使用和重复释放
                _disposed = true;
            }
        }

        /// <summary>
        /// 检查对象是否已被释放，如果已释放则抛出异常
        /// 用于防止在对象释放后继续使用
        /// </summary>
        /// <exception cref="ObjectDisposedException">
        /// 当对象已被释放时抛出此异常
        /// </exception>
        /// <remarks>
        /// 调用时机：
        /// - 在所有公共方法的开始处调用
        /// - 确保不会在释放后的对象上执行操作
        ///
        /// 异常信息：
        /// - 包含类名信息
        /// - 提供清晰的错误描述
        ///
        /// 性能影响：
        /// - 非常轻量的检查操作
        /// - 对性能影响可忽略不计
        /// </remarks>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(YBatchProcessor));
            }
        }

        #endregion
    }

    #region 批量处理数据模型和类型定义

    /// <summary>
    /// 批量任务定义
    /// 表示一个要执行的批量处理任务，包含任务类型、路径和参数信息
    /// </summary>
    /// <remarks>
    /// 任务属性说明：
    /// - Id：唯一标识符，用于跟踪和关联任务结果
    /// - Type：任务类型，决定执行的操作
    /// - SourcePath：源文件或目录路径
    /// - TargetPath：目标文件或目录路径（某些操作可能不需要）
    /// - Parameters：任务特定参数，如压缩级别、覆盖选项等
    /// - Priority：任务优先级（预留功能，当前版本未使用）
    ///
    /// 使用示例：
    /// <code>
    /// var task = new BatchTask
    /// {
    ///     Id = Guid.NewGuid().ToString(),
    ///     Type = BatchTaskType.CopyFile,
    ///     SourcePath = "source.txt",
    ///     TargetPath = "target.txt",
    ///     Parameters = new Dictionary&lt;string, object&gt; { { "overwrite", true } }
    /// };
    /// </code>
    /// </remarks>
    public class BatchTask
    {
        /// <summary>任务唯一标识符</summary>
        public string Id { get; set; } = "";

        /// <summary>任务类型</summary>
        public BatchTaskType Type { get; set; }

        /// <summary>源文件或目录路径</summary>
        public string SourcePath { get; set; } = "";

        /// <summary>目标文件或目录路径</summary>
        public string TargetPath { get; set; } = "";

        /// <summary>任务特定参数</summary>
        public Dictionary<string, object>? Parameters { get; set; }

        /// <summary>任务优先级（预留功能）</summary>
        public int Priority { get; set; } = 0;
    }

    /// <summary>
    /// 批量任务类型枚举
    /// 定义YBatchProcessor支持的所有任务类型
    /// </summary>
    /// <remarks>
    /// 任务类型说明：
    /// - CopyFile：文件复制操作
    /// - MoveFile：文件移动操作
    /// - DeleteFile：文件删除操作
    /// - CompressFile：文件压缩操作
    /// - DecompressFile：文件解压操作
    /// - EncryptFile：文件加密操作（预留）
    /// - DecryptFile：文件解密操作（预留）
    /// - Custom：自定义操作（预留）
    ///
    /// 扩展性：
    /// - 可以添加新的任务类型
    /// - 需要在ProcessSingleTaskAsync中添加对应的处理逻辑
    /// </remarks>
    public enum BatchTaskType
    {
        /// <summary>文件复制</summary>
        CopyFile,

        /// <summary>文件移动</summary>
        MoveFile,

        /// <summary>文件删除</summary>
        DeleteFile,

        /// <summary>文件压缩</summary>
        CompressFile,

        /// <summary>文件解压</summary>
        DecompressFile,

        /// <summary>文件加密（预留）</summary>
        EncryptFile,

        /// <summary>文件解密（预留）</summary>
        DecryptFile,

        /// <summary>自定义操作（预留）</summary>
        Custom
    }

    /// <summary>
    /// 单个批量任务的执行结果
    /// 包含任务的完整执行信息和统计数据
    /// </summary>
    /// <remarks>
    /// 结果属性说明：
    /// - TaskId：对应的任务ID
    /// - TaskType：任务类型
    /// - SourcePath/TargetPath：源路径和目标路径
    /// - Success：执行是否成功
    /// - ErrorMessage：错误信息（如果失败）
    /// - StartTime/EndTime：开始和结束时间
    /// - Duration：执行耗时
    /// - BytesProcessed：处理的字节数
    ///
    /// 用途：
    /// - 详细的任务执行记录
    /// - 错误诊断和调试
    /// - 性能分析和优化
    /// - 操作审计和日志记录
    /// </remarks>
    public class BatchResult
    {
        /// <summary>任务唯一标识符</summary>
        public string TaskId { get; set; } = "";

        /// <summary>任务类型</summary>
        public BatchTaskType TaskType { get; set; }

        /// <summary>源文件或目录路径</summary>
        public string SourcePath { get; set; } = "";

        /// <summary>目标文件或目录路径</summary>
        public string TargetPath { get; set; } = "";

        /// <summary>任务是否执行成功</summary>
        public bool Success { get; set; }

        /// <summary>错误信息（如果失败）</summary>
        public string? ErrorMessage { get; set; }

        /// <summary>任务开始时间</summary>
        public DateTime StartTime { get; set; }

        /// <summary>任务结束时间</summary>
        public DateTime EndTime { get; set; }

        /// <summary>任务执行耗时</summary>
        public TimeSpan Duration { get; set; }

        /// <summary>处理的字节数</summary>
        public long BytesProcessed { get; set; }
    }

    /// <summary>
    /// 批量处理的完整结果
    /// 包含所有任务的汇总统计和详细结果
    /// </summary>
    /// <remarks>
    /// 统计属性说明：
    /// - TotalTasks：总任务数
    /// - CompletedTasks：已完成任务数
    /// - SuccessfulTasks：成功任务数
    /// - FailedTasks：失败任务数
    /// - StartTime/EndTime：批处理开始和结束时间
    /// - Duration：总耗时
    /// - SuccessRate：成功率百分比
    /// - IsCancelled：是否被取消
    /// - TaskResults：所有任务的详细结果
    /// - TotalBytesProcessed：总处理字节数（计算属性）
    ///
    /// 用途：
    /// - 批处理操作的完整报告
    /// - 性能监控和分析
    /// - 用户界面状态显示
    /// - 操作日志和审计
    /// </remarks>
    public class BatchProcessResult
    {
        /// <summary>总任务数</summary>
        public int TotalTasks { get; set; }

        /// <summary>已完成任务数</summary>
        public int CompletedTasks { get; set; }

        /// <summary>成功任务数</summary>
        public int SuccessfulTasks { get; set; }

        /// <summary>失败任务数</summary>
        public int FailedTasks { get; set; }

        /// <summary>批处理开始时间</summary>
        public DateTime StartTime { get; set; }

        /// <summary>批处理结束时间</summary>
        public DateTime EndTime { get; set; }

        /// <summary>批处理总耗时</summary>
        public TimeSpan Duration { get; set; }

        /// <summary>成功率百分比</summary>
        public double SuccessRate { get; set; }

        /// <summary>是否被取消</summary>
        public bool IsCancelled { get; set; }

        /// <summary>所有任务的详细结果列表</summary>
        public List<BatchResult> TaskResults { get; set; } = new();

        /// <summary>总处理字节数（计算属性）</summary>
        public long TotalBytesProcessed => TaskResults.Sum(r => r.BytesProcessed);
    }

    /// <summary>
    /// 批处理进度
    /// </summary>
    public class BatchProgress
    {
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int SuccessfulTasks { get; set; }
        public int FailedTasks { get; set; }
        public double ProgressPercentage { get; set; }
        public BatchTask? CurrentTask { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan EstimatedRemainingTime { get; set; }
    }

    /// <summary>
    /// 文件复制对
    /// </summary>
    public class FileCopyPair
    {
        public string SourcePath { get; set; } = "";
        public string TargetPath { get; set; } = "";
    }

    /// <summary>
    /// 压缩任务
    /// </summary>
    public class CompressionTask
    {
        public string SourcePath { get; set; } = "";
        public string TargetPath { get; set; } = "";
        public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.Optimal;
        public bool IncludeSubdirectories { get; set; } = true;
    }

    /// <summary>
    /// 解压任务
    /// </summary>
    public class DecompressionTask
    {
        public string ZipFilePath { get; set; } = "";
        public string ExtractPath { get; set; } = "";
        public bool Overwrite { get; set; } = false;
    }

    /// <summary>
    /// 任务开始事件参数
    /// </summary>
    public class TaskStartedEventArgs : EventArgs
    {
        public BatchTask Task { get; set; } = new();
    }

    /// <summary>
    /// 任务完成事件参数
    /// </summary>
    public class TaskCompletedEventArgs : EventArgs
    {
        public BatchTask Task { get; set; } = new();
        public BatchResult Result { get; set; } = new();
    }

    /// <summary>
    /// 进度更新事件参数
    /// </summary>
    public class ProgressUpdateEventArgs : EventArgs
    {
        public BatchProgress Progress { get; set; } = new();
    }

    /// <summary>
    /// 批处理完成事件参数
    /// </summary>
    public class BatchCompletedEventArgs : EventArgs
    {
        public BatchProcessResult Result { get; set; } = new();
    }

    #endregion
}
