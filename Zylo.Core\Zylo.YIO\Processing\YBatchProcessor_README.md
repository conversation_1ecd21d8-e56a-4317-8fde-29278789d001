# YBatchProcessor - 高性能批量文件处理器

## 📋 概述

YBatchProcessor 是 Zylo.YIO 库中的核心批量处理组件，提供高性能、多线程的文件批量操作功能。它采用现代异步编程模式，支持并发控制、进度监控、事件通知和错误处理。

## 🚀 核心功能

### 批量文件操作

- **批量复制** - 高效的文件批量复制，支持覆盖控制
- **批量移动** - 文件批量移动操作，支持跨驱动器移动
- **批量删除** - 支持普通删除和安全删除（多次覆写）
- **批量压缩** - 文件批量压缩为ZIP格式，支持多种压缩级别
- **批量解压** - ZIP文件批量解压，支持目录结构保持

### 高级特性

- **并发控制** - 可配置的最大并发数，基于信号量的线程安全控制
- **进度监控** - 实时进度回调和详细的进度统计信息
- **事件系统** - 丰富的事件通知：任务开始、完成、进度更新、批处理完成
- **取消支持** - 支持批量操作的优雅取消，不影响已完成的任务
- **错误处理** - 独立的任务错误处理，单个任务失败不影响其他任务
- **性能统计** - 详细的性能指标：耗时、吞吐量、成功率、字节数统计

## 🏗️ 架构设计

### 生成器属性

YBatchProcessor 使用了三个重要的代码生成器属性：

#### `[YDoc]` - 文档生成器

- **功能**: 自动生成 Markdown 格式的 API 文档
- **作用**: 基于 XML 注释生成完整的类和方法文档
- **输出**: 生成详细的 API 参考文档，包含方法签名、参数说明、使用示例
- **优势**: 保持文档与代码同步，减少文档维护工作量

#### `[YService.Scoped]` - 依赖注入生成器

- **功能**: 自动注册为 Scoped 生命周期的服务
- **作用**: 在依赖注入容器中注册 YBatchProcessor
- **生命周期**: Scoped - 在同一个请求/作用域内是单例
- **使用**: 可以通过构造函数注入或 `IServiceProvider.GetService<YBatchProcessor>()` 获取
- **优势**: 自动化服务注册，支持依赖注入模式

#### `[YStatic]` - 静态方法生成器

- **功能**: 为实例方法生成对应的静态扩展方法
- **作用**: 提供静态调用方式，无需创建实例
- **生成**: 创建静态扩展类，包含所有公共方法的静态版本
- **便利性**: 支持 `YBatchProcessor.BatchCopyFilesAsync(...)` 静态调用
- **兼容性**: 保持原有实例方法的同时，提供静态调用选项

### 核心组件

```
YBatchProcessor
├── 并发控制 (SemaphoreSlim)
├── 任务队列 (ConcurrentQueue)
├── 结果收集 (ConcurrentDictionary)
├── 取消控制 (CancellationTokenSource)
├── 事件系统 (4个事件)
├── 文件压缩器 (YFileCompressor)
└── 代码生成器 (YDoc + YService + YStatic)
```

### 设计原则

- **线程安全** - 使用并发集合和同步原语确保线程安全
- **资源管理** - 实现IDisposable，正确释放所有资源
- **错误隔离** - 单个任务失败不影响其他任务的执行
- **性能优化** - 异步I/O操作，避免线程阻塞
- **可扩展性** - 支持自定义任务类型和处理逻辑
- **代码生成** - 利用生成器减少样板代码，提高开发效率

## 📖 使用指南

### 基本用法

#### 实例方式（传统用法）

```csharp
// 创建批量处理器
using var processor = new YBatchProcessor(maxConcurrency: 4);

// 批量复制文件
var filePairs = new[]
{
    new FileCopyPair { SourcePath = "source1.txt", TargetPath = "target1.txt" },
    new FileCopyPair { SourcePath = "source2.txt", TargetPath = "target2.txt" }
};

var result = await processor.BatchCopyFilesAsync(filePairs, overwrite: true);
Console.WriteLine($"复制完成: {result.SuccessfulTasks}/{result.TotalTasks}");
```

#### 静态方式（YStatic 生成器提供）

```csharp
// 直接使用静态方法，无需创建实例
var filePairs = new[]
{
    new FileCopyPair { SourcePath = "source1.txt", TargetPath = "target1.txt" },
    new FileCopyPair { SourcePath = "source2.txt", TargetPath = "target2.txt" }
};

// YStatic 生成器自动创建的静态扩展方法
var result = await YBatchProcessor.BatchCopyFilesAsync(filePairs, overwrite: true);
Console.WriteLine($"复制完成: {result.SuccessfulTasks}/{result.TotalTasks}");
```

#### 依赖注入方式（YService.Scoped 生成器提供）

```csharp
// 在 Startup.cs 或 Program.cs 中注册服务
services.AddALL(); // YService 生成器自动注册所有服务

// 在控制器或服务中注入使用
public class FileController : ControllerBase
{
    private readonly YBatchProcessor _batchProcessor;

    public FileController(YBatchProcessor batchProcessor)
    {
        _batchProcessor = batchProcessor;
    }

    public async Task<IActionResult> BatchCopy([FromBody] FileCopyPair[] filePairs)
    {
        var result = await _batchProcessor.BatchCopyFilesAsync(filePairs, overwrite: true);
        return Ok(new {
            Success = result.SuccessfulTasks,
            Total = result.TotalTasks
        });
    }
}
```

### 进度监控

```csharp
// 创建进度回调
var progress = new Progress<BatchProgress>(p => 
{
    Console.WriteLine($"进度: {p.ProgressPercentage:F1}% ({p.CompletedTasks}/{p.TotalTasks})");
    Console.WriteLine($"当前任务: {p.CurrentTask?.SourcePath}");
});

// 执行批量操作
var result = await processor.BatchCopyFilesAsync(filePairs, progress: progress);
```

### 事件处理

```csharp
// 订阅事件
processor.TaskStarted += (sender, e) => 
    Console.WriteLine($"开始处理: {e.Task.SourcePath}");

processor.TaskCompleted += (sender, e) => 
    Console.WriteLine($"完成处理: {e.Task.SourcePath} - {(e.Result.Success ? "成功" : "失败")}");

processor.ProgressUpdated += (sender, e) => 
    Console.WriteLine($"进度更新: {e.Progress.ProgressPercentage:F1}%");

processor.BatchCompleted += (sender, e) => 
    Console.WriteLine($"批处理完成: {e.Result.SuccessfulTasks}/{e.Result.TotalTasks}");
```

### 取消操作

```csharp
// 启动长时间运行的批处理
var processingTask = processor.BatchCopyFilesAsync(largeBatch);

// 在需要时取消
processor.CancelBatch();

// 等待处理完成（会返回部分结果）
var result = await processingTask;
Console.WriteLine($"取消前完成: {result.CompletedTasks}/{result.TotalTasks}");
```

## 🎯 API 参考

### 主要方法

| 方法 | 描述 | 参数 |
|------|------|------|
| `BatchCopyFilesAsync` | 批量复制文件 | filePairs, overwrite, progress |
| `BatchMoveFilesAsync` | 批量移动文件 | filePairs, progress |
| `BatchDeleteFilesAsync` | 批量删除文件 | filePaths, secureDelete, progress |
| `BatchCompressFilesAsync` | 批量压缩文件 | compressionTasks, progress |
| `BatchDecompressFilesAsync` | 批量解压文件 | decompressionTasks, progress |
| `ProcessBatchAsync` | 自定义批量处理 | tasks, progress |

### 控制方法

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `CancelBatch()` | 取消当前批处理 | void |
| `IsProcessing()` | 检查是否正在处理 | bool |
| `SetMaxConcurrency(int)` | 设置最大并发数 | void |
| `GetMaxConcurrency()` | 获取当前并发数 | int |

### 事件

| 事件 | 描述 | 参数类型 |
|------|------|----------|
| `TaskStarted` | 任务开始时触发 | TaskStartedEventArgs |
| `TaskCompleted` | 任务完成时触发 | TaskCompletedEventArgs |
| `ProgressUpdated` | 进度更新时触发 | ProgressUpdateEventArgs |
| `BatchCompleted` | 批处理完成时触发 | BatchCompletedEventArgs |

## 📊 性能特性

### 并发性能

- **默认并发数**: CPU核心数
- **可配置范围**: 1 - 无限制（建议不超过CPU核心数的4倍）
- **内存使用**: 低内存占用，高效的资源利用

### 性能基准

- **小文件处理**: < 1ms/任务
- **大文件处理**: > 100MB/s（取决于存储设备）
- **并发效率**: 随并发数线性提升（直到I/O瓶颈）
- **内存稳定性**: 无内存泄漏，正确的资源管理

## ⚠️ 注意事项

### 使用建议

1. **资源管理**: 始终使用 `using` 语句或手动调用 `Dispose()`
2. **并发设置**: 根据硬件配置和任务类型调整并发数
3. **错误处理**: 检查 `BatchProcessResult` 中的失败任务
4. **取消操作**: 取消是异步的，可能需要时间完全停止

### 限制说明

1. **文件锁定**: 无法处理被其他进程锁定的文件
2. **权限要求**: 需要对源文件和目标目录有相应权限
3. **磁盘空间**: 确保有足够的磁盘空间进行操作
4. **路径长度**: 受操作系统路径长度限制

## 🔧 配置选项

### 构造函数参数

```csharp
public YBatchProcessor(
    int maxConcurrency = 4,           // 最大并发数（4表示使用CPU核心数）
    YFileCompressor? fileCompressor = null  // 自定义文件压缩器
)
```

### 压缩选项

```csharp
public class CompressionTask
{
    public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.Optimal;
    public bool IncludeSubdirectories { get; set; } = true;
}
```

## 🧪 测试和验证

YBatchProcessor 包含完整的测试套件，位于 `Zylo.YIO.Demo/YBatchProcessorTests/` 目录：

- **BasicOperationsTests** - 基础操作测试
- **AdvancedFeaturesTests** - 高级功能测试  
- **ErrorHandlingTests** - 错误处理测试
- **PerformanceTests** - 性能测试
- **ResourceManagementTests** - 资源管理测试

运行测试：

```bash
dotnet run --project Zylo.YIO.Demo test
```

## 🔧 代码生成器详解

### YDoc 生成器

YDoc 生成器会自动扫描 YBatchProcessor 的 XML 注释，生成完整的 API 文档：

```markdown
# YBatchProcessor API 文档
## 方法
### BatchCopyFilesAsync
- **描述**: 批量复制文件
- **参数**:
  - filePairs: 文件复制对数组
  - overwrite: 是否覆盖现有文件
- **返回**: BatchProcessResult
```

### YService.Scoped 生成器

自动生成服务注册代码，支持依赖注入：

```csharp
// 生成的注册代码
public static class YBatchProcessorServiceExtensions
{
    public static IServiceCollection AddYBatchProcessor(this IServiceCollection services)
    {
        services.AddScoped<YBatchProcessor>();
        return services;
    }
}
```

### YStatic 生成器

为所有公共方法生成静态扩展版本：

```csharp
// 生成的静态扩展类
public static class YBatchProcessorExtensions
{
    public static async Task<BatchProcessResult> BatchCopyFilesAsync(
        this FileCopyPair[] filePairs,
        bool overwrite = false)
    {
        using var processor = new YBatchProcessor();
        return await processor.BatchCopyFilesAsync(filePairs, overwrite);
    }
}
```

### 生成器优势

- **减少样板代码**: 自动生成重复性代码
- **保持同步**: 代码变更时自动更新生成的代码
- **提高效率**: 开发者专注业务逻辑，生成器处理基础设施
- **多种使用方式**: 实例、静态、依赖注入三种方式任选
- **文档自动化**: XML 注释自动转换为 Markdown 文档

## 📝 版本历史

- **v1.0** - 初始版本，支持基础批量操作
- **v1.1** - 添加压缩/解压功能
- **v1.2** - 完善事件系统和错误处理
- **v1.3** - 添加IDisposable实现和资源管理
- **v1.4** - 性能优化和详细注释
- **v1.5** - 集成代码生成器（YDoc + YService + YStatic）
