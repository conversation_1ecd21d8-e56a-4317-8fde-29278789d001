using System;
using System.IO;
using System.Text;
using System.Security.Cryptography;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;

using Zylo.YIO.Config;
using Zylo.YIO.Processing;

namespace Zylo.YIO.Security
{
    /// <summary>
    /// YFileEncryption - 企业级文件加密和安全工具类
    ///
    /// 🔐 核心功能特性：
    /// • 高强度AES文件加密：支持AES-256-CBC模式，提供军用级加密强度
    /// • 多算法哈希计算：支持MD5、SHA256、SHA512等多种哈希算法
    /// • 数字签名验证：基于RSA的文件完整性验证和身份认证
    /// • 批量加密处理：支持目录级别的批量加密和解密操作
    /// • 流式处理优化：支持大文件的内存友好型流式加密
    /// • 密钥安全管理：安全的密钥生成、存储和销毁机制
    /// • 异步操作支持：完整的async/await模式，适合高并发场景
    ///
    /// 💡 设计原则：
    /// • 安全第一：使用业界标准的加密算法和最佳实践
    /// • 性能优化：流式处理，支持任意大小文件的高效加密
    /// • 易用性：简洁的API设计，支持链式调用和批量操作
    /// • 可扩展性：模块化设计，支持自定义加密算法和参数
    /// • 向后兼容：保持API稳定性，支持版本升级
    ///
    /// 📋 使用场景：
    /// • 敏感文件保护：个人隐私文件、商业机密文档加密
    /// • 数据传输安全：文件传输前加密，确保传输过程安全
    /// • 合规性要求：满足GDPR、HIPAA等法规的数据保护要求
    /// • 备份加密：自动备份系统的文件加密集成
    /// • 批量处理：大规模文件的自动化加密处理
    /// </summary>


    public partial class YFileEncryption
    {
        // ==========================================
        // 🔧 私有字段和常量定义
        // ==========================================

        #region 私有字段

        /// <summary>
        /// YIO框架配置实例
        /// 包含缓冲区大小、超时设置等性能参数
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// AES加密算法的密钥长度（字节）
        /// 使用256位密钥提供最高安全级别
        /// </summary>
        private const int AES_KEY_SIZE = 32; // 256 bits

        /// <summary>
        /// AES加密算法的初始化向量长度（字节）
        /// 标准AES块大小为128位
        /// </summary>
        private const int AES_IV_SIZE = 16; // 128 bits

        /// <summary>
        /// 文件加密时的缓冲区大小
        /// 平衡内存使用和性能的最优值
        /// </summary>
        private const int ENCRYPTION_BUFFER_SIZE = 64 * 1024; // 64KB

        /// <summary>
        /// 加密文件的魔数标识
        /// 用于识别和验证加密文件格式
        /// </summary>
        private static readonly byte[] ENCRYPTION_MAGIC = { 0x59, 0x45, 0x4E, 0x43 }; // "YENC"

        #endregion

        // ==========================================
        // 🏗️ 构造函数和初始化
        // ==========================================

        #region 构造函数

        /// <summary>
        /// 初始化 YFileEncryption 实例
        /// 创建一个配置完整的文件加密管理器
        /// </summary>
        /// <param name="config">
        /// YIO框架配置实例，包含：
        /// • 文件操作缓冲区大小
        /// • 加密算法配置参数
        /// • 超时和重试设置
        /// • 性能优化参数
        /// 如果为null，将使用默认配置
        /// </param>
        /// <example>
        /// <code>
        /// // 使用自定义配置创建加密管理器
        /// var config = new YIOConfig { BufferSize = 8192 };
        /// var encryption = new YFileEncryption(config);
        ///
        /// // 加密文件
        /// var success = encryption.EncryptFile("secret.txt", "secret.enc", "password123");
        /// </code>
        /// </example>
        public YFileEncryption(YIOConfig config)
        {
            // 确保配置不为null，提供默认配置作为后备
            _config = config ?? new YIOConfig();

            // 输出初始化日志
            Console.WriteLine($"YFileEncryption 初始化完成 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// 无参构造函数 - 使用默认配置初始化
        /// 适用于快速创建加密管理器实例，使用框架默认配置
        /// 主要用于静态方法调用和简单场景
        /// </summary>
        /// <example>
        /// <code>
        /// // 快速创建加密管理器
        /// var encryption = new YFileEncryption();
        ///
        /// // 或使用静态方法（由YStatic属性自动生成）
        /// var success = YFileEncryption.EncryptFile("file.txt", "file.enc", "password");
        /// </code>
        /// </example>
        public YFileEncryption() : this(new YIOConfig())
        {
            // 调用主构造函数，使用默认配置
            // 这种设计模式确保所有初始化逻辑集中在主构造函数中
        }

        #endregion

        // ==========================================
        // 🔐 公共API - AES文件加密核心方法
        // ==========================================

        #region AES 文件加密

        /// <summary>
        /// 使用AES-256-CBC模式加密文件
        /// 这是一个安全的文件加密方法，使用业界标准的AES加密算法
        /// 支持任意大小的文件，采用流式处理确保内存效率
        /// </summary>
        /// <param name="inputFilePath">
        /// 要加密的源文件路径
        /// 必须是存在的有效文件路径
        /// </param>
        /// <param name="outputFilePath">
        /// 加密后文件的输出路径
        /// 如果文件已存在将被覆盖
        /// </param>
        /// <param name="password">
        /// 加密密码，将通过SHA256哈希生成256位密钥
        /// 建议使用强密码（至少8位，包含大小写字母、数字和特殊字符）
        /// </param>
        /// <returns>
        /// 加密成功返回true，失败返回false
        /// 失败原因会输出到控制台
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var success = encryption.EncryptFile(
        ///     @"C:\Documents\secret.txt",
        ///     @"C:\Encrypted\secret.enc",
        ///     "MyStrongPassword123!"
        /// );
        /// if (success) Console.WriteLine("文件加密成功");
        /// </code>
        /// </example>
        public bool EncryptFile(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                // === 第一步：参数验证 ===
                // 验证输入文件路径的有效性
                if (string.IsNullOrWhiteSpace(inputFilePath) || !File.Exists(inputFilePath))
                    throw new ArgumentException("输入文件不存在或路径无效", nameof(inputFilePath));

                // 验证输出文件路径不为空
                if (string.IsNullOrWhiteSpace(outputFilePath))
                    throw new ArgumentException("输出文件路径不能为空", nameof(outputFilePath));

                // 验证密码强度（基本检查）
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("密码不能为空", nameof(password));

                // === 第二步：密钥生成 ===
                // 从用户密码生成加密密钥和随机初始化向量
                // 每次加密都使用不同的IV，确保相同文件加密结果不同
                var (key, iv) = GenerateKeyAndIV(password);

                // === 第三步：AES加密器配置 ===
                // 创建AES加密实例，使用默认的CBC模式和PKCS7填充
                using var aes = Aes.Create();
                aes.Key = key;  // 设置256位密钥
                aes.IV = iv;    // 设置128位初始化向量

                // === 第四步：文件流处理 ===
                // 打开输入文件流（只读模式）
                using var inputStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read);

                // 创建输出文件流（覆盖模式）
                using var outputStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);

                // === 第五步：写入文件头信息 ===
                // 写入魔数标识，用于验证文件格式
                outputStream.Write(ENCRYPTION_MAGIC, 0, ENCRYPTION_MAGIC.Length);

                // 写入IV到文件开头，解密时需要读取
                // IV不需要保密，但每次加密必须不同
                outputStream.Write(iv, 0, iv.Length);

                // === 第六步：流式加密处理 ===
                // 创建加密流，将数据写入时自动加密
                using var cryptoStream = new CryptoStream(outputStream, aes.CreateEncryptor(), CryptoStreamMode.Write);

                // 将输入文件内容复制到加密流中
                // CopyTo方法会自动处理大文件的分块读取
                inputStream.CopyTo(cryptoStream);

                // === 第七步：确保数据完整写入 ===
                // FlushFinalBlock确保所有数据都被加密并写入
                cryptoStream.FlushFinalBlock();

                return true;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息，便于调试和问题排查
                Console.WriteLine($"文件加密失败: {ex.Message}");
                Console.WriteLine($"输入文件: {inputFilePath}");
                Console.WriteLine($"输出文件: {outputFilePath}");
                Console.WriteLine($"错误类型: {ex.GetType().Name}");

                return false;
            }
        }

        /// <summary>
        /// 使用AES-256-CBC模式解密文件
        /// 这是一个安全的文件解密方法，与EncryptFile方法配对使用
        /// 支持任意大小的文件，采用流式处理确保内存效率
        /// </summary>
        /// <param name="inputFilePath">
        /// 要解密的加密文件路径
        /// 必须是由本类EncryptFile方法生成的有效加密文件
        /// </param>
        /// <param name="outputFilePath">
        /// 解密后文件的输出路径
        /// 如果文件已存在将被覆盖
        /// </param>
        /// <param name="password">
        /// 解密密码，必须与加密时使用的密码完全一致
        /// 密码错误将导致解密失败或产生损坏的文件
        /// </param>
        /// <returns>
        /// 解密成功返回true，失败返回false
        /// 失败原因会输出到控制台
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var success = encryption.DecryptFile(
        ///     @"C:\Encrypted\secret.enc",
        ///     @"C:\Documents\secret.txt",
        ///     "MyStrongPassword123!"
        /// );
        /// if (success) Console.WriteLine("文件解密成功");
        /// </code>
        /// </example>
        public bool DecryptFile(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                // === 第一步：参数验证 ===
                // 验证加密文件路径的有效性
                if (string.IsNullOrWhiteSpace(inputFilePath) || !File.Exists(inputFilePath))
                    throw new ArgumentException("加密文件不存在或路径无效", nameof(inputFilePath));

                // 验证输出文件路径不为空
                if (string.IsNullOrWhiteSpace(outputFilePath))
                    throw new ArgumentException("输出文件路径不能为空", nameof(outputFilePath));

                // 验证密码不为空
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("解密密码不能为空", nameof(password));

                // === 第二步：打开加密文件流 ===
                using var inputStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read);

                // === 第三步：验证文件格式 ===
                // 读取并验证魔数标识
                var magicBuffer = new byte[ENCRYPTION_MAGIC.Length];
                var magicBytesRead = inputStream.Read(magicBuffer, 0, ENCRYPTION_MAGIC.Length);

                // 检查是否成功读取魔数
                if (magicBytesRead != ENCRYPTION_MAGIC.Length)
                    throw new InvalidDataException("文件格式无效：无法读取文件头");

                // 验证魔数是否匹配
                if (!magicBuffer.SequenceEqual(ENCRYPTION_MAGIC))
                    throw new InvalidDataException("文件格式无效：不是有效的加密文件");

                // === 第四步：读取初始化向量 ===
                // 读取加密时存储的IV（16字节）
                var iv = new byte[AES_IV_SIZE];
                var ivBytesRead = inputStream.Read(iv, 0, AES_IV_SIZE);

                // 检查是否成功读取完整的IV
                if (ivBytesRead != AES_IV_SIZE)
                    throw new InvalidDataException("文件格式无效：无法读取初始化向量");

                // === 第五步：生成解密密钥 ===
                // 使用相同的密码生成相同的密钥
                var key = GenerateKey(password);

                // === 第六步：AES解密器配置 ===
                // 创建AES解密实例，配置与加密时相同的参数
                using var aes = Aes.Create();
                aes.Key = key;  // 设置256位密钥
                aes.IV = iv;    // 设置从文件读取的IV

                // === 第七步：文件流处理 ===
                // 创建输出文件流（覆盖模式）
                using var outputStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);

                // 创建解密流，从输入流读取时自动解密
                using var cryptoStream = new CryptoStream(inputStream, aes.CreateDecryptor(), CryptoStreamMode.Read);

                // === 第八步：流式解密处理 ===
                // 将解密后的数据复制到输出文件
                // CopyTo方法会自动处理大文件的分块读取和解密
                cryptoStream.CopyTo(outputStream);

                return true;
            }
            catch (CryptographicException ex)
            {
                // 专门处理加密相关异常（通常是密码错误）
                Console.WriteLine($"解密失败 - 可能是密码错误: {ex.Message}");
                Console.WriteLine($"加密文件: {inputFilePath}");
                return false;
            }
            catch (Exception ex)
            {
                // 处理其他类型的异常
                Console.WriteLine($"文件解密失败: {ex.Message}");
                Console.WriteLine($"加密文件: {inputFilePath}");
                Console.WriteLine($"输出文件: {outputFilePath}");
                Console.WriteLine($"错误类型: {ex.GetType().Name}");

                return false;
            }
        }

        #endregion

        // ==========================================
        // ⚡ 异步操作 - 高性能异步加密解密
        // ==========================================

        #region 异步加密解密

        /// <summary>
        /// 异步加密文件 - 高性能版本
        /// 使用异步I/O操作，适合大文件处理和高并发场景
        /// 不会阻塞调用线程，提供更好的应用程序响应性
        /// </summary>
        /// <param name="inputFilePath">
        /// 要加密的源文件路径
        /// 必须是存在的有效文件路径
        /// </param>
        /// <param name="outputFilePath">
        /// 加密后文件的输出路径
        /// 如果文件已存在将被覆盖
        /// </param>
        /// <param name="password">
        /// 加密密码，将通过SHA256哈希生成256位密钥
        /// 建议使用强密码
        /// </param>
        /// <returns>
        /// Task&lt;bool&gt; - 异步任务，完成时返回加密是否成功
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var success = await encryption.EncryptFileAsync(
        ///     @"C:\LargeFile.zip",
        ///     @"C:\Encrypted\LargeFile.enc",
        ///     "StrongPassword123!"
        /// );
        /// if (success) Console.WriteLine("大文件异步加密完成");
        /// </code>
        /// </example>
        public async Task<bool> EncryptFileAsync(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                // === 第一步：参数验证 ===
                // 验证输入文件路径的有效性
                if (string.IsNullOrWhiteSpace(inputFilePath) || !File.Exists(inputFilePath))
                    throw new ArgumentException("输入文件不存在或路径无效", nameof(inputFilePath));

                // 验证输出文件路径不为空
                if (string.IsNullOrWhiteSpace(outputFilePath))
                    throw new ArgumentException("输出文件路径不能为空", nameof(outputFilePath));

                // 验证密码不为空
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("密码不能为空", nameof(password));

                // === 第二步：密钥生成 ===
                // 生成加密密钥和随机初始化向量
                var (key, iv) = GenerateKeyAndIV(password);

                // === 第三步：AES加密器配置 ===
                // 创建AES加密实例
                using var aes = Aes.Create();
                aes.Key = key;  // 设置256位密钥
                aes.IV = iv;    // 设置128位初始化向量

                // === 第四步：异步文件流处理 ===
                // 使用异步文件流，提高I/O性能
                await using var inputStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, ENCRYPTION_BUFFER_SIZE, useAsync: true);
                await using var outputStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write, FileShare.None, ENCRYPTION_BUFFER_SIZE, useAsync: true);

                // === 第五步：写入文件头信息 ===
                // 异步写入魔数标识
                await outputStream.WriteAsync(ENCRYPTION_MAGIC.AsMemory());

                // 异步写入IV到文件开头
                await outputStream.WriteAsync(iv.AsMemory());

                // === 第六步：异步流式加密处理 ===
                // 创建加密流，使用异步模式
                await using var cryptoStream = new CryptoStream(outputStream, aes.CreateEncryptor(), CryptoStreamMode.Write);

                // 异步复制文件内容，自动处理大文件分块
                await inputStream.CopyToAsync(cryptoStream);

                // === 第七步：确保数据完整写入 ===
                // 刷新加密流，确保所有数据都被处理
                await cryptoStream.FlushFinalBlockAsync();

                return true;
            }
            catch (Exception ex)
            {
                // 记录详细的错误信息
                Console.WriteLine($"异步文件加密失败: {ex.Message}");
                Console.WriteLine($"输入文件: {inputFilePath}");
                Console.WriteLine($"输出文件: {outputFilePath}");
                Console.WriteLine($"错误类型: {ex.GetType().Name}");

                return false;
            }
        }

        /// <summary>
        /// 异步解密文件 - 高性能版本
        /// 使用异步I/O操作，适合大文件处理和高并发场景
        /// 不会阻塞调用线程，提供更好的应用程序响应性
        /// </summary>
        /// <param name="inputFilePath">
        /// 要解密的加密文件路径
        /// 必须是由本类加密方法生成的有效加密文件
        /// </param>
        /// <param name="outputFilePath">
        /// 解密后文件的输出路径
        /// 如果文件已存在将被覆盖
        /// </param>
        /// <param name="password">
        /// 解密密码，必须与加密时使用的密码完全一致
        /// </param>
        /// <returns>
        /// Task&lt;bool&gt; - 异步任务，完成时返回解密是否成功
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var success = await encryption.DecryptFileAsync(
        ///     @"C:\Encrypted\LargeFile.enc",
        ///     @"C:\Decrypted\LargeFile.zip",
        ///     "StrongPassword123!"
        /// );
        /// if (success) Console.WriteLine("大文件异步解密完成");
        /// </code>
        /// </example>
        public async Task<bool> DecryptFileAsync(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                // === 第一步：参数验证 ===
                // 验证加密文件路径的有效性
                if (string.IsNullOrWhiteSpace(inputFilePath) || !File.Exists(inputFilePath))
                    throw new ArgumentException("加密文件不存在或路径无效", nameof(inputFilePath));

                // 验证输出文件路径不为空
                if (string.IsNullOrWhiteSpace(outputFilePath))
                    throw new ArgumentException("输出文件路径不能为空", nameof(outputFilePath));

                // 验证密码不为空
                if (string.IsNullOrWhiteSpace(password))
                    throw new ArgumentException("解密密码不能为空", nameof(password));

                // === 第二步：异步打开加密文件流 ===
                // 使用异步文件流，提高I/O性能
                await using var inputStream = new FileStream(inputFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, ENCRYPTION_BUFFER_SIZE, useAsync: true);

                // === 第三步：异步验证文件格式 ===
                // 读取并验证魔数标识
                var magicBuffer = new byte[ENCRYPTION_MAGIC.Length];
                var magicBytesRead = await inputStream.ReadAsync(magicBuffer.AsMemory());

                // 检查是否成功读取魔数
                if (magicBytesRead != ENCRYPTION_MAGIC.Length)
                    throw new InvalidDataException("文件格式无效：无法读取文件头");

                // 验证魔数是否匹配
                if (!magicBuffer.SequenceEqual(ENCRYPTION_MAGIC))
                    throw new InvalidDataException("文件格式无效：不是有效的加密文件");

                // === 第四步：异步读取初始化向量 ===
                // 读取加密时存储的IV（16字节）
                var iv = new byte[AES_IV_SIZE];
                var ivBytesRead = await inputStream.ReadAsync(iv.AsMemory());

                // 检查是否成功读取完整的IV
                if (ivBytesRead != AES_IV_SIZE)
                    throw new InvalidDataException("文件格式无效：无法读取初始化向量");

                // === 第五步：生成解密密钥 ===
                // 使用相同的密码生成相同的密钥
                var key = GenerateKey(password);

                // === 第六步：AES解密器配置 ===
                // 创建AES解密实例
                using var aes = Aes.Create();
                aes.Key = key;  // 设置256位密钥
                aes.IV = iv;    // 设置从文件读取的IV

                // === 第七步：异步文件流处理 ===
                // 创建输出文件流，使用异步模式
                await using var outputStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write, FileShare.None, ENCRYPTION_BUFFER_SIZE, useAsync: true);

                // 创建解密流，从输入流读取时自动解密
                await using var cryptoStream = new CryptoStream(inputStream, aes.CreateDecryptor(), CryptoStreamMode.Read);

                // === 第八步：异步流式解密处理 ===
                // 异步复制解密后的数据到输出文件
                await cryptoStream.CopyToAsync(outputStream);

                return true;
            }
            catch (CryptographicException ex)
            {
                // 专门处理加密相关异常（通常是密码错误）
                Console.WriteLine($"异步解密失败 - 可能是密码错误: {ex.Message}");
                Console.WriteLine($"加密文件: {inputFilePath}");
                return false;
            }
            catch (Exception ex)
            {
                // 处理其他类型的异常
                Console.WriteLine($"异步文件解密失败: {ex.Message}");
                Console.WriteLine($"加密文件: {inputFilePath}");
                Console.WriteLine($"输出文件: {outputFilePath}");
                Console.WriteLine($"错误类型: {ex.GetType().Name}");

                return false;
            }
        }

        #endregion

        // ==========================================
        // 🔍 文件哈希计算 - 完整性验证和指纹识别
        // ==========================================

        #region 文件哈希计算

        /// <summary>
        /// 计算文件MD5哈希值
        /// MD5虽然不再推荐用于安全目的，但仍广泛用于文件完整性检查
        /// 适用于快速文件比较和重复文件检测
        /// </summary>
        /// <param name="filePath">
        /// 要计算哈希的文件路径
        /// 必须是存在的有效文件
        /// </param>
        /// <returns>
        /// 32位十六进制MD5哈希字符串（小写）
        /// 例如：d41d8cd98f00b204e9800998ecf8427e
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var md5Hash = encryption.CalculateMD5(@"C:\Documents\file.txt");
        /// Console.WriteLine($"MD5: {md5Hash}");
        /// </code>
        /// </example>
        /// <exception cref="ArgumentException">文件不存在时抛出</exception>
        /// <exception cref="IOException">文件访问错误时抛出</exception>
        public string CalculateMD5(string filePath)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                throw new ArgumentException("文件不存在或路径无效", nameof(filePath));

            try
            {
                // 使用MD5算法计算哈希
                using var md5 = MD5.Create();
                using var stream = File.OpenRead(filePath);

                // 计算哈希值并转换为十六进制字符串
                var hash = md5.ComputeHash(stream);
                return Convert.ToHexString(hash).ToLowerInvariant();
            }
            catch (Exception ex)
            {
                throw new IOException($"计算MD5哈希失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算文件SHA256哈希值
        /// SHA256是目前推荐的安全哈希算法，提供256位哈希值
        /// 适用于安全性要求较高的场景，如数字签名和密码学应用
        /// </summary>
        /// <param name="filePath">
        /// 要计算哈希的文件路径
        /// 必须是存在的有效文件
        /// </param>
        /// <returns>
        /// 64位十六进制SHA256哈希字符串（小写）
        /// 例如：e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var sha256Hash = encryption.CalculateSHA256(@"C:\Documents\important.pdf");
        /// Console.WriteLine($"SHA256: {sha256Hash}");
        ///
        /// // 验证文件完整性
        /// var expectedHash = "abc123...";
        /// if (sha256Hash.Equals(expectedHash, StringComparison.OrdinalIgnoreCase))
        ///     Console.WriteLine("文件完整性验证通过");
        /// </code>
        /// </example>
        /// <exception cref="ArgumentException">文件不存在时抛出</exception>
        /// <exception cref="IOException">文件访问错误时抛出</exception>
        public string CalculateSHA256(string filePath)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                throw new ArgumentException("文件不存在或路径无效", nameof(filePath));

            try
            {
                // 使用SHA256算法计算哈希
                using var sha256 = SHA256.Create();
                using var stream = File.OpenRead(filePath);

                // 计算哈希值并转换为十六进制字符串
                var hash = sha256.ComputeHash(stream);
                return Convert.ToHexString(hash).ToLowerInvariant();
            }
            catch (Exception ex)
            {
                throw new IOException($"计算SHA256哈希失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算文件SHA512哈希值
        /// SHA512提供512位哈希值，安全性比SHA256更高
        /// 适用于对安全性要求极高的场景，但计算速度相对较慢
        /// </summary>
        /// <param name="filePath">
        /// 要计算哈希的文件路径
        /// 必须是存在的有效文件
        /// </param>
        /// <returns>
        /// 128位十六进制SHA512哈希字符串（小写）
        /// 例如：cf83e1357eefb8bdf1542850d66d8007d620e4050b5715dc83f4a921d36ce9ce47d0d13c5d85f2b0ff8318d2877eec2f63b931bd47417a81a538327af927da3e
        /// </returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var sha512Hash = encryption.CalculateSHA512(@"C:\Security\certificate.crt");
        /// Console.WriteLine($"SHA512: {sha512Hash}");
        /// </code>
        /// </example>
        /// <exception cref="ArgumentException">文件不存在时抛出</exception>
        /// <exception cref="IOException">文件访问错误时抛出</exception>
        public string CalculateSHA512(string filePath)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                throw new ArgumentException("文件不存在或路径无效", nameof(filePath));

            try
            {
                // 使用SHA512算法计算哈希
                using var sha512 = SHA512.Create();
                using var stream = File.OpenRead(filePath);

                // 计算哈希值并转换为十六进制字符串
                var hash = sha512.ComputeHash(stream);
                return Convert.ToHexString(hash).ToLowerInvariant();
            }
            catch (Exception ex)
            {
                throw new IOException($"计算SHA512哈希失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证文件哈希值
        /// 通过比较计算出的哈希值与期望值来验证文件完整性
        /// </summary>
        /// <param name="filePath">要验证的文件路径</param>
        /// <param name="expectedHash">期望的哈希值</param>
        /// <param name="algorithm">哈希算法类型</param>
        /// <returns>哈希值匹配返回true，否则返回false</returns>
        /// <example>
        /// <code>
        /// var encryption = new YFileEncryption();
        /// var isValid = encryption.VerifyFileHash(
        ///     @"C:\Downloads\file.zip",
        ///     "d41d8cd98f00b204e9800998ecf8427e",
        ///     HashAlgorithmType.MD5
        /// );
        /// Console.WriteLine(isValid ? "文件完整" : "文件已损坏");
        /// </code>
        /// </example>
        public bool VerifyFileHash(string filePath, string expectedHash, HashAlgorithmType algorithm)
        {
            if (string.IsNullOrWhiteSpace(expectedHash))
                throw new ArgumentException("期望哈希值不能为空", nameof(expectedHash));

            try
            {
                // 根据算法类型计算相应的哈希值
                var actualHash = algorithm switch
                {
                    HashAlgorithmType.MD5 => CalculateMD5(filePath),
                    HashAlgorithmType.SHA256 => CalculateSHA256(filePath),
                    HashAlgorithmType.SHA512 => CalculateSHA512(filePath),
                    _ => throw new ArgumentException($"不支持的哈希算法: {algorithm}")
                };

                // 忽略大小写比较哈希值
                return actualHash.Equals(expectedHash, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"哈希验证失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        // ==========================================
        // 🔧 私有辅助方法 - 密钥生成和工具函数
        // ==========================================

        #region 私有辅助方法

        /// <summary>
        /// 从用户密码生成AES密钥和随机初始化向量
        /// 这是一个关键的安全方法，确保每次加密都使用不同的IV
        /// </summary>
        /// <param name="password">
        /// 用户提供的密码字符串
        /// 将通过SHA256哈希转换为256位密钥
        /// </param>
        /// <returns>
        /// 包含32字节密钥和16字节IV的元组
        /// 密钥是确定性的（相同密码生成相同密钥）
        /// IV是随机的（每次调用都不同）
        /// </returns>
        /// <remarks>
        /// 安全设计说明：
        /// • 密钥通过SHA256哈希生成，确保密码强度
        /// • IV使用密码学安全的随机数生成器
        /// • 每次加密使用不同的IV，防止相同明文产生相同密文
        /// • IV不需要保密，但必须唯一
        /// </remarks>
        private (byte[] key, byte[] iv) GenerateKeyAndIV(string password)
        {
            // 生成确定性密钥（基于密码）
            var key = GenerateKey(password);

            // 生成随机初始化向量
            var iv = new byte[AES_IV_SIZE];

            // 使用密码学安全的随机数生成器
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(iv);

            return (key, iv);
        }

        /// <summary>
        /// 从用户密码生成AES加密密钥
        /// 使用SHA256哈希算法将任意长度的密码转换为256位密钥
        /// 这是一个确定性过程，相同的密码总是生成相同的密钥
        /// </summary>
        /// <param name="password">
        /// 用户提供的密码字符串
        /// 建议使用强密码（至少8位，包含大小写字母、数字和特殊字符）
        /// </param>
        /// <returns>
        /// 32字节（256位）的AES密钥
        /// 适用于AES-256加密算法
        /// </returns>
        /// <remarks>
        /// 安全考虑：
        /// • SHA256是单向哈希函数，无法从密钥反推密码
        /// • 相同密码总是生成相同密钥，确保解密一致性
        /// • 密钥长度固定为32字节，符合AES-256要求
        /// • 建议在生产环境中使用更强的密钥派生函数（如PBKDF2）
        /// </remarks>
        private static byte[] GenerateKey(string password)
        {
            // 使用SHA256哈希算法
            using var sha256 = SHA256.Create();

            // 将密码字符串转换为UTF-8字节数组，然后计算哈希
            return sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
        }

        #endregion

        // ==========================================
        // � 新增企业级功能 - 批量操作和高级安全
        // ==========================================

        #region 批量加密解密

        /// <summary>
        /// 批量加密目录中的所有文件
        /// 支持递归处理子目录，提供进度回调
        /// </summary>
        /// <param name="sourceDirectory">源目录路径</param>
        /// <param name="targetDirectory">目标目录路径</param>
        /// <param name="password">加密密码</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <returns>批量加密结果</returns>
        /// <example>
        /// <code>
        /// var result = encryption.BatchEncryptDirectory(
        ///     @"C:\Documents",
        ///     @"C:\Encrypted",
        ///     "password123",
        ///     recursive: true,
        ///     progress => Console.WriteLine($"进度: {progress.Percentage:F1}%")
        /// );
        /// Console.WriteLine($"成功: {result.SuccessCount}, 失败: {result.FailureCount}");
        /// </code>
        /// </example>
        public BatchOperationResult BatchEncryptDirectory(string sourceDirectory, string targetDirectory,
            string password, bool recursive = true, Action<BatchProgress>? progressCallback = null)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(sourceDirectory) || !Directory.Exists(sourceDirectory))
                throw new ArgumentException("源目录不存在或路径无效", nameof(sourceDirectory));

            if (string.IsNullOrWhiteSpace(targetDirectory))
                throw new ArgumentException("目标目录路径不能为空", nameof(targetDirectory));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("密码不能为空", nameof(password));

            // 创建目标目录
            Directory.CreateDirectory(targetDirectory);

            // 获取所有文件
            var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            var files = Directory.GetFiles(sourceDirectory, "*", searchOption);

            var result = new BatchOperationResult();
            result.TotalFiles = files.Length;
            result.StartTime = DateTime.Now;

            for (int i = 0; i < files.Length; i++)
            {
                var sourceFile = files[i];
                var relativePath = Path.GetRelativePath(sourceDirectory, sourceFile);
                var targetFile = Path.Combine(targetDirectory, relativePath + ".enc");

                // 确保目标文件的目录存在
                var targetDir = Path.GetDirectoryName(targetFile);
                if (!string.IsNullOrEmpty(targetDir))
                    Directory.CreateDirectory(targetDir);

                try
                {
                    var success = EncryptFile(sourceFile, targetFile, password);
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedFiles.Add(new FileOperationInfo
                        {
                            SourcePath = sourceFile,
                            TargetPath = targetFile,
                            Success = true,
                            FileSize = new FileInfo(sourceFile).Length
                        });
                    }
                    else
                    {
                        result.FailureCount++;
                        result.ProcessedFiles.Add(new FileOperationInfo
                        {
                            SourcePath = sourceFile,
                            TargetPath = targetFile,
                            Success = false,
                            ErrorMessage = "加密失败"
                        });
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.ProcessedFiles.Add(new FileOperationInfo
                    {
                        SourcePath = sourceFile,
                        TargetPath = targetFile,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }

                // 报告进度
                var progress = new BatchProgress
                {
                    ProcessedFiles = i + 1,
                    TotalFiles = files.Length,
                    Percentage = (double)(i + 1) / files.Length * 100,
                    CurrentFile = sourceFile
                };
                progressCallback?.Invoke(progress);
            }

            result.EndTime = DateTime.Now;
            result.Duration = result.EndTime - result.StartTime;

            return result;
        }

        /// <summary>
        /// 批量解密目录中的所有加密文件
        /// 支持递归处理子目录，提供进度回调
        /// </summary>
        /// <param name="sourceDirectory">加密文件目录路径</param>
        /// <param name="targetDirectory">解密输出目录路径</param>
        /// <param name="password">解密密码</param>
        /// <param name="recursive">是否递归处理子目录</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <returns>批量解密结果</returns>
        public BatchOperationResult BatchDecryptDirectory(string sourceDirectory, string targetDirectory,
            string password, bool recursive = true, Action<BatchProgress>? progressCallback = null)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(sourceDirectory) || !Directory.Exists(sourceDirectory))
                throw new ArgumentException("源目录不存在或路径无效", nameof(sourceDirectory));

            if (string.IsNullOrWhiteSpace(targetDirectory))
                throw new ArgumentException("目标目录路径不能为空", nameof(targetDirectory));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("密码不能为空", nameof(password));

            // 创建目标目录
            Directory.CreateDirectory(targetDirectory);

            // 获取所有.enc文件
            var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            var files = Directory.GetFiles(sourceDirectory, "*.enc", searchOption);

            var result = new BatchOperationResult();
            result.TotalFiles = files.Length;
            result.StartTime = DateTime.Now;

            for (int i = 0; i < files.Length; i++)
            {
                var sourceFile = files[i];
                var relativePath = Path.GetRelativePath(sourceDirectory, sourceFile);

                // 移除.enc扩展名
                if (relativePath.EndsWith(".enc", StringComparison.OrdinalIgnoreCase))
                    relativePath = relativePath[..^4];

                var targetFile = Path.Combine(targetDirectory, relativePath);

                // 确保目标文件的目录存在
                var targetDir = Path.GetDirectoryName(targetFile);
                if (!string.IsNullOrEmpty(targetDir))
                    Directory.CreateDirectory(targetDir);

                try
                {
                    var success = DecryptFile(sourceFile, targetFile, password);
                    if (success)
                    {
                        result.SuccessCount++;
                        result.ProcessedFiles.Add(new FileOperationInfo
                        {
                            SourcePath = sourceFile,
                            TargetPath = targetFile,
                            Success = true,
                            FileSize = new FileInfo(targetFile).Length
                        });
                    }
                    else
                    {
                        result.FailureCount++;
                        result.ProcessedFiles.Add(new FileOperationInfo
                        {
                            SourcePath = sourceFile,
                            TargetPath = targetFile,
                            Success = false,
                            ErrorMessage = "解密失败"
                        });
                    }
                }
                catch (Exception ex)
                {
                    result.FailureCount++;
                    result.ProcessedFiles.Add(new FileOperationInfo
                    {
                        SourcePath = sourceFile,
                        TargetPath = targetFile,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }

                // 报告进度
                var progress = new BatchProgress
                {
                    ProcessedFiles = i + 1,
                    TotalFiles = files.Length,
                    Percentage = (double)(i + 1) / files.Length * 100,
                    CurrentFile = sourceFile
                };
                progressCallback?.Invoke(progress);
            }

            result.EndTime = DateTime.Now;
            result.Duration = result.EndTime - result.StartTime;

            return result;
        }

        #endregion

        // ==========================================
        // 🔐 密码安全和验证功能
        // ==========================================

        #region 密码强度验证

        /// <summary>
        /// 验证密码强度
        /// 检查密码是否符合安全标准，包括长度、复杂度等要求
        /// </summary>
        /// <param name="password">要验证的密码</param>
        /// <returns>密码强度验证结果</returns>
        /// <example>
        /// <code>
        /// var validation = encryption.ValidatePasswordStrength("MyPassword123!");
        /// if (!validation.IsValid)
        /// {
        ///     Console.WriteLine($"密码问题: {string.Join(", ", validation.Issues)}");
        /// }
        /// Console.WriteLine($"密码强度: {validation.Strength}");
        /// </code>
        /// </example>
        public PasswordValidationResult ValidatePasswordStrength(string password)
        {
            var result = new PasswordValidationResult();

            if (string.IsNullOrEmpty(password))
            {
                result.Issues.Add("密码不能为空");
                result.Strength = PasswordStrength.VeryWeak;
                return result;
            }

            var score = 0;

            // 检查长度
            if (password.Length < 8)
                result.Issues.Add("密码长度至少需要8位");
            else if (password.Length >= 8)
                score += 1;

            if (password.Length >= 12)
                score += 1;

            if (password.Length >= 16)
                score += 1;

            // 检查字符类型
            var hasLower = password.Any(char.IsLower);
            var hasUpper = password.Any(char.IsUpper);
            var hasDigit = password.Any(char.IsDigit);
            var hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            if (!hasLower) result.Issues.Add("密码应包含小写字母");
            if (!hasUpper) result.Issues.Add("密码应包含大写字母");
            if (!hasDigit) result.Issues.Add("密码应包含数字");
            if (!hasSpecial) result.Issues.Add("密码应包含特殊字符");

            if (hasLower) score += 1;
            if (hasUpper) score += 1;
            if (hasDigit) score += 1;
            if (hasSpecial) score += 1;

            // 检查常见弱密码
            var commonPasswords = new[] { "password", "123456", "qwerty", "admin", "letmein" };
            if (commonPasswords.Any(p => password.ToLower().Contains(p)))
            {
                result.Issues.Add("密码包含常见弱密码模式");
                score -= 2;
            }

            // 检查重复字符
            var repeatedChars = password.GroupBy(c => c).Where(g => g.Count() > 3).Any();
            if (repeatedChars)
            {
                result.Issues.Add("密码包含过多重复字符");
                score -= 1;
            }

            // 确定强度等级
            result.Strength = score switch
            {
                <= 2 => PasswordStrength.VeryWeak,
                3 => PasswordStrength.Weak,
                4 => PasswordStrength.Medium,
                5 => PasswordStrength.Strong,
                >= 6 => PasswordStrength.VeryStrong,
            };

            result.IsValid = result.Issues.Count == 0;
            return result;
        }

        /// <summary>
        /// 生成强密码
        /// 根据指定的长度和字符集生成符合安全要求的随机密码
        /// </summary>
        /// <param name="length">密码长度，默认16位</param>
        /// <param name="includeSpecialChars">是否包含特殊字符，默认true</param>
        /// <returns>生成的强密码</returns>
        /// <example>
        /// <code>
        /// var strongPassword = encryption.GenerateStrongPassword(20, true);
        /// Console.WriteLine($"生成的强密码: {strongPassword}");
        /// </code>
        /// </example>
        public string GenerateStrongPassword(int length = 16, bool includeSpecialChars = true)
        {
            if (length < 8)
                throw new ArgumentException("密码长度不能少于8位", nameof(length));

            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string special = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            var chars = lowercase + uppercase + digits;
            if (includeSpecialChars)
                chars += special;

            using var rng = RandomNumberGenerator.Create();
            var password = new char[length];
            var charArray = chars.ToCharArray();

            // 确保至少包含每种字符类型
            password[0] = lowercase[GetRandomIndex(rng, lowercase.Length)];
            password[1] = uppercase[GetRandomIndex(rng, uppercase.Length)];
            password[2] = digits[GetRandomIndex(rng, digits.Length)];

            var startIndex = 3;
            if (includeSpecialChars)
            {
                password[3] = special[GetRandomIndex(rng, special.Length)];
                startIndex = 4;
            }

            // 填充剩余位置
            for (int i = startIndex; i < length; i++)
            {
                password[i] = charArray[GetRandomIndex(rng, charArray.Length)];
            }

            // 随机打乱顺序
            for (int i = 0; i < length; i++)
            {
                var j = GetRandomIndex(rng, length);
                (password[i], password[j]) = (password[j], password[i]);
            }

            return new string(password);
        }

        /// <summary>
        /// 获取随机索引
        /// 使用密码学安全的随机数生成器生成指定范围内的随机索引
        /// </summary>
        /// <param name="rng">随机数生成器实例</param>
        /// <param name="maxValue">最大值（不包含）</param>
        /// <returns>0到maxValue-1之间的随机整数</returns>
        private static int GetRandomIndex(RandomNumberGenerator rng, int maxValue)
        {
            if (maxValue <= 0)
                throw new ArgumentException("最大值必须大于0", nameof(maxValue));

            // 使用4字节生成随机数
            var bytes = new byte[4];
            rng.GetBytes(bytes);

            // 转换为无符号整数并取模
            var value = BitConverter.ToUInt32(bytes, 0);
            return (int)(value % (uint)maxValue);
        }

        #endregion

        // ==========================================
        // 🛡️ 文件完整性和安全验证
        // ==========================================

        #region 文件完整性验证

        /// <summary>
        /// 检查文件是否为有效的加密文件
        /// 通过验证文件头的魔数标识来判断
        /// </summary>
        /// <param name="filePath">要检查的文件路径</param>
        /// <returns>如果是有效的加密文件返回true，否则返回false</returns>
        /// <example>
        /// <code>
        /// if (encryption.IsEncryptedFile(@"C:\Files\document.enc"))
        /// {
        ///     Console.WriteLine("这是一个有效的加密文件");
        /// }
        /// </code>
        /// </example>
        public bool IsEncryptedFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                return false;

            try
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);

                // 检查文件是否足够大以包含魔数
                if (stream.Length < ENCRYPTION_MAGIC.Length)
                    return false;

                // 读取魔数
                var buffer = new byte[ENCRYPTION_MAGIC.Length];
                var bytesRead = stream.Read(buffer, 0, ENCRYPTION_MAGIC.Length);

                return bytesRead == ENCRYPTION_MAGIC.Length && buffer.SequenceEqual(ENCRYPTION_MAGIC);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取加密文件的信息
        /// 包括文件大小、创建时间等元数据
        /// </summary>
        /// <param name="encryptedFilePath">加密文件路径</param>
        /// <returns>加密文件信息</returns>
        /// <example>
        /// <code>
        /// var info = encryption.GetEncryptedFileInfo(@"C:\Files\document.enc");
        /// Console.WriteLine($"文件大小: {info.FileSize} 字节");
        /// Console.WriteLine($"创建时间: {info.CreatedAt}");
        /// </code>
        /// </example>
        public EncryptedFileInfo GetEncryptedFileInfo(string encryptedFilePath)
        {
            if (string.IsNullOrWhiteSpace(encryptedFilePath) || !File.Exists(encryptedFilePath))
                throw new ArgumentException("加密文件不存在或路径无效", nameof(encryptedFilePath));

            if (!IsEncryptedFile(encryptedFilePath))
                throw new ArgumentException("指定文件不是有效的加密文件", nameof(encryptedFilePath));

            var fileInfo = new FileInfo(encryptedFilePath);

            return new EncryptedFileInfo
            {
                FilePath = encryptedFilePath,
                FileSize = fileInfo.Length,
                CreatedAt = fileInfo.CreationTime,
                ModifiedAt = fileInfo.LastWriteTime,
                IsValid = true,
                HasMagicNumber = true,
                EstimatedOriginalSize = fileInfo.Length - ENCRYPTION_MAGIC.Length - AES_IV_SIZE
            };
        }

        /// <summary>
        /// 安全删除文件
        /// 多次覆写文件内容后删除，防止数据恢复
        /// </summary>
        /// <param name="filePath">要安全删除的文件路径</param>
        /// <param name="passes">覆写次数，默认3次</param>
        /// <returns>删除成功返回true，否则返回false</returns>
        /// <example>
        /// <code>
        /// var success = encryption.SecureDeleteFile(@"C:\Temp\sensitive.txt", 5);
        /// if (success)
        /// {
        ///     Console.WriteLine("文件已安全删除");
        /// }
        /// </code>
        /// </example>
        public bool SecureDeleteFile(string filePath, int passes = 3)
        {
            if (string.IsNullOrWhiteSpace(filePath) || !File.Exists(filePath))
                return false;

            if (passes < 1)
                throw new ArgumentException("覆写次数必须大于0", nameof(passes));

            try
            {
                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Write))
                {
                    using var rng = RandomNumberGenerator.Create();

                    for (int pass = 0; pass < passes; pass++)
                    {
                        stream.Seek(0, SeekOrigin.Begin);

                        // 使用不同的模式覆写
                        byte fillByte = pass switch
                        {
                            0 => 0x00, // 全零
                            1 => 0xFF, // 全一
                            _ => (byte)GetRandomIndex(rng, 256) // 随机
                        };

                        var buffer = new byte[ENCRYPTION_BUFFER_SIZE];
                        Array.Fill(buffer, fillByte);

                        long remaining = fileSize;
                        while (remaining > 0)
                        {
                            var toWrite = (int)Math.Min(remaining, buffer.Length);

                            if (pass >= 2) // 最后几次使用随机数据
                                rng.GetBytes(buffer, 0, toWrite);

                            stream.Write(buffer, 0, toWrite);
                            remaining -= toWrite;
                        }

                        stream.Flush();
                    }
                }

                // 删除文件
                File.Delete(filePath);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"安全删除文件失败: {ex.Message}");
                return false;
            }
        }

        #endregion

    }

    // ==========================================
    // 📋 枚举和数据模型定义
    // ==========================================

    /// <summary>
    /// 支持的哈希算法类型
    /// 用于文件完整性验证和数字指纹计算
    /// </summary>
    public enum HashAlgorithmType
    {
        /// <summary>
        /// MD5算法（128位哈希）
        /// 速度快但安全性较低，适用于文件完整性检查
        /// 不推荐用于安全敏感的应用
        /// </summary>
        MD5,

        /// <summary>
        /// SHA256算法（256位哈希）
        /// 目前推荐的安全哈希算法，平衡了安全性和性能
        /// 适用于大多数安全应用场景
        /// </summary>
        SHA256,

        /// <summary>
        /// SHA512算法（512位哈希）
        /// 最高安全级别的哈希算法，但计算速度较慢
        /// 适用于对安全性要求极高的场景
        /// </summary>
        SHA512
    }

    /// <summary>
    /// 密码强度等级
    /// </summary>
    public enum PasswordStrength
    {
        /// <summary>
        /// 非常弱 - 不建议使用
        /// </summary>
        VeryWeak,

        /// <summary>
        /// 弱 - 安全性较低
        /// </summary>
        Weak,

        /// <summary>
        /// 中等 - 基本安全要求
        /// </summary>
        Medium,

        /// <summary>
        /// 强 - 推荐使用
        /// </summary>
        Strong,

        /// <summary>
        /// 非常强 - 最高安全级别
        /// </summary>
        VeryStrong
    }

    /// <summary>
    /// 密码验证结果
    /// </summary>
    public class PasswordValidationResult
    {
        /// <summary>
        /// 密码是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 密码强度等级
        /// </summary>
        public PasswordStrength Strength { get; set; }

        /// <summary>
        /// 密码问题列表
        /// </summary>
        public List<string> Issues { get; set; } = new List<string>();
    }

    /// <summary>
    /// 批量操作结果
    /// </summary>
    public class BatchOperationResult
    {
        /// <summary>
        /// 总文件数
        /// </summary>
        public int TotalFiles { get; set; }

        /// <summary>
        /// 成功处理的文件数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败的文件数
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 处理的文件详细信息
        /// </summary>
        public List<FileOperationInfo> ProcessedFiles { get; set; } = new List<FileOperationInfo>();

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 处理的总字节数
        /// </summary>
        public long TotalBytesProcessed { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public double SuccessRate => TotalFiles > 0 ? (double)SuccessCount / TotalFiles * 100 : 0;
    }

    /// <summary>
    /// 文件操作信息
    /// </summary>
    public class FileOperationInfo
    {
        /// <summary>
        /// 源文件路径
        /// </summary>
        public string SourcePath { get; set; } = string.Empty;

        /// <summary>
        /// 目标文件路径
        /// </summary>
        public string TargetPath { get; set; } = string.Empty;

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime ProcessedAt { get; set; }
    }

    /// <summary>
    /// 批量操作进度信息
    /// </summary>
    public class BatchProgress
    {
        /// <summary>
        /// 已处理文件数
        /// </summary>
        public int ProcessedFiles { get; set; }

        /// <summary>
        /// 总文件数
        /// </summary>
        public int TotalFiles { get; set; }

        /// <summary>
        /// 完成百分比
        /// </summary>
        public double Percentage { get; set; }

        /// <summary>
        /// 当前处理的文件
        /// </summary>
        public string CurrentFile { get; set; } = string.Empty;

        /// <summary>
        /// 已用时间
        /// </summary>
        public TimeSpan ElapsedTime { get; set; }

        /// <summary>
        /// 预估剩余时间
        /// </summary>
        public TimeSpan EstimatedTimeRemaining
        {
            get
            {
                if (ProcessedFiles == 0 || Percentage >= 100)
                    return TimeSpan.Zero;

                var avgTimePerFile = ElapsedTime.TotalMilliseconds / ProcessedFiles;
                var remainingFiles = TotalFiles - ProcessedFiles;
                return TimeSpan.FromMilliseconds(avgTimePerFile * remainingFiles);
            }
        }
    }

    /// <summary>
    /// 加密文件信息
    /// </summary>
    public class EncryptedFileInfo
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifiedAt { get; set; }

        /// <summary>
        /// 是否为有效的加密文件
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 是否包含魔数标识
        /// </summary>
        public bool HasMagicNumber { get; set; }

        /// <summary>
        /// 预估原始文件大小
        /// </summary>
        public long EstimatedOriginalSize { get; set; }
    }
}
