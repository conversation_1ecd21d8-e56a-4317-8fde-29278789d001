# YFileEncryption - 企业级文件加密和安全工具类

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🔐 **军用级文件加密解决方案** - 提供完整的AES-256文件加密、哈希计算、数字签名和批量处理功能

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [批量操作](#-批量操作)
- [密码安全](#-密码安全)
- [哈希计算](#-哈希计算)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **🔐 核心加密功能**

- ✅ **AES-256-CBC 加密**: 军用级加密强度，业界标准算法
- ✅ **流式处理**: 支持任意大小文件，内存友好型设计
- ✅ **异步操作**: 完整的async/await支持，高并发场景优化
- ✅ **安全密钥管理**: SHA256密钥派生，随机IV生成

### **📊 哈希计算功能**

- ✅ **多算法支持**: MD5、SHA256、SHA512哈希计算
- ✅ **文件完整性验证**: 哈希值比较和验证
- ✅ **大文件优化**: 流式哈希计算，支持GB级文件

### **🔒 密码安全功能**

- ✅ **密码强度验证**: 智能密码复杂度检查
- ✅ **安全评级**: 弱、中、强、很强四级评估
- ✅ **常见密码检测**: 防止使用弱密码模式

### **📦 批量处理功能**

- ✅ **目录批量加密**: 递归处理整个目录结构
- ✅ **批量解密**: 自动识别加密文件并解密
- ✅ **进度回调**: 实时处理进度和统计信息
- ✅ **错误处理**: 详细的失败文件信息和错误报告

### **🗑️ 安全删除功能**

- ✅ **多次覆写**: 防止数据恢复的安全删除
- ✅ **自定义覆写次数**: 可配置的安全级别
- ✅ **随机数据填充**: 使用密码学安全随机数

### **🔍 文件验证功能**

- ✅ **加密文件识别**: 自动检测加密文件格式
- ✅ **文件信息获取**: 加密文件元数据读取
- ✅ **完整性检查**: 文件格式和结构验证

### **🛡️ 安全特性**

- ✅ **参数验证**: 严格的输入验证和错误处理
- ✅ **异常安全**: 优雅的错误处理和资源清理
- ✅ **内存安全**: 敏感数据自动清零
- ✅ **跨平台**: Windows、Linux、macOS 兼容

## 🚀 **快速开始**

### **安装**

```csharp
// 通过 NuGet 包管理器安装
Install-Package Zylo.YIO

// 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Security;

var encryption = new YFileEncryption();

// 加密文件
bool success = encryption.EncryptFile(
    @"C:\Documents\secret.txt",
    @"C:\Encrypted\secret.enc",
    "MyStrongPassword123!"
);

// 解密文件
bool decrypted = encryption.DecryptFile(
    @"C:\Encrypted\secret.enc",
    @"C:\Decrypted\secret.txt",
    "MyStrongPassword123!"
);

// 验证密码强度
var validation = encryption.ValidatePasswordStrength("MyPassword123!");
Console.WriteLine($"密码强度: {validation.Strength}");
```

## 🔧 **核心功能**

### **文件加密和解密**

```csharp
var encryption = new YFileEncryption();

// 同步加密
bool encrypted = encryption.EncryptFile(
    inputFile: @"C:\sensitive.docx",
    outputFile: @"C:\encrypted.enc",
    password: "StrongPassword123!"
);

// 异步加密（推荐用于大文件）
bool asyncEncrypted = await encryption.EncryptFileAsync(
    @"C:\large_video.mp4",
    @"C:\encrypted_video.enc",
    "StrongPassword123!"
);

// 同步解密
bool decrypted = encryption.DecryptFile(
    @"C:\encrypted.enc",
    @"C:\decrypted.docx",
    "StrongPassword123!"
);

// 异步解密
bool asyncDecrypted = await encryption.DecryptFileAsync(
    @"C:\encrypted_video.enc",
    @"C:\decrypted_video.mp4",
    "StrongPassword123!"
);
```

### **哈希计算和验证**

```csharp
// 计算文件哈希
string md5Hash = encryption.CalculateMD5(@"C:\file.txt");
string sha256Hash = encryption.CalculateSHA256(@"C:\file.txt");
string sha512Hash = encryption.CalculateSHA512(@"C:\file.txt");

// 验证文件完整性
bool isValid = encryption.VerifyFileHash(
    @"C:\file.txt",
    "expected_hash_value",
    HashAlgorithmType.SHA256
);

Console.WriteLine($"文件完整性验证: {(isValid ? "通过" : "失败")}");
```

## 📦 **批量操作**

### **批量加密目录**

```csharp
// 批量加密整个目录
var result = encryption.BatchEncryptDirectory(
    sourceDirectory: @"C:\Documents",
    targetDirectory: @"C:\Encrypted",
    password: "BatchPassword123!",
    recursive: true,
    progressCallback: progress => {
        Console.WriteLine($"进度: {progress.Percentage:F1}% - {progress.CurrentFile}");
    }
);

// 检查结果
Console.WriteLine($"""
批量加密完成:
- 总文件数: {result.TotalFiles}
- 成功加密: {result.SuccessCount}
- 失败文件: {result.FailureCount}
- 成功率: {result.SuccessRate:F1}%
- 总耗时: {result.Duration}
""");
```

### **批量解密目录**

```csharp
// 批量解密目录中的所有.enc文件
var decryptResult = encryption.BatchDecryptDirectory(
    sourceDirectory: @"C:\Encrypted",
    targetDirectory: @"C:\Decrypted",
    password: "BatchPassword123!",
    recursive: true,
    progressCallback: progress => {
        Console.WriteLine($"解密进度: {progress.Percentage:F1}%");
    }
);

// 处理失败的文件
foreach (var file in decryptResult.ProcessedFiles.Where(f => !f.Success))
{
    Console.WriteLine($"解密失败: {file.SourcePath} - {file.ErrorMessage}");
}
```

## 🔒 **密码安全**

### **密码强度验证**

```csharp
// 验证密码强度
var validation = encryption.ValidatePasswordStrength("MyPassword123!");

if (!validation.IsValid)
{
    Console.WriteLine("密码不符合安全要求:");
    foreach (var issue in validation.Issues)
    {
        Console.WriteLine($"- {issue}");
    }
}

Console.WriteLine($"密码强度等级: {validation.Strength}");
// 输出: 密码强度等级: Strong
```

### **密码强度等级**

| 等级 | 描述 | 要求 |
|------|------|------|
| `VeryWeak` | 非常弱 | 长度不足或过于简单 |
| `Weak` | 弱 | 基本要求但缺乏复杂性 |
| `Medium` | 中等 | 包含多种字符类型 |
| `Strong` | 强 | 长度充足且复杂度高 |

### **密码安全建议**

```csharp
// ✅ 推荐的强密码示例
var strongPasswords = new[]
{
    "MySecureKey2024!@#",
    "P@ssw0rd#2024$Safe",
    "Encrypt!My#Files$2024"
};

// ❌ 避免的弱密码模式
var weakPasswords = new[]
{
    "password123",    // 包含常见词汇
    "12345678",       // 纯数字
    "abcdefgh",       // 纯字母
    "Password"        // 缺乏复杂性
};
```

## 🔍 **文件验证功能**

### **加密文件检测**

```csharp
// 检查文件是否为加密文件
bool isEncrypted = encryption.IsEncryptedFile(@"C:\file.enc");

// 获取加密文件详细信息
var fileInfo = encryption.GetEncryptedFileInfo(@"C:\file.enc");
Console.WriteLine($"""
加密文件信息:
- 文件大小: {fileInfo.FileSize} 字节
- 创建时间: {fileInfo.CreatedAt}
- 是否有效: {fileInfo.IsValid}
- 预估原始大小: {fileInfo.EstimatedOriginalSize} 字节
""");
```

### **安全删除**

```csharp
// 安全删除敏感文件（默认3次覆写）
bool deleted = encryption.SecureDeleteFile(@"C:\sensitive.txt");

// 自定义覆写次数（更高安全级别）
bool secureDeleted = encryption.SecureDeleteFile(
    @"C:\top_secret.txt",
    passes: 7  // 7次覆写
);

if (secureDeleted)
{
    Console.WriteLine("文件已安全删除，无法恢复");
}
```

## 📊 **完整功能函数汇总表格**

### **🔐 核心加密解密方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `EncryptFile` | `string inputFilePath, string outputFilePath, string password` | `bool` | 使用AES-256-CBC模式同步加密文件 |
| `DecryptFile` | `string inputFilePath, string outputFilePath, string password` | `bool` | 同步解密文件，验证格式和密码 |
| `EncryptFileAsync` | `string inputFilePath, string outputFilePath, string password` | `Task<bool>` | 异步加密文件，适合大文件处理 |
| `DecryptFileAsync` | `string inputFilePath, string outputFilePath, string password` | `Task<bool>` | 异步解密文件，高性能版本 |

### **📊 哈希计算方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `CalculateMD5` | `string filePath` | `string` | 计算文件MD5哈希值（十六进制字符串） |
| `CalculateSHA256` | `string filePath` | `string` | 计算文件SHA256哈希值 |
| `CalculateSHA512` | `string filePath` | `string` | 计算文件SHA512哈希值 |
| `VerifyFileHash` | `string filePath, string expectedHash, HashAlgorithmType algorithm` | `bool` | 验证文件哈希值完整性 |

**支持的哈希算法 (HashAlgorithmType 枚举):**

- `MD5` - MD5算法（128位）
- `SHA256` - SHA256算法（256位，推荐）
- `SHA512` - SHA512算法（512位，最高安全性）

### **📦 批量处理方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `BatchEncryptDirectory` | `string sourceDir, string targetDir, string password, bool recursive, Action<BatchProgress> progressCallback` | `BatchOperationResult` | 批量加密目录中的所有文件 |
| `BatchDecryptDirectory` | `string sourceDir, string targetDir, string password, bool recursive, Action<BatchProgress> progressCallback` | `BatchOperationResult` | 批量解密目录中的.enc文件 |

**BatchOperationResult 属性:**

- `int TotalFiles` - 总文件数量
- `int SuccessCount` - 成功处理的文件数
- `int FailureCount` - 失败的文件数
- `double SuccessRate` - 成功率百分比
- `DateTime StartTime` - 开始时间
- `DateTime EndTime` - 结束时间
- `TimeSpan Duration` - 处理耗时
- `List<ProcessedFileInfo> ProcessedFiles` - 详细的文件处理信息

**BatchProgress 属性:**

- `int TotalFiles` - 总文件数
- `int ProcessedFiles` - 已处理文件数
- `double Percentage` - 完成百分比
- `string CurrentFile` - 当前处理的文件路径

### **🔒 密码安全方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `ValidatePasswordStrength` | `string password` | `PasswordValidationResult` | 验证密码强度和安全性 |

**PasswordValidationResult 属性:**

- `bool IsValid` - 密码是否符合安全要求
- `PasswordStrength Strength` - 密码强度等级
- `List<string> Issues` - 密码问题列表

**PasswordStrength 枚举:**

- `VeryWeak` - 非常弱（不推荐使用）
- `Weak` - 弱（基本要求）
- `Medium` - 中等（推荐最低标准）
- `Strong` - 强（推荐使用）

### **🔍 文件验证方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `IsEncryptedFile` | `string filePath` | `bool` | 检查文件是否为有效的加密文件 |
| `GetEncryptedFileInfo` | `string filePath` | `EncryptedFileInfo` | 获取加密文件的详细信息 |

**EncryptedFileInfo 属性:**

- `string FilePath` - 文件路径
- `long FileSize` - 文件大小（字节）
- `DateTime CreatedAt` - 创建时间
- `DateTime ModifiedAt` - 修改时间
- `bool IsValid` - 是否为有效的加密文件
- `bool HasMagicNumber` - 是否包含魔数标识
- `long EstimatedOriginalSize` - 预估原始文件大小

### **🗑️ 安全删除方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `SecureDeleteFile` | `string filePath, int passes = 3` | `bool` | 安全删除文件，多次覆写防止恢复 |

**安全删除覆写模式:**

1. **第一次**: 全零覆写 (0x00)
2. **第二次**: 全一覆写 (0xFF)
3. **后续次数**: 密码学安全随机数据覆写

## 🧪 **测试覆盖**

### **单元测试类别**

| 测试类别 | 覆盖功能 | 测试数量 |
|----------|----------|----------|
| **FileEncryption** | 基础加密解密功能 | 15+ |
| **AsyncOperations** | 异步加密解密 | 8+ |
| **HashCalculation** | 哈希计算和验证 | 12+ |
| **BatchOperations** | 批量处理功能 | 10+ |
| **PasswordValidation** | 密码强度验证 | 15+ |
| **FileValidation** | 文件格式验证 | 8+ |
| **SecureDelete** | 安全删除功能 | 6+ |
| **ErrorHandling** | 异常处理 | 20+ |

### **测试示例**

```csharp
[Fact]
[Trait("Category", "FileEncryption")]
public void EncryptFile_ValidInput_ShouldSucceed()
{
    // Arrange
    var testFile = CreateTestFile("Hello World");
    var encryptedFile = GetTempFilePath(".enc");

    // Act
    var result = _encryption.EncryptFile(testFile, encryptedFile, TestPassword);

    // Assert
    Assert.True(result);
    Assert.True(File.Exists(encryptedFile));
    Assert.True(_encryption.IsEncryptedFile(encryptedFile));
}
```

## 🛡️ **最佳实践**

### **密码安全建议**

```csharp
// ✅ 推荐做法
var strongPassword = "MySecureKey2024!@#";
var validation = encryption.ValidatePasswordStrength(strongPassword);
if (!validation.IsValid)
{
    throw new ArgumentException($"密码不安全: {string.Join(", ", validation.Issues)}");
}

// ✅ 使用异步方法处理大文件
await encryption.EncryptFileAsync(largeFilePath, encryptedPath, strongPassword);
```

### **错误处理模式**

```csharp
try
{
    var success = encryption.EncryptFile(inputFile, outputFile, password);
    if (!success)
    {
        Console.WriteLine("加密失败，请检查文件路径和权限");
    }
}
catch (UnauthorizedAccessException)
{
    Console.WriteLine("文件访问权限不足");
}
catch (FileNotFoundException)
{
    Console.WriteLine("源文件不存在");
}
catch (Exception ex)
{
    Console.WriteLine($"加密过程中发生错误: {ex.Message}");
}
```

### **性能优化建议**

```csharp
// ✅ 大文件使用异步方法
if (new FileInfo(filePath).Length > 10 * 1024 * 1024) // 10MB
{
    await encryption.EncryptFileAsync(filePath, encryptedPath, password);
}
else
{
    encryption.EncryptFile(filePath, encryptedPath, password);
}

// ✅ 批量操作使用进度回调
var result = encryption.BatchEncryptDirectory(
    sourceDir, targetDir, password, true,
    progress => {
        if (progress.ProcessedFiles % 10 == 0) // 每10个文件报告一次
        {
            Console.WriteLine($"已处理: {progress.ProcessedFiles}/{progress.TotalFiles}");
        }
    }
);
```

### **安全注意事项**

1. **密码管理**: 不要在代码中硬编码密码
2. **文件清理**: 处理完成后及时删除临时文件
3. **权限控制**: 确保加密文件的适当访问权限
4. **备份策略**: 加密前确保有可靠的备份
5. **密钥安全**: 妥善保管加密密码，丢失无法恢复

### **跨平台兼容性**

```csharp
// ✅ 使用Path.Combine确保路径兼容性
var outputPath = Path.Combine(targetDirectory, "encrypted_file.enc");

// ✅ 处理不同平台的文件权限
if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) ||
    RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
{
    // Unix系统特定处理
}
```

---

## 📚 **相关文档**

- [YDirectoryOperations README](../Core/YDirectoryOperations_README.md) - 目录操作工具
- [YFileOperations README](../Core/YFileOperations_README.md) - 文件操作工具
- [YFileValidator README](YFileValidator_README.md) - 文件验证工具
- [YSecureDelete README](YSecureDelete_README.md) - 安全删除工具

## 🤝 **贡献指南**

欢迎提交 Issue 和 Pull Request 来改进 YFileEncryption！

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
