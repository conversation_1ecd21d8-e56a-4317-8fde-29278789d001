using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Text;

using Zylo.YIO.Config;

namespace Zylo.YIO.Security
{
    /// <summary>
    /// YFileValidator - 企业级文件验证和安全检查工具类
    ///
    /// 🔍 核心功能特性：
    /// • 文件安全性检查：扩展名验证、文件签名检测、路径安全验证
    /// • 格式验证：图片、文档、压缩包等常见格式的深度验证
    /// • 恶意软件检测：基于文件签名和行为模式的安全扫描
    /// • 批量验证：目录级别的文件安全扫描和报告生成
    /// • MIME类型识别：准确的文件类型检测和分类
    /// • 配置化安全策略：支持自定义安全规则和白名单机制
    ///
    /// 💡 设计原则：
    /// • 安全第一：多层次验证机制，防止恶意文件绕过检测
    /// • 性能优化：智能缓存和流式处理，支持大规模文件扫描
    /// • 可扩展性：模块化设计，支持自定义验证规则和插件
    /// • 易用性：简洁的API设计，支持链式调用和批量操作
    /// • 向后兼容：保持API稳定性，支持版本升级
    ///
    /// 📋 使用场景：
    /// • 文件上传安全：Web应用文件上传的安全验证
    /// • 系统安全扫描：定期扫描系统中的潜在威胁文件
    /// • 数据完整性检查：验证文件格式和内容的一致性
    /// • 合规性检查：满足企业安全策略和法规要求
    /// • 自动化安全流程：集成到CI/CD流水线中的安全检查
    /// </summary>
  

    public partial class YFileValidator
    {
        // ==========================================
        // 🔧 私有字段和常量定义
        // ==========================================

        #region 私有字段和常量

        /// <summary>
        /// YIO框架配置实例
        /// 包含文件大小限制、安全策略、阻止扩展名等配置项
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// 文件签名检测的最大读取字节数
        /// 用于优化性能，避免读取过多不必要的文件内容
        /// </summary>
        private const int MAX_SIGNATURE_BYTES = 32;

        /// <summary>
        /// Windows系统路径长度限制
        /// 传统Windows API的路径长度限制为260字符
        /// </summary>
        private const int MAX_PATH_LENGTH = 260;

        /// <summary>
        /// 危险文件扩展名集合
        /// 包含可执行文件、脚本文件、系统文件等潜在威胁类型
        /// 使用HashSet提供O(1)查找性能，忽略大小写比较
        /// </summary>
        private static readonly HashSet<string> DangerousExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            // 可执行文件类型 - 最高风险
            ".exe", ".com", ".scr", ".pif", ".msi",

            // 脚本文件类型 - 高风险
            ".bat", ".cmd", ".vbs", ".js", ".ps1", ".psm1", ".psd1", ".ps1xml", ".psc1", ".pssc",

            // 系统文件类型 - 高风险
            ".dll", ".sys", ".drv", ".ocx", ".cpl",

            // 配置和注册表文件 - 中等风险
            ".inf", ".reg",

            // Java相关可执行文件 - 中等风险
            ".jar"
        };

        /// <summary>
        /// 安全文件扩展名集合
        /// 包含常见的文档、图片、音视频等相对安全的文件类型
        /// 这些文件类型通常不包含可执行代码
        /// </summary>
        private static readonly HashSet<string> SafeExtensions = new(StringComparer.OrdinalIgnoreCase)
        {
            // 文档文件类型
            ".txt", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".rtf",

            // 图片文件类型
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp", ".svg", ".ico",

            // 音频文件类型
            ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma",

            // 视频文件类型
            ".mp4", ".avi", ".mov", ".wmv", ".mkv", ".flv", ".webm",

            // 压缩文件类型（需要额外验证）
            ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",

            // 数据文件类型
            ".json", ".xml", ".csv", ".ini", ".yaml", ".yml", ".toml"
        };

        /// <summary>
        /// 文件签名（魔数）字典
        /// 用于验证文件的真实格式，防止扩展名伪装攻击
        /// 每种文件格式都有独特的文件头标识
        /// </summary>
        private static readonly Dictionary<string, byte[]> FileSignatures = new()
        {
            // 文档格式签名
            { ".pdf", new byte[] { 0x25, 0x50, 0x44, 0x46 } },                                    // %PDF

            // 图片格式签名
            { ".jpg", new byte[] { 0xFF, 0xD8, 0xFF } },                                          // JPEG
            { ".png", new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A } },          // PNG
            { ".gif", new byte[] { 0x47, 0x49, 0x46, 0x38 } },                                   // GIF8
            { ".bmp", new byte[] { 0x42, 0x4D } },                                               // BM

            // 压缩格式签名
            { ".zip", new byte[] { 0x50, 0x4B, 0x03, 0x04 } },                                   // PK..
            { ".rar", new byte[] { 0x52, 0x61, 0x72, 0x21, 0x1A, 0x07 } },                      // Rar!
            { ".7z", new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C } },                       // 7z

            // 可执行文件签名（用于检测伪装）
            { ".exe", new byte[] { 0x4D, 0x5A } },                                               // MZ
            { ".dll", new byte[] { 0x4D, 0x5A } },                                               // MZ

            // 音频格式签名
            { ".mp3", new byte[] { 0xFF, 0xFB } },                                               // MP3
            { ".wav", new byte[] { 0x52, 0x49, 0x46, 0x46 } },                                   // RIFF

            // 视频格式签名
            { ".mp4", new byte[] { 0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70 } },          // MP4
            { ".avi", new byte[] { 0x52, 0x49, 0x46, 0x46 } }                                    // RIFF (AVI)
        };

        #endregion

        // ==========================================
        // 🏗️ 构造函数和初始化
        // ==========================================

        #region 构造函数

        /// <summary>
        /// 初始化 YFileValidator 实例
        /// 创建一个配置完整的文件验证器，支持自定义安全策略
        /// </summary>
        /// <param name="config">
        /// YIO框架配置实例，包含：
        /// • 文件大小限制配置
        /// • 安全策略设置
        /// • 阻止扩展名列表
        /// • 路径验证规则
        /// 如果为null，将使用默认配置
        /// </param>
        /// <example>
        /// <code>
        /// // 使用自定义配置创建验证器
        /// var config = new YIOConfig
        /// {
        ///     MaxSingleFileSize = 50 * 1024 * 1024, // 50MB
        ///     BlockedExtensions = new[] { ".exe", ".bat", ".cmd" }
        /// };
        /// var validator = new YFileValidator(config);
        ///
        /// // 验证文件安全性
        /// var isSafe = validator.IsSafeFile(@"C:\uploads\document.pdf");
        /// </code>
        /// </example>
        public YFileValidator(YIOConfig config)
        {
            // 确保配置不为null，提供默认配置作为后备
            _config = config ?? new YIOConfig();

            // 输出初始化日志
            Console.WriteLine($"YFileValidator 初始化完成 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"最大文件大小限制: {_config.MaxSingleFileSize / (1024 * 1024)} MB");
            Console.WriteLine($"阻止扩展名数量: {_config.BlockedExtensions?.Length ?? 0}");
        }

        /// <summary>
        /// 无参构造函数 - 使用默认配置初始化
        /// 适用于快速创建验证器实例，使用框架默认安全配置
        /// 主要用于静态方法调用和简单场景
        /// </summary>
        /// <example>
        /// <code>
        /// // 快速创建验证器
        /// var validator = new YFileValidator();
        ///
        /// // 或使用静态方法（由YStatic属性自动生成）
        /// var isSafe = YFileValidator.IsSafeFile(@"C:\temp\file.txt");
        /// </code>
        /// </example>
        public YFileValidator() : this(new YIOConfig())
        {
            // 调用主构造函数，使用默认配置
            // 这种设计模式确保所有初始化逻辑集中在主构造函数中
        }

        #endregion

        // ==========================================
        // 🔒 核心安全检查方法
        // ==========================================

        #region 文件安全性检查

        /// <summary>
        /// 综合检查文件是否安全
        /// 这是主要的安全验证入口点，执行多层次的安全检查
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 支持相对路径和绝对路径
        /// </param>
        /// <returns>
        /// 文件通过所有安全检查返回true，否则返回false
        /// 检查失败的原因会输出到控制台
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查上传文件的安全性
        /// if (validator.IsSafeFile(@"C:\uploads\document.pdf"))
        /// {
        ///     Console.WriteLine("文件安全，可以处理");
        /// }
        /// else
        /// {
        ///     Console.WriteLine("文件存在安全风险，拒绝处理");
        /// }
        /// </code>
        /// </example>
        public bool IsSafeFile(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                // 检查路径是否有效和文件是否存在
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文件路径为空或无效");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文件不存在: {filePath}");
                    return false;
                }

                // === 第二步：扩展名安全检查 ===
                // 检查文件扩展名是否在危险列表中
                if (!IsExtensionSafe(filePath))
                {
                    Console.WriteLine($"文件扩展名不安全: {Path.GetExtension(filePath)}");
                    return false;
                }

                // === 第三步：文件大小验证 ===
                // 检查文件大小是否超过配置限制
                if (!IsFileSizeValid(filePath))
                {
                    Console.WriteLine($"文件大小验证失败: {filePath}");
                    return false;
                }

                // === 第四步：文件签名验证 ===
                // 检查文件头部签名是否与扩展名匹配
                if (!IsFileSignatureValid(filePath))
                {
                    Console.WriteLine($"文件签名验证失败: {filePath}");
                    return false;
                }

                // === 第五步：路径安全检查 ===
                // 检查路径是否包含恶意字符或路径遍历攻击
                if (!IsPathSafe(filePath))
                {
                    Console.WriteLine($"文件路径不安全: {filePath}");
                    return false;
                }

                // === 第六步：配置策略检查 ===
                // 检查是否符合用户自定义的安全策略
                if (_config.EnablePathValidation && !IsPathCompliantWithPolicy(filePath))
                {
                    Console.WriteLine($"文件路径不符合安全策略: {filePath}");
                    return false;
                }

                // 所有检查通过
                return true;
            }
            catch (Exception ex)
            {
                // 捕获所有异常，确保方法不会抛出未处理的异常
                Console.WriteLine($"文件安全检查过程中发生异常: {ex.Message}");
                Console.WriteLine($"异常详情: {ex}");

                // 出现异常时，为了安全起见，返回false
                return false;
            }
        }

        /// <summary>
        /// 检查文件扩展名是否安全
        /// 通过多层次验证确保文件扩展名不在危险列表中
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 可以是完整路径或仅文件名
        /// </param>
        /// <returns>
        /// 扩展名安全返回true，危险或被阻止返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查不同类型的文件
        /// bool pdfSafe = validator.IsExtensionSafe("document.pdf");     // true
        /// bool exeSafe = validator.IsExtensionSafe("malware.exe");      // false
        /// bool txtSafe = validator.IsExtensionSafe("readme.txt");       // true
        /// </code>
        /// </example>
        public bool IsExtensionSafe(string filePath)
        {
            // === 第一步：基础验证 ===
            if (string.IsNullOrWhiteSpace(filePath))
            {
                Console.WriteLine("文件路径为空，无法检查扩展名");
                return false;
            }

            // === 第二步：提取扩展名 ===
            var extension = Path.GetExtension(filePath);

            // 处理没有扩展名的文件
            if (string.IsNullOrEmpty(extension))
            {
                Console.WriteLine($"文件没有扩展名: {filePath}");
                // 根据配置决定是否允许无扩展名文件
                return !_config.EnablePathValidation; // 如果启用严格验证，则拒绝无扩展名文件
            }

            // 转换为小写进行比较
            extension = extension.ToLowerInvariant();

            // === 第三步：危险扩展名检查 ===
            // 检查是否在预定义的危险扩展名列表中
            if (DangerousExtensions.Contains(extension))
            {
                Console.WriteLine($"检测到危险文件扩展名: {extension}");
                return false;
            }

            // === 第四步：配置阻止列表检查 ===
            // 检查用户自定义的阻止扩展名列表
            if (_config.BlockedExtensions?.Contains(extension, StringComparer.OrdinalIgnoreCase) == true)
            {
                Console.WriteLine($"文件扩展名在阻止列表中: {extension}");
                return false;
            }

            // === 第五步：双扩展名检查 ===
            // 检查是否存在双扩展名伪装（如 file.txt.exe）
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            if (!string.IsNullOrEmpty(fileName))
            {
                var secondExtension = Path.GetExtension(fileName);
                if (!string.IsNullOrEmpty(secondExtension) &&
                    DangerousExtensions.Contains(secondExtension.ToLowerInvariant()))
                {
                    Console.WriteLine($"检测到双扩展名伪装: {secondExtension}{extension}");
                    return false;
                }
            }

            // 扩展名检查通过
            return true;
        }

        /// <summary>
        /// 检查文件大小是否符合安全策略
        /// 验证文件大小是否在允许的范围内，防止资源耗尽攻击
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 必须是存在的有效文件路径
        /// </param>
        /// <returns>
        /// 文件大小符合要求返回true，否则返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查文件大小
        /// if (validator.IsFileSizeValid(@"C:\uploads\large_file.zip"))
        /// {
        ///     Console.WriteLine("文件大小符合要求");
        /// }
        /// else
        /// {
        ///     Console.WriteLine("文件过大或为空");
        /// }
        /// </code>
        /// </example>
        public bool IsFileSizeValid(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文件路径为空，无法检查大小");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文件不存在，无法检查大小: {filePath}");
                    return false;
                }

                // === 第二步：获取文件信息 ===
                var fileInfo = new FileInfo(filePath);
                var fileSizeBytes = fileInfo.Length;
                var fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);

                // === 第三步：空文件检查 ===
                // 检查是否为空文件（可能是恶意文件或损坏文件）
                if (fileSizeBytes == 0)
                {
                    Console.WriteLine($"检测到空文件: {filePath}");
                    return false;
                }

                // === 第四步：最大大小限制检查 ===
                // 检查文件是否超过配置的最大大小限制
                if (fileSizeBytes > _config.MaxSingleFileSize)
                {
                    Console.WriteLine($"文件过大: {fileSizeMB:F2} MB，超过限制 {_config.MaxSingleFileSize / (1024.0 * 1024.0):F2} MB");
                    Console.WriteLine($"文件路径: {filePath}");
                    return false;
                }

                // === 第五步：大文件警告 ===
                // 如果文件接近大文件阈值，输出警告信息
                if (_config.EnableSizeWarnings && fileSizeBytes > _config.LargeFileThreshold)
                {
                    Console.WriteLine($"警告：检测到大文件 ({fileSizeMB:F2} MB): {Path.GetFileName(filePath)}");
                }

                // === 第六步：异常大小检查 ===
                // 检查文件大小是否异常（可能是zip炸弹等攻击）
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (IsCompressedFile(extension) && fileSizeBytes > 500 * 1024 * 1024) // 500MB
                {
                    Console.WriteLine($"警告：压缩文件异常大 ({fileSizeMB:F2} MB)，可能存在安全风险: {filePath}");
                    // 注意：这里不直接返回false，而是记录警告，让调用者决定
                }

                // 文件大小检查通过
                Console.WriteLine($"文件大小验证通过: {fileSizeMB:F2} MB - {Path.GetFileName(filePath)}");
                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"文件访问权限不足: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"文件I/O错误: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件大小检查过程中发生未知错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 检查文件扩展名是否为压缩文件类型
        /// 用于辅助判断文件大小的合理性
        /// </summary>
        /// <param name="extension">文件扩展名（小写）</param>
        /// <returns>是压缩文件返回true</returns>
        private static bool IsCompressedFile(string extension)
        {
            var compressedExtensions = new[] { ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz" };
            return compressedExtensions.Contains(extension);
        }

        /// <summary>
        /// 检查文件签名（魔数）是否与扩展名匹配
        /// 通过验证文件头部的字节序列来防止扩展名伪装攻击
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 必须是存在的有效文件路径
        /// </param>
        /// <returns>
        /// 文件签名匹配或无需验证返回true，签名不匹配返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查PDF文件签名
        /// bool isValidPdf = validator.IsFileSignatureValid(@"C:\docs\report.pdf");
        ///
        /// // 检查图片文件签名
        /// bool isValidImage = validator.IsFileSignatureValid(@"C:\images\photo.jpg");
        /// </code>
        /// </example>
        public bool IsFileSignatureValid(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文件路径为空，无法检查签名");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文件不存在，无法检查签名: {filePath}");
                    return false;
                }

                // === 第二步：获取文件扩展名 ===
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                // === 第三步：检查是否需要验证签名 ===
                // 如果没有已知的文件签名定义，跳过检查（认为安全）
                if (!FileSignatures.ContainsKey(extension))
                {
                    Console.WriteLine($"文件类型 {extension} 无需签名验证，跳过检查");
                    return true;
                }

                // === 第四步：获取预期签名 ===
                var expectedSignature = FileSignatures[extension];
                var signatureLength = expectedSignature.Length;

                // === 第五步：读取文件头部字节 ===
                var actualSignature = new byte[signatureLength];

                using (var stream = File.OpenRead(filePath))
                {
                    // 检查文件是否足够大以包含完整签名
                    if (stream.Length < signatureLength)
                    {
                        Console.WriteLine($"文件太小，无法包含完整的 {extension} 签名: {filePath}");
                        return false;
                    }

                    // 读取文件头部字节
                    var bytesRead = stream.Read(actualSignature, 0, signatureLength);

                    if (bytesRead < signatureLength)
                    {
                        Console.WriteLine($"无法读取完整的文件签名，期望 {signatureLength} 字节，实际读取 {bytesRead} 字节");
                        return false;
                    }
                }

                // === 第六步：比较签名 ===
                var signatureMatches = actualSignature.SequenceEqual(expectedSignature);

                if (!signatureMatches)
                {
                    Console.WriteLine($"文件签名不匹配 {extension} 格式:");
                    Console.WriteLine($"  期望签名: {BitConverter.ToString(expectedSignature)}");
                    Console.WriteLine($"  实际签名: {BitConverter.ToString(actualSignature)}");
                    Console.WriteLine($"  文件路径: {filePath}");

                    // 检查是否可能是其他格式的文件
                    var detectedFormat = DetectFileFormatBySignature(actualSignature);
                    if (!string.IsNullOrEmpty(detectedFormat))
                    {
                        Console.WriteLine($"  检测到的实际格式: {detectedFormat}");
                    }

                    return false;
                }

                // 签名验证通过
                Console.WriteLine($"文件签名验证通过: {extension} - {Path.GetFileName(filePath)}");
                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"文件访问权限不足，无法检查签名: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"文件I/O错误，签名检查失败: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文件签名检查过程中发生未知错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 根据文件签名检测实际的文件格式
        /// 用于辅助诊断文件格式不匹配的问题
        /// </summary>
        /// <param name="signature">文件头部字节</param>
        /// <returns>检测到的文件格式，未知格式返回空字符串</returns>
        private static string DetectFileFormatBySignature(byte[] signature)
        {
            // 遍历所有已知签名，寻找匹配的格式
            foreach (var kvp in FileSignatures)
            {
                var knownSignature = kvp.Value;
                if (signature.Length >= knownSignature.Length)
                {
                    var headerBytes = signature.Take(knownSignature.Length).ToArray();
                    if (headerBytes.SequenceEqual(knownSignature))
                    {
                        return kvp.Key;
                    }
                }
            }

            return string.Empty;
        }

        /// <summary>
        /// 检查文件路径是否安全
        /// 防止路径遍历攻击、非法字符注入等安全威胁
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 支持相对路径和绝对路径
        /// </param>
        /// <returns>
        /// 路径安全返回true，存在安全风险返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查正常路径
        /// bool safe1 = validator.IsPathSafe(@"C:\uploads\document.pdf");     // true
        ///
        /// // 检查路径遍历攻击
        /// bool safe2 = validator.IsPathSafe(@"..\..\windows\system32");      // false
        ///
        /// // 检查非法字符
        /// bool safe3 = validator.IsPathSafe(@"file&lt;&gt;name.txt");              // false
        /// </code>
        /// </example>
        public bool IsPathSafe(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文件路径为空或仅包含空白字符");
                    return false;
                }

                // === 第二步：路径长度检查 ===
                // 检查路径是否超过系统限制
                if (filePath.Length > MAX_PATH_LENGTH)
                {
                    Console.WriteLine($"路径过长: {filePath.Length} 字符，超过限制 {MAX_PATH_LENGTH} 字符");
                    return false;
                }

                // === 第三步：非法字符检查 ===
                // 检查路径中是否包含系统不允许的字符
                var invalidPathChars = Path.GetInvalidPathChars();
                var invalidFileChars = Path.GetInvalidFileNameChars();

                if (filePath.IndexOfAny(invalidPathChars) >= 0)
                {
                    Console.WriteLine($"路径包含非法字符: {filePath}");
                    return false;
                }

                // 检查文件名部分的非法字符
                var fileName = Path.GetFileName(filePath);
                if (!string.IsNullOrEmpty(fileName) && fileName.IndexOfAny(invalidFileChars) >= 0)
                {
                    Console.WriteLine($"文件名包含非法字符: {fileName}");
                    return false;
                }

                // === 第四步：路径遍历攻击检查 ===
                // 检查是否包含路径遍历模式
                if (filePath.Contains(".."))
                {
                    Console.WriteLine($"检测到路径遍历攻击模式: {filePath}");
                    return false;
                }

                // === 第五步：路径规范化检查 ===
                // 尝试获取完整路径，检查是否存在异常
                try
                {
                    var normalizedPath = Path.GetFullPath(filePath);

                    // 检查规范化后的路径是否包含危险模式
                    if (normalizedPath.Contains(".."))
                    {
                        Console.WriteLine($"规范化后的路径包含遍历模式: {normalizedPath}");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"路径规范化失败: {ex.Message}");
                    return false;
                }

                // === 第六步：特殊字符和模式检查 ===
                // 检查可能的恶意模式
                var dangerousPatterns = new[]
                {
                    "\\\\", "//",           // 双斜杠
                    "CON", "PRN", "AUX",    // Windows保留名称
                    "NUL", "COM1", "LPT1"   // 设备名称
                };

                var upperPath = filePath.ToUpperInvariant();
                foreach (var pattern in dangerousPatterns)
                {
                    if (upperPath.Contains(pattern))
                    {
                        Console.WriteLine($"路径包含危险模式 '{pattern}': {filePath}");
                        return false;
                    }
                }

                // === 第七步：Unicode和编码检查 ===
                // 检查是否包含可能的Unicode攻击字符
                if (ContainsUnicodeAttackCharacters(filePath))
                {
                    Console.WriteLine($"路径包含可疑的Unicode字符: {filePath}");
                    return false;
                }

                // 路径安全检查通过
                return true;
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"路径格式无效: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"路径安全检查过程中发生未知错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 检查路径是否符合配置的安全策略
        /// 根据用户配置的安全策略进行额外的路径验证
        /// </summary>
        /// <param name="filePath">要检查的文件路径</param>
        /// <returns>符合策略返回true，否则返回false</returns>
        private bool IsPathCompliantWithPolicy(string filePath)
        {
            try
            {
                // 如果未启用路径验证，直接通过
                if (!_config.EnablePathValidation)
                    return true;

                // 检查路径是否在允许的目录范围内
                var fullPath = Path.GetFullPath(filePath);
                var workingDir = Path.GetFullPath(_config.DefaultWorkingDirectory);

                // 确保文件在工作目录范围内
                if (!fullPath.StartsWith(workingDir, StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"文件路径超出允许的工作目录范围: {fullPath}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"路径策略检查失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查字符串是否包含可能的Unicode攻击字符
        /// 防止利用Unicode字符进行的路径伪装攻击
        /// </summary>
        /// <param name="input">要检查的字符串</param>
        /// <returns>包含攻击字符返回true</returns>
        private static bool ContainsUnicodeAttackCharacters(string input)
        {
            // 检查零宽字符和其他可疑Unicode字符
            var suspiciousChars = new[]
            {
                '\u200B', // 零宽空格
                '\u200C', // 零宽非连接符
                '\u200D', // 零宽连接符
                '\uFEFF', // 字节顺序标记
                '\u202E'  // 右到左覆盖字符
            };

            return input.IndexOfAny(suspiciousChars) >= 0;
        }

        #endregion

        // ==========================================
        // 📋 专业格式验证方法
        // ==========================================

        #region 文件格式验证

        /// <summary>
        /// 验证是否为有效的图片文件
        /// 通过扩展名和文件签名双重验证确保文件的真实性
        /// </summary>
        /// <param name="filePath">
        /// 要验证的图片文件路径
        /// 支持常见的图片格式：JPG、PNG、GIF、BMP等
        /// </param>
        /// <returns>
        /// 是有效图片文件返回true，否则返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 验证不同格式的图片
        /// bool isValidJpg = validator.IsValidImageFile(@"C:\images\photo.jpg");
        /// bool isValidPng = validator.IsValidImageFile(@"C:\images\logo.png");
        /// bool isValidGif = validator.IsValidImageFile(@"C:\images\animation.gif");
        ///
        /// if (isValidJpg)
        /// {
        ///     Console.WriteLine("JPEG图片验证通过");
        /// }
        /// </code>
        /// </example>
        public bool IsValidImageFile(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("图片文件路径为空");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"图片文件不存在: {filePath}");
                    return false;
                }

                // === 第二步：扩展名验证 ===
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var supportedImageExtensions = new[]
                {
                    ".jpg", ".jpeg", ".png", ".gif", ".bmp",
                    ".tiff", ".tif", ".webp", ".svg", ".ico"
                };

                if (!supportedImageExtensions.Contains(extension))
                {
                    Console.WriteLine($"不支持的图片格式: {extension}");
                    return false;
                }

                // === 第三步：文件大小检查 ===
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    Console.WriteLine($"图片文件为空: {filePath}");
                    return false;
                }

                // 检查图片文件是否过大（可能是恶意文件）
                var maxImageSize = 50 * 1024 * 1024; // 50MB
                if (fileInfo.Length > maxImageSize)
                {
                    Console.WriteLine($"图片文件过大: {fileInfo.Length / (1024 * 1024)} MB");
                    return false;
                }

                // === 第四步：文件签名验证 ===
                // 对于有签名定义的格式，验证文件头部
                if (!IsFileSignatureValid(filePath))
                {
                    Console.WriteLine($"图片文件签名验证失败: {filePath}");
                    return false;
                }

                // === 第五步：特殊格式检查 ===
                // 对于SVG等特殊格式，进行额外的安全检查
                if (extension == ".svg")
                {
                    if (!IsSafeSvgFile(filePath))
                    {
                        Console.WriteLine($"SVG文件包含不安全内容: {filePath}");
                        return false;
                    }
                }

                Console.WriteLine($"图片文件验证通过: {extension} - {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"图片文件验证过程中发生错误: {ex.Message}");
                Console.WriteLine($"文件路径: {filePath}");
                return false;
            }
        }

        /// <summary>
        /// 验证是否为有效的文档文件
        /// 支持常见的办公文档格式，包括PDF、Office文档等
        /// </summary>
        /// <param name="filePath">
        /// 要验证的文档文件路径
        /// 支持PDF、Word、Excel、PowerPoint、文本文件等格式
        /// </param>
        /// <returns>
        /// 是有效文档文件返回true，否则返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 验证不同类型的文档
        /// bool isPdf = validator.IsValidDocumentFile(@"C:\docs\report.pdf");
        /// bool isWord = validator.IsValidDocumentFile(@"C:\docs\document.docx");
        /// bool isExcel = validator.IsValidDocumentFile(@"C:\docs\spreadsheet.xlsx");
        /// </code>
        /// </example>
        public bool IsValidDocumentFile(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文档文件路径为空");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文档文件不存在: {filePath}");
                    return false;
                }

                // === 第二步：扩展名验证 ===
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var supportedDocumentExtensions = new[]
                {
                    // PDF文档
                    ".pdf",

                    // Microsoft Office文档
                    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",

                    // 文本文档
                    ".txt", ".rtf",

                    // 其他常见文档格式
                    ".odt", ".ods", ".odp", // LibreOffice
                    ".csv", ".tsv"          // 数据文件
                };

                if (!supportedDocumentExtensions.Contains(extension))
                {
                    Console.WriteLine($"不支持的文档格式: {extension}");
                    return false;
                }

                // === 第三步：文件大小检查 ===
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    Console.WriteLine($"文档文件为空: {filePath}");
                    return false;
                }

                // 检查文档文件大小限制
                var maxDocumentSize = 100 * 1024 * 1024; // 100MB
                if (fileInfo.Length > maxDocumentSize)
                {
                    Console.WriteLine($"文档文件过大: {fileInfo.Length / (1024 * 1024)} MB");
                    return false;
                }

                // === 第四步：文件签名验证 ===
                // 对于有签名定义的格式，验证文件头部
                if (!IsFileSignatureValid(filePath))
                {
                    Console.WriteLine($"文档文件签名验证失败: {filePath}");
                    return false;
                }

                // === 第五步：特殊格式安全检查 ===
                // 对于可能包含宏或脚本的文档格式进行额外检查
                if (IsMacroEnabledDocument(extension))
                {
                    Console.WriteLine($"警告：检测到可能包含宏的文档格式: {extension}");
                    // 注意：这里不直接拒绝，而是记录警告
                }

                Console.WriteLine($"文档文件验证通过: {extension} - {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文档文件验证过程中发生错误: {ex.Message}");
                Console.WriteLine($"文件路径: {filePath}");
                return false;
            }
        }

        /// <summary>
        /// 验证是否为有效的压缩文件
        /// 支持常见的压缩格式，并进行安全性检查防止zip炸弹等攻击
        /// </summary>
        /// <param name="filePath">
        /// 要验证的压缩文件路径
        /// 支持ZIP、RAR、7Z、TAR等常见压缩格式
        /// </param>
        /// <returns>
        /// 是有效且安全的压缩文件返回true，否则返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 验证不同类型的压缩文件
        /// bool isZip = validator.IsValidArchiveFile(@"C:\archives\data.zip");
        /// bool isRar = validator.IsValidArchiveFile(@"C:\archives\backup.rar");
        /// bool is7z = validator.IsValidArchiveFile(@"C:\archives\compressed.7z");
        /// </code>
        /// </example>
        public bool IsValidArchiveFile(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("压缩文件路径为空");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"压缩文件不存在: {filePath}");
                    return false;
                }

                // === 第二步：扩展名验证 ===
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                var supportedArchiveExtensions = new[]
                {
                    ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2",
                    ".xz", ".lzma", ".cab", ".iso"
                };

                if (!supportedArchiveExtensions.Contains(extension))
                {
                    Console.WriteLine($"不支持的压缩格式: {extension}");
                    return false;
                }

                // === 第三步：文件大小检查 ===
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    Console.WriteLine($"压缩文件为空: {filePath}");
                    return false;
                }

                // 检查压缩文件大小，防止zip炸弹攻击
                var maxArchiveSize = 500 * 1024 * 1024; // 500MB
                if (fileInfo.Length > maxArchiveSize)
                {
                    Console.WriteLine($"压缩文件过大，可能存在安全风险: {fileInfo.Length / (1024 * 1024)} MB");
                    return false;
                }

                // === 第四步：文件签名验证 ===
                // 验证压缩文件的文件头部签名
                if (!IsFileSignatureValid(filePath))
                {
                    Console.WriteLine($"压缩文件签名验证失败: {filePath}");
                    return false;
                }

                // === 第五步：压缩比检查 ===
                // 对于某些格式，检查是否可能是zip炸弹
                if (extension == ".zip" && IsSuspiciousZipFile(filePath))
                {
                    Console.WriteLine($"检测到可疑的ZIP文件，可能是zip炸弹: {filePath}");
                    return false;
                }

                Console.WriteLine($"压缩文件验证通过: {extension} - {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"压缩文件验证过程中发生错误: {ex.Message}");
                Console.WriteLine($"文件路径: {filePath}");
                return false;
            }
        }

        /// <summary>
        /// 检查SVG文件是否安全
        /// SVG文件可能包含JavaScript代码，需要特殊检查
        /// </summary>
        /// <param name="filePath">SVG文件路径</param>
        /// <returns>安全返回true</returns>
        private bool IsSafeSvgFile(string filePath)
        {
            try
            {
                // 读取SVG文件内容（限制大小）
                var maxSvgSize = 1024 * 1024; // 1MB
                var fileInfo = new FileInfo(filePath);

                if (fileInfo.Length > maxSvgSize)
                {
                    Console.WriteLine($"SVG文件过大: {fileInfo.Length} bytes");
                    return false;
                }

                var content = File.ReadAllText(filePath);

                // 检查是否包含危险的脚本标签
                var dangerousPatterns = new[]
                {
                    "<script", "javascript:", "onload=", "onclick=",
                    "onerror=", "onmouseover=", "eval(", "document."
                };

                var lowerContent = content.ToLowerInvariant();
                foreach (var pattern in dangerousPatterns)
                {
                    if (lowerContent.Contains(pattern))
                    {
                        Console.WriteLine($"SVG文件包含危险模式: {pattern}");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SVG安全检查失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查文档格式是否支持宏
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>支持宏返回true</returns>
        private static bool IsMacroEnabledDocument(string extension)
        {
            var macroEnabledFormats = new[] { ".docm", ".xlsm", ".pptm", ".doc", ".xls", ".ppt" };
            return macroEnabledFormats.Contains(extension);
        }

        /// <summary>
        /// 检查ZIP文件是否可疑（可能是zip炸弹）
        /// </summary>
        /// <param name="filePath">ZIP文件路径</param>
        /// <returns>可疑返回true</returns>
        private bool IsSuspiciousZipFile(string filePath)
        {
            try
            {
                // 简单的启发式检查：文件很小但可能解压后很大
                var fileInfo = new FileInfo(filePath);

                // 如果文件小于1MB但是压缩文件，可能需要额外检查
                if (fileInfo.Length < 1024 * 1024)
                {
                    // 这里可以添加更复杂的zip炸弹检测逻辑
                    // 目前只是基础检查
                    return false;
                }

                return false;
            }
            catch
            {
                return true; // 出错时认为可疑
            }
        }

        #endregion

        // ==========================================
        // 📊 批量验证和扫描功能
        // ==========================================

        #region 批量验证

        /// <summary>
        /// 批量验证文件安全性
        /// 对多个文件进行并行安全检查，提供详细的验证报告
        /// </summary>
        /// <param name="filePaths">
        /// 要验证的文件路径集合
        /// 支持相对路径和绝对路径的混合
        /// </param>
        /// <returns>
        /// 验证结果字典，键为文件路径，值为验证结果
        /// true表示文件安全，false表示存在安全风险
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        /// var files = new[]
        /// {
        ///     @"C:\uploads\document.pdf",
        ///     @"C:\uploads\image.jpg",
        ///     @"C:\uploads\suspicious.exe"
        /// };
        ///
        /// var results = validator.ValidateFiles(files);
        /// foreach (var result in results)
        /// {
        ///     Console.WriteLine($"{result.Key}: {(result.Value ? "安全" : "危险")}");
        /// }
        /// </code>
        /// </example>
        public Dictionary<string, bool> ValidateFiles(IEnumerable<string> filePaths)
        {
            var results = new Dictionary<string, bool>();
            var fileList = filePaths?.ToList() ?? new List<string>();

            if (!fileList.Any())
            {
                Console.WriteLine("没有提供要验证的文件路径");
                return results;
            }

            Console.WriteLine($"开始批量验证 {fileList.Count} 个文件...");
            var startTime = DateTime.Now;

            var safeCount = 0;
            var dangerousCount = 0;
            var errorCount = 0;

            foreach (var filePath in fileList)
            {
                try
                {
                    var isSafe = IsSafeFile(filePath);
                    results[filePath] = isSafe;

                    if (isSafe)
                        safeCount++;
                    else
                        dangerousCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"验证文件时发生错误 {filePath}: {ex.Message}");
                    results[filePath] = false;
                    errorCount++;
                }
            }

            var duration = DateTime.Now - startTime;
            Console.WriteLine($"批量验证完成，耗时: {duration.TotalSeconds:F2} 秒");
            Console.WriteLine($"验证结果 - 安全: {safeCount}, 危险: {dangerousCount}, 错误: {errorCount}");

            return results;
        }

        /// <summary>
        /// 扫描目录中的危险文件
        /// 递归搜索指定目录，识别所有潜在的安全威胁文件
        /// </summary>
        /// <param name="directoryPath">
        /// 要扫描的目录路径
        /// 必须是存在的有效目录路径
        /// </param>
        /// <param name="recursive">
        /// 是否递归搜索子目录
        /// true: 扫描所有子目录（默认）
        /// false: 仅扫描当前目录
        /// </param>
        /// <returns>
        /// 危险文件路径列表
        /// 包含所有未通过安全检查的文件
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 扫描整个目录树
        /// var dangerousFiles = validator.GetDangerousFiles(@"C:\uploads", recursive: true);
        ///
        /// if (dangerousFiles.Any())
        /// {
        ///     Console.WriteLine($"发现 {dangerousFiles.Count} 个危险文件:");
        ///     foreach (var file in dangerousFiles)
        ///     {
        ///         Console.WriteLine($"  - {file}");
        ///     }
        /// }
        /// </code>
        /// </example>
        public List<string> GetDangerousFiles(string directoryPath, bool recursive = true)
        {
            var dangerousFiles = new List<string>();

            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(directoryPath))
                {
                    Console.WriteLine("目录路径为空，无法扫描");
                    return dangerousFiles;
                }

                if (!Directory.Exists(directoryPath))
                {
                    Console.WriteLine($"目录不存在: {directoryPath}");
                    return dangerousFiles;
                }

                // === 第二步：开始扫描 ===
                Console.WriteLine($"开始扫描目录: {directoryPath}");
                Console.WriteLine($"递归模式: {(recursive ? "是" : "否")}");

                var startTime = DateTime.Now;
                var searchOption = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;

                // === 第三步：获取所有文件 ===
                var files = Directory.GetFiles(directoryPath, "*", searchOption);
                Console.WriteLine($"找到 {files.Length} 个文件，开始安全检查...");

                var processedCount = 0;
                var dangerousCount = 0;

                // === 第四步：逐个检查文件 ===
                foreach (var file in files)
                {
                    try
                    {
                        processedCount++;

                        // 显示进度（每100个文件报告一次）
                        if (processedCount % 100 == 0)
                        {
                            Console.WriteLine($"已检查 {processedCount}/{files.Length} 个文件...");
                        }

                        // 执行安全检查
                        if (!IsSafeFile(file))
                        {
                            dangerousFiles.Add(file);
                            dangerousCount++;
                            Console.WriteLine($"发现危险文件: {file}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"检查文件时发生错误 {file}: {ex.Message}");
                        // 出错的文件也视为危险文件
                        dangerousFiles.Add(file);
                        dangerousCount++;
                    }
                }

                // === 第五步：输出扫描结果 ===
                var duration = DateTime.Now - startTime;
                Console.WriteLine($"扫描完成，耗时: {duration.TotalSeconds:F2} 秒");
                Console.WriteLine($"扫描结果 - 总文件: {files.Length}, 危险文件: {dangerousCount}, 安全文件: {files.Length - dangerousCount}");

                if (dangerousCount > 0)
                {
                    Console.WriteLine($"警告：在目录 {directoryPath} 中发现 {dangerousCount} 个危险文件！");
                }
                else
                {
                    Console.WriteLine($"目录 {directoryPath} 扫描完成，未发现危险文件");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"目录访问权限不足: {ex.Message}");
            }
            catch (DirectoryNotFoundException ex)
            {
                Console.WriteLine($"目录未找到: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"扫描危险文件过程中发生未知错误: {ex.Message}");
                Console.WriteLine($"错误详情: {ex}");
            }

            return dangerousFiles;
        }

        #endregion

        // ==========================================
        // 📄 文件信息和类型识别
        // ==========================================

        #region 文件信息获取

        /// <summary>
        /// 获取文件的MIME类型
        /// 根据文件扩展名返回标准的MIME类型字符串
        /// </summary>
        /// <param name="filePath">
        /// 文件路径或文件名
        /// 可以是完整路径或仅包含扩展名的文件名
        /// </param>
        /// <returns>
        /// 标准的MIME类型字符串
        /// 未知类型返回 "application/octet-stream"
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 获取不同文件的MIME类型
        /// string pdfMime = validator.GetMimeType("document.pdf");        // "application/pdf"
        /// string jpgMime = validator.GetMimeType("photo.jpg");           // "image/jpeg"
        /// string txtMime = validator.GetMimeType("readme.txt");          // "text/plain"
        /// string unknownMime = validator.GetMimeType("file.unknown");    // "application/octet-stream"
        /// </code>
        /// </example>
        public string GetMimeType(string filePath)
        {
            // === 基础验证 ===
            if (string.IsNullOrWhiteSpace(filePath))
            {
                Console.WriteLine("文件路径为空，返回默认MIME类型");
                return "application/octet-stream";
            }

            // === 提取扩展名 ===
            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            if (string.IsNullOrEmpty(extension))
            {
                Console.WriteLine($"文件没有扩展名: {filePath}");
                return "application/octet-stream";
            }

            // === MIME类型映射 ===
            var mimeType = extension switch
            {
                // 文本文件类型
                ".txt" => "text/plain",
                ".html" or ".htm" => "text/html",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                ".xml" => "application/xml",
                ".csv" => "text/csv",
                ".rtf" => "application/rtf",

                // 文档文件类型
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",

                // 图片文件类型
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                ".webp" => "image/webp",
                ".svg" => "image/svg+xml",
                ".ico" => "image/x-icon",

                // 音频文件类型
                ".mp3" => "audio/mpeg",
                ".wav" => "audio/wav",
                ".flac" => "audio/flac",
                ".aac" => "audio/aac",
                ".ogg" => "audio/ogg",
                ".wma" => "audio/x-ms-wma",

                // 视频文件类型
                ".mp4" => "video/mp4",
                ".avi" => "video/x-msvideo",
                ".mov" => "video/quicktime",
                ".wmv" => "video/x-ms-wmv",
                ".mkv" => "video/x-matroska",
                ".flv" => "video/x-flv",
                ".webm" => "video/webm",

                // 压缩文件类型
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                ".tar" => "application/x-tar",
                ".gz" => "application/gzip",
                ".bz2" => "application/x-bzip2",

                // 可执行文件类型
                ".exe" => "application/x-msdownload",
                ".msi" => "application/x-msi",
                ".dll" => "application/x-msdownload",

                // 默认类型
                _ => "application/octet-stream"
            };

            return mimeType;
        }

        /// <summary>
        /// 检查文件是否为文本文件
        /// 通过MIME类型和文件内容分析判断文件是否为可读的文本格式
        /// </summary>
        /// <param name="filePath">
        /// 要检查的文件路径
        /// 必须是存在的有效文件路径
        /// </param>
        /// <returns>
        /// 是文本文件返回true，二进制文件返回false
        /// </returns>
        /// <example>
        /// <code>
        /// var validator = new YFileValidator();
        ///
        /// // 检查不同类型的文件
        /// bool isTxt = validator.IsTextFile(@"C:\docs\readme.txt");      // true
        /// bool isJson = validator.IsTextFile(@"C:\config\settings.json"); // true
        /// bool isExe = validator.IsTextFile(@"C:\apps\program.exe");      // false
        /// bool isPdf = validator.IsTextFile(@"C:\docs\document.pdf");     // false
        /// </code>
        /// </example>
        public bool IsTextFile(string filePath)
        {
            try
            {
                // === 第一步：基础验证 ===
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    Console.WriteLine("文件路径为空，无法判断文本类型");
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Console.WriteLine($"文件不存在: {filePath}");
                    return false;
                }

                // === 第二步：MIME类型检查 ===
                var mimeType = GetMimeType(filePath);

                // 明确的文本类型
                if (mimeType.StartsWith("text/"))
                {
                    return true;
                }

                // 特殊的文本格式
                var textMimeTypes = new[]
                {
                    "application/json",
                    "application/xml",
                    "application/javascript",
                    "application/rtf"
                };

                if (textMimeTypes.Contains(mimeType))
                {
                    return true;
                }

                // === 第三步：文件内容检查 ===
                // 对于未知类型，通过读取文件头部判断
                return IsTextBasedOnContent(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"文本文件检查失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 通过文件内容判断是否为文本文件
        /// 读取文件头部字节，分析是否包含可打印字符
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是文本文件返回true</returns>
        private static bool IsTextBasedOnContent(string filePath)
        {
            try
            {
                const int sampleSize = 1024; // 读取前1KB进行分析
                var buffer = new byte[sampleSize];

                using var stream = File.OpenRead(filePath);
                var bytesRead = stream.Read(buffer, 0, sampleSize);

                if (bytesRead == 0)
                    return false; // 空文件不认为是文本文件

                // 检查是否包含过多的控制字符或二进制字符
                var controlCharCount = 0;
                var printableCharCount = 0;

                for (int i = 0; i < bytesRead; i++)
                {
                    var b = buffer[i];

                    // 允许的控制字符：换行、回车、制表符
                    if (b == 0x09 || b == 0x0A || b == 0x0D)
                    {
                        continue; // 这些是正常的文本控制字符
                    }

                    // 可打印ASCII字符范围
                    if (b >= 32 && b <= 126)
                    {
                        printableCharCount++;
                    }
                    // 扩展ASCII字符范围（UTF-8等）
                    else if (b >= 128)
                    {
                        printableCharCount++;
                    }
                    // 控制字符或二进制字符
                    else
                    {
                        controlCharCount++;
                    }
                }

                // 如果控制字符比例过高，认为是二进制文件
                var controlCharRatio = (double)controlCharCount / bytesRead;
                return controlCharRatio < 0.1; // 控制字符少于10%认为是文本文件
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}
