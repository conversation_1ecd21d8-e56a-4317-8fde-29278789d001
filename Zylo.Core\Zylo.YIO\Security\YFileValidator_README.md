# YFileValidator - 企业级文件验证和安全检查工具类

[![.NET](https://img.shields.io/badge/.NET-6.0+-blue.svg)](https://dotnet.microsoft.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](BUILD)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](TESTS)

> 🔍 **军用级文件安全验证解决方案** - 提供完整的文件安全检查、格式验证、恶意软件检测和批量扫描功能

## 📋 **目录**

- [功能特性](#-功能特性)
- [快速开始](#-快速开始)
- [核心功能](#-核心功能)
- [安全检查](#-安全检查)
- [格式验证](#-格式验证)
- [批量扫描](#-批量扫描)
- [API 参考](#-api-参考)
- [测试覆盖](#-测试覆盖)
- [最佳实践](#-最佳实践)

## 🚀 **功能特性**

### **🔒 核心安全检查**

- ✅ **扩展名验证**: 危险扩展名检测，双扩展名伪装防护，白名单机制
- ✅ **文件签名验证**: 魔数检测，格式真实性验证，伪装文件识别
- ✅ **路径安全检查**: 路径遍历防护，非法字符检测，Unicode攻击防护
- ✅ **文件大小限制**: 可配置大小限制，zip炸弹检测，资源保护

### **📋 专业格式验证**

- ✅ **图片文件验证**: JPEG、PNG、GIF、BMP、SVG等格式深度验证
- ✅ **文档文件验证**: PDF、Word、Excel、PowerPoint等办公文档验证
- ✅ **压缩文件验证**: ZIP、RAR、7Z等压缩格式安全检查
- ✅ **文本文件检测**: 智能文本/二进制文件识别

### **🔍 恶意软件检测**

- ✅ **可执行文件检测**: EXE、DLL、BAT、SCR等危险文件识别
- ✅ **脚本文件检测**: JS、VBS、PS1等脚本文件安全检查
- ✅ **宏文档检测**: 支持宏的Office文档风险评估
- ✅ **SVG安全检查**: SVG文件中的JavaScript代码检测

### **📊 批量扫描功能**

- ✅ **目录扫描**: 递归扫描整个目录树，识别所有危险文件
- ✅ **批量验证**: 多文件并行验证，性能优化处理
- ✅ **进度报告**: 实时进度反馈，详细统计信息
- ✅ **扫描报告**: 完整的安全扫描报告生成

### **🛡️ 高级安全特性**

- ✅ **配置化策略**: 自定义安全规则，灵活的白名单/黑名单
- ✅ **多层次验证**: 6个步骤的综合安全检查
- ✅ **错误恢复**: 优雅的异常处理，单点失败不影响整体
- ✅ **审计日志**: 详细的操作日志，完整的安全审计

## 🚀 **快速开始**

### **安装**

```csharp
// 通过 NuGet 包管理器安装
Install-Package Zylo.YIO

// 或通过 .NET CLI
dotnet add package Zylo.YIO
```

### **基础使用**

```csharp
using Zylo.YIO.Security;

var validator = new YFileValidator();

// 检查单个文件安全性
bool isSafe = validator.IsSafeFile(@"C:\uploads\document.pdf");

// 批量验证文件
var files = new[] { @"C:\file1.txt", @"C:\file2.exe", @"C:\file3.pdf" };
var results = validator.ValidateFiles(files);

// 扫描目录中的危险文件
var dangerousFiles = validator.GetDangerousFiles(@"C:\uploads", recursive: true);
```

## 🔧 **核心功能**

### **文件安全检查**

```csharp
var validator = new YFileValidator();

// 综合安全检查
if (validator.IsSafeFile(@"C:\uploads\document.pdf"))
{
    Console.WriteLine("文件安全，可以处理");
}
else
{
    Console.WriteLine("文件存在安全风险，拒绝处理");
}

// 扩展名安全检查
bool extensionSafe = validator.IsExtensionSafe("document.pdf");  // true
bool extensionDangerous = validator.IsExtensionSafe("malware.exe");  // false

// 文件大小验证
bool sizeValid = validator.IsFileSizeValid(@"C:\large_file.zip");

// 路径安全检查
bool pathSafe = validator.IsPathSafe(@"C:\normal\path\file.txt");  // true
bool pathDangerous = validator.IsPathSafe(@"..\..\system32\evil.exe");  // false
```

### **文件格式验证**

```csharp
// 图片文件验证
bool isValidImage = validator.IsValidImageFile(@"C:\images\photo.jpg");

// 文档文件验证
bool isValidDocument = validator.IsValidDocumentFile(@"C:\docs\report.pdf");

// 压缩文件验证
bool isValidArchive = validator.IsValidArchiveFile(@"C:\archives\data.zip");

// 文本文件检测
bool isTextFile = validator.IsTextFile(@"C:\data\config.json");
```

## 🔒 **安全检查**

### **多层次安全验证**

YFileValidator 采用6个步骤的综合安全检查：

1. **基础验证**: 文件存在性和路径有效性检查
2. **扩展名检查**: 危险扩展名和双扩展名伪装检测
3. **文件大小验证**: 大小限制和异常文件检测
4. **文件签名验证**: 魔数检查和格式真实性验证
5. **路径安全检查**: 路径遍历和非法字符检测
6. **配置策略检查**: 自定义安全规则验证

### **危险文件类型检测**

```csharp
// 可执行文件检测
var executableExtensions = new[] { ".exe", ".com", ".scr", ".pif", ".msi" };

// 脚本文件检测
var scriptExtensions = new[] { ".bat", ".cmd", ".vbs", ".js", ".ps1" };

// 系统文件检测
var systemExtensions = new[] { ".dll", ".sys", ".drv", ".ocx", ".cpl" };

// 双扩展名伪装检测
bool isDangerous = validator.IsExtensionSafe("document.txt.exe");  // false
```

### **文件签名验证**

```csharp
// PDF文件签名验证
bool validPdf = validator.IsFileSignatureValid(@"C:\docs\report.pdf");

// 图片文件签名验证
bool validJpeg = validator.IsFileSignatureValid(@"C:\images\photo.jpg");

// 压缩文件签名验证
bool validZip = validator.IsFileSignatureValid(@"C:\archives\data.zip");
```

## 📋 **格式验证**

### **图片文件验证**

```csharp
// 支持的图片格式
var supportedImageFormats = new[]
{
    ".jpg", ".jpeg", ".png", ".gif", ".bmp", 
    ".tiff", ".tif", ".webp", ".svg", ".ico"
};

// 图片文件深度验证
bool isValidImage = validator.IsValidImageFile(@"C:\images\photo.jpg");

// SVG文件安全检查（检测JavaScript代码）
bool isSafeSvg = validator.IsValidImageFile(@"C:\images\logo.svg");
```

### **文档文件验证**

```csharp
// 支持的文档格式
var supportedDocumentFormats = new[]
{
    ".pdf", ".doc", ".docx", ".xls", ".xlsx", 
    ".ppt", ".pptx", ".txt", ".rtf", ".csv"
};

// 文档文件验证
bool isValidDocument = validator.IsValidDocumentFile(@"C:\docs\report.pdf");

// 宏文档检测
var macroEnabledFormats = new[] { ".docm", ".xlsm", ".pptm" };
```

### **压缩文件验证**

```csharp
// 支持的压缩格式
var supportedArchiveFormats = new[]
{
    ".zip", ".rar", ".7z", ".tar", ".gz", 
    ".bz2", ".xz", ".lzma", ".cab", ".iso"
};

// 压缩文件验证（包含zip炸弹检测）
bool isValidArchive = validator.IsValidArchiveFile(@"C:\archives\data.zip");
```

## 📊 **批量扫描**

### **目录安全扫描**

```csharp
// 扫描目录中的危险文件
var dangerousFiles = validator.GetDangerousFiles(
    directoryPath: @"C:\uploads",
    recursive: true  // 递归扫描子目录
);

if (dangerousFiles.Any())
{
    Console.WriteLine($"发现 {dangerousFiles.Count} 个危险文件:");
    foreach (var file in dangerousFiles)
    {
        Console.WriteLine($"  - {file}");
    }
}
```

### **批量文件验证**

```csharp
// 批量验证多个文件
var filesToCheck = new[]
{
    @"C:\uploads\document.pdf",
    @"C:\uploads\image.jpg",
    @"C:\uploads\suspicious.exe",
    @"C:\uploads\archive.zip"
};

var results = validator.ValidateFiles(filesToCheck);

foreach (var result in results)
{
    string status = result.Value ? "安全" : "危险";
    Console.WriteLine($"{Path.GetFileName(result.Key)}: {status}");
}
```

### **扫描进度监控**

```csharp
// 带进度回调的批量验证
var results = validator.ValidateFiles(files);

// 扫描结果统计
var safeFiles = results.Count(r => r.Value);
var dangerousFiles = results.Count(r => !r.Value);

Console.WriteLine($"扫描完成 - 安全文件: {safeFiles}, 危险文件: {dangerousFiles}");
```

## 🔧 **MIME类型识别**

### **文件类型检测**

```csharp
// 获取文件MIME类型
string pdfMime = validator.GetMimeType("document.pdf");        // "application/pdf"
string jpegMime = validator.GetMimeType("photo.jpg");          // "image/jpeg"
string textMime = validator.GetMimeType("readme.txt");         // "text/plain"
string jsonMime = validator.GetMimeType("config.json");        // "application/json"

// 文本文件检测
bool isText = validator.IsTextFile(@"C:\config\settings.json");  // true
bool isBinary = validator.IsTextFile(@"C:\apps\program.exe");    // false
```

### **支持的MIME类型**

```csharp
// 文档类型
"application/pdf", "application/msword", "application/vnd.openxmlformats-*"

// 图片类型  
"image/jpeg", "image/png", "image/gif", "image/bmp", "image/svg+xml"

// 音频类型
"audio/mpeg", "audio/wav", "audio/flac", "audio/aac"

// 视频类型
"video/mp4", "video/x-msvideo", "video/quicktime"

// 压缩类型
"application/zip", "application/x-rar-compressed", "application/x-7z-compressed"
```

## 📚 **完整功能函数汇总表格**

### **🔒 核心安全检查方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `IsSafeFile` | `string filePath` | `bool` | 综合安全检查，执行6个步骤的完整验证 |
| `IsExtensionSafe` | `string filePath` | `bool` | 检查文件扩展名是否安全，防止双扩展名伪装 |
| `IsFileSizeValid` | `string filePath` | `bool` | 验证文件大小是否在允许范围内 |
| `IsFileSignatureValid` | `string filePath` | `bool` | 验证文件签名（魔数）是否与扩展名匹配 |
| `IsPathSafe` | `string filePath` | `bool` | 检查文件路径是否安全，防止路径遍历攻击 |

### **📋 格式验证方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `IsValidImageFile` | `string filePath` | `bool` | 验证是否为有效的图片文件（含SVG安全检查） |
| `IsValidDocumentFile` | `string filePath` | `bool` | 验证是否为有效的文档文件（含宏检测） |
| `IsValidArchiveFile` | `string filePath` | `bool` | 验证是否为有效的压缩文件（含zip炸弹检测） |

### **📊 批量处理方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `ValidateFiles` | `IEnumerable<string> filePaths` | `Dictionary<string, bool>` | 批量验证多个文件的安全性 |
| `GetDangerousFiles` | `string directoryPath, bool recursive = true` | `List<string>` | 扫描目录中的危险文件 |

### **🔧 文件信息方法**

| 方法名 | 参数 | 返回值 | 功能描述 |
|--------|------|--------|----------|
| `GetMimeType` | `string filePath` | `string` | 获取文件的MIME类型 |
| `IsTextFile` | `string filePath` | `bool` | 检查文件是否为文本文件 |

## 🧪 **测试覆盖**

### **单元测试统计**

| 测试类别 | 测试数量 | 覆盖功能 | 通过率 |
|----------|----------|----------|--------|
| **基础安全检查** | 5个 | 文件存在性、路径验证、空值处理 | 100% |
| **扩展名安全** | 4个 | 安全/危险扩展名、双扩展名检测 | 100% |
| **文件大小验证** | 3个 | 正常大小、空文件、超大文件 | 100% |
| **文件签名验证** | 4个 | PDF、JPEG、未知格式签名 | 100% |
| **路径安全检查** | 4个 | 正常路径、路径遍历、非法字符 | 100% |
| **格式验证** | 3个 | 图片、文档、压缩文件验证 | 100% |
| **批量验证** | 3个 | 混合文件、目录扫描、空列表 | 100% |
| **MIME类型** | 3个 | 常见扩展名、未知格式、无扩展名 | 100% |
| **文本文件检测** | 3个 | 纯文本、JSON、二进制文件 | 100% |
| **错误处理** | 2个 | 访问权限、不存在目录 | 100% |
| **性能测试** | 1个 | 100个文件批量处理 | 100% |

**总计**: 35个测试用例，100%通过率

### **测试示例**

```csharp
[Fact]
[Trait("Category", "BasicSecurity")]
public void IsSafeFile_ValidTextFile_ShouldReturnTrue()
{
    // Arrange
    var testFile = Path.Combine(_testDirectory, "safe.txt");
    File.WriteAllText(testFile, "This is a safe text file.");

    // Act
    var result = _validator.IsSafeFile(testFile);

    // Assert
    Assert.True(result);
}

[Fact]
[Trait("Category", "ExtensionSafety")]
public void IsExtensionSafe_DoubleExtension_ShouldDetectThreat()
{
    // Arrange
    var doubleExtensionFile = "document.txt.exe";

    // Act
    var result = _validator.IsExtensionSafe(doubleExtensionFile);

    // Assert
    Assert.False(result);
}
```

## 🛡️ **最佳实践**

### **安全配置建议**

```csharp
// 创建安全配置
var config = new YIOConfig
{
    MaxSingleFileSize = 50 * 1024 * 1024,  // 50MB限制
    BlockedExtensions = new[]
    {
        ".exe", ".bat", ".cmd", ".scr", ".pif",
        ".vbs", ".js", ".jar", ".msi", ".dll"
    },
    EnablePathValidation = true,
    DefaultWorkingDirectory = @"C:\SafeUploads"
};

var validator = new YFileValidator(config);
```

### **文件上传安全流程**

```csharp
public async Task<bool> ProcessUploadedFile(string filePath)
{
    var validator = new YFileValidator();

    // 第一步：基础安全检查
    if (!validator.IsSafeFile(filePath))
    {
        await LogSecurityEvent($"危险文件被拒绝: {filePath}");
        return false;
    }

    // 第二步：格式特定验证
    var extension = Path.GetExtension(filePath).ToLowerInvariant();
    bool isValidFormat = extension switch
    {
        ".jpg" or ".jpeg" or ".png" or ".gif" => validator.IsValidImageFile(filePath),
        ".pdf" or ".doc" or ".docx" => validator.IsValidDocumentFile(filePath),
        ".zip" or ".rar" or ".7z" => validator.IsValidArchiveFile(filePath),
        _ => validator.IsTextFile(filePath)
    };

    if (!isValidFormat)
    {
        await LogSecurityEvent($"文件格式验证失败: {filePath}");
        return false;
    }

    // 第三步：移动到安全目录
    var safeDirectory = @"C:\SafeUploads";
    var safePath = Path.Combine(safeDirectory, Path.GetFileName(filePath));
    File.Move(filePath, safePath);

    await LogSecurityEvent($"文件安全处理完成: {safePath}");
    return true;
}
```

### **批量扫描最佳实践**

```csharp
public async Task<SecurityScanReport> PerformSecurityScan(string rootDirectory)
{
    var validator = new YFileValidator();
    var report = new SecurityScanReport();

    try
    {
        // 扫描危险文件
        var dangerousFiles = validator.GetDangerousFiles(rootDirectory, recursive: true);
        report.DangerousFiles = dangerousFiles;

        // 获取所有文件进行详细分析
        var allFiles = Directory.GetFiles(rootDirectory, "*", SearchOption.AllDirectories);
        var results = validator.ValidateFiles(allFiles);

        // 生成统计报告
        report.TotalFiles = allFiles.Length;
        report.SafeFiles = results.Count(r => r.Value);
        report.DangerousFiles = results.Count(r => !r.Value);
        report.ScanDate = DateTime.Now;

        // 按文件类型分类
        report.FileTypeStats = results
            .GroupBy(r => Path.GetExtension(r.Key).ToLowerInvariant())
            .ToDictionary(g => g.Key, g => new { Safe = g.Count(r => r.Value), Dangerous = g.Count(r => !r.Value) });

        return report;
    }
    catch (Exception ex)
    {
        report.Error = ex.Message;
        return report;
    }
}
```

### **错误处理模式**

```csharp
public bool SafeFileValidation(string filePath)
{
    try
    {
        var validator = new YFileValidator();
        return validator.IsSafeFile(filePath);
    }
    catch (UnauthorizedAccessException)
    {
        // 权限不足时的处理
        LogWarning($"文件访问权限不足: {filePath}");
        return false;  // 安全起见，拒绝处理
    }
    catch (FileNotFoundException)
    {
        // 文件不存在时的处理
        LogWarning($"文件不存在: {filePath}");
        return false;
    }
    catch (Exception ex)
    {
        // 其他异常的处理
        LogError($"文件验证过程中发生错误: {ex.Message}");
        return false;  // 出错时默认拒绝
    }
}
```

### **性能优化建议**

```csharp
// 大规模文件扫描优化
public async Task<List<string>> OptimizedBatchScan(IEnumerable<string> files)
{
    var validator = new YFileValidator();
    var dangerousFiles = new ConcurrentBag<string>();

    // 并行处理提高性能
    await Parallel.ForEachAsync(files, new ParallelOptions
    {
        MaxDegreeOfParallelism = Environment.ProcessorCount
    }, async (file, ct) =>
    {
        if (!validator.IsSafeFile(file))
        {
            dangerousFiles.Add(file);
        }
    });

    return dangerousFiles.ToList();
}
```

---

## 📚 **相关文档**

- [YFileEncryption README](YFileEncryption_README.md) - 文件加密工具
- [YDirectoryOperations README](../Core/YDirectoryOperations_README.md) - 目录操作工具
- [YFileOperations README](../Core/YFileOperations_README.md) - 文件操作工具
- [YPerformanceMonitor README](../Monitoring/YPerformanceMonitor_README.md) - 性能监控工具

## 🤝 **贡献指南**

欢迎提交 Issue 和 Pull Request 来改进 YFileValidator！

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
