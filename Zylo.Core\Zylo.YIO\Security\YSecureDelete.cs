using System;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

using Zylo.YIO.Config;

namespace Zylo.YIO.Security
{
    /// <summary>
    /// YSecureDelete - 企业级安全删除工具类
    ///
    /// 🔐 核心功能特性：
    /// • 多层次安全删除：多次覆写、随机数据填充、文件名混淆
    /// • 军用级数据擦除：符合DoD 5220.22-M标准的安全删除
    /// • 异步高性能处理：支持大文件和批量目录的异步删除
    /// • 可配置安全策略：自定义覆写次数、缓冲区大小、并发控制
    /// • 完整的审计日志：详细记录删除过程和安全操作
    /// • 跨平台兼容：Windows、Linux、macOS全平台支持
    ///
    /// 💡 设计原则：
    /// • 安全第一：多重覆写确保数据无法恢复
    /// • 性能优化：智能缓冲和异步处理，支持大规模删除
    /// • 可靠性：完善的错误处理和资源管理
    /// • 可配置性：灵活的安全策略和性能参数
    /// • 审计性：完整的操作日志和状态跟踪
    ///
    /// 📋 使用场景：
    /// • 敏感文件销毁：金融、医疗、法律等行业的数据销毁
    /// • 系统清理：临时文件、缓存文件的安全清理
    /// • 合规性要求：满足GDPR、HIPAA等法规的数据销毁要求
    /// • 企业安全：员工离职、设备报废时的数据清理
    /// • 开发测试：测试数据的安全清理和环境重置
    /// </summary>
    
    public partial class YSecureDelete : IDisposable
    {
        #region 私有字段和常量

        /// <summary>
        /// YIO配置实例，包含安全删除的各种配置参数
        /// </summary>
        private readonly YIOConfig _config;

        /// <summary>
        /// 密码学安全的随机数生成器，用于生成随机覆写数据
        /// </summary>
        private readonly RandomNumberGenerator _rng;

        /// <summary>
        /// 对象是否已被释放的标志
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 默认缓冲区大小（64KB）- 在性能和内存使用之间的平衡点
        /// </summary>
        private const int DefaultBufferSize = 64 * 1024;

        /// <summary>
        /// 最小覆写次数 - 确保基本的安全删除要求
        /// </summary>
        private const int MinimumPasses = 1;

        /// <summary>
        /// 最大覆写次数 - 防止过度覆写影响性能
        /// </summary>
        private const int MaximumPasses = 35;

        /// <summary>
        /// DoD 5220.22-M 标准覆写次数（美国国防部标准）
        /// </summary>
        private const int DoDStandardPasses = 3;

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 初始化YSecureDelete实例
        ///
        /// 🔧 初始化过程：
        /// 1. 验证并设置配置参数
        /// 2. 创建密码学安全的随机数生成器
        /// 3. 初始化内部状态和资源
        /// 4. 记录初始化日志信息
        /// </summary>
        /// <param name="config">YIO配置实例，包含安全删除的各种参数设置</param>
        /// <exception cref="ArgumentException">当配置参数无效时抛出</exception>
        public YSecureDelete(YIOConfig config)
        {
            // 参数验证：确保配置不为空，使用默认配置作为后备
            _config = config ?? new YIOConfig();

            // 验证配置参数的有效性
            ValidateConfiguration(_config);

            // 创建密码学安全的随机数生成器
            // 使用系统提供的最强随机数生成器确保覆写数据的不可预测性
            _rng = RandomNumberGenerator.Create();

            // 记录初始化信息（如果启用了访问日志）
            if (_config.EnableAccessLogging)
            {
                Console.WriteLine($"YSecureDelete 初始化完成 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"安全删除已启用: {_config.EnableSecureDelete}");
                Console.WriteLine($"默认覆写次数: {_config.SecureDeletePasses}");
                Console.WriteLine($"最大并发操作: {_config.MaxConcurrentOperations}");
            }
        }

        /// <summary>
        /// 无参构造函数（主要用于静态方法调用和依赖注入）
        ///
        /// 使用默认的YIOConfig配置创建实例，适用于：
        /// • 静态方法调用场景
        /// • 依赖注入容器自动创建
        /// • 简单使用场景，不需要自定义配置
        /// </summary>
        public YSecureDelete() : this(new YIOConfig())
        {
        }

        #endregion

        #region 配置验证和辅助方法

        /// <summary>
        /// 验证YIOConfig配置的有效性
        ///
        /// 🔍 验证项目：
        /// • 覆写次数范围检查
        /// • 缓冲区大小合理性
        /// • 并发操作数限制
        /// • 其他安全参数验证
        /// </summary>
        /// <param name="config">要验证的配置实例</param>
        /// <exception cref="ArgumentException">当配置参数无效时抛出</exception>
        private static void ValidateConfiguration(YIOConfig config)
        {
            // 验证覆写次数范围
            if (config.SecureDeletePasses < MinimumPasses || config.SecureDeletePasses > MaximumPasses)
            {
                throw new ArgumentException(
                    $"覆写次数必须在 {MinimumPasses} 到 {MaximumPasses} 之间，当前值: {config.SecureDeletePasses}");
            }

            // 验证缓冲区大小（至少4KB，最大16MB）
            if (config.AsyncBufferSize < 4096 || config.AsyncBufferSize > 16 * 1024 * 1024)
            {
                throw new ArgumentException(
                    $"缓冲区大小必须在 4KB 到 16MB 之间，当前值: {config.AsyncBufferSize} 字节");
            }

            // 验证并发操作数（至少1个，最大CPU核心数的2倍）
            var maxConcurrency = Environment.ProcessorCount * 2;
            if (config.MaxConcurrentOperations < 1 || config.MaxConcurrentOperations > maxConcurrency)
            {
                throw new ArgumentException(
                    $"最大并发操作数必须在 1 到 {maxConcurrency} 之间，当前值: {config.MaxConcurrentOperations}");
            }
        }

        /// <summary>
        /// 标准化覆写次数参数
        ///
        /// 确保覆写次数在合理范围内，并应用配置的默认值
        /// </summary>
        /// <param name="passes">用户指定的覆写次数</param>
        /// <returns>标准化后的覆写次数</returns>
        private int NormalizePasses(int passes)
        {
            // 如果用户指定的次数小于最小值，使用配置的默认值
            if (passes < MinimumPasses)
            {
                return _config.SecureDeletePasses;
            }

            // 如果超过最大值，限制为最大值
            if (passes > MaximumPasses)
            {
                return MaximumPasses;
            }

            return passes;
        }

        #endregion

        #region 核心安全删除方法 - 文件操作

        /// <summary>
        /// 安全删除单个文件（同步版本）
        ///
        /// 🔐 安全删除流程：
        /// 1. 文件存在性和权限检查
        /// 2. 移除文件只读属性
        /// 3. 多次覆写文件内容（使用不同模式）
        /// 4. 文件名混淆处理
        /// 5. 最终删除文件
        /// 6. 验证删除结果
        ///
        /// 💡 覆写模式说明：
        /// • 第1次：全零覆写 (0x00) - 清除原始数据
        /// • 第2次：全一覆写 (0xFF) - 反转位模式
        /// • 第3次：交替模式 (0x55) - 特定位模式
        /// • 后续：密码学安全随机数据 - 确保不可恢复
        /// </summary>
        /// <param name="filePath">要删除的文件路径，支持相对路径和绝对路径</param>
        /// <param name="passes">覆写次数，默认3次（DoD标准），范围1-35次</param>
        /// <returns>删除成功返回true，失败返回false</returns>
        /// <exception cref="ArgumentException">当文件路径无效时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有文件访问权限时抛出</exception>
        /// <exception cref="IOException">当文件被其他进程占用时抛出</exception>
        public bool SecureDeleteFile(string filePath, int passes = DoDStandardPasses)
        {
            // 检查对象是否已被释放
            ThrowIfDisposed();

            try
            {
                // 第一步：输入参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    LogError("文件路径不能为空或空白字符");
                    return false;
                }

                // 第二步：文件存在性检查
                if (!File.Exists(filePath))
                {
                    LogWarning($"文件不存在，无需删除: {filePath}");
                    return true; // 文件不存在视为删除成功
                }

                // 第三步：标准化覆写次数
                passes = NormalizePasses(passes);
                LogInfo($"开始安全删除文件: {filePath}，覆写次数: {passes}");

                // 第四步：获取文件信息并准备删除
                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // 记录文件基本信息
                LogInfo($"文件大小: {fileSize:N0} 字节 ({fileSize / 1024.0 / 1024.0:F2} MB)");

                // 第五步：移除文件只读属性，确保可以写入
                if (fileInfo.IsReadOnly)
                {
                    LogInfo("检测到只读文件，正在移除只读属性...");
                    fileInfo.IsReadOnly = false;
                }

                // 第六步：执行多次覆写操作
                LogInfo($"开始执行 {passes} 次覆写操作...");
                for (int pass = 0; pass < passes; pass++)
                {
                    LogInfo($"执行第 {pass + 1}/{passes} 次覆写...");
                    OverwriteFileContent(filePath, fileSize, pass);
                }

                // 第七步：文件名混淆处理
                LogInfo("开始文件名混淆处理...");
                var obfuscatedPath = ObfuscateFileName(filePath);
                if (obfuscatedPath != filePath)
                {
                    File.Move(filePath, obfuscatedPath);
                    filePath = obfuscatedPath;
                    LogInfo($"文件名已混淆: {Path.GetFileName(obfuscatedPath)}");
                }

                // 第八步：最终删除文件
                LogInfo("执行最终删除操作...");
                File.Delete(filePath);

                // 第九步：验证删除结果
                if (File.Exists(filePath))
                {
                    LogError($"文件删除验证失败，文件仍然存在: {filePath}");
                    return false;
                }

                LogSuccess($"文件已安全删除: {Path.GetFileName(filePath)}");
                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError($"访问权限不足，无法删除文件: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                LogError($"文件IO错误，可能被其他进程占用: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogError($"安全删除文件时发生未知错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步安全删除单个文件（高性能版本）
        ///
        /// 🚀 异步优势：
        /// • 非阻塞操作：不会阻塞调用线程
        /// • 高性能处理：适合大文件和批量操作
        /// • 资源优化：更好的内存和CPU利用率
        /// • 并发支持：可以同时处理多个文件
        ///
        /// 🔐 安全删除流程（与同步版本相同）：
        /// 1. 参数验证和文件检查
        /// 2. 文件属性处理
        /// 3. 异步多次覆写
        /// 4. 文件名混淆
        /// 5. 最终删除和验证
        ///
        /// 💡 适用场景：
        /// • 大文件删除（>100MB）
        /// • 批量文件处理
        /// • Web应用程序
        /// • 高并发环境
        /// </summary>
        /// <param name="filePath">要删除的文件路径，支持相对路径和绝对路径</param>
        /// <param name="passes">覆写次数，默认3次（DoD标准），范围1-35次</param>
        /// <param name="cancellationToken">取消令牌，用于取消长时间运行的操作</param>
        /// <returns>删除成功返回true，失败返回false</returns>
        /// <exception cref="OperationCanceledException">当操作被取消时抛出</exception>
        /// <exception cref="ArgumentException">当文件路径无效时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有文件访问权限时抛出</exception>
        /// <exception cref="IOException">当文件被其他进程占用时抛出</exception>
        public async Task<bool> SecureDeleteFileAsync(string filePath, int passes = DoDStandardPasses, CancellationToken cancellationToken = default)
        {
            // 检查对象是否已被释放
            ThrowIfDisposed();

            try
            {
                // 第一步：输入参数验证
                if (string.IsNullOrWhiteSpace(filePath))
                {
                    LogError("文件路径不能为空或空白字符");
                    return false;
                }

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 第二步：文件存在性检查
                if (!File.Exists(filePath))
                {
                    LogWarning($"文件不存在，无需删除: {filePath}");
                    return true; // 文件不存在视为删除成功
                }

                // 第三步：标准化覆写次数
                passes = NormalizePasses(passes);
                LogInfo($"开始异步安全删除文件: {filePath}，覆写次数: {passes}");

                // 第四步：获取文件信息并准备删除
                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // 记录文件基本信息
                LogInfo($"文件大小: {fileSize:N0} 字节 ({fileSize / 1024.0 / 1024.0:F2} MB)");

                // 第五步：移除文件只读属性，确保可以写入
                if (fileInfo.IsReadOnly)
                {
                    LogInfo("检测到只读文件，正在移除只读属性...");
                    fileInfo.IsReadOnly = false;
                }

                // 第六步：执行多次异步覆写操作
                LogInfo($"开始执行 {passes} 次异步覆写操作...");
                for (int pass = 0; pass < passes; pass++)
                {
                    // 检查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();

                    LogInfo($"执行第 {pass + 1}/{passes} 次异步覆写...");
                    await OverwriteFileContentAsync(filePath, fileSize, pass, cancellationToken);
                }

                // 第七步：文件名混淆处理
                LogInfo("开始文件名混淆处理...");
                var obfuscatedPath = ObfuscateFileName(filePath);
                if (obfuscatedPath != filePath)
                {
                    File.Move(filePath, obfuscatedPath);
                    filePath = obfuscatedPath;
                    LogInfo($"文件名已混淆: {Path.GetFileName(obfuscatedPath)}");
                }

                // 第八步：最终删除文件
                LogInfo("执行最终删除操作...");
                File.Delete(filePath);

                // 第九步：验证删除结果
                if (File.Exists(filePath))
                {
                    LogError($"文件删除验证失败，文件仍然存在: {filePath}");
                    return false;
                }

                LogSuccess($"文件已异步安全删除: {Path.GetFileName(filePath)}");
                return true;
            }
            catch (OperationCanceledException)
            {
                LogWarning($"异步安全删除操作已被取消: {filePath}");
                throw; // 重新抛出取消异常
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError($"访问权限不足，无法删除文件: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                LogError($"文件IO错误，可能被其他进程占用: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogError($"异步安全删除文件时发生未知错误: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 核心安全删除方法 - 目录操作

        /// <summary>
        /// 安全删除目录及其所有内容（递归删除）
        ///
        /// 🗂️ 目录删除流程：
        /// 1. 目录存在性和权限检查
        /// 2. 递归扫描所有子文件和子目录
        /// 3. 按深度优先顺序处理（先文件后目录）
        /// 4. 对每个文件执行安全删除
        /// 5. 清空目录后删除目录本身
        /// 6. 验证删除结果
        ///
        /// 🔐 安全特性：
        /// • 递归处理：自动处理所有子目录和文件
        /// • 权限处理：自动移除只读属性和权限限制
        /// • 错误恢复：单个文件失败不影响整体删除
        /// • 进度跟踪：详细的删除进度和统计信息
        /// • 审计日志：完整的删除操作记录
        ///
        /// ⚠️ 注意事项：
        /// • 此操作不可逆，请谨慎使用
        /// • 大目录删除可能需要较长时间
        /// • 确保有足够的权限访问所有文件
        /// • 建议先备份重要数据
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径，支持相对路径和绝对路径</param>
        /// <param name="passes">覆写次数，默认3次（DoD标准），应用于所有文件</param>
        /// <returns>删除成功返回true，失败返回false</returns>
        /// <exception cref="ArgumentException">当目录路径无效时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有目录访问权限时抛出</exception>
        /// <exception cref="IOException">当目录被其他进程占用时抛出</exception>
        public bool SecureDeleteDirectory(string directoryPath, int passes = DoDStandardPasses)
        {
            // 检查对象是否已被释放
            ThrowIfDisposed();

            try
            {
                // 第一步：输入参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                {
                    LogError("目录路径不能为空或空白字符");
                    return false;
                }

                // 第二步：目录存在性检查
                if (!Directory.Exists(directoryPath))
                {
                    LogWarning($"目录不存在，无需删除: {directoryPath}");
                    return true; // 目录不存在视为删除成功
                }

                // 第三步：标准化覆写次数
                passes = NormalizePasses(passes);
                LogInfo($"开始安全删除目录: {directoryPath}，覆写次数: {passes}");

                // 第四步：递归扫描所有文件
                LogInfo("正在扫描目录结构...");
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                var directories = Directory.GetDirectories(directoryPath, "*", SearchOption.AllDirectories);

                // 记录扫描结果
                LogInfo($"发现 {files.Length} 个文件，{directories.Length} 个子目录");

                // 第五步：计算总大小（用于进度跟踪）
                long totalSize = 0;
                foreach (var file in files)
                {
                    try
                    {
                        totalSize += new FileInfo(file).Length;
                    }
                    catch
                    {
                        // 忽略无法访问的文件
                    }
                }
                LogInfo($"目录总大小: {totalSize:N0} 字节 ({totalSize / 1024.0 / 1024.0:F2} MB)");

                // 第六步：逐个安全删除文件
                int deletedFiles = 0;
                int failedFiles = 0;
                LogInfo("开始删除文件...");

                foreach (var file in files)
                {
                    try
                    {
                        if (SecureDeleteFile(file, passes))
                        {
                            deletedFiles++;
                            LogInfo($"进度: {deletedFiles}/{files.Length} - 已删除: {Path.GetFileName(file)}");
                        }
                        else
                        {
                            failedFiles++;
                            LogWarning($"删除文件失败: {file}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFiles++;
                        LogError($"删除文件时发生异常: {file} - {ex.Message}");
                    }
                }

                // 第七步：删除空目录结构
                LogInfo("开始删除目录结构...");
                DeleteEmptyDirectories(directoryPath);

                // 第八步：验证删除结果
                if (Directory.Exists(directoryPath))
                {
                    LogError($"目录删除验证失败，目录仍然存在: {directoryPath}");
                    return false;
                }

                // 第九步：输出删除统计
                LogSuccess($"目录已安全删除: {Path.GetFileName(directoryPath)}");
                LogInfo($"删除统计 - 成功: {deletedFiles}, 失败: {failedFiles}, 总计: {files.Length}");

                return failedFiles == 0; // 只有所有文件都删除成功才返回true
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError($"访问权限不足，无法删除目录: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                LogError($"目录IO错误，可能被其他进程占用: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogError($"安全删除目录时发生未知错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步安全删除目录及其所有内容（高性能并行版本）
        ///
        /// 🚀 异步并行优势：
        /// • 高性能处理：多文件并行删除，充分利用系统资源
        /// • 智能并发控制：根据系统配置限制并发数，避免资源耗尽
        /// • 非阻塞操作：不会阻塞调用线程，适合Web应用
        /// • 取消支持：支持操作取消，可以中断长时间运行的删除
        /// • 进度监控：实时进度反馈和统计信息
        ///
        /// 🔐 安全删除流程（与同步版本相同）：
        /// 1. 参数验证和目录检查
        /// 2. 递归扫描文件和目录
        /// 3. 并行安全删除所有文件
        /// 4. 清理空目录结构
        /// 5. 验证删除结果
        ///
        /// 💡 适用场景：
        /// • 大目录删除（包含大量文件）
        /// • 高并发环境
        /// • Web应用程序
        /// • 需要进度监控的场景
        /// </summary>
        /// <param name="directoryPath">要删除的目录路径，支持相对路径和绝对路径</param>
        /// <param name="passes">覆写次数，默认3次（DoD标准），应用于所有文件</param>
        /// <param name="cancellationToken">取消令牌，用于取消长时间运行的操作</param>
        /// <returns>删除成功返回true，失败返回false</returns>
        /// <exception cref="OperationCanceledException">当操作被取消时抛出</exception>
        /// <exception cref="ArgumentException">当目录路径无效时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有目录访问权限时抛出</exception>
        /// <exception cref="IOException">当目录被其他进程占用时抛出</exception>
        public async Task<bool> SecureDeleteDirectoryAsync(string directoryPath, int passes = DoDStandardPasses, CancellationToken cancellationToken = default)
        {
            // 检查对象是否已被释放
            ThrowIfDisposed();

            try
            {
                // 第一步：输入参数验证
                if (string.IsNullOrWhiteSpace(directoryPath))
                {
                    LogError("目录路径不能为空或空白字符");
                    return false;
                }

                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 第二步：目录存在性检查
                if (!Directory.Exists(directoryPath))
                {
                    LogWarning($"目录不存在，无需删除: {directoryPath}");
                    return true; // 目录不存在视为删除成功
                }

                // 第三步：标准化覆写次数
                passes = NormalizePasses(passes);
                LogInfo($"开始异步安全删除目录: {directoryPath}，覆写次数: {passes}");

                // 第四步：递归扫描所有文件
                LogInfo("正在扫描目录结构...");
                var files = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories);
                var directories = Directory.GetDirectories(directoryPath, "*", SearchOption.AllDirectories);

                // 记录扫描结果
                LogInfo($"发现 {files.Length} 个文件，{directories.Length} 个子目录");

                // 第五步：计算总大小（用于进度跟踪）
                long totalSize = 0;
                foreach (var file in files)
                {
                    try
                    {
                        totalSize += new FileInfo(file).Length;
                    }
                    catch
                    {
                        // 忽略无法访问的文件
                    }
                }
                LogInfo($"目录总大小: {totalSize:N0} 字节 ({totalSize / 1024.0 / 1024.0:F2} MB)");

                // 第六步：并行删除文件（智能并发控制）
                LogInfo($"开始并行删除文件，最大并发数: {_config.MaxConcurrentOperations}");

                using var semaphore = new SemaphoreSlim(_config.MaxConcurrentOperations);
                var deleteTasks = files.Select(async file =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        // 检查取消令牌
                        cancellationToken.ThrowIfCancellationRequested();

                        var result = await SecureDeleteFileAsync(file, passes, cancellationToken);
                        if (result)
                        {
                            LogInfo($"并行删除成功: {Path.GetFileName(file)}");
                        }
                        else
                        {
                            LogWarning($"并行删除失败: {file}");
                        }
                        return result;
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                // 第七步：等待所有删除任务完成
                LogInfo("等待所有文件删除完成...");
                var results = await Task.WhenAll(deleteTasks);

                // 第八步：统计删除结果
                var successCount = results.Count(r => r);
                var failureCount = results.Count(r => !r);
                LogInfo($"并行删除完成 - 成功: {successCount}, 失败: {failureCount}, 总计: {files.Length}");

                // 第九步：删除空目录结构
                if (successCount > 0)
                {
                    LogInfo("开始删除目录结构...");
                    DeleteEmptyDirectories(directoryPath);
                }

                // 第十步：验证删除结果
                if (Directory.Exists(directoryPath))
                {
                    LogError($"目录删除验证失败，目录仍然存在: {directoryPath}");
                    return false;
                }

                LogSuccess($"目录已异步安全删除: {Path.GetFileName(directoryPath)}");
                return failureCount == 0; // 只有所有文件都删除成功才返回true
            }
            catch (OperationCanceledException)
            {
                LogWarning($"异步安全删除目录操作已被取消: {directoryPath}");
                throw; // 重新抛出取消异常
            }
            catch (UnauthorizedAccessException ex)
            {
                LogError($"访问权限不足，无法删除目录: {ex.Message}");
                return false;
            }
            catch (IOException ex)
            {
                LogError($"目录IO错误，可能被其他进程占用: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                LogError($"异步安全删除目录时发生未知错误: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 核心覆写方法 - 文件内容擦除

        /// <summary>
        /// 同步覆写文件内容（核心安全擦除方法）
        ///
        /// 🔐 覆写策略详解：
        /// • 第1次覆写：全零模式 (0x00) - 清除原始数据痕迹
        /// • 第2次覆写：全一模式 (0xFF) - 反转所有位，增强安全性
        /// • 第3次覆写：交替模式 (0x55) - 特定位模式，进一步混淆
        /// • 后续覆写：密码学安全随机数据 - 确保数据完全不可恢复
        ///
        /// 💡 技术实现：
        /// • 智能缓冲：根据文件大小动态调整缓冲区
        /// • 流式处理：避免大文件内存溢出
        /// • 强制刷新：确保数据写入物理存储
        /// • 进度跟踪：支持大文件的进度监控
        ///
        /// 🛡️ 安全保证：
        /// • 符合DoD 5220.22-M标准
        /// • 防止数据恢复工具恢复
        /// • 适用于机械硬盘和SSD
        /// </summary>
        /// <param name="filePath">要覆写的文件路径</param>
        /// <param name="fileSize">文件大小（字节）</param>
        /// <param name="pass">当前覆写轮次（从0开始）</param>
        /// <exception cref="IOException">当文件无法访问时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有写入权限时抛出</exception>
        private void OverwriteFileContent(string filePath, long fileSize, int pass)
        {
            // 使用using语句确保文件流正确释放
            using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Write, FileShare.None);

            // 智能缓冲区大小计算：
            // - 最小4KB，最大配置的缓冲区大小
            // - 对于小文件，使用文件大小作为缓冲区
            var bufferSize = Math.Min(_config.AsyncBufferSize, Math.Max(4096, (int)Math.Min(fileSize, DefaultBufferSize)));
            var buffer = new byte[bufferSize];

            // 根据覆写轮次填充缓冲区
            FillBuffer(buffer, pass);

            // 执行覆写操作
            long totalWritten = 0;
            while (totalWritten < fileSize)
            {
                // 计算本次要写入的字节数
                var bytesToWrite = (int)Math.Min(bufferSize, fileSize - totalWritten);

                // 写入数据到文件
                stream.Write(buffer, 0, bytesToWrite);
                totalWritten += bytesToWrite;

                // 对于大文件，定期刷新缓冲区
                if (totalWritten % (bufferSize * 10) == 0)
                {
                    stream.Flush();
                }
            }

            // 最终强制刷新，确保所有数据写入物理存储
            stream.Flush();

            // 在Windows上，强制同步到磁盘
            if (Environment.OSVersion.Platform == PlatformID.Win32NT)
            {
                try
                {
                    // 尝试调用FlushFileBuffers（如果可用）
                    stream.Flush(true);
                }
                catch
                {
                    // 忽略不支持的平台
                }
            }
        }

        /// <summary>
        /// 异步覆写文件内容（高性能版本）
        ///
        /// 🚀 异步优势：
        /// • 非阻塞IO：不会阻塞调用线程，提高系统响应性
        /// • 高并发支持：可以同时处理多个文件的覆写操作
        /// • 取消支持：支持操作取消，避免资源浪费
        /// • 内存优化：使用流式处理，避免大文件内存占用
        ///
        /// 🔐 安全特性（与同步版本相同）：
        /// • 多模式覆写：零、一、交替、随机模式
        /// • 强制刷新：确保数据写入物理存储
        /// • 缓冲优化：智能缓冲区大小调整
        /// • 跨平台兼容：支持不同操作系统
        ///
        /// 💡 性能优化：
        /// • 异步IO操作：充分利用系统IO能力
        /// • 批量写入：减少系统调用次数
        /// • 定期刷新：平衡性能和数据安全
        /// </summary>
        /// <param name="filePath">要覆写的文件路径</param>
        /// <param name="fileSize">文件大小（字节）</param>
        /// <param name="pass">当前覆写轮次（从0开始）</param>
        /// <param name="cancellationToken">取消令牌，用于取消长时间运行的操作</param>
        /// <exception cref="OperationCanceledException">当操作被取消时抛出</exception>
        /// <exception cref="IOException">当文件无法访问时抛出</exception>
        /// <exception cref="UnauthorizedAccessException">当没有写入权限时抛出</exception>
        private async Task OverwriteFileContentAsync(string filePath, long fileSize, int pass, CancellationToken cancellationToken = default)
        {
            // 使用using语句确保文件流正确释放
            using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Write, FileShare.None,
                bufferSize: _config.AsyncBufferSize, useAsync: true);

            // 智能缓冲区大小计算（与同步版本相同）
            var bufferSize = Math.Min(_config.AsyncBufferSize, Math.Max(4096, (int)Math.Min(fileSize, DefaultBufferSize)));
            var buffer = new byte[bufferSize];

            // 根据覆写轮次填充缓冲区
            FillBuffer(buffer, pass);

            // 执行异步覆写操作
            long totalWritten = 0;
            while (totalWritten < fileSize)
            {
                // 检查取消令牌
                cancellationToken.ThrowIfCancellationRequested();

                // 计算本次要写入的字节数
                var bytesToWrite = (int)Math.Min(bufferSize, fileSize - totalWritten);

                // 异步写入数据到文件
                await stream.WriteAsync(buffer, 0, bytesToWrite, cancellationToken);
                totalWritten += bytesToWrite;

                // 对于大文件，定期异步刷新缓冲区
                if (totalWritten % (bufferSize * 10) == 0)
                {
                    await stream.FlushAsync(cancellationToken);
                }
            }

            // 最终异步强制刷新，确保所有数据写入物理存储
            await stream.FlushAsync(cancellationToken);
        }

        #endregion

        #region 数据覆写策略方法

        /// <summary>
        /// 根据覆写轮次填充缓冲区（核心安全策略）
        ///
        /// 🔐 覆写模式详解：
        ///
        /// 📋 第1次覆写 (pass % 4 == 0)：全零模式 (0x00)
        /// • 目的：清除原始数据的磁性痕迹
        /// • 原理：将所有位设置为0，消除原始数据模式
        /// • 效果：破坏原始数据的基本结构
        ///
        /// 📋 第2次覆写 (pass % 4 == 1)：全一模式 (0xFF)
        /// • 目的：反转所有位，进一步混淆数据
        /// • 原理：将所有位设置为1，与第一次形成对比
        /// • 效果：确保每个位都经历了0→1的变化
        ///
        /// 📋 第3次覆写 (pass % 4 == 2)：交替模式 (0x55)
        /// • 目的：创建特定的位模式，增强安全性
        /// • 原理：0x55 = 01010101，创建交替的位模式
        /// • 效果：进一步混淆磁性残留信息
        ///
        /// 📋 第4+次覆写 (pass % 4 == 3)：密码学安全随机数据
        /// • 目的：使用不可预测的随机数据最终覆盖
        /// • 原理：密码学安全的随机数生成器产生高质量随机数
        /// • 效果：确保数据完全不可恢复，符合最高安全标准
        ///
        /// 🛡️ 安全保证：
        /// • 符合美国国防部DoD 5220.22-M标准
        /// • 防止磁力显微镜等高级恢复技术
        /// • 适用于机械硬盘和固态硬盘
        /// • 通过多种模式组合确保最大安全性
        /// </summary>
        /// <param name="buffer">要填充的缓冲区数组</param>
        /// <param name="pass">当前覆写轮次（从0开始计数）</param>
        /// <exception cref="ArgumentNullException">当缓冲区为null时抛出</exception>
        private void FillBuffer(byte[] buffer, int pass)
        {
            // 参数验证
            if (buffer == null)
                throw new ArgumentNullException(nameof(buffer));

            // 根据覆写轮次选择不同的填充策略
            switch (pass % 4)
            {
                case 0:
                    // 第1次：全零覆写 - 清除原始数据痕迹
                    Array.Fill<byte>(buffer, 0x00);
                    break;

                case 1:
                    // 第2次：全一覆写 - 反转所有位
                    Array.Fill<byte>(buffer, 0xFF);
                    break;

                case 2:
                    // 第3次：交替模式 - 特定位模式 (01010101)
                    Array.Fill<byte>(buffer, 0x55);
                    break;

                default:
                    // 第4+次：密码学安全随机数据 - 最终安全覆写
                    _rng.GetBytes(buffer);
                    break;
            }
        }

        #endregion

        #region 文件名混淆和目录清理方法

        /// <summary>
        /// 混淆文件名（防止通过文件名推断内容）
        ///
        /// 🔐 文件名混淆策略：
        /// • 随机文件名生成：使用系统提供的安全随机文件名
        /// • 冲突检测：确保生成的文件名不与现有文件冲突
        /// • 路径保持：保持原始目录结构，只混淆文件名
        /// • 错误恢复：混淆失败时安全降级到原文件名
        ///
        /// 💡 安全意义：
        /// • 隐藏文件内容：防止通过文件名推断文件内容
        /// • 审计困难：增加数据恢复和审计的难度
        /// • 完整性保护：确保删除过程的完整性
        /// • 合规要求：满足某些行业的数据销毁标准
        ///
        /// 🛡️ 实现特点：
        /// • 系统级随机：使用操作系统提供的随机文件名生成
        /// • 冲突避免：自动检测并避免文件名冲突
        /// • 异常安全：混淆失败不影响删除操作
        /// • 跨平台兼容：支持不同操作系统的路径规则
        /// </summary>
        /// <param name="filePath">原始文件路径，包含目录和文件名</param>
        /// <returns>混淆后的文件路径，如果混淆失败则返回原路径</returns>
        private string ObfuscateFileName(string filePath)
        {
            try
            {
                // 分离目录和文件名
                var directory = Path.GetDirectoryName(filePath);

                // 生成随机文件名（系统保证的安全随机性）
                var randomName = Path.GetRandomFileName();
                var obfuscatedPath = Path.Combine(directory ?? "", randomName);

                // 冲突检测和解决：确保生成的文件名不与现有文件冲突
                int attempts = 0;
                const int maxAttempts = 10; // 最大尝试次数，防止无限循环

                while (File.Exists(obfuscatedPath) && attempts < maxAttempts)
                {
                    randomName = Path.GetRandomFileName();
                    obfuscatedPath = Path.Combine(directory ?? "", randomName);
                    attempts++;
                }

                // 如果经过多次尝试仍然冲突，添加时间戳确保唯一性
                if (File.Exists(obfuscatedPath))
                {
                    var timestamp = DateTime.Now.Ticks.ToString("X");
                    randomName = $"{Path.GetRandomFileName()}_{timestamp}";
                    obfuscatedPath = Path.Combine(directory ?? "", randomName);
                }

                return obfuscatedPath;
            }
            catch (Exception ex)
            {
                // 混淆失败时记录警告并返回原路径
                // 这确保删除操作可以继续进行，不会因为文件名混淆失败而中断
                LogWarning($"文件名混淆失败，使用原文件名: {ex.Message}");
                return filePath;
            }
        }

        /// <summary>
        /// 递归删除空目录结构（深度优先删除）
        ///
        /// 🗂️ 目录删除策略：
        /// • 深度优先遍历：从最深层的子目录开始删除
        /// • 空目录检测：只删除完全为空的目录
        /// • 递归处理：自动处理多层嵌套的目录结构
        /// • 错误隔离：单个目录删除失败不影响其他目录
        ///
        /// 💡 实现原理：
        /// • 后序遍历：先处理子目录，再处理父目录
        /// • 安全检查：确保目录完全为空才删除
        /// • 异常处理：优雅处理权限和访问问题
        /// • 日志记录：详细记录删除过程和结果
        ///
        /// 🛡️ 安全特性：
        /// • 防误删：只删除空目录，保护有内容的目录
        /// • 权限处理：自动处理只读和权限限制
        /// • 错误恢复：单点失败不影响整体删除
        /// • 审计跟踪：完整的删除操作日志
        /// </summary>
        /// <param name="directoryPath">要删除的根目录路径</param>
        private void DeleteEmptyDirectories(string directoryPath)
        {
            try
            {
                // 第一步：递归删除所有子目录（深度优先）
                var subDirectories = Directory.GetDirectories(directoryPath);
                foreach (var subDir in subDirectories)
                {
                    // 递归处理子目录
                    DeleteEmptyDirectories(subDir);
                }

                // 第二步：检查当前目录是否为空
                var entries = Directory.EnumerateFileSystemEntries(directoryPath);
                if (!entries.Any())
                {
                    // 第三步：尝试删除空目录
                    var dirInfo = new DirectoryInfo(directoryPath);

                    // 移除只读属性（如果有）
                    if ((dirInfo.Attributes & FileAttributes.ReadOnly) == FileAttributes.ReadOnly)
                    {
                        dirInfo.Attributes &= ~FileAttributes.ReadOnly;
                        LogInfo($"移除目录只读属性: {Path.GetFileName(directoryPath)}");
                    }

                    // 删除空目录
                    Directory.Delete(directoryPath);
                    LogInfo($"已删除空目录: {Path.GetFileName(directoryPath)}");
                }
                else
                {
                    // 目录不为空，记录信息但不删除
                    LogInfo($"目录不为空，保留: {Path.GetFileName(directoryPath)}");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                LogWarning($"删除目录权限不足: {Path.GetFileName(directoryPath)} - {ex.Message}");
            }
            catch (IOException ex)
            {
                LogWarning($"删除目录IO错误: {Path.GetFileName(directoryPath)} - {ex.Message}");
            }
            catch (Exception ex)
            {
                LogError($"删除空目录时发生未知错误: {Path.GetFileName(directoryPath)} - {ex.Message}");
            }
        }

        #endregion

        #region 日志记录方法

        /// <summary>
        /// 记录信息级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogInfo(string message)
        {
            if (_config.EnableAccessLogging)
            {
                Console.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            }
        }

        /// <summary>
        /// 记录成功级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogSuccess(string message)
        {
            if (_config.EnableAccessLogging)
            {
                Console.WriteLine($"[SUCCESS] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            }
        }

        /// <summary>
        /// 记录警告级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogWarning(string message)
        {
            if (_config.EnableAccessLogging)
            {
                Console.WriteLine($"[WARNING] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            }
        }

        /// <summary>
        /// 记录错误级别日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogError(string message)
        {
            if (_config.EnableAccessLogging)
            {
                Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}");
            }
        }

        /// <summary>
        /// 检查对象是否已被释放，如果已释放则抛出异常
        /// </summary>
        /// <exception cref="ObjectDisposedException">当对象已被释放时抛出</exception>
        private void ThrowIfDisposed()
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(nameof(YSecureDelete), "YSecureDelete对象已被释放，无法继续使用");
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放YSecureDelete使用的所有资源
        ///
        /// 🔧 资源释放清单：
        /// • 密码学随机数生成器：释放加密资源
        /// • 内部状态清理：清理敏感数据
        /// • 标记对象已释放：防止后续使用
        ///
        /// 💡 使用建议：
        /// • 使用using语句自动释放资源
        /// • 在长期运行的应用中及时释放
        /// • 避免在释放后继续使用对象
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 受保护的资源释放方法
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 释放托管资源
                    try
                    {
                        _rng?.Dispose();
                        LogInfo("YSecureDelete 资源已释放");
                    }
                    catch (Exception ex)
                    {
                        // 释放过程中的异常不应该传播
                        LogError($"释放资源时发生错误: {ex.Message}");
                    }
                }

                // 标记为已释放
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数 - 确保资源得到释放
        /// </summary>
        ~YSecureDelete()
        {
            Dispose(false);
        }

        #endregion
    }
}
