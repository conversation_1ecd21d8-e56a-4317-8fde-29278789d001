using System;
using System.IO;
using Zylo.YIO.Core;

namespace Zylo.YIO.Tests
{
    /// <summary>
    /// YPath 功能测试类
    /// 用于验证路径工具类的各项功能
    /// </summary>
    public class YPathTests
    {
        private readonly YPath _pathUtils;

        public YPathTests()
        {
            _pathUtils = new YPath();
        }

        /// <summary>
        /// 测试基本路径操作
        /// </summary>
        public void TestBasicPathOperations()
        {
            Console.WriteLine("=== 测试基本路径操作 ===");

            // 测试路径组合
            var combined = _pathUtils.CombinePath("C:", "Users", "Name", "file.txt");
            Console.WriteLine($"路径组合: {combined}");

            // 测试安全路径组合
            var safePath = _pathUtils.SafeCombinePath(@"C:\Base", "sub\\file.txt");
            Console.WriteLine($"安全路径组合: {safePath}");

            // 测试危险路径（应该被阻止）
            var dangerousPath = _pathUtils.SafeCombinePath(@"C:\Base", "..\\..\\system32\\file.txt");
            Console.WriteLine($"危险路径测试: {dangerousPath}");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试路径转换功能
        /// </summary>
        public void TestPathConversion()
        {
            Console.WriteLine("=== 测试路径转换功能 ===");

            var windowsPath = @"C:\Users\<USER>\Documents\file.txt";
            
            // 测试转换为Unix格式
            var unixPath = _pathUtils.ToUnixPath(windowsPath);
            Console.WriteLine($"Windows -> Unix: {windowsPath} -> {unixPath}");

            // 测试转换回Windows格式
            var backToWindows = _pathUtils.ToWindowsPath(unixPath);
            Console.WriteLine($"Unix -> Windows: {unixPath} -> {backToWindows}");

            // 测试环境变量展开
            var envPath = "%TEMP%\\myfile.txt";
            var expandedPath = _pathUtils.ExpandEnvironmentVariables(envPath);
            Console.WriteLine($"环境变量展开: {envPath} -> {expandedPath}");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试路径验证功能
        /// </summary>
        public void TestPathValidation()
        {
            Console.WriteLine("=== 测试路径验证功能 ===");

            var testPaths = new[]
            {
                @"C:\Valid\Path\file.txt",
                @"\\server\share\file.txt",
                @"C:\Invalid<>Path\file.txt",
                @"C:\",
                "/usr/local/bin",
                "relative/path/file.txt"
            };

            foreach (var path in testPaths)
            {
                var isValid = _pathUtils.IsValidPath(path);
                var isSafe = _pathUtils.IsSafePath(path);
                var isNetwork = _pathUtils.IsNetworkPath(path);
                var isRoot = _pathUtils.IsRootPath(path);
                var isAbsolute = _pathUtils.IsAbsolutePath(path);

                Console.WriteLine($"路径: {path}");
                Console.WriteLine($"  有效: {isValid}, 安全: {isSafe}, 网络: {isNetwork}, 根: {isRoot}, 绝对: {isAbsolute}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试路径信息提取
        /// </summary>
        public void TestPathInformationExtraction()
        {
            Console.WriteLine("=== 测试路径信息提取 ===");

            var testPath = @"C:\Users\<USER>\Documents\project.tar.gz";
            
            // 测试基本信息提取
            var fileName = _pathUtils.GetFileName(testPath);
            var directory = _pathUtils.GetDirectoryName(testPath);
            var extension = _pathUtils.GetExtension(testPath);
            var root = _pathUtils.GetPathRoot(testPath);

            Console.WriteLine($"路径: {testPath}");
            Console.WriteLine($"文件名: {fileName}");
            Console.WriteLine($"目录: {directory}");
            Console.WriteLine($"扩展名: {extension}");
            Console.WriteLine($"根: {root}");

            // 测试多重扩展名
            var extensions = _pathUtils.GetExtensions(testPath);
            Console.WriteLine($"多重扩展名: [{string.Join(", ", extensions)}]");

            // 测试路径分解
            var pathParts = _pathUtils.GetPathParts(testPath);
            Console.WriteLine($"路径分解:");
            Console.WriteLine($"  完整路径: {pathParts.FullPath}");
            Console.WriteLine($"  深度: {pathParts.Depth}");
            Console.WriteLine($"  是文件: {pathParts.IsFile}");
            Console.WriteLine($"  分段: [{string.Join(", ", pathParts.Segments)}]");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试路径比较和匹配
        /// </summary>
        public void TestPathComparison()
        {
            Console.WriteLine("=== 测试路径比较和匹配 ===");

            // 测试路径相等比较
            var path1 = @"C:\Users\<USER>\file.txt";
            var path2 = @"c:\users\<USER>\file.txt";
            var areEqual = _pathUtils.PathEquals(path1, path2);
            Console.WriteLine($"路径相等: {path1} == {path2} -> {areEqual}");

            // 测试通配符匹配
            var patterns = new[] { "*.txt", "**/*.cs", "src/**/*.js" };
            var testFiles = new[]
            {
                "document.txt",
                @"src\utils\helper.cs",
                @"src\components\ui\button.js",
                "readme.md"
            };

            foreach (var pattern in patterns)
            {
                Console.WriteLine($"模式: {pattern}");
                foreach (var file in testFiles)
                {
                    var matches = _pathUtils.MatchesAdvancedPattern(file, pattern);
                    Console.WriteLine($"  {file} -> {matches}");
                }
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试临时路径生成
        /// </summary>
        public void TestTempPathGeneration()
        {
            Console.WriteLine("=== 测试临时路径生成 ===");

            // 测试临时文件路径
            var tempFile = _pathUtils.GetTempFileName(".log");
            Console.WriteLine($"临时文件: {tempFile}");

            // 测试临时目录路径
            var tempDir = _pathUtils.GetTempDirectoryPath("workspace");
            Console.WriteLine($"临时目录: {tempDir}");

            // 测试唯一路径生成
            var uniquePath = _pathUtils.GetUniquePath(@"C:\temp\test.txt");
            Console.WriteLine($"唯一路径: {uniquePath}");

            // 测试随机路径生成
            var randomPath = _pathUtils.GenerateRandomPath(@"C:\temp", ".tmp", "data");
            Console.WriteLine($"随机路径: {randomPath}");

            Console.WriteLine();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始 YPath 功能测试...\n");

            try
            {
                TestBasicPathOperations();
                TestPathConversion();
                TestPathValidation();
                TestPathInformationExtraction();
                TestPathComparison();
                TestTempPathGeneration();

                Console.WriteLine("所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
