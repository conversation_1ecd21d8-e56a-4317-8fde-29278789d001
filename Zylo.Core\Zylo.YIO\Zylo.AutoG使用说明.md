# 🚀 Zylo.AutoG 在 Zylo.YIO 中的使用说明

## 📋 目录

- [概述](#概述)
- [YService - 依赖注入服务](#yservice---依赖注入服务)
- [YStatic - 静态方法扩展](#ystatic---静态方法扩展)
- [YDoc - 文档生成](#ydoc---文档生成)
- [YLog - 智能日志系统](#ylog---智能日志系统)
- [组合使用示例](#组合使用示例)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🌟 概述

**Zylo.AutoG** 是一个强大的代码生成框架，为 Zylo.YIO 项目提供三大核心功能：

| 功能模块 | 主要用途 | 生成内容 | 适用场景 |
|----------|----------|----------|----------|
| **YService** | 依赖注入服务 | 接口、服务注册代码 | 业务服务、数据访问层 |
| **YStatic** | 静态方法扩展 | 扩展方法类、静态方法 | 工具类、辅助方法 |
| **YDoc** | 文档生成 | API文档、使用指南 | 文档自动化 |
| **YLog** | 智能日志系统 | 日志包装方法、性能监控 | 方法级日志记录 |

### 🎯 在 Zylo.YIO 中的应用价值

- **🔄 减少样板代码** - 自动生成重复性代码
- **📈 提升开发效率** - 专注业务逻辑而非基础设施
- **🛡️ 保证代码质量** - 统一的代码风格和最佳实践
- **📚 自动化文档** - 保持代码与文档同步

## 🏗️ YService - 依赖注入服务

### 核心概念

YService 为 Zylo.YIO 中的服务类提供自动依赖注入支持，生成接口和服务注册代码。

### 基础用法

#### 1. 配置文件处理服务

```csharp
/// <summary>
/// 配置文件处理服务 - 演示 YService 基础用法
/// </summary>
[YService.Scoped]  // 作用域生命周期
public partial class YConfigService
{
    /// <summary>
    /// 加载配置文件
    /// </summary>
    public T LoadConfig<T>(string configName) where T : class
    {
        var processor = new YConfigProcessor();
        return processor.Load<T>(configName);
    }

    /// <summary>
    /// 保存配置文件
    /// </summary>
    public bool SaveConfig<T>(string configName, T config) where T : class
    {
        var processor = new YConfigProcessor();
        return processor.Save(configName, config);
    }

    /// <summary>
    /// 内部方法 - 不生成到接口
    /// </summary>
    [YService.MNoInterface]
    private void ValidateConfig(string configName)
    {
        // 内部验证逻辑
    }
}

// 自动生成：
// - IYConfigService 接口
// - 服务注册扩展方法
```

#### 2. 文件操作服务

```csharp
/// <summary>
/// 文件操作服务 - 演示单例模式
/// </summary>
[YService.Singleton]  // 单例生命周期
public partial class YFileService
{
    /// <summary>
    /// 读取文件内容
    /// </summary>
    public async Task<string> ReadFileAsync(string filePath)
    {
        return await File.ReadAllTextAsync(filePath);
    }

    /// <summary>
    /// 写入文件内容
    /// </summary>
    public async Task<bool> WriteFileAsync(string filePath, string content)
    {
        try
        {
            await File.WriteAllTextAsync(filePath, content);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    public bool FileExists(string filePath) => File.Exists(filePath);
}
```

#### 3. 瞬态服务示例

```csharp
/// <summary>
/// 数据转换服务 - 演示瞬态模式
/// </summary>
[YService.Transient]  // 瞬态生命周期
public partial class YDataConverter
{
    /// <summary>
    /// JSON 转换
    /// </summary>
    public T ConvertFromJson<T>(string json) where T : class
    {
        return JsonSerializer.Deserialize<T>(json);
    }

    /// <summary>
    /// XML 转换
    /// </summary>
    public XDocument ConvertToXml<T>(T data) where T : class
    {
        // XML 转换逻辑
        return new XDocument();
    }
}
```

### 服务注册和使用

```csharp
// Program.cs 或 Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    // 使用自动生成的扩展方法注册服务
    services.AddYConfigService();    // Scoped
    services.AddYFileService();      // Singleton  
    services.AddYDataConverter();    // Transient
}

// 在其他服务中使用
public class MyController
{
    private readonly IYConfigService _configService;
    private readonly IYFileService _fileService;

    public MyController(IYConfigService configService, IYFileService fileService)
    {
        _configService = configService;
        _fileService = fileService;
    }

    public async Task<IActionResult> GetConfig()
    {
        var config = _configService.LoadConfig<AppConfig>("app");
        return Ok(config);
    }
}
```

## ⚡ YStatic - 静态方法扩展

### 核心概念

YStatic 为 Zylo.YIO 中的工具类提供扩展方法生成，让静态方法变成流畅的扩展方法。

### 基础用法

#### 1. 字符串工具扩展

```csharp
/// <summary>
/// 字符串处理工具 - 演示扩展方法生成
/// </summary>
[YStatic]
public static partial class YStringUtils
{
    /// <summary>
    /// 转换为配置文件名格式
    /// </summary>
    public static string ToConfigName(string input)
    {
        return input.ToLower().Replace(" ", "_");
    }

    /// <summary>
    /// 验证配置文件路径
    /// </summary>
    public static bool IsValidConfigPath(string path)
    {
        return !string.IsNullOrEmpty(path) && 
               (path.EndsWith(".json") || path.EndsWith(".xml") || path.EndsWith(".ini"));
    }

    /// <summary>
    /// 生成到静态类的方法
    /// </summary>
    [YStatic.MNoThis]
    public static string GenerateConfigId()
    {
        return Guid.NewGuid().ToString("N")[..8];
    }
}

// 使用方式：
string configName = "App Settings".ToConfigName();        // "app_settings"
bool isValid = "config.json".IsValidConfigPath();         // true
string id = YStringUtilsStatics.GenerateConfigId();       // "a1b2c3d4"
```

#### 2. 文件路径工具

```csharp
/// <summary>
/// 路径处理工具 - 演示 NoThis 类级属性
/// </summary>
[YStatic.NoThis]  // 整个类生成静态方法
public static partial class YPathUtils
{
    /// <summary>
    /// 获取配置文件目录
    /// </summary>
    public static string GetConfigDirectory()
    {
        return Path.Combine(Environment.CurrentDirectory, "configs");
    }

    /// <summary>
    /// 构建配置文件路径
    /// </summary>
    public static string BuildConfigPath(string configName, string extension = ".json")
    {
        var directory = GetConfigDirectory();
        return Path.Combine(directory, $"{configName}{extension}");
    }

    /// <summary>
    /// 确保目录存在
    /// </summary>
    public static void EnsureDirectoryExists(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }
    }
}

// 使用方式：
string configDir = YPathUtilsStatics.GetConfigDirectory();
string configPath = YPathUtilsStatics.BuildConfigPath("app");
YPathUtilsStatics.EnsureDirectoryExists(configDir);
```

#### 3. 混合模式工具

```csharp
/// <summary>
/// 配置验证工具 - 演示混合使用
/// </summary>
[YStatic]
public static partial class YConfigValidator
{
    /// <summary>
    /// 验证 JSON 格式 - 扩展方法
    /// </summary>
    public static bool IsValidJson(string json)
    {
        try
        {
            JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 验证 XML 格式 - 扩展方法
    /// </summary>
    public static bool IsValidXml(string xml)
    {
        try
        {
            XDocument.Parse(xml);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 生成验证报告 - 静态方法
    /// </summary>
    [YStatic.MNoThis]
    public static ValidationReport GenerateReport(string content, string format)
    {
        return new ValidationReport
        {
            IsValid = format.ToLower() switch
            {
                "json" => content.IsValidJson(),
                "xml" => content.IsValidXml(),
                _ => false
            },
            Format = format,
            Timestamp = DateTime.Now
        };
    }

    /// <summary>
    /// 内部方法 - 排除生成
    /// </summary>
    [YStatic.MExclude("内部实现")]
    private static void LogValidation(string message)
    {
        Console.WriteLine($"[Validation] {message}");
    }
}

// 使用方式：
bool isJsonValid = jsonContent.IsValidJson();                    // 扩展方法
bool isXmlValid = xmlContent.IsValidXml();                       // 扩展方法
var report = YConfigValidatorStatics.GenerateReport(content, "json");  // 静态方法
```

## 📚 YDoc - 文档生成

### 核心概念

YDoc 为 Zylo.YIO 中的类自动生成 API 文档和使用指南。

### 基础用法

#### 1. 配置处理器文档

```csharp
/// <summary>
/// 配置文件处理器 - 演示文档生成
/// </summary>
[YDoc]  // 启用文档生成
[YService.Scoped]
public partial class YConfigProcessor
{
    /// <summary>
    /// 加载配置文件
    /// </summary>
    /// <typeparam name="T">配置对象类型</typeparam>
    /// <param name="configName">配置名称</param>
    /// <returns>加载的配置对象</returns>
    /// <example>
    /// <code>
    /// var processor = new YConfigProcessor();
    /// var appConfig = processor.Load&lt;AppConfig&gt;("app");
    /// </code>
    /// </example>
    public T Load<T>(string configName) where T : class
    {
        // 实现逻辑
        return default(T);
    }

    /// <summary>
    /// 保存配置文件
    /// </summary>
    /// <typeparam name="T">配置对象类型</typeparam>
    /// <param name="configName">配置名称</param>
    /// <param name="config">配置对象</param>
    /// <returns>是否保存成功</returns>
    /// <exception cref="ArgumentNullException">当配置对象为空时抛出</exception>
    /// <example>
    /// <code>
    /// var processor = new YConfigProcessor();
    /// var success = processor.Save("app", appConfig);
    /// </code>
    /// </example>
    public bool Save<T>(string configName, T config) where T : class
    {
        if (config == null)
            throw new ArgumentNullException(nameof(config));

        // 实现逻辑
        return true;
    }
}

// 自动生成：
// - YConfigProcessor_API文档.md
// - YConfigProcessor_使用指南.md
```

#### 2. 工具类文档

```csharp
/// <summary>
/// 文件操作工具类
/// 提供常用的文件读写操作功能
/// </summary>
[YDoc]
[YStatic]
public static partial class YFileUtils
{
    /// <summary>
    /// 安全读取文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件内容，失败时返回空字符串</returns>
    /// <remarks>
    /// 此方法会自动处理文件不存在或读取失败的情况
    /// </remarks>
    /// <example>
    /// <code>
    /// string content = "config.json".SafeReadAllText();
    /// if (!string.IsNullOrEmpty(content))
    /// {
    ///     // 处理文件内容
    /// }
    /// </code>
    /// </example>
    public static string SafeReadAllText(string filePath)
    {
        try
        {
            return File.ReadAllText(filePath);
        }
        catch
        {
            return string.Empty;
        }
    }
}
```

## 🔥 YLog - 智能日志系统

### 核心概念

YLog 是基于 Roslyn 源代码生成器的高性能日志框架，为 Zylo.YIO 中的方法提供零运行时开销的自动化日志记录。通过编译时代码生成，实现无反射调用的智能日志包装。

### 🎯 核心特性

- **🚀 零运行时开销** - 编译时代码生成，无反射调用
- **⚡ 异步优先** - 内置异步处理，不阻塞主线程
- **🎯 双重日志模式** - 自动日志 + 手动日志完美结合
- **🆕 灵活命名** - 完全解除Core后缀约束，用户可使用任意方法名
- **🧬 完整泛型支持** - 支持所有泛型场景：多参数、约束、嵌套、异步泛型
- **🛡️ 全访问修饰符** - 支持 public、private、protected、internal
- **📁 智能文件管理** - 自动轮转、大小控制、多种命名模式
- **🔄 并发安全** - 支持高并发日志写入
- **🎨 便捷属性语法** - `[YLog.Debug]`、`[YLog.Error]` 等简洁语法

### 项目配置

#### 1. 添加项目引用

```xml
<!-- 在 .csproj 文件中添加 -->
<ItemGroup>
  <ProjectReference Include="..\Zylo.AutoG\Zylo.AutoG.csproj"
                    OutputItemType="Analyzer"
                    ReferenceOutputAssembly="false" />
  <ProjectReference Include="..\Zylo.YLog.Runtime\Zylo.YLog.Runtime.csproj" />
</ItemGroup>
```

#### 2. 程序启动配置

```csharp
using Zylo.YLog.Runtime;

static void Main()
{
    // 🔥 必须在程序启动时配置 YLogger
    YLogger.ConfigureForDevelopment();  // 开发环境
    // 或者
    // YLogger.ConfigureForProduction(); // 生产环境

    // 你的应用程序代码...
}
```

### 基础用法

#### 1. 🆕 灵活命名 - 无Core后缀约束

```csharp
using Zylo.AutoG.Attributes.Logging;
using Zylo.YLog.Runtime;

// 🔥 类必须是 partial - 必需
public partial class UserService
{
    // 🆕 可以使用任意方法名 - 只需添加Core后缀作为实际实现
    [YLog]
    public string GetUserCore(int userId)  // 实际实现方法
    {
        // 🔥 可以添加手动日志
        YLogger.Info("开始获取用户", "UserId", userId);

        if (userId <= 0)
        {
            YLogger.Warning("无效的用户ID", "UserId", userId);
            return "Invalid User";
        }

        // 业务逻辑
        return $"User {userId}";
    }
}

// 🔸 调用生成的方法
var service = new UserService();

// 🆕 调用时使用自然的方法名（自动去掉Core后缀）
var user = service.GetUser(123);  // 生成器自动创建此方法

// 输出日志：
// [2025-06-30 10:00:11.109] [INFORMATION] [T5] 开始获取用户 [UserId=123]
// [2025-06-30 10:00:11.170] [INFORMATION] [T5] UserService.GetUser(123) => User 123 [60ms]
```

#### 2. 🎨 属性语法

```csharp
public partial class ConfigService
{
    [YLog]                              // 默认 Information 级别
    public void LoadConfigCore() { }

    [YLog(YLogLevel.Debug)]            // 指定日志级别
    public void DebugMethodCore() { }

    [YLog(logParameters: false)]       // 不记录参数
    public void ProcessDataCore(string data) { }

    [YLog(logResult: false)]           // 不记录返回值
    public string GetSecretCore() { return "secret"; }

    [YLog(performanceThresholdMs: 100)] // 只记录超过100ms的调用
    public void SlowOperationCore() { }
}
```

#### 3. 便捷属性语法

```csharp
public partial class BusinessService
{
    [YLog.Debug]        // 等同于 [YLog(YLogLevel.Debug)]
    public void DebugMethodCore() { }

    [YLog.Info]         // 等同于 [YLog(YLogLevel.Information)]
    public void InfoMethodCore() { }

    [YLog.Warning]      // 等同于 [YLog(YLogLevel.Warning)]
    public void WarningMethodCore() { }

    [YLog.Error]        // 等同于 [YLog(YLogLevel.Error)]
    public void ErrorMethodCore() { }
}
```

### 手动日志API

YLog 提供了丰富的手动日志API，与自动日志完美结合：

#### 1. 基础日志方法

```csharp
public partial class OrderService
{
    [YLog.Information]
    public async Task ProcessOrderCore(int orderId)
    {
        // 手动记录业务流程日志
        YLogger.Info("开始处理订单", "OrderId", orderId);

        try
        {
            // 业务逻辑
            await Task.Delay(100);

            YLogger.Info("订单处理完成", "OrderId", orderId);
        }
        catch (Exception ex)
        {
            // 手动记录异常
            YLogger.Error("订单处理失败", ex);
            throw;
        }
    }

    public void SimpleLogging()
    {
        // 简单文本日志
        YLogger.Debug("这是调试信息");
        YLogger.Info("用户登录成功");
        YLogger.Warning("磁盘空间不足");
        YLogger.Error("数据库连接失败");
    }
}
```

#### 2. 带参数的日志

```csharp
public partial class UserService
{
    [YLog]
    public void UpdateUserCore(int userId, string name, string email)
    {
        // 结构化参数日志
        YLogger.Info("开始更新用户",
            "UserId", userId,
            "Name", name,
            "Email", email);

        // 条件日志
        if (string.IsNullOrEmpty(email))
        {
            YLogger.Warning("用户邮箱为空", "UserId", userId);
        }

        // 性能监控
        YLogger.Debug("用户更新耗时", "ElapsedMs", 45);
    }
}
```

### 🧬 泛型支持

YLog 完全支持各种泛型场景：

```csharp
public partial class GenericService<T> where T : class
{
    [YLog]
    public T ProcessCore<TInput, TOutput>(TInput input)
        where TInput : class
        where TOutput : class, new()
    {
        YLogger.Info("处理泛型数据",
            "InputType", typeof(TInput).Name,
            "OutputType", typeof(TOutput).Name);

        // 业务逻辑
        return default(T);
    }

    [YLog.Debug]
    public async Task<List<T>> LoadDataCore<TKey>(TKey key) where TKey : IComparable
    {
        YLogger.Debug("加载数据", "KeyType", typeof(TKey).Name, "Key", key);

        // 异步操作
        await Task.Delay(50);
        return new List<T>();
    }
}
```

### 📁 文件管理配置

#### 默认配置

- **日志目录**: `bin/Debug/net8.0/Logs` (运行目录)
- **文件命名**: `AppName_20250630.log` (Daily模式)
- **文件大小**: 最大 50MB，超过自动分割
- **文件数量**: 最多保留 10 个文件
- **文件轮转**: 自动启用

#### 自定义配置

```csharp
// 自定义日志配置
YLogger.SetLogDirectory("./CustomLogs");
YLogger.SetMaxFileSize(100); // 100MB
YLogger.SetMaxFileCount(20);
YLogger.SetFileNamingMode(LogFileNamingMode.Hourly);

// 或者使用配置对象
var config = new YLogConfig
{
    LogDirectory = "./Logs",
    MaxFileSizeMB = 50,
    MaxFileCount = 10,
    EnableFileRotation = true,
    FileNamingMode = LogFileNamingMode.Daily,
    OutputFormat = LogOutputFormat.JSON
};
YLogger.SetConfig(config);
```

### 🚀 快速配置模式

```csharp
// 开发模式 - 控制台 + 文件输出，Debug 级别
YLogger.ConfigureForDevelopment();

// 生产模式 - 只文件输出，Information 级别
YLogger.ConfigureForProduction();

// 静默模式 - 只文件输出，Warning 级别
YLogger.ConfigureForSilent();

// 调试模式 - 只控制台输出，Debug 级别
YLogger.ConfigureForDebug();

// 禁用所有日志
YLogger.Disable();
```

### ⚠️ 重要注意事项

#### 1. 必需配置

- ✅ 类必须声明为 `partial`
- 🆕 方法名可以任意命名，只需添加 `Core` 后缀作为实际实现
- ✅ 项目必须启用源代码生成器
- ✅ 程序启动时必须调用配置方法

#### 2. 🆕 灵活调用方式

- ✅ 调用生成的方法（自动去掉Core后缀）: `service.GetUser(123)`
- ✅ 用户可以使用任意喜欢的方法名，如：`ProcessData()`, `CalculateResult()`, `ValidateInput()`
- ❌ 不要直接调用Core方法: `service.GetUserCore(123)`

#### 3. 项目引用

- ✅ 必须引用 `Zylo.AutoG` (作为Analyzer)
- ✅ 必须引用 `Zylo.YLog.Runtime`

### 在 Zylo.YIO 中的实际应用

#### YConfigProcessor 日志增强

```csharp
[YService.Transient]
[YStatic]
public partial class YConfigProcessor
{
    [YLog.Info]
    public T LoadCore<T>(string configName) where T : class
    {
        YLogger.Info("开始加载配置", "ConfigName", configName, "Type", typeof(T).Name);

        try
        {
            // 配置加载逻辑
            var result = LoadConfigFromFile<T>(configName);

            YLogger.Info("配置加载成功", "ConfigName", configName);
            return result;
        }
        catch (Exception ex)
        {
            YLogger.Error("配置加载失败", ex);
            throw;
        }
    }

    [YLog.Warning]
    public bool SaveCore<T>(string configName, T config) where T : class
    {
        YLogger.Info("开始保存配置", "ConfigName", configName);

        if (config == null)
        {
            YLogger.Warning("配置对象为空", "ConfigName", configName);
            return false;
        }

        // 保存逻辑
        return true;
    }
}

// 使用方式：
var processor = new YConfigProcessor();
var config = processor.Load<AppConfig>("app");  // 自动记录日志
```

#### YFileCompressor 性能监控

```csharp
[YService.Singleton]
public partial class YFileCompressor
{
    [YLog.Info(performanceThresholdMs: 1000)]  // 只记录超过1秒的压缩操作
    public async Task<CompressionResult> CompressFileCore(string sourceFile, string targetFile)
    {
        YLogger.Info("开始文件压缩",
            "SourceFile", Path.GetFileName(sourceFile),
            "TargetFile", Path.GetFileName(targetFile));

        try
        {
            // 压缩逻辑
            var result = await PerformCompression(sourceFile, targetFile);

            YLogger.Info("压缩完成",
                "OriginalSize", result.OriginalSize,
                "CompressedSize", result.CompressedSize,
                "CompressionRatio", result.CompressionRatio);

            return result;
        }
        catch (Exception ex)
        {
            YLogger.Error("压缩失败", ex);
            throw;
        }
    }
}
```

## 🎯 组合使用示例

### 完整的配置管理服务

```csharp
/// <summary>
/// 高级配置管理服务 - 演示四个功能的组合使用
/// </summary>
[YDoc]                    // 生成 API 文档
[YService.Scoped]         // Scoped 生命周期服务
[YStatic]                 // 生成扩展方法（默认模式）
public partial class YAdvancedConfigManager
{
    private readonly ILogger<YAdvancedConfigManager> _logger;

    public YAdvancedConfigManager(ILogger<YAdvancedConfigManager> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 智能加载配置 - 支持多种格式自动检测
    /// </summary>
    /// <typeparam name="T">配置对象类型</typeparam>
    /// <param name="configName">配置名称</param>
    /// <returns>加载的配置对象</returns>
    /// <example>
    /// <code>
    /// // 作为服务使用（通过依赖注入）
    /// var config = await _configManager.SmartLoad&lt;AppConfig&gt;("app");
    ///
    /// // 作为扩展方法使用
    /// var config = "app".SmartLoad&lt;AppConfig&gt;();
    /// </code>
    /// </example>
    [YLog.Info(performanceThresholdMs: 500)]  // 记录超过500ms的配置加载
    public async Task<T> SmartLoadCore<T>(string configName) where T : class
    {
        // 🔥 YLog 会自动记录方法调用和性能
        // 手动日志记录详细业务流程
        YLogger.Info("开始智能加载配置",
            "ConfigName", configName,
            "ConfigType", typeof(T).Name);

        try
        {
            // 智能检测配置文件格式
            var configPath = configName.BuildConfigPath();
            var format = configPath.DetectConfigFormat();

            YLogger.Debug("检测到配置格式", "Format", format, "Path", configPath);

            // 根据格式加载
            var processor = new YConfigProcessor();
            var result = format switch
            {
                "json" => processor.ReadJson<T>(configPath),
                "xml" => throw new NotSupportedException("XML格式需要特殊处理"),
                "ini" => throw new NotSupportedException("INI格式需要特殊处理"),
                _ => processor.Load<T>(configName)
            };

            YLogger.Info("配置加载成功", "ConfigName", configName);
            return result;
        }
        catch (Exception ex)
        {
            YLogger.Error("配置加载失败", ex);
            throw;
        }
    }

    /// <summary>
    /// 批量加载配置
    /// </summary>
    /// <typeparam name="T">配置对象类型</typeparam>
    /// <param name="configNames">配置名称列表</param>
    /// <returns>配置字典</returns>
    [YLog.Info]  // 记录批量操作
    public async Task<Dictionary<string, T>> LoadMultipleCore<T>(params string[] configNames)
        where T : class
    {
        YLogger.Info("开始批量加载配置", "Count", configNames.Length);

        var results = new Dictionary<string, T>();
        var successCount = 0;
        var failureCount = 0;

        foreach (var configName in configNames)
        {
            try
            {
                var config = await SmartLoadCore<T>(configName);
                if (config != null)
                {
                    results[configName] = config;
                    successCount++;
                }
            }
            catch (Exception ex)
            {
                YLogger.Error("批量加载中单个配置失败", ex);
                failureCount++;
            }
        }

        YLogger.Info("批量加载完成",
            "Total", configNames.Length,
            "Success", successCount,
            "Failed", failureCount);

        return results;
    }

    /// <summary>
    /// 验证配置完整性 - 静态方法
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>验证结果</returns>
    [YStatic.MNoThis]
    public static ConfigValidationResult ValidateConfig(string configPath)
    {
        return new ConfigValidationResult
        {
            IsValid = configPath.IsValidConfigPath() && File.Exists(configPath),
            Path = configPath,
            LastModified = File.Exists(configPath) ? File.GetLastWriteTime(configPath) : null
        };
    }

    /// <summary>
    /// 内部缓存方法 - 不生成到接口
    /// </summary>
    [YService.MNoInterface]
    private void CacheConfig<T>(string configName, T config) where T : class
    {
        // 缓存逻辑
    }

    /// <summary>
    /// 调试方法 - 排除生成
    /// </summary>
    [YStatic.MExclude("仅用于调试")]
    private void DebugLogConfig(string configName)
    {
        _logger.LogDebug("调试配置: {ConfigName}", configName);
    }
}

// 自动生成：
// 1. IYAdvancedConfigManager 接口（YService）
// 2. YAdvancedConfigManagerExtensions 扩展方法类（YStatic）
// 3. YAdvancedConfigManager_API文档.md（YDoc）
// 4. SmartLoad、LoadMultiple 等日志包装方法（YLog）
// 5. 服务注册扩展方法
```

### 使用生成的代码

```csharp
// 1. 服务注册（Program.cs）
builder.Services.AddYAdvancedConfigManager();

// 2. 依赖注入使用
public class ConfigController : ControllerBase
{
    private readonly IYAdvancedConfigManager _configManager;

    public ConfigController(IYAdvancedConfigManager configManager)
    {
        _configManager = configManager;
    }

    [HttpGet("config/{name}")]
    public async Task<IActionResult> GetConfig(string name)
    {
        // 🔥 调用生成的日志包装方法（自动去掉Core后缀）
        var config = await _configManager.SmartLoad<AppConfig>(name);
        return Ok(config);
    }
}

// 3. 扩展方法使用
public class ConfigHelper
{
    public async Task<AppConfig> LoadAppConfig()
    {
        // 🔥 使用生成的扩展方法（带日志记录）
        return await "app".SmartLoad<AppConfig>();
    }

    public void ValidateConfigFile(string path)
    {
        // 使用生成的静态方法
        var result = YAdvancedConfigManagerStatics.ValidateConfig(path);
        Console.WriteLine($"配置有效: {result.IsValid}");
    }
}

// 4. 字符串扩展方法使用
public class PathHelper
{
    public void ProcessConfigPaths()
    {
        var configName = "App Settings".ToConfigName();           // YStringUtils 扩展
        var configPath = configName.BuildConfigPath();            // YPathUtils 扩展
        var isValid = configPath.IsValidConfigPath();             // YStringUtils 扩展
        var format = configPath.DetectConfigFormat();             // 自定义扩展
    }
}
```

## 🎯 最佳实践

### 1. 命名规范

```csharp
// ✅ 推荐的命名方式
[YService.Scoped]
public partial class YConfigService { }        // Y前缀 + 功能 + Service

[YStatic]
public static partial class YStringUtils { }   // Y前缀 + 类型 + Utils

[YDoc]
public partial class YDataProcessor { }        // Y前缀 + 功能 + Processor
```

### 2. 属性组合使用

```csharp
// ✅ 推荐的组合方式
[YDoc]                    // 文档生成
[YService.Scoped]         // 依赖注入
[YStatic]                 // 扩展方法
public partial class YIOHelper
{
    // 服务方法 - 带日志记录
    [YLog.Info(performanceThresholdMs: 200)]
    public async Task<string> ReadFileCore(string path) { }

    // 静态工具方法
    [YStatic.MNoThis]
    [YLog.Debug]
    public static bool IsValidPathCore(string path) { }

    // 内部方法
    [YService.MNoInterface]
    [YStatic.MExclude("内部方法")]
    private void InternalMethod() { }
}
```

### 3. 文档编写规范

```csharp
/// <summary>
/// 类的简要描述 - 一句话说明功能
/// 详细描述可以分多行，解释设计理念和使用场景
/// </summary>
/// <remarks>
/// 重要说明和注意事项
/// </remarks>
[YDoc]
public partial class YExampleService
{
    /// <summary>
    /// 方法的简要描述
    /// </summary>
    /// <param name="parameter">参数描述</param>
    /// <returns>返回值描述</returns>
    /// <exception cref="ArgumentException">异常情况描述</exception>
    /// <example>
    /// <code>
    /// // 使用示例
    /// var result = service.ExampleMethod("test");
    /// </code>
    /// </example>
    public string ExampleMethod(string parameter) { }
}
```

### 4. YLog 使用建议

```csharp
public partial class YDataService
{
    // ✅ 推荐：为重要业务方法添加日志
    [YLog.Info(performanceThresholdMs: 500)]
    public async Task<DataResult> ProcessDataCore(string data)
    {
        // 手动日志记录关键业务节点
        YLogger.Info("开始数据处理", "DataSize", data.Length);

        try
        {
            // 业务逻辑
            var result = await ProcessBusinessLogic(data);

            YLogger.Info("数据处理成功", "ResultCount", result.Count);
            return result;
        }
        catch (Exception ex)
        {
            YLogger.Error("数据处理失败", ex);
            throw;
        }
    }

    // ✅ 推荐：调试方法使用 Debug 级别
    [YLog.Debug]
    public void ValidateDataCore(string data)
    {
        YLogger.Debug("数据验证", "IsValid", !string.IsNullOrEmpty(data));
    }

    // ✅ 推荐：性能敏感方法设置阈值
    [YLog.Warning(performanceThresholdMs: 100)]
    public string QuickProcessCore(string input)
    {
        // 只有超过100ms才记录警告日志
        return input.ToUpper();
    }

    // ❌ 避免：不要在高频调用方法上使用无阈值日志
    // [YLog] // 这会产生大量日志
    // public string HighFrequencyMethod() { }
}
```

### 5. 错误处理模式

```csharp
[YService.Scoped]
public partial class YConfigService
{
    /// <summary>
    /// 安全加载配置 - 失败时返回默认值
    /// </summary>
    public T SafeLoad<T>(string configName, T defaultValue = default) where T : class
    {
        try
        {
            return Load<T>(configName) ?? defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载配置失败: {ConfigName}", configName);
            return defaultValue;
        }
    }

    /// <summary>
    /// 尝试加载配置 - 返回成功状态
    /// </summary>
    public bool TryLoad<T>(string configName, out T config) where T : class
    {
        try
        {
            config = Load<T>(configName);
            return config != null;
        }
        catch
        {
            config = default;
            return false;
        }
    }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 生成的代码找不到

**问题**：使用属性后没有生成对应的代码

**解决方案**：

```csharp
// 检查项目文件配置
<PropertyGroup>
  <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
  <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
</PropertyGroup>

// 检查类是否为 partial
[YService.Scoped]
public partial class MyService { }  // 必须是 partial

// 重新构建项目
dotnet clean
dotnet build
```

#### 2. 依赖注入服务未注册

**问题**：运行时找不到服务

**解决方案**：

```csharp
// 确保在 Program.cs 中注册服务
builder.Services.AddMyService();  // 使用生成的扩展方法

// 或者手动注册
builder.Services.AddScoped<IMyService, MyService>();
```

#### 3. 扩展方法不可用

**问题**：生成的扩展方法无法使用

**解决方案**：

```csharp
// 确保引用了正确的命名空间
using YourProject.Extensions;

// 检查扩展方法的第一个参数类型
[YStatic]
public static class StringUtils
{
    public static string Process(string input) => input;  // ✅ 正确
    public static string Process(int input) => input.ToString();  // ❌ 类型不匹配
}
```

#### 4. 文档生成失败

**问题**：YDoc 没有生成文档

**解决方案**：

```csharp
// 确保 XML 注释格式正确
/// <summary>
/// 正确的注释格式
/// </summary>
public void Method() { }

// 检查项目文件中的文档生成设置
<PropertyGroup>
  <GenerateDocumentationFile>true</GenerateDocumentationFile>
</PropertyGroup>
```

#### 5. YLog 日志不输出

**问题**：添加了 YLog 属性但没有日志输出

**解决方案**：

```csharp
// 1. 确保在程序启动时配置了 YLogger
static void Main()
{
    YLogger.ConfigureForDevelopment();  // 必需！
    // 你的程序代码...
}

// 2. 检查方法命名是否正确
[YLog]
public void ProcessDataCore() { }  // ✅ 正确：Core后缀

// 3. 调用生成的方法而不是Core方法
service.ProcessData();     // ✅ 正确：调用生成的方法
// service.ProcessDataCore(); // ❌ 错误：直接调用Core方法

// 4. 检查日志级别配置
YLogger.Config.MinimumLevel = LogLevel.Debug;  // 确保级别正确
```

#### 6. YLog 性能问题

**问题**：日志记录影响性能

**解决方案**：

```csharp
// 1. 使用性能阈值，只记录慢操作
[YLog.Warning(performanceThresholdMs: 1000)]
public void SlowOperationCore() { }

// 2. 对高频方法使用条件日志
[YLog.Debug(condition: "DEBUG")]
public void HighFrequencyMethodCore() { }

// 3. 调整批处理配置
YLogger.Config.BatchSize = 200;        // 增加批处理大小
YLogger.Config.FlushIntervalMs = 2000; // 增加刷新间隔
```

### 调试技巧

1. **查看生成的代码**：检查 `obj/Generated` 目录
2. **启用详细日志**：在项目文件中添加 `<MSBuildVerbosity>detailed</MSBuildVerbosity>`
3. **清理重建**：`dotnet clean && dotnet build`
4. **检查编译错误**：注意生成器相关的警告和错误

### 在 Zylo.YIO 中的实际应用

#### YConfigProcessor 的实际使用

```csharp
// 当前 YConfigProcessor 的使用方式
[YService.Transient]
[YStatic]
public partial class YConfigProcessor
{
    // 服务方法 - 通过依赖注入使用
    public T Load<T>(string configName) where T : class { }

    // 静态方法 - 通过扩展方法使用
    [YStatic.MNoThis]
    public static T QuickLoad<T>(string configName) where T : class { }
}

// 使用方式：
// 1. 依赖注入
services.AddYConfigProcessor();
var config = _processor.Load<AppConfig>("app");

// 2. 扩展方法
var config = "app".QuickLoad<AppConfig>();
```

#### YPathUtilities 的扩展

```csharp
// 为 YPathUtilities 添加 YStatic 支持
[YStatic]
public static partial class YPathUtilities
{
    // 现有方法自动变成扩展方法
    public static string GetConfigPath(string configName) { }
    public static bool IsValidPath(string path) { }

    // 使用方式：
    var path = "app".GetConfigPath();
    var isValid = path.IsValidPath();
}
```

---

**🚀 通过 Zylo.AutoG，让 Zylo.YIO 的开发更加高效和规范！**

**核心优势：减少样板代码、提升开发效率、保证代码质量、自动化文档生成、零开销日志记录**

### 🎯 四大功能协同优势

- **YService** + **YLog** = 带日志记录的依赖注入服务
- **YStatic** + **YLog** = 带性能监控的扩展方法
- **YDoc** + **YLog** = 自动生成包含日志说明的API文档
- **四功能组合** = 完整的企业级代码生成解决方案
