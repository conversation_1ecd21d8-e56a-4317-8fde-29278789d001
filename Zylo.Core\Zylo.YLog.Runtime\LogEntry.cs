using System;

namespace Zylo.YLog.Runtime
{
    /// <summary>
    /// 日志条目
    /// 🔥 表示单个日志记录的所有信息
    ///
    /// 这是日志系统的核心数据结构，包含了一条日志记录的完整信息。
    /// 支持两种类型的日志：
    /// 1. 方法执行日志：记录方法调用的完整信息（类名、方法名、参数、返回值、执行时间）
    /// 2. 手动日志：记录开发者主动写入的日志消息
    ///
    /// 设计特点：
    /// • 结构化存储：所有信息都有明确的字段定义
    /// • 类型安全：使用强类型属性，避免字符串解析错误
    /// • 扩展性好：可以轻松添加新的字段而不影响现有功能
    /// • 序列化友好：支持 JSON、XML 等多种序列化格式
    ///
    /// 使用场景：
    /// • AOP 拦截器记录方法执行信息
    /// • 手动日志记录业务事件
    /// • 异常处理和错误跟踪
    /// • 性能监控和分析
    /// </summary>
    public class LogEntry
    {
        #region 基本信息

        /// <summary>
        /// 时间戳 - 日志记录的精确时间
        ///
        /// 使用 UTC 时间确保时区一致性，精确到毫秒级别。
        /// 用于日志排序、时间范围查询、性能分析等。
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 日志级别 - 表示日志的重要程度
        ///
        /// 支持的级别：
        /// • Debug：调试信息，开发时使用
        /// • InformationDetailed：详细信息，包含所有执行细节
        /// • Information：一般信息，重要的业务事件
        /// • InformationSimple：简化信息，关键业务节点
        /// • Warning：警告，潜在问题或异常情况
        /// • Error：错误，程序运行中的错误和异常
        /// </summary>
        public string Level { get; set; } = "";

        /// <summary>
        /// 线程ID - 记录日志的线程标识
        ///
        /// 用于：
        /// • 多线程环境下的日志关联
        /// • 并发问题的诊断和分析
        /// • 性能瓶颈的识别
        /// • 线程安全问题的排查
        /// </summary>
        public int ThreadId { get; set; }

        #endregion

        #region 方法信息

        /// <summary>
        /// 类名 - 执行方法所属的类
        ///
        /// 用于：
        /// • 快速定位代码位置
        /// • 按类过滤和分析日志
        /// • 代码结构的理解
        /// • 重构时的影响分析
        /// </summary>
        public string? ClassName { get; set; }

        /// <summary>
        /// 方法名 - 被执行的方法名称
        ///
        /// 结合类名可以精确定位到具体的方法。
        /// 用于代码跟踪、性能分析、调用链分析等。
        /// </summary>
        public string? MethodName { get; set; }

        /// <summary>
        /// 方法参数 - 方法调用时的参数值
        ///
        /// 记录方法调用的输入参数，便于：
        /// • 重现问题场景
        /// • 理解方法调用上下文
        /// • 调试参数相关的问题
        /// • 数据流分析
        ///
        /// 注意：大对象参数会影响性能，建议适当控制记录的详细程度
        /// </summary>
        public object?[]? Parameters { get; set; }

        /// <summary>
        /// 返回值 - 方法执行的返回结果
        ///
        /// 记录方法的输出结果，便于：
        /// • 验证方法执行结果
        /// • 分析数据转换过程
        /// • 调试返回值相关问题
        /// • 业务逻辑验证
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 执行时间（毫秒） - 方法执行耗时
        ///
        /// 精确记录方法从开始到结束的执行时间。
        /// 用于：
        /// • 性能监控和优化
        /// • 识别性能瓶颈
        /// • SLA 监控
        /// • 系统容量规划
        /// </summary>
        public long ElapsedMs { get; set; }

        #endregion

        #region 手动日志信息

        /// <summary>
        /// 手动日志消息 - 开发者主动记录的信息
        ///
        /// 用于记录：
        /// • 业务事件和状态变化
        /// • 调试信息和提示
        /// • 系统运行状态
        /// • 用户操作记录
        ///
        /// 支持格式化字符串和参数替换。
        /// </summary>
        public string? Message { get; set; }

        #endregion

        #region 异常信息

        /// <summary>
        /// 异常信息 - 记录程序执行中的异常
        ///
        /// 包含完整的异常对象，提供：
        /// • 异常类型和消息
        /// • 完整的堆栈跟踪
        /// • 内部异常信息
        /// • 异常发生的上下文
        ///
        /// 这是错误诊断和问题排查的关键信息。
        /// </summary>
        public Exception? Exception { get; set; }

        #endregion
    }
}
