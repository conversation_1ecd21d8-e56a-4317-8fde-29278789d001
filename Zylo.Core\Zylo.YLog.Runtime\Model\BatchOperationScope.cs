namespace Zylo.YLog.Runtime;


/// <summary>
/// 批量操作范围 - 临时降低日志噪音
/// </summary>
public class BatchOperationScope : IDisposable
{
    private readonly string _batchName;
    private readonly int _itemCount;
    private readonly DateTime _startTime;
    private readonly LogLevel? _previousLevel;

    public BatchOperationScope(string batchName, int itemCount)
    {
        _batchName = batchName;
        _itemCount = itemCount;
        _startTime = DateTime.Now;
        _previousLevel = YLogger._globalForceLevel;

        // 批量操作时临时提高日志级别，减少噪音
        YLogger.ForceProductionMode();
    }

    public void Dispose()
    {
        var elapsed = DateTime.Now - _startTime;

        // 恢复原来的级别
        if (_previousLevel.HasValue)
        {
            switch (_previousLevel.Value)
            {
                case LogLevel.Debug: YLogger.ForceDebugMode(); break;
                case LogLevel.Warning: YLogger.ForceProductionMode(); break;
                case LogLevel.Error: YLogger.ForceSilentMode(); break;
                default: YLogger.RestoreIndependentMode(); break;
            }
        }
        else
        {
            YLogger.RestoreIndependentMode();
        }

        YLogger.Info($"✅ 批量操作完成: {_batchName} ({_itemCount} 项, {elapsed.TotalSeconds:F1}s)");
    }
}