namespace Zylo.YLog.Runtime;

    /// <summary>
    /// 实例性能监控器 - 使用特定logger实例记录性能数据
    ///
    /// 🎯 功能说明：
    /// 这是一个实现了IDisposable接口的性能监控器，专门用于监控代码块的执行时间。
    /// 与全局性能监控器不同，它使用特定的日志实例进行记录，可以享受该实例的级别控制。
    ///
    /// 🔧 工作原理：
    /// 1. 构造时记录开始时间并输出开始日志
    /// 2. Dispose时计算执行时间并输出完成日志
    /// 3. 根据执行时间自动选择合适的图标
    ///
    /// 📋 图标含义：
    /// • ⚡ 小于100ms：执行很快
    /// • 🏃 小于1000ms：执行正常
    /// • 🚶 小于5000ms：执行较慢
    /// • 🐌 大于等于5000ms：执行很慢
    ///
    /// 💡 使用示例：
    /// using (logger.Monitor("数据库查询"))
    /// {
    ///     // 执行数据库操作
    /// } // 自动记录: ⚡ 完成: 数据库查询 (85.3ms)
    /// </summary>
    public class InstancePerformanceMonitor : IDisposable
    {
        /// <summary>关联的日志实例</summary>
        private readonly YLoggerInstance _logger;

        /// <summary>操作名称</summary>
        private readonly string _operationName;

        /// <summary>开始时间</summary>
        private readonly DateTime _startTime;

        /// <summary>
        /// 构造函数 - 开始性能监控
        ///
        /// 🎯 功能说明：
        /// 初始化性能监控器，记录开始时间并输出开始日志。
        /// 使用InfoDetailed级别记录，确保在详细模式下可以看到完整的执行流程。
        /// </summary>
        /// <param name="logger">要使用的日志实例</param>
        /// <param name="operationName">操作名称</param>
        public InstancePerformanceMonitor(YLoggerInstance logger, string operationName)
        {
            _logger = logger;
            _operationName = operationName;
            _startTime = DateTime.Now; // 记录开始时间

            // 记录操作开始，使用详细信息级别
            _logger.InfoDetailed($"⏱️ 开始: {_operationName}");
        }

        /// <summary>
        /// 完成时记录执行时间 - 实现IDisposable接口
        ///
        /// 🎯 功能说明：
        /// 计算总执行时间，根据耗时选择合适的图标，并记录完成日志。
        /// 这个方法会在using语句结束时自动调用。
        ///
        /// 📊 性能分级：
        /// • 小于100ms：⚡ 很快 - 通常是内存操作或简单计算
        /// • 小于1000ms：🏃 正常 - 一般的业务操作
        /// • 小于5000ms：🚶 较慢 - 复杂操作或网络请求
        /// • 大于等于5000ms：🐌 很慢 - 可能需要优化的操作
        /// </summary>
        public void Dispose()
        {
            var elapsed = DateTime.Now - _startTime; // 计算执行时间

            // 根据执行时间选择图标，提供直观的性能反馈
            var icon = elapsed.TotalMilliseconds switch
            {
                < 100 => "⚡",   // 很快：小于100毫秒
                < 1000 => "🏃", // 正常：100毫秒到1秒
                < 5000 => "🚶", // 较慢：1秒到5秒
                _ => "🐌"       // 很慢：超过5秒
            };

            // 记录操作完成，包含图标、操作名和精确的执行时间
            _logger.InfoDetailed($"{icon} 完成: {_operationName} ({elapsed.TotalMilliseconds:F1}ms)");

            // 调用GC.SuppressFinalize以优化垃圾回收性能
            GC.SuppressFinalize(this);
        }
    }