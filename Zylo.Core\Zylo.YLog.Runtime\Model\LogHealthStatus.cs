namespace Zylo.YLog.Runtime;

/// <summary>
/// 日志系统健康状态
/// </summary>
public class LogHealthStatus
{
    /// <summary>
    /// 日志系统是否健康
    /// </summary>
    /// <value></value>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 错误率
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public double ErrorRate { get; set; }

    /// <summary>
    /// 总日志数
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int TotalLogs { get; set; }

    /// <summary>
    /// 最后一条日志记录时间
    /// </summary>
    /// <value></value>
    public DateTime LastLogTime { get; set; }

    /// <summary>
    /// 运行时间
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public double UptimeMinutes { get; set; }

    /// <summary>
    /// 获取健康报告
    /// </summary>
    /// <returns></returns>
    public string GetHealthReport()
    {
        var status = IsHealthy ? "🟢 健康" : "🔴 异常";
        return $"{status} | 错误率: {ErrorRate:P1} | 总日志: {TotalLogs} | 运行: {UptimeMinutes:F1}分钟";
    }
}