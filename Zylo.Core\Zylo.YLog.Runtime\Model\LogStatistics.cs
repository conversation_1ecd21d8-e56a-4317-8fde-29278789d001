namespace Zylo.YLog.Runtime;

/// <summary>
/// 日志统计信息
/// </summary>
public class LogStatistics
{
    /// <summary>
    /// 日志系统启动时间
    /// </summary>
    /// <value></value>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 最后一条日志记录时间
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public DateTime LastLogTime { get; set; }


    /// <summary>
    /// 总日志记录数
    /// </summary>
    /// <value></value>
    public int TotalLogs { get; set; }


    /// <summary>
    /// 调试级别日志数
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int DebugCount { get; set; }

    /// <summary>
    /// 详细信息级别日志数
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int InfoCount { get; set; }

    /// <summary>
    /// 简化信息级别日志数
    /// </summary> <summary>
    /// 
    /// </summary>
    /// <value></value>
    public int WarningCount { get; set; }

    /// <summary>
    /// 错误级别日志数
    /// </summary>
    /// <value></value>
    public int ErrorCount { get; set; }

    /// <summary>
    /// 每个类的日志数
    /// </summary>
    /// <returns></returns>
    public Dictionary<string, int> ClassCounts { get; set; } = new();
}
