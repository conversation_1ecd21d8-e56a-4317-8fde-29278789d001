namespace Zylo.YLog.Runtime;

/// <summary>
/// 性能监控 - 自动记录执行时间
/// </summary>
public class PerformanceMonitor : IDisposable
{
    private readonly string _operationName;
    private readonly LogLevel _level;
    private readonly DateTime _startTime;

    public PerformanceMonitor(string operationName, LogLevel level)
    {
        _operationName = operationName;
        _level = level;
        _startTime = DateTime.Now;

        switch (_level)
        {
            case LogLevel.Debug: YLogger.Debug($"⏱️ 开始: {_operationName}"); break;
            case LogLevel.Information: YLogger.Info($"⏱️ 开始: {_operationName}"); break;
            case LogLevel.Warning: YLogger.Warning($"⏱️ 开始: {_operationName}"); break;
            case LogLevel.Error: YLogger.Error($"⏱️ 开始: {_operationName}"); break;
            default: YLogger.Info($"⏱️ 开始: {_operationName}"); break;
        }
    }

    public void Dispose()
    {
        var elapsed = DateTime.Now - _startTime;
        var icon = elapsed.TotalMilliseconds switch
        {
            < 100 => "⚡", // 很快
            < 1000 => "🏃", // 正常
            < 5000 => "🚶", // 较慢
            _ => "🐌" // 很慢
        };

        var message = $"{icon} 完成: {_operationName} ({elapsed.TotalMilliseconds:F1}ms)";
        switch (_level)
        {
            case LogLevel.Debug: YLogger.Debug(message); break;
            case LogLevel.Information: YLogger.Info(message); break;
            case LogLevel.Warning: YLogger.Warning(message); break;
            case LogLevel.Error: YLogger.Error(message); break;
            default: YLogger.Info(message); break;
        }
    }
}