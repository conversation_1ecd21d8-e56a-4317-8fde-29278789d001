namespace Zylo.YLog.Runtime;

/// <summary>
/// 临时日志级别控制 - 自动恢复
/// </summary>
public class TemporaryLogLevel : IDisposable
{
    private readonly LogLevel? _previousLevel;

    public TemporaryLogLevel(LogLevel temporaryLevel)
    {
        _previousLevel = YLogger._globalForceLevel;
        switch (temporaryLevel)
        {
            case LogLevel.Debug:
                YLogger.ForceDebugMode();
                break;
            case LogLevel.Warning:
                YLogger.ForceProductionMode();
                break;
            case LogLevel.Error:
                YLogger.ForceSilentMode();
                break;
            default:
                YLogger.ForceDebugMode(); // 默认详细模式
                break;
        }
    }

    public void Dispose()
    {
        if (_previousLevel.HasValue)
        {
            switch (_previousLevel.Value)
            {
                case LogLevel.Debug:
                    YLogger.ForceDebugMode();
                    break;
                case LogLevel.Warning:
                    YLogger.ForceProductionMode();
                    break;
                case LogLevel.Error:
                    YLogger.ForceSilentMode();
                    break;
                default:
                    YLogger.RestoreIndependentMode();
                    break;
            }
        }
        else
        {
            YLogger.RestoreIndependentMode();
        }
    }
}