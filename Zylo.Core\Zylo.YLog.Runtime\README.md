# 🎯 Zylo.YLog.Runtime - 现代化多级别日志系统

## ✨ 特性概览

🎨 **图标化显示** - 直观的emoji图标 + 简洁缩写，快速识别
📐 **完美对齐** - 固定宽度格式，专业美观
🎛️ **多级别INFO** - 细粒度控制，调试时专注重点，稳定时简化输出
🏷️ **按类管理** - 每个类独立级别，灵活控制详细程度
🔍 **全局控制** - 运行时动态调整所有类的日志级别
🧵 **线程标识** - 清晰的线程图标标识
⚡ **高性能** - 异步处理，不阻塞主线程

## 📋 日志级别体系

| 级别 | 显示 | 使用场景 | 全局方法调用 | 实例方法调用 |
|------|------|----------|-------------|-------------|
| 🐛 DBG | 调试信息 | 开发调试，详细跟踪 | `YLogger.Debug("调试信息")` | `logger.Debug("调试信息")` |
| 📋 IN+ | 详细信息 | 重点关注的类/方法 | `YLogger.InfoDetailed("详细信息")` | `logger.InfoDetailed("详细信息")` |
| ℹ️ INF | 一般信息 | 常规业务记录 | `YLogger.Info("一般信息")` | `logger.Info("一般信息")` |
| 💡 IN- | 简化信息 | 已测试稳定的功能 | `YLogger.InfoSimple("简化信息")` | `logger.InfoSimple("简化信息")` |
| ⚠️ WRN | 警告信息 | 需要注意的问题 | `YLogger.Warning("警告信息")` | `logger.Warning("警告信息")` |
| ❌ ERR | 错误信息 | 错误和异常 | `YLogger.Error("错误信息")` | `logger.Error("错误信息")` |

## 🚀 两种使用方式

### 1. 全局日志方法 - 简单直接

```csharp
// 直接使用全局方法，无需创建实例
YLogger.Debug("调试信息：用户ID验证通过");
YLogger.InfoDetailed("详细信息：开始处理订单，包含商品验证、库存检查");
YLogger.Info("一般信息：用户登录成功");
YLogger.InfoSimple("简化信息：订单处理完成");
YLogger.Warning("警告信息：配置文件缺失，使用默认配置");
YLogger.Error("错误信息：数据库连接失败");

// 支持格式化参数
YLogger.Info("用户 {0} 登录成功，来源IP: {1}", username, clientIP);
YLogger.Error("用户 {0} 的数据保存失败，错误代码: {1}", userId, errorCode);

// 支持异常记录
try {
    // 业务逻辑
} catch (Exception ex) {
    YLogger.Error("处理用户请求时发生错误", ex);
}
```

### 2. 按类日志实例 - 精细控制

```csharp
// 🔥 正在重点调试的新功能
private static readonly YLoggerInstance _logger = YLogger.ForDebug<PaymentService>();
// 输出：DBG + IN+ + INF + IN- + WRN + ERR

// 🔥 重点关注的业务类
private static readonly YLoggerInstance _logger = YLogger.ForDetailed<UserService>();
// 输出：IN+ + INF + IN- + WRN + ERR

// 🔥 一般业务类
private static readonly YLoggerInstance _logger = YLogger.ForInfo<EmailService>();
// 输出：INF + IN- + WRN + ERR

// 🔥 已测试稳定的类
private static readonly YLoggerInstance _logger = YLogger.ForSimple<CacheService>();
// 输出：IN- + WRN + ERR

// 🔥 默认推荐（重要信息）
private static readonly YLoggerInstance _logger = YLogger.ForWarning<OrderService>();
// 输出：WRN + ERR

// 🔥 生产环境稳定类
private static readonly YLoggerInstance _logger = YLogger.ForSilent<DatabaseService>();
// 输出：ERR
```

### 3. 环境特定配置

```csharp
// 开发环境 - 输出详细信息
private static readonly YLoggerInstance _logger = YLogger.ForDevelopment<UserService>();

// 生产环境 - 只输出错误
private static readonly YLoggerInstance _logger = YLogger.ForProduction<UserService>();

// 自定义级别
private static readonly YLoggerInstance _logger = YLogger.For<UserService>(LogLevel.Information);
```

## 🎛️ 全局控制

```csharp
// 强制所有类输出Debug级别及以上（开发调试）
YLogger.ForceDebugMode();

// 强制所有类只输出Warning级别及以上（生产模式）
YLogger.ForceProductionMode();

// 强制所有类只输出Error级别（静默模式）
YLogger.ForceSilentMode();

// 恢复各类独立控制
YLogger.RestoreIndependentMode();

// 直接设置全局强制级别
YLogger.SetGlobalForceLevel(LogLevel.Information);
YLogger.SetGlobalForceLevel(null); // 恢复独立控制

// 查看当前全局设置
var currentLevel = YLogger.GetGlobalForceLevel();
Console.WriteLine($"当前全局强制级别: {currentLevel?.ToString() ?? "无"}");
```

## ⏰ 临时控制（自动恢复）

```csharp
// 临时启用详细模式
using (YLogger.TemporaryVerbose())
{
    // 这里所有日志都会输出详细信息
    userService.ProcessUser(123);
} // 自动恢复到原来的级别

// 临时启用静默模式
using (YLogger.TemporarySilent())
{
    // 这里只输出错误信息
    batchService.ProcessLargeData();
} // 自动恢复

// 临时启用指定级别
using (YLogger.TemporaryLevel(LogLevel.Warning))
{
    // 这里只输出警告和错误
    complexOperation.Execute();
} // 自动恢复
```

## 📊 输出效果

```text
════════════════════════════════════════════════════════════════════════════════
🎯 新的运行会话开始 - 2025-07-13 10:47:19.767 🎯
════════════════════════════════════════════════════════════════════════════════
[2025-07-13 02:47:19.944] ℹ️ INF         🧵 全局日志: 应用程序启动完成
[2025-07-13 02:47:19.944] 💡 IN-         🧵 全局日志: 配置加载完成
[2025-07-13 02:47:19.944] ⚠️ WRN         🧵 全局日志: 配置文件缺失，使用默认配置
[2025-07-13 02:47:19.948] 🐛 DBG         🧵 全局日志: 调试信息输出
[2025-07-13 02:47:19.949] 📋 IN+         🧵 全局日志: 详细信息记录
[2025-07-13 02:47:20.102] ℹ️ INF         🧵 🎛️ 全局强制级别已设置: Warning
[2025-07-13 02:47:20.103] ❌ ERR         🧵 全局日志: 数据库连接失败
```

### 🎯 完美对齐的关键技术

- **制表符对齐** - 使用`\t`解决emoji宽度不一致问题
- **统一格式** - 所有级别都是`emoji + 空格 + 3字符缩写 + 制表符`
- **视觉一致** - 完美的列对齐，专业美观

## 🎯 核心价值

### 多级别INFO的实际应用场景

**开发阶段**：

- 新功能开发：使用 `ForDebug<T>()` 看到所有细节
- 重点调试：使用 `ForDetailed<T>()` 专注关键信息
- 一般开发：使用 `ForInfo<T>()` 看到主要流程

**测试阶段**：

- 稳定功能：使用 `ForSimple<T>()` 减少干扰
- 问题排查：临时使用 `TemporaryVerbose()` 查看详情

**生产环境**：

- 核心业务：使用 `ForWarning<T>()` 只看重要信息
- 稳定组件：使用 `ForSilent<T>()` 只记录错误

## 🔧 快速配置

```csharp
// 开发模式配置 - 控制台+文件，Debug级别，自动显示配置
YLogger.ConfigureForDevelopment();

// 生产模式配置 - 只文件，Warning级别
YLogger.ConfigureForProduction();

// 测试模式配置 - 只控制台，Info级别
YLogger.ConfigureForTesting();

// 静默模式配置 - 只输出Error
YLogger.ConfigureForSilent();

// 详细模式配置 - 输出所有级别
YLogger.ConfigureForVerbose();
```

## 📈 最佳实践

### 🎯 选择合适的日志方式

1. **简单场景** - 直接使用全局方法 `YLogger.Info("消息")`
2. **复杂项目** - 为每个类创建实例 `YLogger.For<T>()`
3. **新功能开发** - 使用 `ForDebug<T>()` 或 `ForDetailed<T>()`
4. **稳定功能** - 使用 `ForSimple<T>()` 或 `ForWarning<T>()`
5. **生产环境** - 使用 `ForWarning<T>()` 或 `ForSilent<T>()`

### 🔧 运行时控制

1. **临时调试** - 使用 `TemporaryVerbose()` 包装代码块
2. **批量处理** - 使用 `TemporarySilent()` 减少日志噪音
3. **全局控制** - 使用 `ForceDebugMode()` 等快速切换模式
4. **性能监控** - 使用 `MonitorPerformance()` 自动记录执行时间

### 📋 环境配置

1. **开发环境** - `ConfigureForDevelopment()` 输出详细信息
2. **测试环境** - `ConfigureForTesting()` 只输出到控制台
3. **生产环境** - `ConfigureForProduction()` 只输出警告和错误到文件

## 🚀 高级功能

### 性能监控

```csharp
// 全局性能监控
using (YLogger.MonitorPerformance("整体业务流程"))
{
    // 自动记录执行时间，智能图标显示
    ProcessComplexOperation();
} // 输出: 🏃 完成: 整体业务流程 (152.8ms)

// 指定日志级别的性能监控
using (YLogger.MonitorPerformance("数据库查询", LogLevel.Debug))
{
    // 只在Debug级别时记录性能信息
    ExecuteDatabaseQuery();
}
```

### 批量操作优化

```csharp
// 批量操作时自动优化日志输出
using (YLogger.BatchOperation("批量处理用户数据", 1000))
{
    // 自动降低日志级别，减少噪音
    // 自动记录批量操作总体性能
    for (int i = 0; i < 1000; i++)
    {
        ProcessUserData(users[i]);
    }
} // 输出批量操作完成信息和总耗时
```

### 统计和健康监控

```csharp
// 获取日志统计
var stats = YLogger.GetStatistics();
Console.WriteLine($"总日志: {stats.TotalLogs}, 错误: {stats.ErrorCount}");

// 健康状态检查
var health = YLogger.CheckHealth();
Console.WriteLine(health.GetHealthReport());
// 输出: 🟢 健康 | 错误率: 9.6% | 总日志: 104 | 运行: 0.0分钟

// 重置统计
YLogger.ResetStatistics();
```

### 实用工具

```csharp
// 显示当前配置
YLogger.ShowCurrentConfig();

// 获取当前日志文件路径
string logPath = YLogger.GetCurrentLogFilePath();
Console.WriteLine($"日志文件: {logPath}");

// 清空所有日志文件
YLogger.ClearAllLogs();
```

## 🔧 技术特点

- ✅ **高性能** - 异步处理，ConcurrentQueue，批量刷新
- ✅ **线程安全** - 支持高并发场景
- ✅ **多格式输出** - TXT、JSON、XML、CSV
- ✅ **文件持久化** - 自动文件管理，按日期分割
- ✅ **内存优化** - 队列管理，防止内存泄漏
- ✅ **异常安全** - 完善的错误处理机制
- ✅ **emoji兼容** - 制表符对齐，解决宽度不一致问题

---

*多级别INFO让你在开发时专注重点，在生产时保持简洁！* 🎨
