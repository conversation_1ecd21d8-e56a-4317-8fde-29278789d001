# 🎯 Zylo.YLog.Runtime - 现代化多级别日志系统

## ✨ 特性概览

🎨 **图标化显示** - 直观的emoji图标 + 简洁缩写，快速识别  
📐 **完美对齐** - 固定宽度格式，专业美观  
🎛️ **多级别INFO** - 细粒度控制，调试时专注重点，稳定时简化输出  
🏷️ **按类管理** - 每个类独立级别，灵活控制详细程度  
🔍 **自动方法名** - 自动获取调用方法名，无需手动传入  
🧵 **线程标识** - 清晰的线程图标标识  
⚡ **高性能** - 异步处理，不阻塞主线程

## 📋 日志级别体系

| 级别 | 显示 | 使用场景 | 方法调用 |
|------|------|----------|----------|
| 🐛 DBG | 调试信息 | 开发调试，详细跟踪 | `_logger.Debug("调试信息")` |
| 📋 IN+ | 详细信息 | 重点关注的类/方法 | `_logger.InfoDetailed("详细信息")` |
| ℹ️ INF | 一般信息 | 常规业务记录 | `_logger.Info("一般信息")` |
| 💡 IN- | 简化信息 | 已测试稳定的功能 | `_logger.InfoSimple("简化信息")` |
| ⚠️ WRN | 警告信息 | 需要注意的问题 | `_logger.Warning("警告信息")` |
| ❌ ERR | 错误信息 | 错误和异常 | `_logger.Error("错误信息")` |

## 🚀 灵活的使用方式

### 1. 根据开发阶段选择级别

```csharp
// 🔥 正在重点调试的新功能
private static readonly YLoggerInstance _logger = YLogger.ForDebug<PaymentService>();
// 输出：DBG + IN+ + INF + IN- + WRN + ERR

// 🔥 重点关注的业务类  
private static readonly YLoggerInstance _logger = YLogger.ForDetailed<UserService>();
// 输出：IN+ + INF + IN- + WRN + ERR

// 🔥 一般业务类
private static readonly YLoggerInstance _logger = YLogger.ForInfo<EmailService>();
// 输出：INF + IN- + WRN + ERR

// 🔥 已测试稳定的类
private static readonly YLoggerInstance _logger = YLogger.ForSimple<CacheService>();
// 输出：IN- + WRN + ERR

// 🔥 默认推荐（重要信息）
private static readonly YLoggerInstance _logger = YLogger.ForWarning<OrderService>();
// 输出：WRN + ERR

// 🔥 生产环境稳定类
private static readonly YLoggerInstance _logger = YLogger.ForSilent<DatabaseService>();
// 输出：ERR
```

### 2. 传统方式（兼容）

```csharp
// 开发环境 - 输出详细信息
private static readonly YLoggerInstance _logger = YLogger.ForDevelopment<UserService>();

// 生产环境 - 只输出错误
private static readonly YLoggerInstance _logger = YLogger.ForProduction<UserService>();

// 自定义级别
private static readonly YLoggerInstance _logger = YLogger.For<UserService>(LogLevel.Information);
```

## 🎛️ 全局控制

```csharp
// 强制所有类输出Debug级别及以上（开发调试）
YLogger.ForceDebugMode();

// 强制所有类只输出Warning级别及以上（生产模式）
YLogger.ForceProductionMode();

// 强制所有类只输出Error级别（静默模式）
YLogger.ForceSilentMode();

// 恢复各类独立控制
YLogger.RestoreIndependentMode();
```

## ⏰ 临时控制（自动恢复）

```csharp
// 临时启用详细模式
using (YLogger.TemporaryVerbose())
{
    // 这里所有日志都会输出详细信息
    userService.ProcessUser(123);
} // 自动恢复到原来的级别

// 临时启用静默模式
using (YLogger.TemporarySilent())
{
    // 这里只输出错误信息
    batchService.ProcessLargeData();
} // 自动恢复
```

## 📊 输出效果

```
════════════════════════════════════════════════════════════════════════════════
🎯 新的运行会话开始 - 2025-07-13 10:47:19.767 🎯
════════════════════════════════════════════════════════════════════════════════
[2025-07-13 02:47:19.944] ℹ️ INF         🧵 [UserService.ProcessUser] 执行用户业务逻辑
[2025-07-13 02:47:19.944] 💡 IN-         🧵 [UserService.ProcessUser] 用户处理完成
[2025-07-13 02:47:19.944] ⚠️ WRN         🧵 [UserService.ProcessUser] 用户处理耗时较长
[2025-07-13 02:47:19.948] 🐛 DBG         🧵 [PaymentService.ProcessPayment] 🔍 调试: 开始处理支付
[2025-07-13 02:47:19.949] 📋 IN+         🧵 [PaymentService.ProcessPayment] 验证支付参数: 金额=99.99
[2025-07-13 02:47:20.102] 📋 IN+         🧵 [PaymentService.Dispose] 🏃 完成: 处理支付 ¥99.99 (152.8ms)
[2025-07-13 02:47:20.102] ℹ️ INF         🧵 🎛️ 全局强制级别已设置: Warning
[2025-07-13 02:47:20.103] ❌ ERR         🧵 [EmailService.SendEmail] 邮件发送失败
```

### 🎯 完美对齐的关键技术

- **制表符对齐** - 使用`\t`解决emoji宽度不一致问题
- **统一格式** - 所有级别都是`emoji + 空格 + 3字符缩写 + 制表符`
- **视觉一致** - 完美的列对齐，专业美观

## 🎯 核心价值

### 多级别INFO的实际应用场景

**开发阶段**：

- 新功能开发：使用 `ForDebug<T>()` 看到所有细节
- 重点调试：使用 `ForDetailed<T>()` 专注关键信息
- 一般开发：使用 `ForInfo<T>()` 看到主要流程

**测试阶段**：

- 稳定功能：使用 `ForSimple<T>()` 减少干扰
- 问题排查：临时使用 `TemporaryVerbose()` 查看详情

**生产环境**：

- 核心业务：使用 `ForWarning<T>()` 只看重要信息
- 稳定组件：使用 `ForSilent<T>()` 只记录错误

## 🔧 快速配置

```csharp
// 开发模式配置
YLogger.ConfigureForDevelopment();

// 生产模式配置  
YLogger.ConfigureForProduction();

// 静默模式配置
YLogger.ConfigureForSilent();

// 详细模式配置
YLogger.ConfigureForVerbose();
```

## 📈 最佳实践

1. **新功能开发** - 使用 `ForDebug<T>()` 或 `ForDetailed<T>()`
2. **稳定功能** - 使用 `ForSimple<T>()` 或 `ForWarning<T>()`
3. **生产环境** - 使用 `ForWarning<T>()` 或 `ForSilent<T>()`
4. **临时调试** - 使用 `TemporaryVerbose()` 包装代码块
5. **批量处理** - 使用 `TemporarySilent()` 减少日志噪音

## 🚀 高级功能

### 性能监控

```csharp
// 实例性能监控
using (_logger.Monitor("处理支付"))
{
    // 自动记录执行时间，智能图标显示
    ProcessPayment(amount);
} // 输出: 🏃 完成: 处理支付 (152.8ms)

// 全局性能监控
using (YLogger.MonitorPerformance("整体业务流程"))
{
    // 跨多个类的性能监控
}

// 批量操作优化
using (YLogger.BatchOperation("批量处理", 1000))
{
    // 自动降低日志级别，减少噪音
    // 自动记录批量操作性能
}
```

### 条件和计数器日志

```csharp
// 条件日志 - 避免不必要的输出
_logger.InfoIf(isDebugMode, "调试模式已启用");
_logger.WarningIf(userCount > 200, "用户数量超出警告阈值");

// 计数器日志 - 循环中的智能采样
for (int i = 1; i <= 1000; i++)
{
    _logger.InfoEvery(100, $"处理第{i}个项目"); // 每100次记录一次
}

// 上下文日志 - 自动附加调试信息
var context = new { UserId = 123, SessionId = "abc-123" };
_logger.InfoWithContext("用户登录成功", context);
```

### 统计和健康监控

```csharp
// 获取日志统计
var stats = YLogger.GetStatistics();
Console.WriteLine($"总日志: {stats.TotalLogs}, 错误: {stats.ErrorCount}");

// 健康状态检查
var health = YLogger.CheckHealth();
Console.WriteLine(health.GetHealthReport());
// 输出: 🟢 健康 | 错误率: 9.6% | 总日志: 104 | 运行: 0.0分钟

// 重置统计
YLogger.ResetStatistics();
```

## 🔧 技术特点

- ✅ **高性能** - 异步处理，ConcurrentQueue，批量刷新
- ✅ **线程安全** - 支持高并发场景
- ✅ **多格式输出** - TXT、JSON、XML、CSV
- ✅ **文件持久化** - 自动文件管理，按日期分割
- ✅ **内存优化** - 队列管理，防止内存泄漏
- ✅ **异常安全** - 完善的错误处理机制
- ✅ **emoji兼容** - 制表符对齐，解决宽度不一致问题

---

*多级别INFO让你在开发时专注重点，在生产时保持简洁！* 🎨
