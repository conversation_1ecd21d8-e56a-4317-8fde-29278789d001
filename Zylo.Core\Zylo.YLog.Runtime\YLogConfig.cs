using System;
using System.IO;

namespace Zylo.YLog.Runtime
{
    /// <summary>
    /// 日志输出格式枚举
    /// 🔥 支持多种格式输出，满足不同的使用场景
    ///
    /// 各格式特点：
    /// • TXT：人类可读性最好，适合开发调试
    /// • JSON：结构化程度高，适合程序分析和日志聚合
    /// • XML：标准化格式，适合系统集成和数据交换
    /// • CSV：表格化格式，适合 Excel 分析和数据处理
    /// </summary>
    public enum LogOutputFormat
    {
        /// <summary>
        /// 纯文本格式 - 默认格式，易读性好
        ///
        /// 特点：
        /// • 人类可读性最佳
        /// • 文件大小适中
        /// • 支持彩色控制台输出
        /// • 适合开发和调试场景
        ///
        /// 示例：
        /// [2024-01-15 14:30:25.123] ℹ️ INF 🧵 [MyClass.MyMethod] 操作完成 [150ms]
        /// </summary>
        TXT = 0,

        /// <summary>
        /// JSON格式 - 结构化，便于程序分析
        ///
        /// 特点：
        /// • 结构化程度高
        /// • 便于程序解析
        /// • 支持复杂数据类型
        /// • 适合日志聚合和分析系统
        ///
        /// 适用场景：
        /// • ELK Stack 日志分析
        /// • 微服务日志聚合
        /// • 自动化监控系统
        /// </summary>
        JSON = 1,

        /// <summary>
        /// XML格式 - 结构化，便于系统集成
        ///
        /// 特点：
        /// • 标准化格式
        /// • 自描述性强
        /// • 支持复杂嵌套结构
        /// • 便于系统间数据交换
        ///
        /// 适用场景：
        /// • 企业系统集成
        /// • 标准化日志交换
        /// • 配置管理系统
        /// </summary>
        XML = 2,

        /// <summary>
        /// CSV格式 - 表格化，便于Excel分析
        ///
        /// 特点：
        /// • 表格化结构
        /// • Excel 原生支持
        /// • 文件大小较小
        /// • 便于数据分析
        ///
        /// 适用场景：
        /// • 数据分析和报表
        /// • 性能统计
        /// • 业务数据导出
        /// </summary>
        CSV = 3
    }

    /// <summary>
    /// YLog 配置类
    /// 🔥 集中管理所有日志配置项
    ///
    /// 这是日志系统的配置中心，控制日志系统的所有行为。
    /// 配置项分为几个主要类别：
    /// • 输出配置：控制日志输出到哪里（文件、控制台）
    /// • 性能配置：控制系统性能相关参数（队列大小、批处理等）
    /// • 级别配置：控制日志过滤级别
    /// • 文件管理：控制日志文件的创建、轮转、清理
    /// • 格式配置：控制日志输出格式和内容
    ///
    /// 设计特点：
    /// • 运行时可修改：大部分配置可以在运行时动态调整
    /// • 合理默认值：提供适合大多数场景的默认配置
    /// • 灵活扩展：易于添加新的配置项
    /// • 类型安全：使用强类型属性，避免配置错误
    /// </summary>
    public class YLogConfig
    {
        #region 输出配置

        /// <summary>
        /// 是否启用文件输出
        ///
        /// 控制是否将日志写入文件。文件输出的优点：
        /// • 持久化存储，不会因程序重启而丢失
        /// • 支持大量日志的存储
        /// • 便于后续分析和审计
        /// • 支持多种格式（TXT、JSON、XML、CSV）
        ///
        /// 建议：生产环境建议启用，开发环境可根据需要选择
        /// </summary>
        public bool EnableFileOutput { get; set; } = true;

        /// <summary>
        /// 是否启用控制台输出
        ///
        /// 控制是否将日志输出到控制台。控制台输出的优点：
        /// • 实时可见，便于开发调试
        /// • 支持彩色输出，提升可读性
        /// • 无需额外的文件管理
        /// • 适合容器化环境的日志收集
        ///
        /// 建议：开发环境建议启用，生产环境根据部署方式选择
        /// </summary>
        public bool EnableConsoleOutput { get; set; } = true;

        /// <summary>
        /// 日志目录 - 日志文件存储的目录路径
        ///
        /// 默认使用应用程序基础目录下的 Logs 文件夹。
        /// 如果目录不存在，系统会自动创建。
        ///
        /// 路径选择优先级：
        /// 1. 应用程序基础目录/Logs
        /// 2. 当前工作目录/Logs
        /// 3. 用户文档目录/YLog
        /// 4. 系统临时目录/YLog
        /// </summary>
        public string LogDirectory { get; set; } = GetDefaultLogDirectory();

        #endregion

        #region 性能配置

        /// <summary>
        /// 最大队列大小 - 内存中日志队列的最大容量
        ///
        /// 当队列中的日志条目超过此数量时，会触发立即刷新。
        /// 这是一个重要的性能和内存控制参数：
        ///
        /// • 较大值：更好的批处理效率，但占用更多内存
        /// • 较小值：更及时的日志输出，但可能影响性能
        ///
        /// 默认值 1000 适合大多数应用场景。
        /// 高并发场景可以适当增大，内存敏感场景可以减小。
        /// </summary>
        public int MaxQueueSize { get; set; } = 1000;

        /// <summary>
        /// 批处理大小 - 每次刷新处理的最大日志条目数
        ///
        /// 控制每次从队列中取出多少条日志进行批量处理。
        /// 批处理可以显著提升 I/O 效率：
        ///
        /// • 减少文件写入次数
        /// • 提高磁盘 I/O 效率
        /// • 降低系统调用开销
        ///
        /// 默认值 100 在性能和及时性之间取得平衡。
        /// </summary>
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// 刷新间隔（毫秒） - 定时刷新的时间间隔
        ///
        /// 即使队列未满，也会定期刷新以确保日志及时输出。
        /// 这是日志及时性的重要保证：
        ///
        /// • 较小值：日志更及时，但可能影响性能
        /// • 较大值：更好的性能，但日志可能延迟
        ///
        /// 默认值 1000ms（1秒）适合大多数场景。
        /// 实时性要求高的场景可以减小到 500ms 或更少。
        /// </summary>
        public int FlushIntervalMs { get; set; } = 1000;

        #endregion

        #region 日志级别配置

        /// <summary>
        /// 最小日志级别 - 全局的最小日志级别过滤
        ///
        /// 只有达到或超过此级别的日志才会被处理。
        /// 这是性能优化的重要手段：
        ///
        /// 级别说明（从低到高）：
        /// • Debug：调试信息，开发时使用
        /// • InformationDetailed：详细信息，重点调试时使用
        /// • Information：一般信息，重要业务事件
        /// • InformationSimple：简化信息，关键业务节点
        /// • Warning：警告，潜在问题
        /// • Error：错误，程序异常
        ///
        /// 建议设置：
        /// • 开发环境：Debug
        /// • 测试环境：Information
        /// • 生产环境：Warning
        /// </summary>
        public LogLevel MinimumLevel { get; set; } = LogLevel.Debug;

        #endregion

        #region 文件管理配置

        /// <summary>单个日志文件最大大小（MB）</summary>
        public int MaxFileSizeMB { get; set; } = 50;

        /// <summary>最大保留日志文件数量</summary>
        public int MaxFileCount { get; set; } = 10;

        /// <summary>是否启用文件轮转</summary>
        public bool EnableFileRotation { get; set; } = true;

        /// <summary>
        /// 日志文件命名模式
        /// 🔥 支持多种命名方式
        /// </summary>
        public LogFileNamingMode FileNamingMode { get; set; } = LogFileNamingMode.Daily;

        /// <summary>
        /// 自定义文件名前缀
        /// 🔥 默认为应用程序名称
        /// </summary>
        public string FileNamePrefix { get; set; } = GetDefaultFilePrefix();

        /// <summary>
        /// 获取默认文件名前缀
        /// </summary>
        private static string GetDefaultFilePrefix()
        {
            try
            {
                return System.Reflection.Assembly.GetEntryAssembly()?.GetName().Name ?? "YLog";
            }
            catch
            {
                return "YLog";
            }
        }

        #endregion

        #region 格式配置

        /// <summary>是否包含参数信息</summary>
        public bool IncludeParameters { get; set; } = true;

        /// <summary>是否包含返回值信息</summary>
        public bool IncludeReturnValue { get; set; } = true;

        /// <summary>是否包含执行时间</summary>
        public bool IncludeElapsedTime { get; set; } = true;

        /// <summary>是否包含线程信息</summary>
        public bool IncludeThreadInfo { get; set; } = true;

        /// <summary>
        /// 日志输出格式
        /// 🔥 支持多种格式：TXT、JSON、XML、CSV
        /// </summary>
        public LogOutputFormat OutputFormat { get; set; } = LogOutputFormat.TXT;

        #endregion

        #region 静态方法

        /// <summary>
        /// 获取默认日志目录
        /// 🔥 使用应用程序运行目录下的 Logs 文件夹，更加合理
        /// </summary>
        private static string GetDefaultLogDirectory()
        {
            try
            {
                // 🔥 优先级1: 应用程序基础目录下的 Logs 文件夹 (bin/Debug/net8.0/Logs)
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                if (!string.IsNullOrEmpty(baseDirectory))
                {
                    return Path.Combine(baseDirectory, "Logs");
                }

                // 🔥 优先级2: 当前工作目录下的 Logs 文件夹 (作为备选)
                var currentDirectory = Directory.GetCurrentDirectory();
                if (!string.IsNullOrEmpty(currentDirectory))
                {
                    return Path.Combine(currentDirectory, "Logs");
                }

                // 🔥 优先级3: 用户文档目录 (最后的备选)
                return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "YLog");
            }
            catch
            {
                // 🔥 如果所有方法都失败，使用临时目录
                return Path.Combine(Path.GetTempPath(), "YLog");
            }
        }

        #endregion
    }

    /// <summary>
    /// 日志级别枚举
    /// 🔥 v1.1 增强：Info 级别细分，支持详细和简化模式
    ///
    /// 这是一个精心设计的日志级别体系，特别针对 Info 级别进行了细分，
    /// 以满足不同开发阶段和调试需求的精细化控制。
    ///
    /// 级别设计理念：
    /// • Debug：开发期调试，包含最详细的技术信息
    /// • Info 三级细分：根据信息密度和重要性分层
    /// • Warning/Error：标准的问题级别
    ///
    /// 使用建议：
    /// • 新功能开发：使用 InformationDetailed 查看所有细节
    /// • 功能稳定后：降级到 Information 或 InformationSimple
    /// • 生产环境：使用 Warning 或 Error
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 调试级别 - 最详细的技术调试信息
        ///
        /// 包含：
        /// • 变量值的详细输出
        /// • 方法调用的完整参数
        /// • 算法执行的中间步骤
        /// • 性能测量的详细数据
        ///
        /// 适用场景：
        /// • 开发阶段的深度调试
        /// • 复杂算法的逻辑验证
        /// • 性能问题的详细分析
        /// • 新功能的开发调试
        ///
        /// 注意：Debug 级别会产生大量日志，不建议在生产环境使用
        /// </summary>
        Debug = 0,

        /// <summary>
        /// 详细信息级别 - 包含所有执行细节
        ///
        /// 这是 Info 级别的最详细版本，包含：
        /// • 完整的方法执行流程
        /// • 详细的业务逻辑步骤
        /// • 重要的状态变化过程
        /// • 关键数据的转换过程
        ///
        /// 适用场景：
        /// • 重点功能模块的详细跟踪
        /// • 复杂业务流程的完整记录
        /// • 问题排查时的详细信息收集
        /// • 新功能的全面监控
        ///
        /// 与 Debug 的区别：更关注业务逻辑而非技术细节
        /// </summary>
        InformationDetailed = 1,

        /// <summary>
        /// 一般信息级别 - 重要的业务信息
        ///
        /// 这是标准的信息级别，记录：
        /// • 重要的业务事件
        /// • 关键的状态变化
        /// • 主要的操作结果
        /// • 系统运行状态
        ///
        /// 适用场景：
        /// • 日常开发的标准日志级别
        /// • 业务流程的关键节点记录
        /// • 系统运行状态的监控
        /// • 用户操作的重要事件记录
        ///
        /// 这是最常用的信息级别，平衡了信息量和可读性
        /// </summary>
        Information = 2,

        /// <summary>
        /// 简化信息级别 - 只显示关键业务节点
        ///
        /// 这是 Info 级别的简化版本，只记录：
        /// • 最关键的业务节点
        /// • 重要的操作开始和结束
        /// • 关键的决策点
        /// • 重要的结果摘要
        ///
        /// 适用场景：
        /// • 已经测试稳定的功能模块
        /// • 减少日志噪音的场景
        /// • 关注核心流程的监控
        /// • 生产环境的轻量级监控
        ///
        /// 与 Information 的区别：信息密度更低，更加精炼
        /// </summary>
        InformationSimple = 3,

        /// <summary>
        /// 警告级别 - 潜在问题或异常情况
        ///
        /// 记录不会导致程序崩溃，但需要关注的问题：
        /// • 配置缺失或不合理
        /// • 性能问题或资源不足
        /// • 业务规则的边界情况
        /// • 外部依赖的异常响应
        /// • 数据质量问题
        ///
        /// 适用场景：
        /// • 生产环境的标准监控级别
        /// • 系统健康状态的监控
        /// • 潜在问题的早期发现
        /// • 运维告警的触发条件
        ///
        /// 这是生产环境推荐的最低日志级别
        /// </summary>
        Warning = 4,

        /// <summary>
        /// 错误级别 - 程序错误和异常
        ///
        /// 记录影响程序正常运行的错误：
        /// • 未处理的异常
        /// • 业务逻辑错误
        /// • 数据访问失败
        /// • 外部服务调用失败
        /// • 系统资源不足导致的错误
        ///
        /// 适用场景：
        /// • 错误监控和告警
        /// • 问题排查和诊断
        /// • 系统稳定性监控
        /// • 故障分析和恢复
        ///
        /// Error 级别的日志通常需要立即关注和处理
        /// </summary>
        Error = 5
    }

    /// <summary>
    /// 日志文件命名模式
    /// 🔥 支持多种文件命名方式，满足不同的文件管理需求
    ///
    /// 不同模式的选择考虑因素：
    /// • 日志量大小：大量日志需要更频繁的分割
    /// • 查询需求：按时间查询选择时间分割，按大小查询选择大小分割
    /// • 存储管理：不同分割方式影响文件数量和大小
    /// • 运维需求：不同模式的备份和清理策略不同
    /// </summary>
    public enum LogFileNamingMode
    {
        /// <summary>
        /// 按日期分割 - 一天一个文件 (默认)
        ///
        /// 文件命名格式：AppName_20240115.log
        ///
        /// 优点：
        /// • 便于按日期查找日志
        /// • 文件大小相对可控
        /// • 便于日常维护和备份
        /// • 符合大多数运维习惯
        ///
        /// 适用场景：
        /// • 中等日志量的应用（每天几MB到几GB）
        /// • 需要按日期分析日志的场景
        /// • 标准的企业应用
        ///
        /// 这是推荐的默认模式，适合大多数应用场景
        /// </summary>
        Daily,

        /// <summary>
        /// 按小时分割 - 一小时一个文件
        ///
        /// 文件命名格式：AppName_20240115_14.log
        ///
        /// 优点：
        /// • 更精细的时间分割
        /// • 单个文件更小，便于处理
        /// • 便于高精度的时间分析
        /// • 适合高并发场景
        ///
        /// 适用场景：
        /// • 高并发、大日志量的应用
        /// • 需要精确时间分析的场景
        /// • 实时日志处理系统
        /// • 微服务架构的详细监控
        ///
        /// 注意：会产生较多文件，需要合理的清理策略
        /// </summary>
        Hourly,

        /// <summary>
        /// 单个文件 - 只有一个日志文件
        ///
        /// 文件命名格式：AppName.log
        ///
        /// 优点：
        /// • 文件管理最简单
        /// • 便于实时查看（tail -f）
        /// • 无需考虑文件分割逻辑
        /// • 适合开发和测试环境
        ///
        /// 适用场景：
        /// • 开发和测试环境
        /// • 日志量较小的应用
        /// • 临时调试场景
        /// • 容器化环境（日志由外部收集）
        ///
        /// 注意：文件可能会变得很大，需要定期清理
        /// </summary>
        Single,

        /// <summary>
        /// 按大小分割 - 超过大小就创建新文件
        ///
        /// 文件命名格式：AppName_20240115_001.log, AppName_20240115_002.log
        ///
        /// 优点：
        /// • 文件大小可控
        /// • 便于文件系统管理
        /// • 适合固定大小的存储分配
        /// • 便于网络传输和备份
        ///
        /// 适用场景：
        /// • 对文件大小有严格要求的场景
        /// • 存储空间有限的环境
        /// • 需要定期传输日志文件的场景
        /// • 文件系统对大文件支持不好的情况
        ///
        /// 配合 MaxFileSizeMB 配置使用
        /// </summary>
        BySize,

        /// <summary>
        /// 按周分割 - 一周一个文件
        ///
        /// 文件命名格式：AppName_2024W03.log
        ///
        /// 优点：
        /// • 文件数量适中
        /// • 便于周期性分析
        /// • 符合周报告的时间周期
        /// • 文件大小适中
        ///
        /// 适用场景：
        /// • 中低日志量的应用
        /// • 需要周期性分析的业务
        /// • 按周进行运维管理的场景
        /// • 长期存储但不需要精细分割的情况
        /// </summary>
        Weekly,

        /// <summary>
        /// 按月分割 - 一月一个文件
        ///
        /// 文件命名格式：AppName_202401.log
        ///
        /// 优点：
        /// • 文件数量最少
        /// • 便于长期存储管理
        /// • 符合月报告的时间周期
        /// • 减少文件管理开销
        ///
        /// 适用场景：
        /// • 低日志量的应用
        /// • 长期存储的日志
        /// • 按月进行分析的业务
        /// • 存储成本敏感的场景
        ///
        /// 注意：单个文件可能会比较大，查询时需要更多时间
        /// </summary>
        Monthly
    }
}
