﻿using System;
using System.Collections.Concurrent;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Zylo.YLog.Runtime
{
    /// <summary>
    /// YLog 运行时引擎
    /// 🔥 高性能日志引擎，支持零分配和异步处理
    ///
    /// 核心特性：
    /// • 零分配设计 - 使用对象池和 StringBuilder 缓存，减少 GC 压力
    /// • 异步批处理 - 后台线程批量写入，不阻塞业务线程，提升性能
    /// • 多目标输出 - 支持文件、控制台等多种输出目标
    /// • 多格式支持 - TXT、JSON、XML、CSV 四种输出格式
    /// • 结构化日志 - 完整记录方法调用、参数、返回值、执行时间
    /// • 线程安全 - 使用并发队列，支持高并发场景
    /// • 文件管理 - 支持文件轮转、大小控制、自动清理
    /// • 统计功能 - 实时统计各级别日志数量和系统健康状态
    ///
    /// 架构设计：
    /// • 生产者-消费者模式：业务线程快速入队，后台线程批量处理
    /// • 分层缓冲：内存队列 -> 批量处理 -> 文件/控制台输出
    /// • 故障隔离：日志系统错误不影响业务逻辑执行
    /// • 配置驱动：通过 YLogConfig 灵活控制所有行为
    ///
    /// 性能特点：
    /// • 业务线程延迟：< 1ms（仅入队操作）
    /// • 批处理效率：支持每秒处理数万条日志
    /// • 内存占用：固定大小队列，可控内存使用
    /// • 文件 I/O：批量写入，减少磁盘操作次数
    /// </summary>
    public static class YLogEngine
    {
        #region 配置和状态

        /// <summary>
        /// 日志条目队列 - 使用并发队列确保线程安全
        /// 业务线程将日志条目快速入队，后台线程批量出队处理
        /// </summary>
        private static readonly ConcurrentQueue<LogEntry> _logQueue = new();

        /// <summary>
        /// 定时刷新器 - 定期将队列中的日志批量写入目标
        /// 默认每秒刷新一次，确保日志及时输出
        /// </summary>
        private static readonly Timer _flushTimer;

        /// <summary>
        /// 同步锁对象 - 用于保护关键区域的线程安全
        /// </summary>
        private static readonly object _lockObject = new();

        /// <summary>
        /// 初始化状态标志 - 标记引擎是否已完成初始化
        /// </summary>
        private static volatile bool _isInitialized = false;

        /// <summary>
        /// 关闭状态标志 - 标记引擎是否正在关闭
        /// 关闭时会拒绝新的日志条目，并完成剩余日志的处理
        /// </summary>
        private static volatile bool _isShuttingDown = false;

        /// <summary>
        /// 日志配置 - 控制引擎的所有行为
        ///
        /// 包含输出目标、格式、性能参数、文件管理等所有配置项。
        /// 可以在运行时动态修改，立即生效。
        /// </summary>
        public static YLogConfig Config { get; set; } = new YLogConfig();

        /// <summary>
        /// 统计信息 - 记录日志系统的运行统计
        /// 包括各级别日志数量、运行时间、错误率等信息
        /// </summary>
        private static readonly LogStatistics _statistics = new LogStatistics { StartTime = DateTime.Now };

        /// <summary>
        /// 统计信息同步锁 - 保护统计数据的线程安全
        /// </summary>
        private static readonly object _statsLock = new object();

        #endregion

        #region 初始化

        /// <summary>
        /// 静态构造函数 - 自动初始化日志引擎
        ///
        /// 执行以下初始化操作：
        /// 1. 启动后台定时器，定期刷新日志队列
        /// 2. 写入会话分隔符，标记新的运行会话开始
        /// 3. 注册应用程序退出事件，确保优雅关闭
        ///
        /// 这种设计确保了日志系统的自动启动和优雅关闭，
        /// 无需手动初始化即可使用。
        /// </summary>
        static YLogEngine()
        {
            // 🔥 启动后台刷新定时器 - 每秒执行一次批量处理
            _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
            _isInitialized = true;

            // 🔥 添加运行会话分隔符 - 便于区分不同的运行会话
            WriteSessionSeparator();

            // 🔥 注册应用程序退出事件 - 确保程序退出时完成日志处理
            AppDomain.CurrentDomain.ProcessExit += (s, e) => Shutdown();
        }

        /// <summary>
        /// 写入运行会话分隔符
        ///
        /// 在日志文件中写入美观的分隔符，标记新的运行会话开始。
        /// 这有助于在日志文件中区分不同的程序运行实例，
        /// 特别是在调试和问题排查时非常有用。
        ///
        /// 分隔符格式：
        /// ════════════════════════════════════════════════════════════════════════════════
        /// 🎯 新的运行会话开始 - 2024-01-15 14:30:25.123 🎯
        /// ════════════════════════════════════════════════════════════════════════════════
        /// </summary>
        private static void WriteSessionSeparator()
        {
            var sessionId = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var separator = "═".PadRight(80, '═');

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = "Information",
                Message = $"\n{separator}\n🎯 新的运行会话开始 - {sessionId} 🎯\n{separator}",
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);
        }

        /// <summary>
        /// 关闭日志引擎
        ///
        /// 执行优雅关闭流程：
        /// 1. 设置关闭标志，拒绝新的日志条目
        /// 2. 停止定时器，避免新的刷新操作
        /// 3. 执行最后一次刷新，确保所有日志都被处理
        ///
        /// 这个方法通常在应用程序退出时自动调用，
        /// 也可以手动调用以强制关闭日志系统。
        /// </summary>
        public static void Shutdown()
        {
            _isShuttingDown = true;
            _flushTimer?.Dispose();
            FlushLogs(null);
        }

        #endregion

        #region 统计功能

        /// <summary>
        /// 更新统计信息
        ///
        /// 每当有新的日志条目时，更新相应的统计计数器。
        /// 统计信息包括：
        /// • 总日志数量
        /// • 各级别日志数量（Debug、Info、Warning、Error）
        /// • 各类的日志数量
        /// • 最后日志时间
        ///
        /// 这些统计信息可用于：
        /// • 监控系统运行状态
        /// • 分析日志分布情况
        /// • 检测异常日志模式
        /// • 性能分析和优化
        /// </summary>
        /// <param name="entry">要统计的日志条目</param>
        private static void UpdateStatistics(LogEntry entry)
        {
            lock (_statsLock)
            {
                _statistics.LastLogTime = entry.Timestamp;
                _statistics.TotalLogs++;

                // 按级别统计 - 将所有 Info 变体归类为 Info
                switch (entry.Level.ToUpper())
                {
                    case "DEBUG":
                        _statistics.DebugCount++;
                        break;
                    case "INFORMATION":
                    case "INFORMATIONDETAILED":
                    case "INFORMATIONSIMPLE":
                        _statistics.InfoCount++;
                        break;
                    case "WARNING":
                        _statistics.WarningCount++;
                        break;
                    case "ERROR":
                        _statistics.ErrorCount++;
                        break;
                }

                // 按类统计 - 记录每个类的日志数量
                if (!string.IsNullOrEmpty(entry.ClassName))
                {
                    _statistics.ClassCounts[entry.ClassName] =
                        _statistics.ClassCounts.GetValueOrDefault(entry.ClassName, 0) + 1;
                }
            }
        }

        /// <summary>
        /// 获取统计信息
        ///
        /// 返回当前的日志统计信息快照。
        /// 由于使用了锁保护，返回的是线程安全的副本。
        ///
        /// 统计信息用途：
        /// • 系统监控和报告
        /// • 性能分析
        /// • 问题诊断
        /// • 日志健康检查
        /// </summary>
        /// <returns>当前统计信息的副本</returns>
        public static LogStatistics GetStatistics()
        {
            lock (_statsLock)
            {
                return new LogStatistics
                {
                    StartTime = _statistics.StartTime,
                    LastLogTime = _statistics.LastLogTime,
                    TotalLogs = _statistics.TotalLogs,
                    DebugCount = _statistics.DebugCount,
                    InfoCount = _statistics.InfoCount,
                    WarningCount = _statistics.WarningCount,
                    ErrorCount = _statistics.ErrorCount,
                    ClassCounts = new Dictionary<string, int>(_statistics.ClassCounts)
                };
            }
        }

        /// <summary>
        /// 重置统计信息
        ///
        /// 清空所有统计计数器，重新开始统计。
        /// 通常用于：
        /// • 定期重置统计（如每日重置）
        /// • 测试场景的统计清理
        /// • 系统重启后的统计重置
        ///
        /// 注意：重置操作是原子的，不会影响正在进行的日志记录。
        /// </summary>
        public static void ResetStatistics()
        {
            lock (_statsLock)
            {
                _statistics.StartTime = DateTime.Now;
                _statistics.LastLogTime = DateTime.MinValue;
                _statistics.TotalLogs = 0;
                _statistics.DebugCount = 0;
                _statistics.InfoCount = 0;
                _statistics.WarningCount = 0;
                _statistics.ErrorCount = 0;
                _statistics.ClassCounts.Clear();
            }
        }

        #endregion

        #region 公共日志方法

        /// <summary>
        /// 写入方法执行日志
        ///
        /// 这是核心的日志记录方法，用于记录方法的完整执行信息。
        /// 包括方法名、参数、返回值、执行时间等所有相关信息。
        ///
        /// 特性：
        /// • 结构化记录：完整保存方法调用的所有信息
        /// • 性能监控：记录方法执行时间，便于性能分析
        /// • 异步处理：快速入队，不阻塞业务线程
        /// • 智能刷新：队列过长时自动触发刷新
        ///
        /// 适用场景：
        /// • AOP 拦截器自动记录
        /// • 性能敏感方法的监控
        /// • 复杂业务流程的跟踪
        /// </summary>
        /// <param name="className">类名</param>
        /// <param name="methodName">方法名</param>
        /// <param name="parameters">方法参数数组</param>
        /// <param name="result">方法返回值</param>
        /// <param name="elapsedMs">执行时间（毫秒）</param>
        /// <param name="level">日志级别</param>
        public static void WriteLog(string className, string methodName, object?[] parameters, object? result, long elapsedMs, string level)
        {
            if (_isShuttingDown) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = level,
                ClassName = className,
                MethodName = methodName,
                Parameters = parameters,
                Result = result,
                ElapsedMs = elapsedMs,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);

            // 🔥 队列过长时立即刷新，避免内存积压
            if (_logQueue.Count > Config.MaxQueueSize)
            {
                Task.Run(() => FlushLogs(null));
            }
        }

        /// <summary>
        /// 写入异常日志
        ///
        /// 专门用于记录方法执行过程中发生的异常。
        /// 除了基本的方法信息外，还会记录完整的异常详情。
        ///
        /// 特性：
        /// • 立即刷新：异常日志优先级高，立即写入
        /// • 完整异常信息：包括异常类型、消息、堆栈跟踪
        /// • 上下文保留：保留异常发生时的方法调用上下文
        ///
        /// 适用场景：
        /// • 方法执行异常的自动记录
        /// • 错误诊断和问题排查
        /// • 系统稳定性监控
        /// </summary>
        /// <param name="className">发生异常的类名</param>
        /// <param name="methodName">发生异常的方法名</param>
        /// <param name="parameters">方法参数（异常发生时的参数值）</param>
        /// <param name="exception">异常对象</param>
        /// <param name="elapsedMs">异常发生前的执行时间（毫秒）</param>
        public static void WriteError(string className, string methodName, object?[] parameters, Exception exception, long elapsedMs)
        {
            if (_isShuttingDown) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = "Error",
                ClassName = className,
                MethodName = methodName,
                Parameters = parameters,
                Exception = exception,
                ElapsedMs = elapsedMs,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);

            // 🔥 错误日志立即刷新，确保及时记录
            Task.Run(() => FlushLogs(null));
        }

        /// <summary>
        /// 手动日志 - Debug 级别
        ///
        /// 记录调试信息，用于开发阶段的问题排查。
        /// 支持格式化参数，便于动态构建日志消息。
        ///
        /// 使用示例：
        /// Debug("变量值: {0}, 状态: {1}", value, status);
        /// </summary>
        /// <param name="message">日志消息，支持格式化占位符</param>
        /// <param name="args">格式化参数</param>
        public static void Debug(string message, params object[] args)
        {
            WriteManualLog("Debug", message, args);
        }

        /// <summary>
        /// 手动日志 - Info 级别
        ///
        /// 记录一般信息，用于重要业务事件的记录。
        /// 这是最常用的日志级别，平衡了信息量和性能。
        ///
        /// 使用示例：
        /// Info("用户 {0} 登录成功", username);
        /// </summary>
        /// <param name="message">日志消息，支持格式化占位符</param>
        /// <param name="args">格式化参数</param>
        public static void Info(string message, params object[] args)
        {
            WriteManualLog("Information", message, args);
        }

        /// <summary>
        /// 手动日志 - 详细信息级别
        ///
        /// 记录详细的执行信息，包含所有执行细节。
        /// 适用于重点调试的功能模块或复杂业务流程的跟踪。
        ///
        /// 与 Debug 的区别：更关注业务逻辑而非技术细节。
        ///
        /// 使用示例：
        /// InfoDetailed("开始处理订单 {0}，包含 {1} 个商品", orderId, itemCount);
        /// </summary>
        /// <param name="message">详细日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void InfoDetailed(string message, params object[] args)
        {
            WriteManualLog("InformationDetailed", message, args);
        }

        /// <summary>
        /// 手动日志 - 简化信息级别
        ///
        /// 只记录关键业务节点，减少日志噪音。
        /// 适用于已经测试稳定的功能模块。
        ///
        /// 使用示例：
        /// InfoSimple("订单处理完成: {0}", orderId);
        /// </summary>
        /// <param name="message">简化日志消息</param>
        /// <param name="args">格式化参数</param>
        public static void InfoSimple(string message, params object[] args)
        {
            WriteManualLog("InformationSimple", message, args);
        }

        /// <summary>
        /// 手动日志 - Warning 级别
        ///
        /// 记录警告信息，表示潜在问题或异常情况。
        /// 这些问题不会导致程序崩溃，但需要关注。
        ///
        /// 使用示例：
        /// Warning("配置文件 {0} 不存在，使用默认配置", configFile);
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <param name="args">格式化参数</param>
        public static void Warning(string message, params object[] args)
        {
            WriteManualLog("Warning", message, args);
        }

        /// <summary>
        /// 手动日志 - Error 级别
        ///
        /// 记录错误信息，表示程序运行中的错误。
        /// 这些错误可能影响功能正常运行，需要立即关注。
        ///
        /// 使用示例：
        /// Error("数据库连接失败: {0}", connectionString);
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="args">格式化参数</param>
        public static void Error(string message, params object[] args)
        {
            WriteManualLog("Error", message, args);
        }

        /// <summary>
        /// 手动日志 - Error 级别（仅异常）
        ///
        /// 记录异常信息，使用异常的消息作为日志内容。
        /// 这是处理异常的便捷方法。
        ///
        /// 使用示例：
        /// try { ... } catch (Exception ex) { Error(ex); }
        /// </summary>
        /// <param name="exception">要记录的异常</param>
        public static void Error(Exception exception)
        {
            Error(exception.Message, exception);
        }

        /// <summary>
        /// 手动日志 - Error 级别（消息+异常）
        ///
        /// 记录错误消息和异常详情，提供最完整的错误诊断信息。
        /// 除了自定义消息外，还会记录完整的异常信息和堆栈跟踪。
        ///
        /// 这是处理异常时的推荐方式，提供了最丰富的错误上下文。
        /// 错误日志会立即刷新，确保及时记录。
        ///
        /// 使用示例：
        /// Error("处理用户请求时发生错误", exception);
        /// </summary>
        /// <param name="message">错误描述消息</param>
        /// <param name="exception">相关的异常对象</param>
        public static void Error(string message, Exception exception)
        {
            if (_isShuttingDown) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = "Error",
                Message = message,
                Exception = exception,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);
            // 错误日志立即刷新，确保及时记录
            Task.Run(() => FlushLogs(null));
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 写入手动日志
        /// 🔥 v1.1增强：添加日志级别过滤
        /// </summary>
        private static void WriteManualLog(string level, string message, object[] args)
        {
            if (_isShuttingDown) return;

            // 🔥 v1.1新增：日志级别过滤
            if (!ShouldLog(level)) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.UtcNow,
                Level = level,
                Message = message,
                Parameters = args,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);
            UpdateStatistics(entry); // 🔥 v4.0新增：更新统计
        }

        /// <summary>
        /// 判断是否应该记录该级别的日志
        /// 🔥 v1.1新增：支持 Info 级别细分
        /// </summary>
        private static bool ShouldLog(string level)
        {
            var levelValue = GetLogLevelValue(level);
            var minLevelValue = GetLogLevelValue(Config.MinimumLevel.ToString());
            return levelValue >= minLevelValue;
        }

        /// <summary>
        /// 获取日志级别的数值
        /// 🔥 v1.1新增：支持新的 Info 分级
        /// </summary>
        private static int GetLogLevelValue(string level)
        {
            return level switch
            {
                "Debug" => 0,
                "InformationDetailed" => 1,
                "Information" => 2,
                "InformationSimple" => 3,
                "Warning" => 4,
                "Error" => 5,
                _ => 2 // 默认为 Information 级别
            };
        }

        /// <summary>
        /// 刷新日志到输出目标
        /// 🔥 完整的文件持久化功能
        /// </summary>
        private static void FlushLogs(object? state)
        {
            if (_logQueue.IsEmpty) return;

            var entries = new List<LogEntry>();

            // 🔥 批量取出日志条目
            while (_logQueue.TryDequeue(out var entry) && entries.Count < Config.BatchSize)
            {
                entries.Add(entry);
            }

            if (!entries.Any()) return;

            try
            {
                // 🔥 写入文件（JSON 格式）
                if (Config.EnableFileOutput)
                {
                    WriteToFile(entries);
                }

                // 🔥 写入控制台
                if (Config.EnableConsoleOutput)
                {
                    WriteToConsole(entries);
                }
            }
            catch (Exception ex)
            {
                // 🔥 日志系统本身的错误，写入控制台
                Console.WriteLine($"YLog Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入文件 - 支持多种格式
        /// 🔥 根据配置的格式输出：TXT、JSON、XML、CSV
        /// </summary>
        private static void WriteToFile(List<LogEntry> entries)
        {
            var logFile = GetLogFilePath();
            var directory = Path.GetDirectoryName(logFile);

            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 🔥 检查文件大小并进行轮转
            if (Config.EnableFileRotation)
            {
                CheckAndRotateLogFile(logFile);
            }

            var content = FormatLogEntries(entries, Config.OutputFormat);
            File.AppendAllText(logFile, content, Encoding.UTF8);
        }

        /// <summary>
        /// 根据格式格式化日志条目
        /// 🔥 支持多种输出格式
        /// </summary>
        private static string FormatLogEntries(List<LogEntry> entries, LogOutputFormat format)
        {
            return format switch
            {
                LogOutputFormat.JSON => FormatAsJson(entries),
                LogOutputFormat.XML => FormatAsXml(entries),
                LogOutputFormat.CSV => FormatAsCsv(entries),
                LogOutputFormat.TXT or _ => FormatAsTxt(entries)
            };
        }

        /// <summary>
        /// 写入控制台
        /// </summary>
        private static void WriteToConsole(List<LogEntry> entries)
        {
            foreach (var entry in entries)
            {
                var color = GetConsoleColor(entry.Level);
                var originalColor = Console.ForegroundColor;

                Console.ForegroundColor = color;
                Console.WriteLine(FormatLogEntryAsText(entry));
                Console.ForegroundColor = originalColor;
            }
        }

        /// <summary>
        /// 格式化为 TXT 格式
        /// </summary>
        private static string FormatAsTxt(List<LogEntry> entries)
        {
            var sb = new StringBuilder();
            foreach (var entry in entries)
            {
                sb.AppendLine(FormatLogEntryAsText(entry));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 格式化日志条目为文本
        /// 🔥 简单的文本格式，避免序列化问题
        /// </summary>
        /// <summary>
        /// 预处理级别显示 - 简单直接的对齐方案
        /// 🔥 v10.0简化版：直接硬编码对齐，简单有效
        /// </summary>
        private static string GetPreprocessedLevelDisplay(string level)
        {
            // 简单直接：为每个级别硬编码对齐的空格数
            // 基于实际观察，直接调整到完美对齐
            return level.ToUpper() switch
            {
                "DEBUG" => "🐛 DBG\t",           // 使用制表符对齐
                "INFORMATIONDETAILED" => "📋 IN+\t",    // 使用制表符对齐
                "INFORMATION" => "ℹ️ INF\t",           // 使用制表符对齐
                "INFORMATIONSIMPLE" => "💡 IN-\t ",     // 制表符+1个空格微调
                "WARNING" => "⚠️ WRN\t",              // 使用制表符对齐
                "ERROR" => "❌ ERR\t",                // 使用制表符对齐
                _ => $"❓ {level.Substring(0, Math.Min(3, level.Length)).ToUpper()}\t"
            };
        }

        /// <summary>
        /// 计算文字的实际显示宽度
        /// 🔍 考虑emoji字符的特殊显示宽度
        /// </summary>
        private static int CalculateDisplayWidth(string text)
        {
            int width = 0;
            foreach (char c in text)
            {
                if (IsEmoji(c))
                {
                    width += 2; // emoji通常占用2个字符宽度
                }
                else
                {
                    width += 1; // 普通字符占用1个字符宽度
                }
            }
            return width;
        }

        /// <summary>
        /// 判断字符是否为emoji
        /// </summary>
        private static bool IsEmoji(char c)
        {
            // 简单的emoji判断逻辑
            return c >= 0x1F000 && c <= 0x1F9FF || // 各种符号和象形文字
                   c >= 0x2600 && c <= 0x26FF ||   // 杂项符号
                   c >= 0x2700 && c <= 0x27BF;     // 装饰符号
        }

        /// <summary>
        /// 获取级别显示 - 使用预处理后的固定宽度
        /// 🔥 v4.1重构：简化为调用预处理方法
        /// </summary>
        private static string GetLevelDisplay(string level)
        {
            return GetPreprocessedLevelDisplay(level);
        }



        /// <summary>
        /// 获取线程图标
        /// 🔥 v2.0新增：线程可视化
        /// </summary>
        private static string GetThreadIcon(int threadId)
        {
            return threadId switch
            {
                1 => "🧵",      // 主线程
                2 => "🔗",      // 第二线程
                3 => "⚡",      // 第三线程
                4 => "🌟",      // 第四线程
                _ => "🔄"       // 其他线程
            };
        }

        private static string FormatLogEntryAsText(LogEntry entry)
        {
            var sb = new StringBuilder();

            // 🔥 v4.1重构：使用预处理后的固定宽度级别显示
            sb.Append($"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] ");
            sb.Append($"{GetPreprocessedLevelDisplay(entry.Level)} ");  // 预处理后的固定宽度
            sb.Append($"{GetThreadIcon(entry.ThreadId)} ");             // 线程图标

            // 🔥 方法信息
            if (!string.IsNullOrEmpty(entry.ClassName) && !string.IsNullOrEmpty(entry.MethodName))
            {
                sb.Append($"{entry.ClassName}.{entry.MethodName}");

                // 🔥 参数信息
                if (entry.Parameters != null && entry.Parameters.Length > 0)
                {
                    var paramStrs = new List<string>();
                    for (int i = 0; i < entry.Parameters.Length; i++)
                    {
                        paramStrs.Add(SafeToString(entry.Parameters[i]));
                    }
                    sb.Append($"({string.Join(", ", paramStrs)})");
                }
                else
                {
                    sb.Append("()");
                }

                // 🔥 返回值
                if (entry.Result != null)
                {
                    sb.Append($" => {SafeToString(entry.Result)}");
                }

                // 🔥 执行时间
                if (entry.ElapsedMs > 0)
                {
                    sb.Append($" [{entry.ElapsedMs}ms]");
                }
            }

            // 🔥 手动日志消息
            if (!string.IsNullOrEmpty(entry.Message))
            {
                if (!string.IsNullOrEmpty(entry.ClassName))
                {
                    sb.Append(" - ");
                }
                sb.Append(entry.Message);

                // 🔥 手动日志参数
                if (entry.Parameters != null && entry.Parameters.Length > 0)
                {
                    sb.Append(" [");
                    for (int i = 0; i < entry.Parameters.Length; i += 2)
                    {
                        if (i > 0) sb.Append(", ");
                        if (i + 1 < entry.Parameters.Length)
                        {
                            sb.Append($"{entry.Parameters[i]}={SafeToString(entry.Parameters[i + 1])}");
                        }
                        else
                        {
                            sb.Append(SafeToString(entry.Parameters[i]));
                        }
                    }
                    sb.Append("]");
                }
            }

            // 🔥 异常信息
            if (entry.Exception != null)
            {
                sb.AppendLine();
                sb.Append($"    Exception: {entry.Exception.GetType().Name}: {entry.Exception.Message}");
                if (!string.IsNullOrEmpty(entry.Exception.StackTrace))
                {
                    sb.AppendLine();
                    sb.Append($"    StackTrace: {entry.Exception.StackTrace}");
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 安全转换对象为字符串
        /// </summary>
        private static string SafeToString(object? obj)
        {
            if (obj == null) return "null";

            try
            {
                return obj.ToString() ?? "null";
            }
            catch
            {
                return $"<{obj.GetType().Name}>";
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// 🔥 支持多种文件命名模式
        /// </summary>
        private static string GetLogFilePath()
        {
            var now = DateTime.Now;
            var prefix = Config.FileNamePrefix;

            var fileName = Config.FileNamingMode switch
            {
                LogFileNamingMode.Daily => $"{prefix}_{now:yyyyMMdd}.log",
                LogFileNamingMode.Hourly => $"{prefix}_{now:yyyyMMdd_HH}.log",
                LogFileNamingMode.Weekly => $"{prefix}_{GetWeekString(now)}.log",
                LogFileNamingMode.Monthly => $"{prefix}_{now:yyyyMM}.log",
                LogFileNamingMode.Single => $"{prefix}.log",
                LogFileNamingMode.BySize => $"{prefix}_{now:yyyyMMdd}.log", // 按大小分割时仍按日期命名基础文件
                _ => $"{prefix}_{now:yyyyMMdd}.log"
            };

            return Path.Combine(Config.LogDirectory, fileName);
        }

        /// <summary>
        /// 获取周字符串 (年份 + 第几周)
        /// </summary>
        private static string GetWeekString(DateTime date)
        {
            var culture = System.Globalization.CultureInfo.CurrentCulture;
            var weekOfYear = culture.Calendar.GetWeekOfYear(date,
                culture.DateTimeFormat.CalendarWeekRule,
                culture.DateTimeFormat.FirstDayOfWeek);
            return $"{date.Year}W{weekOfYear:D2}";
        }

        /// <summary>
        /// 获取控制台颜色
        /// </summary>
        private static ConsoleColor GetConsoleColor(string level)
        {
            return level switch
            {
                "Debug" => ConsoleColor.Gray,
                "Information" => ConsoleColor.White,
                "Warning" => ConsoleColor.Yellow,
                "Error" => ConsoleColor.Red,
                _ => ConsoleColor.White
            };
        }

        /// <summary>
        /// 检查并轮转日志文件
        /// 🔥 文件大小管理和轮转功能
        /// </summary>
        private static void CheckAndRotateLogFile(string logFile)
        {
            try
            {
                if (!File.Exists(logFile)) return;

                var fileInfo = new FileInfo(logFile);
                var maxSizeBytes = Config.MaxFileSizeMB * 1024 * 1024;

                // 🔥 检查文件大小
                if (fileInfo.Length > maxSizeBytes)
                {
                    RotateLogFiles(logFile);
                }
            }
            catch (Exception ex)
            {
                // 🔥 轮转失败不应该影响日志记录
                Console.WriteLine($"YLog 文件轮转失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行日志文件轮转
        /// </summary>
        private static void RotateLogFiles(string currentLogFile)
        {
            var directory = Path.GetDirectoryName(currentLogFile);
            var fileName = Path.GetFileNameWithoutExtension(currentLogFile);
            var extension = Path.GetExtension(currentLogFile);

            if (string.IsNullOrEmpty(directory)) return;

            // 🔥 生成带时间戳的备份文件名
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFileName = $"{fileName}_{timestamp}{extension}";
            var backupFilePath = Path.Combine(directory, backupFileName);

            // 🔥 移动当前文件为备份文件
            File.Move(currentLogFile, backupFilePath);

            // 🔥 清理旧的日志文件
            CleanupOldLogFiles(directory, fileName, extension);
        }

        /// <summary>
        /// 清理旧的日志文件
        /// </summary>
        private static void CleanupOldLogFiles(string directory, string baseFileName, string extension)
        {
            try
            {
                var pattern = $"{baseFileName}_*{extension}";
                var logFiles = Directory.GetFiles(directory, pattern)
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                // 🔥 保留最新的 N 个文件，删除其余的
                var filesToDelete = logFiles.Skip(Config.MaxFileCount).ToList();
                foreach (var file in filesToDelete)
                {
                    try
                    {
                        file.Delete();
                    }
                    catch
                    {
                        // 🔥 删除失败不影响其他操作
                    }
                }
            }
            catch
            {
                // 🔥 清理失败不影响日志记录
            }
        }

        #endregion

        #region 多格式支持

        /// <summary>
        /// 格式化为 JSON 格式
        /// 🔥 结构化格式，便于程序分析
        /// </summary>
        private static string FormatAsJson(List<LogEntry> entries)
        {
            var sb = new StringBuilder();
            foreach (var entry in entries)
            {
                var jsonEntry = new
                {
                    timestamp = entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    level = entry.Level,
                    threadId = entry.ThreadId,
                    className = entry.ClassName,
                    methodName = entry.MethodName,
                    parameters = entry.Parameters?.Select(SafeToString).ToArray(),
                    result = SafeToString(entry.Result),
                    elapsedMs = entry.ElapsedMs,
                    message = entry.Message,
                    exception = entry.Exception != null ? new
                    {
                        type = entry.Exception.GetType().Name,
                        message = entry.Exception.Message,
                        stackTrace = entry.Exception.StackTrace
                    } : null
                };

                sb.AppendLine(JsonSerializer.Serialize(jsonEntry, new JsonSerializerOptions
                {
                    WriteIndented = false,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                }));
            }
            return sb.ToString();
        }

        /// <summary>
        /// 格式化为 XML 格式
        /// 🔥 结构化格式，便于系统集成
        /// </summary>
        private static string FormatAsXml(List<LogEntry> entries)
        {
            var sb = new StringBuilder();
            foreach (var entry in entries)
            {
                sb.AppendLine("<LogEntry>");
                sb.AppendLine($"  <Timestamp>{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}</Timestamp>");
                sb.AppendLine($"  <Level>{EscapeXml(entry.Level)}</Level>");
                sb.AppendLine($"  <ThreadId>{entry.ThreadId}</ThreadId>");

                if (!string.IsNullOrEmpty(entry.ClassName))
                    sb.AppendLine($"  <ClassName>{EscapeXml(entry.ClassName)}</ClassName>");

                if (!string.IsNullOrEmpty(entry.MethodName))
                    sb.AppendLine($"  <MethodName>{EscapeXml(entry.MethodName)}</MethodName>");

                if (entry.Parameters != null && entry.Parameters.Length > 0)
                {
                    sb.AppendLine("  <Parameters>");
                    for (int i = 0; i < entry.Parameters.Length; i++)
                    {
                        sb.AppendLine($"    <Parameter Index=\"{i}\">{EscapeXml(SafeToString(entry.Parameters[i]))}</Parameter>");
                    }
                    sb.AppendLine("  </Parameters>");
                }

                if (entry.Result != null)
                    sb.AppendLine($"  <Result>{EscapeXml(SafeToString(entry.Result))}</Result>");

                if (entry.ElapsedMs > 0)
                    sb.AppendLine($"  <ElapsedMs>{entry.ElapsedMs}</ElapsedMs>");

                if (!string.IsNullOrEmpty(entry.Message))
                    sb.AppendLine($"  <Message>{EscapeXml(entry.Message)}</Message>");

                if (entry.Exception != null)
                {
                    sb.AppendLine("  <Exception>");
                    sb.AppendLine($"    <Type>{EscapeXml(entry.Exception.GetType().Name)}</Type>");
                    sb.AppendLine($"    <Message>{EscapeXml(entry.Exception.Message)}</Message>");
                    if (!string.IsNullOrEmpty(entry.Exception.StackTrace))
                        sb.AppendLine($"    <StackTrace>{EscapeXml(entry.Exception.StackTrace)}</StackTrace>");
                    sb.AppendLine("  </Exception>");
                }

                sb.AppendLine("</LogEntry>");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 格式化为 CSV 格式
        /// 🔥 表格格式，便于 Excel 分析
        /// </summary>
        private static string FormatAsCsv(List<LogEntry> entries)
        {
            var sb = new StringBuilder();

            // 🔥 添加 CSV 头部（只在文件开始时添加）
            var logFile = GetLogFilePath();
            if (!File.Exists(logFile) || new FileInfo(logFile).Length == 0)
            {
                sb.AppendLine("Timestamp,Level,ThreadId,ClassName,MethodName,Parameters,Result,ElapsedMs,Message,ExceptionType,ExceptionMessage");
            }

            foreach (var entry in entries)
            {
                var fields = new[]
                {
                    entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    entry.Level,
                    entry.ThreadId.ToString(),
                    entry.ClassName ?? "",
                    entry.MethodName ?? "",
                    entry.Parameters != null ? string.Join(";", entry.Parameters.Select(SafeToString)) : "",
                    SafeToString(entry.Result),
                    entry.ElapsedMs.ToString(),
                    entry.Message ?? "",
                    entry.Exception?.GetType().Name ?? "",
                    entry.Exception?.Message ?? ""
                };

                // 🔥 转义 CSV 字段
                var escapedFields = fields.Select(EscapeCsv);
                sb.AppendLine(string.Join(",", escapedFields));
            }

            return sb.ToString();
        }

        /// <summary>
        /// XML 转义
        /// </summary>
        private static string EscapeXml(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            return text
                .Replace("&", "&amp;")
                .Replace("<", "&lt;")
                .Replace(">", "&gt;")
                .Replace("\"", "&quot;")
                .Replace("'", "&apos;");
        }

        /// <summary>
        /// CSV 转义
        /// </summary>
        private static string EscapeCsv(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
            if (text.Contains(',') || text.Contains('"') || text.Contains('\n') || text.Contains('\r'))
            {
                return $"\"{text.Replace("\"", "\"\"")}\"";
            }

            return text;
        }

        #endregion
    }
}
