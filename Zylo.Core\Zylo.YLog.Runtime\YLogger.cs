using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.CompilerServices;

namespace Zylo.YLog.Runtime
{
    /// <summary>
    /// YLogger - 统一日志管理器
    /// 🔥 提供全局日志控制和按类日志实例管理
    ///
    /// 核心功能：
    /// 1. 全局日志方法：YLogger.Info("消息") - 直接记录日志
    /// 2. 按类日志实例：YLogger.For&lt;MyClass&gt;(LogLevel.Info) - 为特定类创建日志实例
    /// 3. 全局强制级别：YLogger.ForceProductionMode() - 覆盖所有类的日志级别
    /// 4. 快速配置：YLogger.ConfigureForDevelopment() - 一键配置开发环境
    ///
    /// 设计特点：
    /// • 分层控制：全局级别 > 类级别，支持灵活的日志控制策略
    /// • 多级别INFO：支持详细、一般、简化三种信息级别，适应不同调试需求
    /// • 自动化管理：支持自动配置显示、性能监控、批量操作等高级功能
    /// • 线程安全：所有操作都是线程安全的，支持高并发场景
    /// • 零配置使用：开箱即用，同时支持深度定制
    ///
    /// 使用示例：
    /// <code>
    /// // 全局日志
    /// YLogger.Info("应用程序启动");
    /// YLogger.Warning("配置文件缺失，使用默认配置");
    ///
    /// // 按类日志实例
    /// var logger = YLogger.For&lt;MyService&gt;(LogLevel.Debug);
    /// logger.Info("服务初始化完成");
    /// logger.Debug("详细调试信息");
    ///
    /// // 快速模式切换
    /// YLogger.ForceDebugMode();  // 所有类输出详细信息
    /// YLogger.ForceProductionMode();  // 所有类只输出警告和错误
    ///
    /// // 性能监控
    /// using (YLogger.MonitorPerformance("数据库查询"))
    /// {
    ///     // 执行数据库操作
    /// }
    /// </code>
    /// </summary>
    public static class YLogger
    {
        #region 全局强制级别控制

        /// <summary>
        /// 全局强制日志级别
        /// 当设置时，会覆盖所有类实例的日志级别设置
        /// null 表示使用各类的独立设置
        /// </summary>
        internal static LogLevel? _globalForceLevel = null;

        /// <summary>
        /// 设置全局强制级别 - 覆盖所有类的设置
        ///
        /// 这是一个强大的全局控制功能，可以在运行时动态调整所有日志输出级别：
        /// • 设置为 Debug：所有类都输出详细调试信息
        /// • 设置为 Warning：所有类只输出警告和错误
        /// • 设置为 null：恢复各类的独立控制
        ///
        /// 典型使用场景：
        /// • 生产环境临时开启调试模式
        /// • 性能测试时临时关闭详细日志
        /// • 问题排查时临时提升日志级别
        /// </summary>
        /// <param name="level">要设置的全局级别，null 表示恢复独立控制</param>
        public static void SetGlobalForceLevel(LogLevel? level)
        {
            _globalForceLevel = level;
            if (level.HasValue)
            {
                Info($"🎛️ 全局强制级别已设置: {level.Value}");
            }
            else
            {
                Info("🔓 已恢复各类独立控制");
            }
        }

        /// <summary>
        /// 获取当前全局强制级别
        /// </summary>
        /// <returns>当前的全局强制级别，null 表示使用各类独立设置</returns>
        public static LogLevel? GetGlobalForceLevel() => _globalForceLevel;

        /// <summary>
        /// 检查是否应该记录日志（全局强制级别优先）
        ///
        /// 日志级别判断逻辑：
        /// 1. 如果设置了全局强制级别，使用全局级别进行判断
        /// 2. 如果没有设置全局强制级别，使用类级别进行判断
        ///
        /// 这种设计确保了全局控制的优先级，同时保持了类级别控制的灵活性
        /// </summary>
        /// <param name="level">要记录的日志级别</param>
        /// <param name="classLevel">类实例设置的最小级别</param>
        /// <returns>true 表示应该记录该级别的日志</returns>
        internal static bool ShouldLogGlobally(LogLevel level, LogLevel classLevel)
        {
            return _globalForceLevel.HasValue ? level >= _globalForceLevel.Value : level >= classLevel;
        }

        #endregion

        #region 快速模式切换

        /// <summary>
        /// 强制Debug模式 - 所有类输出详细信息
        ///
        /// 适用场景：
        /// • 开发调试时需要查看所有执行细节
        /// • 生产环境临时排查问题
        /// • 性能分析时需要详细的执行信息
        ///
        /// 注意：Debug模式会产生大量日志，可能影响性能
        /// </summary>
        public static void ForceDebugMode() => SetGlobalForceLevel(LogLevel.Debug);

        /// <summary>
        /// 强制生产模式 - 所有类只输出Warning及以上
        ///
        /// 适用场景：
        /// • 生产环境的标准配置
        /// • 性能测试时减少日志开销
        /// • 日常运行时只关注警告和错误
        ///
        /// 这是推荐的生产环境设置，平衡了信息量和性能
        /// </summary>
        public static void ForceProductionMode() => SetGlobalForceLevel(LogLevel.Warning);

        /// <summary>
        /// 强制静默模式 - 所有类只输出Error
        ///
        /// 适用场景：
        /// • 高性能要求的场景
        /// • 批量处理时减少日志噪音
        /// • 只关注严重错误的情况
        ///
        /// 注意：静默模式可能会错过重要的警告信息
        /// </summary>
        public static void ForceSilentMode() => SetGlobalForceLevel(LogLevel.Error);

        /// <summary>
        /// 恢复独立模式 - 各类按自己的设置
        ///
        /// 取消全局强制级别，让每个类实例使用自己的日志级别设置。
        /// 这是默认模式，提供最大的灵活性。
        ///
        /// 适用场景：
        /// • 需要对不同类使用不同日志级别
        /// • 开发时对重点调试的类使用详细级别
        /// • 对稳定的类使用简化级别
        /// </summary>
        public static void RestoreIndependentMode() => SetGlobalForceLevel(null);

        #endregion

        #region 📝 全局日志方法

        // ==================== Debug 级别 ====================

        /// <summary>
        /// 调试日志 - 记录详细的调试信息
        ///
        /// 🎯 用途：
        /// 用于记录程序执行的详细信息，帮助开发者理解程序流程和变量状态。
        /// 通常在开发和调试阶段使用，生产环境建议关闭以提升性能。
        ///
        /// 📋 适用场景：
        /// • 变量值的跟踪和验证
        /// • 算法执行步骤的详细记录
        /// • 复杂逻辑的流程跟踪
        /// • 性能敏感代码的执行分析
        ///
        /// 💡 使用示例：
        /// YLogger.Debug("用户ID验证通过，开始加载用户数据");
        /// </summary>
        /// <param name="message">调试消息</param>
        public static void Debug(string message) => YLogEngine.Debug(message);

        /// <summary>
        /// 调试日志（带参数） - 支持格式化参数
        ///
        /// 支持使用占位符和参数的格式化日志记录，避免字符串拼接的性能开销。
        ///
        /// 💡 使用示例：
        /// YLogger.Debug("用户 {0} 执行了 {1} 操作，耗时 {2}ms", userId, action, elapsed);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void Debug(string message, params object[] args) => YLogEngine.Debug(message, args);

        // ==================== Information 级别（三个子级别）====================

        /// <summary>
        /// 详细信息日志 - 包含所有执行细节
        ///
        /// 🎯 用途：
        /// 这是 Info 级别的最详细版本，包含完整的执行信息和业务流程细节。
        /// 适用于重点调试的功能模块，或需要详细了解执行过程的场景。
        ///
        /// 📋 与其他Info级别的区别：
        /// • InfoDetailed：最详细，包含所有执行细节
        /// • Info：标准级别，记录重要业务事件
        /// • InfoSimple：最简化，只记录关键节点
        ///
        /// 💡 使用示例：
        /// YLogger.InfoDetailed("开始处理订单，包含商品验证、库存检查、价格计算等步骤");
        /// </summary>
        /// <param name="message">详细信息消息</param>
        public static void InfoDetailed(string message) => YLogEngine.InfoDetailed(message);

        /// <summary>
        /// 详细信息日志（带参数）
        ///
        /// 💡 使用示例：
        /// YLogger.InfoDetailed("处理订单 {0}，包含 {1} 个商品，总价 {2}", orderId, itemCount, totalPrice);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void InfoDetailed(string message, params object[] args) => YLogEngine.InfoDetailed(message, args);

        /// <summary>
        /// 一般信息日志 - 记录重要的业务信息
        ///
        /// 🎯 用途：
        /// 这是标准的信息级别，用于记录重要的业务事件和状态变化。
        /// 适用于记录关键的业务流程节点，便于了解系统运行状态。
        ///
        /// 📋 记录内容：
        /// • 重要的业务操作开始和结束
        /// • 关键的状态变化
        /// • 用户重要操作的记录
        /// • 系统配置和初始化信息
        ///
        /// 💡 使用示例：
        /// YLogger.Info("用户登录成功，开始加载个人信息");
        /// </summary>
        /// <param name="message">信息消息</param>
        public static void Info(string message) => YLogEngine.Info(message);

        /// <summary>
        /// 一般信息日志（带参数）
        ///
        /// 💡 使用示例：
        /// YLogger.Info("用户 {0} 登录成功，来源IP: {1}", username, clientIP);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void Info(string message, params object[] args) => YLogEngine.Info(message, args);

        /// <summary>
        /// 简化信息日志 - 只显示关键业务节点
        ///
        /// 🎯 用途：
        /// 这是 Info 级别的简化版本，只记录最关键的业务节点。
        /// 适用于已经测试稳定的功能模块，减少日志噪音。
        ///
        /// 📋 记录内容：
        /// • 最关键的业务操作结果
        /// • 重要的里程碑事件
        /// • 核心流程的开始和结束
        ///
        /// 💡 使用示例：
        /// YLogger.InfoSimple("订单处理完成");
        /// </summary>
        /// <param name="message">简化信息消息</param>
        public static void InfoSimple(string message) => YLogEngine.InfoSimple(message);

        /// <summary>
        /// 简化信息日志（带参数）
        ///
        /// 💡 使用示例：
        /// YLogger.InfoSimple("订单 {0} 处理完成", orderId);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void InfoSimple(string message, params object[] args) => YLogEngine.InfoSimple(message, args);

        // ==================== Warning 级别 ====================

        /// <summary>
        /// 警告日志 - 记录潜在问题或异常情况
        ///
        /// 🎯 用途：
        /// 用于记录不会导致程序崩溃，但需要关注的问题。
        /// 这些问题可能会影响系统性能或用户体验，需要适时处理。
        ///
        /// 📋 记录内容：
        /// • 配置缺失或不合理
        /// • 性能问题或资源不足
        /// • 业务规则的边界情况
        /// • 外部依赖的异常响应
        /// • 数据质量问题
        ///
        /// 💡 使用示例：
        /// YLogger.Warning("配置文件缺失，使用默认配置");
        /// </summary>
        /// <param name="message">警告消息</param>
        public static void Warning(string message) => YLogEngine.Warning(message);

        /// <summary>
        /// 警告日志（带参数）
        ///
        /// 💡 使用示例：
        /// YLogger.Warning("数据库连接池使用率达到 {0}%，建议检查连接泄漏", usagePercent);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void Warning(string message, params object[] args) => YLogEngine.Warning(message, args);

        // ==================== Error 级别 ====================

        /// <summary>
        /// 错误日志 - 记录程序错误和异常
        ///
        /// 🎯 用途：
        /// 用于记录程序运行中的错误，这些错误可能影响功能正常运行。
        /// 错误日志通常需要立即关注和处理。
        ///
        /// 📋 记录内容：
        /// • 业务逻辑执行失败
        /// • 数据访问错误
        /// • 外部服务调用失败
        /// • 系统资源不足导致的错误
        /// • 配置错误导致的功能异常
        ///
        /// 💡 使用示例：
        /// YLogger.Error("数据库连接失败，无法保存用户数据");
        /// </summary>
        /// <param name="message">错误消息</param>
        public static void Error(string message) => YLogEngine.Error(message);

        /// <summary>
        /// 错误日志（带参数）
        ///
        /// 💡 使用示例：
        /// YLogger.Error("用户 {0} 的数据保存失败，错误代码: {1}", userId, errorCode);
        /// </summary>
        /// <param name="message">包含占位符的消息模板</param>
        /// <param name="args">要格式化的参数</param>
        public static void Error(string message, params object[] args) => YLogEngine.Error(message, args);

        /// <summary>
        /// 错误日志（带异常） - 记录异常详细信息
        ///
        /// 🎯 用途：
        /// 除了错误消息外，还会记录完整的异常信息，包括堆栈跟踪。
        /// 这是处理异常时的推荐方式，提供最完整的错误诊断信息。
        ///
        /// 📋 记录内容：
        /// • 自定义的错误描述消息
        /// • 异常类型和消息
        /// • 完整的堆栈跟踪
        /// • 内部异常信息（如果有）
        ///
        /// 💡 使用示例：
        /// try { ... } catch (Exception ex) { YLogger.Error("处理用户请求时发生错误", ex); }
        /// </summary>
        /// <param name="message">错误描述消息</param>
        /// <param name="exception">相关的异常对象</param>
        public static void Error(string message, Exception exception) => YLogEngine.Error(message, exception);

        #endregion

        #region ⚙️ 快速配置

        /// <summary>
        /// 开发模式配置 - 控制台+文件，Debug级别，自动显示配置
        ///
        /// 🎯 配置内容：
        /// • 启用控制台输出：便于实时查看日志
        /// • 启用文件输出：便于历史记录和分析
        /// • 设置Debug级别：输出所有级别的日志
        /// • 自动显示配置：启动时显示各类的日志配置
        ///
        /// 📋 适用场景：
        /// • 本地开发环境
        /// • 功能调试和测试
        /// • 新功能开发阶段
        /// • 问题排查和分析
        ///
        /// 💡 使用示例：
        /// YLogger.ConfigureForDevelopment(); // 在程序启动时调用
        /// </summary>
        public static void ConfigureForDevelopment()
        {
            YLogEngine.Config.EnableConsoleOutput = true; // 启用控制台输出
            YLogEngine.Config.EnableFileOutput = true; // 启用文件输出
            YLogEngine.Config.MinimumLevel = LogLevel.Debug; // 设置最低级别为Debug
            YLoggerInstance.EnableAutoShowConfig(); // 启用自动配置显示
        }

        /// <summary>
        /// 生产模式配置 - 只文件，Warning级别
        ///
        /// 🎯 配置内容：
        /// • 禁用控制台输出：避免影响生产环境性能
        /// • 启用文件输出：保留重要日志用于监控和审计
        /// • 设置Warning级别：只记录警告和错误
        /// • 禁用自动配置显示：减少启动时的输出
        ///
        /// 📋 适用场景：
        /// • 生产环境部署
        /// • 性能敏感的应用
        /// • 长期运行的服务
        /// • 需要日志审计的系统
        ///
        /// 💡 使用示例：
        /// YLogger.ConfigureForProduction(); // 在生产环境启动时调用
        /// </summary>
        public static void ConfigureForProduction()
        {
            YLogEngine.Config.EnableConsoleOutput = false; // 禁用控制台输出
            YLogEngine.Config.EnableFileOutput = true; // 启用文件输出
            YLogEngine.Config.MinimumLevel = LogLevel.Warning; // 设置最低级别为Warning
        }

        /// <summary>
        /// 测试模式配置 - 只控制台，Info级别
        ///
        /// 🎯 配置内容：
        /// • 启用控制台输出：便于测试时实时查看结果
        /// • 禁用文件输出：避免测试时产生大量日志文件
        /// • 设置Info级别：输出重要的业务信息
        /// • 禁用自动配置显示：保持测试输出的简洁
        ///
        /// 📋 适用场景：
        /// • 单元测试和集成测试
        /// • CI/CD 环境
        /// • 临时验证和演示
        /// • 快速功能验证
        ///
        /// 💡 使用示例：
        /// YLogger.ConfigureForTesting(); // 在测试环境启动时调用
        /// </summary>
        public static void ConfigureForTesting()
        {
            YLogEngine.Config.EnableConsoleOutput = true; // 启用控制台输出
            YLogEngine.Config.EnableFileOutput = false; // 禁用文件输出
            YLogEngine.Config.MinimumLevel = LogLevel.Information; // 设置最低级别为Information
        }

        #endregion

        #region 🏷️ 按类日志实例

        // ==================== 基础实例创建方法 ====================

        /// <summary>
        /// 为指定类型创建日志实例 - 最常用的方法
        ///
        /// 🎯 功能说明：
        /// 为指定的类型创建一个独立的日志实例，该实例可以有自己的日志级别设置。
        /// 这是最常用的方法，提供了类级别的精细化日志控制。
        ///
        /// 📋 默认设置：
        /// • 默认最小级别：Warning（只输出警告和错误）
        /// • 自动配置显示：关闭
        /// • 类名：自动从类型获取
        ///
        /// 💡 使用示例：
        /// var logger = YLogger.For&lt;UserService&gt;();
        /// var logger = YLogger.For&lt;UserService&gt;(LogLevel.Debug);
        /// </summary>
        /// <param name="minimumLevel">该类实例的最小日志级别，默认为Warning</param>
        /// <returns>该类的日志实例</returns>
        public static YLoggerInstance For<T>(LogLevel minimumLevel = LogLevel.Warning)
            => new YLoggerInstance(typeof(T).Name, minimumLevel);

        /// <summary>
        /// 为指定类名创建日志实例 - 动态类名版本
        ///
        /// 🎯 功能说明：
        /// 当无法使用泛型时，可以通过字符串指定类名创建日志实例。
        /// 适用于动态创建或者跨程序集的场景。
        ///
        /// 💡 使用示例：
        /// var logger = YLogger.For("UserService");
        /// var logger = YLogger.For("UserService", LogLevel.Debug);
        /// </summary>
        /// <param name="className">类名</param>
        /// <param name="minimumLevel">该类实例的最小日志级别，默认为Warning</param>
        /// <returns>该类的日志实例</returns>
        public static YLoggerInstance For(string className, LogLevel minimumLevel = LogLevel.Warning)
            => new YLoggerInstance(className, minimumLevel);

        // ==================== 环境特定的实例创建方法 ====================

        /// <summary>
        /// 开发模式实例 - 输出详细信息，自动显示配置
        ///
        /// 🎯 功能说明：
        /// 专为开发环境设计的日志实例，默认输出详细的调试信息。
        /// 启用自动配置显示，便于了解当前的日志设置。
        ///
        /// 📋 配置特点：
        /// • 默认最小级别：Debug（输出所有级别的日志）
        /// • 自动配置显示：启用
        /// • 适用场景：开发调试、新功能开发
        ///
        /// 💡 使用示例：
        /// var logger = YLogger.ForDevelopment&lt;UserService&gt;();
        /// </summary>
        /// <param name="minimumLevel">最小日志级别，默认为Debug</param>
        /// <returns>开发模式的日志实例</returns>
        public static YLoggerInstance ForDevelopment<T>(LogLevel minimumLevel = LogLevel.Debug)
            => new YLoggerInstance(typeof(T).Name, minimumLevel, autoShowConfig: true);

        /// <summary>
        /// 生产环境实例 - 只输出错误，无配置显示
        ///
        /// 🎯 功能说明：
        /// 专为生产环境设计的日志实例，只记录严重的错误信息。
        /// 禁用配置显示，减少启动时的输出噪音。
        ///
        /// 📋 配置特点：
        /// • 默认最小级别：Error（只输出错误）
        /// • 自动配置显示：禁用
        /// • 适用场景：生产环境、稳定的功能模块
        ///
        /// 💡 使用示例：
        /// var logger = YLogger.ForProduction&lt;UserService&gt;();
        /// </summary>
        /// <param name="minimumLevel">最小日志级别，默认为Error</param>
        /// <returns>生产环境的日志实例</returns>
        public static YLoggerInstance ForProduction<T>(LogLevel minimumLevel = LogLevel.Error)
            => new YLoggerInstance(typeof(T).Name, minimumLevel, autoShowConfig: false);

        // ==================== 细粒度级别控制方法 ====================

        /// <summary>
        /// 调试模式实例 - 输出所有信息（包括DEBUG）
        ///
        /// 🎯 功能说明：
        /// 最详细的日志模式，输出包括Debug在内的所有级别日志。
        /// 启用自动配置显示，便于调试时了解配置状态。
        ///
        /// 📋 输出级别：Debug、Information、Warning、Error
        /// 💡 使用示例：var logger = YLogger.ForDebug&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForDebug<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.Debug, autoShowConfig: true);

        /// <summary>
        /// 详细模式实例 - 输出详细信息及以上（适合重点调试的类）
        ///
        /// 🎯 功能说明：
        /// 输出详细的业务信息，适合对重点功能模块进行详细跟踪。
        /// 比Debug级别稍微精简，但仍然包含丰富的执行细节。
        ///
        /// 📋 输出级别：InformationDetailed、Information、InformationSimple、Warning、Error
        /// 💡 使用示例：var logger = YLogger.ForDetailed&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForDetailed<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.InformationDetailed, autoShowConfig: false);

        /// <summary>
        /// 一般模式实例 - 输出标准信息及以上（常规信息）
        ///
        /// 🎯 功能说明：
        /// 标准的信息级别，记录重要的业务事件和状态变化。
        /// 这是日常开发中最常用的级别，平衡了信息量和性能。
        ///
        /// 📋 输出级别：Information、InformationSimple、Warning、Error
        /// 💡 使用示例：var logger = YLogger.ForInfo&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForInfo<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.Information, autoShowConfig: false);

        /// <summary>
        /// 简化模式实例 - 输出简化信息及以上（已测试稳定的类）
        ///
        /// 🎯 功能说明：
        /// 只记录最关键的业务节点，适合已经测试稳定的功能模块。
        /// 减少日志噪音，同时保留重要的业务信息。
        ///
        /// 📋 输出级别：InformationSimple、Warning、Error
        /// 💡 使用示例：var logger = YLogger.ForSimple&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForSimple<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.InformationSimple, autoShowConfig: false);

        /// <summary>
        /// 警告模式实例 - 输出警告及以上（默认推荐）
        ///
        /// 🎯 功能说明：
        /// 只记录警告和错误信息，这是推荐的默认级别。
        /// 适合大多数稳定运行的功能模块，平衡了信息量和性能。
        ///
        /// 📋 输出级别：Warning、Error
        /// 💡 使用示例：var logger = YLogger.ForWarning&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForWarning<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.Warning, autoShowConfig: false);

        /// <summary>
        /// 静默模式实例 - 只输出错误（生产环境稳定类）
        ///
        /// 🎯 功能说明：
        /// 最精简的日志模式，只记录严重的错误信息。
        /// 适合生产环境中非常稳定的功能模块，或者性能敏感的场景。
        ///
        /// 📋 输出级别：Error
        /// 💡 使用示例：var logger = YLogger.ForSilent&lt;UserService&gt;();
        /// </summary>
        public static YLoggerInstance ForSilent<T>()
            => new YLoggerInstance(typeof(T).Name, LogLevel.Error, autoShowConfig: false);

        #endregion

        #region 实用工具

        /// <summary>显示当前全局配置</summary>
        public static void ShowCurrentConfig()
        {
            Console.WriteLine("=== YLogger 当前配置 ===");
            Console.WriteLine($"全局强制级别: {_globalForceLevel?.ToString() ?? "无（各类独立控制）"}");
            Console.WriteLine($"控制台输出: {YLogEngine.Config.EnableConsoleOutput}");
            Console.WriteLine($"文件输出: {YLogEngine.Config.EnableFileOutput}");
            Console.WriteLine($"全局最小级别: {YLogEngine.Config.MinimumLevel}");
            Console.WriteLine($"日志目录: {YLogEngine.Config.LogDirectory}");
            Console.WriteLine("========================");
        }

        /// <summary>获取当前日志文件路径</summary>
        public static string GetCurrentLogFilePath()
        {
            var fileName = $"ylog_{DateTime.Now:yyyyMMdd}.log";
            return Path.Combine(YLogEngine.Config.LogDirectory, fileName);
        }

        /// <summary>快速配置 - 静默模式（只输出Error）</summary>
        public static void ConfigureForSilent()
        {
            ForceSilentMode();
            Info("🔇 已配置为静默模式");
        }

        /// <summary>快速配置 - 详细模式（输出所有级别）</summary>
        public static void ConfigureForVerbose()
        {
            ForceDebugMode();
            Info("📢 已配置为详细模式");
        }

        /// <summary>清空所有日志文件</summary>
        public static void ClearAllLogs()
        {
            try
            {
                var logDir = YLogEngine.Config.LogDirectory;
                if (Directory.Exists(logDir))
                {
                    var logFiles = Directory.GetFiles(logDir, "ylog_*.log");
                    foreach (var file in logFiles)
                    {
                        File.Delete(file);
                    }

                    Info($"🗑️ 已清空 {logFiles.Length} 个日志文件");
                }
            }
            catch (Exception ex)
            {
                Error($"清空日志文件失败: {ex.Message}");
            }
        }

        /// <summary>临时启用详细日志（自动恢复）</summary>
        public static IDisposable TemporaryVerbose()
        {
            return new TemporaryLogLevel(LogLevel.Debug);
        }

        /// <summary>临时启用静默模式（自动恢复）</summary>
        public static IDisposable TemporarySilent()
        {
            return new TemporaryLogLevel(LogLevel.Error);
        }

        /// <summary>临时启用指定级别（自动恢复）</summary>
        public static IDisposable TemporaryLevel(LogLevel level)
        {
            return new TemporaryLogLevel(level);
        }

        /// <summary>监控方法执行时间</summary>
        public static IDisposable MonitorPerformance(string operationName, LogLevel level = LogLevel.Information)
        {
            return new PerformanceMonitor(operationName, level);
        }

        /// <summary>批量操作时临时降低日志级别</summary>
        public static IDisposable BatchOperation(string batchName, int itemCount)
        {
            Info($"🔄 开始批量操作: {batchName} ({itemCount} 项)");
            return new BatchOperationScope(batchName, itemCount);
        }

        /// <summary>获取日志统计信息</summary>
        public static LogStatistics GetStatistics()
        {
            return YLogEngine.GetStatistics();
        }

        /// <summary>重置日志统计</summary>
        public static void ResetStatistics()
        {
            YLogEngine.ResetStatistics();
        }

        /// <summary>检查日志系统健康状态</summary>
        public static LogHealthStatus CheckHealth()
        {
            var stats = GetStatistics();
            var errorRate = stats.TotalLogs > 0 ? (double)stats.ErrorCount / stats.TotalLogs : 0;

            return new LogHealthStatus
            {
                IsHealthy = errorRate < 0.1, // 错误率低于10%认为健康
                ErrorRate = errorRate,
                TotalLogs = stats.TotalLogs,
                LastLogTime = stats.LastLogTime,
                UptimeMinutes = (DateTime.Now - stats.StartTime).TotalMinutes
            };
        }

        #endregion
    }












}