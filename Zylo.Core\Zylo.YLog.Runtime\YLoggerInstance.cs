using System.Runtime.CompilerServices;

namespace Zylo.YLog.Runtime;

    /// <summary>
    /// 按类控制的日志实例
    /// 🔥 每个类可以有独立的日志级别控制，但输出统一管理
    /// </summary>
    public class YLoggerInstance
    {
        private readonly string _className;
        private LogLevel _minimumLevel;
        private static readonly List<YLoggerInstance> _allInstances = new List<YLoggerInstance>();
        private static bool _autoShowConfigEnabled = false;
        private static bool _hasShownAutoConfig = false;

        #region 构造和配置

        public YLoggerInstance(string className, LogLevel minimumLevel = LogLevel.Information,
            bool autoShowConfig = false)
        {
            _className = className;
            _minimumLevel = minimumLevel;

            lock (_allInstances)
            {
                _allInstances.Add(this);
                CheckAndAutoShowConfig();
            }

            if (autoShowConfig)
            {
                ShowInstanceConfig();
            }
        }

        /// <summary>设置该类的最小日志级别</summary>
        public void SetMinimumLevel(LogLevel level) => _minimumLevel = level;

        /// <summary>获取该类的最小日志级别</summary>
        public LogLevel GetMinimumLevel() => _minimumLevel;

        /// <summary>判断是否应该记录该级别的日志（考虑全局强制级别）</summary>
        private bool ShouldLog(LogLevel level) => YLogger.ShouldLogGlobally(level, _minimumLevel);

        #endregion

        #region 📝 实例日志方法

        // ==================== Debug 级别 ====================

        /// <summary>
        /// 调试日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录调试信息，自动在消息前添加 [类名.方法名] 前缀。
        /// 方法名通过 CallerMemberName 自动获取，无需手动传入。
        ///
        /// 💡 使用示例：
        /// logger.Debug("用户验证通过"); // 输出: [UserService.ValidateUser] 用户验证通过
        /// </summary>
        /// <param name="message">调试消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void Debug(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.Debug)) return; // 检查是否应该记录该级别的日志
            YLogEngine.Debug($"[{_className}.{methodName}] {message}"); // 添加类名和方法名前缀
        }

        // ==================== Information 级别（三个子级别）====================

        /// <summary>
        /// 详细信息日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录详细的业务信息，包含完整的执行细节。
        /// 适用于重点调试的功能模块或复杂业务流程的跟踪。
        ///
        /// 💡 使用示例：
        /// logger.InfoDetailed("开始处理订单，验证商品和库存");
        /// // 输出: [OrderService.ProcessOrder] 开始处理订单，验证商品和库存
        /// </summary>
        /// <param name="message">详细信息消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void InfoDetailed(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.InformationDetailed)) return;
            YLogEngine.InfoDetailed($"[{_className}.{methodName}] {message}");
        }

        /// <summary>
        /// 一般信息日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录重要的业务事件和状态变化。
        /// 这是最常用的信息级别，平衡了信息量和可读性。
        ///
        /// 💡 使用示例：
        /// logger.Info("用户登录成功");
        /// // 输出: [UserService.Login] 用户登录成功
        /// </summary>
        /// <param name="message">信息消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void Info(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.Information)) return;
            YLogEngine.Info($"[{_className}.{methodName}] {message}");
        }

        /// <summary>
        /// 简化信息日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 只记录最关键的业务节点，减少日志噪音。
        /// 适用于已经测试稳定的功能模块。
        ///
        /// 💡 使用示例：
        /// logger.InfoSimple("订单处理完成");
        /// // 输出: [OrderService.ProcessOrder] 订单处理完成
        /// </summary>
        /// <param name="message">简化信息消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void InfoSimple(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.InformationSimple)) return;
            YLogEngine.InfoSimple($"[{_className}.{methodName}] {message}");
        }

        // ==================== Warning 级别 ====================

        /// <summary>
        /// 警告日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录潜在问题或异常情况，这些问题不会导致程序崩溃但需要关注。
        ///
        /// 💡 使用示例：
        /// logger.Warning("配置文件缺失，使用默认配置");
        /// // 输出: [ConfigService.LoadConfig] 配置文件缺失，使用默认配置
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void Warning(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.Warning)) return;
            YLogEngine.Warning($"[{_className}.{methodName}] {message}");
        }

        // ==================== Error 级别 ====================

        /// <summary>
        /// 错误日志 - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录程序运行中的错误，这些错误可能影响功能正常运行。
        ///
        /// 💡 使用示例：
        /// logger.Error("数据库连接失败");
        /// // 输出: [DataService.Connect] 数据库连接失败
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void Error(string message, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.Error)) return;
            YLogEngine.Error($"[{_className}.{methodName}] {message}");
        }

        /// <summary>
        /// 错误日志（带异常） - 自动包含类名和方法名
        ///
        /// 🎯 功能说明：
        /// 记录错误消息和完整的异常信息，包括堆栈跟踪。
        /// 这是处理异常时的推荐方式，提供最完整的错误诊断信息。
        ///
        /// 💡 使用示例：
        /// try { ... } catch (Exception ex) { logger.Error("处理用户请求失败", ex); }
        /// // 输出: [UserService.ProcessRequest] 处理用户请求失败 + 异常详情
        /// </summary>
        /// <param name="message">错误描述消息</param>
        /// <param name="exception">相关的异常对象</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void Error(string message, Exception exception, [CallerMemberName] string methodName = "")
        {
            if (!ShouldLog(LogLevel.Error)) return;
            YLogEngine.Error($"[{_className}.{methodName}] {message}", exception);
        }

        #endregion

        #region 🔄 自动执行包装

        /// <summary>
        /// 自动执行包装（有返回值） - 自动记录执行过程和性能
        ///
        /// 🎯 功能说明：
        /// 包装方法执行，自动记录开始、结束、执行时间和返回值。
        /// 如果发生异常，会自动记录异常信息并重新抛出。
        ///
        /// 📋 自动记录内容：
        /// • 执行开始：记录方法名和参数
        /// • 执行结束：记录方法名、返回值和执行时间
        /// • 异常情况：记录异常信息和执行时间
        ///
        /// 🔧 适用场景：
        /// • 需要监控执行时间的关键方法
        /// • 需要记录输入输出的业务方法
        /// • 需要统一异常处理的方法
        /// • 性能敏感的方法调用
        ///
        /// 💡 使用示例：
        /// var result = logger.Execute("ProcessOrder", () => ProcessOrderInternal(orderId), orderId);
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="methodName">方法名称（用于日志显示）</param>
        /// <param name="action">要执行的方法</param>
        /// <param name="parameters">方法参数（用于日志显示）</param>
        /// <returns>方法执行的返回值</returns>
        public T Execute<T>(string methodName, Func<T> action, params object[] parameters)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew(); // 开始计时
            try
            {
                // 记录执行开始，包含方法名和参数
                Info($"▶▶▶ 开始执行 {methodName}({string.Join(", ", parameters)})");

                var result = action(); // 执行实际方法
                stopwatch.Stop(); // 停止计时

                // 记录执行结束，包含返回值和执行时间
                Info($"◀◀◀ 结束执行 {methodName} => {result} [{stopwatch.ElapsedMilliseconds}ms]");
                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop(); // 停止计时
                // 记录异常信息和执行时间，然后重新抛出异常
                Error($"方法 {methodName} 执行异常 [{stopwatch.ElapsedMilliseconds}ms]", ex);
                throw; // 重新抛出异常，保持原有的异常处理流程
            }
        }

        /// <summary>
        /// 自动执行包装（无返回值） - 自动记录执行过程和性能
        ///
        /// 🎯 功能说明：
        /// 包装无返回值的方法执行，自动记录开始、结束和执行时间。
        /// 内部调用有返回值的版本，返回值设为null。
        ///
        /// 💡 使用示例：
        /// logger.Execute("SendEmail", () => SendEmailInternal(email), email);
        /// </summary>
        /// <param name="methodName">方法名称（用于日志显示）</param>
        /// <param name="action">要执行的方法</param>
        /// <param name="parameters">方法参数（用于日志显示）</param>
        public void Execute(string methodName, Action action, params object[] parameters)
        {
            // 调用有返回值的版本，将Action包装为Func<object>
            Execute<object?>(methodName, () =>
            {
                action(); // 执行实际的Action
                return null; // 返回null作为占位符
            }, parameters);
        }

        /// <summary>
        /// 监控方法执行时间 - 实例版本
        ///
        /// 🎯 功能说明：
        /// 创建一个性能监控器，使用using语句自动记录代码块的执行时间。
        /// 监控器会在创建时记录开始时间，在Dispose时记录结束时间和总耗时。
        ///
        /// 📋 自动记录内容：
        /// • 开始时：记录操作开始
        /// • 结束时：记录操作完成和执行时间，并根据耗时显示不同图标
        ///
        /// 🔧 图标说明：
        /// • ⚡ 小于100ms：很快
        /// • 🏃 小于1000ms：正常
        /// • 🚶 小于5000ms：较慢
        /// • 🐌 大于等于5000ms：很慢
        ///
        /// 💡 使用示例：
        /// using (logger.Monitor("数据库查询"))
        /// {
        ///     // 执行数据库操作
        /// } // 自动记录执行时间
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>性能监控器，需要使用using语句</returns>
        public IDisposable Monitor(string operationName)
        {
            return new InstancePerformanceMonitor(this, operationName);
        }

        #endregion

        #region 🎯 条件和上下文日志方法

        /// <summary>
        /// 条件信息日志 - 满足条件时才记录
        ///
        /// 🎯 功能说明：
        /// 只有当指定条件为true时才记录信息日志。
        /// 避免在代码中写大量的if语句来控制日志输出。
        ///
        /// 💡 使用示例：
        /// logger.InfoIf(user.IsVip, "VIP用户登录"); // 只有VIP用户才记录
        /// </summary>
        /// <param name="condition">记录日志的条件</param>
        /// <param name="message">日志消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void InfoIf(bool condition, string message, [CallerMemberName] string methodName = "")
        {
            if (condition) Info(message, methodName);
        }

        /// <summary>
        /// 条件调试日志 - 满足条件时才记录
        ///
        /// 🎯 功能说明：
        /// 只有当指定条件为true时才记录调试日志。
        /// 适用于需要根据特定条件输出调试信息的场景。
        ///
        /// 💡 使用示例：
        /// logger.DebugIf(isDebugMode, "详细的调试信息"); // 只在调试模式下记录
        /// </summary>
        /// <param name="condition">记录日志的条件</param>
        /// <param name="message">日志消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void DebugIf(bool condition, string message, [CallerMemberName] string methodName = "")
        {
            if (condition) Debug(message, methodName);
        }

        /// <summary>
        /// 条件警告日志 - 满足条件时才记录
        ///
        /// 🎯 功能说明：
        /// 只有当指定条件为true时才记录警告日志。
        /// 适用于需要根据特定条件输出警告信息的场景。
        ///
        /// 💡 使用示例：
        /// logger.WarningIf(memoryUsage > 80, "内存使用率过高"); // 内存使用率超过80%时警告
        /// </summary>
        /// <param name="condition">记录日志的条件</param>
        /// <param name="message">日志消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void WarningIf(bool condition, string message, [CallerMemberName] string methodName = "")
        {
            if (condition) Warning(message, methodName);
        }

        /// <summary>
        /// 带上下文的信息日志 - 自动附加上下文信息
        ///
        /// 🎯 功能说明：
        /// 记录信息日志时自动附加上下文对象的信息。
        /// 如果上下文对象不为null，会在消息后添加上下文信息。
        ///
        /// 💡 使用示例：
        /// logger.InfoWithContext("用户操作", new { UserId = 123, Action = "Login" });
        /// // 输出: 用户操作 | 上下文: { UserId = 123, Action = Login }
        /// </summary>
        /// <param name="message">主要日志消息</param>
        /// <param name="context">上下文对象</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void InfoWithContext(string message, object? context, [CallerMemberName] string methodName = "")
        {
            var contextStr = context != null ? $" | 上下文: {context}" : "";
            Info($"{message}{contextStr}", methodName);
        }

        /// <summary>
        /// 计数器字典 - 用于跟踪每个方法的调用次数
        /// 键格式：类名.方法名
        /// 值：调用次数
        /// </summary>
        private static readonly Dictionary<string, int> _counters = new();

        /// <summary>
        /// 每N次执行一次日志 - 减少高频操作的日志噪音
        ///
        /// 🎯 功能说明：
        /// 只在每N次调用时记录一次日志，用于减少高频操作产生的日志噪音。
        /// 每个方法都有独立的计数器，基于类名和方法名进行区分。
        ///
        /// 🔧 适用场景：
        /// • 循环处理中的进度报告
        /// • 高频API调用的监控
        /// • 批量数据处理的状态更新
        /// • 定期心跳检查的记录
        ///
        /// 💡 使用示例：
        /// logger.InfoEvery(100, "已处理数据"); // 每100次调用记录一次
        /// // 输出: 已处理数据 (第100次)、已处理数据 (第200次)...
        /// </summary>
        /// <param name="interval">记录间隔（每N次记录一次）</param>
        /// <param name="message">日志消息</param>
        /// <param name="methodName">调用方法名（自动获取）</param>
        public void InfoEvery(int interval, string message, [CallerMemberName] string methodName = "")
        {
            var key = $"{_className}.{methodName}"; // 生成唯一的计数器键
            lock (_counters) // 线程安全的计数器操作
            {
                _counters[key] = _counters.GetValueOrDefault(key, 0) + 1; // 增加计数
                if (_counters[key] % interval == 0) // 检查是否到达记录间隔
                {
                    Info($"{message} (第{_counters[key]}次)", methodName);
                }
            }
        }

        #endregion


        #region 配置显示

        public void ShowInstanceConfig()
        {
            Console.WriteLine($"=== {_className} 日志配置 ===");
            Console.WriteLine($"类名: {_className}");
            Console.WriteLine($"最小级别: {_minimumLevel}");
            Console.WriteLine($"全局强制: {YLogger.GetGlobalForceLevel()?.ToString() ?? "无"}");
            Console.WriteLine("========================");
        }

        public static void EnableAutoShowConfig() => _autoShowConfigEnabled = true;
        public static void DisableAutoShowConfig() => _autoShowConfigEnabled = false;

        private static void CheckAndAutoShowConfig()
        {
            if (_autoShowConfigEnabled && !_hasShownAutoConfig)
            {
                _hasShownAutoConfig = true;
                ShowAllInstancesConfig();
            }
        }

        public static void ShowAllInstancesConfig()
        {
            Console.WriteLine("📋 各类的日志级别配置：");
            Console.WriteLine(new string('-', 50));
            lock (_allInstances)
            {
                foreach (var instance in _allInstances)
                {
                    instance.ShowInstanceConfig();
                }
            }
        }

        #endregion
    }