﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
    <PackageId>Zylo.YLog.Runtime</PackageId>
    <Product>Zylo Log Runtime</Product>

    <!-- NuGet 包信息 -->
    <Description>🚀 Zylo.YLog.Runtime - YLog 运行时引擎。提供统一的日志记录和管理功能，支持多种日志级别、格式化输出和配置管理。</Description>
    <PackageTags>logging;runtime;engine;management;zylo</PackageTags>
    <PackageProjectUrl>https://github.com/zylo/zylo-ylog</PackageProjectUrl>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

    <!-- 发布说明 -->
    <PackageReleaseNotes>
      🎉 v1.3.2 - 功能增强与稳定性提升：
      - 🔧 统一的日志记录引擎
      - 📊 多种日志级别支持
      - 🎯 格式化输出功能
      - ⚙️ 灵活的配置管理
      - 🚀 高性能日志处理
    </PackageReleaseNotes>
  </PropertyGroup>

  <ItemGroup>
    <None Update="README.md">
      <Pack>true</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

</Project>