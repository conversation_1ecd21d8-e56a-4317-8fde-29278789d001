using System;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Demo.Examples
{
    /// <summary>
    /// 基础字符匹配演示
    /// 展示 Zylo.YRegex 的基础字符匹配功能
    /// </summary>
    public static class BasicCharacterDemo
    {
        public static void Run()
        {
            Console.WriteLine("演示基础字符匹配功能...\n");

            DigitDemo();
            LetterDemo();
            AlphaNumericDemo();
            WhitespaceDemo();
            SpecialCharacterDemo();
            LiteralDemo();
        }

        /// <summary>
        /// 数字字符演示
        /// </summary>
        private static void DigitDemo()
        {
            Console.WriteLine("🔢 数字字符演示");
            Console.WriteLine("================");

            // 单个数字
            var singleDigit = YRegexBuilder.Create()
                .Digit("单个数字")
                .Build();

            // 3位数字
            var threeDigits = YRegexBuilder.Create()
                .Digits(3, "三位数字")
                .Build();

            // 3-5位数字
            var rangeDigits = YRegexBuilder.Create()
                .Digits(3, 5, "3到5位数字")
                .Build();

            var testCases = new[]
            {
                ("5", singleDigit, "单个数字"),
                ("a", singleDigit, "单个数字"),
                ("123", threeDigits, "三位数字"),
                ("12", threeDigits, "三位数字"),
                ("1234", rangeDigits, "3-5位数字"),
                ("12", rangeDigits, "3-5位数字")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"描述: {singleDigit.Description}");
            Console.WriteLine($"模式: {singleDigit.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 字母字符演示
        /// </summary>
        private static void LetterDemo()
        {
            Console.WriteLine("🔤 字母字符演示");
            Console.WriteLine("================");

            // 单个字母
            var singleLetter = YRegexBuilder.Create()
                .Letter("单个字母")
                .Build();

            // 大写字母
            var upperCase = YRegexBuilder.Create()
                .CharacterRange('A', 'Z', "大写字母")
                .Build();

            // 小写字母
            var lowerCase = YRegexBuilder.Create()
                .CharacterRange('a', 'z', "小写字母")
                .Build();

            // 5个字母
            var fiveLetters = YRegexBuilder.Create()
                .Letters(5, "五个字母")
                .Build();

            var testCases = new[]
            {
                ("A", singleLetter, "单个字母"),
                ("5", singleLetter, "单个字母"),
                ("A", upperCase, "大写字母"),
                ("a", upperCase, "大写字母"),
                ("a", lowerCase, "小写字母"),
                ("A", lowerCase, "小写字母"),
                ("Hello", fiveLetters, "五个字母"),
                ("Hi", fiveLetters, "五个字母")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"描述: {singleLetter.Description}");
            Console.WriteLine($"模式: {singleLetter.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 字母数字字符演示
        /// </summary>
        private static void AlphaNumericDemo()
        {
            Console.WriteLine("🔠 字母数字字符演示");
            Console.WriteLine("==================");

            // 字母数字字符
            var alphaNumeric = YRegexBuilder.Create()
                .AlphaNumeric("字母数字字符")
                .Build();

            // 单词字符（包含下划线）
            var wordChar = YRegexBuilder.Create()
                .CharacterSet("a-zA-Z0-9_", "单词字符")
                .Build();

            // 6位字母数字
            var sixAlphaNum = YRegexBuilder.Create()
                .AlphaNumeric(6, "六位字母数字")
                .Build();

            var testCases = new[]
            {
                ("A", alphaNumeric, "字母数字"),
                ("5", alphaNumeric, "字母数字"),
                ("@", alphaNumeric, "字母数字"),
                ("_", wordChar, "单词字符"),
                ("@", wordChar, "单词字符"),
                ("abc123", sixAlphaNum, "六位字母数字"),
                ("abc12", sixAlphaNum, "六位字母数字")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"描述: {alphaNumeric.Description}");
            Console.WriteLine($"模式: {alphaNumeric.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 空白字符演示
        /// </summary>
        private static void WhitespaceDemo()
        {
            Console.WriteLine("⬜ 空白字符演示");
            Console.WriteLine("================");

            // 空白字符
            var whitespace = YRegexBuilder.Create()
                .Whitespace("空白字符")
                .Build();

            // 空格
            var space = YRegexBuilder.Create()
                .Space("空格")
                .Build();

            // 制表符
            var tab = YRegexBuilder.Create()
                .Tab("制表符")
                .Build();

            var testCases = new[]
            {
                (" ", whitespace, "空白字符"),
                ("\t", whitespace, "空白字符"),
                ("a", whitespace, "空白字符"),
                (" ", space, "空格"),
                ("\t", space, "空格"),
                ("\t", tab, "制表符"),
                (" ", tab, "制表符")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                var displayInput = input == " " ? "空格" : input == "\t" ? "制表符" : input;
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{displayInput}' -> {result}");
            }

            Console.WriteLine($"描述: {whitespace.Description}");
            Console.WriteLine($"模式: {whitespace.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 特殊字符演示
        /// </summary>
        private static void SpecialCharacterDemo()
        {
            Console.WriteLine("🔣 特殊字符演示");
            Console.WriteLine("================");

            // 任意字符
            var anyChar = YRegexBuilder.Create()
                .CharacterSet(".", "任意字符")
                .Build();

            // 点号
            var dot = YRegexBuilder.Create()
                .Dot("点号")
                .Build();

            // @符号
            var at = YRegexBuilder.Create()
                .At("@符号")
                .Build();

            var testCases = new[]
            {
                ("a", anyChar, "任意字符"),
                ("5", anyChar, "任意字符"),
                ("@", anyChar, "任意字符"),
                (".", dot, "点号"),
                ("a", dot, "点号"),
                ("@", at, "@符号"),
                ("a", at, "@符号")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"描述: {anyChar.Description}");
            Console.WriteLine($"模式: {anyChar.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 字面量演示
        /// </summary>
        private static void LiteralDemo()
        {
            Console.WriteLine("📝 字面量演示");
            Console.WriteLine("==============");

            // 字面量文本
            var hello = YRegexBuilder.Create()
                .Literal("Hello", "Hello文本")
                .Build();

            // 复杂字面量
            var email = YRegexBuilder.Create()
                .Literal("user", "用户名")
                .At("@符号")
                .Literal("example.com", "域名")
                .Build();

            var testCases = new[]
            {
                ("Hello", hello, "Hello文本"),
                ("hello", hello, "Hello文本"),
                ("Hi", hello, "Hello文本"),
                ("<EMAIL>", email, "邮箱格式"),
                ("<EMAIL>", email, "邮箱格式"),
                ("<EMAIL>", email, "邮箱格式")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"描述: {hello.Description}");
            Console.WriteLine($"模式: {hello.Pattern}");
            Console.WriteLine();
        }
    }
}
