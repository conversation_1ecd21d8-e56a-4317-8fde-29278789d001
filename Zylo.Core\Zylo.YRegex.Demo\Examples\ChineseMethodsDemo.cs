using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Demo.Examples;

/// <summary>
/// 中文方法演示
/// 展示 RegZ/RegV/RegQ/RegG 前缀的中文方法使用
/// </summary>
public static class ChineseMethodsDemo
{
    /// <summary>
    /// 运行中文方法演示
    /// </summary>
    public static void Run()
    {
        Console.WriteLine("🇨🇳 中文方法演示");
        Console.WriteLine("==================");
        Console.WriteLine("展示 RegZ/RegV/RegQ/RegG 前缀的中文方法使用");
        Console.WriteLine();

        DemoRegZMethods();
        DemoRegVMethods();
        DemoRegQMethods();
        DemoRegGMethods();

        Console.WriteLine("💡 中文方法让正则表达式更易读易懂！");
    }

    /// <summary>
    /// 演示 RegZ 字符方法
    /// </summary>
    private static void DemoRegZMethods()
    {
        Console.WriteLine("🔤 RegZ 字符方法演示");
        Console.WriteLine("====================");

        // 使用中文方法构建验证器
        var phoneValidator = YRegexBuilder.Create()
            .RegZ数字(3)           // 3位数字
            .RegZ连字符()          // 连字符
            .RegZ数字(4)           // 4位数字
            .RegZ连字符()          // 连字符
            .RegZ数字(4)           // 4位数字
            .Build();

        Console.WriteLine("📱 电话号码格式验证 (使用中文方法):");
        Console.WriteLine($"模式: {phoneValidator.Pattern}");
        Console.WriteLine($"描述: {phoneValidator.Description}");

        var testPhones = new[] { "123-4567-8901", "abc-defg-hijk", "123-456-789" };
        foreach (var phone in testPhones)
        {
            var result = phoneValidator.IsMatch(phone);
            Console.WriteLine($"{(result ? "✅" : "❌")} {phone} -> {result}");
        }
        Console.WriteLine();

        // 字母数字组合
        var usernameValidator = YRegexBuilder.Create()
            .RegZ字母()            // 必须以字母开头
            .RegZ字母数字(2, 19)   // 2-19个字母数字字符
            .Build();

        Console.WriteLine("👤 用户名验证 (使用中文方法):");
        Console.WriteLine($"模式: {usernameValidator.Pattern}");
        Console.WriteLine($"描述: {usernameValidator.Description}");

        var testUsernames = new[] { "user123", "123user", "a", "validUser", "user_name" };
        foreach (var username in testUsernames)
        {
            var result = usernameValidator.IsMatch(username);
            Console.WriteLine($"{(result ? "✅" : "❌")} {username} -> {result}");
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 演示 RegV 验证方法
    /// </summary>
    private static void DemoRegVMethods()
    {
        Console.WriteLine("✅ RegV 验证方法演示");
        Console.WriteLine("====================");

        // 邮箱验证
        var emailValidator = YRegexBuilder.Create()
            .RegV邮箱(true, "严格邮箱验证")
            .Build();

        Console.WriteLine("📧 邮箱验证 (使用中文方法):");
        Console.WriteLine($"描述: {emailValidator.Description}");

        var testEmails = new[] { "<EMAIL>", "<EMAIL>", "invalid.email", "user@" };
        foreach (var email in testEmails)
        {
            var result = emailValidator.IsMatch(email);
            Console.WriteLine($"{(result ? "✅" : "❌")} {email} -> {result}");
        }
        Console.WriteLine();

        // 手机号验证
        var phoneValidator = YRegexBuilder.Create()
            .RegV手机号("china", "中国手机号验证")
            .Build();

        Console.WriteLine("📱 手机号验证 (使用中文方法):");
        Console.WriteLine($"描述: {phoneValidator.Description}");

        var testPhones = new[] { "13812345678", "15987654321", "12345678901", "+86-138-1234-5678" };
        foreach (var phone in testPhones)
        {
            var result = phoneValidator.IsMatch(phone);
            Console.WriteLine($"{(result ? "✅" : "❌")} {phone} -> {result}");
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 演示 RegQ 量词方法
    /// </summary>
    private static void DemoRegQMethods()
    {
        Console.WriteLine("🔢 RegQ 量词方法演示");
        Console.WriteLine("====================");

        // 使用中文量词方法
        var passwordValidator = YRegexBuilder.Create()
            .RegZ字母数字()
            .RegQ至少次数(8, "至少8位")
            .Build();

        Console.WriteLine("🔐 密码长度验证 (使用中文方法):");
        Console.WriteLine($"模式: {passwordValidator.Pattern}");
        Console.WriteLine($"描述: {passwordValidator.Description}");

        var testPasswords = new[] { "abc123", "password123", "verylongpassword", "short" };
        foreach (var password in testPasswords)
        {
            var result = passwordValidator.IsMatch(password);
            Console.WriteLine($"{(result ? "✅" : "❌")} {password} -> {result}");
        }
        Console.WriteLine();

        // 可选内容
        var urlValidator = YRegexBuilder.Create()
            .RegZ文本("http")
            .RegZ文本("s")
            .RegQ可选("HTTPS可选")
            .RegZ文本("://")
            .RegZ字母数字()
            .RegQ一次或多次("域名")
            .Build();

        Console.WriteLine("🌐 URL验证 (使用中文方法):");
        Console.WriteLine($"模式: {urlValidator.Pattern}");
        Console.WriteLine($"描述: {urlValidator.Description}");

        var testUrls = new[] { "http://example.com", "https://example.com", "ftp://example.com" };
        foreach (var url in testUrls)
        {
            var result = urlValidator.IsMatch(url);
            Console.WriteLine($"{(result ? "✅" : "❌")} {url} -> {result}");
        }
        Console.WriteLine();
    }

    /// <summary>
    /// 演示 RegG 分组方法
    /// </summary>
    private static void DemoRegGMethods()
    {
        Console.WriteLine("🔗 RegG 分组方法演示");
        Console.WriteLine("====================");

        // 平衡组演示
        var bracketValidator = YRegexBuilder.Create()
            .RegG平衡组('(', ')', "平衡括号")
            .Build();

        Console.WriteLine("🔗 平衡括号验证 (使用中文方法):");
        Console.WriteLine($"描述: {bracketValidator.Description}");

        var testBrackets = new[] { "()", "(())", "((()))", "(()", "())" };
        foreach (var bracket in testBrackets)
        {
            try
            {
                var result = bracketValidator.IsMatch(bracket);
                Console.WriteLine($"{(result ? "✅" : "❌")} {bracket} -> {result}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ {bracket} -> 错误: {ex.Message}");
            }
        }
        Console.WriteLine();

        Console.WriteLine("💡 RegG 方法提供了强大的分组和引用功能！");
        Console.WriteLine();
    }
}
