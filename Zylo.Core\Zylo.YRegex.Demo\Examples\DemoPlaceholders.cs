using System;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Demo.Examples
{
    /// <summary>
    /// 字符类和范围演示
    /// </summary>
    public static class CharacterClassDemo
    {
        public static void Run()
        {
            Console.WriteLine("演示字符类和范围功能...");
            Console.WriteLine("功能包括：字符集合、否定字符集、字符范围等");
            Console.WriteLine("详细实现请参考完整版本");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 分组和断言演示
    /// </summary>
    public static class GroupingDemo
    {
        public static void Run()
        {
            Console.WriteLine("演示分组和断言功能...");
            Console.WriteLine("功能包括：捕获组、命名组、前瞻后顾断言等");
            Console.WriteLine("详细实现请参考完整版本");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 专业领域验证演示
    /// </summary>
    public static class ProfessionalValidationDemo
    {
        public static void Run()
        {
            Console.WriteLine("🏛️ 专业领域验证演示");
            Console.WriteLine("====================");

            // ISBN验证
            var isbn = YRegexBuilder.Create()
                .ISBN("isbn13", "ISBN-13")
                .Build();

            // DOI验证
            var doi = YRegexBuilder.Create()
                .DOI("DOI标识符")
                .Build();

            // IBAN验证
            var iban = YRegexBuilder.Create()
                .IBAN("DE", "德国IBAN")
                .Build();

            var testData = new[]
            {
                ("ISBN", "978-3-16-148410-0", isbn),
                ("DOI", "10.1000/182", doi),
                ("IBAN", "**********************", iban)
            };

            foreach (var (type, value, validator) in testData)
            {
                var result = validator.IsMatch(value);
                Console.WriteLine($"{(result ? "✅" : "❌")} {type}: {value}");
            }

            Console.WriteLine("\n💡 Zylo.YRegex 支持 60+ 专业领域验证！");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 中文内容验证演示
    /// </summary>
    public static class ChineseValidationDemo
    {
        public static void Run()
        {
            Console.WriteLine("🇨🇳 中文内容验证演示");
            Console.WriteLine("====================");

            // 中文字符验证
            var chinese = YRegexBuilder.Create()
                .ChineseCharacters("中文字符")
                .OneOrMore("一个或多个")
                .Build();

            // 中文姓名验证
            var chineseName = YRegexBuilder.Create()
                .StartOfString("开始")
                .ChineseCharacters("中文字符")
                .Between(2, 4, "2-4个字符")
                .EndOfString("结束")
                .Build();

            var testData = new[]
            {
                ("中文字符", "你好世界", chinese),
                ("中文字符", "Hello世界", chinese),
                ("中文姓名", "张三", chineseName),
                ("中文姓名", "欧阳修", chineseName)
            };

            foreach (var (type, value, validator) in testData)
            {
                var result = validator.IsMatch(value);
                Console.WriteLine($"{(result ? "✅" : "❌")} {type}: {value}");
            }

            Console.WriteLine("\n💡 完整支持中文、日文、韩文等多语言字符！");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 网站示例对比演示
    /// </summary>
    public static class WebsiteExamplesDemo
    {
        public static void Run()
        {
            Console.WriteLine("🌐 网站示例对比演示");
            Console.WriteLine("====================");

            Console.WriteLine("📝 博客园常用正则表达式 - 100% 支持");
            Console.WriteLine("🔧 11meigui工具正则表达式 - 100% 支持");
            Console.WriteLine("🎓 菜鸟教程正则表达式 - 100% 支持");
            Console.WriteLine();

            // 展示一个对比示例
            Console.WriteLine("传统正则 vs Zylo.YRegex 对比：");
            Console.WriteLine("传统: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

            var email = YRegexBuilder.Create()
                .QuickEmail(true, "严格邮箱验证")
                .Build();

            Console.WriteLine($"Zylo: {email.Description}");
            Console.WriteLine($"模式: {email.Pattern}");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 实际应用场景演示
    /// </summary>
    public static class RealWorldDemo
    {
        public static void Run()
        {
            Console.WriteLine("🎯 实际应用场景演示");
            Console.WriteLine("====================");

            Console.WriteLine("1. 用户注册表单验证");
            Console.WriteLine("2. 数据清洗和提取");
            Console.WriteLine("3. 配置文件解析");
            Console.WriteLine("4. 日志分析");
            Console.WriteLine("5. API 参数验证");
            Console.WriteLine();

            Console.WriteLine("💡 详细实现请参考完整版本或文档");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 性能对比演示
    /// </summary>
    public static class PerformanceDemo
    {
        public static void Run()
        {
            Console.WriteLine("📊 性能对比演示");
            Console.WriteLine("================");

            Console.WriteLine("🚀 Zylo.YRegex 性能特点：");
            Console.WriteLine("✅ 编译优化 - 使用 RegexOptions.Compiled");
            Console.WriteLine("✅ 智能缓存 - 自动缓存常用模式");
            Console.WriteLine("✅ 超时保护 - 防止复杂模式导致性能问题");
            Console.WriteLine("✅ 文化无关 - 确保跨文化一致性");
            Console.WriteLine();

            Console.WriteLine("💡 详细性能测试请参考完整版本");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// API 完整功能演示
    /// </summary>
    public static class CompleteAPIDemo
    {
        public static void Run()
        {
            Console.WriteLine("📚 API 完整功能演示");
            Console.WriteLine("====================");

            Console.WriteLine("🔤 基础字符方法: 30+ 个");
            Console.WriteLine("🔢 量词方法: 10+ 个");
            Console.WriteLine("🔗 分组和断言: 15+ 个");
            Console.WriteLine("🚀 快捷验证方法: 30+ 个");
            Console.WriteLine("🏛️ 专业领域验证: 60+ 个");
            Console.WriteLine("📄 文档格式验证: 15+ 个");
            Console.WriteLine("🌍 Unicode 和国际化: 20+ 个");
            Console.WriteLine("⚙️ 配置方法: 10+ 个");
            Console.WriteLine();

            Console.WriteLine("总计: 200+ 个 API 方法！");
            Console.WriteLine("💡 详细API文档请参考 Documentation/Complete_API_Reference.md");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 交互式验证器构建演示
    /// </summary>
    public static class InteractiveBuilderDemo
    {
        public static void Run()
        {
            Console.WriteLine("🛠️ 交互式验证器构建演示");
            Console.WriteLine("========================");

            Console.WriteLine("这是一个交互式功能，允许用户：");
            Console.WriteLine("1. 选择验证类型");
            Console.WriteLine("2. 配置验证参数");
            Console.WriteLine("3. 实时测试验证结果");
            Console.WriteLine("4. 查看生成的正则表达式");
            Console.WriteLine("5. 导出验证器代码");
            Console.WriteLine();

            Console.WriteLine("💡 完整交互式功能请参考完整版本");
            Console.WriteLine();
        }
    }
}
