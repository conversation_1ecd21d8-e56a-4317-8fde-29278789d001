using System;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Demo.Examples
{
    /// <summary>
    /// 量词使用演示
    /// </summary>
    public static class QuantifierDemo
    {
        public static void Run()
        {
            Console.WriteLine("演示量词使用功能...\n");

            BasicQuantifiersDemo();
            LazyQuantifiersDemo();
            ExactQuantifiersDemo();
        }

        private static void BasicQuantifiersDemo()
        {
            Console.WriteLine("🔢 基础量词演示");
            Console.WriteLine("================");

            // 零次或多次 (*)
            var zeroOrMore = YRegexBuilder.Create()
                .Literal("a", "字母a")
                .ZeroOrMore("零次或多次")
                .Literal("b", "字母b")
                .Build();

            // 一次或多次 (+)
            var oneOrMore = YRegexBuilder.Create()
                .Literal("a", "字母a")
                .OneOrMore("一次或多次")
                .Literal("b", "字母b")
                .Build();

            // 零次或一次 (?)
            var zeroOrOne = YRegexBuilder.Create()
                .Literal("colou", "前缀")
                .Literal("r", "字母r")
                .ZeroOrOne("可选")
                .Build();

            var testCases = new[]
            {
                ("b", zeroOrMore, "a*b"),
                ("ab", zeroOrMore, "a*b"),
                ("aaab", zeroOrMore, "a*b"),
                ("b", oneOrMore, "a+b"),
                ("ab", oneOrMore, "a+b"),
                ("aaab", oneOrMore, "a+b"),
                ("colo", zeroOrOne, "colou?r"),
                ("colour", zeroOrOne, "colou?r")
            };

            foreach (var (input, validator, pattern) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {pattern}: '{input}' -> {result}");
            }

            Console.WriteLine($"\n描述: {zeroOrMore.Description}");
            Console.WriteLine($"模式: {zeroOrMore.Pattern}");
            Console.WriteLine();
        }

        private static void LazyQuantifiersDemo()
        {
            Console.WriteLine("🐌 懒惰量词演示");
            Console.WriteLine("================");

            // 懒惰零次或多次
            var lazyZeroOrMore = YRegexBuilder.Create()
                .Literal("<", "开始标签")
                .CharacterSet(".", "任意字符")
                .ZeroOrMoreLazy("懒惰匹配")
                .Literal(">", "结束标签")
                .Build();

            // 贪婪零次或多次（对比）
            var greedyZeroOrMore = YRegexBuilder.Create()
                .Literal("<", "开始标签")
                .CharacterSet(".", "任意字符")
                .ZeroOrMore("贪婪匹配")
                .Literal(">", "结束标签")
                .Build();

            var testInput = "<div>content</div>";

            Console.WriteLine($"测试字符串: {testInput}");
            Console.WriteLine($"懒惰匹配结果: {lazyZeroOrMore.Match(testInput).Value}");
            Console.WriteLine($"贪婪匹配结果: {greedyZeroOrMore.Match(testInput).Value}");

            Console.WriteLine($"\n懒惰描述: {lazyZeroOrMore.Description}");
            Console.WriteLine($"懒惰模式: {lazyZeroOrMore.Pattern}");
            Console.WriteLine();
        }

        private static void ExactQuantifiersDemo()
        {
            Console.WriteLine("🎯 精确量词演示");
            Console.WriteLine("================");

            // 精确次数
            var exactly = YRegexBuilder.Create()
                .Digit("数字")
                .Exactly(3, "精确3次")
                .Build();

            // 至少次数
            var atLeast = YRegexBuilder.Create()
                .Letter("字母")
                .AtLeast(2, "至少2次")
                .Build();

            // 范围次数
            var between = YRegexBuilder.Create()
                .AlphaNumeric("字母数字")
                .Between(3, 5, "3到5次")
                .Build();

            var testCases = new[]
            {
                ("123", exactly, "精确3位数字"),
                ("12", exactly, "精确3位数字"),
                ("1234", exactly, "精确3位数字"),
                ("ab", atLeast, "至少2个字母"),
                ("abc", atLeast, "至少2个字母"),
                ("a", atLeast, "至少2个字母"),
                ("abc", between, "3-5个字母数字"),
                ("ab", between, "3-5个字母数字"),
                ("abcdef", between, "3-5个字母数字")
            };

            foreach (var (input, validator, description) in testCases)
            {
                var result = validator.IsMatch(input);
                Console.WriteLine($"{(result ? "✅" : "❌")} {description}: '{input}' -> {result}");
            }

            Console.WriteLine($"\n描述: {exactly.Description}");
            Console.WriteLine($"模式: {exactly.Pattern}");
            Console.WriteLine();
        }
    }
}
