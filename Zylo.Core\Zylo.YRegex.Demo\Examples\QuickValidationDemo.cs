using System;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Demo.Examples
{
    /// <summary>
    /// 快捷验证方法演示
    /// 展示 Zylo.YRegex 的快捷验证功能
    /// </summary>
    public static class QuickValidationDemo
    {
        public static void Run()
        {
            Console.WriteLine("演示快捷验证方法功能...\n");

            EmailValidationDemo();
            PhoneValidationDemo();
            PasswordValidationDemo();
            DateTimeValidationDemo();
            NetworkValidationDemo();
            UserInputValidationDemo();
        }

        /// <summary>
        /// 邮箱验证演示
        /// </summary>
        private static void EmailValidationDemo()
        {
            Console.WriteLine("📧 邮箱验证演示");
            Console.WriteLine("================");

            // 基础邮箱验证
            var basicEmail = YRegexBuilder.Create()
                .QuickEmail(false, "基础邮箱验证")
                .Build();

            // 严格邮箱验证
            var strictEmail = YRegexBuilder.Create()
                .QuickEmail(true, "严格邮箱验证")
                .Build();

            var testEmails = new[]
            {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "invalid.email",
                "user@",
                "@domain.com",
                "user@domain",
                "<EMAIL>"
            };

            Console.WriteLine("基础邮箱验证结果:");
            foreach (var email in testEmails)
            {
                var result = basicEmail.IsMatch(email);
                Console.WriteLine($"{(result ? "✅" : "❌")} {email}");
            }

            Console.WriteLine("\n严格邮箱验证结果:");
            foreach (var email in testEmails)
            {
                var result = strictEmail.IsMatch(email);
                Console.WriteLine($"{(result ? "✅" : "❌")} {email}");
            }

            Console.WriteLine($"\n描述: {basicEmail.Description}");
            Console.WriteLine($"模式: {basicEmail.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 手机号验证演示
        /// </summary>
        private static void PhoneValidationDemo()
        {
            Console.WriteLine("📱 手机号验证演示");
            Console.WriteLine("==================");

            // 中国手机号
            var chinaPhone = YRegexBuilder.Create()
                .QuickPhone("china", "中国手机号")
                .Build();

            // 美国手机号
            var usPhone = YRegexBuilder.Create()
                .QuickPhone("us", "美国手机号")
                .Build();

            // 国际手机号
            var intlPhone = YRegexBuilder.Create()
                .QuickPhone("international", "国际手机号")
                .Build();

            var testPhones = new[]
            {
                "13812345678",    // 中国移动
                "15987654321",    // 中国联通
                "18612345678",    // 中国电信
                "******-123-4567", // 美国格式
                "(*************",  // 美国格式
                "+86-138-1234-5678", // 国际格式
                "12345678901",     // 无效
                "1381234567"       // 位数不够
            };

            Console.WriteLine("中国手机号验证:");
            foreach (var phone in testPhones)
            {
                var result = chinaPhone.IsMatch(phone);
                Console.WriteLine($"{(result ? "✅" : "❌")} {phone}");
            }

            Console.WriteLine("\n美国手机号验证:");
            foreach (var phone in testPhones)
            {
                var result = usPhone.IsMatch(phone);
                Console.WriteLine($"{(result ? "✅" : "❌")} {phone}");
            }

            Console.WriteLine($"\n描述: {chinaPhone.Description}");
            Console.WriteLine($"模式: {chinaPhone.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 密码验证演示
        /// </summary>
        private static void PasswordValidationDemo()
        {
            Console.WriteLine("🔐 密码验证演示");
            Console.WriteLine("================");

            // 弱密码
            var weakPassword = YRegexBuilder.Create()
                .QuickPassword("weak", "弱密码")
                .Build();

            // 中等密码
            var mediumPassword = YRegexBuilder.Create()
                .QuickPassword("medium", "中等密码")
                .Build();

            // 强密码
            var strongPassword = YRegexBuilder.Create()
                .QuickPassword("strong", "强密码")
                .Build();

            // 超强密码
            var ultraPassword = YRegexBuilder.Create()
                .QuickPassword("ultra", "超强密码")
                .Build();

            var testPasswords = new[]
            {
                "123456",           // 弱
                "password",         // 弱
                "Password1",        // 中等
                "Password123",      // 中等
                "Password1!",       // 强
                "StrongPass123!",   // 强
                "UltraStr0ng!@#$",  // 超强
                "weak",             // 太弱
                "VeryComplexPassword123!@#" // 超强
            };

            Console.WriteLine("密码强度验证结果:");
            Console.WriteLine("密码".PadRight(25) + "弱".PadRight(6) + "中".PadRight(6) + "强".PadRight(6) + "超强");
            Console.WriteLine(new string('-', 50));

            foreach (var password in testPasswords)
            {
                var weak = weakPassword.IsMatch(password) ? "✅" : "❌";
                var medium = mediumPassword.IsMatch(password) ? "✅" : "❌";
                var strong = strongPassword.IsMatch(password) ? "✅" : "❌";
                var ultra = ultraPassword.IsMatch(password) ? "✅" : "❌";

                Console.WriteLine($"{password.PadRight(25)}{weak.PadRight(6)}{medium.PadRight(6)}{strong.PadRight(6)}{ultra}");
            }

            Console.WriteLine($"\n强密码描述: {strongPassword.Description}");
            Console.WriteLine($"强密码模式: {strongPassword.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 日期时间验证演示
        /// </summary>
        private static void DateTimeValidationDemo()
        {
            Console.WriteLine("📅 日期时间验证演示");
            Console.WriteLine("====================");

            // ISO日期
            var isoDate = YRegexBuilder.Create()
                .QuickDate("iso", "ISO日期")
                .Build();

            // 美式日期
            var usDate = YRegexBuilder.Create()
                .QuickDate("us", "美式日期")
                .Build();

            // 中文日期
            var chineseDate = YRegexBuilder.Create()
                .QuickDate("chinese", "中文日期")
                .Build();

            // 24小时制时间
            var time24h = YRegexBuilder.Create()
                .QuickTime("24h", "24小时制")
                .Build();

            // 12小时制时间
            var time12h = YRegexBuilder.Create()
                .QuickTime("12h", "12小时制")
                .Build();

            var testDates = new[]
            {
                "2024-03-15",      // ISO
                "03/15/2024",      // 美式
                "2024年03月15日",   // 中文
                "2024-13-01",      // 无效月份
                "2024-02-30"       // 无效日期
            };

            var testTimes = new[]
            {
                "14:30:00",        // 24小时制
                "2:30:00 PM",      // 12小时制
                "09:05:30",        // 24小时制
                "25:00:00",        // 无效小时
                "12:60:00"         // 无效分钟
            };

            Console.WriteLine("日期验证结果:");
            Console.WriteLine("日期".PadRight(20) + "ISO".PadRight(8) + "美式".PadRight(8) + "中文");
            Console.WriteLine(new string('-', 40));

            foreach (var date in testDates)
            {
                var iso = isoDate.IsMatch(date) ? "✅" : "❌";
                var us = usDate.IsMatch(date) ? "✅" : "❌";
                var chinese = chineseDate.IsMatch(date) ? "✅" : "❌";

                Console.WriteLine($"{date.PadRight(20)}{iso.PadRight(8)}{us.PadRight(8)}{chinese}");
            }

            Console.WriteLine("\n时间验证结果:");
            Console.WriteLine("时间".PadRight(15) + "24小时".PadRight(10) + "12小时");
            Console.WriteLine(new string('-', 30));

            foreach (var time in testTimes)
            {
                var h24 = time24h.IsMatch(time) ? "✅" : "❌";
                var h12 = time12h.IsMatch(time) ? "✅" : "❌";

                Console.WriteLine($"{time.PadRight(15)}{h24.PadRight(10)}{h12}");
            }

            Console.WriteLine($"\nISO日期描述: {isoDate.Description}");
            Console.WriteLine($"ISO日期模式: {isoDate.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 网络相关验证演示
        /// </summary>
        private static void NetworkValidationDemo()
        {
            Console.WriteLine("🌐 网络相关验证演示");
            Console.WriteLine("====================");

            // URL验证
            var url = YRegexBuilder.Create()
                .QuickURL(true, "URL验证")
                .Build();

            // IPv4验证
            var ipv4 = YRegexBuilder.Create()
                .QuickIP("ipv4", "IPv4地址")
                .Build();

            // IPv6验证
            var ipv6 = YRegexBuilder.Create()
                .QuickIP("ipv6", "IPv6地址")
                .Build();

            var testUrls = new[]
            {
                "https://www.example.com",
                "http://localhost:8080",
                "ftp://files.example.com",
                "www.example.com",
                "invalid-url"
            };

            var testIPs = new[]
            {
                "***********",
                "********",
                "***************",
                "999.999.999.999",
                "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
                "::1",
                "invalid-ip"
            };

            Console.WriteLine("URL验证结果:");
            foreach (var testUrl in testUrls)
            {
                var result = url.IsMatch(testUrl);
                Console.WriteLine($"{(result ? "✅" : "❌")} {testUrl}");
            }

            Console.WriteLine("\nIP地址验证结果:");
            Console.WriteLine("IP地址".PadRight(40) + "IPv4".PadRight(8) + "IPv6");
            Console.WriteLine(new string('-', 55));

            foreach (var ip in testIPs)
            {
                var v4 = ipv4.IsMatch(ip) ? "✅" : "❌";
                var v6 = ipv6.IsMatch(ip) ? "✅" : "❌";

                Console.WriteLine($"{ip.PadRight(40)}{v4.PadRight(8)}{v6}");
            }

            Console.WriteLine($"\nURL描述: {url.Description}");
            Console.WriteLine($"URL模式: {url.Pattern}");
            Console.WriteLine();
        }

        /// <summary>
        /// 用户输入验证演示
        /// </summary>
        private static void UserInputValidationDemo()
        {
            Console.WriteLine("👤 用户输入验证演示");
            Console.WriteLine("====================");

            // 用户名验证
            var username = YRegexBuilder.Create()
                .QuickUsername("standard", "标准用户名")
                .Build();

            // 文件名验证
            var filename = YRegexBuilder.Create()
                .QuickFilename("safe", "安全文件名")
                .Build();

            // 颜色值验证
            var color = YRegexBuilder.Create()
                .StartOfString("开始")
                .Literal("#", "井号").ZeroOrOne("可选")
                .CharacterSet("0-9a-fA-F", "十六进制字符").Between(3, 6, "3-6位")
                .EndOfString("结束")
                .Build();

            var testUsernames = new[]
            {
                "user123",
                "test_user",
                "valid-user",
                "123invalid",
                "user@name",
                "a"
            };

            var testFilenames = new[]
            {
                "document.pdf",
                "image.jpg",
                "file name.txt",
                "file<name>.doc",
                "valid_file-123.xlsx"
            };

            var testColors = new[]
            {
                "#FF0000",
                "#fff",
                "#123ABC",
                "red",
                "#GGGGGG",
                "#12345"
            };

            Console.WriteLine("用户名验证结果:");
            foreach (var user in testUsernames)
            {
                var result = username.IsMatch(user);
                Console.WriteLine($"{(result ? "✅" : "❌")} {user}");
            }

            Console.WriteLine("\n文件名验证结果:");
            foreach (var file in testFilenames)
            {
                var result = filename.IsMatch(file);
                Console.WriteLine($"{(result ? "✅" : "❌")} {file}");
            }

            Console.WriteLine("\n颜色值验证结果:");
            foreach (var colorValue in testColors)
            {
                var result = color.IsMatch(colorValue);
                Console.WriteLine($"{(result ? "✅" : "❌")} {colorValue}");
            }

            Console.WriteLine($"\n用户名描述: {username.Description}");
            Console.WriteLine($"用户名模式: {username.Pattern}");
            Console.WriteLine();
        }
    }
}
