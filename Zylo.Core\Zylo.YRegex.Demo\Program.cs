using System;
using System.Text;
using Zylo.YRegex.Demo.Examples;

namespace Zylo.YRegex.Demo
{
    /// <summary>
    /// Zylo.YRegex 实例演示程序
    /// 展示如何使用 Zylo.YRegex 构建各种正则表达式验证器
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            // 设置控制台编码支持中文
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;

            try
            {
                ShowWelcome();
                ShowMainMenu();

                // 添加提示，让用户知道程序已经启动
                Console.WriteLine("💡 提示：输入 0 可以运行所有演示，查看完整功能！");

                while (true)
                {
                    Console.Write("\n请选择要运行的演示 (输入数字或 'q' 退出): ");
                    var input = Console.ReadLine()?.Trim().ToLower();

                    if (input == "q" || input == "quit" || input == "exit")
                    {
                        Console.WriteLine("\n感谢使用 Zylo.YRegex Demo！👋");
                        break;
                    }

                    if (int.TryParse(input, out int choice))
                    {
                        RunDemo(choice);
                    }
                    else
                    {
                        Console.WriteLine("❌ 无效输入，请输入数字或 'q' 退出");
                    }

                    Console.WriteLine("\n" + new string('=', 60));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序运行出错: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 显示欢迎信息
        /// </summary>
        private static void ShowWelcome()
        {
            Console.WriteLine("🚀 Zylo.YRegex 实例演示程序");
            Console.WriteLine("============================");
            Console.WriteLine("现代化的 C# 正则表达式构建器");
            Console.WriteLine("让复杂的正则表达式变得简单易懂！");
            Console.WriteLine();
        }

        /// <summary>
        /// 显示主菜单
        /// </summary>
        private static void ShowMainMenu()
        {
            Console.WriteLine("📚 可用演示:");
            Console.WriteLine("0.  🚀 运行所有演示 (推荐)");
            Console.WriteLine("1.  基础字符匹配演示");
            Console.WriteLine("2.  量词使用演示");
            Console.WriteLine("3.  字符类和范围演示");
            Console.WriteLine("4.  分组和断言演示");
            Console.WriteLine("5.  快捷验证方法演示");
            Console.WriteLine("6.  专业领域验证演示");
            Console.WriteLine("7.  中文内容验证演示");
            Console.WriteLine("8.  网站示例对比演示");
            Console.WriteLine("9.  实际应用场景演示");
            Console.WriteLine("10. 性能对比演示");
            Console.WriteLine("11. API 完整功能演示");
            Console.WriteLine("12. 交互式验证器构建");
            Console.WriteLine("13. 🇨🇳 中文方法演示 (RegZ/RegV/RegQ/RegG)");
            Console.WriteLine();
            Console.WriteLine("输入 'q' 退出程序");
        }

        /// <summary>
        /// 运行指定的演示
        /// </summary>
        private static void RunDemo(int choice)
        {
            Console.WriteLine();

            try
            {
                switch (choice)
                {
                    case 0:
                        Console.WriteLine("🚀 运行所有演示");
                        Console.WriteLine("================");
                        RunAllDemos();
                        break;

                    case 1:
                        Console.WriteLine("🔤 基础字符匹配演示");
                        Console.WriteLine("==================");
                        BasicCharacterDemo.Run();
                        break;

                    case 2:
                        Console.WriteLine("🔢 量词使用演示");
                        Console.WriteLine("==============");
                        QuantifierDemo.Run();
                        break;

                    case 3:
                        Console.WriteLine("📝 字符类和范围演示");
                        Console.WriteLine("==================");
                        CharacterClassDemo.Run();
                        break;

                    case 4:
                        Console.WriteLine("🔗 分组和断言演示");
                        Console.WriteLine("================");
                        GroupingDemo.Run();
                        break;

                    case 5:
                        Console.WriteLine("🚀 快捷验证方法演示");
                        Console.WriteLine("==================");
                        QuickValidationDemo.Run();
                        break;

                    case 6:
                        Console.WriteLine("🏛️ 专业领域验证演示");
                        Console.WriteLine("====================");
                        ProfessionalValidationDemo.Run();
                        break;

                    case 7:
                        Console.WriteLine("🇨🇳 中文内容验证演示");
                        Console.WriteLine("====================");
                        ChineseValidationDemo.Run();
                        break;

                    case 8:
                        Console.WriteLine("🌐 网站示例对比演示");
                        Console.WriteLine("====================");
                        WebsiteExamplesDemo.Run();
                        break;

                    case 9:
                        Console.WriteLine("🎯 实际应用场景演示");
                        Console.WriteLine("====================");
                        RealWorldDemo.Run();
                        break;

                    case 10:
                        Console.WriteLine("📊 性能对比演示");
                        Console.WriteLine("================");
                        PerformanceDemo.Run();
                        break;

                    case 11:
                        Console.WriteLine("📚 API 完整功能演示");
                        Console.WriteLine("====================");
                        CompleteAPIDemo.Run();
                        break;

                    case 12:
                        Console.WriteLine("🛠️ 交互式验证器构建");
                        Console.WriteLine("====================");
                        InteractiveBuilderDemo.Run();
                        break;

                    case 13:
                        Console.WriteLine("🇨🇳 中文方法演示");
                        Console.WriteLine("====================");
                        ChineseMethodsDemo.Run();
                        break;

                    default:
                        Console.WriteLine("❌ 无效选择，请输入 1-13 之间的数字");
                        ShowMainMenu();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 演示运行出错: {ex.Message}");
                Console.WriteLine($"详细信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 运行所有演示
        /// </summary>
        private static void RunAllDemos()
        {
            Console.WriteLine("🎯 开始运行所有演示功能...\n");

            var demos = new[]
            {
                (1, "基础字符匹配演示", (Action)(() => BasicCharacterDemo.Run())),
                (2, "量词使用演示", (Action)(() => QuantifierDemo.Run())),
                (3, "字符类和范围演示", (Action)(() => CharacterClassDemo.Run())),
                (4, "分组和断言演示", (Action)(() => GroupingDemo.Run())),
                (5, "快捷验证方法演示", (Action)(() => QuickValidationDemo.Run())),
                (6, "专业领域验证演示", (Action)(() => ProfessionalValidationDemo.Run())),
                (7, "中文内容验证演示", (Action)(() => ChineseValidationDemo.Run())),
                (8, "网站示例对比演示", (Action)(() => WebsiteExamplesDemo.Run())),
                (9, "实际应用场景演示", (Action)(() => RealWorldDemo.Run())),
                (10, "性能对比演示", (Action)(() => PerformanceDemo.Run())),
                (11, "API 完整功能演示", (Action)(() => CompleteAPIDemo.Run())),
                (12, "交互式验证器构建", (Action)(() => InteractiveBuilderDemo.Run())),
                (13, "中文方法演示", (Action)(() => ChineseMethodsDemo.Run()))
            };

            int successCount = 0;
            int totalCount = demos.Length;

            foreach (var (number, name, action) in demos)
            {
                try
                {
                    Console.WriteLine($"🔄 正在运行: {number}. {name}");
                    Console.WriteLine(new string('-', 50));

                    action();

                    Console.WriteLine($"✅ {name} - 运行成功");
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ {name} - 运行失败: {ex.Message}");
                }

                Console.WriteLine(new string('=', 60));
                Console.WriteLine();
            }

            // 显示总结
            Console.WriteLine("📊 运行总结");
            Console.WriteLine("============");
            Console.WriteLine($"总演示数量: {totalCount}");
            Console.WriteLine($"成功运行: {successCount}");
            Console.WriteLine($"失败数量: {totalCount - successCount}");
            Console.WriteLine($"成功率: {(double)successCount / totalCount * 100:F1}%");

            if (successCount == totalCount)
            {
                Console.WriteLine("🎉 所有演示都运行成功！Zylo.YRegex 功能完整！");
            }
            else
            {
                Console.WriteLine("⚠️ 部分演示运行失败，请检查具体错误信息。");
            }
        }
    }
}
