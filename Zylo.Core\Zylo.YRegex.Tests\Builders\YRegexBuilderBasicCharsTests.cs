using FluentAssertions;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Tests.Builders;

/// <summary>
/// YRegexBuilder 基础字符功能测试
/// </summary>
public class YRegexBuilderBasicCharsTests
{
    #region 数字字符测试

    [Fact]
    public void Digit_ShouldMatchSingleDigit()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Digit()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("0").Should().BeTrue();
        validator.IsMatch("5").Should().BeTrue();
        validator.IsMatch("9").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("12").Should().BeFalse(); // 多个数字
        validator.IsMatch("").Should().BeFalse(); // 空字符串
    }

    [Fact]
    public void Digits_WithCount_ShouldMatchExactDigitCount()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Digits(3)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("123").Should().BeTrue();
        validator.IsMatch("000").Should().BeTrue();
        validator.IsMatch("999").Should().BeTrue();
        validator.IsMatch("12").Should().BeFalse(); // 少于3位
        validator.IsMatch("1234").Should().BeFalse(); // 多于3位
        validator.IsMatch("abc").Should().BeFalse(); // 非数字
    }

    [Fact]
    public void Digits_WithRange_ShouldMatchDigitRange()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Digits(2, 4)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("12").Should().BeTrue(); // 2位
        validator.IsMatch("123").Should().BeTrue(); // 3位
        validator.IsMatch("1234").Should().BeTrue(); // 4位
        validator.IsMatch("1").Should().BeFalse(); // 少于2位
        validator.IsMatch("12345").Should().BeFalse(); // 多于4位
    }

    [Fact]
    public void NonDigit_ShouldMatchNonDigitCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .NonDigit()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("Z").Should().BeTrue();
        validator.IsMatch("@").Should().BeTrue();
        validator.IsMatch("5").Should().BeFalse();
        validator.IsMatch("0").Should().BeFalse();
    }

    #endregion

    #region 字母字符测试

    [Fact]
    public void Letter_ShouldMatchSingleLetter()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Letter()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("Z").Should().BeTrue();
        validator.IsMatch("m").Should().BeTrue();
        validator.IsMatch("5").Should().BeFalse();
        validator.IsMatch("@").Should().BeFalse();
        validator.IsMatch("ab").Should().BeFalse(); // 多个字母
    }

    [Fact]
    public void Letters_WithCount_ShouldMatchExactLetterCount()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Letters(3)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("abc").Should().BeTrue();
        validator.IsMatch("XYZ").Should().BeTrue();
        validator.IsMatch("AbC").Should().BeTrue();
        validator.IsMatch("ab").Should().BeFalse(); // 少于3位
        validator.IsMatch("abcd").Should().BeFalse(); // 多于3位
        validator.IsMatch("123").Should().BeFalse(); // 非字母
    }

    [Fact]
    public void LowerCase_ShouldMatchLowercaseLetters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .LowerCase()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("z").Should().BeTrue();
        validator.IsMatch("m").Should().BeTrue();
        validator.IsMatch("A").Should().BeFalse();
        validator.IsMatch("Z").Should().BeFalse();
        validator.IsMatch("5").Should().BeFalse();
    }

    [Fact]
    public void UpperCase_ShouldMatchUppercaseLetters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .UpperCase()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("A").Should().BeTrue();
        validator.IsMatch("Z").Should().BeTrue();
        validator.IsMatch("M").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("z").Should().BeFalse();
        validator.IsMatch("5").Should().BeFalse();
    }

    #endregion

    #region 字母数字字符测试

    [Fact]
    public void AlphaNumeric_ShouldMatchLettersAndDigits()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .AlphaNumeric()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("Z").Should().BeTrue();
        validator.IsMatch("5").Should().BeTrue();
        validator.IsMatch("0").Should().BeTrue();
        validator.IsMatch("@").Should().BeFalse();
        validator.IsMatch(" ").Should().BeFalse();
        validator.IsMatch("_").Should().BeFalse();
    }

    [Fact]
    public void AlphaNumeric_WithCount_ShouldMatchExactCount()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .AlphaNumeric(4)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("abc1").Should().BeTrue();
        validator.IsMatch("A1B2").Should().BeTrue();
        validator.IsMatch("1234").Should().BeTrue();
        validator.IsMatch("abcd").Should().BeTrue();
        validator.IsMatch("abc").Should().BeFalse(); // 少于4位
        validator.IsMatch("abc12").Should().BeFalse(); // 多于4位
        validator.IsMatch("abc@").Should().BeFalse(); // 包含特殊字符
    }

    [Fact]
    public void WordChar_ShouldMatchWordCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .WordChar()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("Z").Should().BeTrue();
        validator.IsMatch("5").Should().BeTrue();
        validator.IsMatch("_").Should().BeTrue(); // 下划线是单词字符
        validator.IsMatch("@").Should().BeFalse();
        validator.IsMatch(" ").Should().BeFalse();
        validator.IsMatch("-").Should().BeFalse();
    }

    [Fact]
    public void NonWordChar_ShouldMatchNonWordCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .NonWordChar()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("@").Should().BeTrue();
        validator.IsMatch(" ").Should().BeTrue();
        validator.IsMatch("-").Should().BeTrue();
        validator.IsMatch(".").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("5").Should().BeFalse();
        validator.IsMatch("_").Should().BeFalse();
    }

    #endregion

    #region 空白字符测试

    [Fact]
    public void Whitespace_ShouldMatchWhitespaceCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Whitespace()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(" ").Should().BeTrue(); // 空格
        validator.IsMatch("\t").Should().BeTrue(); // 制表符
        validator.IsMatch("\n").Should().BeTrue(); // 换行符
        validator.IsMatch("\r").Should().BeTrue(); // 回车符
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("5").Should().BeFalse();
    }

    [Fact]
    public void NonWhitespace_ShouldMatchNonWhitespaceCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .NonWhitespace()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("5").Should().BeTrue();
        validator.IsMatch("@").Should().BeTrue();
        validator.IsMatch(" ").Should().BeFalse();
        validator.IsMatch("\t").Should().BeFalse();
        validator.IsMatch("\n").Should().BeFalse();
    }

    [Fact]
    public void Space_ShouldMatchSpaceCharacter()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Space()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(" ").Should().BeTrue();
        validator.IsMatch("\t").Should().BeFalse(); // 制表符不是空格
        validator.IsMatch("\n").Should().BeFalse(); // 换行符不是空格
        validator.IsMatch("a").Should().BeFalse();
    }

    [Fact]
    public void Tab_ShouldMatchTabCharacter()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Tab()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("\t").Should().BeTrue();
        validator.IsMatch(" ").Should().BeFalse(); // 空格不是制表符
        validator.IsMatch("\n").Should().BeFalse();
        validator.IsMatch("a").Should().BeFalse();
    }

    [Fact]
    public void Newline_ShouldMatchNewlineCharacters()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Newline()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("\n").Should().BeTrue(); // LF
        validator.IsMatch("\r\n").Should().BeTrue(); // CRLF
        validator.IsMatch("\r").Should().BeFalse(); // 单独的CR不匹配
        validator.IsMatch(" ").Should().BeFalse();
        validator.IsMatch("a").Should().BeFalse();
    }

    #endregion

    #region 特殊字符测试

    [Fact]
    public void Dot_ShouldMatchLiteralDot()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Dot()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(".").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse(); // 不应该匹配任意字符
        validator.IsMatch("5").Should().BeFalse();
        validator.IsMatch(" ").Should().BeFalse();
    }

    [Fact]
    public void At_ShouldMatchAtSymbol()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .At()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("@").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("5").Should().BeFalse();
    }

    [Fact]
    public void Hyphen_ShouldMatchHyphenCharacter()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Hyphen()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("-").Should().BeTrue();
        validator.IsMatch("_").Should().BeFalse();
        validator.IsMatch("a").Should().BeFalse();
    }

    [Fact]
    public void Underscore_ShouldMatchUnderscoreCharacter()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Underscore()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("_").Should().BeTrue();
        validator.IsMatch("-").Should().BeFalse();
        validator.IsMatch("a").Should().BeFalse();
    }

    #endregion

    #region 字符集合测试

    [Fact]
    public void OneOf_ShouldMatchAnyCharacterInSet()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .OneOf("abc")
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("b").Should().BeTrue();
        validator.IsMatch("c").Should().BeTrue();
        validator.IsMatch("d").Should().BeFalse();
        validator.IsMatch("A").Should().BeFalse(); // 大小写敏感
    }

    [Fact]
    public void OneOf_WithSpecialCharacters_ShouldEscapeProperly()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .OneOf(@"[]^-\")
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("[").Should().BeTrue();
        validator.IsMatch("]").Should().BeTrue();
        validator.IsMatch("^").Should().BeTrue();
        validator.IsMatch("-").Should().BeTrue();
        validator.IsMatch(@"\").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
    }

    [Fact]
    public void NoneOf_ShouldMatchCharactersNotInSet()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .NoneOf("abc")
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("d").Should().BeTrue();
        validator.IsMatch("1").Should().BeTrue();
        validator.IsMatch("@").Should().BeTrue();
        validator.IsMatch("a").Should().BeFalse();
        validator.IsMatch("b").Should().BeFalse();
        validator.IsMatch("c").Should().BeFalse();
    }

    [Fact]
    public void Range_ShouldMatchCharacterRange()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Range('a', 'z')
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("a").Should().BeTrue();
        validator.IsMatch("m").Should().BeTrue();
        validator.IsMatch("z").Should().BeTrue();
        validator.IsMatch("A").Should().BeFalse(); // 大写字母
        validator.IsMatch("1").Should().BeFalse(); // 数字
    }

    #endregion

    #region 组合测试

    [Fact]
    public void CombinedCharacterClasses_ShouldWorkTogether()
    {
        // Arrange & Act - 匹配用户名格式：字母开头，后跟字母数字下划线
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Letter() // 必须以字母开头
            .AlphaNumeric(2, 19) // 后跟2-19个字母数字字符
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("user123").Should().BeTrue();
        validator.IsMatch("Admin").Should().BeTrue();
        validator.IsMatch("a12").Should().BeTrue(); // 最短3位
        validator.IsMatch("1user").Should().BeFalse(); // 不能以数字开头
        validator.IsMatch("u").Should().BeFalse(); // 太短
        validator.IsMatch("user@123").Should().BeFalse(); // 包含特殊字符
    }

    [Fact]
    public void EmailPattern_UsingBasicChars_ShouldWork()
    {
        // Arrange & Act - 简单的邮箱模式
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .AlphaNumeric(1, 64) // 用户名部分
            .At()
            .AlphaNumeric(1, 63) // 域名部分
            .Dot()
            .Letters(2, 6) // 顶级域名
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch("<EMAIL>").Should().BeTrue();
        validator.IsMatch("<EMAIL>").Should().BeTrue();
        validator.IsMatch("@domain.com").Should().BeFalse(); // 缺少用户名
        validator.IsMatch("user@.com").Should().BeFalse(); // 缺少域名
        validator.IsMatch("user@domain").Should().BeFalse(); // 缺少顶级域名
    }

    #endregion
}
