using FluentAssertions;
using Zylo.YRegex.Builders;
using Zylo.YRegex.Core;

namespace Zylo.YRegex.Tests.Builders;

/// <summary>
/// YRegexBuilder 核心功能测试
/// </summary>
public class YRegexBuilderCoreTests
{
    #region 基础构建测试

    [Fact]
    public void Create_ShouldReturnNewInstance()
    {
        // Act
        var builder = YRegexBuilder.Create();

        // Assert
        builder.Should().NotBeNull();
        builder.Should().BeOfType<YRegexBuilder>();
    }

    [Fact]
    public void Pattern_ShouldAddRawPattern()
    {
        // Arrange
        var builder = YRegexBuilder.Create();
        var pattern = @"\d+";

        // Act
        var result = builder.Pattern(pattern, "数字模式");
        var validator = result.Build();

        // Assert
        validator.Pattern.Should().Contain(pattern);
        validator.Description.Should().Contain("数字模式");
    }

    [Fact]
    public void Literal_ShouldEscapeSpecialCharacters()
    {
        // Arrange
        var builder = YRegexBuilder.Create();
        var literal = "test.com";

        // Act
        var result = builder.Literal(literal);
        var validator = result.Build();

        // Assert
        validator.Pattern.Should().Contain(@"test\.com");
        validator.IsMatch("test.com").Should().BeTrue();
        validator.IsMatch("testXcom").Should().BeFalse();
    }

    [Fact]
    public void StartOfLine_ShouldAddCaretAnchor()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        var result = builder.StartOfLine();
        var pattern = result.BuildPattern();

        // Assert
        pattern.Should().StartWith("^");
    }

    [Fact]
    public void EndOfLine_ShouldAddDollarAnchor()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        var result = builder.EndOfLine();
        var pattern = result.BuildPattern();

        // Assert
        pattern.Should().EndWith("$");
    }

    [Fact]
    public void WordBoundary_ShouldAddWordBoundaryPattern()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        var result = builder.WordBoundary();
        var pattern = result.BuildPattern();

        // Assert
        pattern.Should().Contain(@"\b");
    }

    #endregion

    #region 链式调用测试

    [Fact]
    public void ChainedCalls_ShouldBuildCorrectPattern()
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine("开始")
            .Literal("hello", "问候")
            .Whitespace("空格")
            .Literal("world", "世界")
            .EndOfLine("结束")
            .Build();

        // Assert
        validator.IsMatch("hello world").Should().BeTrue();
        validator.IsMatch("hello  world").Should().BeFalse(); // 多个空格
        validator.IsMatch(" hello world").Should().BeFalse(); // 前面有空格
        validator.IsMatch("hello world ").Should().BeFalse(); // 后面有空格

        validator.Description.Should().Contain("开始");
        validator.Description.Should().Contain("问候");
        validator.Description.Should().Contain("空格");
        validator.Description.Should().Contain("世界");
        validator.Description.Should().Contain("结束");
    }

    [Fact]
    public void FluentInterface_ShouldReturnSameBuilderInstance()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        var result1 = builder.StartOfLine();
        var result2 = result1.Literal("test");
        var result3 = result2.EndOfLine();

        // Assert
        result1.Should().BeSameAs(builder);
        result2.Should().BeSameAs(builder);
        result3.Should().BeSameAs(builder);
    }

    #endregion

    #region 选项和配置测试

    [Fact]
    public void WithOptions_ShouldSetRegexOptions()
    {
        // Arrange
        var builder = YRegexBuilder.Create();
        var options = System.Text.RegularExpressions.RegexOptions.IgnoreCase;

        // Act
        var validator = builder
            .WithOptions(options)
            .Literal("TEST")
            .Build();

        // Assert
        validator.GetOptions().Should().HaveFlag(options);
        validator.IsMatch("test").Should().BeTrue(); // 忽略大小写
        validator.IsMatch("TEST").Should().BeTrue();
        validator.IsMatch("Test").Should().BeTrue();
    }

    [Fact]
    public void WithTimeout_ShouldSetTimeout()
    {
        // Arrange
        var builder = YRegexBuilder.Create();
        var timeout = TimeSpan.FromSeconds(10);

        // Act
        var validator = builder
            .WithTimeout(timeout)
            .Literal("test")
            .Build();

        // Assert
        validator.GetTimeout().Should().Be(timeout);
    }

    [Fact]
    public void AddValidator_ShouldAddCustomValidation()
    {
        // Arrange
        var builder = YRegexBuilder.Create();
        var customValidator = new Func<string, bool>(s => s.Length > 5);

        // Act
        var validator = builder
            .Literal("test")
            .AddValidator(customValidator, "长度检查")
            .Build();

        // Assert
        validator.IsMatch("test").Should().BeFalse(); // 长度不够
        validator.IsMatch("test123").Should().BeTrue(); // 长度足够
        validator.Description.Should().Contain("长度检查");
    }

    #endregion

    #region 构建结果测试

    [Fact]
    public void Build_ShouldReturnValidValidator()
    {
        // Arrange
        var builder = YRegexBuilder.Create().Literal("test");

        // Act
        var validator = builder.Build();

        // Assert
        validator.Should().NotBeNull();
        validator.Pattern.Should().NotBeEmpty();
        validator.IsMatch("test").Should().BeTrue();
        validator.IsMatch("other").Should().BeFalse();
    }

    [Fact]
    public void BuildRegex_ShouldReturnRegexInstance()
    {
        // Arrange
        var builder = YRegexBuilder.Create().Literal("test");

        // Act
        var regex = builder.BuildRegex();

        // Assert
        regex.Should().NotBeNull();
        regex.IsMatch("test").Should().BeTrue();
        regex.IsMatch("other").Should().BeFalse();
    }

    [Fact]
    public void BuildPattern_ShouldReturnPatternString()
    {
        // Arrange
        var builder = YRegexBuilder.Create().Literal("test");

        // Act
        var pattern = builder.BuildPattern();

        // Assert
        pattern.Should().NotBeEmpty();
        pattern.Should().Contain("test");
    }

    [Fact]
    public void GetCurrentPattern_ShouldReturnCurrentState()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act & Assert
        builder.GetCurrentPattern().Should().BeEmpty();

        builder.Literal("test");
        builder.GetCurrentPattern().Should().Contain("test");

        builder.Literal("123");
        var pattern = builder.GetCurrentPattern();
        pattern.Should().Contain("test");
        pattern.Should().Contain("123");
    }

    [Fact]
    public void GetCurrentDescription_ShouldReturnCurrentDescription()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act & Assert
        builder.GetCurrentDescription().Should().BeEmpty();

        builder.Literal("test", "测试");
        builder.GetCurrentDescription().Should().Contain("测试");

        builder.Literal("123", "数字");
        var description = builder.GetCurrentDescription();
        description.Should().Contain("测试");
        description.Should().Contain("数字");
    }

    #endregion

    #region 边界条件测试

    [Fact]
    public void EmptyPattern_ShouldThrowException()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act & Assert
        var act = () => builder.Build(); // 完全空的构建器应该抛出异常
        act.Should().Throw<ArgumentException>(); // 空模式应该抛出异常
    }

    [Fact]
    public void NullPattern_ShouldNotAddToBuilder()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        builder.Pattern(null!);
        var pattern = builder.BuildPattern();

        // Assert
        pattern.Should().BeEmpty();
    }

    [Fact]
    public void NullLiteral_ShouldNotAddToBuilder()
    {
        // Arrange
        var builder = YRegexBuilder.Create();

        // Act
        builder.Literal(null!);
        var pattern = builder.BuildPattern();

        // Assert
        pattern.Should().BeEmpty();
    }

    [Fact]
    public void ToString_ShouldReturnBuilderInfo()
    {
        // Arrange
        var builder = YRegexBuilder.Create()
            .Literal("test", "测试模式");

        // Act
        var result = builder.ToString();

        // Assert
        result.Should().Contain("YRegexBuilder");
        result.Should().Contain("测试模式");
    }

    #endregion

    #region 上下文测试

    [Fact]
    public void CreateWithContext_ShouldUseContextSettings()
    {
        // Arrange
        var options = new YRegexOptions
        {
            DefaultRegexOptions = System.Text.RegularExpressions.RegexOptions.IgnoreCase,
            DefaultTimeout = TimeSpan.FromSeconds(5)
        };
        var context = new Zylo.YRegex.Implementations.YRegexContext(options);

        // Act
        var validator = YRegexBuilder.Create(context)
            .Literal("TEST")
            .Build();

        // Assert
        validator.IsMatch("test").Should().BeTrue(); // 应该忽略大小写
        validator.GetTimeout().Should().Be(TimeSpan.FromSeconds(5));
    }

    #endregion
}
