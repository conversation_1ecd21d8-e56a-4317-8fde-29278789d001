using FluentAssertions;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Tests.Builders;

/// <summary>
/// YRegexBuilder 友好 API 功能测试
/// </summary>
public class YRegexBuilderFriendlyAPITests
{
    #region 常用模式测试

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("user@", false)]
    [InlineData("@domain.com", false)]
    [InlineData("user.domain.com", false)]
    public void Email_ShouldValidateEmailAddresses(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Email()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("13812345678", true)]
    [InlineData("15987654321", true)]
    [InlineData("18612345678", true)]
    [InlineData("19012345678", true)]
    [InlineData("12345678901", false)] // 不是有效号段
    [InlineData("1381234567", false)]  // 位数不够
    [InlineData("138123456789", false)] // 位数太多
    [InlineData("23812345678", false)] // 不是1开头
    public void Phone_ShouldValidateChinesePhoneNumbers(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Phone()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("https://www.example.com", true)]
    [InlineData("http://domain.org", true)]
    [InlineData("https://sub.domain.com/path", true)]
    [InlineData("https://example.com/path?query=value", true)]
    [InlineData("ftp://example.com", false)] // 不是http/https
    [InlineData("www.example.com", false)] // 缺少协议
    [InlineData("https://", false)] // 缺少域名
    public void Url_ShouldValidateUrls(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Url()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("***********", true)]
    [InlineData("********", true)]
    [InlineData("***************", true)]
    [InlineData("0.0.0.0", true)]
    [InlineData("256.1.1.1", false)] // 超出范围
    [InlineData("192.168.1", false)] // 缺少段
    [InlineData("***********.1", false)] // 多余段
    [InlineData("************", false)] // 前导零
    public void IPv4_ShouldValidateIPv4Addresses(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .IPv4()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("2024-03-15", true)]
    [InlineData("2000-01-01", true)]
    [InlineData("2024-12-31", true)]
    [InlineData("24-03-15", false)] // 年份不够
    [InlineData("2024-13-15", false)] // 月份无效
    [InlineData("2024-03-32", false)] // 日期无效
    [InlineData("2024/03/15", false)] // 分隔符错误
    public void Date_ShouldValidateDateFormat(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Date()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("12:30:45", true)]
    [InlineData("00:00:00", true)]
    [InlineData("23:59:59", true)]
    [InlineData("24:00:00", false)] // 小时无效
    [InlineData("12:60:30", false)] // 分钟无效
    [InlineData("12:30:60", false)] // 秒无效
    [InlineData("12:30", false)] // 缺少秒
    public void Time_ShouldValidateTimeFormat(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Time()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("110101199001011234", true)] // 标准18位
    [InlineData("11010119900101123X", true)] // 末位X
    [InlineData("11010119900101123x", true)] // 末位小写x
    [InlineData("110101199001011", false)] // 17位
    [InlineData("1101011990010112345", false)] // 19位
    [InlineData("010101199001011234", false)] // 地区码无效
    [InlineData("110101189001011234", false)] // 年份无效
    public void IdCard_ShouldValidateChineseIdCard(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .IdCard()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    #endregion

    #region 数字模式测试

    [Theory]
    [InlineData("123", true, true)]
    [InlineData("-456", true, true)]
    [InlineData("0", true, true)]
    [InlineData("123", false, true)]
    [InlineData("-456", false, false)] // 不允许负数
    [InlineData("12.3", true, false)] // 不是整数
    [InlineData("abc", true, false)]
    public void Integer_ShouldValidateIntegers(string input, bool allowNegative, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Integer(allowNegative)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("123", true, true)]
    [InlineData("123.45", true, true)]
    [InlineData("-123.45", true, true)]
    [InlineData("0.5", true, true)]
    [InlineData("123.45", false, true)]
    [InlineData("-123.45", false, false)] // 不允许负数
    [InlineData("abc", true, false)]
    public void Decimal_ShouldValidateDecimals(string input, bool allowNegative, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Decimal(allowNegative)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("¥100", "¥", true)]
    [InlineData("¥100.50", "¥", true)]
    [InlineData("$50.99", "$", true)]
    [InlineData("€25", "€", true)]
    [InlineData("100", "¥", false)] // 缺少货币符号
    [InlineData("¥100.5", "¥", false)] // 小数位不正确
    public void Currency_ShouldValidateCurrencyAmounts(string input, string currency, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Currency(currency)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    #endregion

    #region 文本模式测试

    [Theory]
    [InlineData("user123", 3, 20, true)]
    [InlineData("admin", 3, 20, true)]
    [InlineData("test_user", 3, 20, true)]
    [InlineData("ab", 3, 20, false)] // 太短
    [InlineData("verylongusernamethatexceedslimit", 3, 20, false)] // 太长
    [InlineData("user@123", 3, 20, false)] // 包含特殊字符
    [InlineData("user-name", 3, 20, false)] // 包含连字符
    public void Username_ShouldValidateUsernames(string input, int minLength, int maxLength, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Username(minLength, maxLength)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("Password123", 8, 16, false, true)] // 包含大小写和数字
    [InlineData("Password123!", 8, 16, true, true)] // 包含特殊字符
    [InlineData("password123", 8, 16, false, false)] // 缺少大写
    [InlineData("PASSWORD123", 8, 16, false, false)] // 缺少小写
    [InlineData("Password", 8, 16, false, false)] // 缺少数字
    [InlineData("Pass123", 8, 16, false, false)] // 太短
    [InlineData("Password123", 8, 16, true, false)] // 缺少特殊字符
    public void Password_ShouldValidatePasswordStrength(string input, int minLength, int maxLength, bool requireSpecialChar, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Password(minLength, maxLength, requireSpecialChar)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("张", true)]
    [InlineData("李", true)]
    [InlineData("王", true)]
    [InlineData("a", false)]
    [InlineData("1", false)]
    [InlineData("@", false)]
    public void Chinese_ShouldMatchChineseCharacters(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .Chinese()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("张三", true)]
    [InlineData("李四", true)]
    [InlineData("王小明", true)]
    [InlineData("欧阳修", true)]
    [InlineData("张", false)] // 太短
    [InlineData("张三李四王", false)] // 太长
    [InlineData("John", false)] // 非中文
    public void ChineseName_ShouldValidateChineseNames(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .ChineseName()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    #endregion

    #region 文件和路径模式测试

    [Theory]
    [InlineData("document.txt", new[] { "txt", "doc" }, true)]
    [InlineData("image.jpg", new[] { "jpg", "png", "gif" }, true)]
    [InlineData("file.pdf", new[] { "txt", "doc" }, false)]
    [InlineData("file", new[] { "txt" }, false)] // 无扩展名
    [InlineData("file.txt", null, true)] // 无扩展名限制
    public void FileName_ShouldValidateFileNames(string input, string[]? extensions, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .FileName(extensions)
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData(@"C:\Users\<USER>\Documents\file.txt", true)]
    [InlineData(@"D:\Program Files\App\config.ini", true)]
    [InlineData(@"C:\", true)]
    [InlineData(@"/home/<USER>/file.txt", false)] // Unix路径
    [InlineData(@"relative\path\file.txt", false)] // 相对路径
    public void WindowsPath_ShouldValidateWindowsPaths(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfLine()
            .WindowsPath()
            .EndOfLine()
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
    }

    [Theory]
    [InlineData("/home/<USER>/file.txt", true)]
    [InlineData("/var/log/app.log", true)]
    [InlineData("/", true)]
    [InlineData(@"C:\Users\<USER>