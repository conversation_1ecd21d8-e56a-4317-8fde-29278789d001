using FluentAssertions;
using Xunit;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Tests;

/// <summary>
/// 扩展字符功能测试
/// </summary>
public class ExtendedCharsTests
{
    #region 国际化字符测试

    [Theory]
    [InlineData("你好", true)]
    [InlineData("世界", true)]
    [InlineData("Hello", false)]
    [InlineData("123", false)]
    public void ChineseCharacters_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .ChineseCharacters("中文字符")
            .OneOrMore("一个或多个")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("中文字符");
    }

    [Theory]
    [InlineData("こんにちは", true)]
    [InlineData("カタカナ", true)]
    [InlineData("Hello", false)]
    [InlineData("123", false)]
    public void JapaneseCharacters_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .JapaneseCharacters("日文字符")
            .OneOrMore("一个或多个")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("日文字符");
    }

    [Theory]
    [InlineData("안녕하세요", true)]
    [InlineData("Hello", false)]
    [InlineData("123", false)]
    public void KoreanCharacters_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .KoreanCharacters("韩文字符")
            .OneOrMore("一个或多个")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("韩文字符");
    }

    #endregion

    #region 字符类组合测试

    [Theory]
    [InlineData("A", true)]
    [InlineData("F", true)]
    [InlineData("a", true)]
    [InlineData("f", true)]
    [InlineData("0", true)]
    [InlineData("9", true)]
    [InlineData("G", false)]
    [InlineData("g", false)]
    public void HexDigitCharacter_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .HexDigitCharacter("十六进制数字")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("十六进制数字");
    }

    [Theory]
    [InlineData("0", true)]
    [InlineData("7", true)]
    [InlineData("8", false)]
    [InlineData("9", false)]
    [InlineData("a", false)]
    public void OctalDigitCharacter_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .OctalDigitCharacter("八进制数字")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("八进制数字");
    }

    [Theory]
    [InlineData("0", true)]
    [InlineData("1", true)]
    [InlineData("2", false)]
    [InlineData("a", false)]
    public void BinaryDigitCharacter_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .BinaryDigitCharacter("二进制数字")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("二进制数字");
    }

    #endregion

    #region 常用词组测试

    [Theory]
    [InlineData("user123", true)]
    [InlineData("testUser", true)]
    [InlineData("a12", true)]
    [InlineData("123user", false)] // 不以字母开头
    [InlineData("ab", false)] // 太短
    public void UsernamePattern_Alphanumeric_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfString("开始")
            .UsernamePattern("alphanumeric", "用户名")
            .EndOfString("结束")
            .Build(); // 确保完全匹配

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("用户名");
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid.email", false)]
    [InlineData("@domain.com", false)]
    public void EmailPattern_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .EmailPattern("邮箱格式")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("邮箱格式");
    }

    [Theory]
    [InlineData("************", true)]
    [InlineData("************", true)]
    [InlineData("123-45-7890", false)]
    [InlineData("abc-def-ghij", false)]
    public void PhonePattern_US_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .PhonePattern("us", "美式电话")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("美式电话");
    }

    [Theory]
    [InlineData("13812345678", true)]
    [InlineData("15987654321", true)]
    [InlineData("12345678901", false)] // 第二位不对
    [InlineData("1381234567", false)]  // 少一位
    public void PhonePattern_China_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .PhonePattern("china", "中国手机号")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("中国手机号");
    }

    [Theory]
    [InlineData("http://example.com", true)]
    [InlineData("https://test.org", true)]
    [InlineData("ftp://test.com", false)]
    [InlineData("invalid-url", false)]
    public void UrlPattern_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .UrlPattern("URL格式")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("URL格式");
    }

    #endregion

    #region 编程语言标识符测试

    [Theory]
    [InlineData("myVariable", true)]
    [InlineData("_private", true)]
    [InlineData("@keyword", true)]
    [InlineData("123invalid", false)]
    [InlineData("my-var", false)]
    public void ProgrammingIdentifier_CSharp_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfString("开始")
            .ProgrammingIdentifier("csharp", "C#标识符")
            .EndOfString("结束")
            .Build(); // 确保完全匹配

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("C#标识符");
    }

    [Theory]
    [InlineData("myVariable", true)]
    [InlineData("_private", true)]
    [InlineData("$jquery", true)]
    [InlineData("123invalid", false)]
    [InlineData("my-var", false)]
    public void ProgrammingIdentifier_JavaScript_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfString("开始")
            .ProgrammingIdentifier("javascript", "JS标识符")
            .EndOfString("结束")
            .Build(); // 确保完全匹配

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("JS标识符");
    }

    [Theory]
    [InlineData("my_variable", true)]
    [InlineData("_private", true)]
    [InlineData("123invalid", false)]
    [InlineData("my-var", false)]
    public void ProgrammingIdentifier_Python_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .StartOfString("开始")
            .ProgrammingIdentifier("python", "Python标识符")
            .EndOfString("结束")
            .Build(); // 确保完全匹配

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("Python标识符");
    }

    #endregion

    #region 字符范围和集合测试

    [Theory]
    [InlineData("a", true)]
    [InlineData("m", true)]
    [InlineData("z", true)]
    [InlineData("A", false)]
    [InlineData("1", false)]
    public void CharacterRange_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .CharacterRange('a', 'z', "小写字母范围")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("小写字母范围");
    }

    [Theory]
    [InlineData("a", true)]
    [InlineData("e", true)]
    [InlineData("i", true)]
    [InlineData("b", false)]
    [InlineData("1", false)]
    public void CharacterSet_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .CharacterSet("aeiou", "元音字母")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("元音字母");
    }

    [Theory]
    [InlineData("b", true)]
    [InlineData("c", true)]
    [InlineData("d", true)]
    [InlineData("a", false)]
    [InlineData("e", false)]
    public void NegatedCharacterSet_ShouldMatchCorrectly(string input, bool expected)
    {
        // Arrange & Act
        var validator = YRegexBuilder.Create()
            .NegatedCharacterSet("aeiou", "非元音字母")
            .Build();

        // Assert
        validator.IsMatch(input).Should().Be(expected);
        validator.Description.Should().Contain("非元音字母");
    }

    #endregion
}
