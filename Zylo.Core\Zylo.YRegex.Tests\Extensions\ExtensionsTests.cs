using FluentAssertions;
using PCRE;
using Zylo.YRegex.Builders;
using Zylo.YRegex.Extensions;

namespace Zylo.YRegex.Tests.Extensions;

/// <summary>
/// 扩展功能测试
/// </summary>
public class ExtensionsTests
{
    #region PCRE 扩展测试

    [Fact]
    public void CompilePcre_ShouldCreatePcreRegex()
    {
        // Arrange
        var pattern = @"\d+";

        // Act
        var pcreRegex = pattern.CompilePcre();

        // Assert
        pcreRegex.Should().NotBeNull();
        pcreRegex.IsMatch("123").Should().BeTrue();
        pcreRegex.IsMatch("abc").Should().BeFalse();
    }

    [Fact]
    public void CompilePcreWithJit_ShouldCreateOptimizedRegex()
    {
        // Arrange
        var pattern = @"\d+";

        // Act
        var pcreRegex = pattern.CompilePcreWithJit();

        // Assert
        pcreRegex.Should().NotBeNull();
        pcreRegex.IsMatch("123").Should().BeTrue();
        pcreRegex.IsMatch("abc").Should().BeFalse();
    }

    [Fact]
    public void YPcreMatch_ShouldReturnMatchResult()
    {
        // Arrange
        var input = "abc123def";
        var pattern = @"\d+";

        // Act
        var match = input.YPcreMatch(pattern);

        // Assert
        match.Should().NotBeNull();
        match!.Success.Should().BeTrue();
        match.Value.Should().Be("123");
    }

    [Fact]
    public void YPcreMatches_ShouldReturnAllMatches()
    {
        // Arrange
        var input = "abc123def456ghi";
        var pattern = @"\d+";

        // Act
        var matches = input.YPcreMatches(pattern).ToList();

        // Assert
        matches.Should().HaveCount(2);
        matches[0].Value.Should().Be("123");
        matches[1].Value.Should().Be("456");
    }

    [Fact]
    public void YPcreIsMatch_ShouldReturnBooleanResult()
    {
        // Arrange
        var input = "abc123def";
        var pattern = @"\d+";

        // Act & Assert
        input.YPcreIsMatch(pattern).Should().BeTrue();
        "abcdef".YPcreIsMatch(pattern).Should().BeFalse();
    }

    [Fact]
    public void YPcreReplace_ShouldReplaceMatches()
    {
        // Arrange
        var input = "abc123def456ghi";
        var pattern = @"\d+";
        var replacement = "XXX";

        // Act
        var result = input.YPcreReplace(pattern, replacement);

        // Assert
        result.Should().Be("abcXXXdefXXXghi");
    }

    [Fact]
    public void YPcreSplit_ShouldSplitString()
    {
        // Arrange
        var input = "abc123def456ghi";
        var pattern = @"\d+";

        // Act
        var parts = input.YPcreSplit(pattern);

        // Assert
        parts.Should().HaveCount(3);
        parts[0].Should().Be("abc");
        parts[1].Should().Be("def");
        parts[2].Should().Be("ghi");
    }

    [Fact]
    public void CreateRecursivePattern_ShouldCreateValidPattern()
    {
        // Arrange & Act
        var pattern = YPcreExtensions.CreateRecursivePattern('(', ')');

        // Assert
        pattern.Should().NotBeEmpty();
        pattern.Should().Contain(@"\(");
        pattern.Should().Contain(@"\)");
        pattern.Should().Contain("(?R)");
    }

    [Fact]
    public void CreateConditionalPattern_ShouldCreateValidPattern()
    {
        // Arrange & Act
        var pattern = YPcreExtensions.CreateConditionalPattern(@"(\d+)", @"[a-z]+", @"\d+");

        // Assert
        pattern.Should().NotBeEmpty();
        pattern.Should().Contain("(?(1)");
        pattern.Should().Contain(@"(\d+)");
        pattern.Should().Contain(@"[a-z]+");
    }

    [Fact]
    public void ToPcreRegex_ShouldConvertValidator()
    {
        // Arrange
        var validator = YRegexBuilder.Create().Digits(3).Build();

        // Act
        var pcreRegex = validator.ToPcreRegex();

        // Assert
        pcreRegex.Should().NotBeNull();
        pcreRegex.IsMatch("123").Should().BeTrue();
        pcreRegex.IsMatch("12").Should().BeFalse();
    }

    [Fact]
    public void PcreIsMatch_OnValidator_ShouldWork()
    {
        // Arrange
        var validator = YRegexBuilder.Create().Email().Build();

        // Act & Assert
        validator.PcreIsMatch("<EMAIL>").Should().BeTrue();
        validator.PcreIsMatch("invalid-email").Should().BeFalse();
    }

    #endregion

    #region RegExtract 扩展测试

    [Fact]
    public void YExtractEmails_ShouldExtractEmailAddresses()
    {
        // Arrange
        var input = "联系方式：<EMAIL> 或 <EMAIL>";

        // Act
        var emails = input.YExtractEmails().ToList();

        // Assert
        emails.Should().HaveCount(2);
        emails.Should().Contain("<EMAIL>");
        emails.Should().Contain("<EMAIL>");
    }

    [Fact]
    public void YExtractPhones_ShouldExtractPhoneNumbers()
    {
        // Arrange
        var input = "电话：13812345678 或 15987654321";

        // Act
        var phones = input.YExtractPhones().ToList();

        // Assert
        phones.Should().HaveCount(2);
        phones.Should().Contain("13812345678");
        phones.Should().Contain("15987654321");
    }

    [Fact]
    public void YExtractUrls_ShouldExtractUrls()
    {
        // Arrange
        var input = "访问 https://www.example.com 或 http://domain.org";

        // Act
        var urls = input.YExtractUrls().ToList();

        // Assert
        urls.Should().HaveCount(2);
        urls.Should().Contain("https://www.example.com");
        urls.Should().Contain("http://domain.org");
    }

    [Fact]
    public void YExtractIPv4_ShouldExtractIPAddresses()
    {
        // Arrange
        var input = "服务器：*********** 和 ********";

        // Act
        var ips = input.YExtractIPv4().ToList();

        // Assert
        ips.Should().HaveCount(2);
        ips.Should().Contain("***********");
        ips.Should().Contain("********");
    }

    [Fact]
    public void YExtractDates_ShouldExtractDates()
    {
        // Arrange
        var input = "日期：2024-03-15 和 2024-12-31";

        // Act
        var dates = input.YExtractDates().ToList();

        // Assert
        dates.Should().HaveCount(2);
        dates[0].Year.Should().Be("2024");
        dates[0].Month.Should().Be("03");
        dates[0].Day.Should().Be("15");
        dates[1].Year.Should().Be("2024");
        dates[1].Month.Should().Be("12");
        dates[1].Day.Should().Be("31");
    }

    [Fact]
    public void YExtractTimes_ShouldExtractTimes()
    {
        // Arrange
        var input = "时间：10:30:45 和 23:59:59";

        // Act
        var times = input.YExtractTimes().ToList();

        // Assert
        times.Should().HaveCount(2);
        times[0].Hour.Should().Be("10");
        times[0].Minute.Should().Be("30");
        times[0].Second.Should().Be("45");
        times[1].Hour.Should().Be("23");
        times[1].Minute.Should().Be("59");
        times[1].Second.Should().Be("59");
    }

    [Fact]
    public void YExtractIntegers_ShouldExtractIntegers()
    {
        // Arrange
        var input = "数字：123, -456, 789";

        // Act
        var integers = input.YExtractIntegers().ToList();

        // Assert
        integers.Should().HaveCount(3);
        integers.Should().Contain(123);
        integers.Should().Contain(-456);
        integers.Should().Contain(789);
    }

    [Fact]
    public void YExtractIntegers_WithoutNegative_ShouldOnlyExtractPositive()
    {
        // Arrange
        var input = "数字：123, -456, 789";

        // Act
        var integers = input.YExtractIntegers(allowNegative: false).ToList();

        // Assert
        integers.Should().HaveCount(2);
        integers.Should().Contain(123);
        integers.Should().Contain(789);
        integers.Should().NotContain(-456);
    }

    [Fact]
    public void YExtractDecimals_ShouldExtractDecimals()
    {
        // Arrange
        var input = "价格：123.45, -67.89, 100";

        // Act
        var decimals = input.YExtractDecimals().ToList();

        // Assert
        decimals.Should().HaveCount(3);
        decimals.Should().Contain(123.45m);
        decimals.Should().Contain(-67.89m);
        decimals.Should().Contain(100m);
    }

    [Fact]
    public void YExtractCurrency_ShouldExtractCurrencyAmounts()
    {
        // Arrange
        var input = "价格：¥100.50 和 ¥200";

        // Act
        var currencies = input.YExtractCurrency("¥").ToList();

        // Assert
        currencies.Should().HaveCount(2);
        currencies[0].Currency.Should().Be("¥");
        currencies[0].Amount.Should().Be("100.50");
        currencies[1].Currency.Should().Be("¥");
        currencies[1].Amount.Should().Be("200");
    }

    [Fact]
    public void YExtractKeyValues_ShouldExtractKeyValuePairs()
    {
        // Arrange
        var input = "配置：name=test, port=8080, debug=true";

        // Act
        var keyValues = input.YExtractKeyValues().ToList();

        // Assert
        keyValues.Should().HaveCount(3);
        keyValues.Should().Contain(kv => kv.Key == "name" && kv.Value == "test");
        keyValues.Should().Contain(kv => kv.Key == "port" && kv.Value == "8080");
        keyValues.Should().Contain(kv => kv.Key == "debug" && kv.Value == "true");
    }

    [Fact]
    public void YExtractHtmlTags_ShouldExtractHtmlTags()
    {
        // Arrange
        var input = "<div class='test'>内容</div><span>文本</span>";

        // Act
        var tags = input.YExtractHtmlTags().ToList();

        // Assert
        tags.Should().HaveCount(2);
        tags[0].Tag.Should().Be("div");
        tags[0].Attributes.Should().Be("class='test'");
        tags[0].Content.Should().Be("内容");
        tags[1].Tag.Should().Be("span");
        tags[1].Content.Should().Be("文本");
    }

    [Fact]
    public void YExtractJsonFields_ShouldExtractJsonFields()
    {
        // Arrange
        var input = @"{""name"": ""test"", ""age"": ""25"", ""city"": ""Beijing""}";

        // Act
        var fields = input.YExtractJsonFields().ToList();

        // Assert
        fields.Should().HaveCount(3);
        fields.Should().Contain(f => f.Key == "name" && f.Value == "test");
        fields.Should().Contain(f => f.Key == "age" && f.Value == "25");
        fields.Should().Contain(f => f.Key == "city" && f.Value == "Beijing");
    }

    #endregion

    #region 数据模型测试

    [Fact]
    public void DateData_ToDateTime_ShouldConvertCorrectly()
    {
        // Arrange
        var dateData = new YRegExtractExtensions.DateData
        {
            Year = "2024",
            Month = "03",
            Day = "15"
        };

        // Act
        var dateTime = dateData.ToDateTime();

        // Assert
        dateTime.Should().Be(new DateTime(2024, 3, 15));
    }

    [Fact]
    public void TimeData_ToTimeSpan_ShouldConvertCorrectly()
    {
        // Arrange
        var timeData = new YRegExtractExtensions.TimeData
        {
            Hour = "10",
            Minute = "30",
            Second = "45"
        };

        // Act
        var timeSpan = timeData.ToTimeSpan();

        // Assert
        timeSpan.Should().Be(new TimeSpan(10, 30, 45));
    }

    [Fact]
    public void CurrencyData_GetAmount_ShouldConvertCorrectly()
    {
        // Arrange
        var currencyData = new YRegExtractExtensions.CurrencyData
        {
            Currency = "¥",
            Amount = "123.45"
        };

        // Act
        var amount = currencyData.GetAmount();

        // Assert
        amount.Should().Be(123.45m);
    }

    #endregion

    #region 错误处理测试

    [Fact]
    public void YExtract_WithInvalidPattern_ShouldReturnNull()
    {
        // Arrange
        var input = "test";
        var invalidPattern = "["; // 无效的正则表达式

        // Act
        var result = input.YExtract<YRegExtractExtensions.EmailData>(invalidPattern);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void YExtractMany_WithInvalidPattern_ShouldReturnEmpty()
    {
        // Arrange
        var input = "test";
        var invalidPattern = "["; // 无效的正则表达式

        // Act
        var result = input.YExtractMany<YRegExtractExtensions.EmailData>(invalidPattern);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void DateData_ToDateTime_WithInvalidData_ShouldReturnMinValue()
    {
        // Arrange
        var dateData = new YRegExtractExtensions.DateData
        {
            Year = "invalid",
            Month = "03",
            Day = "15"
        };

        // Act
        var dateTime = dateData.ToDateTime();

        // Assert
        dateTime.Should().Be(DateTime.MinValue);
    }

    [Fact]
    public void TimeData_ToTimeSpan_WithInvalidData_ShouldReturnZero()
    {
        // Arrange
        var timeData = new YRegExtractExtensions.TimeData
        {
            Hour = "invalid",
            Minute = "30",
            Second = "45"
        };

        // Act
        var timeSpan = timeData.ToTimeSpan();

        // Assert
        timeSpan.Should().Be(TimeSpan.Zero);
    }

    #endregion
}
