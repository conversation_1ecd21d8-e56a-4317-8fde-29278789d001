using FluentAssertions;
using System.Text.RegularExpressions;
using Zylo.YRegex.Core;
using Zylo.YRegex.Implementations;
using Zylo.YRegex.Validators;

namespace Zylo.YRegex.Tests.Validators;

/// <summary>
/// YRegexValidator 功能测试
/// </summary>
public class YRegexValidatorTests
{
    #region 基础功能测试

    [Fact]
    public void Constructor_WithValidPattern_ShouldCreateValidator()
    {
        // Arrange
        var pattern = @"\d+";
        var description = "数字模式";

        // Act
        var validator = new YRegexValidator(pattern, description);

        // Assert
        validator.Should().NotBeNull();
        validator.Pattern.Should().Be(pattern);
        validator.Description.Should().Be(description);
    }

    [Fact]
    public void Constructor_WithEmptyPattern_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new YRegexValidator("", "描述");
        act.Should().Throw<ArgumentException>()
           .WithMessage("*正则表达式模式不能为空*");
    }

    [Fact]
    public void Constructor_WithNullPattern_ShouldThrowException()
    {
        // Act & Assert
        var act = () => new YRegexValidator(null!, "描述");
        act.Should().Throw<ArgumentException>()
           .WithMessage("*正则表达式模式不能为空*");
    }

    [Fact]
    public void IsMatch_WithValidInput_ShouldReturnCorrectResult()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act & Assert
        validator.IsMatch("123").Should().BeTrue();
        validator.IsMatch("abc").Should().BeFalse();
        validator.IsMatch("123abc").Should().BeTrue(); // 部分匹配
    }

    [Fact]
    public void IsMatch_WithNullInput_ShouldReturnFalse()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act & Assert
        validator.IsMatch(null!).Should().BeFalse();
    }

    #endregion

    #region 匹配功能测试

    [Fact]
    public void Match_WithValidInput_ShouldReturnMatch()
    {
        // Arrange
        var validator = new YRegexValidator(@"(\d+)", "数字组");

        // Act
        var match = validator.Match("abc123def");

        // Assert
        match.Success.Should().BeTrue();
        match.Value.Should().Be("123");
        match.Groups[1].Value.Should().Be("123");
    }

    [Fact]
    public void Match_WithInvalidInput_ShouldReturnEmptyMatch()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act
        var match = validator.Match("abc");

        // Assert
        match.Success.Should().BeFalse();
        match.Should().Be(Match.Empty);
    }

    [Fact]
    public void Match_WithNullInput_ShouldReturnEmptyMatch()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act
        var match = validator.Match(null!);

        // Assert
        match.Success.Should().BeFalse();
        match.Should().Be(Match.Empty);
    }

    [Fact]
    public void Matches_WithValidInput_ShouldReturnAllMatches()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act
        var matches = validator.Matches("abc123def456ghi");

        // Assert
        matches.Should().HaveCount(2);
        matches[0].Value.Should().Be("123");
        matches[1].Value.Should().Be("456");
    }

    [Fact]
    public void Matches_WithNullInput_ShouldReturnEmptyCollection()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act
        var matches = validator.Matches(null!);

        // Assert
        matches.Should().HaveCount(0);
    }

    #endregion

    #region 额外验证器测试

    [Fact]
    public void AddValidator_ShouldAddCustomValidation()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");
        var customValidator = new Func<string, bool>(s => int.Parse(s) > 100);

        // Act
        validator.AddValidator(customValidator);

        // Assert
        validator.IsMatch("50").Should().BeFalse(); // 正则匹配但自定义验证失败
        validator.IsMatch("150").Should().BeTrue(); // 正则匹配且自定义验证通过
        validator.IsMatch("abc").Should().BeFalse(); // 正则不匹配
    }

    [Fact]
    public void AddValidator_WithNullValidator_ShouldNotThrow()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");

        // Act & Assert
        var act = () => validator.AddValidator(null!);
        act.Should().NotThrow();
    }

    [Fact]
    public void AddValidator_ShouldReturnSameInstance()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");
        var customValidator = new Func<string, bool>(s => true);

        // Act
        var result = validator.AddValidator(customValidator);

        // Assert
        result.Should().BeSameAs(validator);
    }

    [Fact]
    public void MultipleValidators_ShouldAllBeApplied()
    {
        // Arrange
        var validator = new YRegexValidator(@"\d+", "数字");
        var validator1 = new Func<string, bool>(s => int.Parse(s) > 10);
        var validator2 = new Func<string, bool>(s => int.Parse(s) < 100);

        // Act
        validator.AddValidator(validator1).AddValidator(validator2);

        // Assert
        validator.IsMatch("5").Should().BeFalse(); // 小于10
        validator.IsMatch("50").Should().BeTrue(); // 在10-100之间
        validator.IsMatch("150").Should().BeFalse(); // 大于100
    }

    #endregion

    #region 属性和方法测试

    [Fact]
    public void GetDescription_ShouldReturnDescription()
    {
        // Arrange
        var description = "测试描述";
        var validator = new YRegexValidator(@"\d+", description);

        // Act & Assert
        validator.GetDescription().Should().Be(description);
        validator.Description.Should().Be(description); // 属性访问
    }

    [Fact]
    public void GetPattern_ShouldReturnPattern()
    {
        // Arrange
        var pattern = @"\d+";
        var validator = new YRegexValidator(pattern, "描述");

        // Act & Assert
        validator.GetPattern().Should().Be(pattern);
        validator.Pattern.Should().Be(pattern); // 属性访问
    }

    [Fact]
    public void GetOptions_ShouldReturnRegexOptions()
    {
        // Arrange
        var options = RegexOptions.IgnoreCase | RegexOptions.Multiline;
        var validator = new YRegexValidator(@"\d+", "描述", options);

        // Act & Assert
        validator.GetOptions().Should().Be(options);
    }

    [Fact]
    public void GetTimeout_ShouldReturnTimeout()
    {
        // Arrange
        var timeout = TimeSpan.FromSeconds(10);
        var validator = new YRegexValidator(@"\d+", "描述", timeout: timeout);

        // Act & Assert
        validator.GetTimeout().Should().Be(timeout);
    }

    #endregion

    #region 隐式转换测试

    [Fact]
    public void ImplicitConversionToString_ShouldReturnPattern()
    {
        // Arrange
        var pattern = @"\d+";
        var validator = new YRegexValidator(pattern, "描述");

        // Act
        string result = validator;

        // Assert
        result.Should().Be(pattern);
    }

    [Fact]
    public void ImplicitConversionToRegex_ShouldReturnRegex()
    {
        // Arrange
        var pattern = @"\d+";
        var validator = new YRegexValidator(pattern, "描述");

        // Act
        Regex result = validator;

        // Assert
        result.Should().NotBeNull();
        result.ToString().Should().Be(pattern);
    }

    [Fact]
    public void ImplicitConversionWithNullValidator_ShouldReturnEmpty()
    {
        // Arrange
        YRegexValidator? validator = null;

        // Act
        string stringResult = validator!;
        Regex regexResult = validator!;

        // Assert
        stringResult.Should().BeEmpty();
        regexResult.ToString().Should().BeEmpty();
    }

    #endregion

    #region ToString 测试

    [Fact]
    public void ToString_WithDescription_ShouldIncludeDescription()
    {
        // Arrange
        var pattern = @"\d+";
        var description = "数字模式";
        var validator = new YRegexValidator(pattern, description);

        // Act
        var result = validator.ToString();

        // Assert
        result.Should().Contain("YRegexValidator");
        result.Should().Contain(description);
        result.Should().Contain(pattern);
    }

    [Fact]
    public void ToString_WithoutDescription_ShouldOnlyIncludePattern()
    {
        // Arrange
        var pattern = @"\d+";
        var validator = new YRegexValidator(pattern);

        // Act
        var result = validator.ToString();

        // Assert
        result.Should().Contain("YRegexValidator");
        result.Should().Contain(pattern);
    }

    #endregion

    #region 上下文集成测试

    [Fact]
    public void Constructor_WithContext_ShouldUseContextSettings()
    {
        // Arrange
        var options = new YRegexOptions
        {
            DefaultRegexOptions = RegexOptions.IgnoreCase,
            DefaultTimeout = TimeSpan.FromSeconds(5)
        };
        var context = new YRegexContext(options);
        var pattern = @"[a-z]+";

        // Act
        var validator = new YRegexValidator(pattern, "测试", context: context);

        // Assert
        validator.IsMatch("ABC").Should().BeTrue(); // 应该忽略大小写
        validator.GetTimeout().Should().Be(TimeSpan.FromSeconds(5));
    }

    #endregion

    #region 异常处理测试

    [Fact]
    public void IsMatch_WithTimeoutException_ShouldReturnFalse()
    {
        // Arrange - 创建一个可能导致超时的复杂模式
        var validator = new YRegexValidator(@"(a+)+b", "复杂模式", timeout: TimeSpan.FromMilliseconds(1));

        // Act & Assert
        try
        {
            var result = validator.IsMatch("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaac");
            result.Should().BeFalse();
        }
        catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
        {
            // 超时异常是预期的，测试通过
            Assert.True(true);
        }
    }

    [Fact]
    public void Match_WithTimeoutException_ShouldReturnEmptyMatch()
    {
        // Arrange
        var validator = new YRegexValidator(@"(a+)+b", "复杂模式", timeout: TimeSpan.FromMilliseconds(1));

        // Act & Assert
        try
        {
            var match = validator.Match("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaac");
            match.Should().Be(Match.Empty);
        }
        catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
        {
            // 超时异常是预期的，测试通过
            Assert.True(true);
        }
    }

    [Fact]
    public void Matches_WithTimeoutException_ShouldReturnEmptyCollection()
    {
        // Arrange
        var validator = new YRegexValidator(@"(a+)+b", "复杂模式", timeout: TimeSpan.FromMilliseconds(1));

        // Act & Assert
        try
        {
            var matches = validator.Matches("aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaac");
            matches.Should().HaveCount(0);
        }
        catch (System.Text.RegularExpressions.RegexMatchTimeoutException)
        {
            // 超时异常是预期的，测试通过
            Assert.True(true);
        }
    }

    #endregion
}
