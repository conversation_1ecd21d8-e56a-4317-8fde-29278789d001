namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 高级组合功能
/// 支持复杂自定义正则表达式的构建
/// </summary>
public partial class YRegexBuilder
{
    #region 高级组合方法

    /// <summary>
    /// 条件匹配：根据条件选择不同的模式
    /// </summary>
    /// <param name="condition">条件构建器</param>
    /// <param name="truePattern">条件为真时的模式</param>
    /// <param name="falsePattern">条件为假时的模式</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Conditional(
        Action<YRegexBuilder> condition,
        Action<YRegexBuilder> truePattern,
        Action<YRegexBuilder> falsePattern,
        string description = "")
    {
        var conditionBuilder = new YRegexBuilder();
        condition(conditionBuilder);

        var trueBuilder = new YRegexBuilder();
        truePattern(trueBuilder);

        var falseBuilder = new YRegexBuilder();
        falsePattern(falseBuilder);

        // 构建条件表达式：(condition)?(truePattern|falsePattern)
        _pattern.Append($"({conditionBuilder.GetCurrentPattern()})?");
        _pattern.Append($"(?(1){trueBuilder.GetCurrentPattern()}|{falseBuilder.GetCurrentPattern()})");

        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"条件匹配({conditionBuilder.GetCurrentDescription()})" : description);

        return this;
    }

    /// <summary>
    /// 递归模式：匹配嵌套结构
    /// </summary>
    /// <param name="openPattern">开始模式</param>
    /// <param name="closePattern">结束模式</param>
    /// <param name="contentPattern">内容模式</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Recursive(
        string openPattern,
        string closePattern,
        string contentPattern = @"[^()]*",
        string description = "")
    {
        // 递归模式：open(content|(?R))*close
        var escapedOpen = System.Text.RegularExpressions.Regex.Escape(openPattern);
        var escapedClose = System.Text.RegularExpressions.Regex.Escape(closePattern);

        _pattern.Append($"{escapedOpen}(?:{contentPattern}|(?R))*{escapedClose}");
        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"递归模式[{openPattern}...{closePattern}]" : description);

        return this;
    }

    /// <summary>
    /// 平衡组：匹配平衡的嵌套结构（.NET 特有）
    /// </summary>
    /// <param name="openChar">开始字符</param>
    /// <param name="closeChar">结束字符</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder BalancedGroup(char openChar, char closeChar, string description = "")
    {
        // .NET 平衡组语法
        var pattern = $@"{System.Text.RegularExpressions.Regex.Escape(openChar.ToString())}" +
                     $@"(?>[^{openChar}{closeChar}]|" +
                     $@"(?<open>{System.Text.RegularExpressions.Regex.Escape(openChar.ToString())})|" +
                     $@"(?<-open>{System.Text.RegularExpressions.Regex.Escape(closeChar.ToString())}))*" +
                     $@"(?(open)(?!)){System.Text.RegularExpressions.Regex.Escape(closeChar.ToString())}";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"平衡组[{openChar}...{closeChar}]" : description);

        return this;
    }

    /// <summary>
    /// 自定义模式：直接插入正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="description">描述</param>
    /// <param name="escape">是否转义特殊字符</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Custom(string pattern, string description = "", bool escape = false)
    {
        if (escape)
        {
            pattern = System.Text.RegularExpressions.Regex.Escape(pattern);
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "自定义模式" : description);

        return this;
    }

    /// <summary>
    /// 引用其他构建器的结果
    /// </summary>
    /// <param name="otherBuilder">其他构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Reference(YRegexBuilder otherBuilder, string description = "")
    {
        _pattern.Append(otherBuilder.GetCurrentPattern());
        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"引用({otherBuilder.GetCurrentDescription()})" : description);

        return this;
    }

    #endregion

    #region 高级字符类

    /// <summary>
    /// Unicode 字符类
    /// </summary>
    /// <param name="category">Unicode 类别</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UnicodeCategory(string category, string description = "")
    {
        _pattern.Append($@"\p{{{category}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"Unicode类别[{category}]" : description);
        return this;
    }

    /// <summary>
    /// 非 Unicode 字符类
    /// </summary>
    /// <param name="category">Unicode 类别</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NotUnicodeCategory(string category, string description = "")
    {
        _pattern.Append($@"\P{{{category}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"非Unicode类别[{category}]" : description);
        return this;
    }

    /// <summary>
    /// 命名字符类
    /// </summary>
    /// <param name="name">字符类名称</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NamedCharacterClass(string name, string description = "")
    {
        _pattern.Append($@"\p{{{name}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"命名字符类[{name}]" : description);
        return this;
    }

    #endregion

    #region 高级锚点

    /// <summary>
    /// 字符串开始（不是行开始）- 使用 \A 锚点
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder StartOfStringAbsolute(string description = "")
    {
        _pattern.Append(@"\A");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字符串开始(绝对)" : description);
        return this;
    }

    /// <summary>
    /// 字符串结束（不是行结束）- 使用 \Z 锚点
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EndOfStringAbsolute(string description = "")
    {
        _pattern.Append(@"\Z");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字符串结束(绝对)" : description);
        return this;
    }

    /// <summary>
    /// 绝对字符串结束
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AbsoluteEndOfString(string description = "")
    {
        _pattern.Append(@"\z");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "绝对字符串结束" : description);
        return this;
    }

    /// <summary>
    /// 上一个匹配的结束位置
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PreviousMatchEnd(string description = "")
    {
        _pattern.Append(@"\G");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "上一个匹配结束" : description);
        return this;
    }

    #endregion

    #region 高级引用

    /// <summary>
    /// 反向引用：引用之前的捕获组
    /// </summary>
    /// <param name="groupNumber">组号</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder BackReference(int groupNumber, string description = "")
    {
        _pattern.Append($@"\{groupNumber}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"反向引用[组{groupNumber}]" : description);
        return this;
    }

    /// <summary>
    /// 命名反向引用：引用命名捕获组
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NamedBackReference(string groupName, string description = "")
    {
        _pattern.Append($@"\k<{groupName}>");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"命名反向引用[{groupName}]" : description);
        return this;
    }

    #endregion

    #region 高级选项

    /// <summary>
    /// 内联选项：在模式中设置选项
    /// </summary>
    /// <param name="options">选项字符串</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder InlineOptions(string options, string description = "")
    {
        _pattern.Append($"(?{options})");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"内联选项[{options}]" : description);
        return this;
    }

    /// <summary>
    /// 忽略大小写（局部）
    /// </summary>
    /// <param name="builder">构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IgnoreCase(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?i:");
        var innerBuilder = new YRegexBuilder();
        builder(innerBuilder);
        _pattern.Append(innerBuilder.GetCurrentPattern());
        _pattern.Append(")");

        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"忽略大小写({innerBuilder.GetCurrentDescription()})" : description);

        return this;
    }

    /// <summary>
    /// 多行模式（局部）
    /// </summary>
    /// <param name="builder">构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Multiline(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?m:");
        var innerBuilder = new YRegexBuilder();
        builder(innerBuilder);
        _pattern.Append(innerBuilder.GetCurrentPattern());
        _pattern.Append(")");

        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"多行模式({innerBuilder.GetCurrentDescription()})" : description);

        return this;
    }

    #endregion

    #region 原子组和占有量词

    /// <summary>
    /// 原子组：防止回溯
    /// </summary>
    /// <param name="builder">构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AtomicGroup(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?>");
        var innerBuilder = new YRegexBuilder();
        builder(innerBuilder);
        _pattern.Append(innerBuilder.GetCurrentPattern());
        _pattern.Append(")");

        _descriptions.Add(string.IsNullOrEmpty(description) ?
            $"原子组({innerBuilder.GetCurrentDescription()})" : description);

        return this;
    }

    #endregion

    #region RegG 中文分组方法包装

    /// <summary>
    /// 平衡组：匹配平衡的括号或标签 (中文包装方法)
    /// </summary>
    /// <param name="openChar">开始字符</param>
    /// <param name="closeChar">结束字符</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG平衡组(char openChar, char closeChar, string description = "") => BalancedGroup(openChar, closeChar, description);

    /// <summary>
    /// 反向引用：引用之前的捕获组 (中文包装方法)
    /// </summary>
    /// <param name="groupNumber">组号</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG反向引用(int groupNumber, string description = "") => BackReference(groupNumber, description);

    /// <summary>
    /// 命名反向引用：引用命名捕获组 (中文包装方法)
    /// </summary>
    /// <param name="groupName">组名</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG命名反向引用(string groupName, string description = "") => NamedBackReference(groupName, description);

    /// <summary>
    /// 原子组：防止回溯 (中文包装方法)
    /// </summary>
    /// <param name="builder">构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG原子组(Action<YRegexBuilder> builder, string description = "") => AtomicGroup(builder, description);

    /// <summary>
    /// 条件匹配：根据条件选择不同的模式 (中文包装方法)
    /// </summary>
    /// <param name="condition">条件构建器</param>
    /// <param name="truePattern">条件为真时的模式</param>
    /// <param name="falsePattern">条件为假时的模式</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG条件匹配(
        Action<YRegexBuilder> condition,
        Action<YRegexBuilder> truePattern,
        Action<YRegexBuilder> falsePattern,
        string description = "") => Conditional(condition, truePattern, falsePattern, description);

    /// <summary>
    /// 递归模式：匹配嵌套结构 (中文包装方法)
    /// </summary>
    /// <param name="openPattern">开始模式</param>
    /// <param name="closePattern">结束模式</param>
    /// <param name="contentPattern">内容模式</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegG递归模式(string openPattern, string closePattern, string contentPattern = @"[^()]*", string description = "") => Recursive(openPattern, closePattern, contentPattern, description);

    #endregion
}
