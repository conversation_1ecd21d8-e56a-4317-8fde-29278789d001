using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 基础字符方法
/// 包含数字、字母、符号等基础字符匹配功能
/// </summary>
public partial class YRegexBuilder
{
    #region 数字字符

    /// <summary>
    /// 匹配数字字符 [0-9]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Digit(string description = "")
    {
        _pattern.Append(@"\d");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定数量的数字字符
    /// </summary>
    /// <param name="count">数字个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Digits(int count, string description = "")
    {
        _pattern.Append($@"\d{{{count}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{count}位数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定范围的数字字符
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Digits(int min, int max, string description = "")
    {
        _pattern.Append($@"\d{{{min},{max}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{min}-{max}位数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配非数字字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NonDigit(string description = "")
    {
        _pattern.Append(@"\D");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "非数字" : description);
        return this;
    }

    #endregion

    #region 字母字符

    /// <summary>
    /// 匹配字母字符 [a-zA-Z]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Letter(string description = "")
    {
        _pattern.Append("[a-zA-Z]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字母" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定数量的字母字符
    /// </summary>
    /// <param name="count">字母个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Letters(int count, string description = "")
    {
        _pattern.Append($"[a-zA-Z]{{{count}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{count}位字母" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定范围的字母字符
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Letters(int min, int max, string description = "")
    {
        _pattern.Append($"[a-zA-Z]{{{min},{max}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{min}-{max}位字母" : description);
        return this;
    }

    /// <summary>
    /// 匹配小写字母 [a-z]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder LowerCase(string description = "")
    {
        _pattern.Append("[a-z]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "小写字母" : description);
        return this;
    }

    /// <summary>
    /// 匹配大写字母 [A-Z]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UpperCase(string description = "")
    {
        _pattern.Append("[A-Z]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "大写字母" : description);
        return this;
    }

    #endregion

    #region 字母数字字符

    /// <summary>
    /// 匹配字母数字字符 [a-zA-Z0-9]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AlphaNumeric(string description = "")
    {
        _pattern.Append(@"[a-zA-Z0-9]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字母数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定数量的字母数字字符
    /// </summary>
    /// <param name="count">字符个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AlphaNumeric(int count, string description = "")
    {
        _pattern.Append($@"[a-zA-Z0-9]{{{count}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{count}位字母数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配指定范围的字母数字字符
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AlphaNumeric(int min, int max, string description = "")
    {
        _pattern.Append($@"[a-zA-Z0-9]{{{min},{max}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{min}-{max}位字母数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配单词字符 [a-zA-Z0-9_]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder WordChar(string description = "")
    {
        _pattern.Append(@"\w");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "单词字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配非单词字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NonWordChar(string description = "")
    {
        _pattern.Append(@"\W");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "非单词字符" : description);
        return this;
    }

    #endregion

    #region 空白字符

    /// <summary>
    /// 匹配空白字符（空格、制表符、换行符等）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Whitespace(string description = "")
    {
        _pattern.Append(@"\s");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "空白字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配非空白字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NonWhitespace(string description = "")
    {
        _pattern.Append(@"\S");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "非空白字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配空格字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Space(string description = "")
    {
        _pattern.Append(" ");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "空格" : description);
        return this;
    }

    /// <summary>
    /// 匹配制表符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Tab(string description = "")
    {
        _pattern.Append(@"\t");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "制表符" : description);
        return this;
    }

    /// <summary>
    /// 匹配换行符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Newline(string description = "")
    {
        _pattern.Append(@"\r?\n");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "换行符" : description);
        return this;
    }

    #endregion

    #region 特殊字符

    /// <summary>
    /// 匹配点号 .
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Dot(string description = "")
    {
        _pattern.Append(@"\.");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "点号" : description);
        return this;
    }

    /// <summary>
    /// 匹配 @ 符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder At(string description = "")
    {
        _pattern.Append("@");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "@符号" : description);
        return this;
    }

    /// <summary>
    /// 匹配连字符 -
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Hyphen(string description = "")
    {
        _pattern.Append("-");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "连字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配下划线 _
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Underscore(string description = "")
    {
        _pattern.Append("_");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "下划线" : description);
        return this;
    }

    #endregion

    #region 字符集合

    /// <summary>
    /// 匹配字符集合中的任意一个字符
    /// </summary>
    /// <param name="chars">字符集合</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OneOf(string chars, string description = "")
    {
        if (!string.IsNullOrEmpty(chars))
        {
            var escapedChars = chars.Replace(@"\", @"\\")
                                   .Replace("]", @"\]")
                                   .Replace("^", @"\^")
                                   .Replace("-", @"\-");
            _pattern.Append($"[{escapedChars}]");
            _descriptions.Add(string.IsNullOrEmpty(description) ? $"字符集[{chars}]" : description);
        }
        return this;
    }

    /// <summary>
    /// 匹配不在字符集合中的任意字符
    /// </summary>
    /// <param name="chars">字符集合</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NoneOf(string chars, string description = "")
    {
        if (!string.IsNullOrEmpty(chars))
        {
            var escapedChars = chars.Replace(@"\", @"\\")
                                   .Replace("]", @"\]")
                                   .Replace("^", @"\^")
                                   .Replace("-", @"\-");
            _pattern.Append($"[^{escapedChars}]");
            _descriptions.Add(string.IsNullOrEmpty(description) ? $"非字符集[{chars}]" : description);
        }
        return this;
    }

    /// <summary>
    /// 匹配字符范围
    /// </summary>
    /// <param name="start">起始字符</param>
    /// <param name="end">结束字符</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Range(char start, char end, string description = "")
    {
        _pattern.Append($"[{start}-{end}]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"字符范围[{start}-{end}]" : description);
        return this;
    }

    #endregion

    #region RegZ 中文字符方法包装

    /// <summary>
    /// 匹配单个数字字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ数字(string description = "") => Digit(description);

    /// <summary>
    /// 匹配指定数量的数字字符 (中文包装方法)
    /// </summary>
    /// <param name="count">数字个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ数字(int count, string description = "") => Digits(count, description);

    /// <summary>
    /// 匹配指定范围的数字字符 (中文包装方法)
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ数字(int min, int max, string description = "") => Digits(min, max, description);

    /// <summary>
    /// 匹配非数字字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ非数字(string description = "") => NonDigit(description);

    /// <summary>
    /// 匹配单个字母字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母(string description = "") => Letter(description);

    /// <summary>
    /// 匹配指定数量的字母字符 (中文包装方法)
    /// </summary>
    /// <param name="count">字母个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母(int count, string description = "") => Letters(count, description);

    /// <summary>
    /// 匹配指定范围的字母字符 (中文包装方法)
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母(int min, int max, string description = "") => Letters(min, max, description);

    /// <summary>
    /// 匹配大写字母 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ大写字母(string description = "") => UpperCase(description);

    /// <summary>
    /// 匹配小写字母 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ小写字母(string description = "") => LowerCase(description);

    /// <summary>
    /// 匹配字母数字字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母数字(string description = "") => AlphaNumeric(description);

    /// <summary>
    /// 匹配指定数量的字母数字字符 (中文包装方法)
    /// </summary>
    /// <param name="count">字符个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母数字(int count, string description = "") => AlphaNumeric(count, description);

    /// <summary>
    /// 匹配指定范围的字母数字字符 (中文包装方法)
    /// </summary>
    /// <param name="min">最少个数</param>
    /// <param name="max">最多个数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ字母数字(int min, int max, string description = "") => AlphaNumeric(min, max, description);

    /// <summary>
    /// 匹配单词字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ单词字符(string description = "") => WordChar(description);

    /// <summary>
    /// 匹配非单词字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ非单词字符(string description = "") => NonWordChar(description);

    /// <summary>
    /// 匹配空白字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ空白字符(string description = "") => Whitespace(description);

    /// <summary>
    /// 匹配非空白字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ非空白字符(string description = "") => NonWhitespace(description);

    /// <summary>
    /// 匹配空格字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ空格(string description = "") => Space(description);

    /// <summary>
    /// 匹配制表符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ制表符(string description = "") => Tab(description);

    /// <summary>
    /// 匹配换行符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ换行符(string description = "") => Newline(description);

    /// <summary>
    /// 匹配任意字符 (中文包装方法)
    /// 注意：此方法匹配除换行符外的任意字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ任意字符(string description = "")
    {
        _pattern.Append('.');
        _descriptions.Add(string.IsNullOrEmpty(description) ? "任意字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配字面量文本 (中文包装方法)
    /// </summary>
    /// <param name="text">要匹配的文本</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ文本(string text, string description = "") => Literal(text, description);

    /// <summary>
    /// 匹配点号字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ点号(string description = "") => Dot(description);

    /// <summary>
    /// 匹配@符号 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ艾特符号(string description = "") => At(description);

    /// <summary>
    /// 匹配连字符 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ连字符(string description = "") => Hyphen(description);

    /// <summary>
    /// 匹配下划线 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegZ下划线(string description = "") => Underscore(description);

    #endregion
}
