using System.Text;
using System.Text.RegularExpressions;
using Zylo.YRegex.Core;
using Zylo.YRegex.Validators;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegex 核心构建器 - 企业级正则表达式构建工具
///
/// 提供流式API来构建复杂的正则表达式，支持：
/// - 🎯 直观的函数式API，告别复杂正则语法
/// - 🌍 完整的Unicode支持和国际化功能
/// - 🏛️ 60+专业领域验证（金融、技术、学术等）
/// - 📄 15+文档格式验证（JSON、XML、CSV等）
/// - 💻 15+编程语言语法支持
/// - 🚀 高性能缓存和优化
/// - 🧪 100%测试覆盖
///
/// 使用示例：
/// <code>
/// var phoneValidator = YRegexBuilder.Create()
///     .Digits(3, "区号")
///     .Dash("分隔符")
///     .Digits(3, "前缀")
///     .Dash("分隔符")
///     .Digits(4, "号码")
///     .Build();
///
/// Console.WriteLine(phoneValidator.IsMatch("************")); // True
/// Console.WriteLine(phoneValidator.GetDescription()); // "3位数字 + 分隔符 + 3位数字 + 分隔符 + 4位数字"
/// </code>
/// </summary>
public partial class YRegexBuilder
{
    #region 私有字段

    /// <summary>
    /// 正则表达式模式构建器
    /// 用于逐步构建完整的正则表达式模式
    /// </summary>
    private readonly StringBuilder _pattern;

    /// <summary>
    /// 描述信息列表
    /// 记录每个构建步骤的人类可读描述，用于生成最终的验证器描述
    /// </summary>
    private readonly List<string> _descriptions;

    /// <summary>
    /// 额外验证器列表
    /// 存储用户自定义的验证函数，在正则匹配之外提供额外的验证逻辑
    /// </summary>
    private readonly List<Func<string, bool>> _additionalValidators;

    /// <summary>
    /// 正则表达式选项
    /// 控制正则表达式的行为，如忽略大小写、多行模式等
    /// </summary>
    private RegexOptions _options;

    /// <summary>
    /// 超时时间
    /// 防止复杂正则表达式导致的性能问题，默认为5秒
    /// </summary>
    private TimeSpan _timeout;

    /// <summary>
    /// 正则表达式上下文
    /// 提供缓存、性能监控等高级功能的可选上下文
    /// </summary>
    private IYRegexContext? _context;

    #endregion

    #region 构造函数和工厂方法

    /// <summary>
    /// 私有构造函数
    ///
    /// 初始化YRegexBuilder的所有内部状态：
    /// - 创建空的模式构建器
    /// - 初始化描述列表
    /// - 设置默认选项和超时时间
    ///
    /// 使用私有构造函数确保只能通过Create()方法创建实例
    /// </summary>
    private YRegexBuilder()
    {
        // 初始化正则表达式模式构建器
        // StringBuilder提供高效的字符串拼接性能
        _pattern = new StringBuilder();

        // 初始化描述信息列表
        // 用于记录每个构建步骤的人类可读描述
        _descriptions = new List<string>();

        // 初始化额外验证器列表
        // 允许用户添加自定义验证逻辑
        _additionalValidators = new List<Func<string, bool>>();

        // 设置默认正则表达式选项
        // Compiled: 编译正则表达式以提高性能
        // CultureInvariant: 使用固定文化设置，确保跨文化一致性
        _options = RegexOptions.Compiled | RegexOptions.CultureInvariant;

        // 设置默认超时时间为5秒
        // 防止复杂正则表达式导致的性能问题和无限循环
        _timeout = TimeSpan.FromSeconds(5);
    }

    /// <summary>
    /// 创建新的构建器实例
    /// </summary>
    /// <returns>构建器实例</returns>
    public static YRegexBuilder Create()
    {
        return new YRegexBuilder();
    }

    /// <summary>
    /// 创建带上下文的构建器实例
    /// </summary>
    /// <param name="context">正则上下文</param>
    /// <returns>构建器实例</returns>
    public static YRegexBuilder Create(IYRegexContext context)
    {
        var builder = new YRegexBuilder();
        builder._context = context;
        builder._options = context.Options;
        builder._timeout = context.Timeout;
        return builder;
    }

    /// <summary>
    /// 添加原始正则表达式模式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Pattern(string pattern, string description = "")
    {
        if (!string.IsNullOrEmpty(pattern))
        {
            _pattern.Append(pattern);
            _descriptions.Add(string.IsNullOrEmpty(description) ? $"模式[{pattern}]" : description);
        }
        return this;
    }

    /// <summary>
    /// 添加字面量文本（会自动转义特殊字符）
    /// </summary>
    /// <param name="text">字面量文本</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Literal(string text, string description = "")
    {
        if (!string.IsNullOrEmpty(text))
        {
            var escapedText = Regex.Escape(text);
            _pattern.Append(escapedText);
            _descriptions.Add(string.IsNullOrEmpty(description) ? $"字面量[{text}]" : description);
        }
        return this;
    }

    /// <summary>
    /// 开始行匹配
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder StartOfLine(string description = "")
    {
        _pattern.Append("^");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "行开始" : description);
        return this;
    }

    /// <summary>
    /// 结束行匹配
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EndOfLine(string description = "")
    {
        _pattern.Append("$");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "行结束" : description);
        return this;
    }

    /// <summary>
    /// 开始字符串匹配
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder StartOfString(string description = "")
    {
        _pattern.Append(@"\A");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字符串开始" : description);
        return this;
    }

    /// <summary>
    /// 结束字符串匹配
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EndOfString(string description = "")
    {
        _pattern.Append(@"\z");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "字符串结束" : description);
        return this;
    }

    /// <summary>
    /// 单词边界
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder WordBoundary(string description = "")
    {
        _pattern.Append(@"\b");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "单词边界" : description);
        return this;
    }

    /// <summary>
    /// 非单词边界
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NonWordBoundary(string description = "")
    {
        _pattern.Append(@"\B");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "非单词边界" : description);
        return this;
    }

    /// <summary>
    /// 匹配任意字符（除换行符）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AnyChar(string description = "")
    {
        _pattern.Append(".");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "任意字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配任意字符（包括换行符）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AnyCharIncludingNewline(string description = "")
    {
        _pattern.Append(@"[\s\S]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "任意字符(含换行)" : description);
        return this;
    }

    /// <summary>
    /// 设置正则表达式选项
    /// </summary>
    /// <param name="options">正则表达式选项</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder WithOptions(RegexOptions options)
    {
        _options = options;
        return this;
    }

    /// <summary>
    /// 设置超时时间
    /// </summary>
    /// <param name="timeout">超时时间</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder WithTimeout(TimeSpan timeout)
    {
        _timeout = timeout;
        return this;
    }

    /// <summary>
    /// 添加额外验证器
    /// </summary>
    /// <param name="validator">验证函数</param>
    /// <param name="description">验证器描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AddValidator(Func<string, bool> validator, string description = "")
    {
        if (validator != null)
        {
            _additionalValidators.Add(validator);
            _descriptions.Add(string.IsNullOrEmpty(description) ? "额外验证" : $"额外验证({description})");
        }
        return this;
    }

    /// <summary>
    /// 构建验证器
    /// </summary>
    /// <returns>YRegexValidator 实例</returns>
    public YRegexValidator Build()
    {
        var pattern = _pattern.ToString();
        var description = string.Join(" + ", _descriptions);

        var validator = new YRegexValidator(pattern, description, _options, _timeout, _context);

        // 添加额外验证器
        foreach (var additionalValidator in _additionalValidators)
        {
            validator.AddValidator(additionalValidator);
        }

        return validator;
    }

    /// <summary>
    /// 构建正则表达式
    /// </summary>
    /// <returns>Regex 实例</returns>
    public Regex BuildRegex()
    {
        var pattern = _pattern.ToString();
        return _context?.GetOrCreateRegex(pattern) ?? new Regex(pattern, _options, _timeout);
    }

    /// <summary>
    /// 构建模式字符串
    /// </summary>
    /// <returns>正则表达式模式字符串</returns>
    public string BuildPattern()
    {
        return _pattern.ToString();
    }

    /// <summary>
    /// 获取当前模式
    /// </summary>
    /// <returns>当前正则表达式模式</returns>
    public string GetCurrentPattern()
    {
        return _pattern.ToString();
    }

    /// <summary>
    /// 获取当前描述
    /// </summary>
    /// <returns>当前描述</returns>
    public string GetCurrentDescription()
    {
        return string.Join(" + ", _descriptions);
    }

    /// <summary>
    /// 重写 ToString 方法
    ///
    /// 提供构建器的字符串表示，包含：
    /// - 人类可读的描述信息
    /// - 生成的正则表达式模式
    ///
    /// 用于调试和日志记录
    /// </summary>
    /// <returns>构建器信息字符串</returns>
    public override string ToString()
    {
        // 获取当前构建的正则表达式模式
        var pattern = _pattern.ToString();

        // 将所有描述信息用 " + " 连接
        var description = string.Join(" + ", _descriptions);

        // 根据是否有描述信息返回不同格式的字符串
        return string.IsNullOrEmpty(description)
            ? $"YRegexBuilder: {pattern}"                    // 只有模式
            : $"YRegexBuilder: {description} ({pattern})";   // 描述 + 模式
    }

    #endregion
}
