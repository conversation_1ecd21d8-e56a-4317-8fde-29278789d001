using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 文档格式验证支持
/// 
/// 提供15+常用文档格式的验证功能，涵盖：
/// - 📄 数据格式：JSON、XML、CSV、YAML、TOML等
/// - 🌐 Web格式：HTML、CSS、JavaScript等
/// - 📝 标记语言：Markdown、LaTeX、reStructuredText等
/// - 📊 配置文件：INI、Properties、ENV等
/// - 🗃️ 数据交换：SQL、GraphQL、Protocol Buffers等
/// 
/// 所有验证都基于标准格式规范和最佳实践
/// </summary>
public partial class YRegexBuilder
{
    #region 数据格式验证

    /// <summary>
    /// JSON格式验证
    /// 
    /// 验证基本的JSON结构：
    /// - 对象：{"key": "value"}
    /// - 数组：[1, 2, 3]
    /// - 基本类型：字符串、数字、布尔值、null
    /// 
    /// 注意：这是简化的JSON验证，完整验证建议使用专门的JSON解析器
    /// </summary>
    /// <param name="strict">是否严格模式（要求完整的JSON结构）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    /// <example>
    /// <code>
    /// // 简单JSON验证
    /// var jsonValidator = YRegexBuilder.Create()
    ///     .JSONFormat(false, "JSON片段")
    ///     .Build();
    /// 
    /// // 严格JSON验证
    /// var strictJsonValidator = YRegexBuilder.Create()
    ///     .JSONFormat(true, "完整JSON")
    ///     .Build();
    /// </code>
    /// </example>
    public YRegexBuilder JSONFormat(bool strict = false, string description = "")
    {
        string pattern;
        
        if (strict)
        {
            // 严格模式：完整的JSON对象或数组
            pattern = @"^\s*(?:\{(?:[^{}]|(?:\{[^{}]*\}))*\}|\[(?:[^\[\]]|(?:\[[^\[\]]*\]))*\])\s*$";
        }
        else
        {
            // 宽松模式：JSON值片段
            pattern = @"(?:\{[^{}]*\}|\[[^\[\]]*\]|""[^""]*""|'[^']*'|-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?|true|false|null)";
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"JSON格式({(strict ? "严格" : "宽松")})" 
            : description);
        return this;
    }

    /// <summary>
    /// XML格式验证
    /// 
    /// 验证基本的XML结构：
    /// - 标签：&lt;tag&gt;content&lt;/tag&gt;
    /// - 自闭合标签：&lt;tag/&gt;
    /// - 属性：&lt;tag attr="value"&gt;
    /// - 注释：&lt;!-- comment --&gt;
    /// </summary>
    /// <param name="allowSelfClosing">是否允许自闭合标签</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder XMLFormat(bool allowSelfClosing = true, string description = "")
    {
        string pattern;
        
        if (allowSelfClosing)
        {
            // 支持自闭合标签
            pattern = @"<(?:[a-zA-Z_][\w\-\.]*(?:\s+[a-zA-Z_][\w\-\.]*\s*=\s*(?:""[^""]*""|'[^']*'))*\s*/?|/[a-zA-Z_][\w\-\.]*\s*|!--[\s\S]*?--)>";
        }
        else
        {
            // 只支持配对标签
            pattern = @"<(?:[a-zA-Z_][\w\-\.]*(?:\s+[a-zA-Z_][\w\-\.]*\s*=\s*(?:""[^""]*""|'[^']*'))*\s*|/[a-zA-Z_][\w\-\.]*\s*|!--[\s\S]*?--)>";
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"XML格式({(allowSelfClosing ? "含自闭合" : "仅配对")})" 
            : description);
        return this;
    }

    /// <summary>
    /// CSV格式验证
    /// 
    /// 验证CSV（逗号分隔值）格式：
    /// - 基本格式：value1,value2,value3
    /// - 引号包围：&quot;value with, comma&quot;,value2
    /// - 转义引号：&quot;value with &quot;&quot;quote&quot;&quot;&quot;
    /// </summary>
    /// <param name="separator">分隔符（默认为逗号）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CSVFormat(string separator = ",", string description = "")
    {
        // 转义分隔符以防特殊字符
        var escapedSeparator = System.Text.RegularExpressions.Regex.Escape(separator);
        
        // CSV字段模式：引号包围的字段或不含分隔符的字段
        var fieldPattern = $@"(?:""(?:[^""]|"""")*""|[^{escapedSeparator}\r\n]*)";
        
        // 完整CSV行模式
        var pattern = $@"^{fieldPattern}(?:{escapedSeparator}{fieldPattern})*$";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"CSV格式(分隔符:{separator})" 
            : description);
        return this;
    }

    /// <summary>
    /// YAML格式验证
    /// 
    /// 验证基本的YAML结构：
    /// - 键值对：key: value
    /// - 列表：- item
    /// - 嵌套结构（通过缩进）
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder YAMLFormat(string description = "")
    {
        // YAML基本模式：键值对、列表项、注释
        var pattern = @"(?:^\s*[a-zA-Z_][\w\-]*\s*:\s*.*$|^\s*-\s+.*$|^\s*#.*$)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "YAML格式" : description);
        return this;
    }

    /// <summary>
    /// TOML格式验证
    /// 
    /// 验证TOML（Tom's Obvious, Minimal Language）格式：
    /// - 键值对：key = "value"
    /// - 节：[section]
    /// - 数组：array = [1, 2, 3]
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder TOMLFormat(string description = "")
    {
        // TOML基本模式：键值对、节、注释
        var pattern = @"(?:^\s*[a-zA-Z_][\w\-\.]*\s*=\s*.*$|^\s*\[[a-zA-Z_][\w\-\.]*\]$|^\s*#.*$)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "TOML格式" : description);
        return this;
    }

    #endregion

    #region Web格式验证

    /// <summary>
    /// HTML标签验证
    /// 
    /// 验证HTML标签格式：
    /// - 开始标签：&lt;div class="example"&gt;
    /// - 结束标签：&lt;/div&gt;
    /// - 自闭合标签：&lt;br/&gt;, &lt;img src="..."&gt;
    /// </summary>
    /// <param name="tagName">特定标签名（为空则匹配所有标签）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder HTMLTag(string tagName = "", string description = "")
    {
        string pattern;
        
        if (string.IsNullOrEmpty(tagName))
        {
            // 匹配所有HTML标签
            pattern = @"</?[a-zA-Z][a-zA-Z0-9]*(?:\s+[a-zA-Z-]+(?:\s*=\s*(?:""[^""]*""|'[^']*'|[^\s>]+))?)*\s*/?>";
        }
        else
        {
            // 匹配特定标签
            var escapedTagName = System.Text.RegularExpressions.Regex.Escape(tagName);
            pattern = $@"</?{escapedTagName}(?:\s+[a-zA-Z-]+(?:\s*=\s*(?:""[^""]*""|'[^']*'|[^\s>]+))?)*\s*/?>";
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"HTML标签{(string.IsNullOrEmpty(tagName) ? "" : $"({tagName})")}" 
            : description);
        return this;
    }

    /// <summary>
    /// CSS选择器验证
    /// 
    /// 验证CSS选择器格式：
    /// - 类选择器：.class-name
    /// - ID选择器：#element-id
    /// - 标签选择器：div, span
    /// - 属性选择器：[attr="value"]
    /// - 伪类：:hover, :nth-child(2n+1)
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CSSSelector(string description = "")
    {
        // CSS选择器模式：支持类、ID、标签、属性、伪类
        var pattern = @"(?:[a-zA-Z][a-zA-Z0-9-]*|#[a-zA-Z][a-zA-Z0-9-]*|\.[a-zA-Z][a-zA-Z0-9-]*|\[[a-zA-Z][a-zA-Z0-9-]*(?:[~|^$*]?=[""']?[^""'\]]*[""']?)?\]|:[a-zA-Z-]+(?:\([^)]*\))?)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "CSS选择器" : description);
        return this;
    }

    /// <summary>
    /// CSS属性验证
    /// 
    /// 验证CSS属性声明格式：
    /// - 属性声明：color: red;
    /// - 带重要性：margin: 0 !important;
    /// - 多值属性：padding: 10px 20px 30px 40px;
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CSSProperty(string description = "")
    {
        // CSS属性声明模式
        var pattern = @"[a-zA-Z-]+\s*:\s*[^;]+(?:\s*!important)?\s*;?";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "CSS属性" : description);
        return this;
    }

    #endregion

    #region 标记语言验证

    /// <summary>
    /// Markdown格式验证
    /// 
    /// 验证Markdown语法元素：
    /// - 标题：# Header, ## Header
    /// - 链接：[text](url)
    /// - 图片：![alt](url)
    /// - 代码：`code`, ```code block```
    /// - 列表：- item, 1. item
    /// </summary>
    /// <param name="element">特定元素类型（header, link, image, code, list, all）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder MarkdownFormat(string element = "all", string description = "")
    {
        string pattern = element.ToLower() switch
        {
            "header" => @"^#{1,6}\s+.+$",
            "link" => @"\[([^\]]+)\]\(([^)]+)\)",
            "image" => @"!\[([^\]]*)\]\(([^)]+)\)",
            "code" => @"`[^`]+`|```[\s\S]*?```",
            "list" => @"^(?:\s*[-*+]\s+.+|\s*\d+\.\s+.+)$",
            "all" => @"(?:^#{1,6}\s+.+$|\[([^\]]+)\]\(([^)]+)\)|!\[([^\]]*)\]\(([^)]+)\)|`[^`]+`|```[\s\S]*?```|^(?:\s*[-*+]\s+.+|\s*\d+\.\s+.+)$)",
            _ => throw new ArgumentException($"不支持的Markdown元素类型: {element}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"Markdown({element})" 
            : description);
        return this;
    }

    /// <summary>
    /// LaTeX命令验证
    /// 
    /// 验证LaTeX命令格式：
    /// - 命令：\command
    /// - 带参数命令：\command{arg}
    /// - 环境：\begin{env}...\end{env}
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder LaTeXCommand(string description = "")
    {
        // LaTeX命令模式
        var pattern = @"\\[a-zA-Z]+(?:\[[^\]]*\])?(?:\{[^}]*\})*";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "LaTeX命令" : description);
        return this;
    }

    #endregion

    #region 配置文件验证

    /// <summary>
    /// INI文件格式验证
    /// 
    /// 验证INI配置文件格式：
    /// - 节：[section]
    /// - 键值对：key=value
    /// - 注释：; comment, # comment
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder INIFormat(string description = "")
    {
        // INI格式模式：节、键值对、注释
        var pattern = @"(?:^\s*\[[^\]]+\]$|^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*.*$|^\s*[;#].*$)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "INI格式" : description);
        return this;
    }

    /// <summary>
    /// Properties文件格式验证
    /// 
    /// 验证Java Properties文件格式：
    /// - 键值对：key=value, key:value
    /// - 多行值：key=value\
    /// - 注释：# comment, ! comment
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PropertiesFormat(string description = "")
    {
        // Properties格式模式
        var pattern = @"(?:^\s*[a-zA-Z_][a-zA-Z0-9_\.]*\s*[=:]\s*.*(?:\\$|$)|^\s*[#!].*$)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "Properties格式" : description);
        return this;
    }

    /// <summary>
    /// 环境变量格式验证
    /// 
    /// 验证环境变量文件格式（.env）：
    /// - 变量定义：VAR_NAME=value
    /// - 引号值：VAR_NAME="quoted value"
    /// - 注释：# comment
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EnvFormat(string description = "")
    {
        // 环境变量格式模式
        var pattern = @"(?:^\s*[A-Z_][A-Z0-9_]*\s*=\s*(?:""[^""]*""|'[^']*'|[^\s#]*)\s*(?:#.*)?$|^\s*#.*$)";

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? "环境变量格式" : description);
        return this;
    }

    #endregion
}
