using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 扩展字符支持
/// 支持所有正则表达式字符、特殊字符和组合
/// 兼容 .NET 6.0 和 .NET 8.0
/// </summary>
public partial class YRegexBuilder
{
    #region 国际化字符支持

    /// <summary>
    /// 匹配中文字符 (CJK统一汉字)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ChineseCharacters(string description = "")
    {
        _pattern.Append(@"[\u4e00-\u9fff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "中文字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配日文字符 (平假名、片假名)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder JapaneseCharacters(string description = "")
    {
        _pattern.Append(@"[\u3040-\u309f\u30a0-\u30ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "日文字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配韩文字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder KoreanCharacters(string description = "")
    {
        _pattern.Append(@"[\u1100-\u11ff\uac00-\ud7af]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "韩文字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配阿拉伯文字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ArabicCharacters(string description = "")
    {
        _pattern.Append(@"[\u0600-\u06ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "阿拉伯文字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配希伯来文字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder HebrewCharacters(string description = "")
    {
        _pattern.Append(@"[\u0590-\u05ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "希伯来文字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配泰文字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ThaiCharacters(string description = "")
    {
        _pattern.Append(@"[\u0e00-\u0e7f]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "泰文字符" : description);
        return this;
    }

    #endregion

    #region 特殊字符和符号

    /// <summary>
    /// 匹配表情符号 (Emoji)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EmojiCharacters(string description = "")
    {
        _pattern.Append(@"[\U0001f600-\U0001f64f\U0001f300-\U0001f5ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "表情符号" : description);
        return this;
    }

    /// <summary>
    /// 匹配数学符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder MathSymbols(string description = "")
    {
        _pattern.Append(@"[\u2200-\u22ff\u2190-\u21ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "数学符号" : description);
        return this;
    }

    /// <summary>
    /// 匹配货币符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CurrencySymbols(string description = "")
    {
        _pattern.Append(@"[\u00a2-\u00a5\u20a0-\u20cf]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "货币符号" : description);
        return this;
    }

    /// <summary>
    /// 匹配箭头符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ArrowSymbols(string description = "")
    {
        _pattern.Append(@"[\u2190-\u21ff]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "箭头符号" : description);
        return this;
    }

    #endregion

    #region 字符类组合

    /// <summary>
    /// 匹配十六进制数字 [0-9A-Fa-f]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder HexDigitCharacter(string description = "")
    {
        _pattern.Append("[0-9A-Fa-f]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "十六进制数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配八进制数字 [0-7]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OctalDigitCharacter(string description = "")
    {
        _pattern.Append("[0-7]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "八进制数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配二进制数字 [01]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder BinaryDigitCharacter(string description = "")
    {
        _pattern.Append("[01]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "二进制数字" : description);
        return this;
    }

    /// <summary>
    /// 匹配标点符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CommonPunctuation(string description = "")
    {
        _pattern.Append(@"[.,;:!?]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "常用标点" : description);
        return this;
    }

    /// <summary>
    /// 匹配引号字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuoteCharacters(string description = "")
    {
        _pattern.Append(@"[""'`]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "引号字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配括号字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder BracketCharacters(string description = "")
    {
        _pattern.Append(@"[(){}\[\]]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "括号字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配数学运算符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder MathOperators(string description = "")
    {
        _pattern.Append(@"[+\-*/=]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "数学运算符" : description);
        return this;
    }

    /// <summary>
    /// 匹配比较运算符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ComparisonOperators(string description = "")
    {
        _pattern.Append("[<>=!]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "比较运算符" : description);
        return this;
    }

    /// <summary>
    /// 匹配特殊符号
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder SpecialSymbols(string description = "")
    {
        _pattern.Append(@"[@#$%^&*]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "特殊符号" : description);
        return this;
    }

    #endregion

    #region 编程相关字符

    /// <summary>
    /// 匹配标识符字符 [a-zA-Z0-9_]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IdentifierCharacter(string description = "")
    {
        _pattern.Append("[a-zA-Z0-9_]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "标识符字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配标识符开始字符 [a-zA-Z_]
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IdentifierStartCharacter(string description = "")
    {
        _pattern.Append("[a-zA-Z_]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "标识符开始字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配文件名安全字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder FilenameSafeCharacter(string description = "")
    {
        _pattern.Append(@"[a-zA-Z0-9._-]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "文件名安全字符" : description);
        return this;
    }

    /// <summary>
    /// 匹配Base64字符
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Base64Character(string description = "")
    {
        _pattern.Append("[a-zA-Z0-9+/=]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "Base64字符" : description);
        return this;
    }

    #endregion

    #region 十六进制和Unicode字符

    /// <summary>
    /// 匹配十六进制字符
    /// </summary>
    /// <param name="hexValue">十六进制值 (如 "41" 表示 'A')</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder HexCharacter(string hexValue, string description = "")
    {
        _pattern.Append($@"\x{hexValue}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"十六进制字符(\\x{hexValue})" : description);
        return this;
    }

    /// <summary>
    /// 匹配Unicode字符
    /// </summary>
    /// <param name="unicodeValue">Unicode值 (如 "0041" 表示 'A')</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UnicodeCharacter(string unicodeValue, string description = "")
    {
        _pattern.Append($@"\u{unicodeValue}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"Unicode字符(\\u{unicodeValue})" : description);
        return this;
    }

    #endregion

    #region 字符范围和集合

    /// <summary>
    /// 匹配字符范围
    /// </summary>
    /// <param name="startChar">起始字符</param>
    /// <param name="endChar">结束字符</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CharacterRange(char startChar, char endChar, string description = "")
    {
        _pattern.Append($"[{startChar}-{endChar}]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"字符范围({startChar}-{endChar})" : description);
        return this;
    }

    /// <summary>
    /// 匹配字符集合中的任意一个
    /// </summary>
    /// <param name="characters">字符集合</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CharacterSet(string characters, string description = "")
    {
        var escapedChars = System.Text.RegularExpressions.Regex.Escape(characters);
        _pattern.Append($"[{escapedChars}]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"字符集合({characters})" : description);
        return this;
    }

    /// <summary>
    /// 匹配字符集合之外的任意字符
    /// </summary>
    /// <param name="characters">排除的字符集合</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NegatedCharacterSet(string characters, string description = "")
    {
        var escapedChars = System.Text.RegularExpressions.Regex.Escape(characters);
        _pattern.Append($"[^{escapedChars}]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"非字符集合({characters})" : description);
        return this;
    }

    #endregion

    #region 常用词组和组合 - 参考老版本

    /// <summary>
    /// 匹配常用词组：用户名格式
    /// </summary>
    /// <param name="style">风格：alphanumeric, underscore, dash</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UsernamePattern(string style = "alphanumeric", string description = "")
    {
        switch (style.ToLower())
        {
            case "alphanumeric":
                _pattern.Append(@"[a-zA-Z][a-zA-Z0-9]{2,19}");
                break;
            case "underscore":
                _pattern.Append(@"[a-zA-Z][a-zA-Z0-9_]{2,19}");
                break;
            case "dash":
                _pattern.Append(@"[a-zA-Z][a-zA-Z0-9-]{2,19}");
                break;
            default:
                _pattern.Append(@"[a-zA-Z][a-zA-Z0-9]{2,19}");
                break;
        }
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"用户名格式({style})" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：密码格式
    /// </summary>
    /// <param name="strength">强度：weak, medium, strong</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PasswordPattern(string strength = "medium", string description = "")
    {
        switch (strength.ToLower())
        {
            case "weak":
                _pattern.Append(@"[a-zA-Z0-9]{6,20}");
                break;
            case "medium":
                _pattern.Append(@"(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z0-9]{8,20}");
                break;
            case "strong":
                _pattern.Append(@"(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,50}");
                break;
            default:
                _pattern.Append(@"(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z0-9]{8,20}");
                break;
        }
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"密码格式({strength})" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：邮箱格式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder EmailPattern(string description = "")
    {
        _pattern.Append(@"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "邮箱格式" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：电话号码格式
    /// </summary>
    /// <param name="region">地区：us, china, international</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PhonePattern(string region = "us", string description = "")
    {
        switch (region.ToLower())
        {
            case "us":
                _pattern.Append(@"\d{3}-\d{3}-\d{4}");
                break;
            case "china":
                _pattern.Append(@"1[3-9]\d{9}");
                break;
            case "international":
                _pattern.Append(@"\+\d{1,3}\d{7,15}");
                break;
            default:
                _pattern.Append(@"\d{3}-\d{3}-\d{4}");
                break;
        }
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"电话格式({region})" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：URL格式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UrlPattern(string description = "")
    {
        _pattern.Append(@"https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/[a-zA-Z0-9._~:/?#[\]@!$&'()*+,;=-]*)?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "URL格式" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：日期格式
    /// </summary>
    /// <param name="format">格式：iso, us, eu</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder DatePattern(string format = "iso", string description = "")
    {
        switch (format.ToLower())
        {
            case "iso":
                _pattern.Append(@"\d{4}-\d{2}-\d{2}");
                break;
            case "us":
                _pattern.Append(@"\d{2}/\d{2}/\d{4}");
                break;
            case "eu":
                _pattern.Append(@"\d{2}/\d{2}/\d{4}");
                break;
            default:
                _pattern.Append(@"\d{4}-\d{2}-\d{2}");
                break;
        }
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"日期格式({format})" : description);
        return this;
    }

    /// <summary>
    /// 匹配常用词组：时间格式
    /// </summary>
    /// <param name="format">格式：24h, 12h</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder TimePattern(string format = "24h", string description = "")
    {
        switch (format.ToLower())
        {
            case "24h":
                _pattern.Append(@"\d{2}:\d{2}:\d{2}");
                break;
            case "12h":
                _pattern.Append(@"\d{1,2}:\d{2}:\d{2} (AM|PM)");
                break;
            default:
                _pattern.Append(@"\d{2}:\d{2}:\d{2}");
                break;
        }
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"时间格式({format})" : description);
        return this;
    }

    #endregion

    #region 编程语言标识符 - 参考老版本

    /// <summary>
    /// 匹配编程语言标识符
    /// </summary>
    /// <param name="language">编程语言</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ProgrammingIdentifier(string language, string description = "")
    {
        var pattern = language.ToLower() switch
        {
            "csharp" or "c#" => @"[a-zA-Z_@][a-zA-Z0-9_]*",
            "java" => @"[a-zA-Z_$][a-zA-Z0-9_$]*",
            "javascript" or "js" => @"[a-zA-Z_$][a-zA-Z0-9_$]*",
            "python" => @"[a-zA-Z_][a-zA-Z0-9_]*",
            "cpp" or "c++" => @"[a-zA-Z_][a-zA-Z0-9_]*",
            "go" => @"[a-zA-Z_][a-zA-Z0-9_]*",
            "rust" => @"[a-zA-Z_][a-zA-Z0-9_]*",
            "php" => @"\$[a-zA-Z_][a-zA-Z0-9_]*",
            "ruby" => @"[a-zA-Z_][a-zA-Z0-9_]*[?!]?",
            _ => @"[a-zA-Z_][a-zA-Z0-9_]*"
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{language}标识符" : description);
        return this;
    }

    #endregion
}
