using Zylo.YRegex.Enums;
using Zylo.YRegex.Validators;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 友好 API 方法
/// 提供常用模式的快捷方法，让正则表达式更易用
/// </summary>
public partial class YRegexBuilder
{
    #region 常用模式快捷方法

    /// <summary>
    /// 邮箱地址模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Email(string description = "")
    {
        // 简化的邮箱模式，实际项目中可能需要更复杂的验证
        _pattern.Append(@"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "邮箱地址" : description);
        return this;
    }

    /// <summary>
    /// 手机号码模式（中国）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Phone(string description = "")
    {
        // 中国手机号模式
        _pattern.Append(@"1[3-9]\d{9}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "手机号码" : description);
        return this;
    }

    /// <summary>
    /// URL 地址模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Url(string description = "")
    {
        // 简化的 URL 模式
        _pattern.Append(@"https?://[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})+(?:/[^\s]*)?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "URL地址" : description);
        return this;
    }

    /// <summary>
    /// IPv4 地址模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IPv4(string description = "")
    {
        // IPv4 地址模式 - 不允许前导零
        _pattern.Append(@"(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "IPv4地址" : description);
        return this;
    }

    /// <summary>
    /// 日期模式（YYYY-MM-DD）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Date(string description = "")
    {
        // 日期模式 YYYY-MM-DD
        _pattern.Append(@"\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12][0-9]|3[01])");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "日期(YYYY-MM-DD)" : description);
        return this;
    }

    /// <summary>
    /// 时间模式（HH:MM:SS）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Time(string description = "")
    {
        // 时间模式 HH:MM:SS
        _pattern.Append(@"(?:[01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "时间(HH:MM:SS)" : description);
        return this;
    }

    /// <summary>
    /// 身份证号码模式（中国）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IdCard(string description = "")
    {
        // 中国身份证号码模式（18位）- 更严格的验证
        _pattern.Append(@"[1-9]\d{5}(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dXx]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "身份证号码" : description);
        return this;
    }

    #endregion

    #region 数字模式

    /// <summary>
    /// 整数模式
    /// </summary>
    /// <param name="allowNegative">是否允许负数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Integer(bool allowNegative = true, string description = "")
    {
        if (allowNegative)
        {
            _pattern.Append(@"-?\d+");
            _descriptions.Add(string.IsNullOrEmpty(description) ? "整数(含负数)" : description);
        }
        else
        {
            _pattern.Append(@"\d+");
            _descriptions.Add(string.IsNullOrEmpty(description) ? "正整数" : description);
        }
        return this;
    }

    /// <summary>
    /// 小数模式
    /// </summary>
    /// <param name="allowNegative">是否允许负数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Decimal(bool allowNegative = true, string description = "")
    {
        if (allowNegative)
        {
            _pattern.Append(@"-?\d+(?:\.\d+)?");
            _descriptions.Add(string.IsNullOrEmpty(description) ? "小数(含负数)" : description);
        }
        else
        {
            _pattern.Append(@"\d+(?:\.\d+)?");
            _descriptions.Add(string.IsNullOrEmpty(description) ? "正小数" : description);
        }
        return this;
    }

    /// <summary>
    /// 货币金额模式
    /// </summary>
    /// <param name="currency">货币符号</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Currency(string currency = "¥", string description = "")
    {
        var escapedCurrency = System.Text.RegularExpressions.Regex.Escape(currency);
        _pattern.Append($@"{escapedCurrency}\d+(?:\.\d{{2}})?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"货币金额({currency})" : description);
        return this;
    }

    #endregion

    #region 文本模式

    /// <summary>
    /// 用户名模式（字母数字下划线）
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Username(int minLength = 3, int maxLength = 20, string description = "")
    {
        _pattern.Append($@"[a-zA-Z0-9_]{{{minLength},{maxLength}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"用户名({minLength}-{maxLength}位)" : description);
        return this;
    }

    /// <summary>
    /// 密码模式（包含大小写字母、数字）
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="requireSpecialChar">是否要求特殊字符</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Password(int minLength = 8, int maxLength = 20, bool requireSpecialChar = false, string description = "")
    {
        // 使用前瞻确保包含大写字母、小写字母、数字
        _pattern.Append(@"(?=.*[a-z])(?=.*[A-Z])(?=.*\d)");

        if (requireSpecialChar)
        {
            _pattern.Append(@"(?=.*[!@#$%^&*(),.?""{}|<>])");
        }

        if (requireSpecialChar)
        {
            _pattern.Append($@"[a-zA-Z\d!@#$%^&*(),.?""{{}}|<>]{{{minLength},{maxLength}}}");
        }
        else
        {
            _pattern.Append($@"[a-zA-Z\d]{{{minLength},{maxLength}}}");
        }

        var specialDesc = requireSpecialChar ? "含特殊字符" : "";
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"密码({minLength}-{maxLength}位{specialDesc})" : description);
        return this;
    }

    /// <summary>
    /// 中文字符模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Chinese(string description = "")
    {
        _pattern.Append(@"[\u4e00-\u9fa5]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "中文字符" : description);
        return this;
    }

    /// <summary>
    /// 中文姓名模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ChineseName(string description = "")
    {
        _pattern.Append(@"[\u4e00-\u9fa5]{2,4}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "中文姓名" : description);
        return this;
    }

    #endregion

    #region 文件和路径模式

    /// <summary>
    /// 文件名模式
    /// </summary>
    /// <param name="extensions">允许的扩展名列表</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder FileName(IEnumerable<string>? extensions = null, string description = "")
    {
        _pattern.Append(@"[^<>:""/\\|?*]+");

        if (extensions != null)
        {
            var extList = extensions.ToList();
            if (extList.Count > 0)
            {
                _pattern.Append(@"\.(");
                for (int i = 0; i < extList.Count; i++)
                {
                    if (i > 0) _pattern.Append("|");
                    _pattern.Append(System.Text.RegularExpressions.Regex.Escape(extList[i].TrimStart('.')));
                }
                _pattern.Append(")");
            }
        }

        var extDesc = extensions != null ? $"[{string.Join(",", extensions)}]" : "";
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"文件名{extDesc}" : description);
        return this;
    }

    /// <summary>
    /// Windows 路径模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder WindowsPath(string description = "")
    {
        _pattern.Append(@"[a-zA-Z]:\\(?:[^<>:""/\\|?*]+\\)*[^<>:""/\\|?*]*");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "Windows路径" : description);
        return this;
    }

    /// <summary>
    /// Unix 路径模式
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UnixPath(string description = "")
    {
        _pattern.Append(@"/(?:[^/\0]+/)*[^/\0]*");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "Unix路径" : description);
        return this;
    }

    #endregion

    #region 便捷组合方法

    /// <summary>
    /// 可选的模式（等同于 ZeroOrOne）
    /// </summary>
    /// <param name="builder">可选内容构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Optional(Action<YRegexBuilder> builder, string description = "")
    {
        NonCapturingGroup(builder, string.IsNullOrEmpty(description) ? "可选" : description);
        ZeroOrOne();
        return this;
    }

    /// <summary>
    /// 重复的模式
    /// </summary>
    /// <param name="builder">重复内容构建器</param>
    /// <param name="min">最少次数</param>
    /// <param name="max">最多次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Repeat(Action<YRegexBuilder> builder, int min, int max, string description = "")
    {
        NonCapturingGroup(builder, string.IsNullOrEmpty(description) ? "重复" : description);
        Between(min, max);
        return this;
    }

    /// <summary>
    /// 包围的模式（如括号、引号等）
    /// </summary>
    /// <param name="start">开始字符</param>
    /// <param name="end">结束字符</param>
    /// <param name="content">内容构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Surrounded(string start, string end, Action<YRegexBuilder> content, string description = "")
    {
        Literal(start);
        content(this);
        Literal(end);

        _descriptions.Add(string.IsNullOrEmpty(description) ? $"包围[{start}...{end}]" : description);
        return this;
    }

    #endregion

    #region 前瞻条件 - 参考老版本设计

    /// <summary>
    /// 添加前瞻条件要求
    /// </summary>
    /// <param name="conditions">条件数组</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RequireConditions(LookaheadCondition[] conditions, string description = "")
    {
        foreach (var condition in conditions)
        {
            switch (condition)
            {
                case LookaheadCondition.HasUpperCase:
                    _pattern.Insert(0, @"(?=.*[A-Z])");
                    break;
                case LookaheadCondition.HasLowerCase:
                    _pattern.Insert(0, @"(?=.*[a-z])");
                    break;
                case LookaheadCondition.HasDigit:
                    _pattern.Insert(0, @"(?=.*\d)");
                    break;
                case LookaheadCondition.HasSpecialChar:
                    _pattern.Insert(0, @"(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?])");
                    break;
                case LookaheadCondition.NoWhitespace:
                    _pattern.Insert(0, @"(?!.*\s)");
                    break;
                case LookaheadCondition.NoConsecutiveRepeats:
                    _pattern.Insert(0, @"(?!.*(.)\1\1)");
                    break;
                case LookaheadCondition.StartsWithLetter:
                    _pattern.Insert(0, @"(?=^[a-zA-Z])");
                    break;
                case LookaheadCondition.StartsWithDigit:
                    _pattern.Insert(0, @"(?=^\d)");
                    break;
                case LookaheadCondition.NotStartsWithSpecial:
                    _pattern.Insert(0, @"(?!^[!@#$%^&*()_+\-=\[\]{}|;:,.<>?])");
                    break;
                case LookaheadCondition.HasChinese:
                    _pattern.Insert(0, @"(?=.*[\u4e00-\u9fff])");
                    break;
                case LookaheadCondition.OnlyAscii:
                    _pattern.Insert(0, @"(?=^[\x00-\x7F]*$)");
                    break;
            }
        }

        _descriptions.Add(string.IsNullOrEmpty(description) ? "前瞻条件" : description);
        return this;
    }

    #endregion

    #region 快捷验证器 - 参考老版本设计

    /// <summary>
    /// 快速创建邮箱验证器
    /// </summary>
    /// <returns>邮箱验证器</returns>
    public static YRegexValidator QuickEmail()
    {
        return Create()
            .AlphaNumeric(1, 64, "邮箱用户名")
            .At("@符号")
            .AlphaNumeric(1, 63, "域名")
            .Dot("点号")
            .Letters(2, 6, "顶级域名")
            .Build();
    }

    /// <summary>
    /// 快速创建电话号码验证器（美式格式）
    /// </summary>
    /// <returns>电话验证器</returns>
    public static YRegexValidator QuickPhone()
    {
        return Create()
            .Digits(3, "区号")
            .Literal("-", "分隔符")
            .Digits(3, "前缀")
            .Literal("-", "分隔符")
            .Digits(4, "号码")
            .Build();
    }

    /// <summary>
    /// 快速创建中国手机号验证器
    /// </summary>
    /// <returns>手机号验证器</returns>
    public static YRegexValidator QuickChinesePhone()
    {
        return Create()
            .Literal("1", "手机号开头")
            .OneOf("3456789", "第二位")
            .Digits(9, 9, "后续9位")  // 精确9位
            .Build();
    }

    /// <summary>
    /// 快速创建密码验证器
    /// </summary>
    /// <param name="strength">强度：weak, medium, strong</param>
    /// <returns>密码验证器</returns>
    public static YRegexValidator QuickPassword(string strength = "medium")
    {
        var builder = Create();

        switch (strength.ToLower())
        {
            case "weak":
                return builder
                    .AlphaNumeric(6, 20, "弱密码")
                    .Build();

            case "medium":
                return builder
                    .RequireConditions(new[] {
                        LookaheadCondition.HasUpperCase,
                        LookaheadCondition.HasLowerCase,
                        LookaheadCondition.HasDigit
                    }, "中等密码要求")
                    .Custom(@"[a-zA-Z0-9]{8,20}", "密码内容")
                    .Build();

            case "strong":
                return builder
                    .RequireConditions(new[] {
                        LookaheadCondition.HasUpperCase,
                        LookaheadCondition.HasLowerCase,
                        LookaheadCondition.HasDigit,
                        LookaheadCondition.HasSpecialChar
                    }, "强密码要求")
                    .Custom(@"[a-zA-Z0-9!@#$%^&*()_+\-=\[\]{}|;:,.<>?]{8,50}", "密码内容")
                    .Build();

            default:
                return QuickPassword("medium");
        }
    }

    /// <summary>
    /// 快速创建用户名验证器
    /// </summary>
    /// <param name="style">风格：alphanumeric, underscore, dash</param>
    /// <returns>用户名验证器</returns>
    public static YRegexValidator QuickUsername(string style = "alphanumeric")
    {
        var builder = Create();

        switch (style.ToLower())
        {
            case "alphanumeric":
                return builder
                    .RequireConditions(new[] { LookaheadCondition.StartsWithLetter }, "字母开头")
                    .Letter("首字母")
                    .AlphaNumeric(2, 19, "用户名")
                    .Build();

            case "underscore":
                return builder
                    .Letter("首字母")
                    .AlphaNumeric(1, 19, "用户名主体")
                    .Build();

            case "dash":
                return builder
                    .Letter("首字母")
                    .AlphaNumeric(1, 19, "用户名主体")
                    .Build();

            default:
                return QuickUsername("alphanumeric");
        }
    }

    #endregion
}
