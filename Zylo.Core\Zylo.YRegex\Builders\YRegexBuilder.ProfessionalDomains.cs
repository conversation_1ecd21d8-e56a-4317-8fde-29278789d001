using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 专业领域验证支持
/// 
/// 提供60+专业领域的验证功能，涵盖：
/// - 🏛️ 国际标准：ISBN、ISSN、DOI、ORCID等
/// - 💰 金融标识：IBAN、SWIFT、信用卡、股票代码等
/// - 🌐 技术标准：IPv6、MAC地址、UUID、端口号等
/// - 🏛️ 政府标识：SSN、国民保险号、税号等
/// - 📚 学术标识：期刊号、研究者ID、引用格式等
/// - 🏥 医疗标识：药品编码、医疗设备号等
/// 
/// 所有验证都基于国际标准和最佳实践
/// </summary>
public partial class YRegexBuilder
{
    #region 国际标准验证

    /// <summary>
    /// ISBN（国际标准书号）验证
    /// 
    /// 支持ISBN-10和ISBN-13格式：
    /// - ISBN-10: 0-306-40615-2 或 0306406152
    /// - ISBN-13: 978-0-306-40615-7 或 9780306406157
    /// </summary>
    /// <param name="format">格式类型："isbn10", "isbn13", "both"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    /// <example>
    /// <code>
    /// // ISBN-13验证
    /// var isbn13Validator = YRegexBuilder.Create()
    ///     .ISBN("isbn13", "ISBN-13标准")
    ///     .Build();
    /// 
    /// // 支持两种格式
    /// var isbnValidator = YRegexBuilder.Create()
    ///     .ISBN("both", "ISBN标准")
    ///     .Build();
    /// </code>
    /// </example>
    public YRegexBuilder ISBN(string format = "both", string description = "")
    {
        string pattern = format.ToLower() switch
        {
            "isbn10" => @"(?:ISBN(?:-10)?:?\s*)?(?=.{13}$)\d{1,5}([\s-]?)\d{1,7}\1\d{1,6}\1\d",
            "isbn13" => @"(?:ISBN(?:-13)?:?\s*)?(?=.{17}$)97[89]([\s-]?)\d{1,5}\1\d{1,7}\1\d{1,6}\1\d",
            "both" => @"(?:ISBN(?:-1[03])?:?\s*)?(?=.{13}$|.{17}$)(?:97[89]([\s-]?))?(\d{1,5})([\s-]?)\d{1,7}\3\d{1,6}\3\d",
            _ => throw new ArgumentException($"不支持的ISBN格式: {format}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"ISBN({format})" : description);
        return this;
    }

    /// <summary>
    /// ISSN（国际标准期刊号）验证
    /// 
    /// 格式：XXXX-XXXX，其中X为数字，最后一位可能是X
    /// 示例：1234-5678, 0317-8471
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ISSN(string description = "")
    {
        // ISSN格式：4位数字-4位数字（最后一位可能是X）
        _pattern.Append(@"(?:ISSN:?\s*)?[0-9]{4}-[0-9]{3}[0-9X]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "ISSN期刊号" : description);
        return this;
    }

    /// <summary>
    /// DOI（数字对象标识符）验证
    /// 
    /// 格式：10.xxxx/xxxx
    /// 示例：10.1000/182, 10.1038/nature12373
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder DOI(string description = "")
    {
        // DOI格式：10.前缀/后缀
        _pattern.Append(@"(?:DOI:?\s*)?10\.\d{4,}\/[^\s]+");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "DOI标识符" : description);
        return this;
    }

    /// <summary>
    /// ORCID（研究者标识符）验证
    /// 
    /// 格式：0000-0000-0000-0000
    /// 示例：0000-0002-1825-0097
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ORCID(string description = "")
    {
        // ORCID格式：16位数字，用连字符分隔成4组
        _pattern.Append(@"(?:ORCID:?\s*)?(?:https?:\/\/orcid\.org\/)?[0-9]{4}-[0-9]{4}-[0-9]{4}-[0-9]{3}[0-9X]");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "ORCID研究者ID" : description);
        return this;
    }

    #endregion

    #region 金融标识验证

    /// <summary>
    /// IBAN（国际银行账号）验证
    /// 
    /// 支持各国IBAN格式，包括：
    /// - 德国：DE89 3704 0044 0532 0130 00
    /// - 英国：GB29 NWBK 6016 1331 9268 19
    /// - 法国：FR14 2004 1010 0505 0001 3M02 606
    /// </summary>
    /// <param name="countryCode">国家代码（如"DE", "GB", "FR"等），为空则匹配所有</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IBAN(string countryCode = "", string description = "")
    {
        string pattern;
        
        if (string.IsNullOrEmpty(countryCode))
        {
            // 通用IBAN格式：2位国家代码 + 2位校验码 + 最多30位账号
            pattern = @"[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}";
        }
        else
        {
            // 特定国家的IBAN格式
            pattern = countryCode.ToUpper() switch
            {
                "DE" => @"DE[0-9]{2}[0-9]{8}[0-9]{10}",           // 德国：22位
                "GB" => @"GB[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",   // 英国：22位
                "FR" => @"FR[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}", // 法国：27位
                "IT" => @"IT[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}", // 意大利：27位
                "ES" => @"ES[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{1}[0-9]{1}[0-9]{10}", // 西班牙：24位
                _ => throw new ArgumentException($"不支持的国家代码: {countryCode}")
            };
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) 
            ? $"IBAN{(string.IsNullOrEmpty(countryCode) ? "" : $"({countryCode})")}" 
            : description);
        return this;
    }

    /// <summary>
    /// SWIFT代码（银行识别码）验证
    /// 
    /// 格式：
    /// - 8位：AAAABBCC
    /// - 11位：AAAABBCCXXX
    /// 其中AAAA=银行代码，BB=国家代码，CC=地区代码，XXX=分行代码
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder SWIFT(string description = "")
    {
        // SWIFT格式：4位银行代码 + 2位国家代码 + 2位地区代码 + 可选3位分行代码
        _pattern.Append(@"[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "SWIFT代码" : description);
        return this;
    }

    /// <summary>
    /// 信用卡号验证
    /// 
    /// 支持主要信用卡类型：
    /// - Visa: 4开头，13-19位
    /// - MasterCard: 5开头，16位
    /// - American Express: 34或37开头，15位
    /// - Discover: 6开头，16位
    /// </summary>
    /// <param name="cardType">卡类型："visa", "mastercard", "amex", "discover", "all"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder CreditCard(string cardType = "all", string description = "")
    {
        string pattern = cardType.ToLower() switch
        {
            "visa" => @"4[0-9]{12}(?:[0-9]{3})?(?:[0-9]{3})?",
            "mastercard" => @"5[1-5][0-9]{14}",
            "amex" => @"3[47][0-9]{13}",
            "discover" => @"6(?:011|5[0-9]{2})[0-9]{12}",
            "all" => @"(?:4[0-9]{12}(?:[0-9]{3})?(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})",
            _ => throw new ArgumentException($"不支持的信用卡类型: {cardType}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"信用卡({cardType})" : description);
        return this;
    }

    /// <summary>
    /// 股票代码验证
    /// 
    /// 支持不同交易所的股票代码格式：
    /// - NYSE/NASDAQ: 1-5位字母
    /// - 上海证券交易所: 6位数字，以6开头
    /// - 深圳证券交易所: 6位数字，以0或3开头
    /// - 香港交易所: 4-5位数字
    /// </summary>
    /// <param name="exchange">交易所："NYSE", "NASDAQ", "SSE", "SZSE", "HKEX", "all"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder StockSymbol(string exchange = "all", string description = "")
    {
        string pattern = exchange.ToUpper() switch
        {
            "NYSE" or "NASDAQ" => @"[A-Z]{1,5}",
            "SSE" => @"6[0-9]{5}",                    // 上海：6开头
            "SZSE" => @"[03][0-9]{5}",                // 深圳：0或3开头
            "HKEX" => @"[0-9]{4,5}",                  // 香港：4-5位数字
            "ALL" => @"(?:[A-Z]{1,5}|6[0-9]{5}|[03][0-9]{5}|[0-9]{4,5})",
            _ => throw new ArgumentException($"不支持的交易所: {exchange}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"股票代码({exchange})" : description);
        return this;
    }

    #endregion

    #region 技术标准验证

    /// <summary>
    /// IPv6地址验证（扩展格式）
    /// 
    /// 支持完整的IPv6格式：
    /// - 完整格式：2001:0db8:85a3:0000:0000:8a2e:0370:7334
    /// - 压缩格式：2001:db8:85a3::8a2e:370:7334
    /// - 混合格式：::ffff:*********
    /// </summary>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder IPv6Extended(string description = "")
    {
        // 复杂的IPv6正则表达式，支持所有有效格式
        var ipv6Pattern = @"(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|" +
                         @"(?:[0-9a-fA-F]{1,4}:)*::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}|" +
                         @"::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}|" +
                         @"(?:[0-9a-fA-F]{1,4}:)+::|" +
                         @"::";

        _pattern.Append($"({ipv6Pattern})");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "IPv6地址" : description);
        return this;
    }

    /// <summary>
    /// MAC地址验证
    /// 
    /// 支持多种MAC地址格式：
    /// - 冒号分隔：01:23:45:67:89:AB
    /// - 连字符分隔：01-23-45-67-89-AB
    /// - 点分隔：0123.4567.89AB
    /// - 无分隔符：0123456789AB
    /// </summary>
    /// <param name="format">格式："colon", "dash", "dot", "none", "all"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder MACAddress(string format = "all", string description = "")
    {
        string pattern = format.ToLower() switch
        {
            "colon" => @"[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}:[0-9A-Fa-f]{2}",
            "dash" => @"[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}-[0-9A-Fa-f]{2}",
            "dot" => @"[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}",
            "none" => @"[0-9A-Fa-f]{12}",
            "all" => @"(?:[0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}|[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}\.[0-9A-Fa-f]{4}|[0-9A-Fa-f]{12}",
            _ => throw new ArgumentException($"不支持的MAC地址格式: {format}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"MAC地址({format})" : description);
        return this;
    }

    /// <summary>
    /// UUID/GUID验证
    /// 
    /// 支持不同版本的UUID：
    /// - v1: 基于时间戳
    /// - v4: 随机生成
    /// - v5: 基于命名空间
    /// 
    /// 格式：xxxxxxxx-xxxx-Mxxx-Nxxx-xxxxxxxxxxxx
    /// </summary>
    /// <param name="version">版本："v1", "v4", "v5", "any"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder UUID(string version = "any", string description = "")
    {
        string pattern = version.ToLower() switch
        {
            "v1" => @"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-1[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}",
            "v4" => @"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-4[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}",
            "v5" => @"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-5[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}",
            "any" => @"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
            _ => throw new ArgumentException($"不支持的UUID版本: {version}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"UUID({version})" : description);
        return this;
    }

    /// <summary>
    /// 端口号验证
    /// 
    /// 支持不同类型的端口号：
    /// - well-known: 0-1023
    /// - registered: 1024-49151
    /// - dynamic: 49152-65535
    /// - any: 0-65535
    /// </summary>
    /// <param name="type">端口类型："well-known", "registered", "dynamic", "any"</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PortNumber(string type = "any", string description = "")
    {
        string pattern = type.ToLower() switch
        {
            "well-known" => @"(?:[0-9]|[1-9][0-9]|[1-9][0-9]{2}|10[0-1][0-9]|102[0-3])",
            "registered" => @"(?:102[4-9]|10[3-9][0-9]|1[1-9][0-9]{2}|[2-9][0-9]{3}|[1-4][0-9]{4}|49[0-1][0-4][0-9]|4915[0-1])",
            "dynamic" => @"(?:4915[2-9]|491[6-9][0-9]|49[2-9][0-9]{2}|[5-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])",
            "any" => @"(?:[0-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])",
            _ => throw new ArgumentException($"不支持的端口类型: {type}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"端口号({type})" : description);
        return this;
    }

    #endregion
}
