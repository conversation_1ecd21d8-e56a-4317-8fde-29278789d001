namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 量词和重复方法
/// 包含所有量词、重复、长度控制等功能
/// </summary>
public partial class YRegexBuilder
{
    #region 基础量词

    /// <summary>
    /// 零次或一次（可选）
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrOne(string description = "")
    {
        _pattern.Append("?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "零次或一次" : description);
        return this;
    }

    /// <summary>
    /// 零次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrMore(string description = "")
    {
        _pattern.Append("*");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "零次或多次" : description);
        return this;
    }

    /// <summary>
    /// 一次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OneOrMore(string description = "")
    {
        _pattern.Append("+");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "一次或多次" : description);
        return this;
    }

    /// <summary>
    /// 精确次数
    /// </summary>
    /// <param name="count">重复次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Exactly(int count, string description = "")
    {
        _pattern.Append($"{{{count}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"精确{count}次" : description);
        return this;
    }

    /// <summary>
    /// 至少指定次数
    /// </summary>
    /// <param name="minCount">最少次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder AtLeast(int minCount, string description = "")
    {
        _pattern.Append($"{{{minCount},}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"至少{minCount}次" : description);
        return this;
    }

    /// <summary>
    /// 指定范围次数
    /// </summary>
    /// <param name="minCount">最少次数</param>
    /// <param name="maxCount">最多次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Between(int minCount, int maxCount, string description = "")
    {
        _pattern.Append($"{{{minCount},{maxCount}}}");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"{minCount}-{maxCount}次" : description);
        return this;
    }

    #endregion

    #region 懒惰量词

    /// <summary>
    /// 懒惰的零次或一次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrOneLazy(string description = "")
    {
        _pattern.Append("??");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "懒惰零次或一次" : description);
        return this;
    }

    /// <summary>
    /// 懒惰的零次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrMoreLazy(string description = "")
    {
        _pattern.Append("*?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "懒惰零次或多次" : description);
        return this;
    }

    /// <summary>
    /// 懒惰的一次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OneOrMoreLazy(string description = "")
    {
        _pattern.Append("+?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "懒惰一次或多次" : description);
        return this;
    }

    /// <summary>
    /// 懒惰的指定范围次数
    /// </summary>
    /// <param name="minCount">最少次数</param>
    /// <param name="maxCount">最多次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder BetweenLazy(int minCount, int maxCount, string description = "")
    {
        _pattern.Append($"{{{minCount},{maxCount}}}?");
        _descriptions.Add(string.IsNullOrEmpty(description) ? $"懒惰{minCount}-{maxCount}次" : description);
        return this;
    }

    #endregion

    #region 占有量词（Possessive）

    /// <summary>
    /// 占有的零次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrMorePossessive(string description = "")
    {
        _pattern.Append("*+");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "占有零次或多次" : description);
        return this;
    }

    /// <summary>
    /// 占有的一次或多次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OneOrMorePossessive(string description = "")
    {
        _pattern.Append("++");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "占有一次或多次" : description);
        return this;
    }

    /// <summary>
    /// 占有的零次或一次
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder ZeroOrOnePossessive(string description = "")
    {
        _pattern.Append("?+");
        _descriptions.Add(string.IsNullOrEmpty(description) ? "占有零次或一次" : description);
        return this;
    }

    #endregion

    #region 分组和捕获

    /// <summary>
    /// 创建捕获组
    /// </summary>
    /// <param name="builder">组内构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Group(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(");
        var groupBuilder = new YRegexBuilder();
        builder(groupBuilder);
        _pattern.Append(groupBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var groupDesc = string.IsNullOrEmpty(description) ? "捕获组" : description;
        _descriptions.Add($"{groupDesc}({groupBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 创建命名捕获组
    /// </summary>
    /// <param name="name">组名</param>
    /// <param name="builder">组内构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NamedGroup(string name, Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append($"(?<{name}>");
        var groupBuilder = new YRegexBuilder();
        builder(groupBuilder);
        _pattern.Append(groupBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var groupDesc = string.IsNullOrEmpty(description) ? $"命名组[{name}]" : description;
        _descriptions.Add($"{groupDesc}({groupBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 创建非捕获组
    /// </summary>
    /// <param name="builder">组内构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NonCapturingGroup(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?:");
        var groupBuilder = new YRegexBuilder();
        builder(groupBuilder);
        _pattern.Append(groupBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var groupDesc = string.IsNullOrEmpty(description) ? "非捕获组" : description;
        _descriptions.Add($"{groupDesc}({groupBuilder.GetCurrentDescription()})");
        return this;
    }

    #endregion

    #region 选择和分支

    /// <summary>
    /// 或操作（选择）
    /// </summary>
    /// <param name="builder">备选构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder Or(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("|");
        var orBuilder = new YRegexBuilder();
        builder(orBuilder);
        _pattern.Append(orBuilder.GetCurrentPattern());

        var orDesc = string.IsNullOrEmpty(description) ? "或" : description;
        _descriptions.Add($"{orDesc}({orBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 或操作（选择多个选项）
    /// </summary>
    /// <param name="options">选项列表</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder OneOf(IEnumerable<string> options, string description = "")
    {
        var optionList = options.ToList();
        if (optionList.Count > 0)
        {
            _pattern.Append("(");
            for (int i = 0; i < optionList.Count; i++)
            {
                if (i > 0) _pattern.Append("|");
                _pattern.Append(System.Text.RegularExpressions.Regex.Escape(optionList[i]));
            }
            _pattern.Append(")");

            var optionsDesc = string.Join("|", optionList);
            _descriptions.Add(string.IsNullOrEmpty(description) ? $"选择[{optionsDesc}]" : description);
        }
        return this;
    }

    #endregion

    #region 前瞻和后顾

    /// <summary>
    /// 正向前瞻
    /// </summary>
    /// <param name="builder">前瞻构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PositiveLookahead(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?=");
        var lookaheadBuilder = new YRegexBuilder();
        builder(lookaheadBuilder);
        _pattern.Append(lookaheadBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var lookaheadDesc = string.IsNullOrEmpty(description) ? "正向前瞻" : description;
        _descriptions.Add($"{lookaheadDesc}({lookaheadBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 负向前瞻
    /// </summary>
    /// <param name="builder">前瞻构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NegativeLookahead(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?!");
        var lookaheadBuilder = new YRegexBuilder();
        builder(lookaheadBuilder);
        _pattern.Append(lookaheadBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var lookaheadDesc = string.IsNullOrEmpty(description) ? "负向前瞻" : description;
        _descriptions.Add($"{lookaheadDesc}({lookaheadBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 正向后顾
    /// </summary>
    /// <param name="builder">后顾构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder PositiveLookbehind(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?<=");
        var lookbehindBuilder = new YRegexBuilder();
        builder(lookbehindBuilder);
        _pattern.Append(lookbehindBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var lookbehindDesc = string.IsNullOrEmpty(description) ? "正向后顾" : description;
        _descriptions.Add($"{lookbehindDesc}({lookbehindBuilder.GetCurrentDescription()})");
        return this;
    }

    /// <summary>
    /// 负向后顾
    /// </summary>
    /// <param name="builder">后顾构建器</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NegativeLookbehind(Action<YRegexBuilder> builder, string description = "")
    {
        _pattern.Append("(?<!");
        var lookbehindBuilder = new YRegexBuilder();
        builder(lookbehindBuilder);
        _pattern.Append(lookbehindBuilder.GetCurrentPattern());
        _pattern.Append(")");

        var lookbehindDesc = string.IsNullOrEmpty(description) ? "负向后顾" : description;
        _descriptions.Add($"{lookbehindDesc}({lookbehindBuilder.GetCurrentDescription()})");
        return this;
    }

    #endregion

    #region RegQ 中文量词方法包装

    /// <summary>
    /// 零次或一次（可选）(中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ零次或一次(string description = "") => ZeroOrOne(description);

    /// <summary>
    /// 零次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ零次或多次(string description = "") => ZeroOrMore(description);

    /// <summary>
    /// 一次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ一次或多次(string description = "") => OneOrMore(description);

    /// <summary>
    /// 精确次数 (中文包装方法)
    /// </summary>
    /// <param name="count">重复次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ精确次数(int count, string description = "") => Exactly(count, description);

    /// <summary>
    /// 至少次数 (中文包装方法)
    /// </summary>
    /// <param name="min">最少次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ至少次数(int min, string description = "") => AtLeast(min, description);

    /// <summary>
    /// 范围次数 (中文包装方法)
    /// </summary>
    /// <param name="min">最少次数</param>
    /// <param name="max">最多次数</param>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ范围次数(int min, int max, string description = "") => Between(min, max, description);

    /// <summary>
    /// 懒惰的零次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ懒惰零次或多次(string description = "") => ZeroOrMoreLazy(description);

    /// <summary>
    /// 懒惰的一次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ懒惰一次或多次(string description = "") => OneOrMoreLazy(description);

    /// <summary>
    /// 占有的零次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ占有零次或多次(string description = "") => ZeroOrMorePossessive(description);

    /// <summary>
    /// 占有的一次或多次 (中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ占有一次或多次(string description = "") => OneOrMorePossessive(description);

    /// <summary>
    /// 可选匹配（零次或一次）(中文包装方法)
    /// </summary>
    /// <param name="description">描述</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegQ可选(string description = "") => ZeroOrOne(description);

    #endregion
}
