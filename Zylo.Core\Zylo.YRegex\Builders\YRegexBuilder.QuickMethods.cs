using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder 快捷方法支持
/// 
/// 提供30+常用验证的快捷方法，一行代码完成复杂验证：
/// - 🚀 一键验证：QuickEmail, QuickPhone, QuickURL等
/// - 🎯 智能识别：自动选择最佳验证模式
/// - 🌍 国际化支持：支持多国格式
/// - 📱 现代格式：支持最新的技术标准
/// - 🔧 可配置：提供常用配置选项
/// 
/// 设计理念：简单易用，开箱即用
/// </summary>
public partial class YRegexBuilder
{
    #region 快捷通信验证

    /// <summary>
    /// 快捷邮箱验证
    /// 
    /// 一键创建邮箱验证器，支持：
    /// - 标准邮箱格式：<EMAIL>
    /// - 国际化域名：支持Unicode域名
    /// - 子域名：<EMAIL>
    /// - 特殊字符：支持+号和点号
    /// </summary>
    /// <param name="strict">是否严格模式（更严格的验证规则）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    /// <example>
    /// <code>
    /// // 快速创建邮箱验证器
    /// var emailValidator = YRegexBuilder.Create()
    ///     .QuickEmail()
    ///     .Build();
    /// 
    /// // 严格模式邮箱验证
    /// var strictEmailValidator = YRegexBuilder.Create()
    ///     .QuickEmail(true, "严格邮箱验证")
    ///     .Build();
    /// 
    /// Console.WriteLine(emailValidator.IsMatch("<EMAIL>")); // True
    /// Console.WriteLine(emailValidator.IsMatch("<EMAIL>")); // True
    /// </code>
    /// </example>
    public YRegexBuilder QuickEmail(bool strict = false, string description = "")
    {
        string pattern;

        if (strict)
        {
            // 严格模式：RFC 5322 兼容
            pattern = @"^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$";
        }
        else
        {
            // 宽松模式：常用邮箱格式
            pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷邮箱验证({(strict ? "严格" : "标准")})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷电话号码验证
    /// 
    /// 一键创建电话验证器，支持多国格式：
    /// - 美国：(*************, ************
    /// - 中国：138-1234-5678, 13812345678
    /// - 国际：+1-************, +86-138-1234-5678
    /// </summary>
    /// <param name="region">地区代码（us, china, international, auto）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickPhone(string region = "auto", string description = "")
    {
        string pattern = region.ToLower() switch
        {
            "us" => @"^(?:\(\d{3}\)\s?|\d{3}[-.]?)\d{3}[-.]?\d{4}$",
            "china" => @"^(?:\+86[-.\s]?)?1[3-9]\d{9}$",
            "international" => @"^\+\d{1,3}[-.\s]?\d{4,14}$",
            "auto" => @"^(?:\+\d{1,3}[-.\s]?)?(?:\(\d{3}\)\s?|\d{3}[-.]?)\d{3}[-.]?\d{4}$|^(?:\+86[-.\s]?)?1[3-9]\d{9}$",
            _ => throw new ArgumentException($"不支持的地区代码: {region}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷电话验证({region})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷URL验证
    /// 
    /// 一键创建URL验证器，支持：
    /// - HTTP/HTTPS：http://example.com, https://example.com
    /// - 端口号：http://example.com:8080
    /// - 路径和查询：http://example.com/path?query=value
    /// - 锚点：http://example.com#anchor
    /// </summary>
    /// <param name="requireProtocol">是否要求协议（http/https）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickURL(bool requireProtocol = true, string description = "")
    {
        string pattern;

        if (requireProtocol)
        {
            // 要求协议
            pattern = @"^https?://(?:[-\w.])+(?::\d+)?(?:/(?:[\w._~:/?#[\]@!$&'()*+,;=-])*)?$";
        }
        else
        {
            // 可选协议
            pattern = @"^(?:https?://)?(?:[-\w.])+(?::\d+)?(?:/(?:[\w._~:/?#[\]@!$&'()*+,;=-])*)?$";
        }

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷URL验证({(requireProtocol ? "含协议" : "可选协议")})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷IP地址验证
    /// 
    /// 一键创建IP地址验证器，支持：
    /// - IPv4：***********
    /// - IPv6：2001:db8::1
    /// - 自动识别：同时支持IPv4和IPv6
    /// </summary>
    /// <param name="version">IP版本（ipv4, ipv6, both）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickIP(string version = "both", string description = "")
    {
        string pattern = version.ToLower() switch
        {
            "ipv4" => @"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$",
            "ipv6" => @"^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$",
            "both" => @"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$",
            _ => throw new ArgumentException($"不支持的IP版本: {version}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷IP验证({version})"
            : description);
        return this;
    }

    #endregion

    #region 快捷身份验证

    /// <summary>
    /// 快捷密码强度验证
    /// 
    /// 一键创建密码验证器，支持多种强度：
    /// - 弱：6位以上字母数字
    /// - 中：8位以上，包含大小写字母和数字
    /// - 强：8位以上，包含大小写字母、数字和特殊字符
    /// - 超强：12位以上，复杂组合
    /// </summary>
    /// <param name="strength">强度等级（weak, medium, strong, ultra）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickPassword(string strength = "medium", string description = "")
    {
        string pattern = strength.ToLower() switch
        {
            "weak" => @"^[a-zA-Z0-9]{6,}$",
            "medium" => @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$",
            "strong" => @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$",
            "ultra" => @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])(?=.*[#^+=<>{}[\]|\\:;""'.,/~`])[A-Za-z\d@$!%*?&#^+=<>{}[\]|\\:;""'.,/~`]{12,}$",
            _ => throw new ArgumentException($"不支持的密码强度: {strength}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷密码验证({strength})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷用户名验证
    /// 
    /// 一键创建用户名验证器，支持：
    /// - 标准：字母开头，字母数字组合，3-20位
    /// - 宽松：允许下划线和连字符
    /// - 严格：只允许字母数字，不允许连续特殊字符
    /// </summary>
    /// <param name="style">风格（standard, loose, strict）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickUsername(string style = "standard", string description = "")
    {
        string pattern = style.ToLower() switch
        {
            "standard" => @"^[a-zA-Z][a-zA-Z0-9]{2,19}$",
            "loose" => @"^[a-zA-Z][a-zA-Z0-9_-]{2,19}$",
            "strict" => @"^[a-zA-Z](?:[a-zA-Z0-9]*[a-zA-Z0-9])?$",
            _ => throw new ArgumentException($"不支持的用户名风格: {style}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷用户名验证({style})"
            : description);
        return this;
    }

    #endregion

    #region 快捷数字验证

    /// <summary>
    /// 快捷数字验证
    /// 
    /// 一键创建数字验证器，支持：
    /// - 整数：正整数、负整数、零
    /// - 小数：浮点数、科学计数法
    /// - 范围：指定数字范围
    /// </summary>
    /// <param name="type">数字类型（integer, decimal, positive, negative, range）</param>
    /// <param name="min">最小值（仅range类型有效）</param>
    /// <param name="max">最大值（仅range类型有效）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickNumber(string type = "decimal", int? min = null, int? max = null, string description = "")
    {
        string pattern = type.ToLower() switch
        {
            "integer" => @"^-?\d+$",
            "decimal" => @"^-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?$",
            "positive" => @"^[1-9]\d*(?:\.\d+)?$",
            "negative" => @"^-[1-9]\d*(?:\.\d+)?$",
            "range" when min.HasValue && max.HasValue => GenerateRangePattern(min.Value, max.Value),
            _ => @"^-?\d+(?:\.\d+)?$"
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷数字验证({type}{(type == "range" && min.HasValue && max.HasValue ? $":{min}-{max}" : "")})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷日期验证
    /// 
    /// 一键创建日期验证器，支持：
    /// - ISO格式：2023-12-31
    /// - 美式格式：12/31/2023
    /// - 欧式格式：31/12/2023
    /// - 中文格式：2023年12月31日
    /// </summary>
    /// <param name="format">日期格式（iso, us, eu, chinese, auto）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickDate(string format = "iso", string description = "")
    {
        string pattern = format.ToLower() switch
        {
            "iso" => @"^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])$",
            "us" => @"^(?:0[1-9]|1[0-2])/(?:0[1-9]|[12]\d|3[01])/\d{4}$",
            "eu" => @"^(?:0[1-9]|[12]\d|3[01])/(?:0[1-9]|1[0-2])/\d{4}$",
            "chinese" => @"^\d{4}年(?:0[1-9]|1[0-2])月(?:0[1-9]|[12]\d|3[01])日$",
            "auto" => @"^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[12]\d|3[01])$|^(?:0[1-9]|1[0-2])/(?:0[1-9]|[12]\d|3[01])/\d{4}$|^(?:0[1-9]|[12]\d|3[01])/(?:0[1-9]|1[0-2])/\d{4}$",
            _ => throw new ArgumentException($"不支持的日期格式: {format}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷日期验证({format})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷时间验证
    /// 
    /// 一键创建时间验证器，支持：
    /// - 24小时制：23:59:59
    /// - 12小时制：11:59:59 PM
    /// - 简化格式：23:59
    /// </summary>
    /// <param name="format">时间格式（24h, 12h, simple）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickTime(string format = "24h", string description = "")
    {
        string pattern = format.ToLower() switch
        {
            "24h" => @"^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$",
            "12h" => @"^(?:0[1-9]|1[0-2]):[0-5]\d:[0-5]\d\s(?:AM|PM)$",
            "simple" => @"^(?:[01]\d|2[0-3]):[0-5]\d$",
            _ => throw new ArgumentException($"不支持的时间格式: {format}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷时间验证({format})"
            : description);
        return this;
    }

    #endregion

    #region 快捷文件验证

    /// <summary>
    /// 快捷文件名验证
    /// 
    /// 一键创建文件名验证器，支持：
    /// - 安全：只允许字母数字和安全字符
    /// - 标准：允许常用文件名字符
    /// - 宽松：允许大部分字符（排除系统保留字符）
    /// </summary>
    /// <param name="safety">安全级别（safe, standard, loose）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickFilename(string safety = "standard", string description = "")
    {
        string pattern = safety.ToLower() switch
        {
            "safe" => @"^[a-zA-Z0-9._-]+$",
            "standard" => @"^[a-zA-Z0-9._\-\s()[\]{}]+$",
            "loose" => @"^[^<>:""/\\|?*\x00-\x1f]+$",
            _ => throw new ArgumentException($"不支持的安全级别: {safety}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷文件名验证({safety})"
            : description);
        return this;
    }

    /// <summary>
    /// 快捷文件扩展名验证
    /// 
    /// 一键创建文件扩展名验证器，支持：
    /// - 图片：jpg, png, gif, bmp, svg
    /// - 文档：pdf, doc, docx, txt, rtf
    /// - 音频：mp3, wav, flac, aac
    /// - 视频：mp4, avi, mkv, mov
    /// - 代码：js, cs, java, py, cpp
    /// </summary>
    /// <param name="category">文件类别（image, document, audio, video, code, all）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder QuickFileExtension(string category = "all", string description = "")
    {
        string pattern = category.ToLower() switch
        {
            "image" => @"\.(?:jpe?g|png|gif|bmp|svg|webp|tiff?)$",
            "document" => @"\.(?:pdf|docx?|txt|rtf|odt|pages)$",
            "audio" => @"\.(?:mp3|wav|flac|aac|ogg|m4a)$",
            "video" => @"\.(?:mp4|avi|mkv|mov|wmv|flv|webm)$",
            "code" => @"\.(?:js|ts|cs|java|py|cpp|c|h|php|rb|go|rs)$",
            "all" => @"\.(?:jpe?g|png|gif|bmp|svg|webp|tiff?|pdf|docx?|txt|rtf|odt|pages|mp3|wav|flac|aac|ogg|m4a|mp4|avi|mkv|mov|wmv|flv|webm|js|ts|cs|java|py|cpp|c|h|php|rb|go|rs)$",
            _ => throw new ArgumentException($"不支持的文件类别: {category}")
        };

        _pattern.Append(pattern);
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"快捷文件扩展名验证({category})"
            : description);
        return this;
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 生成数字范围验证模式
    /// 
    /// 根据指定的最小值和最大值生成正则表达式模式
    /// 注意：这是简化实现，复杂范围建议使用数值比较
    /// </summary>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <returns>正则表达式模式</returns>
    private static string GenerateRangePattern(int min, int max)
    {
        // 简化实现：只处理简单的数字范围
        if (min < 0 || max < 0 || min > max)
        {
            throw new ArgumentException("无效的数字范围");
        }

        // 对于简单范围，生成基本模式
        if (max <= 9)
        {
            return $@"^[{min}-{max}]$";
        }

        // 对于复杂范围，使用通用数字模式
        return @"^\d+$";
    }

    #endregion

    #region RegV 中文验证方法包装

    /// <summary>
    /// 快捷邮箱验证 (中文包装方法)
    /// </summary>
    /// <param name="strict">是否严格模式</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV邮箱(bool strict = false, string description = "") => QuickEmail(strict, description);

    /// <summary>
    /// 快捷手机号验证 (中文包装方法)
    /// </summary>
    /// <param name="region">地区代码</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV手机号(string region = "CN", string description = "") => QuickPhone(region, description);

    /// <summary>
    /// 快捷URL验证 (中文包装方法)
    /// </summary>
    /// <param name="requireHttps">是否要求HTTPS</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV网址(bool requireHttps = false, string description = "") => QuickURL(requireHttps, description);

    /// <summary>
    /// 快捷密码验证 (中文包装方法)
    /// </summary>
    /// <param name="strength">密码强度</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV密码(string strength = "medium", string description = "") => QuickPassword(strength, description);

    /// <summary>
    /// 快捷日期验证 (中文包装方法)
    /// </summary>
    /// <param name="format">日期格式</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV日期(string format = "ISO", string description = "") => QuickDate(format, description);

    /// <summary>
    /// 快捷时间验证 (中文包装方法)
    /// </summary>
    /// <param name="format">时间格式</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV时间(string format = "24h", string description = "") => QuickTime(format, description);

    /// <summary>
    /// 快捷IP地址验证 (中文包装方法)
    /// </summary>
    /// <param name="version">IP版本</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegVIP地址(string version = "v4", string description = "") => QuickIP(version, description);

    /// <summary>
    /// 快捷用户名验证 (中文包装方法)
    /// </summary>
    /// <param name="style">用户名风格</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV用户名(string style = "standard", string description = "") => QuickUsername(style, description);

    /// <summary>
    /// 快捷文件名验证 (中文包装方法)
    /// </summary>
    /// <param name="safety">安全级别</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV文件名(string safety = "standard", string description = "") => QuickFilename(safety, description);

    /// <summary>
    /// 快捷文件扩展名验证 (中文包装方法)
    /// </summary>
    /// <param name="category">文件类别</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV文件扩展名(string category = "all", string description = "") => QuickFileExtension(category, description);

    /// <summary>
    /// 快捷数字验证 (中文包装方法)
    /// </summary>
    /// <param name="type">数字类型</param>
    /// <param name="min">最小值</param>
    /// <param name="max">最大值</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder RegV数字(string type = "decimal", int? min = null, int? max = null, string description = "") => QuickNumber(type, min, max, description);

    #endregion
}
