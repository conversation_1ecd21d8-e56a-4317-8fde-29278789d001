using System.Globalization;
using System.Text;

namespace Zylo.YRegex.Builders;

/// <summary>
/// YRegexBuilder Unicode 完整支持
/// 
/// 提供完整的Unicode字符类别支持，基于.NET Unicode标准：
/// - 🌍 完整的Unicode通用类别 (\p{Lu}, \p{Ll}, \p{Nd} 等)
/// - 🔤 Unicode命名块支持 (\p{IsBasicLatin}, \p{IsGreek} 等)
/// - 🚫 否定Unicode类别支持 (\P{Lu}, \P{Nd} 等)
/// - 📝 字符类减法支持 ([a-z-[aeiou]])
/// - 🎯 智能Unicode字符检测和分类
/// 
/// 参考微软官方文档：
/// https://learn.microsoft.com/en-us/dotnet/standard/base-types/character-classes-in-regular-expressions
/// </summary>
public partial class YRegexBuilder
{
    #region Unicode 通用类别支持 - 扩展功能

    /// <summary>
    /// 验证Unicode类别名称是否有效
    ///
    /// 这是一个公共方法，允许用户在使用前验证Unicode类别名称
    /// </summary>
    /// <param name="category">Unicode类别名称</param>
    /// <returns>是否为有效的Unicode类别</returns>
    /// <example>
    /// <code>
    /// bool isValid = YRegexBuilder.IsValidUnicodeCategory("Lu"); // true
    /// bool isInvalid = YRegexBuilder.IsValidUnicodeCategory("XX"); // false
    /// </code>
    /// </example>
    public static bool IsValidUnicodeCategory(string category)
    {
        // .NET支持的所有Unicode通用类别
        var validCategories = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // 字母类 (Letter)
            "Lu", "Ll", "Lt", "Lm", "Lo", "L",
            // 标记类 (Mark)
            "Mn", "Mc", "Me", "M",
            // 数字类 (Number)
            "Nd", "Nl", "No", "N",
            // 标点类 (Punctuation)
            "Pc", "Pd", "Ps", "Pe", "Pi", "Pf", "Po", "P",
            // 符号类 (Symbol)
            "Sm", "Sc", "Sk", "So", "S",
            // 分隔符类 (Separator)
            "Zs", "Zl", "Zp", "Z",
            // 其他类 (Other)
            "Cc", "Cf", "Cs", "Co", "Cn", "C"
        };

        return validCategories.Contains(category);
    }

    /// <summary>
    /// 获取所有支持的Unicode类别列表
    ///
    /// 返回.NET支持的所有Unicode通用类别名称
    /// </summary>
    /// <returns>Unicode类别名称列表</returns>
    public static IReadOnlyList<string> GetSupportedUnicodeCategories()
    {
        return new List<string>
        {
            // 字母类 (Letter)
            "Lu", "Ll", "Lt", "Lm", "Lo", "L",
            // 标记类 (Mark)
            "Mn", "Mc", "Me", "M",
            // 数字类 (Number)
            "Nd", "Nl", "No", "N",
            // 标点类 (Punctuation)
            "Pc", "Pd", "Ps", "Pe", "Pi", "Pf", "Po", "P",
            // 符号类 (Symbol)
            "Sm", "Sc", "Sk", "So", "S",
            // 分隔符类 (Separator)
            "Zs", "Zl", "Zp", "Z",
            // 其他类 (Other)
            "Cc", "Cf", "Cs", "Co", "Cn", "C"
        }.AsReadOnly();
    }

    #endregion

    #region Unicode 命名块支持

    /// <summary>
    /// 匹配指定Unicode命名块的字符
    /// 
    /// 支持所有.NET标准Unicode命名块，如：
    /// - IsBasicLatin (0000-007F)
    /// - IsLatin-1Supplement (0080-00FF)
    /// - IsGreek (0370-03FF)
    /// - IsArabic (0600-06FF)
    /// - IsCJKUnifiedIdeographs (4E00-9FFF)
    /// - 等等...
    /// </summary>
    /// <param name="blockName">Unicode块名称（如 "IsBasicLatin", "IsGreek" 等）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    /// <example>
    /// <code>
    /// // 匹配基本拉丁字符
    /// var latinValidator = YRegexBuilder.Create()
    ///     .UnicodeBlock("IsBasicLatin", "基本拉丁字符")
    ///     .Build();
    /// 
    /// // 匹配希腊字符
    /// var greekValidator = YRegexBuilder.Create()
    ///     .UnicodeBlock("IsGreek", "希腊字符")
    ///     .Build();
    /// 
    /// // 匹配中日韩统一汉字
    /// var cjkValidator = YRegexBuilder.Create()
    ///     .UnicodeBlock("IsCJKUnifiedIdeographs", "中日韩汉字")
    ///     .Build();
    /// </code>
    /// </example>
    public YRegexBuilder UnicodeBlock(string blockName, string description = "")
    {
        // 验证块名称的有效性
        if (string.IsNullOrWhiteSpace(blockName))
        {
            throw new ArgumentException("Unicode块名称不能为空", nameof(blockName));
        }

        // 验证是否为有效的Unicode块名称
        if (!IsValidUnicodeBlock(blockName))
        {
            throw new ArgumentException($"无效的Unicode块名称: {blockName}", nameof(blockName));
        }

        // 构建Unicode块模式
        _pattern.Append($@"\p{{{blockName}}}");
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"Unicode块[{blockName}]"
            : description);

        return this;
    }

    /// <summary>
    /// 匹配不属于指定Unicode命名块的字符（否定匹配）
    /// </summary>
    /// <param name="blockName">Unicode块名称</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    public YRegexBuilder NotUnicodeBlock(string blockName, string description = "")
    {
        // 验证块名称的有效性
        if (string.IsNullOrWhiteSpace(blockName))
        {
            throw new ArgumentException("Unicode块名称不能为空", nameof(blockName));
        }

        // 验证是否为有效的Unicode块名称
        if (!IsValidUnicodeBlock(blockName))
        {
            throw new ArgumentException($"无效的Unicode块名称: {blockName}", nameof(blockName));
        }

        // 构建否定Unicode块模式
        _pattern.Append($@"\P{{{blockName}}}");
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"非Unicode块[{blockName}]"
            : description);

        return this;
    }

    #endregion

    #region 字符类减法支持

    /// <summary>
    /// 字符类减法 - 从基础字符集中排除指定字符集
    /// 
    /// 实现 [base_group-[excluded_group]] 语法
    /// 这是.NET正则表达式的高级功能，允许从一个字符类中减去另一个字符类
    /// </summary>
    /// <param name="baseGroup">基础字符组（如 "a-z"）</param>
    /// <param name="excludedGroup">要排除的字符组（如 "aeiou"）</param>
    /// <param name="description">描述信息</param>
    /// <returns>构建器实例</returns>
    /// <example>
    /// <code>
    /// // 匹配除元音外的小写字母
    /// var consonantValidator = YRegexBuilder.Create()
    ///     .CharacterSubtraction("a-z", "aeiou", "辅音字母")
    ///     .Build();
    /// 
    /// // 匹配除偶数外的数字
    /// var oddDigitValidator = YRegexBuilder.Create()
    ///     .CharacterSubtraction("0-9", "2468", "奇数")
    ///     .Build();
    /// 
    /// // 匹配除特定标点外的所有标点
    /// var limitedPunctuationValidator = YRegexBuilder.Create()
    ///     .CharacterSubtraction(@"\p{P}", ".,;:", "限定标点")
    ///     .Build();
    /// </code>
    /// </example>
    public YRegexBuilder CharacterSubtraction(string baseGroup, string excludedGroup, string description = "")
    {
        // 参数验证
        if (string.IsNullOrWhiteSpace(baseGroup))
        {
            throw new ArgumentException("基础字符组不能为空", nameof(baseGroup));
        }

        if (string.IsNullOrWhiteSpace(excludedGroup))
        {
            throw new ArgumentException("排除字符组不能为空", nameof(excludedGroup));
        }

        // 构建字符类减法模式
        _pattern.Append($"[{baseGroup}-[{excludedGroup}]]");
        _descriptions.Add(string.IsNullOrEmpty(description)
            ? $"字符减法[{baseGroup}-[{excludedGroup}]]"
            : description);

        return this;
    }

    #endregion

    #region 私有辅助方法

    /// <summary>
    /// 验证Unicode块名称是否有效
    /// 
    /// 检查给定的块名称是否为.NET支持的有效Unicode命名块
    /// 注意：这里只列出了常用的块，实际.NET支持更多块
    /// </summary>
    /// <param name="blockName">块名称</param>
    /// <returns>是否有效</returns>
    private static bool IsValidUnicodeBlock(string blockName)
    {
        // .NET支持的常用Unicode命名块（部分列表）
        var validBlocks = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // 基础拉丁和扩展
            "IsBasicLatin", "IsLatin-1Supplement", "IsLatinExtended-A", "IsLatinExtended-B",
            "IsLatinExtendedAdditional",
            
            // 欧洲语言
            "IsGreek", "IsGreekandCoptic", "IsGreekExtended", "IsCyrillic", "IsCyrillicSupplement",
            "IsArmenian",
            
            // 中东语言
            "IsHebrew", "IsArabic", "IsArabicPresentationForms-A", "IsArabicPresentationForms-B",
            "IsSyriac", "IsThaana",
            
            // 南亚语言
            "IsDevanagari", "IsBengali", "IsGurmukhi", "IsGujarati", "IsOriya", "IsTamil",
            "IsTelugu", "IsKannada", "IsMalayalam", "IsSinhala",
            
            // 东南亚语言
            "IsThai", "IsLao", "IsTibetan", "IsMyanmar", "IsKhmer", "IsMongolian",
            
            // 东亚语言
            "IsCJKRadicalsSupplement", "IsKangxiRadicals", "IsIdeographicDescriptionCharacters",
            "IsCJKSymbolsandPunctuation", "IsHiragana", "IsKatakana", "IsBopomofo",
            "IsHangulCompatibilityJamo", "IsKanbun", "IsBopomofoExtended",
            "IsKatakanaPhoneticExtensions", "IsEnclosedCJKLettersandMonths",
            "IsCJKCompatibility", "IsCJKUnifiedIdeographsExtensionA", "IsCJKUnifiedIdeographs",
            "IsHangulSyllables", "IsCJKCompatibilityIdeographs",
            
            // 符号和标点
            "IsGeneralPunctuation", "IsSuperscriptsandSubscripts", "IsCurrencySymbols",
            "IsCombiningDiacriticalMarksforSymbols", "IsLetterlikeSymbols", "IsNumberForms",
            "IsArrows", "IsMathematicalOperators", "IsMiscellaneousTechnical",
            "IsControlPictures", "IsOpticalCharacterRecognition", "IsEnclosedAlphanumerics",
            "IsBoxDrawing", "IsBlockElements", "IsGeometricShapes", "IsMiscellaneousSymbols",
            "IsDingbats",
            
            // 其他重要块
            "IsPrivateUse", "IsPrivateUseArea", "IsHighSurrogates", "IsHighPrivateUseSurrogates",
            "IsLowSurrogates", "IsSpecials", "IsHalfwidthandFullwidthForms"
        };

        return validBlocks.Contains(blockName);
    }

    #endregion
}
