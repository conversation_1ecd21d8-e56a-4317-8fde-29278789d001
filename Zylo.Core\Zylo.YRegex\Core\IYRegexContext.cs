using System.Text.RegularExpressions;

namespace Zylo.YRegex.Core;

/// <summary>
/// 正则表达式上下文接口
/// 提供正则表达式的执行环境和配置
/// </summary>
public interface IYRegexContext
{
    /// <summary>
    /// 正则表达式选项
    /// </summary>
    RegexOptions Options { get; set; }

    /// <summary>
    /// 超时时间
    /// </summary>
    TimeSpan Timeout { get; set; }

    /// <summary>
    /// 是否启用缓存
    /// </summary>
    bool EnableCache { get; set; }

    /// <summary>
    /// 缓存最大大小
    /// </summary>
    int MaxCacheSize { get; set; }

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    bool EnablePerformanceMonitoring { get; set; }

    /// <summary>
    /// 创建正则表达式实例
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>正则表达式实例</returns>
    Regex CreateRegex(string pattern);

    /// <summary>
    /// 获取或创建缓存的正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>正则表达式实例</returns>
    Regex GetOrCreateRegex(string pattern);

    /// <summary>
    /// 清除缓存
    /// </summary>
    void ClearCache();

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    /// <returns>缓存统计</returns>
    (int Count, int HitCount, int MissCount) GetCacheStats();
}
