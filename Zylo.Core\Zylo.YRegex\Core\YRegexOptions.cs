using System.Text.RegularExpressions;

namespace Zylo.YRegex.Core;

/// <summary>
/// YRegex 配置选项
/// </summary>
public class YRegexOptions
{
    /// <summary>
    /// 默认正则表达式选项
    /// </summary>
    public RegexOptions DefaultRegexOptions { get; set; } = RegexOptions.Compiled | RegexOptions.CultureInvariant;

    /// <summary>
    /// 默认超时时间
    /// </summary>
    public TimeSpan DefaultTimeout { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCache { get; set; } = true;

    /// <summary>
    /// 缓存最大大小
    /// </summary>
    public int MaxCacheSize { get; set; } = 100;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = false;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool EnableVerboseLogging { get; set; } = false;

    /// <summary>
    /// 是否启用 PCRE 引擎
    /// </summary>
    public bool EnablePcreEngine { get; set; } = false;

    /// <summary>
    /// 是否启用 JIT 编译（PCRE）
    /// </summary>
    public bool EnableJitCompilation { get; set; } = true;

    /// <summary>
    /// 验证失败时是否抛出异常
    /// </summary>
    public bool ThrowOnValidationFailure { get; set; } = false;

    /// <summary>
    /// 默认描述语言
    /// </summary>
    public string DefaultDescriptionLanguage { get; set; } = "zh-CN";

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置实例</returns>
    public static YRegexOptions CreateDefault()
    {
        return new YRegexOptions();
    }

    /// <summary>
    /// 创建高性能配置
    /// </summary>
    /// <returns>高性能配置实例</returns>
    public static YRegexOptions CreateHighPerformance()
    {
        return new YRegexOptions
        {
            DefaultRegexOptions = RegexOptions.Compiled | RegexOptions.CultureInvariant | RegexOptions.ExplicitCapture,
            EnableCache = true,
            MaxCacheSize = 500,
            EnablePerformanceMonitoring = true,
            EnablePcreEngine = true,
            EnableJitCompilation = true,
            DefaultTimeout = TimeSpan.FromSeconds(10)
        };
    }

    /// <summary>
    /// 创建开发调试配置
    /// </summary>
    /// <returns>开发调试配置实例</returns>
    public static YRegexOptions CreateDebug()
    {
        return new YRegexOptions
        {
            DefaultRegexOptions = RegexOptions.None,
            EnableCache = false,
            EnablePerformanceMonitoring = true,
            EnableVerboseLogging = true,
            ThrowOnValidationFailure = true,
            DefaultTimeout = TimeSpan.FromMinutes(1)
        };
    }
}
