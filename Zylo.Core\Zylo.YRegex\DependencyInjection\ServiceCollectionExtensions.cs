using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using FluentValidation;
using Zylo.YRegex.Core;
using Zylo.YRegex.Implementations;

namespace Zylo.YRegex.DependencyInjection;

/// <summary>
/// 依赖注入扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 YRegex 服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYRegex(
        this IServiceCollection services,
        Action<YRegexOptions>? configureOptions = null)
    {
        // 配置选项
        var options = new YRegexOptions();
        configureOptions?.Invoke(options);
        services.AddSingleton(options);

        // 注册核心服务
        services.AddSingleton<IYRegexContext, YRegexContext>();

        // 注册验证器工厂
        services.AddTransient<IYRegexValidatorFactory, YRegexValidatorFactory>();

        return services;
    }

    /// <summary>
    /// 添加 YRegex 服务（从配置文件）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <param name="sectionName">配置节名称</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYRegex(
        this IServiceCollection services,
        IConfiguration configuration,
        string sectionName = "YRegex")
    {
        // 从配置文件绑定选项
        var options = new YRegexOptions();
        configuration.GetSection(sectionName).Bind(options);
        services.AddSingleton(options);

        // 注册核心服务
        services.AddSingleton<IYRegexContext, YRegexContext>();
        services.AddTransient<IYRegexValidatorFactory, YRegexValidatorFactory>();

        return services;
    }

    /// <summary>
    /// 添加 YRegex 高性能配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYRegexHighPerformance(this IServiceCollection services)
    {
        return services.AddYRegex(options =>
        {
            var highPerfOptions = YRegexOptions.CreateHighPerformance();
            options.DefaultRegexOptions = highPerfOptions.DefaultRegexOptions;
            options.EnableCache = highPerfOptions.EnableCache;
            options.MaxCacheSize = highPerfOptions.MaxCacheSize;
            options.EnablePerformanceMonitoring = highPerfOptions.EnablePerformanceMonitoring;
            options.EnablePcreEngine = highPerfOptions.EnablePcreEngine;
            options.EnableJitCompilation = highPerfOptions.EnableJitCompilation;
            options.DefaultTimeout = highPerfOptions.DefaultTimeout;
        });
    }

    /// <summary>
    /// 添加 YRegex 调试配置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYRegexDebug(this IServiceCollection services)
    {
        return services.AddYRegex(options =>
        {
            var debugOptions = YRegexOptions.CreateDebug();
            options.DefaultRegexOptions = debugOptions.DefaultRegexOptions;
            options.EnableCache = debugOptions.EnableCache;
            options.EnablePerformanceMonitoring = debugOptions.EnablePerformanceMonitoring;
            options.EnableVerboseLogging = debugOptions.EnableVerboseLogging;
            options.ThrowOnValidationFailure = debugOptions.ThrowOnValidationFailure;
            options.DefaultTimeout = debugOptions.DefaultTimeout;
        });
    }

    /// <summary>
    /// 添加 FluentValidation 集成
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddYRegexFluentValidation(this IServiceCollection services)
    {
        // 添加 FluentValidation 验证器
        services.AddValidatorsFromAssemblyContaining<YRegexOptions>();

        return services;
    }
}

/// <summary>
/// YRegex 验证器工厂接口
/// </summary>
public interface IYRegexValidatorFactory
{
    /// <summary>
    /// 创建邮箱验证器
    /// </summary>
    /// <returns>邮箱验证器</returns>
    Validators.YRegexValidator CreateEmailValidator();

    /// <summary>
    /// 创建手机号验证器
    /// </summary>
    /// <returns>手机号验证器</returns>
    Validators.YRegexValidator CreatePhoneValidator();

    /// <summary>
    /// 创建 URL 验证器
    /// </summary>
    /// <returns>URL 验证器</returns>
    Validators.YRegexValidator CreateUrlValidator();

    /// <summary>
    /// 创建身份证验证器
    /// </summary>
    /// <returns>身份证验证器</returns>
    Validators.YRegexValidator CreateIdCardValidator();

    /// <summary>
    /// 创建用户名验证器
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>用户名验证器</returns>
    Validators.YRegexValidator CreateUsernameValidator(int minLength = 3, int maxLength = 20);

    /// <summary>
    /// 创建密码验证器
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="requireSpecialChar">是否要求特殊字符</param>
    /// <returns>密码验证器</returns>
    Validators.YRegexValidator CreatePasswordValidator(int minLength = 8, int maxLength = 20, bool requireSpecialChar = false);

    /// <summary>
    /// 创建自定义验证器
    /// </summary>
    /// <param name="builderAction">构建器配置</param>
    /// <returns>自定义验证器</returns>
    Validators.YRegexValidator CreateCustomValidator(Action<Builders.YRegexBuilder> builderAction);
}

/// <summary>
/// YRegex 验证器工厂实现
/// </summary>
public class YRegexValidatorFactory : IYRegexValidatorFactory
{
    private readonly IYRegexContext _context;
    private readonly ILogger<YRegexValidatorFactory>? _logger;

    /// <summary>
    /// 初始化验证器工厂
    /// </summary>
    /// <param name="context">YRegex 上下文</param>
    /// <param name="logger">日志记录器</param>
    public YRegexValidatorFactory(IYRegexContext context, ILogger<YRegexValidatorFactory>? logger = null)
    {
        _context = context;
        _logger = logger;
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreateEmailValidator()
    {
        _logger?.LogDebug("创建邮箱验证器");
        return Builders.YRegexBuilder.Create(_context).Email().Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreatePhoneValidator()
    {
        _logger?.LogDebug("创建手机号验证器");
        return Builders.YRegexBuilder.Create(_context).Phone().Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreateUrlValidator()
    {
        _logger?.LogDebug("创建URL验证器");
        return Builders.YRegexBuilder.Create(_context).Url().Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreateIdCardValidator()
    {
        _logger?.LogDebug("创建身份证验证器");
        return Builders.YRegexBuilder.Create(_context).IdCard().Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreateUsernameValidator(int minLength = 3, int maxLength = 20)
    {
        _logger?.LogDebug("创建用户名验证器: {MinLength}-{MaxLength}", minLength, maxLength);
        return Builders.YRegexBuilder.Create(_context).Username(minLength, maxLength).Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreatePasswordValidator(int minLength = 8, int maxLength = 20, bool requireSpecialChar = false)
    {
        _logger?.LogDebug("创建密码验证器: {MinLength}-{MaxLength}, 特殊字符: {RequireSpecialChar}",
            minLength, maxLength, requireSpecialChar);
        return Builders.YRegexBuilder.Create(_context).Password(minLength, maxLength, requireSpecialChar).Build();
    }

    /// <inheritdoc />
    public Validators.YRegexValidator CreateCustomValidator(Action<Builders.YRegexBuilder> builderAction)
    {
        _logger?.LogDebug("创建自定义验证器");
        var builder = Builders.YRegexBuilder.Create(_context);
        builderAction(builder);
        return builder.Build();
    }
}

/// <summary>
/// 配置扩展方法
/// </summary>
public static class YRegexConfigurationExtensions
{
    /// <summary>
    /// 获取 YRegex 配置
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <param name="sectionName">配置节名称</param>
    /// <returns>YRegex 选项</returns>
    public static YRegexOptions GetYRegexOptions(this IConfiguration configuration, string sectionName = "YRegex")
    {
        var options = new YRegexOptions();
        configuration.GetSection(sectionName).Bind(options);
        return options;
    }

    /// <summary>
    /// 验证 YRegex 配置
    /// </summary>
    /// <param name="options">YRegex 选项</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, List<string> Errors) ValidateYRegexOptions(this YRegexOptions options)
    {
        var errors = new List<string>();

        if (options.DefaultTimeout <= TimeSpan.Zero)
        {
            errors.Add("DefaultTimeout 必须大于零");
        }

        if (options.MaxCacheSize < 0)
        {
            errors.Add("MaxCacheSize 不能为负数");
        }

        if (options.EnableCache && options.MaxCacheSize == 0)
        {
            errors.Add("启用缓存时 MaxCacheSize 必须大于零");
        }

        return (errors.Count == 0, errors);
    }
}
