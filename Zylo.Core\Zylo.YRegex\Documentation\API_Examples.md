# Zylo.YRegex API 完整示例文档

基于菜鸟教程正则表达式示例，使用 Zylo.YRegex 实现

## 📚 目录

- [基础字符匹配](#基础字符匹配)
- [量词使用](#量词使用)
- [字符类和范围](#字符类和范围)
- [分组和替换](#分组和替换)
- [断言和前瞻](#断言和前瞻)
- [实际应用示例](#实际应用示例)
- [专业验证示例](#专业验证示例)

## 🔤 基础字符匹配

### 1. 简单字符匹配

```csharp
// 传统正则：/a/
var simpleA = YRegexBuilder.Create()
    .Literal("a", "字母a")
    .Build();

// 传统正则：/abc/
var simpleABC = YRegexBuilder.Create()
    .Literal("abc", "字符串abc")
    .Build();

// 测试
Console.WriteLine(simpleA.IsMatch("apple"));     // True
Console.WriteLine(simpleABC.IsMatch("abcdef"));  // True
Console.WriteLine(simpleABC.IsMatch("xyz"));     // False
```

### 2. 通配符匹配

```csharp
// 传统正则：/a.c/
var wildcardPattern = YRegexBuilder.Create()
    .Literal("a", "字母a")
    .AnyCharacter("任意字符")
    .Literal("c", "字母c")
    .Build();

// 测试
Console.WriteLine(wildcardPattern.IsMatch("aac")); // True
Console.WriteLine(wildcardPattern.IsMatch("abc")); // True
Console.WriteLine(wildcardPattern.IsMatch("a1c")); // True
Console.WriteLine(wildcardPattern.IsMatch("ac"));  // False
```

### 3. 转义字符

```csharp
// 传统正则：/filename\.ext/
var filenamePattern = YRegexBuilder.Create()
    .Literal("filename", "文件名")
    .Dot("点号")
    .Literal("ext", "扩展名")
    .Build();

// 测试
Console.WriteLine(filenamePattern.IsMatch("filename.ext")); // True
Console.WriteLine(filenamePattern.IsMatch("filenameXext")); // False
```

## 🔢 量词使用

### 1. 零次或多次 (*)

```csharp
// 传统正则：/a*b/
var zeroOrMorePattern = YRegexBuilder.Create()
    .Literal("a", "字母a")
    .ZeroOrMore("零次或多次")
    .Literal("b", "字母b")
    .Build();

// 测试
Console.WriteLine(zeroOrMorePattern.IsMatch("b"));    // True
Console.WriteLine(zeroOrMorePattern.IsMatch("ab"));   // True
Console.WriteLine(zeroOrMorePattern.IsMatch("aab"));  // True
Console.WriteLine(zeroOrMorePattern.IsMatch("aaab")); // True
```

### 2. 一次或多次 (+)

```csharp
// 传统正则：/a+b/
var oneOrMorePattern = YRegexBuilder.Create()
    .Literal("a", "字母a")
    .OneOrMore("一次或多次")
    .Literal("b", "字母b")
    .Build();

// 测试
Console.WriteLine(oneOrMorePattern.IsMatch("b"));    // False
Console.WriteLine(oneOrMorePattern.IsMatch("ab"));   // True
Console.WriteLine(oneOrMorePattern.IsMatch("aab"));  // True
Console.WriteLine(oneOrMorePattern.IsMatch("aaab")); // True
```

### 3. 零次或一次 (?)

```csharp
// 传统正则：/colou?r/
var optionalPattern = YRegexBuilder.Create()
    .Literal("colou", "前缀")
    .Literal("r", "字母r").ZeroOrOne("可选")
    .Literal("r", "字母r")
    .Build();

// 更简洁的写法
var colorPattern = YRegexBuilder.Create()
    .Literal("colo", "前缀")
    .Literal("u", "字母u").ZeroOrOne("可选u")
    .Literal("r", "字母r")
    .Build();

// 测试
Console.WriteLine(colorPattern.IsMatch("color"));  // True
Console.WriteLine(colorPattern.IsMatch("colour")); // True
Console.WriteLine(colorPattern.IsMatch("colr"));   // False
```

### 4. 精确次数

```csharp
// 传统正则：/z{3}/
var exactThreePattern = YRegexBuilder.Create()
    .Literal("z", "字母z")
    .Exactly(3, "精确3次")
    .Build();

// 传统正则：/z{3,6}/
var rangePattern = YRegexBuilder.Create()
    .Literal("z", "字母z")
    .Between(3, 6, "3到6次")
    .Build();

// 传统正则：/z{3,}/
var atLeastPattern = YRegexBuilder.Create()
    .Literal("z", "字母z")
    .AtLeast(3, "至少3次")
    .Build();

// 测试
Console.WriteLine(exactThreePattern.IsMatch("zzz"));    // True
Console.WriteLine(exactThreePattern.IsMatch("zzzz"));   // False
Console.WriteLine(rangePattern.IsMatch("zzz"));        // True
Console.WriteLine(rangePattern.IsMatch("zzzzzz"));     // True
Console.WriteLine(rangePattern.IsMatch("zzzzzzz"));    // False
```

## 📝 字符类和范围

### 1. 字符集合

```csharp
// 传统正则：/[abc]/
var characterSetPattern = YRegexBuilder.Create()
    .CharacterSet("abc", "字符集abc")
    .Build();

// 传统正则：/[a-z]/
var rangePattern = YRegexBuilder.Create()
    .CharacterRange('a', 'z', "小写字母")
    .Build();

// 传统正则：/[A-Za-z0-9]/
var alphanumericPattern = YRegexBuilder.Create()
    .AlphaNumeric("字母数字")
    .Build();

// 测试
Console.WriteLine(characterSetPattern.IsMatch("a")); // True
Console.WriteLine(characterSetPattern.IsMatch("d")); // False
Console.WriteLine(rangePattern.IsMatch("m"));        // True
Console.WriteLine(alphanumericPattern.IsMatch("A")); // True
```

### 2. 否定字符集

```csharp
// 传统正则：/[^abc]/
var negatedSetPattern = YRegexBuilder.Create()
    .NegatedCharacterSet("abc", "非abc字符")
    .Build();

// 传统正则：/[^0-9]/
var nonDigitPattern = YRegexBuilder.Create()
    .NonDigit("非数字字符")
    .Build();

// 测试
Console.WriteLine(negatedSetPattern.IsMatch("d")); // True
Console.WriteLine(negatedSetPattern.IsMatch("a")); // False
Console.WriteLine(nonDigitPattern.IsMatch("a"));   // True
Console.WriteLine(nonDigitPattern.IsMatch("5"));   // False
```

### 3. 预定义字符类

```csharp
// 传统正则：/\d/
var digitPattern = YRegexBuilder.Create()
    .Digit("数字")
    .Build();

// 传统正则：/\w/
var wordPattern = YRegexBuilder.Create()
    .WordCharacter("单词字符")
    .Build();

// 传统正则：/\s/
var whitespacePattern = YRegexBuilder.Create()
    .Whitespace("空白字符")
    .Build();

// 测试
Console.WriteLine(digitPattern.IsMatch("5"));     // True
Console.WriteLine(wordPattern.IsMatch("a"));      // True
Console.WriteLine(whitespacePattern.IsMatch(" ")); // True
```

## 🔗 分组和替换

### 1. 基本分组

```csharp
// 传统正则：/(abc)+/
var groupPattern = YRegexBuilder.Create()
    .Group(g => g.Literal("abc", "abc组合"), "分组")
    .OneOrMore("一次或多次")
    .Build();

// 测试
Console.WriteLine(groupPattern.IsMatch("abc"));    // True
Console.WriteLine(groupPattern.IsMatch("abcabc")); // True
```

### 2. 命名分组

```csharp
// 传统正则：/(?<year>\d{4})-(?<month>\d{2})-(?<day>\d{2})/
var datePattern = YRegexBuilder.Create()
    .NamedGroup("year", g => g.Digits(4), "年份")
    .Hyphen("分隔符")
    .NamedGroup("month", g => g.Digits(2), "月份")
    .Hyphen("分隔符")
    .NamedGroup("day", g => g.Digits(2), "日期")
    .Build();

// 测试和提取
var match = datePattern.Match("2024-03-15");
if (match.Success)
{
    Console.WriteLine($"年: {match.Groups["year"].Value}");   // 2024
    Console.WriteLine($"月: {match.Groups["month"].Value}");  // 03
    Console.WriteLine($"日: {match.Groups["day"].Value}");   // 15
}
```

### 3. 选择和分支

```csharp
// 传统正则：/gray|grey/
var alternationPattern = YRegexBuilder.Create()
    .Literal("gray", "美式拼写")
    .Or("或")
    .Literal("grey", "英式拼写")
    .Build();

// 传统正则：/gr(a|e)y/
var groupAlternationPattern = YRegexBuilder.Create()
    .Literal("gr", "前缀")
    .Group(g => g
        .Literal("a", "字母a")
        .Or("或")
        .Literal("e", "字母e"), "选择组")
    .Literal("y", "后缀")
    .Build();

// 测试
Console.WriteLine(alternationPattern.IsMatch("gray"));      // True
Console.WriteLine(alternationPattern.IsMatch("grey"));      // True
Console.WriteLine(groupAlternationPattern.IsMatch("gray")); // True
Console.WriteLine(groupAlternationPattern.IsMatch("grey")); // True
```

## 🔍 断言和前瞻

### 1. 单词边界

```csharp
// 传统正则：/\bter\b/
var wordBoundaryPattern = YRegexBuilder.Create()
    .WordBoundary("单词边界")
    .Literal("ter", "ter")
    .WordBoundary("单词边界")
    .Build();

// 测试
Console.WriteLine(wordBoundaryPattern.IsMatch("ter"));      // True
Console.WriteLine(wordBoundaryPattern.IsMatch("chapter"));  // False
Console.WriteLine(wordBoundaryPattern.IsMatch("terminal")); // False
```

### 2. 正向前瞻

```csharp
// 传统正则：/Windows(?=95 |98 |NT )/
var lookaheadPattern = YRegexBuilder.Create()
    .Literal("Windows", "Windows")
    .PositiveLookahead(la => la
        .Literal("95", "95版本")
        .Or("或")
        .Literal("98", "98版本")
        .Or("或")
        .Literal("NT", "NT版本"), "版本前瞻")
    .Build();

// 测试
Console.WriteLine(lookaheadPattern.IsMatch("Windows95")); // True
Console.WriteLine(lookaheadPattern.IsMatch("Windows98")); // True
Console.WriteLine(lookaheadPattern.IsMatch("WindowsXP")); // False
```

### 3. 负向前瞻

```csharp
// 匹配不以特定字符开头的单词
var negativeLookaheadPattern = YRegexBuilder.Create()
    .WordBoundary("单词边界")
    .NegativeLookahead(nla => nla
        .CharacterSet("aeiou", "元音字母"), "非元音开头")
    .Letters(1, 10, "单词")
    .WordBoundary("单词边界")
    .Build();

// 测试
Console.WriteLine(negativeLookaheadPattern.IsMatch("hello")); // True (h开头)
Console.WriteLine(negativeLookaheadPattern.IsMatch("apple")); // False (a开头)
```

## 🎯 实际应用示例

### 1. 用户名验证（菜鸟教程示例）

```csharp
// 传统正则：/[a-zA-Z0-9_-]+/
// 菜鸟教程：用户名可以包含字母、数字、下划线和中划线
var usernamePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .CharacterSet("a-zA-Z0-9_-", "用户名字符")
    .OneOrMore("一个或多个")
    .EndOfString("结束")
    .Build();

// 更友好的写法
var friendlyUsernamePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .AlphaNumeric("字母数字")
    .Or("或")
    .Underscore("下划线")
    .Or("或")
    .Hyphen("连字符")
    .OneOrMore("一个或多个")
    .EndOfString("结束")
    .Build();

// 使用快捷方法
var quickUsernamePattern = YRegexBuilder.Create()
    .QuickUsername("loose", "宽松用户名")
    .Build();

// 测试
var testUsernames = new[] { "abc123", "user_name", "test-user", "123invalid", "a", "valid_user-123" };
foreach (var username in testUsernames)
{
    var isValid = usernamePattern.IsMatch(username);
    Console.WriteLine($"用户名 '{username}': {(isValid ? "✅ 有效" : "❌ 无效")}");
}
```

### 2. HTML 标签匹配（菜鸟教程示例）

```csharp
// 传统正则：/<iframe([\s\S]*?)<\/iframe>/
// 匹配 iframe 标签及其内容
var iframePattern = YRegexBuilder.Create()
    .Literal("<iframe", "iframe开始标签")
    .Group(g => g
        .AnyCharacter("任意字符")
        .ZeroOrMoreLazy("懒惰匹配")
    , "标签属性和内容")
    .Literal("</iframe>", "iframe结束标签")
    .Build();

// 传统正则：/<img.*?src="(.*?)".*?\/?>/gi
// 匹配所有 img 标签的 src 属性
var imgSrcPattern = YRegexBuilder.Create()
    .Literal("<img", "img标签开始")
    .AnyCharacter("任意字符").ZeroOrMoreLazy("懒惰匹配")
    .Literal("src=\"", "src属性开始")
    .NamedGroup("src", g => g
        .AnyCharacter("任意字符")
        .ZeroOrMoreLazy("懒惰匹配")
    , "src值")
    .Literal("\"", "引号结束")
    .AnyCharacter("任意字符").ZeroOrMoreLazy("懒惰匹配")
    .Literal(">", "标签结束")
    .Build();

// 使用专门的HTML方法
var htmlTagPattern = YRegexBuilder.Create()
    .HTMLTag("img", "img标签")
    .Build();

// 测试
var htmlContent = @"
<div>
    <img src=""image1.jpg"" alt=""Image 1"">
    <iframe src=""video.html"">Video content</iframe>
    <img src=""image2.png"" class=""responsive"">
</div>";

var imgMatches = imgSrcPattern.Matches(htmlContent);
foreach (Match match in imgMatches)
{
    Console.WriteLine($"找到图片: {match.Groups["src"].Value}");
}
```

### 3. 章节标题匹配（菜鸟教程示例）

```csharp
// 传统正则：/^(Chapter|Section) [1-9][0-9]{0,1}$/
// 匹配 "Chapter 1" 到 "Section 99" 格式的标题
var chapterPattern = YRegexBuilder.Create()
    .StartOfString("行开始")
    .Group(g => g
        .Literal("Chapter", "章节")
        .Or("或")
        .Literal("Section", "部分")
    , "标题类型")
    .Space("空格")
    .CharacterRange('1', '9', "首位数字1-9")
    .Digit("第二位数字").ZeroOrOne("可选")
    .EndOfString("行结束")
    .Build();

// 更灵活的版本
var flexibleChapterPattern = YRegexBuilder.Create()
    .StartOfString("行开始")
    .NonCapturingGroup(g => g
        .Literal("Chapter", "章节")
        .Or("或")
        .Literal("Section", "部分")
    , "标题类型（非捕获）")
    .Whitespace("空白字符").OneOrMore("一个或多个")
    .NamedGroup("number", g => g
        .Digits(1, 2, "章节号")
    , "章节编号")
    .EndOfString("行结束")
    .Build();

// 测试
var chapterTitles = new[]
{
    "Chapter 1", "Section 5", "Chapter 12", "Section 99",
    "Chapter 0", "Section 100", "Part 1", "Chapter"
};

foreach (var title in chapterTitles)
{
    var match = flexibleChapterPattern.Match(title);
    if (match.Success)
    {
        Console.WriteLine($"✅ 有效标题: {title} (编号: {match.Groups["number"].Value})");
    }
    else
    {
        Console.WriteLine($"❌ 无效标题: {title}");
    }
}
```

### 4. URL 解析（菜鸟教程示例）

```csharp
// 传统正则：/(\w+):\/\/([^/:]+)(:\d*)?([^# ]*)/
// 解析 URL 为协议、域名、端口和路径
var urlParsePattern = YRegexBuilder.Create()
    .NamedGroup("protocol", g => g
        .WordCharacter("单词字符").OneOrMore("一个或多个")
    , "协议")
    .Literal("://", "协议分隔符")
    .NamedGroup("domain", g => g
        .NegatedCharacterSet("/:# ", "非特殊字符").OneOrMore("一个或多个")
    , "域名")
    .Group(g => g
        .Colon("冒号")
        .NamedGroup("port", p => p
            .Digits(1, 5, "端口号")
        , "端口")
    , "端口组").ZeroOrOne("可选端口")
    .NamedGroup("path", g => g
        .NegatedCharacterSet("# ", "非特殊字符").ZeroOrMore("零个或多个")
    , "路径")
    .Build();

// 使用快捷方法
var quickUrlPattern = YRegexBuilder.Create()
    .QuickURL(true, "完整URL")
    .Build();

// 测试
var urls = new[]
{
    "https://www.example.com:8080/path/to/resource",
    "http://localhost:3000/api/users",
    "ftp://files.example.com/downloads",
    "https://github.com/user/repo"
};

foreach (var url in urls)
{
    var match = urlParsePattern.Match(url);
    if (match.Success)
    {
        Console.WriteLine($"URL: {url}");
        Console.WriteLine($"  协议: {match.Groups["protocol"].Value}");
        Console.WriteLine($"  域名: {match.Groups["domain"].Value}");
        Console.WriteLine($"  端口: {match.Groups["port"].Value}");
        Console.WriteLine($"  路径: {match.Groups["path"].Value}");
        Console.WriteLine();
    }
}
```

### 5. 重复单词检测（菜鸟教程示例）

```csharp
// 传统正则：/\b([a-z]+) \1\b/gi
// 检测连续重复的单词
var duplicateWordPattern = YRegexBuilder.Create()
    .WordBoundary("单词边界")
    .NamedGroup("word", g => g
        .Letters(1, 20, "单词字母")
    , "单词")
    .Space("空格")
    .BackReference("word", "重复单词")
    .WordBoundary("单词边界")
    .IgnoreCase("忽略大小写")
    .Build();

// 测试
var textWithDuplicates = "This is is a test test sentence with with duplicate words.";
var matches = duplicateWordPattern.Matches(textWithDuplicates);

Console.WriteLine($"原文: {textWithDuplicates}");
Console.WriteLine("发现重复单词:");
foreach (Match match in matches)
{
    Console.WriteLine($"  '{match.Groups["word"].Value}' 在位置 {match.Index}");
}
```

### 6. 空行匹配（菜鸟教程示例）

```csharp
// 传统正则：/^\s*$/
// 匹配空行（只包含空白字符的行）
var emptyLinePattern = YRegexBuilder.Create()
    .StartOfLine("行开始")
    .Whitespace("空白字符").ZeroOrMore("零个或多个")
    .EndOfLine("行结束")
    .Build();

// 测试
var textWithEmptyLines = @"Line 1

Line 3

Line 5";

var lines = textWithEmptyLines.Split('\n');
for (int i = 0; i < lines.Length; i++)
{
    if (emptyLinePattern.IsMatch(lines[i]))
    {
        Console.WriteLine($"第 {i + 1} 行是空行");
    }
    else
    {
        Console.WriteLine($"第 {i + 1} 行: '{lines[i]}'");
    }
}
```

### 7. ID 号验证（菜鸟教程示例）

```csharp
// 传统正则：/\d{2}-\d{5}/
// 验证由两位数字、一个连字符再加5位数字组成的ID号
var idPattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .Digits(2, "两位数字")
    .Hyphen("连字符")
    .Digits(5, "五位数字")
    .EndOfString("结束")
    .Build();

// 更灵活的版本，支持不同格式
var flexibleIdPattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .NamedGroup("prefix", g => g
        .Digits(2, 3, "前缀数字")
    , "前缀")
    .CharacterSet("-_", "分隔符")
    .NamedGroup("suffix", g => g
        .Digits(4, 6, "后缀数字")
    , "后缀")
    .EndOfString("结束")
    .Build();

// 测试
var idNumbers = new[] { "12-34567", "99-12345", "123-4567", "12_34567", "1-23456", "12-345" };

foreach (var id in idNumbers)
{
    var isValid = idPattern.IsMatch(id);
    var flexibleMatch = flexibleIdPattern.Match(id);

    Console.WriteLine($"ID: {id}");
    Console.WriteLine($"  标准格式: {(isValid ? "✅" : "❌")}");
    if (flexibleMatch.Success)
    {
        Console.WriteLine($"  灵活格式: ✅ (前缀: {flexibleMatch.Groups["prefix"].Value}, 后缀: {flexibleMatch.Groups["suffix"].Value})");
    }
    else
    {
        Console.WriteLine($"  灵活格式: ❌");
    }
    Console.WriteLine();
}
```

## 🏛️ 专业验证示例

### 1. 邮箱验证（多种复杂度）

```csharp
// 基础邮箱验证
var basicEmailPattern = YRegexBuilder.Create()
    .QuickEmail(false, "基础邮箱")
    .Build();

// 严格邮箱验证（符合RFC标准）
var strictEmailPattern = YRegexBuilder.Create()
    .QuickEmail(true, "严格邮箱")
    .Build();

// 自定义邮箱验证
var customEmailPattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .NamedGroup("localpart", g => g
        .AlphaNumeric("字母数字")
        .Or("或")
        .CharacterSet("._+-", "特殊字符")
        .OneOrMore("一个或多个")
    , "本地部分")
    .At("@符号")
    .NamedGroup("domain", g => g
        .AlphaNumeric("字母数字")
        .Or("或")
        .CharacterSet(".-", "域名字符")
        .OneOrMore("一个或多个")
    , "域名部分")
    .Dot("点号")
    .NamedGroup("tld", g => g
        .Letters(2, 6, "顶级域名")
    , "顶级域名")
    .EndOfString("结束")
    .Build();

// 测试不同邮箱格式
var emails = new[]
{
    "<EMAIL>",
    "<EMAIL>",
    "invalid.email",
    "user@",
    "user@domain",
    "<EMAIL>"
};

foreach (var email in emails)
{
    Console.WriteLine($"邮箱: {email}");
    Console.WriteLine($"  基础验证: {(basicEmailPattern.IsMatch(email) ? "✅" : "❌")}");
    Console.WriteLine($"  严格验证: {(strictEmailPattern.IsMatch(email) ? "✅" : "❌")}");

    var customMatch = customEmailPattern.Match(email);
    if (customMatch.Success)
    {
        Console.WriteLine($"  自定义验证: ✅");
        Console.WriteLine($"    本地部分: {customMatch.Groups["localpart"].Value}");
        Console.WriteLine($"    域名: {customMatch.Groups["domain"].Value}");
        Console.WriteLine($"    顶级域名: {customMatch.Groups["tld"].Value}");
    }
    else
    {
        Console.WriteLine($"  自定义验证: ❌");
    }
    Console.WriteLine();
}
```

### 2. 中国手机号验证

```csharp
// 传统正则：/^1[3-9]\d{9}$/
// 中国手机号：1开头，第二位3-9，总共11位
var chinesePhonePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .Literal("1", "1开头")
    .CharacterRange('3', '9', "第二位3-9")
    .Digits(9, "后9位数字")
    .EndOfString("结束")
    .Build();

// 更详细的手机号验证（区分运营商）
var detailedPhonePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .Literal("1", "1开头")
    .NamedGroup("carrier", g => g
        .CharacterSet("34578", "移动")
        .Or("或")
        .CharacterSet("56", "联通")
        .Or("或")
        .CharacterSet("89", "电信")
    , "运营商标识")
    .NamedGroup("number", g => g
        .Digits(9, "号码")
    , "号码部分")
    .EndOfString("结束")
    .Build();

// 使用快捷方法
var quickPhonePattern = YRegexBuilder.Create()
    .QuickPhone("china", "中国手机号")
    .Build();

// 测试
var phoneNumbers = new[]
{
    "13812345678", "15987654321", "18612345678",
    "12345678901", "1381234567", "138123456789"
};

foreach (var phone in phoneNumbers)
{
    Console.WriteLine($"手机号: {phone}");
    Console.WriteLine($"  基础验证: {(chinesePhonePattern.IsMatch(phone) ? "✅" : "❌")}");
    Console.WriteLine($"  快捷验证: {(quickPhonePattern.IsMatch(phone) ? "✅" : "❌")}");

    var detailedMatch = detailedPhonePattern.Match(phone);
    if (detailedMatch.Success)
    {
        var carrier = detailedMatch.Groups["carrier"].Value;
        var carrierName = carrier switch
        {
            "3" or "4" or "5" or "7" or "8" => "移动",
            "5" or "6" => "联通",
            "8" or "9" => "电信",
            _ => "未知"
        };
        Console.WriteLine($"  详细验证: ✅ (运营商: {carrierName})");
    }
    else
    {
        Console.WriteLine($"  详细验证: ❌");
    }
    Console.WriteLine();
}
```

### 3. 密码强度验证

```csharp
// 弱密码：至少6位字母数字
var weakPasswordPattern = YRegexBuilder.Create()
    .QuickPassword("weak", "弱密码")
    .Build();

// 中等密码：至少8位，包含字母和数字
var mediumPasswordPattern = YRegexBuilder.Create()
    .QuickPassword("medium", "中等密码")
    .Build();

// 强密码：至少8位，包含大小写字母、数字和特殊字符
var strongPasswordPattern = YRegexBuilder.Create()
    .QuickPassword("strong", "强密码")
    .Build();

// 自定义密码验证（使用前瞻断言）
var customPasswordPattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .PositiveLookahead(la => la
        .AnyCharacter("任意字符").ZeroOrMore("零个或多个")
        .CharacterRange('a', 'z', "小写字母")
    , "包含小写字母")
    .PositiveLookahead(la => la
        .AnyCharacter("任意字符").ZeroOrMore("零个或多个")
        .CharacterRange('A', 'Z', "大写字母")
    , "包含大写字母")
    .PositiveLookahead(la => la
        .AnyCharacter("任意字符").ZeroOrMore("零个或多个")
        .Digit("数字")
    , "包含数字")
    .PositiveLookahead(la => la
        .AnyCharacter("任意字符").ZeroOrMore("零个或多个")
        .CharacterSet("!@#$%^&*", "特殊字符")
    , "包含特殊字符")
    .AnyCharacter("任意字符").Between(8, 20, "8-20位")
    .EndOfString("结束")
    .Build();

// 测试密码
var passwords = new[]
{
    "123456", "password", "Password1", "Password1!",
    "Weak", "StrongPass123!", "simple", "Complex1@"
};

foreach (var password in passwords)
{
    Console.WriteLine($"密码: {password}");
    Console.WriteLine($"  弱密码: {(weakPasswordPattern.IsMatch(password) ? "✅" : "❌")}");
    Console.WriteLine($"  中等密码: {(mediumPasswordPattern.IsMatch(password) ? "✅" : "❌")}");
    Console.WriteLine($"  强密码: {(strongPasswordPattern.IsMatch(password) ? "✅" : "❌")}");
    Console.WriteLine($"  自定义验证: {(customPasswordPattern.IsMatch(password) ? "✅" : "❌")}");
    Console.WriteLine();
}
```

### 4. 日期时间验证

```csharp
// ISO 8601 日期格式：YYYY-MM-DD
var isoDatePattern = YRegexBuilder.Create()
    .QuickDate("iso", "ISO日期")
    .Build();

// 美式日期格式：MM/DD/YYYY
var usDatePattern = YRegexBuilder.Create()
    .QuickDate("us", "美式日期")
    .Build();

// 中文日期格式：YYYY年MM月DD日
var chineseDatePattern = YRegexBuilder.Create()
    .QuickDate("chinese", "中文日期")
    .Build();

// 自定义日期验证（更严格的月份和日期验证）
var strictDatePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .NamedGroup("year", g => g
        .CharacterRange('1', '2', "年份首位")
        .Digits(3, "年份后三位")
    , "年份")
    .Hyphen("分隔符")
    .NamedGroup("month", g => g
        .Literal("0", "0开头")
        .CharacterRange('1', '9', "1-9月")
        .Or("或")
        .Literal("1", "1开头")
        .CharacterRange('0', '2', "10-12月")
    , "月份")
    .Hyphen("分隔符")
    .NamedGroup("day", g => g
        .CharacterSet("012", "日期首位")
        .Digit("日期末位")
        .Or("或")
        .Literal("3", "30-31日")
        .CharacterRange('01', "0-1")
    , "日期")
    .EndOfString("结束")
    .Build();

// 时间验证：HH:MM:SS
var timePattern = YRegexBuilder.Create()
    .QuickTime("24h", "24小时制")
    .Build();

// 测试日期时间
var dates = new[]
{
    "2024-03-15", "03/15/2024", "2024年03月15日",
    "2024-13-01", "2024-02-30", "invalid-date"
};

var times = new[] { "14:30:00", "09:05:30", "25:00:00", "12:60:00" };

Console.WriteLine("=== 日期验证 ===");
foreach (var date in dates)
{
    Console.WriteLine($"日期: {date}");
    Console.WriteLine($"  ISO格式: {(isoDatePattern.IsMatch(date) ? "✅" : "❌")}");
    Console.WriteLine($"  美式格式: {(usDatePattern.IsMatch(date) ? "✅" : "❌")}");
    Console.WriteLine($"  中文格式: {(chineseDatePattern.IsMatch(date) ? "✅" : "❌")}");
    Console.WriteLine($"  严格验证: {(strictDatePattern.IsMatch(date) ? "✅" : "❌")}");
    Console.WriteLine();
}

Console.WriteLine("=== 时间验证 ===");
foreach (var time in times)
{
    Console.WriteLine($"时间: {time} -> {(timePattern.IsMatch(time) ? "✅" : "❌")}");
}
```

### 5. 国际标准验证

```csharp
// ISBN 图书编号验证
var isbnPattern = YRegexBuilder.Create()
    .ISBN("isbn13", "ISBN-13")
    .Build();

// DOI 学术标识验证
var doiPattern = YRegexBuilder.Create()
    .DOI("DOI标识符")
    .Build();

// IBAN 国际银行账号验证
var ibanPattern = YRegexBuilder.Create()
    .IBAN("DE", "德国IBAN")
    .Build();

// UUID 验证
var uuidPattern = YRegexBuilder.Create()
    .UUID("v4", "UUID v4")
    .Build();

// 测试国际标准
var testData = new[]
{
    ("ISBN", "978-3-16-148410-0", isbnPattern),
    ("DOI", "10.1000/182", doiPattern),
    ("IBAN", "**********************", ibanPattern),
    ("UUID", "550e8400-e29b-41d4-a716-************", uuidPattern)
};

Console.WriteLine("=== 国际标准验证 ===");
foreach (var (type, value, pattern) in testData)
{
    Console.WriteLine($"{type}: {value} -> {(pattern.IsMatch(value) ? "✅" : "❌")}");
}
```

### 6. 中文内容验证

```csharp
// 中文字符验证
var chinesePattern = YRegexBuilder.Create()
    .ChineseCharacters("中文字符")
    .OneOrMore("一个或多个")
    .Build();

// 中文姓名验证（2-4个中文字符）
var chineseNamePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .ChineseCharacters("中文字符")
    .Between(2, 4, "2-4个字符")
    .EndOfString("结束")
    .Build();

// 中国身份证号验证（18位）
var chineseIdPattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .Digits(17, "前17位数字")
    .Group(g => g
        .Digit("数字")
        .Or("或")
        .CharacterSet("Xx", "X或x")
    , "校验位")
    .EndOfString("结束")
    .Build();

// 中国车牌号验证
var licensePlatePattern = YRegexBuilder.Create()
    .StartOfString("开始")
    .ChineseCharacters("省份简称")
    .AlphaNumeric("字母或数字")
    .CharacterSet("A-Z0-9", "车牌号码")
    .Between(5, 6, "5-6位")
    .EndOfString("结束")
    .Build();

// 测试中文内容
var chineseTests = new[]
{
    ("中文", "你好世界", chinesePattern),
    ("姓名", "张三", chineseNamePattern),
    ("身份证", "110101199003077777", chineseIdPattern),
    ("车牌", "京A12345", licensePlatePattern)
};

Console.WriteLine("=== 中文内容验证 ===");
foreach (var (type, value, pattern) in chineseTests)
{
    Console.WriteLine($"{type}: {value} -> {(pattern.IsMatch(value) ? "✅" : "❌")}");
}
```

## 📊 性能对比示例

```csharp
// 性能测试：传统正则 vs Zylo.YRegex
public class PerformanceComparison
{
    public static void ComparePerformance()
    {
        // 传统正则表达式
        var traditionalRegex = new Regex(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            RegexOptions.Compiled);

        // Zylo.YRegex
        var yRegexValidator = YRegexBuilder.Create()
            .QuickEmail(true, "严格邮箱")
            .Build();

        var testEmails = GenerateTestEmails(10000);

        // 测试传统正则
        var sw1 = Stopwatch.StartNew();
        int traditionalMatches = 0;
        foreach (var email in testEmails)
        {
            if (traditionalRegex.IsMatch(email))
                traditionalMatches++;
        }
        sw1.Stop();

        // 测试 Zylo.YRegex
        var sw2 = Stopwatch.StartNew();
        int yRegexMatches = 0;
        foreach (var email in testEmails)
        {
            if (yRegexValidator.IsMatch(email))
                yRegexMatches++;
        }
        sw2.Stop();

        Console.WriteLine("=== 性能对比 ===");
        Console.WriteLine($"测试邮箱数量: {testEmails.Length}");
        Console.WriteLine($"传统正则: {sw1.ElapsedMilliseconds}ms, 匹配: {traditionalMatches}");
        Console.WriteLine($"Zylo.YRegex: {sw2.ElapsedMilliseconds}ms, 匹配: {yRegexMatches}");
        Console.WriteLine($"性能比率: {(double)sw1.ElapsedMilliseconds / sw2.ElapsedMilliseconds:F2}x");
    }

    private static string[] GenerateTestEmails(int count)
    {
        var emails = new string[count];
        var random = new Random();

        for (int i = 0; i < count; i++)
        {
            if (i % 2 == 0)
            {
                // 生成有效邮箱
                emails[i] = $"user{i}@example{i % 100}.com";
            }
            else
            {
                // 生成无效邮箱
                emails[i] = $"invalid{i}@";
            }
        }

        return emails;
    }
}
```

## 🎓 学习总结

### Zylo.YRegex vs 传统正则表达式

| 特性 | 传统正则表达式 | Zylo.YRegex |
|------|----------------|-------------|
| **学习曲线** | 陡峭，需要记忆复杂语法 | 平缓，方法名即功能 |
| **可读性** | 差，复杂模式难以理解 | 优秀，代码即文档 |
| **维护性** | 困难，修改容易出错 | 简单，类型安全 |
| **调试** | 困难，错误难以定位 | 容易，编译时检查 |
| **复用性** | 低，难以组合 | 高，模块化设计 |
| **性能** | 需要手动优化 | 自动优化和缓存 |

### 最佳实践建议

1. **简单模式优先使用快捷方法**

   ```csharp
   // 推荐
   var emailValidator = YRegexBuilder.Create().QuickEmail().Build();

   // 不推荐（除非需要自定义）
   var complexEmailValidator = YRegexBuilder.Create()
       .StartOfString()
       .AlphaNumeric(1, 64)
       // ... 复杂构建
       .Build();
   ```

2. **复杂模式使用分组和命名**

   ```csharp
   var urlParser = YRegexBuilder.Create()
       .NamedGroup("protocol", g => g.WordCharacter().OneOrMore())
       .Literal("://")
       .NamedGroup("domain", g => g.AlphaNumeric().OneOrMore())
       .Build();
   ```

3. **利用描述信息进行文档化**

   ```csharp
   var validator = YRegexBuilder.Create()
       .Digits(3, "区号")
       .Hyphen("分隔符")
       .Digits(8, "电话号码")
       .Build();

   Console.WriteLine(validator.Description); // 自动生成描述
   ```

4. **性能敏感场景使用编译优化**

   ```csharp
   var validator = YRegexBuilder.Create()
       .QuickEmail()
       .Compiled(true) // 启用编译优化
       .Build();
   ```

## 🔗 相关资源

- [菜鸟教程正则表达式](https://www.runoob.com/regexp/regexp-tutorial.html)
- [Zylo.YRegex GitHub 仓库](https://github.com/YourRepo/Zylo.YRegex)
- [正则表达式在线测试工具](https://regex101.com/)
- [Unicode 字符类别参考](https://www.unicode.org/reports/tr44/)

---

**通过 Zylo.YRegex，让复杂的正则表达式变得简单易懂！** 🎉
