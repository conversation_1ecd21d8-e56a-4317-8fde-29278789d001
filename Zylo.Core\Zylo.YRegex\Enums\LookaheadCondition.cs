namespace Zylo.YRegex.Enums;

/// <summary>
/// 前瞻条件枚举
/// 用于密码强度验证等复杂条件
/// </summary>
public enum LookaheadCondition
{
    /// <summary>
    /// 包含大写字母
    /// </summary>
    HasUpperCase,

    /// <summary>
    /// 包含小写字母
    /// </summary>
    HasLowerCase,

    /// <summary>
    /// 包含数字
    /// </summary>
    HasDigit,

    /// <summary>
    /// 包含特殊字符
    /// </summary>
    HasSpecialChar,

    /// <summary>
    /// 不包含空白字符
    /// </summary>
    NoWhitespace,

    /// <summary>
    /// 不包含连续重复字符
    /// </summary>
    NoConsecutiveRepeats,

    /// <summary>
    /// 以字母开头
    /// </summary>
    StartsWithLetter,

    /// <summary>
    /// 以数字开头
    /// </summary>
    StartsWithDigit,

    /// <summary>
    /// 不以特殊字符开头
    /// </summary>
    NotStartsWithSpecial,

    /// <summary>
    /// 包含中文字符
    /// </summary>
    Has<PERSON>hine<PERSON>,

    /// <summary>
    /// 只包含ASCII字符
    /// </summary>
    OnlyAscii
}
