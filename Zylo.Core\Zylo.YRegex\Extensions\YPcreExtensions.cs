using PCRE;
using Zylo.YRegex.Validators;

namespace Zylo.YRegex.Extensions;

/// <summary>
/// PCRE.NET 扩展方法
/// 提供高性能的 PCRE 正则表达式功能
/// </summary>
public static class YPcreExtensions
{
    #region PCRE 编译和缓存

    /// <summary>
    /// 编译为 PCRE 正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>PCRE 正则表达式</returns>
    public static PcreRegex CompilePcre(this string pattern, PcreOptions options = PcreOptions.None)
    {
        return new PcreRegex(pattern, options);
    }

    /// <summary>
    /// 编译为带 JIT 优化的 PCRE 正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>PCRE 正则表达式</returns>
    public static PcreRegex CompilePcreWithJit(this string pattern, PcreOptions options = PcreOptions.None)
    {
        // 注意：PCRE.NET 的 JIT 编译可能在某些版本中不可用
        // 这里简化为普通编译
        return new PcreRegex(pattern, options);
    }

    /// <summary>
    /// 使用 PCRE 引擎进行匹配
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>匹配结果</returns>
    public static PcreMatch? YPcreMatch(this string input, string pattern, PcreOptions options = PcreOptions.None)
    {
        var regex = pattern.CompilePcre(options);
        return regex.Match(input);
    }

    /// <summary>
    /// 使用 PCRE 引擎进行全部匹配
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>所有匹配结果</returns>
    public static IEnumerable<PcreMatch> YPcreMatches(this string input, string pattern, PcreOptions options = PcreOptions.None)
    {
        var regex = pattern.CompilePcre(options);
        return regex.Matches(input);
    }

    /// <summary>
    /// 使用 PCRE 引擎检查是否匹配
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>是否匹配</returns>
    public static bool YPcreIsMatch(this string input, string pattern, PcreOptions options = PcreOptions.None)
    {
        var regex = pattern.CompilePcre(options);
        return regex.IsMatch(input);
    }

    #endregion

    #region PCRE 高级功能

    /// <summary>
    /// 使用 PCRE 进行替换
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="replacement">替换字符串</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>替换后的字符串</returns>
    public static string YPcreReplace(this string input, string pattern, string replacement, PcreOptions options = PcreOptions.None)
    {
        var regex = pattern.CompilePcre(options);
        return regex.Replace(input, replacement);
    }

    /// <summary>
    /// 使用 PCRE 进行分割
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>分割后的字符串数组</returns>
    public static string[] YPcreSplit(this string input, string pattern, PcreOptions options = PcreOptions.None)
    {
        var regex = pattern.CompilePcre(options);
        return regex.Split(input).ToArray();
    }

    /// <summary>
    /// 使用递归模式匹配（PCRE 特有功能）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="recursivePattern">递归模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>匹配结果</returns>
    /// <example>
    /// // 匹配嵌套括号
    /// var result = input.YPcreRecursiveMatch(@"\((?:[^()]++|(?R))*+\)");
    /// </example>
    public static PcreMatch? YPcreRecursiveMatch(this string input, string recursivePattern, PcreOptions options = PcreOptions.None)
    {
        var regex = recursivePattern.CompilePcre(options);
        return regex.Match(input);
    }

    /// <summary>
    /// 使用条件模式匹配（PCRE 特有功能）
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="conditionalPattern">条件模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>匹配结果</returns>
    /// <example>
    /// // 条件匹配：如果前面有数字，则匹配字母
    /// var result = input.YPcreConditionalMatch(@"(\d+)?(?(1)[a-z]+|\d+)");
    /// </example>
    public static PcreMatch? YPcreConditionalMatch(this string input, string conditionalPattern, PcreOptions options = PcreOptions.None)
    {
        var regex = conditionalPattern.CompilePcre(options);
        return regex.Match(input);
    }

    #endregion

    #region YRegexValidator 的 PCRE 扩展

    /// <summary>
    /// 将 YRegexValidator 转换为 PCRE 正则表达式
    /// </summary>
    /// <param name="validator">YRegex 验证器</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>PCRE 正则表达式</returns>
    public static PcreRegex ToPcreRegex(this YRegexValidator validator, PcreOptions options = PcreOptions.None)
    {
        return validator.Pattern.CompilePcre(options);
    }

    /// <summary>
    /// 将 YRegexValidator 转换为带 JIT 优化的 PCRE 正则表达式
    /// </summary>
    /// <param name="validator">YRegex 验证器</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>PCRE 正则表达式</returns>
    public static PcreRegex ToPcreRegexWithJit(this YRegexValidator validator, PcreOptions options = PcreOptions.None)
    {
        return validator.Pattern.CompilePcreWithJit(options);
    }

    /// <summary>
    /// 使用 PCRE 引擎进行高性能匹配
    /// </summary>
    /// <param name="validator">YRegex 验证器</param>
    /// <param name="input">输入字符串</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>是否匹配</returns>
    public static bool PcreIsMatch(this YRegexValidator validator, string input, PcreOptions options = PcreOptions.None)
    {
        return input.YPcreIsMatch(validator.Pattern, options);
    }

    /// <summary>
    /// 使用 PCRE 引擎进行高性能匹配并返回详细结果
    /// </summary>
    /// <param name="validator">YRegex 验证器</param>
    /// <param name="input">输入字符串</param>
    /// <param name="options">PCRE 选项</param>
    /// <returns>匹配结果</returns>
    public static PcreMatch? PcreMatch(this YRegexValidator validator, string input, PcreOptions options = PcreOptions.None)
    {
        return input.YPcreMatch(validator.Pattern, options);
    }

    #endregion

    #region PCRE 性能优化

    /// <summary>
    /// PCRE 正则表达式缓存
    /// </summary>
    private static readonly Dictionary<string, PcreRegex> _pcreCache = new();
    private static readonly object _cacheLock = new();

    /// <summary>
    /// 获取或创建缓存的 PCRE 正则表达式
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="options">PCRE 选项</param>
    /// <param name="enableJit">是否启用 JIT 编译</param>
    /// <returns>PCRE 正则表达式</returns>
    public static PcreRegex GetOrCreatePcreRegex(string pattern, PcreOptions options = PcreOptions.None, bool enableJit = true)
    {
        var key = $"{pattern}|{options}|{enableJit}";

        lock (_cacheLock)
        {
            if (_pcreCache.TryGetValue(key, out var cachedRegex))
            {
                return cachedRegex;
            }

            var regex = new PcreRegex(pattern, options);
            // 注意：JIT 编译在某些 PCRE.NET 版本中可能不可用
            // if (enableJit)
            // {
            //     regex.Study(PcreStudyOptions.JitCompile);
            // }

            _pcreCache[key] = regex;
            return regex;
        }
    }

    /// <summary>
    /// 清除 PCRE 缓存
    /// </summary>
    public static void ClearPcreCache()
    {
        lock (_cacheLock)
        {
            _pcreCache.Clear();
        }
    }

    /// <summary>
    /// 获取 PCRE 缓存统计信息
    /// </summary>
    /// <returns>缓存统计</returns>
    public static (int Count, IEnumerable<string> Patterns) GetPcreCacheStats()
    {
        lock (_cacheLock)
        {
            return (_pcreCache.Count, _pcreCache.Keys.ToList());
        }
    }

    #endregion

    #region PCRE 特有模式

    /// <summary>
    /// 创建递归匹配模式（匹配嵌套结构）
    /// </summary>
    /// <param name="openChar">开始字符</param>
    /// <param name="closeChar">结束字符</param>
    /// <param name="allowedContent">允许的内容模式</param>
    /// <returns>递归匹配模式</returns>
    /// <example>
    /// var pattern = YPcreExtensions.CreateRecursivePattern('(', ')', @"[^()]*");
    /// // 生成: \((?:[^()]*+|(?R))*+\)
    /// </example>
    public static string CreateRecursivePattern(char openChar, char closeChar, string allowedContent = @"[^()]*")
    {
        var escapedOpen = System.Text.RegularExpressions.Regex.Escape(openChar.ToString());
        var escapedClose = System.Text.RegularExpressions.Regex.Escape(closeChar.ToString());
        return $@"{escapedOpen}(?:{allowedContent}++|(?R))*+{escapedClose}";
    }

    /// <summary>
    /// 创建条件匹配模式
    /// </summary>
    /// <param name="condition">条件模式</param>
    /// <param name="truePattern">条件为真时的模式</param>
    /// <param name="falsePattern">条件为假时的模式</param>
    /// <returns>条件匹配模式</returns>
    /// <example>
    /// var pattern = YPcreExtensions.CreateConditionalPattern(@"(\d+)", @"[a-z]+", @"\d+");
    /// // 生成: (\d+)?(?(1)[a-z]+|\d+)
    /// </example>
    public static string CreateConditionalPattern(string condition, string truePattern, string falsePattern)
    {
        return $"({condition})?(?(1){truePattern}|{falsePattern})";
    }

    #endregion
}
