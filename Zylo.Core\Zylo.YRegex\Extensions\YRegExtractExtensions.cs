using RegExtract;
using Zylo.YRegex.Validators;

namespace Zylo.YRegex.Extensions;

/// <summary>
/// RegExtract 扩展方法
/// 提供简化的数据提取功能
/// </summary>
public static class YRegExtractExtensions
{
    #region 基础提取方法

    /// <summary>
    /// 从字符串中提取数据到对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>提取的对象</returns>
    public static T? YExtract<T>(this string input, string pattern) where T : class, new()
    {
        try
        {
            // 简化的实现：使用反射映射命名组到属性
            var regex = new System.Text.RegularExpressions.Regex(pattern);
            var match = regex.Match(input);

            if (!match.Success) return null;

            var obj = new T();
            var properties = typeof(T).GetProperties();

            foreach (var prop in properties)
            {
                if (match.Groups[prop.Name].Success)
                {
                    var value = match.Groups[prop.Name].Value;
                    if (prop.PropertyType == typeof(string))
                    {
                        prop.SetValue(obj, value);
                    }
                }
            }

            return obj;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 从字符串中提取多个数据对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="input">输入字符串</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>提取的对象列表</returns>
    public static IEnumerable<T> YExtractMany<T>(this string input, string pattern) where T : class, new()
    {
        try
        {
            var regex = new System.Text.RegularExpressions.Regex(pattern);
            var matches = regex.Matches(input);
            var results = new List<T>();
            var properties = typeof(T).GetProperties();

            foreach (System.Text.RegularExpressions.Match match in matches)
            {
                if (!match.Success) continue;

                var obj = new T();

                // 映射命名组到属性
                foreach (var prop in properties)
                {
                    if (match.Groups[prop.Name].Success)
                    {
                        var value = match.Groups[prop.Name].Value;
                        if (prop.PropertyType == typeof(string))
                        {
                            prop.SetValue(obj, value);
                        }
                    }
                }

                // 如果没有命名组，尝试映射到第一个字符串属性
                if (properties.Length > 0 && properties[0].PropertyType == typeof(string))
                {
                    var firstProp = properties[0];
                    if (firstProp.GetValue(obj) == null || string.IsNullOrEmpty(firstProp.GetValue(obj)?.ToString()))
                    {
                        firstProp.SetValue(obj, match.Value);
                    }
                }

                results.Add(obj);
            }

            return results;
        }
        catch
        {
            return Enumerable.Empty<T>();
        }
    }

    /// <summary>
    /// 使用 YRegexValidator 提取数据
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="input">输入字符串</param>
    /// <param name="validator">YRegex 验证器</param>
    /// <returns>提取的对象</returns>
    public static T? YExtract<T>(this string input, YRegexValidator validator) where T : class, new()
    {
        return input.YExtract<T>(validator.Pattern);
    }

    /// <summary>
    /// 使用 YRegexValidator 提取多个数据对象
    /// </summary>
    /// <typeparam name="T">目标对象类型</typeparam>
    /// <param name="input">输入字符串</param>
    /// <param name="validator">YRegex 验证器</param>
    /// <returns>提取的对象列表</returns>
    public static IEnumerable<T> YExtractMany<T>(this string input, YRegexValidator validator) where T : class, new()
    {
        return input.YExtractMany<T>(validator.Pattern);
    }

    #endregion

    #region 常用数据提取

    /// <summary>
    /// 提取邮箱地址
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>邮箱地址列表</returns>
    public static IEnumerable<string> YExtractEmails(this string input)
    {
        var pattern = @"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}";
        return input.YExtractMany<EmailData>(pattern).Select(e => e.Email);
    }

    /// <summary>
    /// 提取手机号码
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>手机号码列表</returns>
    public static IEnumerable<string> YExtractPhones(this string input)
    {
        var pattern = @"1[3-9]\d{9}";
        return input.YExtractMany<PhoneData>(pattern).Select(p => p.Phone);
    }

    /// <summary>
    /// 提取 URL 地址
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>URL 地址列表</returns>
    public static IEnumerable<string> YExtractUrls(this string input)
    {
        var pattern = @"https?://[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})+(?:/[^\s]*)?";
        return input.YExtractMany<UrlData>(pattern).Select(u => u.Url);
    }

    /// <summary>
    /// 提取 IPv4 地址
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>IPv4 地址列表</returns>
    public static IEnumerable<string> YExtractIPv4(this string input)
    {
        var pattern = @"(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)";
        return input.YExtractMany<IPv4Data>(pattern).Select(ip => ip.IP);
    }

    /// <summary>
    /// 提取日期
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>日期对象列表</returns>
    public static IEnumerable<DateData> YExtractDates(this string input)
    {
        var pattern = @"(?<Year>\d{4})-(?<Month>\d{2})-(?<Day>\d{2})";
        return input.YExtractMany<DateData>(pattern);
    }

    /// <summary>
    /// 提取时间
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>时间对象列表</returns>
    public static IEnumerable<TimeData> YExtractTimes(this string input)
    {
        var pattern = @"(?<Hour>[01][0-9]|2[0-3]):(?<Minute>[0-5][0-9]):(?<Second>[0-5][0-9])";
        return input.YExtractMany<TimeData>(pattern);
    }

    #endregion

    #region 数字提取

    /// <summary>
    /// 提取整数
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="allowNegative">是否允许负数</param>
    /// <returns>整数列表</returns>
    public static IEnumerable<int> YExtractIntegers(this string input, bool allowNegative = true)
    {
        var pattern = allowNegative ? @"-?\d+" : @"\d+";
        var regex = new System.Text.RegularExpressions.Regex(pattern);
        var matches = regex.Matches(input);

        var results = new List<int>();
        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            if (int.TryParse(match.Value, out var result))
            {
                // 如果不允许负数，检查原始匹配位置前是否有负号
                if (!allowNegative && match.Index > 0 && input[match.Index - 1] == '-')
                {
                    continue; // 跳过负数
                }

                results.Add(result);
            }
        }

        return results;
    }

    /// <summary>
    /// 提取小数
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="allowNegative">是否允许负数</param>
    /// <returns>小数列表</returns>
    public static IEnumerable<decimal> YExtractDecimals(this string input, bool allowNegative = true)
    {
        var pattern = allowNegative ? @"-?\d+(?:\.\d+)?" : @"\d+(?:\.\d+)?";
        var regex = new System.Text.RegularExpressions.Regex(pattern);
        var matches = regex.Matches(input);

        var results = new List<decimal>();
        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            if (decimal.TryParse(match.Value, out var result))
            {
                results.Add(result);
            }
        }

        return results;
    }

    /// <summary>
    /// 提取货币金额
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="currency">货币符号</param>
    /// <returns>货币金额对象列表</returns>
    public static IEnumerable<CurrencyData> YExtractCurrency(this string input, string currency = "¥")
    {
        var escapedCurrency = System.Text.RegularExpressions.Regex.Escape(currency);
        var pattern = $@"(?<Currency>{escapedCurrency})(?<Amount>\d+(?:\.\d{{2}})?)";
        return input.YExtractMany<CurrencyData>(pattern);
    }

    #endregion

    #region 复杂数据提取

    /// <summary>
    /// 提取键值对
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="separator">分隔符</param>
    /// <returns>键值对列表</returns>
    public static IEnumerable<KeyValueData> YExtractKeyValues(this string input, string separator = "=")
    {
        var escapedSeparator = System.Text.RegularExpressions.Regex.Escape(separator);
        var pattern = $@"(?<Key>[a-zA-Z_][a-zA-Z0-9_]*)\s*{escapedSeparator}\s*(?<Value>[^\r\n;,]+)";
        return input.YExtractMany<KeyValueData>(pattern);
    }

    /// <summary>
    /// 提取 HTML 标签
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>HTML 标签对象列表</returns>
    public static IEnumerable<HtmlTagData> YExtractHtmlTags(this string input)
    {
        var pattern = @"<(?<Tag>[a-zA-Z][a-zA-Z0-9]*)\s*(?<Attributes>[^>]*)>(?<Content>.*?)</\k<Tag>>";
        return input.YExtractMany<HtmlTagData>(pattern);
    }

    /// <summary>
    /// 提取 JSON 字段
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>JSON 字段对象列表</returns>
    public static IEnumerable<JsonFieldData> YExtractJsonFields(this string input)
    {
        var pattern = @"""(?<Key>[^""]+)""\s*:\s*""(?<Value>[^""]+)""";
        return input.YExtractMany<JsonFieldData>(pattern);
    }

    #endregion

    #region 数据模型

    /// <summary>
    /// 邮箱数据模型
    /// </summary>
    public class EmailData
    {
        public string Email { get; set; } = string.Empty;
    }

    /// <summary>
    /// 手机号数据模型
    /// </summary>
    public class PhoneData
    {
        public string Phone { get; set; } = string.Empty;
    }

    /// <summary>
    /// URL 数据模型
    /// </summary>
    public class UrlData
    {
        public string Url { get; set; } = string.Empty;
    }

    /// <summary>
    /// IPv4 数据模型
    /// </summary>
    public class IPv4Data
    {
        public string IP { get; set; } = string.Empty;
    }

    /// <summary>
    /// 日期数据模型
    /// </summary>
    public class DateData
    {
        public string Year { get; set; } = string.Empty;
        public string Month { get; set; } = string.Empty;
        public string Day { get; set; } = string.Empty;

        public DateTime ToDateTime()
        {
            if (int.TryParse(Year, out var year) &&
                int.TryParse(Month, out var month) &&
                int.TryParse(Day, out var day))
            {
                return new DateTime(year, month, day);
            }
            return DateTime.MinValue;
        }
    }

    /// <summary>
    /// 时间数据模型
    /// </summary>
    public class TimeData
    {
        public string Hour { get; set; } = string.Empty;
        public string Minute { get; set; } = string.Empty;
        public string Second { get; set; } = string.Empty;

        public TimeSpan ToTimeSpan()
        {
            if (int.TryParse(Hour, out var hour) &&
                int.TryParse(Minute, out var minute) &&
                int.TryParse(Second, out var second))
            {
                return new TimeSpan(hour, minute, second);
            }
            return TimeSpan.Zero;
        }
    }

    /// <summary>
    /// 整数数据模型
    /// </summary>
    public class IntegerData
    {
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// 小数数据模型
    /// </summary>
    public class DecimalData
    {
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// 货币数据模型
    /// </summary>
    public class CurrencyData
    {
        public string Currency { get; set; } = string.Empty;
        public string Amount { get; set; } = string.Empty;

        public decimal GetAmount()
        {
            return decimal.TryParse(Amount, out var result) ? result : 0;
        }
    }

    /// <summary>
    /// 键值对数据模型
    /// </summary>
    public class KeyValueData
    {
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    /// <summary>
    /// HTML 标签数据模型
    /// </summary>
    public class HtmlTagData
    {
        public string Tag { get; set; } = string.Empty;
        public string Attributes { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
    }

    /// <summary>
    /// JSON 字段数据模型
    /// </summary>
    public class JsonFieldData
    {
        public string Key { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    #endregion
}
