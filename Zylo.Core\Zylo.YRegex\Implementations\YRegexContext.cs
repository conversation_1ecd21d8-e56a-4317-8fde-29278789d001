using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Zylo.YRegex.Core;

namespace Zylo.YRegex.Implementations;

/// <summary>
/// YRegex 上下文实现
/// 提供正则表达式的执行环境和缓存管理
/// </summary>
public class YRegexContext : IYRegexContext, IDisposable
{
    private readonly YRegexOptions _options;
    private readonly IMemoryCache _cache;
    private readonly ILogger<YRegexContext>? _logger;
    private readonly ConcurrentDictionary<string, (int HitCount, DateTime LastAccess)> _cacheStats;
    private bool _disposed;

    /// <summary>
    /// 初始化 YRegex 上下文
    /// </summary>
    /// <param name="options">配置选项</param>
    /// <param name="logger">日志记录器</param>
    public YRegexContext(YRegexOptions options, ILogger<YRegexContext>? logger = null)
    {
        _options = options ?? throw new ArgumentNullException(nameof(options));
        _logger = logger;
        _cacheStats = new ConcurrentDictionary<string, (int HitCount, DateTime LastAccess)>();

        // 创建内存缓存
        var cacheOptions = new MemoryCacheOptions
        {
            SizeLimit = _options.MaxCacheSize,
            CompactionPercentage = 0.25 // 当达到限制时，移除 25% 的条目
        };
        _cache = new MemoryCache(cacheOptions);

        _logger?.LogInformation("YRegexContext 已初始化，缓存大小限制: {MaxCacheSize}", _options.MaxCacheSize);
    }

    /// <inheritdoc />
    public RegexOptions Options
    {
        get => _options.DefaultRegexOptions;
        set => _options.DefaultRegexOptions = value;
    }

    /// <inheritdoc />
    public TimeSpan Timeout
    {
        get => _options.DefaultTimeout;
        set => _options.DefaultTimeout = value;
    }

    /// <inheritdoc />
    public bool EnableCache
    {
        get => _options.EnableCache;
        set => _options.EnableCache = value;
    }

    /// <inheritdoc />
    public int MaxCacheSize
    {
        get => _options.MaxCacheSize;
        set => _options.MaxCacheSize = value;
    }

    /// <inheritdoc />
    public bool EnablePerformanceMonitoring
    {
        get => _options.EnablePerformanceMonitoring;
        set => _options.EnablePerformanceMonitoring = value;
    }

    /// <inheritdoc />
    public Regex CreateRegex(string pattern)
    {
        if (string.IsNullOrEmpty(pattern))
            throw new ArgumentException("正则表达式模式不能为空", nameof(pattern));

        try
        {
            var regex = new Regex(pattern, _options.DefaultRegexOptions, _options.DefaultTimeout);
            
            if (_options.EnableVerboseLogging)
            {
                _logger?.LogDebug("创建正则表达式: {Pattern}, 选项: {Options}, 超时: {Timeout}", 
                    pattern, _options.DefaultRegexOptions, _options.DefaultTimeout);
            }

            return regex;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "创建正则表达式失败: {Pattern}", pattern);
            throw;
        }
    }

    /// <inheritdoc />
    public Regex GetOrCreateRegex(string pattern)
    {
        if (string.IsNullOrEmpty(pattern))
            throw new ArgumentException("正则表达式模式不能为空", nameof(pattern));

        if (!_options.EnableCache)
        {
            return CreateRegex(pattern);
        }

        var cacheKey = GenerateCacheKey(pattern);

        // 尝试从缓存获取
        if (_cache.TryGetValue(cacheKey, out Regex? cachedRegex) && cachedRegex != null)
        {
            // 更新缓存统计
            _cacheStats.AddOrUpdate(cacheKey, 
                (1, DateTime.UtcNow), 
                (key, value) => (value.HitCount + 1, DateTime.UtcNow));

            if (_options.EnableVerboseLogging)
            {
                _logger?.LogDebug("从缓存获取正则表达式: {Pattern}", pattern);
            }

            return cachedRegex;
        }

        // 创建新的正则表达式
        var regex = CreateRegex(pattern);

        // 添加到缓存
        var cacheEntryOptions = new MemoryCacheEntryOptions
        {
            Size = 1, // 每个条目占用 1 个单位
            SlidingExpiration = TimeSpan.FromMinutes(30), // 30分钟滑动过期
            Priority = CacheItemPriority.Normal
        };

        _cache.Set(cacheKey, regex, cacheEntryOptions);

        // 初始化缓存统计
        _cacheStats.TryAdd(cacheKey, (0, DateTime.UtcNow));

        if (_options.EnableVerboseLogging)
        {
            _logger?.LogDebug("创建并缓存正则表达式: {Pattern}", pattern);
        }

        return regex;
    }

    /// <inheritdoc />
    public void ClearCache()
    {
        if (_cache is MemoryCache memoryCache)
        {
            memoryCache.Compact(1.0); // 清除所有缓存条目
        }

        _cacheStats.Clear();

        _logger?.LogInformation("YRegex 缓存已清除");
    }

    /// <inheritdoc />
    public (int Count, int HitCount, int MissCount) GetCacheStats()
    {
        if (!_options.EnableCache)
        {
            return (0, 0, 0);
        }

        var totalHits = _cacheStats.Values.Sum(v => v.HitCount);
        var totalRequests = _cacheStats.Count;
        var missCount = Math.Max(0, totalRequests - totalHits);

        return (_cacheStats.Count, totalHits, missCount);
    }

    /// <summary>
    /// 生成缓存键
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <returns>缓存键</returns>
    private string GenerateCacheKey(string pattern)
    {
        // 包含模式、选项和超时时间来生成唯一的缓存键
        return $"{pattern}|{_options.DefaultRegexOptions}|{_options.DefaultTimeout.TotalMilliseconds}";
    }

    /// <summary>
    /// 获取详细的缓存统计信息
    /// </summary>
    /// <returns>详细缓存统计</returns>
    public Dictionary<string, object> GetDetailedCacheStats()
    {
        var stats = new Dictionary<string, object>();

        if (!_options.EnableCache)
        {
            stats["Enabled"] = false;
            return stats;
        }

        var (count, hitCount, missCount) = GetCacheStats();
        var hitRate = count > 0 ? (double)hitCount / (hitCount + missCount) * 100 : 0;

        stats["Enabled"] = true;
        stats["Count"] = count;
        stats["HitCount"] = hitCount;
        stats["MissCount"] = missCount;
        stats["HitRate"] = $"{hitRate:F2}%";
        stats["MaxSize"] = _options.MaxCacheSize;

        // 最常用的模式
        var topPatterns = _cacheStats
            .OrderByDescending(kvp => kvp.Value.HitCount)
            .Take(5)
            .ToDictionary(kvp => kvp.Key.Split('|')[0], kvp => kvp.Value.HitCount);
        stats["TopPatterns"] = topPatterns;

        return stats;
    }

    /// <summary>
    /// 清理过期的缓存统计
    /// </summary>
    public void CleanupExpiredStats()
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-24); // 清理24小时前的统计

        var expiredKeys = _cacheStats
            .Where(kvp => kvp.Value.LastAccess < cutoffTime)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var key in expiredKeys)
        {
            _cacheStats.TryRemove(key, out _);
        }

        if (expiredKeys.Count > 0)
        {
            _logger?.LogDebug("清理了 {Count} 个过期的缓存统计项", expiredKeys.Count);
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _cache?.Dispose();
            _cacheStats.Clear();
            _disposed = true;
            
            _logger?.LogInformation("YRegexContext 已释放");
        }
    }
}
