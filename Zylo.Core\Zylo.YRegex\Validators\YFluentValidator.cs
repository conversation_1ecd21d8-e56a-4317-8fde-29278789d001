using FluentValidation;
using Zylo.YRegex.Builders;

namespace Zylo.YRegex.Validators;

/// <summary>
/// FluentValidation 集成的 YRegex 验证器
/// 提供强类型的验证规则定义
/// </summary>
/// <typeparam name="T">要验证的对象类型</typeparam>
public class YFluentValidator<T> : AbstractValidator<T>
{
    /// <summary>
    /// 添加正则表达式验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="validator">YRegex 验证器</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YRegexRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        YRegexValidator validator,
        string? errorMessage = null)
    {
        RuleFor(expression)
            .Must(value => string.IsNullOrEmpty(value) || validator.IsMatch(value))
            .WithMessage(errorMessage ?? $"字段必须符合格式: {validator.Description}");

        return this;
    }

    /// <summary>
    /// 添加邮箱验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YEmailRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        string? errorMessage = null)
    {
        var emailValidator = YRegexBuilder.Create().Email().Build();
        return YRegexRule(expression, emailValidator, errorMessage ?? "请输入有效的邮箱地址");
    }

    /// <summary>
    /// 添加手机号验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YPhoneRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        string? errorMessage = null)
    {
        var phoneValidator = YRegexBuilder.Create().Phone().Build();
        return YRegexRule(expression, phoneValidator, errorMessage ?? "请输入有效的手机号码");
    }

    /// <summary>
    /// 添加 URL 验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YUrlRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        string? errorMessage = null)
    {
        var urlValidator = YRegexBuilder.Create().Url().Build();
        return YRegexRule(expression, urlValidator, errorMessage ?? "请输入有效的网址");
    }

    /// <summary>
    /// 添加身份证号验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YIdCardRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        string? errorMessage = null)
    {
        var idCardValidator = YRegexBuilder.Create().IdCard().Build();
        return YRegexRule(expression, idCardValidator, errorMessage ?? "请输入有效的身份证号码");
    }

    /// <summary>
    /// 添加用户名验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YUsernameRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        int minLength = 3,
        int maxLength = 20,
        string? errorMessage = null)
    {
        var usernameValidator = YRegexBuilder.Create().Username(minLength, maxLength).Build();
        return YRegexRule(expression, usernameValidator,
            errorMessage ?? $"用户名长度必须在 {minLength}-{maxLength} 位之间，只能包含字母、数字和下划线");
    }

    /// <summary>
    /// 添加密码强度验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="requireSpecialChar">是否要求特殊字符</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YPasswordRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        int minLength = 8,
        int maxLength = 20,
        bool requireSpecialChar = false,
        string? errorMessage = null)
    {
        var passwordValidator = YRegexBuilder.Create().Password(minLength, maxLength, requireSpecialChar).Build();
        var specialCharMsg = requireSpecialChar ? "、特殊字符" : "";
        return YRegexRule(expression, passwordValidator,
            errorMessage ?? $"密码长度必须在 {minLength}-{maxLength} 位之间，必须包含大写字母、小写字母、数字{specialCharMsg}");
    }

    /// <summary>
    /// 添加中文姓名验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YChineseNameRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        string? errorMessage = null)
    {
        var nameValidator = YRegexBuilder.Create().ChineseName().Build();
        return YRegexRule(expression, nameValidator, errorMessage ?? "请输入有效的中文姓名");
    }

    /// <summary>
    /// 添加自定义构建器验证规则
    /// </summary>
    /// <param name="expression">属性表达式</param>
    /// <param name="builderAction">构建器配置</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>验证器实例</returns>
    public YFluentValidator<T> YCustomRule(
        System.Linq.Expressions.Expression<Func<T, string?>> expression,
        Action<YRegexBuilder> builderAction,
        string? errorMessage = null)
    {
        var builder = YRegexBuilder.Create();
        builderAction(builder);
        var validator = builder.Build();

        return YRegexRule(expression, validator, errorMessage ?? $"字段必须符合格式: {validator.Description}");
    }
}

/// <summary>
/// YFluentValidator 扩展方法
/// </summary>
public static class YFluentValidatorExtensions
{
    /// <summary>
    /// 创建 YFluentValidator 实例
    /// </summary>
    /// <typeparam name="T">要验证的对象类型</typeparam>
    /// <returns>YFluentValidator 实例</returns>
    public static YFluentValidator<T> CreateYValidator<T>()
    {
        return new YFluentValidator<T>();
    }

    /// <summary>
    /// 为 FluentValidation 的 RuleBuilder 添加 YRegex 验证
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="ruleBuilder">规则构建器</param>
    /// <param name="validator">YRegex 验证器</param>
    /// <returns>规则构建器</returns>
    public static IRuleBuilderOptions<T, string?> YRegex<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        YRegexValidator validator)
    {
        return ruleBuilder
            .Must(value => string.IsNullOrEmpty(value) || validator.IsMatch(value))
            .WithMessage($"字段必须符合格式: {validator.Description}");
    }

    /// <summary>
    /// 为 FluentValidation 的 RuleBuilder 添加邮箱验证
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="ruleBuilder">规则构建器</param>
    /// <returns>规则构建器</returns>
    public static IRuleBuilderOptions<T, string?> YEmail<T>(
        this IRuleBuilder<T, string?> ruleBuilder)
    {
        var emailValidator = YRegexBuilder.Create().Email().Build();
        return ruleBuilder.YRegex(emailValidator).WithMessage("请输入有效的邮箱地址");
    }

    /// <summary>
    /// 为 FluentValidation 的 RuleBuilder 添加手机号验证
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="ruleBuilder">规则构建器</param>
    /// <returns>规则构建器</returns>
    public static IRuleBuilderOptions<T, string?> YPhone<T>(
        this IRuleBuilder<T, string?> ruleBuilder)
    {
        var phoneValidator = YRegexBuilder.Create().Phone().Build();
        return ruleBuilder.YRegex(phoneValidator).WithMessage("请输入有效的手机号码");
    }

    /// <summary>
    /// 为 FluentValidation 的 RuleBuilder 添加自定义 YRegex 验证
    /// </summary>
    /// <typeparam name="T">对象类型</typeparam>
    /// <param name="ruleBuilder">规则构建器</param>
    /// <param name="builderAction">构建器配置</param>
    /// <returns>规则构建器</returns>
    public static IRuleBuilderOptions<T, string?> YCustom<T>(
        this IRuleBuilder<T, string?> ruleBuilder,
        Action<YRegexBuilder> builderAction)
    {
        var builder = YRegexBuilder.Create();
        builderAction(builder);
        var validator = builder.Build();

        return ruleBuilder.YRegex(validator);
    }
}
