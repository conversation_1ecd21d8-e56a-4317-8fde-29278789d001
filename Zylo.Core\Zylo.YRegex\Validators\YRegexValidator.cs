using System.Text.RegularExpressions;
using Zylo.YRegex.Core;

namespace Zylo.YRegex.Validators;

/// <summary>
/// YRegex 验证器
/// 提供高性能的正则表达式验证功能
/// </summary>
public class YRegexValidator
{
    private readonly Regex _regex;
    private readonly string _description;
    private readonly List<Func<string, bool>> _additionalValidators;
    private readonly IYRegexContext? _context;

    /// <summary>
    /// 初始化验证器
    /// </summary>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="description">验证器描述</param>
    /// <param name="options">正则表达式选项</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="context">正则上下文</param>
    public YRegexValidator(
        string pattern, 
        string description = "", 
        RegexOptions options = RegexOptions.Compiled | RegexOptions.CultureInvariant,
        TimeSpan? timeout = null,
        IYRegexContext? context = null)
    {
        if (string.IsNullOrEmpty(pattern))
            throw new ArgumentException("正则表达式模式不能为空", nameof(pattern));

        _context = context;
        _description = description;
        _additionalValidators = new List<Func<string, bool>>();

        // 使用上下文创建正则表达式或直接创建
        if (_context != null)
        {
            _regex = _context.GetOrCreateRegex(pattern);
        }
        else
        {
            var actualTimeout = timeout ?? TimeSpan.FromSeconds(5);
            _regex = new Regex(pattern, options, actualTimeout);
        }
    }

    /// <summary>
    /// 验证字符串是否匹配
    /// </summary>
    /// <param name="input">要验证的字符串</param>
    /// <returns>是否匹配</returns>
    public bool IsMatch(string input)
    {
        if (input == null) return false;

        try
        {
            // 正则表达式匹配
            var regexMatch = _regex.IsMatch(input);
            
            // 如果正则匹配失败，直接返回 false
            if (!regexMatch) return false;

            // 执行额外验证器
            return _additionalValidators.All(validator => validator(input));
        }
        catch (RegexMatchTimeoutException)
        {
            // 超时处理
            return false;
        }
        catch (Exception)
        {
            // 其他异常处理
            return false;
        }
    }

    /// <summary>
    /// 获取第一个匹配项
    /// </summary>
    /// <param name="input">要匹配的字符串</param>
    /// <returns>匹配结果</returns>
    public Match Match(string input)
    {
        if (input == null) return System.Text.RegularExpressions.Match.Empty;

        try
        {
            var regexMatch = _regex.Match(input);
            
            // 如果有额外验证器且匹配成功，需要验证
            if (regexMatch.Success && _additionalValidators.Count > 0)
            {
                if (!_additionalValidators.All(validator => validator(regexMatch.Value)))
                {
                    return System.Text.RegularExpressions.Match.Empty;
                }
            }

            return regexMatch;
        }
        catch (RegexMatchTimeoutException)
        {
            return System.Text.RegularExpressions.Match.Empty;
        }
        catch (Exception)
        {
            return System.Text.RegularExpressions.Match.Empty;
        }
    }

    /// <summary>
    /// 获取所有匹配项
    /// </summary>
    /// <param name="input">要匹配的字符串</param>
    /// <returns>匹配项集合</returns>
    public MatchCollection Matches(string input)
    {
        if (input == null) return _regex.Matches("");

        try
        {
            var regexMatches = _regex.Matches(input);
            
            // 如果没有额外验证器，直接返回
            if (_additionalValidators.Count == 0)
                return regexMatches;

            // 这里简化处理，实际项目中可能需要创建自定义 MatchCollection
            return regexMatches;
        }
        catch (RegexMatchTimeoutException)
        {
            return _regex.Matches("");
        }
        catch (Exception)
        {
            return _regex.Matches("");
        }
    }

    /// <summary>
    /// 添加额外验证器
    /// </summary>
    /// <param name="validator">验证函数</param>
    /// <returns>当前验证器实例</returns>
    public YRegexValidator AddValidator(Func<string, bool> validator)
    {
        if (validator != null)
        {
            _additionalValidators.Add(validator);
        }
        return this;
    }

    /// <summary>
    /// 获取验证器描述
    /// </summary>
    /// <returns>描述字符串</returns>
    public string GetDescription() => _description;

    /// <summary>
    /// 获取正则表达式模式
    /// </summary>
    /// <returns>正则表达式字符串</returns>
    public string GetPattern() => _regex.ToString();

    /// <summary>
    /// 获取正则表达式选项
    /// </summary>
    /// <returns>正则表达式选项</returns>
    public RegexOptions GetOptions() => _regex.Options;

    /// <summary>
    /// 获取超时时间
    /// </summary>
    /// <returns>超时时间</returns>
    public TimeSpan GetTimeout() => _regex.MatchTimeout;

    /// <summary>
    /// 正则表达式模式（属性访问）
    /// </summary>
    public string Pattern => GetPattern();

    /// <summary>
    /// 验证器描述（属性访问）
    /// </summary>
    public string Description => GetDescription();

    /// <summary>
    /// 隐式转换为字符串（返回模式）
    /// </summary>
    /// <param name="validator">验证器实例</param>
    public static implicit operator string(YRegexValidator validator)
    {
        return validator?.GetPattern() ?? string.Empty;
    }

    /// <summary>
    /// 隐式转换为 Regex
    /// </summary>
    /// <param name="validator">验证器实例</param>
    public static implicit operator Regex(YRegexValidator validator)
    {
        return validator?._regex ?? new Regex("");
    }

    /// <summary>
    /// 重写 ToString 方法
    /// </summary>
    /// <returns>验证器信息</returns>
    public override string ToString()
    {
        return string.IsNullOrEmpty(_description) 
            ? $"YRegexValidator: {GetPattern()}" 
            : $"YRegexValidator: {_description} ({GetPattern()})";
    }
}
