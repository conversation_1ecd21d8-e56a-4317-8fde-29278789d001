<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
    <PackageId>Zylo.YRegex</PackageId>
    <Product>Zylo Regex Builder</Product>

    <!-- NuGet 包信息 -->
    <Description>🚀 Zylo.YRegex - 企业级正则表达式构建器和验证器。提供直观易用的流式API，支持复杂模式构建、智能验证、高性能匹配等现代化正则表达式处理功能。</Description>
    <PackageTags>regex;validation;builder;pattern;fluent;api;expression;match;zylo</PackageTags>
    <PackageProjectUrl>https://github.com/zylo/zylo-yregex</PackageProjectUrl>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

    <!-- 发布说明 -->
    <PackageReleaseNotes>
      🎉 v1.3.2 - 功能增强与稳定性提升：
      - 🔧 流式 API 正则表达式构建器
      - 🔍 智能验证和模式匹配
      - 🚀 高性能正则表达式处理
      - 📊 支持复杂模式构建
      - 💉 完整的依赖注入支持
    </PackageReleaseNotes>

    <!-- 文档生成 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
  <!-- ========== NuGet 包版本号 ========== -->
  <ItemGroup>
    <None Include="README.md" Pack="true" PackagePath="\" /><!-- 将README.md包含到NuGet包中 -->
  </ItemGroup>

  <ItemGroup>
    <!-- 正则表达式核心库 -->
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="PCRE.NET" Version="1.0.0" />
    <PackageReference Include="RegExtract" Version="2.0.0" />

    <!-- 现代化函数式解析库 -->
    <PackageReference Include="Parlot" Version="1.3.6" />
    <PackageReference Include="Pidgin" Version="3.4.0" />
    <PackageReference Include="Superpower" Version="3.0.0" />
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />

    <!-- Y系列集成 -->
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />

    <!-- 性能优化 -->
    <PackageReference Include="System.Memory" Version="4.5.5" />
    <PackageReference Include="System.Buffers" Version="4.5.1" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net6.0'">
    <PackageReference Include="System.Text.Json" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

</Project>