using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Zylo.YString.Core;

/// <summary>
/// 字符串操作工具箱核心接口
/// <para>提供字符串操作的基础功能，支持链式调用和函数式编程风格</para>
/// <para>设计原则：不可变性、流畅API、高性能</para>
/// </summary>
/// <remarks>
/// 该接口是整个字符串工具箱的核心，所有字符串操作都基于此接口构建。
/// 采用不可变设计模式，每次操作都返回新的实例，确保线程安全。
/// </remarks>
/// <example>
/// <code>
/// var result = StringOperationToolbox.From("Hello World")
///     .Apply(s => s.ToUpper())
///     .Apply(s => s.Replace(" ", "_"))
///     .ToString(); // "HELLO_WORLD"
/// </code>
/// </example>
public interface IStringOperationToolbox
{
    /// <summary>
    /// 获取当前字符串值
    /// </summary>
    /// <value>
    /// 当前工具箱实例包含的字符串值，永远不为null（空字符串表示为string.Empty）
    /// </value>
    /// <remarks>
    /// 该属性提供对内部字符串值的只读访问，确保数据的不可变性。
    /// 如果原始输入为null，会自动转换为string.Empty。
    /// </remarks>
    string Value { get; }

    /// <summary>
    /// 设置新的字符串值，创建新的工具箱实例
    /// </summary>
    /// <param name="value">要设置的新字符串值，null值会被转换为string.Empty</param>
    /// <returns>包含新字符串值的工具箱实例</returns>
    /// <remarks>
    /// 由于采用不可变设计，此方法不会修改当前实例，而是创建一个新的实例。
    /// 这确保了线程安全性和函数式编程的纯函数特性。
    /// </remarks>
    /// <example>
    /// <code>
    /// var original = StringOperationToolbox.From("Hello");
    /// var newInstance = original.Set("World");
    /// // original.Value 仍然是 "Hello"
    /// // newInstance.Value 是 "World"
    /// </code>
    /// </example>
    IStringOperationToolbox Set(string value);

    /// <summary>
    /// 应用同步转换函数到当前字符串值
    /// </summary>
    /// <param name="transform">
    /// 转换函数，接受当前字符串值作为参数，返回转换后的字符串
    /// </param>
    /// <returns>包含转换结果的新工具箱实例</returns>
    /// <exception cref="ArgumentNullException">当transform参数为null时抛出</exception>
    /// <remarks>
    /// 这是函数式编程的核心方法，允许应用任意的字符串转换逻辑。
    /// 转换函数应该是纯函数，不应该有副作用。
    /// 如果转换函数抛出异常，该异常会向上传播。
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = toolbox
    ///     .Apply(s => s.Trim())           // 去除首尾空格
    ///     .Apply(s => s.ToLowerCase())    // 转换为小写
    ///     .Apply(s => s.Replace(" ", "-")); // 替换空格为连字符
    /// </code>
    /// </example>
    IStringOperationToolbox Apply(Func<string, string> transform);

    /// <summary>
    /// 异步应用转换函数到当前字符串值
    /// </summary>
    /// <param name="transform">
    /// 异步转换函数，接受当前字符串值作为参数，返回包含转换结果的Task
    /// </param>
    /// <returns>包含转换结果的新工具箱实例的Task</returns>
    /// <exception cref="ArgumentNullException">当transform参数为null时抛出</exception>
    /// <remarks>
    /// 用于处理需要异步操作的字符串转换，如网络请求、文件I/O等。
    /// 支持async/await模式，可以与其他异步操作无缝集成。
    /// 转换函数中的异常会通过Task传播。
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = await toolbox
    ///     .ApplyAsync(async s => await TranslateAsync(s))
    ///     .ConfigureAwait(false);
    /// </code>
    /// </example>
    Task<IStringOperationToolbox> ApplyAsync(Func<string, Task<string>> transform);

    /// <summary>
    /// 获取最终的字符串结果
    /// </summary>
    /// <returns>当前工具箱实例包含的字符串值</returns>
    /// <remarks>
    /// 重写了Object.ToString()方法，提供了从工具箱实例到字符串的显式转换。
    /// 这使得工具箱可以在需要字符串的上下文中直接使用。
    /// 返回值与Value属性相同，但提供了更明确的语义。
    /// </remarks>
    /// <example>
    /// <code>
    /// string result = toolbox.ToString();
    /// // 或者利用隐式转换
    /// string result = toolbox;
    /// </code>
    /// </example>
    string ToString();
}

/// <summary>
/// 字符串截取操作接口
/// <para>提供多种字符串截取方式，支持位置截取、模式截取、条件截取等</para>
/// </summary>
/// <remarks>
/// 所有截取操作都是安全的，不会因为索引越界而抛出异常。
/// 当参数无效时，通常返回空字符串或原字符串，具体行为见各方法说明。
/// 截取操作遵循"宽进严出"原则：输入参数容错性强，输出结果严格可靠。
/// </remarks>
/// <example>
/// <code>
/// var text = "Hello, World! This is a test.";
/// var result = StringOperationToolbox.From(text)
///     .Slice(0, 5)                    // "Hello"
///     .SliceFrom("World")             // "World! This is a test."
///     .SliceBetween("Hello", "test"); // ", World! This is a "
/// </code>
/// </example>
public interface IStringSliceOperations
{
    /// <summary>
    /// 按位置和长度截取字符串片段
    /// </summary>
    /// <param name="start">起始位置（从0开始的索引）</param>
    /// <param name="length">要截取的字符数量</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>安全截取实现：</para>
    /// <list type="bullet">
    /// <item>如果start小于0或大于等于字符串长度，返回空字符串</item>
    /// <item>如果length小于等于0，返回空字符串</item>
    /// <item>如果start+length超出字符串边界，自动调整为可用的最大长度</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello World".Slice(0, 5)  // "Hello"
    /// "Hello World".Slice(6, 5)  // "World"
    /// "Hello World".Slice(6, 100) // "World" (自动调整长度)
    /// "Hello World".Slice(-1, 5)  // "" (无效起始位置)
    /// </code>
    /// </example>
    IStringOperationToolbox Slice(int start, int length);

    /// <summary>
    /// 从指定字符串首次出现的位置开始截取到字符串末尾
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>截取行为：</para>
    /// <list type="bullet">
    /// <item>包含startString本身在内的所有后续内容</item>
    /// <item>如果startString为null或空字符串，返回原字符串</item>
    /// <item>如果未找到startString，返回空字符串</item>
    /// <item>使用序数比较（区分大小写）</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello World! This is test.".SliceFrom("World")  // "World! This is test."
    /// "Hello World! This is test.".SliceFrom("xyz")    // ""
    /// "Hello World! This is test.".SliceFrom("")       // "Hello World! This is test."
    /// </code>
    /// </example>
    IStringOperationToolbox SliceFrom(string startString);

    /// <summary>
    /// 从字符串开始截取到指定字符串首次出现的位置（不包含该字符串）
    /// </summary>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>截取行为：</para>
    /// <list type="bullet">
    /// <item>不包含endString本身，只截取到其前面的内容</item>
    /// <item>如果endString为null或空字符串，返回原字符串</item>
    /// <item>如果未找到endString，返回原字符串</item>
    /// <item>使用序数比较（区分大小写）</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello World! This is test.".SliceTo("World")  // "Hello "
    /// "Hello World! This is test.".SliceTo("xyz")    // "Hello World! This is test."
    /// "Hello World! This is test.".SliceTo("")       // "Hello World! This is test."
    /// </code>
    /// </example>
    IStringOperationToolbox SliceTo(string endString);

    /// <summary>
    /// 截取两个标记字符串之间的内容（不包含标记字符串本身）
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>截取逻辑：</para>
    /// <list type="number">
    /// <item>首先查找startString的位置</item>
    /// <item>从startString结束位置开始查找endString</item>
    /// <item>返回两个标记之间的内容</item>
    /// </list>
    /// <para>边界情况：</para>
    /// <list type="bullet">
    /// <item>如果任一标记字符串为null或空，返回空字符串</item>
    /// <item>如果未找到startString，返回空字符串</item>
    /// <item>如果找到startString但未找到endString，返回空字符串</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello [World] Test".SliceBetween("[", "]")     // "World"
    /// "&lt;tag&gt;content&lt;/tag&gt;".SliceBetween("&lt;tag&gt;", "&lt;/tag&gt;") // "content"
    /// "Hello World".SliceBetween("x", "y")            // ""
    /// </code>
    /// </example>
    IStringOperationToolbox SliceBetween(string startString, string endString);

    /// <summary>
    /// 使用正则表达式模式截取第一个匹配的内容
    /// </summary>
    /// <param name="pattern">正则表达式模式字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>正则截取特性：</para>
    /// <list type="bullet">
    /// <item>只返回第一个匹配项的完整内容</item>
    /// <item>如果模式无效或编译失败，返回空字符串</item>
    /// <item>如果没有匹配项，返回空字符串</item>
    /// <item>支持标准.NET正则表达式语法</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Email: <EMAIL> Phone: 123".SliceByPattern(@"\w+@\w+\.\w+")  // "<EMAIL>"
    /// "Hello 123 World 456".SliceByPattern(@"\d+")                       // "123"
    /// "No numbers here".SliceByPattern(@"\d+")                           // ""
    /// </code>
    /// </example>
    IStringOperationToolbox SliceByPattern(string pattern);

    /// <summary>
    /// 按指定起始位置和长度截取字符串（Slice方法的别名）
    /// </summary>
    /// <param name="startPosition">起始位置（从0开始的索引）</param>
    /// <param name="length">要截取的字符数量</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// 此方法与Slice方法功能完全相同，提供更明确的语义化命名。
    /// 适用于需要强调"按长度截取"语义的场景。
    /// </remarks>
    /// <seealso cref="Slice(int, int)"/>
    IStringOperationToolbox SliceByLength(int startPosition, int length);
}

/// <summary>
/// 字符串查找操作接口
/// <para>提供多种字符串查找方式，支持精确查找、模式匹配、上下文查找等</para>
/// </summary>
/// <remarks>
/// <para>查找操作特点：</para>
/// <list type="bullet">
/// <item>所有查找操作都返回IStringSearchResult，包含位置、匹配内容等详细信息</item>
/// <item>支持单次查找和全局查找两种模式</item>
/// <item>提供正则表达式支持，满足复杂模式匹配需求</item>
/// <item>查找失败时不抛出异常，通过结果对象的Found属性判断</item>
/// </list>
/// <para>性能考虑：</para>
/// <list type="bullet">
/// <item>简单字符串查找使用优化的IndexOf算法</item>
/// <item>正则表达式查找会编译模式以提高重复使用性能</item>
/// <item>大文本查找时建议使用流式处理</item>
/// </list>
/// </remarks>
/// <example>
/// <code>
/// var text = "Hello World! Hello Universe!";
/// var result = StringOperationToolbox.From(text)
///     .FindAll("Hello");
/// Console.WriteLine($"找到 {result.Positions.Count} 个匹配");
/// </code>
/// </example>
public interface IStringSearchOperations
{
    /// <summary>
    /// 查找指定字符串的第一个匹配项
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <returns>包含查找结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>查找特性：</para>
    /// <list type="bullet">
    /// <item>只查找第一个匹配项，找到后立即停止</item>
    /// <item>使用序数字符串比较（区分大小写）</item>
    /// <item>如果searchString为null或空字符串，Found属性为false</item>
    /// <item>性能优化：对于单次查找，比FindAll更高效</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = toolbox.Find("World");
    /// if (result.Found)
    /// {
    ///     Console.WriteLine($"找到位置: {result.Positions[0]}");
    ///     Console.WriteLine($"匹配内容: {result.Matches[0]}");
    /// }
    /// </code>
    /// </example>
    IStringSearchResult Find(string searchString);

    /// <summary>
    /// 查找指定字符串的所有匹配项
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>全局查找特性：</para>
    /// <list type="bullet">
    /// <item>查找所有不重叠的匹配项</item>
    /// <item>返回的位置列表按出现顺序排序</item>
    /// <item>对于重叠匹配，采用贪婪策略（优先匹配较早出现的）</item>
    /// <item>适用于统计、替换等需要全部匹配的场景</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = toolbox.FindAll("test");
    /// Console.WriteLine($"共找到 {result.Positions.Count} 个匹配");
    /// foreach (var pos in result.Positions)
    /// {
    ///     Console.WriteLine($"位置: {pos}");
    /// }
    /// </code>
    /// </example>
    IStringSearchResult FindAll(string searchString);

    /// <summary>
    /// 使用正则表达式模式查找所有匹配项
    /// </summary>
    /// <param name="pattern">正则表达式模式字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>正则查找特性：</para>
    /// <list type="bullet">
    /// <item>支持完整的.NET正则表达式语法</item>
    /// <item>自动处理正则表达式编译错误，失败时Found为false</item>
    /// <item>返回完整匹配内容，不包括捕获组</item>
    /// <item>对于复杂模式，建议预编译正则表达式以提高性能</item>
    /// </list>
    /// <para>常用模式示例：</para>
    /// <list type="bullet">
    /// <item>邮箱：@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"</item>
    /// <item>数字：@"\d+"</item>
    /// <item>单词：@"\b\w+\b"</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = toolbox.FindByPattern(@"\b\w{4}\b"); // 查找4字母单词
    /// foreach (var match in result.Matches)
    /// {
    ///     Console.WriteLine($"找到单词: {match}");
    /// }
    /// </code>
    /// </example>
    IStringSearchResult FindByPattern(string pattern);

    /// <summary>
    /// 查找位于两个标记字符串之间的所有内容
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>区间查找逻辑：</para>
    /// <list type="number">
    /// <item>查找startString的所有出现位置</item>
    /// <item>对每个startString，从其结束位置开始查找对应的endString</item>
    /// <item>提取startString和endString之间的内容（不包含标记本身）</item>
    /// <item>支持嵌套和重叠的标记对</item>
    /// </list>
    /// <para>应用场景：</para>
    /// <list type="bullet">
    /// <item>提取HTML/XML标签内容</item>
    /// <item>解析配置文件中的节</item>
    /// <item>提取括号、引号内的内容</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var html = "&lt;div&gt;Hello&lt;/div&gt;&lt;div&gt;World&lt;/div&gt;";
    /// var result = toolbox.FindBetween("&lt;div&gt;", "&lt;/div&gt;");
    /// // result.Matches 包含: ["Hello", "World"]
    /// </code>
    /// </example>
    IStringSearchResult FindBetween(string startString, string endString);

    /// <summary>
    /// 查找指定字符串并包含其前后指定长度的上下文内容
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <param name="beforeContext">目标字符串前面要包含的字符数量</param>
    /// <param name="afterContext">目标字符串后面要包含的字符数量</param>
    /// <returns>包含带上下文匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>上下文查找特性：</para>
    /// <list type="bullet">
    /// <item>返回的匹配内容包含目标字符串及其上下文</item>
    /// <item>上下文长度会根据字符串边界自动调整</item>
    /// <item>适用于文本分析、关键词高亮等场景</item>
    /// <item>位置信息指向原始目标字符串的位置</item>
    /// </list>
    /// <para>边界处理：</para>
    /// <list type="bullet">
    /// <item>如果beforeContext超出字符串开始，从字符串开始计算</item>
    /// <item>如果afterContext超出字符串结束，到字符串结束为止</item>
    /// <item>负数的上下文长度会被视为0</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var text = "The quick brown fox jumps over the lazy dog";
    /// var result = toolbox.FindWithContext("fox", 5, 5);
    /// // result.Matches[0] 可能是: "brown fox jumps"
    /// // 包含"fox"前5个字符和后5个字符
    /// </code>
    /// </example>
    IStringSearchResult FindWithContext(string searchString, int beforeContext, int afterContext);
}

/// <summary>
/// 字符串查找结果接口
/// <para>封装字符串查找操作的结果，提供位置、内容、统计等信息</para>
/// </summary>
/// <remarks>
/// <para>结果对象特性：</para>
/// <list type="bullet">
/// <item>不可变对象，线程安全</item>
/// <item>延迟计算，只在访问时进行实际查找</item>
/// <item>提供多种结果访问方式</item>
/// <item>支持链式操作继续处理</item>
/// </list>
/// <para>性能说明：</para>
/// <list type="bullet">
/// <item>位置和匹配列表使用只读集合，避免意外修改</item>
/// <item>大量匹配时建议使用流式处理</item>
/// <item>结果对象可以缓存重复使用</item>
/// </list>
/// </remarks>
/// <example>
/// <code>
/// var result = toolbox.FindAll("test");
/// if (result.Found)
/// {
///     Console.WriteLine($"找到 {result.Positions.Count} 个匹配");
///     var firstMatch = result.Matches[0];
///     var firstPosition = result.Positions[0];
/// }
/// </code>
/// </example>
public interface IStringSearchResult
{
    /// <summary>
    /// 获取是否找到任何匹配项
    /// </summary>
    /// <value>
    /// 如果找到一个或多个匹配项返回true，否则返回false
    /// </value>
    /// <remarks>
    /// 这是检查查找操作是否成功的首选方式。
    /// 等价于 Positions.Count > 0 或 Matches.Count > 0。
    /// 在处理查找结果前应该首先检查此属性。
    /// </remarks>
    /// <example>
    /// <code>
    /// if (result.Found)
    /// {
    ///     // 安全地处理查找结果
    ///     ProcessMatches(result.Matches);
    /// }
    /// else
    /// {
    ///     Console.WriteLine("未找到匹配项");
    /// }
    /// </code>
    /// </example>
    bool Found { get; }

    /// <summary>
    /// 获取所有匹配项在原字符串中的位置列表
    /// </summary>
    /// <value>
    /// 只读的位置列表，按出现顺序排序，位置从0开始计算
    /// </value>
    /// <remarks>
    /// <para>位置信息特点：</para>
    /// <list type="bullet">
    /// <item>位置基于原始字符串的字符索引（从0开始）</item>
    /// <item>列表按位置升序排列</item>
    /// <item>对于重叠匹配，只包含非重叠的位置</item>
    /// <item>空结果时返回空列表（不是null）</item>
    /// </list>
    /// <para>使用场景：</para>
    /// <list type="bullet">
    /// <item>精确定位匹配内容</item>
    /// <item>计算匹配项之间的距离</item>
    /// <item>实现高亮显示功能</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var positions = result.Positions;
    /// for (int i = 0; i &lt; positions.Count; i++)
    /// {
    ///     Console.WriteLine($"第{i+1}个匹配位置: {positions[i]}");
    /// }
    /// </code>
    /// </example>
    IReadOnlyList<int> Positions { get; }

    /// <summary>
    /// 获取所有匹配的字符串内容列表
    /// </summary>
    /// <value>
    /// 只读的匹配内容列表，与位置列表一一对应
    /// </value>
    /// <remarks>
    /// <para>匹配内容特点：</para>
    /// <list type="bullet">
    /// <item>与Positions列表索引一一对应</item>
    /// <item>对于简单字符串查找，所有匹配内容相同</item>
    /// <item>对于正则表达式查找，可能包含不同的匹配内容</item>
    /// <item>对于上下文查找，包含目标字符串及其上下文</item>
    /// </list>
    /// <para>内容处理：</para>
    /// <list type="bullet">
    /// <item>保持原始字符串的大小写和格式</item>
    /// <item>不进行任何转义或编码处理</item>
    /// <item>空匹配表示为空字符串</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var matches = result.Matches;
    /// foreach (var match in matches)
    /// {
    ///     Console.WriteLine($"匹配内容: '{match}'");
    /// }
    /// </code>
    /// </example>
    IReadOnlyList<string> Matches { get; }

    /// <summary>
    /// 将查找结果转换为新的字符串工具箱实例以继续处理
    /// </summary>
    /// <returns>包含处理后内容的新工具箱实例</returns>
    /// <remarks>
    /// <para>转换逻辑：</para>
    /// <list type="bullet">
    /// <item>如果没有找到匹配项，返回包含空字符串的工具箱</item>
    /// <item>如果找到匹配项，将所有匹配内容用换行符连接</item>
    /// <item>返回的工具箱可以继续进行其他字符串操作</item>
    /// <item>这是实现链式操作的关键方法</item>
    /// </list>
    /// <para>使用场景：</para>
    /// <list type="bullet">
    /// <item>对查找结果进行进一步处理</item>
    /// <item>实现复杂的字符串操作链</item>
    /// <item>将查找结果作为下一步操作的输入</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var processedResult = toolbox
    ///     .FindAll("error")           // 查找所有错误
    ///     .GetResults()               // 获取查找结果
    ///     .Apply(s => s.ToUpper())    // 转换为大写
    ///     .ToString();                // 获取最终结果
    /// </code>
    /// </example>
    IStringOperationToolbox GetResults();
}
