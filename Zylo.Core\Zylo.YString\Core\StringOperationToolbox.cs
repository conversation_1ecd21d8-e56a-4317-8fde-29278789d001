using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Zylo.YString.Operations;

namespace Zylo.YString.Core;

/// <summary>
/// 字符串操作工具箱主类 - 超强字符串处理的核心实现
/// <para>提供200+字符串操作方法，支持截取、查找、替换、统计、位置操作等超灵活功能</para>
/// </summary>
/// <remarks>
/// <para>设计原则：</para>
/// <list type="bullet">
/// <item><strong>不可变性</strong>：所有操作都返回新实例，确保线程安全</item>
/// <item><strong>流畅API</strong>：支持链式调用，提供直观的编程体验</item>
/// <item><strong>高性能</strong>：优化的算法实现，最小化内存分配</item>
/// <item><strong>容错性</strong>：安全的边界检查，不会因无效参数抛出异常</item>
/// <item><strong>可扩展</strong>：模块化设计，支持自定义扩展</item>
/// </list>
/// <para>核心特性：</para>
/// <list type="bullet">
/// <item>支持.NET 6.0和.NET 8.0多目标框架</item>
/// <item>零外部依赖，纯净的核心库</item>
/// <item>完整的Unicode支持</item>
/// <item>异步操作支持</item>
/// <item>丰富的扩展方法</item>
/// </list>
/// </remarks>
/// <example>
/// <para>基础使用示例：</para>
/// <code>
/// // 创建工具箱实例
/// var toolbox = StringOperationToolbox.From("Hello, World!");
///
/// // 链式操作
/// var result = toolbox
///     .SliceTo("World")           // "Hello, "
///     .Apply(s => s.Trim())       // "Hello,"
///     .Apply(s => s.TrimEnd(','))  // "Hello"
///     .ToString();
/// </code>
/// <para>复杂操作示例：</para>
/// <code>
/// var text = "Hello World! Hello Universe! Hello Galaxy!";
/// var analysis = StringOperationToolbox.From(text)
///     .FindAll("Hello")                    // 查找所有"Hello"
///     .GetResults()                        // 获取查找结果
///     .Apply(s => $"找到{s.Split('\n').Length}个匹配");
/// </code>
/// </example>
public sealed class StringOperationToolbox : IStringOperationToolbox, IStringSliceOperations, IStringSearchOperations
{
    #region 私有字段

    /// <summary>
    /// 内部存储的字符串值
    /// </summary>
    /// <remarks>
    /// 使用readonly确保不可变性，null值在构造时转换为string.Empty
    /// </remarks>
    private readonly string _value;

    #endregion

    #region 构造函数

    /// <summary>
    /// 私有构造函数 - 确保只能通过静态工厂方法创建实例
    /// </summary>
    /// <param name="value">要封装的字符串值，null会被转换为string.Empty</param>
    /// <remarks>
    /// 私有构造函数实现了工厂模式，确保：
    /// <list type="bullet">
    /// <item>统一的实例创建方式</item>
    /// <item>一致的null值处理</item>
    /// <item>未来可能的实例缓存优化</item>
    /// </list>
    /// </remarks>
    private StringOperationToolbox(string value)
    {
        // 防御性编程：确保内部字符串永远不为null
        _value = value ?? string.Empty;
    }

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取当前工具箱实例包含的字符串值
    /// </summary>
    /// <value>当前字符串值，永远不为null</value>
    /// <remarks>
    /// 该属性提供对内部字符串的只读访问，保证数据的不可变性。
    /// 即使原始输入为null，也会返回string.Empty。
    /// </remarks>
    public string Value => _value;

    #endregion

    #region 静态工厂方法

    /// <summary>
    /// 从指定字符串创建工具箱实例
    /// </summary>
    /// <param name="value">初始字符串值，null会被转换为string.Empty</param>
    /// <returns>新的工具箱实例</returns>
    /// <remarks>
    /// 这是创建工具箱实例的主要方式，提供了清晰的语义。
    /// 方法名"From"表明了数据的来源和转换关系。
    /// </remarks>
    /// <example>
    /// <code>
    /// var toolbox1 = StringOperationToolbox.From("Hello World");
    /// var toolbox2 = StringOperationToolbox.From(null); // 等价于Empty
    /// var toolbox3 = StringOperationToolbox.From("");   // 等价于Empty
    /// </code>
    /// </example>
    public static StringOperationToolbox From(string value) => new(value);

    /// <summary>
    /// 创建包含空字符串的工具箱实例
    /// </summary>
    /// <value>包含空字符串的工具箱实例</value>
    /// <remarks>
    /// 提供了一个语义清晰的空实例创建方式，避免了From("")的歧义。
    /// 在需要空起点的操作链中特别有用。
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = StringOperationToolbox.Empty
    ///     .Apply(s => "Hello")
    ///     .Apply(s => s + " World")
    ///     .ToString(); // "Hello World"
    /// </code>
    /// </example>
    public static StringOperationToolbox Empty => new(string.Empty);

    #endregion

    #region 核心接口实现

    /// <summary>
    /// 设置新的字符串值，创建新的工具箱实例
    /// </summary>
    /// <param name="value">要设置的新字符串值</param>
    /// <returns>包含新字符串值的工具箱实例</returns>
    /// <remarks>
    /// 实现IStringOperationToolbox.Set方法。
    /// 由于不可变设计，此方法创建新实例而不是修改当前实例。
    /// </remarks>
    public IStringOperationToolbox Set(string value) => new StringOperationToolbox(value);

    /// <summary>
    /// 应用同步转换函数到当前字符串值
    /// </summary>
    /// <param name="transform">转换函数，不能为null</param>
    /// <returns>包含转换结果的新工具箱实例</returns>
    /// <exception cref="ArgumentNullException">当transform为null时抛出</exception>
    /// <remarks>
    /// <para>这是函数式编程的核心方法，支持任意的字符串转换逻辑。</para>
    /// <para>性能考虑：</para>
    /// <list type="bullet">
    /// <item>转换函数应该是纯函数，避免副作用</item>
    /// <item>对于复杂转换，考虑使用专门的方法而不是Apply</item>
    /// <item>转换函数的异常会直接传播给调用者</item>
    /// </list>
    /// </remarks>
    public IStringOperationToolbox Apply(Func<string, string> transform)
    {
        // 参数验证：确保转换函数不为null
        ArgumentNullException.ThrowIfNull(transform);

        // 应用转换函数并创建新实例
        return new StringOperationToolbox(transform(_value));
    }

    /// <summary>
    /// 异步应用转换函数到当前字符串值
    /// </summary>
    /// <param name="transform">异步转换函数，不能为null</param>
    /// <returns>包含转换结果的新工具箱实例的Task</returns>
    /// <exception cref="ArgumentNullException">当transform为null时抛出</exception>
    /// <remarks>
    /// <para>支持需要异步操作的字符串转换，如网络请求、文件I/O等。</para>
    /// <para>使用建议：</para>
    /// <list type="bullet">
    /// <item>配合ConfigureAwait(false)使用以避免死锁</item>
    /// <item>转换函数中的异常通过Task传播</item>
    /// <item>适用于I/O密集型的字符串处理操作</item>
    /// </list>
    /// </remarks>
    public async Task<IStringOperationToolbox> ApplyAsync(Func<string, Task<string>> transform)
    {
        // 参数验证：确保异步转换函数不为null
        ArgumentNullException.ThrowIfNull(transform);

        // 异步应用转换函数
        var result = await transform(_value).ConfigureAwait(false);

        // 创建包含结果的新实例
        return new StringOperationToolbox(result);
    }

    /// <summary>
    /// 获取字符串表示形式
    /// </summary>
    /// <returns>当前工具箱包含的字符串值</returns>
    /// <remarks>
    /// 重写Object.ToString()方法，提供从工具箱到字符串的显式转换。
    /// 返回值与Value属性相同，但语义更明确。
    /// </remarks>
    public override string ToString() => _value;

    #endregion

    #region 隐式转换操作符

    /// <summary>
    /// 定义从StringOperationToolbox到string的隐式转换
    /// </summary>
    /// <param name="toolbox">要转换的工具箱实例</param>
    /// <returns>工具箱包含的字符串值</returns>
    /// <remarks>
    /// 允许在需要字符串的上下文中直接使用工具箱实例。
    /// 这使得工具箱可以无缝集成到现有的字符串处理代码中。
    /// </remarks>
    /// <example>
    /// <code>
    /// StringOperationToolbox toolbox = StringOperationToolbox.From("Hello");
    /// string result = toolbox; // 隐式转换
    /// Console.WriteLine(result); // 输出: Hello
    /// </code>
    /// </example>
    public static implicit operator string(StringOperationToolbox toolbox) => toolbox._value;

    /// <summary>
    /// 定义从string到StringOperationToolbox的隐式转换
    /// </summary>
    /// <param name="value">要转换的字符串值</param>
    /// <returns>包含该字符串的新工具箱实例</returns>
    /// <remarks>
    /// 允许在需要工具箱的上下文中直接使用字符串。
    /// 这简化了工具箱的使用，减少了显式创建的代码。
    /// </remarks>
    /// <example>
    /// <code>
    /// StringOperationToolbox toolbox = "Hello World"; // 隐式转换
    /// var result = toolbox.SliceTo("World");
    /// </code>
    /// </example>
    public static implicit operator StringOperationToolbox(string value) => From(value);

    #endregion

    #region 字符串截取操作实现

    /// <summary>
    /// 按位置和长度安全截取字符串片段
    /// </summary>
    /// <param name="start">起始位置（从0开始的索引）</param>
    /// <param name="length">要截取的字符数量</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>安全截取实现，包含完整的边界检查：</para>
    /// <list type="bullet">
    /// <item>起始位置无效时返回空字符串</item>
    /// <item>长度无效时返回空字符串</item>
    /// <item>超出边界时自动调整到可用范围</item>
    /// </list>
    /// </remarks>
    public IStringOperationToolbox Slice(int start, int length)
    {
        // 边界检查：起始位置必须有效且长度必须为正数
        if (start < 0 || start >= _value.Length || length <= 0)
            return new StringOperationToolbox(string.Empty);

        // 计算实际可截取的长度，防止越界
        var actualLength = Math.Min(length, _value.Length - start);

        // 执行安全截取
        return new StringOperationToolbox(_value.Substring(start, actualLength));
    }

    /// <summary>
    /// 从指定标记字符串首次出现位置开始截取到字符串末尾
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>截取逻辑：</para>
    /// <list type="number">
    /// <item>查找startString在原字符串中的位置</item>
    /// <item>从该位置开始截取到字符串末尾</item>
    /// <item>包含startString本身</item>
    /// </list>
    /// </remarks>
    public IStringOperationToolbox SliceFrom(string startString)
    {
        // 参数验证：空字符串或null时返回原字符串
        if (string.IsNullOrEmpty(startString))
            return new StringOperationToolbox(_value);

        // 查找起始标记的位置（使用序数比较以提高性能）
        var index = _value.IndexOf(startString, StringComparison.Ordinal);

        // 未找到标记时返回空字符串
        if (index == -1)
            return new StringOperationToolbox(string.Empty);

        // 从找到的位置截取到末尾（使用.NET 6.0+的优化方法）
        return new StringOperationToolbox(_value[index..]);
    }

    /// <summary>
    /// 从字符串开始截取到指定标记字符串首次出现位置（不包含标记）
    /// </summary>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>截取逻辑：</para>
    /// <list type="number">
    /// <item>查找endString在原字符串中的位置</item>
    /// <item>从字符串开始截取到该位置</item>
    /// <item>不包含endString本身</item>
    /// </list>
    /// </remarks>
    public IStringOperationToolbox SliceTo(string endString)
    {
        // 参数验证：空字符串或null时返回原字符串
        if (string.IsNullOrEmpty(endString))
            return new StringOperationToolbox(_value);

        // 查找结束标记的位置
        var index = _value.IndexOf(endString, StringComparison.Ordinal);

        // 未找到标记时返回原字符串
        if (index == -1)
            return new StringOperationToolbox(_value);

        // 从开始截取到标记位置（使用.NET 6.0+的优化方法）
        return new StringOperationToolbox(_value[..index]);
    }

    /// <summary>
    /// 截取两个标记字符串之间的内容（不包含标记本身）
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>复杂截取逻辑的详细实现：</para>
    /// <list type="number">
    /// <item>验证两个标记字符串都不为空</item>
    /// <item>查找起始标记的位置</item>
    /// <item>从起始标记结束位置开始查找结束标记</item>
    /// <item>提取两个标记之间的内容</item>
    /// </list>
    /// <para>错误处理：任何步骤失败都返回空字符串</para>
    /// </remarks>
    public IStringOperationToolbox SliceBetween(string startString, string endString)
    {
        // 参数验证：两个标记都必须非空
        if (string.IsNullOrEmpty(startString) || string.IsNullOrEmpty(endString))
            return new StringOperationToolbox(string.Empty);

        // 第一步：查找起始标记的位置
        var startIndex = _value.IndexOf(startString, StringComparison.Ordinal);
        if (startIndex == -1)
            return new StringOperationToolbox(string.Empty);

        // 第二步：计算内容开始位置（跳过起始标记）
        startIndex += startString.Length;

        // 第三步：从内容开始位置查找结束标记
        var endIndex = _value.IndexOf(endString, startIndex, StringComparison.Ordinal);
        if (endIndex == -1)
            return new StringOperationToolbox(string.Empty);

        // 第四步：提取两个标记之间的内容（使用.NET 6.0+的范围语法）
        return new StringOperationToolbox(_value[startIndex..endIndex]);
    }

    /// <summary>
    /// 使用正则表达式模式截取第一个匹配的内容
    /// </summary>
    /// <param name="pattern">正则表达式模式字符串</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// <para>正则截取的安全实现：</para>
    /// <list type="bullet">
    /// <item>使用try-catch确保正则表达式错误不会导致程序崩溃</item>
    /// <item>只返回第一个完整匹配项</item>
    /// <item>编译失败或无匹配时返回空字符串</item>
    /// </list>
    /// <para>性能考虑：</para>
    /// <list type="bullet">
    /// <item>每次调用都会重新编译正则表达式</item>
    /// <item>对于重复使用的模式，建议预编译</item>
    /// <item>复杂模式可能影响性能</item>
    /// </list>
    /// </remarks>
    public IStringOperationToolbox SliceByPattern(string pattern)
    {
        // 参数验证：空模式时返回原字符串
        if (string.IsNullOrEmpty(pattern))
            return new StringOperationToolbox(_value);

        try
        {
            // 创建正则表达式对象（每次都重新编译）
            var regex = new System.Text.RegularExpressions.Regex(pattern);

            // 执行匹配操作
            var match = regex.Match(_value);

            // 返回匹配结果或空字符串
            return match.Success
                ? new StringOperationToolbox(match.Value)
                : new StringOperationToolbox(string.Empty);
        }
        catch
        {
            // 正则表达式编译或执行失败时返回空字符串
            // 这确保了方法的健壮性，不会因为无效模式而崩溃
            return new StringOperationToolbox(string.Empty);
        }
    }

    /// <summary>
    /// 按指定起始位置和长度截取字符串（Slice方法的语义化别名）
    /// </summary>
    /// <param name="startPosition">起始位置（从0开始的索引）</param>
    /// <param name="length">要截取的字符数量</param>
    /// <returns>包含截取结果的新工具箱实例</returns>
    /// <remarks>
    /// 此方法是Slice方法的直接委托，提供更明确的语义化命名。
    /// 功能完全相同，只是方法名更清楚地表达了"按长度截取"的意图。
    /// </remarks>
    public IStringOperationToolbox SliceByLength(int startPosition, int length)
    {
        // 直接委托给Slice方法，避免代码重复
        return Slice(startPosition, length);
    }

    #endregion

    #region 字符串查找操作实现

    /// <summary>
    /// 查找指定字符串的第一个匹配项
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <returns>包含查找结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>单次查找的高效实现：</para>
    /// <list type="bullet">
    /// <item>只查找第一个匹配项，性能优于FindAll</item>
    /// <item>使用序数字符串比较，区分大小写</item>
    /// <item>通过StringSearchResult封装结果</item>
    /// </list>
    /// </remarks>
    public IStringSearchResult Find(string searchString)
    {
        // 创建单次查找的结果对象（findAll = false）
        return new StringSearchResult(_value, searchString, findAll: false);
    }

    /// <summary>
    /// 查找指定字符串的所有匹配项
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>全局查找的完整实现：</para>
    /// <list type="bullet">
    /// <item>查找所有不重叠的匹配项</item>
    /// <item>结果按位置顺序排列</item>
    /// <item>适用于统计和批量处理场景</item>
    /// </list>
    /// </remarks>
    public IStringSearchResult FindAll(string searchString)
    {
        // 创建全局查找的结果对象（findAll = true）
        return new StringSearchResult(_value, searchString, findAll: true);
    }

    /// <summary>
    /// 使用正则表达式模式查找所有匹配项
    /// </summary>
    /// <param name="pattern">正则表达式模式字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>正则查找的专门实现：</para>
    /// <list type="bullet">
    /// <item>支持完整的.NET正则表达式语法</item>
    /// <item>自动处理编译错误，失败时Found为false</item>
    /// <item>返回完整匹配内容，不包括捕获组</item>
    /// </list>
    /// <para>参数说明：</para>
    /// <list type="bullet">
    /// <item>第三个参数true表示查找所有匹配</item>
    /// <item>第四个参数true表示使用正则表达式模式</item>
    /// </list>
    /// </remarks>
    public IStringSearchResult FindByPattern(string pattern)
    {
        // 创建正则表达式查找的结果对象
        // findAll = true: 查找所有匹配
        // isRegex = true: 使用正则表达式模式
        return new StringSearchResult(_value, pattern, findAll: true, isRegex: true);
    }

    /// <summary>
    /// 查找位于两个标记字符串之间的所有内容
    /// </summary>
    /// <param name="startString">起始标记字符串</param>
    /// <param name="endString">结束标记字符串</param>
    /// <returns>包含所有匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>区间查找的专门实现：</para>
    /// <list type="bullet">
    /// <item>使用专门的StringBetweenSearchResult类处理复杂逻辑</item>
    /// <item>支持嵌套和重叠的标记对</item>
    /// <item>返回的内容不包含标记字符串本身</item>
    /// </list>
    /// </remarks>
    public IStringSearchResult FindBetween(string startString, string endString)
    {
        // 使用专门的区间查找结果类
        return new StringBetweenSearchResult(_value, startString, endString);
    }

    /// <summary>
    /// 查找指定字符串并包含其前后指定长度的上下文内容
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <param name="beforeContext">目标字符串前面要包含的字符数量</param>
    /// <param name="afterContext">目标字符串后面要包含的字符数量</param>
    /// <returns>包含带上下文匹配结果的IStringSearchResult对象</returns>
    /// <remarks>
    /// <para>上下文查找的专门实现：</para>
    /// <list type="bullet">
    /// <item>使用专门的StringContextSearchResult类处理上下文逻辑</item>
    /// <item>自动处理字符串边界，防止越界</item>
    /// <item>返回的内容包含目标字符串及其上下文</item>
    /// </list>
    /// </remarks>
    public IStringSearchResult FindWithContext(string searchString, int beforeContext, int afterContext)
    {
        // 使用专门的上下文查找结果类
        return new StringContextSearchResult(_value, searchString, beforeContext, afterContext);
    }

    #endregion
}
