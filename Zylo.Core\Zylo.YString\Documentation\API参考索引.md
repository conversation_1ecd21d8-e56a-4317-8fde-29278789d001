# Zylo.YString API 参考索引

## 📋 快速导航

| 分类 | 接口/类 | 描述 | 文档链接 |
|------|---------|------|----------|
| 核心接口 | `IStringOperationToolbox` | 字符串操作工具箱核心接口 | [详细文档](#istringoperationtoolbox) |
| 截取操作 | `IStringSliceOperations` | 字符串截取操作接口 | [详细文档](#istringsliceoperations) |
| 查找操作 | `IStringSearchOperations` | 字符串查找操作接口 | [详细文档](#istringsearchoperations) |
| 查找结果 | `IStringSearchResult` | 字符串查找结果接口 | [详细文档](#istringsearchresult) |
| 主实现类 | `StringOperationToolbox` | 工具箱主实现类 | [详细文档](#stringoperationtoolbox) |
| 扩展方法 | `StringExtensions` | 字符串扩展方法类 | [详细文档](#stringextensions) |

## 🔍 方法快速查找

### 按功能分类

#### 🔪 截取操作
| 方法 | 功能 | 示例 |
|------|------|------|
| `Slice(int start, int length)` | 按位置和长度截取 | `text.Slice(0, 5)` |
| `SliceFrom(string startString)` | 从指定字符串开始截取 | `text.SliceFrom("Hello")` |
| `SliceTo(string endString)` | 截取到指定字符串 | `text.SliceTo("World")` |
| `SliceBetween(string start, string end)` | 截取两个标记之间的内容 | `text.SliceBetween("[", "]")` |
| `SliceByPattern(string pattern)` | 使用正则表达式截取 | `text.SliceByPattern(@"\d+")` |
| `SliceByLength(int start, int length)` | 按长度截取（别名） | `text.SliceByLength(0, 5)` |

#### 🔍 查找操作
| 方法 | 功能 | 示例 |
|------|------|------|
| `Find(string searchString)` | 查找第一个匹配项 | `text.Find("Hello")` |
| `FindAll(string searchString)` | 查找所有匹配项 | `text.FindAll("Hello")` |
| `FindByPattern(string pattern)` | 使用正则表达式查找 | `text.FindByPattern(@"\w+")` |
| `FindBetween(string start, string end)` | 查找两个标记之间的内容 | `text.FindBetween("<", ">")` |
| `FindWithContext(string search, int before, int after)` | 带上下文的查找 | `text.FindWithContext("word", 3, 3)` |

#### 📍 位置操作
| 方法 | 功能 | 示例 |
|------|------|------|
| `GetAllPositions(string search)` | 获取所有匹配位置 | `text.GetAllPositions("Hello")` |
| `CountOccurrences(string search)` | 统计出现次数 | `text.CountOccurrences("Hello")` |
| `GetLeftContent(string marker, int length)` | 获取标记左侧内容 | `text.GetLeftContent("World", 5)` |
| `GetRightContent(string marker, int length)` | 获取标记右侧内容 | `text.GetRightContent("Hello", 5)` |
| `GetSurroundingContent(string marker, int left, int right)` | 获取标记周围内容 | `text.GetSurroundingContent("word", 3, 3)` |
| `GetNthPosition(string search, int occurrence)` | 获取第N次出现的位置 | `text.GetNthPosition("Hello", 2)` |

#### 🔧 工具方法
| 方法 | 功能 | 示例 |
|------|------|------|
| `ToToolbox()` | 转换为工具箱实例 | `"text".ToToolbox()` |
| `SafeSubstring(int start, int length)` | 安全的子字符串截取 | `text.SafeSubstring(0, 5)` |
| `IsValidPosition(int position)` | 检查位置是否有效 | `text.IsValidPosition(5)` |
| `IsPositionAtWordBoundary(int position)` | 检查是否在单词边界 | `text.IsPositionAtWordBoundary(5)` |
| `Apply(Func<string, string> transform)` | 应用转换函数 | `toolbox.Apply(s => s.ToUpper())` |
| `ApplyAsync(Func<string, Task<string>> transform)` | 应用异步转换函数 | `await toolbox.ApplyAsync(ProcessAsync)` |

### 按字母顺序

| 方法名 | 所属类/接口 | 功能描述 |
|--------|-------------|----------|
| `Apply` | `IStringOperationToolbox` | 应用同步转换函数 |
| `ApplyAsync` | `IStringOperationToolbox` | 应用异步转换函数 |
| `CountOccurrences` | `StringExtensions` | 统计字符串出现次数 |
| `Find` | `IStringSearchOperations` | 查找第一个匹配项 |
| `FindAll` | `IStringSearchOperations` | 查找所有匹配项 |
| `FindBetween` | `IStringSearchOperations` | 查找两个标记之间的内容 |
| `FindByPattern` | `IStringSearchOperations` | 使用正则表达式查找 |
| `FindWithContext` | `IStringSearchOperations` | 带上下文的查找 |
| `Found` | `IStringSearchResult` | 是否找到匹配项（属性） |
| `GetAllPositions` | `StringExtensions` | 获取所有匹配位置 |
| `GetLeftContent` | `StringExtensions` | 获取标记左侧内容 |
| `GetNthPosition` | `StringExtensions` | 获取第N次出现的位置 |
| `GetResults` | `IStringSearchResult` | 将查找结果转换为工具箱 |
| `GetRightContent` | `StringExtensions` | 获取标记右侧内容 |
| `GetSurroundingContent` | `StringExtensions` | 获取标记周围内容 |
| `IsPositionAtWordBoundary` | `StringExtensions` | 检查是否在单词边界 |
| `IsValidPosition` | `StringExtensions` | 检查位置是否有效 |
| `Matches` | `IStringSearchResult` | 匹配的内容列表（属性） |
| `Positions` | `IStringSearchResult` | 匹配的位置列表（属性） |
| `SafeSubstring` | `StringExtensions` | 安全的子字符串截取 |
| `Set` | `IStringOperationToolbox` | 设置新的字符串值 |
| `Slice` | `IStringSliceOperations` | 按位置和长度截取 |
| `SliceBetween` | `IStringSliceOperations` | 截取两个标记之间的内容 |
| `SliceByLength` | `IStringSliceOperations` | 按长度截取（别名） |
| `SliceByPattern` | `IStringSliceOperations` | 使用正则表达式截取 |
| `SliceFrom` | `IStringSliceOperations` | 从指定字符串开始截取 |
| `SliceTo` | `IStringSliceOperations` | 截取到指定字符串 |
| `ToToolbox` | `StringExtensions` | 转换为工具箱实例 |
| `ToString` | `IStringOperationToolbox` | 获取字符串表示形式 |
| `Value` | `IStringOperationToolbox` | 当前字符串值（属性） |

## 🎯 使用场景索引

### 文本处理
- **提取内容**: `SliceBetween`, `FindBetween`
- **格式化**: `Apply`, `SafeSubstring`
- **清理**: `Apply` + 自定义函数

### 数据分析
- **统计**: `CountOccurrences`, `GetAllPositions`
- **查找**: `Find`, `FindAll`, `FindByPattern`
- **位置**: `GetNthPosition`, `IsValidPosition`

### 配置解析
- **键值对**: `SliceTo`, `SliceFrom`
- **节提取**: `SliceBetween`, `FindBetween`
- **验证**: `IsValidPosition`, `Found`

### 日志分析
- **时间戳**: `SliceTo`, `Slice`
- **级别**: `SliceBetween`
- **消息**: `SliceFrom`

### HTML/XML处理
- **标签内容**: `FindBetween`, `SliceBetween`
- **属性值**: `SliceByPattern`
- **清理**: `Apply` + 正则表达式

## 📚 相关文档

- [完整API文档](API文档.md) - 详细的API说明和示例
- [使用指南](使用指南.md) - 从入门到高级的完整教程
- [代码优化总结](代码优化总结.md) - 代码质量和性能优化
- [项目README](../README.md) - 项目概述和快速开始
- [演示程序](../Demo/Program.cs) - 实际使用示例
- [测试报告](../Demo/测试报告.md) - 功能测试结果

## 🔗 外部资源

- [.NET字符串文档](https://docs.microsoft.com/en-us/dotnet/api/system.string)
- [正则表达式参考](https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference)
- [C#编码规范](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/inside-a-program/coding-conventions)

---

**最后更新**: 2025年1月  
**版本**: 1.0.0
