# Zylo.YString API 文档

![.NET Version](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)
![License](https://img.shields.io/badge/License-MIT-green)
![Version](https://img.shields.io/badge/Version-1.0.0-orange)

## 📋 概述

**Zylo.StringToolbox** 是一个超强字符串处理工具箱，提供200+字符串操作方法，支持截取、查找、替换、统计、位置操作等超灵活功能。

### 🎯 核心特性

- **流畅API设计** - 支持链式调用，提供直观的编程体验
- **不可变操作** - 所有操作返回新实例，确保线程安全
- **高性能实现** - 使用.NET最新特性，优化的算法实现
- **完整错误处理** - 安全的边界检查，不会因无效参数抛出异常
- **丰富扩展方法** - 提供便捷的字符串扩展功能

## 🚀 快速开始

### 安装

```bash
# 通过NuGet安装（如果已发布）
Install-Package Zylo.StringToolbox

# 或者直接引用项目
<ProjectReference Include="path/to/Zylo.StringToolbox.csproj" />
```

### 基础使用

```csharp
using Zylo.StringToolbox.Core;
using Zylo.StringToolbox.Extensions;

// 创建工具箱实例
var toolbox = StringOperationToolbox.From("Hello, World!");

// 或者使用扩展方法
var toolbox2 = "Hello, World!".ToToolbox();

// 链式操作
var result = toolbox
    .SliceTo("World")           // "Hello, "
    .Apply(s => s.Trim())       // "Hello,"
    .Apply(s => s.TrimEnd(','))  // "Hello"
    .ToString();
```

## 📚 核心API

### IStringOperationToolbox 接口

字符串操作工具箱的核心接口，提供基础的字符串操作功能。

#### 属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `Value` | `string` | 获取当前字符串值，永远不为null |

#### 方法

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| `Set(string value)` | `IStringOperationToolbox` | 设置新的字符串值 |
| `Apply(Func<string, string> transform)` | `IStringOperationToolbox` | 应用同步转换函数 |
| `ApplyAsync(Func<string, Task<string>> transform)` | `Task<IStringOperationToolbox>` | 应用异步转换函数 |
| `ToString()` | `string` | 获取字符串表示形式 |

#### 使用示例

```csharp
var toolbox = StringOperationToolbox.From("Hello World");

// 设置新值
var newToolbox = toolbox.Set("New Value");

// 应用转换
var upperCase = toolbox.Apply(s => s.ToUpper());

// 异步转换
var asyncResult = await toolbox.ApplyAsync(async s => 
{
    await Task.Delay(100);
    return s.ToLower();
});
```

### IStringSliceOperations 接口

提供多种字符串截取方式，支持位置截取、模式截取、条件截取等。

#### 方法

| 方法 | 参数 | 返回类型 | 描述 |
|------|------|----------|------|
| `Slice(int start, int length)` | start: 起始位置<br>length: 长度 | `IStringOperationToolbox` | 按位置和长度截取 |
| `SliceFrom(string startString)` | startString: 起始标记 | `IStringOperationToolbox` | 从指定字符串开始截取 |
| `SliceTo(string endString)` | endString: 结束标记 | `IStringOperationToolbox` | 截取到指定字符串 |
| `SliceBetween(string startString, string endString)` | startString: 起始标记<br>endString: 结束标记 | `IStringOperationToolbox` | 截取两个标记之间的内容 |
| `SliceByPattern(string pattern)` | pattern: 正则表达式 | `IStringOperationToolbox` | 使用正则表达式截取 |
| `SliceByLength(int startPosition, int length)` | startPosition: 起始位置<br>length: 长度 | `IStringOperationToolbox` | 按长度截取（Slice的别名） |

#### 使用示例

```csharp
var text = "Hello, World! This is a test.";
var toolbox = StringOperationToolbox.From(text);

// 基础截取
var slice1 = toolbox.Slice(0, 5);                    // "Hello"
var slice2 = toolbox.SliceFrom("World");             // "World! This is a test."
var slice3 = toolbox.SliceTo("test");                // "Hello, World! This is a "
var slice4 = toolbox.SliceBetween("Hello, ", "!");   // "World"
var slice5 = toolbox.SliceByPattern(@"\b\w{4}\b");   // "This"
```

### IStringSearchOperations 接口

提供多种字符串查找方式，支持精确查找、模式匹配、上下文查找等。

#### 方法

| 方法 | 参数 | 返回类型 | 描述 |
|------|------|----------|------|
| `Find(string searchString)` | searchString: 查找目标 | `IStringSearchResult` | 查找第一个匹配项 |
| `FindAll(string searchString)` | searchString: 查找目标 | `IStringSearchResult` | 查找所有匹配项 |
| `FindByPattern(string pattern)` | pattern: 正则表达式 | `IStringSearchResult` | 使用正则表达式查找 |
| `FindBetween(string startString, string endString)` | startString: 起始标记<br>endString: 结束标记 | `IStringSearchResult` | 查找两个标记之间的内容 |
| `FindWithContext(string searchString, int beforeContext, int afterContext)` | searchString: 查找目标<br>beforeContext: 前置上下文长度<br>afterContext: 后置上下文长度 | `IStringSearchResult` | 带上下文的查找 |

#### 使用示例

```csharp
var text = "Hello World! Hello Universe! Hello Galaxy!";
var toolbox = StringOperationToolbox.From(text);

// 查找操作
var search1 = toolbox.Find("Hello");                    // 第一个匹配
var search2 = toolbox.FindAll("Hello");                 // 所有匹配
var search3 = toolbox.FindByPattern(@"\b\w{5}\b");      // 正则查找
var search4 = toolbox.FindBetween("Hello ", "!");       // 区间查找
var search5 = toolbox.FindWithContext("World", 3, 3);   // 上下文查找
```

### IStringSearchResult 接口

封装字符串查找操作的结果，提供位置、内容、统计等信息。

#### 属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `Found` | `bool` | 是否找到任何匹配项 |
| `Positions` | `IReadOnlyList<int>` | 所有匹配项的位置列表 |
| `Matches` | `IReadOnlyList<string>` | 所有匹配的字符串内容列表 |

#### 方法

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| `GetResults()` | `IStringOperationToolbox` | 将查找结果转换为工具箱实例 |

#### 使用示例

```csharp
var result = toolbox.FindAll("Hello");

if (result.Found)
{
    Console.WriteLine($"找到 {result.Positions.Count} 个匹配");
    
    for (int i = 0; i < result.Positions.Count; i++)
    {
        Console.WriteLine($"位置 {result.Positions[i]}: {result.Matches[i]}");
    }
    
    // 继续处理结果
    var processed = result.GetResults()
        .Apply(s => s.ToUpper())
        .ToString();
}
```

## 🔧 扩展方法

### StringExtensions 类

提供丰富的字符串扩展方法，增强.NET内置字符串功能。

#### 工具箱转换

| 方法 | 返回类型 | 描述 |
|------|----------|------|
| `ToToolbox()` | `StringOperationToolbox` | 转换为工具箱实例 |

#### 安全字符串操作

| 方法 | 参数 | 返回类型 | 描述 |
|------|------|----------|------|
| `SafeSubstring(int start, int length)` | start: 起始位置<br>length: 长度 | `string` | 安全的子字符串截取 |
| `SafeSubstring(int start)` | start: 起始位置 | `string` | 安全的子字符串截取（到末尾） |

#### 位置操作

| 方法 | 参数 | 返回类型 | 描述 |
|------|------|----------|------|
| `GetAllPositions(string searchString, bool ignoreCase = false)` | searchString: 查找目标<br>ignoreCase: 是否忽略大小写 | `List<int>` | 获取所有匹配位置 |
| `CountOccurrences(string searchString, bool ignoreCase = false)` | searchString: 查找目标<br>ignoreCase: 是否忽略大小写 | `int` | 统计出现次数 |
| `GetLeftContent(string marker, int length)` | marker: 标记字符串<br>length: 长度 | `string` | 获取标记左侧内容 |
| `GetRightContent(string marker, int length)` | marker: 标记字符串<br>length: 长度 | `string` | 获取标记右侧内容 |
| `GetSurroundingContent(string marker, int leftLength, int rightLength)` | marker: 标记字符串<br>leftLength: 左侧长度<br>rightLength: 右侧长度 | `string` | 获取标记周围内容 |
| `GetNthPosition(string searchString, int occurrence, bool ignoreCase = false)` | searchString: 查找目标<br>occurrence: 第几次出现<br>ignoreCase: 是否忽略大小写 | `int` | 获取第N次出现的位置 |

#### 验证功能

| 方法 | 参数 | 返回类型 | 描述 |
|------|------|----------|------|
| `IsValidPosition(int position)` | position: 位置 | `bool` | 检查位置是否有效 |
| `IsPositionAtWordBoundary(int position)` | position: 位置 | `bool` | 检查位置是否在单词边界 |

#### 使用示例

```csharp
var text = "Hello World! Hello Universe!";

// 工具箱转换
var toolbox = text.ToToolbox();

// 安全操作
var safe = text.SafeSubstring(6, 5);        // "World"

// 位置操作
var positions = text.GetAllPositions("Hello");     // [0, 13]
var count = text.CountOccurrences("Hello");        // 2
var left = text.GetLeftContent("World", 6);        // "Hello "
var right = text.GetRightContent("World", 1);      // "!"

// 验证功能
var isValid = text.IsValidPosition(5);             // true
var isBoundary = text.IsPositionAtWordBoundary(5); // true
```

## 🎯 高级用法

### 链式操作

```csharp
var result = "Hello, World! This is a test."
    .ToToolbox()
    .SliceTo("test")
    .Apply(s => s.ToUpper())
    .Apply(s => s.Replace(" ", "_"))
    .Apply(s => s.Trim('_'))
    .ToString();
// 结果: "HELLO,_WORLD!_THIS_IS_A"
```

### 异步操作

```csharp
var result = await "Hello World"
    .ToToolbox()
    .ApplyAsync(async s => 
    {
        // 模拟异步操作
        await Task.Delay(100);
        return await TranslateAsync(s);
    });
```

### 复杂查找

```csharp
var html = "<div>Hello</div><div>World</div>";
var content = StringOperationToolbox.From(html)
    .FindBetween("<div>", "</div>")
    .GetResults()
    .Apply(s => string.Join(", ", s.Split('\n')))
    .ToString();
// 结果: "Hello, World"
```

## 📖 更多资源

- [项目计划](../Zylo.StringToolbox项目计划.md)
- [代码优化总结](代码优化总结.md)
- [演示程序](../Demo/Program.cs)
- [测试报告](../Demo/测试报告.md)

---

**版本**: 1.0.0  
**最后更新**: 2025年1月  
**许可证**: MIT
