# Zylo.Ystring 使用指南

## 📋 目录

1. [快速入门](#快速入门)
2. [基础概念](#基础概念)
3. [字符串截取](#字符串截取)
4. [字符串查找](#字符串查找)
5. [位置操作](#位置操作)
6. [扩展方法](#扩展方法)
7. [高级技巧](#高级技巧)
8. [最佳实践](#最佳实践)
9. [常见问题](#常见问题)

## 🚀 快速入门

### 安装和引用

```csharp
using Zylo.StringToolbox.Core;
using Zylo.StringToolbox.Extensions;
```

### 第一个示例

```csharp
// 方式1：使用静态工厂方法
var toolbox = StringOperationToolbox.From("Hello, World!");

// 方式2：使用扩展方法（推荐）
var toolbox2 = "Hello, World!".ToToolbox();

// 简单操作
var result = toolbox.Slice(0, 5).ToString(); // "Hello"
```

## 🎯 基础概念

### 不可变性

所有操作都返回新的实例，原始实例不会被修改：

```csharp
var original = "Hello World".ToToolbox();
var modified = original.Apply(s => s.ToUpper());

Console.WriteLine(original.Value);  // "Hello World" (未改变)
Console.WriteLine(modified.Value);  // "HELLO WORLD" (新实例)
```

### 链式调用

支持流畅的链式操作：

```csharp
var result = "  Hello, World!  "
    .ToToolbox()
    .Apply(s => s.Trim())           // 去除首尾空格
    .SliceTo("World")               // 截取到"World"
    .Apply(s => s.ToUpper())        // 转换为大写
    .ToString();                    // "HELLO, "
```

### 安全性

所有操作都包含边界检查，不会抛出异常：

```csharp
var text = "Hello".ToToolbox();
var safe = text.Slice(10, 5);      // 返回空字符串，不抛出异常
var safe2 = text.SliceFrom("xyz"); // 返回空字符串，不抛出异常
```

## ✂️ 字符串截取

### 基础截取

```csharp
var text = "Hello, World! This is a test.";

// 按位置和长度截取
var slice1 = text.ToToolbox().Slice(0, 5);        // "Hello"
var slice2 = text.ToToolbox().Slice(7, 5);        // "World"

// 安全截取（不会越界）
var slice3 = text.ToToolbox().Slice(0, 100);      // 自动调整长度
var slice4 = text.ToToolbox().Slice(-1, 5);       // 返回空字符串
```

### 标记截取

```csharp
var text = "Hello, World! This is a test.";

// 从指定字符串开始截取
var from = text.ToToolbox().SliceFrom("World");    // "World! This is a test."

// 截取到指定字符串
var to = text.ToToolbox().SliceTo("test");         // "Hello, World! This is a "

// 截取两个标记之间的内容
var between = text.ToToolbox().SliceBetween("Hello, ", "!");  // "World"
```

### 正则截取

```csharp
var text = "Email: <EMAIL> Phone: ************";

// 截取邮箱地址
var email = text.ToToolbox()
    .SliceByPattern(@"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b");
// "<EMAIL>"

// 截取电话号码
var phone = text.ToToolbox()
    .SliceByPattern(@"\d{3}-\d{3}-\d{4}");
// "************"
```

## 🔍 字符串查找

### 基础查找

```csharp
var text = "Hello World! Hello Universe! Hello Galaxy!";

// 查找第一个匹配
var first = text.ToToolbox().Find("Hello");
Console.WriteLine($"找到: {first.Found}");           // True
Console.WriteLine($"位置: {first.Positions[0]}");    // 0

// 查找所有匹配
var all = text.ToToolbox().FindAll("Hello");
Console.WriteLine($"找到 {all.Positions.Count} 个"); // 3
Console.WriteLine($"位置: [{string.Join(", ", all.Positions)}]"); // [0, 13, 29]
```

### 正则查找

```csharp
var text = "Hello World! Hello Universe! Hello Galaxy!";

// 查找所有5字母单词
var words = text.ToToolbox().FindByPattern(@"\b\w{5}\b");
Console.WriteLine($"匹配: [{string.Join(", ", words.Matches)}]");
// [Hello, World, Hello, Hello]
```

### 区间查找

```csharp
var html = "<div>Hello</div><div>World</div><div>Test</div>";

// 查找所有div标签内容
var content = html.ToToolbox().FindBetween("<div>", "</div>");
Console.WriteLine($"内容: [{string.Join(", ", content.Matches)}]");
// [Hello, World, Test]
```

### 上下文查找

```csharp
var text = "The quick brown fox jumps over the lazy dog";

// 查找"fox"及其前后5个字符
var context = text.ToToolbox().FindWithContext("fox", 5, 5);
Console.WriteLine($"上下文: {context.Matches[0]}");
// "brown fox jumps"
```

## 📍 位置操作

### 位置查找

```csharp
var text = "Hello World! Hello Universe! Hello Galaxy!";

// 获取所有位置
var positions = text.GetAllPositions("Hello");
Console.WriteLine($"位置: [{string.Join(", ", positions)}]"); // [0, 13, 29]

// 统计出现次数
var count = text.CountOccurrences("Hello");
Console.WriteLine($"出现次数: {count}"); // 3

// 获取第N次出现的位置
var second = text.GetNthPosition("Hello", 2);
Console.WriteLine($"第2次出现位置: {second}"); // 13
```

### 内容提取

```csharp
var text = "Hello World! This is a test.";

// 获取"World"左侧6个字符
var left = text.GetLeftContent("World", 6);
Console.WriteLine($"左侧内容: '{left}'"); // "Hello "

// 获取"World"右侧1个字符
var right = text.GetRightContent("World", 1);
Console.WriteLine($"右侧内容: '{right}'"); // "!"

// 获取"World"周围内容
var surrounding = text.GetSurroundingContent("World", 6, 1);
Console.WriteLine($"周围内容: '{surrounding}'"); // "Hello World!"
```

### 位置验证

```csharp
var text = "Hello World";

// 检查位置是否有效
var valid = text.IsValidPosition(5);
Console.WriteLine($"位置5有效: {valid}"); // True

var invalid = text.IsValidPosition(20);
Console.WriteLine($"位置20有效: {invalid}"); // False

// 检查是否在单词边界
var boundary = text.IsPositionAtWordBoundary(5);
Console.WriteLine($"位置5在单词边界: {boundary}"); // True
```

## 🔧 扩展方法

### 安全操作

```csharp
var text = "Hello World";

// 安全的子字符串操作
var safe1 = text.SafeSubstring(6, 5);    // "World"
var safe2 = text.SafeSubstring(6, 100);  // "World" (自动调整)
var safe3 = text.SafeSubstring(20, 5);   // "" (返回空字符串)
var safe4 = text.SafeSubstring(6);       // "World" (到末尾)
```

### 工具箱转换

```csharp
// 直接转换为工具箱
var toolbox = "Hello World".ToToolbox();

// 继续链式操作
var result = "Hello World"
    .ToToolbox()
    .Apply(s => s.ToUpper())
    .SliceTo("WORLD")
    .ToString(); // "HELLO "
```

## 🎯 高级技巧

### 复杂文本处理

```csharp
var log = "2025-01-01 10:30:15 [ERROR] Database connection failed";

// 提取日期、时间、级别和消息
var date = log.ToToolbox().SliceByPattern(@"\d{4}-\d{2}-\d{2}");
var time = log.ToToolbox().SliceByPattern(@"\d{2}:\d{2}:\d{2}");
var level = log.ToToolbox().SliceBetween("[", "]");
var message = log.ToToolbox().SliceFrom("] ").Apply(s => s.Substring(2));

Console.WriteLine($"日期: {date}");     // 2025-01-01
Console.WriteLine($"时间: {time}");     // 10:30:15
Console.WriteLine($"级别: {level}");    // ERROR
Console.WriteLine($"消息: {message}");  // Database connection failed
```

### 数据清洗

```csharp
var dirtyData = "  Hello,   World!  \n\t  This   is    messy.  ";

var cleaned = dirtyData
    .ToToolbox()
    .Apply(s => s.Trim())                           // 去除首尾空白
    .Apply(s => System.Text.RegularExpressions.Regex.Replace(s, @"\s+", " ")) // 合并多个空格
    .Apply(s => s.Replace("\n", "").Replace("\t", "")) // 去除换行和制表符
    .ToString();

Console.WriteLine($"清洗后: '{cleaned}'"); // "Hello, World! This is messy."
```

### 批量处理

```csharp
var texts = new[] { "Hello World", "Goodbye Moon", "Welcome Home" };

var results = texts
    .Select(text => text.ToToolbox()
        .Apply(s => s.ToUpper())
        .Apply(s => s.Replace(" ", "_"))
        .ToString())
    .ToArray();

// 结果: ["HELLO_WORLD", "GOODBYE_MOON", "WELCOME_HOME"]
```

### 异步处理

```csharp
async Task<string> ProcessTextAsync(string input)
{
    return await input.ToToolbox()
        .ApplyAsync(async s =>
        {
            // 模拟异步操作（如网络请求、数据库查询等）
            await Task.Delay(100);
            return s.ToUpper();
        })
        .ContinueWith(t => t.Result.ToString());
}
```

## 💡 最佳实践

### 1. 优先使用扩展方法

```csharp
// 推荐
var result = "Hello World".ToToolbox().Slice(0, 5);

// 不推荐
var result = StringOperationToolbox.From("Hello World").Slice(0, 5);
```

### 2. 合理使用链式调用

```csharp
// 好的链式调用 - 逻辑清晰
var result = text
    .ToToolbox()
    .Apply(s => s.Trim())
    .SliceTo("important")
    .Apply(s => s.ToUpper())
    .ToString();

// 避免过长的链 - 考虑分步骤
var step1 = text.ToToolbox().Apply(s => s.Trim());
var step2 = step1.SliceTo("important");
var result = step2.Apply(s => s.ToUpper()).ToString();
```

### 3. 错误处理

```csharp
// 利用工具箱的安全特性
var result = userInput.ToToolbox()
    .Apply(s => s?.Trim() ?? "")  // 处理null
    .SliceTo("@")                 // 安全截取
    .ToString();

// 检查查找结果
var searchResult = text.ToToolbox().Find("pattern");
if (searchResult.Found)
{
    // 处理找到的结果
    ProcessResults(searchResult.Matches);
}
```

### 4. 性能考虑

```csharp
// 对于大量重复操作，考虑缓存工具箱实例
var toolbox = largeText.ToToolbox();
var results = patterns.Select(p => toolbox.SliceByPattern(p)).ToList();

// 对于简单操作，直接使用.NET内置方法可能更快
var simple = text.Substring(0, 5); // 简单截取
var complex = text.ToToolbox().SliceBetween("[", "]"); // 复杂截取
```

## ❓ 常见问题

### Q: 为什么操作不会修改原始字符串？

A: Zylo.StringToolbox 采用不可变设计，所有操作都返回新实例，确保线程安全和函数式编程特性。

### Q: 如何处理null值？

A: 工具箱会自动将null值转换为空字符串，所有操作都是null安全的。

### Q: 正则表达式失败会怎样？

A: 正则表达式编译或匹配失败时，会返回空结果而不是抛出异常。

### Q: 性能如何？

A: 工具箱使用了.NET最新特性进行优化，对于复杂字符串操作性能优秀，但简单操作建议直接使用.NET内置方法。

### Q: 支持哪些.NET版本？

A: 支持.NET 6.0和.NET 8.0，利用了最新的语言特性和性能优化。

---

**更多信息请参考**: [API文档](API文档.md) | [项目计划](../Zylo.StringToolbox项目计划.md)
