using System;
using System.Collections.Generic;
using System.Linq;
using Zylo.YString.Core;

namespace Zylo.YString.Extensions;

/// <summary>
/// 字符串扩展方法集合
/// <para>提供丰富的字符串操作扩展方法，增强.NET内置字符串功能</para>
/// </summary>
/// <remarks>
/// <para>扩展方法特点：</para>
/// <list type="bullet">
/// <item><strong>安全性</strong>：所有方法都包含完整的边界检查和null值处理</item>
/// <item><strong>性能优化</strong>：使用.NET 6.0+的最新特性，如范围语法</item>
/// <item><strong>易用性</strong>：提供直观的方法名和参数，降低学习成本</item>
/// <item><strong>一致性</strong>：与StringOperationToolbox的API设计保持一致</item>
/// </list>
/// <para>主要功能分类：</para>
/// <list type="bullet">
/// <item>工具箱转换：ToToolbox()</item>
/// <item>安全字符串操作：SafeSubstring()</item>
/// <item>位置查找：GetAllPositions(), GetFirstPosition(), GetLastPosition()</item>
/// <item>内容提取：GetLeftContent(), GetRightContent(), GetSurroundingContent()</item>
/// <item>统计功能：CountOccurrences()</item>
/// <item>验证功能：IsValidPosition(), IsPositionAtWordBoundary()</item>
/// </list>
/// </remarks>
/// <example>
/// <para>基础使用示例：</para>
/// <code>
/// string text = "Hello World! Hello Universe!";
///
/// // 转换为工具箱
/// var toolbox = text.ToToolbox();
///
/// // 统计出现次数
/// int count = text.CountOccurrences("Hello"); // 2
///
/// // 获取所有位置
/// var positions = text.GetAllPositions("Hello"); // [0, 13]
///
/// // 获取左侧内容
/// string left = text.GetLeftContent("World", 6); // "Hello "
/// </code>
/// </example>
public static class StringExtensions
{
    #region 工具箱转换扩展

    /// <summary>
    /// 将字符串转换为StringOperationToolbox实例以启用链式操作
    /// </summary>
    /// <param name="value">要转换的字符串值，null会被转换为string.Empty</param>
    /// <returns>包含该字符串的新工具箱实例</returns>
    /// <remarks>
    /// <para>这是进入StringToolbox生态系统的主要入口点。</para>
    /// <para>转换特性：</para>
    /// <list type="bullet">
    /// <item>null安全：null值自动转换为空字符串</item>
    /// <item>零开销：直接委托给工厂方法</item>
    /// <item>链式友好：返回的工具箱支持所有链式操作</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = "Hello World"
    ///     .ToToolbox()
    ///     .SliceTo("World")
    ///     .Apply(s => s.ToUpper())
    ///     .ToString(); // "HELLO "
    /// </code>
    /// </example>
    public static StringOperationToolbox ToToolbox(this string value)
    {
        return StringOperationToolbox.From(value);
    }

    #endregion

    #region 安全字符串操作扩展

    /// <summary>
    /// 安全的子字符串截取操作，包含完整的边界检查
    /// </summary>
    /// <param name="value">源字符串</param>
    /// <param name="start">起始位置（从0开始的索引）</param>
    /// <param name="length">要截取的字符数量</param>
    /// <returns>截取的子字符串，无效参数时返回空字符串</returns>
    /// <remarks>
    /// <para>相比于.NET内置的Substring方法，此方法提供了完整的安全保护：</para>
    /// <list type="bullet">
    /// <item>null或空字符串：返回空字符串</item>
    /// <item>起始位置无效：返回空字符串</item>
    /// <item>长度无效：返回空字符串</item>
    /// <item>超出边界：自动调整到可用范围</item>
    /// </list>
    /// <para>性能优化：使用.NET 6.0+的Substring方法</para>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello World".SafeSubstring(0, 5);   // "Hello"
    /// "Hello World".SafeSubstring(6, 100); // "World" (自动调整长度)
    /// "Hello World".SafeSubstring(-1, 5);  // "" (无效起始位置)
    /// "Hello World".SafeSubstring(20, 5);  // "" (起始位置超出范围)
    /// </code>
    /// </example>
    public static string SafeSubstring(this string value, int start, int length)
    {
        // 参数验证：检查所有可能的无效情况
        if (string.IsNullOrEmpty(value) || start < 0 || start >= value.Length || length <= 0)
            return string.Empty;

        // 计算实际可截取的长度，防止越界
        var actualLength = Math.Min(length, value.Length - start);

        // 执行安全截取
        return value.Substring(start, actualLength);
    }

    /// <summary>
    /// 安全的子字符串截取操作（从指定位置到字符串结尾）
    /// </summary>
    /// <param name="value">源字符串</param>
    /// <param name="start">起始位置（从0开始的索引）</param>
    /// <returns>从起始位置到结尾的子字符串，无效参数时返回空字符串</returns>
    /// <remarks>
    /// <para>这是SafeSubstring的重载版本，用于截取到字符串末尾。</para>
    /// <para>安全特性：</para>
    /// <list type="bullet">
    /// <item>完整的边界检查</item>
    /// <item>使用.NET 6.0+的范围语法优化性能</item>
    /// <item>null安全处理</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// "Hello World".SafeSubstring(6);  // "World"
    /// "Hello World".SafeSubstring(0);  // "Hello World"
    /// "Hello World".SafeSubstring(-1); // ""
    /// "Hello World".SafeSubstring(20); // ""
    /// </code>
    /// </example>
    public static string SafeSubstring(this string value, int start)
    {
        // 参数验证：检查字符串和起始位置的有效性
        if (string.IsNullOrEmpty(value) || start < 0 || start >= value.Length)
            return string.Empty;

        // 使用.NET 6.0+的范围语法进行高效截取
        return value[start..];
    }

    #endregion

    #region 字符串位置操作扩展

    /// <summary>
    /// 获取字符串的所有位置
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="searchString">要查找的字符串</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>位置列表</returns>
    public static IList<int> GetAllPositions(this string value, string searchString, bool ignoreCase = false)
    {
        var positions = new List<int>();

        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(searchString))
            return positions;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        var startIndex = 0;

        while (true)
        {
            var index = value.IndexOf(searchString, startIndex, comparison);
            if (index == -1)
                break;

            positions.Add(index);
            startIndex = index + 1;
        }

        return positions;
    }

    /// <summary>
    /// 统计字符串出现次数
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="searchString">要统计的字符串</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>出现次数</returns>
    public static int CountOccurrences(this string value, string searchString, bool ignoreCase = false)
    {
        return value.GetAllPositions(searchString, ignoreCase).Count;
    }

    /// <summary>
    /// 获取指定字符串左侧的内容
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="marker">标记字符串</param>
    /// <param name="length">获取长度</param>
    /// <returns>左侧内容</returns>
    public static string GetLeftContent(this string value, string marker, int length)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(marker) || length <= 0)
            return string.Empty;

        var index = value.IndexOf(marker, StringComparison.Ordinal);
        if (index == -1)
            return string.Empty;

        var start = Math.Max(0, index - length);
        return value[start..index];
    }

    /// <summary>
    /// 获取指定字符串右侧的内容
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="marker">标记字符串</param>
    /// <param name="length">获取长度</param>
    /// <returns>右侧内容</returns>
    public static string GetRightContent(this string value, string marker, int length)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(marker) || length <= 0)
            return string.Empty;

        var index = value.IndexOf(marker, StringComparison.Ordinal);
        if (index == -1)
            return string.Empty;

        var start = index + marker.Length;
        if (start >= value.Length)
            return string.Empty;

        var actualLength = Math.Min(length, value.Length - start);
        return value.Substring(start, actualLength);
    }

    /// <summary>
    /// 获取指定字符串周围的内容
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="marker">标记字符串</param>
    /// <param name="leftLength">左侧长度</param>
    /// <param name="rightLength">右侧长度</param>
    /// <returns>周围内容</returns>
    public static string GetSurroundingContent(this string value, string marker, int leftLength, int rightLength)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(marker))
            return string.Empty;

        var index = value.IndexOf(marker, StringComparison.Ordinal);
        if (index == -1)
            return string.Empty;

        var start = Math.Max(0, index - leftLength);
        var end = Math.Min(value.Length, index + marker.Length + rightLength);

        return value[start..end];
    }

    /// <summary>
    /// 获取第一次出现的位置
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="searchString">要查找的字符串</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>位置，未找到返回-1</returns>
    public static int GetFirstPosition(this string value, string searchString, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(searchString))
            return -1;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        return value.IndexOf(searchString, comparison);
    }

    /// <summary>
    /// 获取最后一次出现的位置
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="searchString">要查找的字符串</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>位置，未找到返回-1</returns>
    public static int GetLastPosition(this string value, string searchString, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(value) || string.IsNullOrEmpty(searchString))
            return -1;

        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        return value.LastIndexOf(searchString, comparison);
    }

    /// <summary>
    /// 获取第N次出现的位置
    /// </summary>
    /// <param name="value">原始字符串</param>
    /// <param name="searchString">要查找的字符串</param>
    /// <param name="occurrence">第几次出现（从1开始）</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>位置，未找到返回-1</returns>
    public static int GetNthPosition(this string value, string searchString, int occurrence, bool ignoreCase = false)
    {
        if (occurrence <= 0)
            return -1;

        var positions = value.GetAllPositions(searchString, ignoreCase);
        return occurrence <= positions.Count ? positions[occurrence - 1] : -1;
    }

    #endregion

    #region 字符串验证扩展

    /// <summary>
    /// 检查是否为有效位置
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="position">位置</param>
    /// <returns>是否有效</returns>
    public static bool IsValidPosition(this string value, int position)
    {
        return !string.IsNullOrEmpty(value) && position >= 0 && position < value.Length;
    }

    /// <summary>
    /// 检查位置是否在单词边界
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="position">位置</param>
    /// <returns>是否在单词边界</returns>
    public static bool IsPositionAtWordBoundary(this string value, int position)
    {
        if (!value.IsValidPosition(position))
            return false;

        var isCurrentCharWordChar = char.IsLetterOrDigit(value[position]);
        var isPrevCharWordChar = position > 0 && char.IsLetterOrDigit(value[position - 1]);
        var isNextCharWordChar = position < value.Length - 1 && char.IsLetterOrDigit(value[position + 1]);

        return (!isCurrentCharWordChar && (isPrevCharWordChar || isNextCharWordChar)) ||
               (isCurrentCharWordChar && (!isPrevCharWordChar || !isNextCharWordChar));
    }

    #endregion
}
