using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Zylo.YString.Core;

namespace Zylo.YString.Operations;

/// <summary>
/// 字符串查找结果的标准实现类
/// <para>支持普通字符串查找和正则表达式查找两种模式</para>
/// </summary>
/// <remarks>
/// <para>设计特点：</para>
/// <list type="bullet">
/// <item><strong>延迟计算</strong>：在构造时立即执行查找操作</item>
/// <item><strong>不可变性</strong>：查找结果一旦创建就不可修改</item>
/// <item><strong>线程安全</strong>：所有公共成员都是只读的</item>
/// <item><strong>内存优化</strong>：使用List而非数组以节省内存</item>
/// </list>
/// <para>支持的查找模式：</para>
/// <list type="bullet">
/// <item>普通字符串查找（区分大小写）</item>
/// <item>正则表达式模式匹配</item>
/// <item>单次查找和全局查找</item>
/// </list>
/// </remarks>
/// <example>
/// <code>
/// // 普通字符串查找
/// var result1 = new StringSearchResult("Hello World Hello", "Hello", true);
/// Console.WriteLine($"找到 {result1.Positions.Count} 个匹配");
///
/// // 正则表达式查找
/// var result2 = new StringSearchResult("test123abc456", @"\d+", true, true);
/// Console.WriteLine($"数字: {string.Join(", ", result2.Matches)}");
/// </code>
/// </example>
public class StringSearchResult : IStringSearchResult
{
    #region 私有字段

    /// <summary>
    /// 原始字符串的只读副本
    /// </summary>
    /// <remarks>
    /// 存储原始字符串以支持GetResults()方法，
    /// 使用只读字段确保不可变性
    /// </remarks>
    private readonly string _originalString;

    /// <summary>
    /// 匹配位置的内部列表
    /// </summary>
    /// <remarks>
    /// 使用List&lt;int&gt;而非数组以支持动态添加，
    /// 通过只读属性暴露为IReadOnlyList确保外部不可修改
    /// </remarks>
    private readonly List<int> _positions;

    /// <summary>
    /// 匹配内容的内部列表
    /// </summary>
    /// <remarks>
    /// 与_positions一一对应，存储每个匹配位置的实际内容，
    /// 对于普通字符串查找，所有匹配内容相同
    /// </remarks>
    private readonly List<string> _matches;

    #endregion

    #region 构造函数

    /// <summary>
    /// 创建字符串查找结果实例并立即执行查找操作
    /// </summary>
    /// <param name="originalString">要在其中查找的原始字符串</param>
    /// <param name="searchString">要查找的目标字符串或正则表达式模式</param>
    /// <param name="findAll">是否查找所有匹配项（false表示只查找第一个）</param>
    /// <param name="isRegex">是否将searchString视为正则表达式模式</param>
    /// <remarks>
    /// <para>构造函数执行流程：</para>
    /// <list type="number">
    /// <item>初始化内部字段和集合</item>
    /// <item>验证输入参数的有效性</item>
    /// <item>根据isRegex参数选择查找策略</item>
    /// <item>执行实际的查找操作</item>
    /// <item>填充位置和匹配内容列表</item>
    /// </list>
    /// <para>性能考虑：</para>
    /// <list type="bullet">
    /// <item>查找操作在构造时完成，后续访问为O(1)</item>
    /// <item>对于大文本和复杂正则表达式，构造可能较慢</item>
    /// <item>结果对象可以缓存重复使用</item>
    /// </list>
    /// </remarks>
    public StringSearchResult(string originalString, string searchString, bool findAll, bool isRegex = false)
    {
        // 防御性编程：确保原始字符串不为null
        _originalString = originalString ?? string.Empty;

        // 初始化结果集合
        _positions = new List<int>();
        _matches = new List<string>();

        // 参数验证：空的查找字符串直接返回空结果
        if (string.IsNullOrEmpty(searchString))
            return;

        // 根据查找模式选择相应的查找策略
        if (isRegex)
        {
            // 使用正则表达式查找
            FindByRegex(searchString, findAll);
        }
        else
        {
            // 使用普通字符串查找
            FindByString(searchString, findAll);
        }
    }

    #endregion

    #region 公共属性实现

    /// <summary>
    /// 获取是否找到任何匹配项
    /// </summary>
    /// <value>如果找到一个或多个匹配项返回true，否则返回false</value>
    /// <remarks>
    /// 这是一个计算属性，基于位置列表的数量来判断。
    /// 性能：O(1)操作，直接访问List.Count属性。
    /// 等价于：Positions.Count > 0 或 Matches.Count > 0
    /// </remarks>
    public bool Found => _positions.Count > 0;

    /// <summary>
    /// 获取所有匹配项在原字符串中的位置列表
    /// </summary>
    /// <value>只读的位置列表，按出现顺序排序，位置从0开始计算</value>
    /// <remarks>
    /// <para>返回特性：</para>
    /// <list type="bullet">
    /// <item>返回IReadOnlyList确保外部无法修改</item>
    /// <item>位置按升序排列（查找算法保证）</item>
    /// <item>空结果时返回空列表而非null</item>
    /// <item>每次访问都创建新的只读包装器</item>
    /// </list>
    /// <para>性能说明：</para>
    /// <list type="bullet">
    /// <item>AsReadOnly()创建轻量级包装器，不复制数据</item>
    /// <item>频繁访问时建议缓存结果</item>
    /// </list>
    /// </remarks>
    public IReadOnlyList<int> Positions => _positions.AsReadOnly();

    /// <summary>
    /// 获取所有匹配的字符串内容列表
    /// </summary>
    /// <value>只读的匹配内容列表，与位置列表一一对应</value>
    /// <remarks>
    /// <para>内容特性：</para>
    /// <list type="bullet">
    /// <item>与Positions列表索引完全对应</item>
    /// <item>普通字符串查找：所有元素内容相同</item>
    /// <item>正则表达式查找：可能包含不同的匹配内容</item>
    /// <item>保持原始字符串的大小写和格式</item>
    /// </list>
    /// <para>使用示例：</para>
    /// <code>
    /// for (int i = 0; i &lt; result.Positions.Count; i++)
    /// {
    ///     Console.WriteLine($"位置{result.Positions[i]}: {result.Matches[i]}");
    /// }
    /// </code>
    /// </remarks>
    public IReadOnlyList<string> Matches => _matches.AsReadOnly();

    #endregion

    #region 公共方法实现

    /// <summary>
    /// 将查找结果转换为新的字符串工具箱实例以继续处理
    /// </summary>
    /// <returns>包含处理后内容的新工具箱实例</returns>
    /// <remarks>
    /// <para>转换逻辑详解：</para>
    /// <list type="number">
    /// <item>检查是否有匹配结果</item>
    /// <item>无匹配时：返回包含空字符串的工具箱</item>
    /// <item>有匹配时：将所有匹配内容用换行符连接</item>
    /// <item>创建新的工具箱实例包含连接后的结果</item>
    /// </list>
    /// <para>设计考虑：</para>
    /// <list type="bullet">
    /// <item>使用Environment.NewLine确保跨平台兼容性</item>
    /// <item>空结果返回空字符串而非null，保持一致性</item>
    /// <item>支持链式操作的关键方法</item>
    /// </list>
    /// <para>性能影响：</para>
    /// <list type="bullet">
    /// <item>string.Join操作对大量匹配可能较慢</item>
    /// <item>创建新的工具箱实例有轻微开销</item>
    /// <item>结果可以缓存以避免重复计算</item>
    /// </list>
    /// </remarks>
    /// <example>
    /// <code>
    /// var result = toolbox
    ///     .FindAll("error")           // 查找所有错误
    ///     .GetResults()               // 转换为工具箱
    ///     .Apply(s => s.ToUpper())    // 转换为大写
    ///     .ToString();                // 获取最终结果
    /// </code>
    /// </example>
    public IStringOperationToolbox GetResults()
    {
        // 检查是否有匹配结果
        if (!Found)
            return StringOperationToolbox.From(string.Empty);

        // 将所有匹配内容用换行符连接
        var joinedMatches = string.Join(Environment.NewLine, _matches);

        // 创建包含结果的新工具箱实例
        return StringOperationToolbox.From(joinedMatches);
    }

    #endregion

    #region 私有查找方法实现

    /// <summary>
    /// 执行普通字符串查找操作
    /// </summary>
    /// <param name="searchString">要查找的目标字符串</param>
    /// <param name="findAll">是否查找所有匹配项（false表示只查找第一个）</param>
    /// <remarks>
    /// <para>算法实现：</para>
    /// <list type="number">
    /// <item>使用IndexOf方法进行高效的字符串搜索</item>
    /// <item>采用序数比较（StringComparison.Ordinal）确保精确匹配</item>
    /// <item>通过循环和起始位置递增实现全局查找</item>
    /// <item>避免重叠匹配，每次从上一个匹配的下一个字符开始</item>
    /// </list>
    /// <para>性能特点：</para>
    /// <list type="bullet">
    /// <item>时间复杂度：O(n*m)，其中n是原字符串长度，m是查找字符串长度</item>
    /// <item>空间复杂度：O(k)，其中k是匹配项数量</item>
    /// <item>使用.NET优化的IndexOf实现，性能优于手工循环</item>
    /// </list>
    /// </remarks>
    private void FindByString(string searchString, bool findAll)
    {
        // 初始化搜索起始位置
        var startIndex = 0;

        // 循环查找直到没有更多匹配
        while (true)
        {
            // 从当前起始位置开始查找目标字符串
            // 使用Ordinal比较确保区分大小写的精确匹配
            var index = _originalString.IndexOf(searchString, startIndex, StringComparison.Ordinal);

            // 如果没有找到匹配项，退出循环
            if (index == -1)
                break;

            // 记录找到的位置
            _positions.Add(index);

            // 记录匹配的内容（对于普通字符串查找，总是相同的）
            _matches.Add(searchString);

            // 如果只需要第一个匹配，立即退出
            if (!findAll)
                break;

            // 更新下次搜索的起始位置
            // +1 确保不会重复匹配同一个位置，避免无限循环
            startIndex = index + 1;
        }
    }

    /// <summary>
    /// 执行正则表达式模式查找操作
    /// </summary>
    /// <param name="pattern">正则表达式模式字符串</param>
    /// <param name="findAll">是否查找所有匹配项（false表示只查找第一个）</param>
    /// <remarks>
    /// <para>正则表达式处理流程：</para>
    /// <list type="number">
    /// <item>编译正则表达式模式</item>
    /// <item>根据findAll参数选择匹配策略</item>
    /// <item>提取匹配位置和内容</item>
    /// <item>处理编译或匹配过程中的异常</item>
    /// </list>
    /// <para>错误处理策略：</para>
    /// <list type="bullet">
    /// <item>捕获所有异常，确保方法不会崩溃</item>
    /// <item>无效模式或匹配失败时返回空结果</item>
    /// <item>异常包括：语法错误、超时、内存不足等</item>
    /// </list>
    /// <para>性能考虑：</para>
    /// <list type="bullet">
    /// <item>每次调用都重新编译正则表达式</item>
    /// <item>复杂模式可能导致性能问题</item>
    /// <item>大文本匹配时建议使用编译选项</item>
    /// </list>
    /// </remarks>
    private void FindByRegex(string pattern, bool findAll)
    {
        try
        {
            // 编译正则表达式模式
            // 注意：这里每次都重新编译，对于重复使用的模式效率较低
            var regex = new Regex(pattern);

            if (findAll)
            {
                // 全局匹配：查找所有匹配项
                var matches = regex.Matches(_originalString);

                // 遍历所有匹配结果
                foreach (Match match in matches)
                {
                    // 验证匹配是否成功（通常总是true，但保险起见）
                    if (match.Success)
                    {
                        // 记录匹配位置（基于原字符串的索引）
                        _positions.Add(match.Index);

                        // 记录匹配内容（可能与模式不同，如捕获组）
                        _matches.Add(match.Value);
                    }
                }
            }
            else
            {
                // 单次匹配：只查找第一个匹配项
                var match = regex.Match(_originalString);

                // 检查是否找到匹配
                if (match.Success)
                {
                    // 记录第一个匹配的位置和内容
                    _positions.Add(match.Index);
                    _matches.Add(match.Value);
                }
            }
        }
        catch
        {
            // 全面的异常处理：捕获所有可能的异常
            // 可能的异常类型：
            // - ArgumentException: 无效的正则表达式语法
            // - RegexMatchTimeoutException: 匹配超时
            // - OutOfMemoryException: 内存不足
            // - 其他运行时异常

            // 发生异常时不添加任何结果，保持_positions和_matches为空
            // 这确保了Found属性返回false，调用者可以安全地处理失败情况
        }
    }

    #endregion
}

/// <summary>
/// 字符串之间查找结果
/// </summary>
public class StringBetweenSearchResult : IStringSearchResult
{
    private readonly string _originalString;
    private readonly List<int> _positions;
    private readonly List<string> _matches;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="originalString">原始字符串</param>
    /// <param name="startString">起始字符串</param>
    /// <param name="endString">结束字符串</param>
    public StringBetweenSearchResult(string originalString, string startString, string endString)
    {
        _originalString = originalString ?? string.Empty;
        _positions = new List<int>();
        _matches = new List<string>();

        if (string.IsNullOrEmpty(startString) || string.IsNullOrEmpty(endString))
            return;

        FindBetween(startString, endString);
    }

    /// <summary>
    /// 是否找到
    /// </summary>
    public bool Found => _positions.Count > 0;

    /// <summary>
    /// 匹配的位置列表
    /// </summary>
    public IReadOnlyList<int> Positions => _positions.AsReadOnly();

    /// <summary>
    /// 匹配的内容列表
    /// </summary>
    public IReadOnlyList<string> Matches => _matches.AsReadOnly();

    /// <summary>
    /// 获取结果
    /// </summary>
    /// <returns>工具箱实例</returns>
    public IStringOperationToolbox GetResults()
    {
        if (!Found)
            return StringOperationToolbox.From(string.Empty);

        return StringOperationToolbox.From(string.Join(Environment.NewLine, _matches));
    }

    /// <summary>
    /// 查找两个字符串之间的内容
    /// </summary>
    /// <param name="startString">起始字符串</param>
    /// <param name="endString">结束字符串</param>
    private void FindBetween(string startString, string endString)
    {
        var searchIndex = 0;

        while (searchIndex < _originalString.Length)
        {
            var startIndex = _originalString.IndexOf(startString, searchIndex, StringComparison.Ordinal);
            if (startIndex == -1)
                break;

            var contentStartIndex = startIndex + startString.Length;
            var endIndex = _originalString.IndexOf(endString, contentStartIndex, StringComparison.Ordinal);
            if (endIndex == -1)
                break;

            var content = _originalString.Substring(contentStartIndex, endIndex - contentStartIndex);
            _positions.Add(contentStartIndex);
            _matches.Add(content);

            searchIndex = endIndex + endString.Length;
        }
    }
}

/// <summary>
/// 带上下文的字符串查找结果
/// </summary>
public class StringContextSearchResult : IStringSearchResult
{
    private readonly string _originalString;
    private readonly List<int> _positions;
    private readonly List<string> _matches;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="originalString">原始字符串</param>
    /// <param name="searchString">查找字符串</param>
    /// <param name="beforeContext">前置上下文长度</param>
    /// <param name="afterContext">后置上下文长度</param>
    public StringContextSearchResult(string originalString, string searchString, int beforeContext, int afterContext)
    {
        _originalString = originalString ?? string.Empty;
        _positions = new List<int>();
        _matches = new List<string>();

        if (string.IsNullOrEmpty(searchString))
            return;

        FindWithContext(searchString, beforeContext, afterContext);
    }

    /// <summary>
    /// 是否找到
    /// </summary>
    public bool Found => _positions.Count > 0;

    /// <summary>
    /// 匹配的位置列表
    /// </summary>
    public IReadOnlyList<int> Positions => _positions.AsReadOnly();

    /// <summary>
    /// 匹配的内容列表
    /// </summary>
    public IReadOnlyList<string> Matches => _matches.AsReadOnly();

    /// <summary>
    /// 获取结果
    /// </summary>
    /// <returns>工具箱实例</returns>
    public IStringOperationToolbox GetResults()
    {
        if (!Found)
            return StringOperationToolbox.From(string.Empty);

        return StringOperationToolbox.From(string.Join(Environment.NewLine, _matches));
    }

    /// <summary>
    /// 带上下文查找
    /// </summary>
    /// <param name="searchString">查找字符串</param>
    /// <param name="beforeContext">前置上下文长度</param>
    /// <param name="afterContext">后置上下文长度</param>
    private void FindWithContext(string searchString, int beforeContext, int afterContext)
    {
        var startIndex = 0;

        while (true)
        {
            var index = _originalString.IndexOf(searchString, startIndex, StringComparison.Ordinal);
            if (index == -1)
                break;

            var contextStart = Math.Max(0, index - beforeContext);
            var contextEnd = Math.Min(_originalString.Length, index + searchString.Length + afterContext);
            var contextContent = _originalString.Substring(contextStart, contextEnd - contextStart);

            _positions.Add(index);
            _matches.Add(contextContent);

            startIndex = index + 1;
        }
    }
}
