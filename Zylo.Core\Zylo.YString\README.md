# Zylo.StringToolbox 超强字符串操作工具箱

![.NET Version](https://img.shields.io/badge/.NET-6.0%20%7C%208.0-blue)
![License](https://img.shields.io/badge/License-MIT-green)
![NuGet](https://img.shields.io/badge/NuGet-v1.0.0-orange)

**Zylo.StringToolbox** 是一个超强的字符串操作工具箱，提供200+字符串处理方法，支持截取、查找、替换、统计、位置操作等超灵活功能。

## ✨ 核心特性

- 🚀 **超灵活操作** - 支持链式调用，操作流畅自然
- 🔍 **强大查找功能** - 支持正则表达式、上下文查找、批量查找
- ✂️ **精确截取功能** - 支持位置截取、模式截取、范围截取
- 📊 **完整统计功能** - 字符串出现次数、位置统计、内容分析
- 📍 **位置操作功能** - 获取左右内容、位置查找、范围操作
- ⚡ **高性能设计** - 优化算法，零分配操作
- 🔧 **易于使用** - 直观的API设计，丰富的扩展方法

## 🚀 快速开始

### 安装

```bash
dotnet add package Zylo.StringToolbox
```

### 基础使用

```csharp
using Zylo.StringToolbox.Core;
using Zylo.StringToolbox.Extensions;

// 创建工具箱实例
var text = "Hello, World! This is a test string.";
var toolbox = StringOperationToolbox.From(text);

// 或者使用扩展方法
var toolbox2 = text.ToToolbox();
```

## 📋 功能演示

### 🔍 字符串查找操作

```csharp
var text = "Hello World! Hello Universe! Hello Galaxy!";

// 查找所有匹配
var result = StringOperationToolbox.From(text)
    .FindAll("Hello");
Console.WriteLine($"找到 {result.Positions.Count} 个匹配");

// 统计出现次数
var count = text.CountOccurrences("Hello");
Console.WriteLine($"'Hello' 出现了 {count} 次");

// 获取所有位置
var positions = text.GetAllPositions("Hello");
Console.WriteLine($"位置: [{string.Join(", ", positions)}]");
```

### ✂️ 字符串截取操作

```csharp
var text = "Hello, World! This is a test string.";

// 基础截取
var slice1 = StringOperationToolbox.From(text)
    .Slice(0, 5)
    .ToString(); // "Hello"

// 从指定字符串开始截取
var slice2 = StringOperationToolbox.From(text)
    .SliceFrom("World")
    .ToString(); // "World! This is a test string."

// 截取两个字符串之间的内容
var slice3 = StringOperationToolbox.From(text)
    .SliceBetween("Hello, ", "!")
    .ToString(); // "World"
```

### 📍 位置和内容操作

```csharp
var text = "Hello World! Hello Universe!";

// 获取指定字符串左侧内容
var leftContent = text.GetLeftContent("World", 6);
Console.WriteLine(leftContent); // "Hello "

// 获取指定字符串右侧内容
var rightContent = text.GetRightContent("World", 1);
Console.WriteLine(rightContent); // "!"

// 获取周围内容
var surrounding = text.GetSurroundingContent("Universe", 6, 1);
Console.WriteLine(surrounding); // "Hello Universe!"

// 获取第N次出现的位置
var nthPos = text.GetNthPosition("Hello", 2);
Console.WriteLine(nthPos); // 13
```

### 🔗 链式操作

```csharp
var result = StringOperationToolbox.From("  Hello, World! This is a test.  ")
    .Apply(s => s.Trim())                    // 去除首尾空格
    .SliceTo("test")                         // 截取到"test"
    .Apply(s => s.Replace("Hello", "Hi"))    // 替换文本
    .Apply(s => s.ToUpper())                 // 转大写
    .ToString();

Console.WriteLine(result); // "HI, WORLD! THIS IS A "
```

## 🔧 高级功能

### 正则表达式支持

```csharp
var text = "Email: <EMAIL>, Phone: ************";

// 按正则模式截取
var email = StringOperationToolbox.From(text)
    .SliceByPattern(@"\b\w+@\w+\.\w+\b")
    .ToString(); // "<EMAIL>"

// 按正则模式查找
var phoneResult = StringOperationToolbox.From(text)
    .FindByPattern(@"\d{3}-\d{3}-\d{4}");
Console.WriteLine(phoneResult.Matches[0]); // "************"
```

### 上下文查找

```csharp
var text = "The quick brown fox jumps over the lazy dog.";

// 带上下文的查找
var contextResult = StringOperationToolbox.From(text)
    .FindWithContext("fox", 5, 5);
Console.WriteLine(contextResult.Matches[0]); // "brown fox jumps"
```

### 异步操作

```csharp
var largeText = await File.ReadAllTextAsync("large-file.txt");

var result = await StringOperationToolbox.From(largeText)
    .ApplyAsync(async text => await ProcessLargeTextAsync(text));
```

## 📊 性能特性

- ⚡ **高效算法** - 优化的字符串处理算法
- 🔄 **零分配** - 核心操作避免不必要的内存分配
- 📈 **可扩展** - 支持自定义扩展和插件
- 🧵 **线程安全** - 不可变设计，天然线程安全

## 🎯 使用场景

- **文本处理** - 日志分析、数据清洗、内容提取
- **数据解析** - CSV处理、配置文件解析、格式转换
- **内容分析** - 关键词提取、模式匹配、统计分析
- **字符串操作** - 格式化、验证、转换、替换

## 📚 API 参考

### 核心类

- `StringOperationToolbox` - 主工具箱类
- `IStringOperationToolbox` - 核心接口
- `IStringSliceOperations` - 截取操作接口
- `IStringSearchOperations` - 查找操作接口

### 扩展方法

- `ToToolbox()` - 转换为工具箱
- `CountOccurrences()` - 统计出现次数
- `GetAllPositions()` - 获取所有位置
- `GetLeftContent()` - 获取左侧内容
- `GetRightContent()` - 获取右侧内容
- `GetSurroundingContent()` - 获取周围内容

## 🤝 贡献

欢迎贡献代码、报告问题或提出建议！

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🔗 相关链接

- [GitHub 仓库](https://github.com/zylo/StringToolbox)
- [NuGet 包](https://www.nuget.org/packages/Zylo.StringToolbox)
- [文档](https://docs.zylo.dev/stringtoolbox)

---

**Zylo.StringToolbox** - 让字符串操作变得简单而强大！ 🚀
