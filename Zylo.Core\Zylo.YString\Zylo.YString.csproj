<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <!-- 基本项目配置 - 使用 Directory.Build.props 中的全局配置 -->
        <PackageId>Zylo.YString</PackageId>
        <Product>Zylo String Toolbox</Product>

        <!-- NuGet 包信息 -->
        <Description>🚀 Zylo.YString - 超强字符串操作工具箱。提供200+字符串处理方法，支持截取、查找、替换、统计、位置操作等超灵活功能。</Description>
        <PackageTags>string;manipulation;toolbox;position;search;replace;extract;zylo</PackageTags>
        <PackageProjectUrl>https://github.com/zylo/StringToolbox</PackageProjectUrl>
        <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>

        <!-- 发布说明 -->
        <PackageReleaseNotes>
            🎉 v1.2 - 功能增强与稳定性提升：
            - 🔧 200+ 字符串处理方法
            - 🔍 强大的查找和替换功能
            - 📊 字符串统计和分析
            - 🎯 精确的位置操作
            - 🚀 高性能字符串处理
        </PackageReleaseNotes>
    </PropertyGroup>
    <!-- ========== NuGet 包版本号 ========== -->
    <ItemGroup>
        <None Include="README.md" Pack="true" PackagePath="\" /><!-- 将README.md包含到NuGet包中 -->
    </ItemGroup>

</Project>