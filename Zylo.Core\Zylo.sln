﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36109.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.Core", "Zylo.Core\Zylo.Core.csproj", "{8FB31863-7641-4A24-81C3-55F8CB505B19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YLog.Runtime", "Zylo.YLog.Runtime\Zylo.YLog.Runtime.csproj", "{C876FCD3-65CD-478A-B1D6-16609D04D626}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YIO", "Zylo.YIO\Zylo.YIO.csproj", "{E0194A1D-3A5F-4E20-9887-F28E02AB72AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YIO.Tests", "Zylo.YIO.Tests\Zylo.YIO.Tests.csproj", "{28713054-BC81-4439-A514-166AA9E3F471}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YIO.Demo", "Zylo.YIO.Demo\Zylo.YIO.Demo.csproj", "{7A828D29-8E80-471C-BD84-558EFAF3DE6A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YData", "Zylo.YData\Zylo.YData.csproj", "{C38A9F62-B0C9-4535-B6B8-38CD29F19696}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YData.Tests", "Zylo.YData.Tests\Zylo.YData.Tests.csproj", "{947C469E-EB29-4429-A2EF-CA44D0B96BD2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YData.Demo", "Zylo.YData.Demo\Zylo.YData.Demo.csproj", "{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YRegex", "Zylo.YRegex\Zylo.YRegex.csproj", "{1793E3F5-0D5E-432F-B66B-9A0608B59F2D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YRegex.Tests", "Zylo.YRegex.Tests\Zylo.YRegex.Tests.csproj", "{954BE377-266F-431A-A952-1E4EEA57D794}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YRegex.Demo", "Zylo.YRegex.Demo\Zylo.YRegex.Demo.csproj", "{08415DB1-E20B-4BC3-80E3-E10CE59405D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Zylo.ToolTest", "Zylo.ToolTest", "{66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.YString", "Zylo.YString\Zylo.YString.csproj", "{8EE4F752-57FD-40B4-98EB-08B55122110D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Zylo.Tool", "Zylo.Tool", "{ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.All", "Zylo.All\Zylo.All.csproj", "{84E6BD90-794C-49F3-9082-B768D8C83816}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Zylo.Attrib", "Zylo.Attrib", "{38FC331D-D50D-4D20-968B-CDE49F805A91}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Zylo.Toolkit", "Zylo.Toolkit\Zylo.Toolkit.csproj", "{5A546AA4-863F-448F-AC11-E7A43A4668AA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ZyloServiceTest", "ZyloServiceTest\ZyloServiceTest.csproj", "{4A40A20D-ACE4-452D-A612-917E557A3F2B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "YStaticTest", "YStaticTest\YStaticTest.csproj", "{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestApp", "TestApp\TestApp.csproj", "{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8FB31863-7641-4A24-81C3-55F8CB505B19}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FB31863-7641-4A24-81C3-55F8CB505B19}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FB31863-7641-4A24-81C3-55F8CB505B19}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FB31863-7641-4A24-81C3-55F8CB505B19}.Release|Any CPU.Build.0 = Release|Any CPU
		{C876FCD3-65CD-478A-B1D6-16609D04D626}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C876FCD3-65CD-478A-B1D6-16609D04D626}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C876FCD3-65CD-478A-B1D6-16609D04D626}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C876FCD3-65CD-478A-B1D6-16609D04D626}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0194A1D-3A5F-4E20-9887-F28E02AB72AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0194A1D-3A5F-4E20-9887-F28E02AB72AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0194A1D-3A5F-4E20-9887-F28E02AB72AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0194A1D-3A5F-4E20-9887-F28E02AB72AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{28713054-BC81-4439-A514-166AA9E3F471}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28713054-BC81-4439-A514-166AA9E3F471}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28713054-BC81-4439-A514-166AA9E3F471}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28713054-BC81-4439-A514-166AA9E3F471}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A828D29-8E80-471C-BD84-558EFAF3DE6A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A828D29-8E80-471C-BD84-558EFAF3DE6A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A828D29-8E80-471C-BD84-558EFAF3DE6A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A828D29-8E80-471C-BD84-558EFAF3DE6A}.Release|Any CPU.Build.0 = Release|Any CPU
		{C38A9F62-B0C9-4535-B6B8-38CD29F19696}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C38A9F62-B0C9-4535-B6B8-38CD29F19696}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C38A9F62-B0C9-4535-B6B8-38CD29F19696}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C38A9F62-B0C9-4535-B6B8-38CD29F19696}.Release|Any CPU.Build.0 = Release|Any CPU
		{947C469E-EB29-4429-A2EF-CA44D0B96BD2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{947C469E-EB29-4429-A2EF-CA44D0B96BD2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{947C469E-EB29-4429-A2EF-CA44D0B96BD2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{947C469E-EB29-4429-A2EF-CA44D0B96BD2}.Release|Any CPU.Build.0 = Release|Any CPU
		{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{1793E3F5-0D5E-432F-B66B-9A0608B59F2D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1793E3F5-0D5E-432F-B66B-9A0608B59F2D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1793E3F5-0D5E-432F-B66B-9A0608B59F2D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1793E3F5-0D5E-432F-B66B-9A0608B59F2D}.Release|Any CPU.Build.0 = Release|Any CPU
		{954BE377-266F-431A-A952-1E4EEA57D794}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{954BE377-266F-431A-A952-1E4EEA57D794}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{954BE377-266F-431A-A952-1E4EEA57D794}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{954BE377-266F-431A-A952-1E4EEA57D794}.Release|Any CPU.Build.0 = Release|Any CPU
		{08415DB1-E20B-4BC3-80E3-E10CE59405D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{08415DB1-E20B-4BC3-80E3-E10CE59405D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{08415DB1-E20B-4BC3-80E3-E10CE59405D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{08415DB1-E20B-4BC3-80E3-E10CE59405D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EE4F752-57FD-40B4-98EB-08B55122110D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EE4F752-57FD-40B4-98EB-08B55122110D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EE4F752-57FD-40B4-98EB-08B55122110D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EE4F752-57FD-40B4-98EB-08B55122110D}.Release|Any CPU.Build.0 = Release|Any CPU
		{84E6BD90-794C-49F3-9082-B768D8C83816}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84E6BD90-794C-49F3-9082-B768D8C83816}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84E6BD90-794C-49F3-9082-B768D8C83816}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84E6BD90-794C-49F3-9082-B768D8C83816}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A546AA4-863F-448F-AC11-E7A43A4668AA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A546AA4-863F-448F-AC11-E7A43A4668AA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A546AA4-863F-448F-AC11-E7A43A4668AA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A546AA4-863F-448F-AC11-E7A43A4668AA}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A40A20D-ACE4-452D-A612-917E557A3F2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A40A20D-ACE4-452D-A612-917E557A3F2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A40A20D-ACE4-452D-A612-917E557A3F2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A40A20D-ACE4-452D-A612-917E557A3F2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E}.Release|Any CPU.Build.0 = Release|Any CPU
		{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8FB31863-7641-4A24-81C3-55F8CB505B19} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{C876FCD3-65CD-478A-B1D6-16609D04D626} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{E0194A1D-3A5F-4E20-9887-F28E02AB72AA} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{28713054-BC81-4439-A514-166AA9E3F471} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{7A828D29-8E80-471C-BD84-558EFAF3DE6A} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{C38A9F62-B0C9-4535-B6B8-38CD29F19696} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{947C469E-EB29-4429-A2EF-CA44D0B96BD2} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{F826A8C5-B241-47BD-9388-3D6DEFE0B6C5} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{1793E3F5-0D5E-432F-B66B-9A0608B59F2D} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{954BE377-266F-431A-A952-1E4EEA57D794} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{08415DB1-E20B-4BC3-80E3-E10CE59405D1} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{8EE4F752-57FD-40B4-98EB-08B55122110D} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{84E6BD90-794C-49F3-9082-B768D8C83816} = {ECB878E0-6EFF-4C2B-9CD4-08DC881B8857}
		{4A40A20D-ACE4-452D-A612-917E557A3F2B} = {38FC331D-D50D-4D20-968B-CDE49F805A91}
		{AE64FEE5-017C-425E-81C1-B51D8E7BFB3E} = {38FC331D-D50D-4D20-968B-CDE49F805A91}
		{F078E03E-5AB2-4DCF-908C-51C08EE0B9FD} = {66A2E7AF-E471-4AF8-BA7B-1A225886BDB2}
		{5A546AA4-863F-448F-AC11-E7A43A4668AA} = {38FC331D-D50D-4D20-968B-CDE49F805A91}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2DC0BDFC-8C9B-4705-8933-93B2DBFF0702}
	EndGlobalSection
EndGlobal
