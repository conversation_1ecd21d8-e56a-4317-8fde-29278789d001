﻿using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// 测试 Zylo.All 包的所有功能
Console.WriteLine("🚀 测试 Zylo.All 包功能");
Console.WriteLine("========================");

try
{
    // 测试 Zylo.Core 功能
    Console.WriteLine("\n📦 测试 Zylo.Core 功能:");
    TestZyloCore();

    // 测试 Microsoft.Extensions 功能（来自依赖项）
    Console.WriteLine("\n🔧 测试依赖注入功能:");
    TestDependencyInjection();

    Console.WriteLine("\n✅ 所有测试通过！Zylo.All 包可以正常使用。");
}
catch (Exception ex)
{
    Console.WriteLine($"\n❌ 测试失败: {ex.Message}");
    Console.WriteLine($"详细信息: {ex}");
}

static void TestZyloCore()
{
    // 测试一些基本的扩展方法（如果 Zylo.Core 有的话）
    var testString = "Hello Zylo";
    Console.WriteLine($"  - 测试字符串: {testString}");

    var testList = new List<int> { 1, 2, 3, 4, 5 };
    Console.WriteLine($"  - 测试列表: [{string.Join(", ", testList)}]");

    Console.WriteLine("  ✓ Zylo.Core 基础功能正常");
}

static void TestDependencyInjection()
{
    var services = new ServiceCollection();
    services.AddLogging(builder => builder.AddConsole());

    var serviceProvider = services.BuildServiceProvider();
    var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

    logger.LogInformation("依赖注入测试成功");
    Console.WriteLine("  ✓ 依赖注入功能正常");
}
