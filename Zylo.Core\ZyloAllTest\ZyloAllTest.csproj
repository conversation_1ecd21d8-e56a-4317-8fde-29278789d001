﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <!-- 测试本地 NuGet 包 -->
  <ItemGroup>
    <PackageReference Include="Zylo.All" Version="1.3.1" />
  </ItemGroup>

  <!-- 本地 NuGet 源配置 -->
  <PropertyGroup>
    <RestoreSources>$(RestoreSources);D:\NuGet;https://api.nuget.org/v3/index.json</RestoreSources>
  </PropertyGroup>

</Project>