using Zylo.Toolkit.Attributes;

namespace ZyloServiceTest;

/// <summary>
/// YService v1.4 复杂参数处理测试
/// 
/// 🎯 测试目标：
/// 验证 YService 能否正确处理各种复杂参数类型，
/// 包括泛型、元组、字符串默认值、ref/out 参数等
/// </summary>
[YService]
public class ComplexParameterTests
{
    #region v1.4 升级 - 泛型参数测试

    /// <summary>
    /// 测试泛型参数：Dictionary&lt;string, int&gt;
    /// </summary>
    public string ProcessDictionary(Dictionary<string, int> data, string prefix = "Result: ")
    {
        return prefix + string.Join(", ", data.Select(kvp => $"{kvp.Key}={kvp.Value}"));
    }

    /// <summary>
    /// 测试复杂泛型：List&lt;Dictionary&lt;string, object&gt;&gt;
    /// </summary>
    public int CountComplexData(List<Dictionary<string, object>> complexData)
    {
        return complexData.Sum(dict => dict.Count);
    }

    #endregion

    #region v1.4 升级 - 字符串默认值测试

    /// <summary>
    /// 测试包含逗号的字符串默认值
    /// </summary>
    public string FormatWithComma(string text, string separator = ", ")
    {
        return $"Result: {text}{separator}End";
    }

    /// <summary>
    /// 测试包含分号的字符串默认值
    /// </summary>
    public string FormatWithSemicolon(string text, string delimiter = "; ")
    {
        return $"Items: {text}{delimiter}Done";
    }

    /// <summary>
    /// 测试包含转义字符的字符串默认值
    /// </summary>
    public string FormatPath(string filename, string basePath = "C:\\temp\\")
    {
        return basePath + filename;
    }

    /// <summary>
    /// 测试包含引号的字符串默认值
    /// </summary>
    public string FormatQuoted(string text, string wrapper = "\"")
    {
        return wrapper + text + wrapper;
    }

    #endregion

    #region v1.4 升级 - 元组参数测试

    /// <summary>
    /// 测试元组参数：(int x, int y)
    /// </summary>
    public double CalculateDistance((int x, int y) point1, (int x, int y) point2)
    {
        var dx = point2.x - point1.x;
        var dy = point2.y - point1.y;
        return Math.Sqrt(dx * dx + dy * dy);
    }

    /// <summary>
    /// 测试复杂元组：(string name, int age, bool active)
    /// </summary>
    public string FormatPerson((string name, int age, bool active) person)
    {
        return $"{person.name} ({person.age}) - {(person.active ? "Active" : "Inactive")}";
    }

    #endregion

    #region v1.4 升级 - ref/out/in 参数测试

    /// <summary>
    /// 测试 ref 参数
    /// </summary>
    public void SwapValues(ref int a, ref int b)
    {
        (a, b) = (b, a);
    }

    /// <summary>
    /// 测试 out 参数
    /// </summary>
    public bool TryParseData(string input, out int result, out string error)
    {
        error = "";
        if (int.TryParse(input, out result))
        {
            return true;
        }
        error = "Invalid number format";
        return false;
    }

    /// <summary>
    /// 测试 in 参数（只读引用）
    /// </summary>
    public double CalculateDistanceIn(in (double x, double y) point1, in (double x, double y) point2)
    {
        var dx = point2.x - point1.x;
        var dy = point2.y - point1.y;
        return Math.Sqrt(dx * dx + dy * dy);
    }

    #endregion

    #region v1.4 升级 - params 参数测试

    /// <summary>
    /// 测试 params 参数
    /// </summary>
    public string CombineStrings(string separator, params string[] values)
    {
        return string.Join(separator, values);
    }

    /// <summary>
    /// 测试 params 与其他参数组合
    /// </summary>
    public T[] CreateArray<T>(int capacity, T defaultValue, params T[] initialValues)
    {
        var result = new T[capacity];
        Array.Fill(result, defaultValue);
        
        for (int i = 0; i < Math.Min(initialValues.Length, capacity); i++)
        {
            result[i] = initialValues[i];
        }
        
        return result;
    }

    #endregion

    #region v1.4 升级 - 复杂组合测试

    /// <summary>
    /// 测试多种复杂参数的组合使用（简化版）
    /// </summary>
    public bool ProcessComplexData<T>(
        Dictionary<string, List<T>> data,
        ref int processedCount,
        out string summary,
        params string[] filters)
    {
        processedCount = 0;
        summary = "";

        try
        {
            var filteredData = data.Where(kvp => 
                filters.Length == 0 || filters.Contains(kvp.Key)).ToList();

            processedCount = filteredData.Sum(kvp => kvp.Value.Count);
            summary = "Result: " + 
                     string.Join(", ", filteredData.Select(kvp => $"{kvp.Key}: {kvp.Value.Count}")) + 
                     " - Done";

            return true;
        }
        catch
        {
            summary = "Error occurred during processing";
            return false;
        }
    }

    #endregion
}
