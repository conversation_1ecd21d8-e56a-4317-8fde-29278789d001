﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ZyloServiceTest.Services;
using ZyloServiceTest.Tests;

namespace ZyloServiceTest;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 YService 功能测试开始");
        Console.WriteLine(new string('=', 60));

        // 🏗️ 创建主机构建器
        var builder = Host.CreateApplicationBuilder(args);

        // 🔧 注册 YService 生成的服务
        builder.Services.AddAllZyloServiceTestServices();

        // 🚀 构建主机
        var host = builder.Build();

        try
        {
            Console.WriteLine("🎯 YService 功能测试开始");
            Console.WriteLine(new string('=', 60));

            // 🧪 运行所有测试
            await RunAllTests(host.Services);

            Console.WriteLine(new string('=', 60));
            Console.WriteLine("✅ 所有测试完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    static async Task RunAllTests(IServiceProvider services)
    {
        // 📋 测试类级属性服务
        await ClassLevelServiceTests.RunTests(services);

        // 🔧 测试方法级属性服务
        await MethodLevelServiceTests.RunTests(services);

        // 🏗️ 测试静态类包装器
        StaticServiceTests.RunTests(services);

        // 📝 测试文档注释保留
        DocumentationTests.RunTests(services);

        // 🔧 测试复杂类型支持
        await ComplexTypeTests.RunTests(services);

        // 🆕 测试 v1.1 新功能
        await V11FeatureTests.RunTests(services);

        // 🔍 测试诊断功能
        V11FeatureTests.TestDiagnosticFeatures();

        // 🔧 测试便捷属性参数支持
        await ConvenienceAttributeTests.RunTests(services);

        // 🔄 测试生命周期验证
        ConvenienceAttributeTests.TestLifetimeVerification(services);
    }
}
