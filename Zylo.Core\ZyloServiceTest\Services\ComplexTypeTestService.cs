


namespace ZyloServiceTest.Services;

/// <summary>
/// 复杂类型测试服务 - 测试泛型类、委托、复杂类型参数的支持
/// </summary>
[YServiceScoped]
public partial class ComplexTypeTestService
{
    #region 🔧 泛型方法测试

    /// <summary>
    /// 基础泛型方法
    /// </summary>
    /// <typeparam name="T">泛型类型参数</typeparam>
    /// <param name="item">输入项</param>
    /// <returns>处理后的项</returns>
    public T ProcessGeneric<T>(T item) where T : class
    {
        Console.WriteLine($"处理泛型类型: {typeof(T).Name}");
        return item;
    }

    /// <summary>
    /// 多泛型参数方法
    /// </summary>
    /// <typeparam name="TInput">输入类型</typeparam>
    /// <typeparam name="TOutput">输出类型</typeparam>
    /// <param name="input">输入数据</param>
    /// <param name="converter">转换函数</param>
    /// <returns>转换后的数据</returns>
    public TOutput Transform<TInput, TOutput>(TInput input, Func<TInput, TOutput> converter)
        where TInput : class
        where TOutput : class, new()
    {
        return converter(input) ?? new TOutput();
    }

    /// <summary>
    /// 复杂泛型约束方法
    /// </summary>
    /// <typeparam name="T">泛型类型</typeparam>
    /// <param name="items">数据集合</param>
    /// <returns>最大值</returns>
    public T FindMax<T>(IEnumerable<T> items) where T : IComparable<T>
    {
        return items.Max();
    }

    #endregion

    #region 🔧 委托参数测试

    /// <summary>
    /// Action 委托参数
    /// </summary>
    /// <param name="action">要执行的动作</param>
    /// <param name="message">消息</param>
    public void ExecuteAction(Action<string> action, string message)
    {
        action(message);
    }

    /// <summary>
    /// Func 委托参数
    /// </summary>
    /// <typeparam name="T">输入类型</typeparam>
    /// <typeparam name="TResult">返回类型</typeparam>
    /// <param name="func">处理函数</param>
    /// <param name="input">输入值</param>
    /// <returns>处理结果</returns>
    public TResult ProcessWithFunc<T, TResult>(Func<T, TResult> func, T input)
    {
        return func(input);
    }

    /// <summary>
    /// Predicate 委托参数
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="items">数据集合</param>
    /// <param name="predicate">筛选条件</param>
    /// <returns>筛选结果</returns>
    public IEnumerable<T> FilterItems<T>(IEnumerable<T> items, Predicate<T> predicate)
    {
        return items.Where(item => predicate(item));
    }

    /// <summary>
    /// 自定义委托参数
    /// </summary>
    /// <param name="processor">自定义处理器</param>
    /// <param name="data">数据</param>
    /// <returns>处理结果</returns>
    public string ProcessWithCustomDelegate(CustomProcessor processor, string data)
    {
        return processor(data, DateTime.Now);
    }

    #endregion

    #region 🔧 复杂类型参数测试

    /// <summary>
    /// 字典参数
    /// </summary>
    /// <param name="data">字典数据</param>
    /// <param name="key">键</param>
    /// <returns>值</returns>
    public string? GetFromDictionary(Dictionary<string, object> data, string key)
    {
        return data.TryGetValue(key, out var value) ? value?.ToString() : null;
    }

    /// <summary>
    /// 元组参数和返回值
    /// </summary>
    /// <param name="input">输入元组</param>
    /// <returns>处理后的元组</returns>
    public (string Name, int Age, bool IsActive) ProcessTuple((string Name, int Age) input)
    {
        return (input.Name.ToUpper(), input.Age, true);
    }

    /// <summary>
    /// 可空引用类型
    /// </summary>
    /// <param name="data">可空数据</param>
    /// <returns>处理结果</returns>
    public string ProcessNullable(string? data)
    {
        return data ?? "默认值";
    }

    /// <summary>
    /// 数组和集合参数
    /// </summary>
    /// <param name="array">数组</param>
    /// <param name="list">列表</param>
    /// <param name="set">集合</param>
    /// <returns>合并结果</returns>
    public IEnumerable<T> CombineCollections<T>(T[] array, List<T> list, HashSet<T> set)
    {
        return array.Concat(list).Concat(set).Distinct();
    }

    /// <summary>
    /// 嵌套泛型类型
    /// </summary>
    /// <param name="data">嵌套泛型数据</param>
    /// <returns>处理结果</returns>
    public Dictionary<string, List<T>> ProcessNestedGeneric<T>(Dictionary<string, List<T>> data)
    {
        return data.ToDictionary(
            kvp => kvp.Key.ToUpper(),
            kvp => kvp.Value.ToList()
        );
    }

    #endregion

    #region 🔧 异步方法测试

    /// <summary>
    /// 异步泛型方法
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="processor">异步处理器</param>
    /// <param name="data">数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理结果</returns>
    public async Task<T> ProcessAsync<T>(Func<T, Task<T>> processor, T data, CancellationToken cancellationToken = default)
    {
        cancellationToken.ThrowIfCancellationRequested();
        return await processor(data);
    }

    /// <summary>
    /// 异步委托方法
    /// </summary>
    /// <param name="asyncAction">异步动作</param>
    /// <param name="message">消息</param>
    /// <returns>任务</returns>
    public async Task ExecuteAsync(Func<string, Task> asyncAction, string message)
    {
        await asyncAction(message);
    }

    #endregion

    #region 🔧 接口和抽象类型测试

    /// <summary>
    /// 接口参数
    /// </summary>
    /// <param name="enumerable">可枚举接口</param>
    /// <param name="comparer">比较器接口</param>
    /// <returns>排序结果</returns>
    public IEnumerable<T> SortItems<T>(IEnumerable<T> enumerable, IComparer<T>? comparer = null)
    {
        return comparer == null
            ? enumerable.OrderBy(x => x)
            : enumerable.OrderBy(x => x, comparer);
    }

    /// <summary>
    /// 多接口约束
    /// </summary>
    /// <typeparam name="T">类型参数</typeparam>
    /// <param name="item">数据项</param>
    /// <returns>处理结果</returns>
    public string ProcessMultiInterface<T>(T item)
        where T : IComparable<T>, IEquatable<T>, IFormattable
    {
        return $"Comparable: {item.CompareTo(default(T))}, " +
               $"Equals: {item.Equals(default(T))}, " +
               $"Formatted: {item.ToString("G", null)}";
    }

    #endregion
}

#region 🔧 自定义类型定义

/// <summary>
/// 自定义委托类型
/// </summary>
/// <param name="data">数据</param>
/// <param name="timestamp">时间戳</param>
/// <returns>处理结果</returns>
public delegate string CustomProcessor(string data, DateTime timestamp);

/// <summary>
/// 测试数据类
/// </summary>
public class TestComplexData
{
    public string Name { get; set; } = string.Empty;
    public int Value { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

#endregion
