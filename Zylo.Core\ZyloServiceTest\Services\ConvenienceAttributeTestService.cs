

namespace ZyloServiceTest.Services;

/// <summary>
/// 便捷属性参数测试服务 - 测试 YServiceScoped/Singleton/Transient 的参数支持
/// </summary>
/// <remarks>
/// 这个文件测试便捷属性的各种参数组合：
/// - 无参数（默认行为）
/// - 单参数（自定义接口名称）
/// - 双参数（接口名称 + 描述）
/// </remarks>

#region 🔧 YServiceScoped 参数测试

/// <summary>
/// Scoped 服务 - 无参数（默认行为）
/// </summary>
[YServiceScoped]
public partial class DefaultScopedService
{
    /// <summary>
    /// 处理默认数据
    /// </summary>
    /// <param name="data">输入数据</param>
    /// <returns>处理结果</returns>
    public string ProcessDefault(string data)
    {
        return $"默认 Scoped 处理: {data}";
    }
}

/// <summary>
/// Scoped 服务 - 自定义接口名称
/// </summary>
[YServiceScoped("ICustomScopedProcessor")]
public partial class CustomScopedService
{
    /// <summary>
    /// 处理自定义数据
    /// </summary>
    /// <param name="data">输入数据</param>
    /// <returns>处理结果</returns>
    public string ProcessCustom(string data)
    {
        return $"自定义 Scoped 处理: {data}";
    }
}

/// <summary>
/// Scoped 服务 - 接口名称 + 描述
/// </summary>
[YServiceScoped("IAdvancedScopedManager", "高级作用域管理服务")]
public partial class AdvancedScopedService
{
    /// <summary>
    /// 执行高级处理
    /// </summary>
    /// <param name="data">输入数据</param>
    /// <param name="options">处理选项</param>
    /// <returns>处理结果</returns>
    public string ProcessAdvanced(string data, string options = "default")
    {
        return $"高级 Scoped 处理: {data} (选项: {options})";
    }
}

#endregion

#region 🔧 YServiceSingleton 参数测试

/// <summary>
/// Singleton 服务 - 无参数（默认行为）
/// </summary>
[YServiceSingleton]
public partial class DefaultSingletonService
{
    /// <summary>
    /// 获取全局配置
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>配置值</returns>
    public string GetGlobalConfig(string key)
    {
        return $"默认 Singleton 配置[{key}] = GlobalValue";
    }
}

/// <summary>
/// Singleton 服务 - 自定义接口名称
/// </summary>
[YServiceSingleton("ICustomCacheManager")]
public partial class CustomSingletonService
{
    /// <summary>
    /// 缓存数据
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <returns>缓存结果</returns>
    public bool CacheData(string key, string value)
    {
        Console.WriteLine($"自定义 Singleton 缓存: {key} = {value}");
        return true;
    }
}

/// <summary>
/// Singleton 服务 - 接口名称 + 描述
/// </summary>
[YServiceSingleton("IGlobalStateController", "全局状态控制器服务")]
public partial class AdvancedSingletonService
{
    /// <summary>
    /// 管理全局状态
    /// </summary>
    /// <param name="state">状态值</param>
    /// <param name="persistent">是否持久化</param>
    /// <returns>操作结果</returns>
    public string ManageGlobalState(string state, bool persistent = true)
    {
        var persistText = persistent ? "持久化" : "临时";
        return $"高级 Singleton 状态管理: {state} ({persistText})";
    }
}

#endregion

#region 🔧 YServiceTransient 参数测试

/// <summary>
/// Transient 服务 - 无参数（默认行为）
/// </summary>
[YServiceTransient]
public partial class DefaultTransientService
{
    /// <summary>
    /// 生成临时数据
    /// </summary>
    /// <param name="prefix">前缀</param>
    /// <returns>生成的数据</returns>
    public string GenerateTemporaryData(string prefix = "TEMP")
    {
        return $"默认 Transient 数据: {prefix}_{Guid.NewGuid():N}";
    }
}

/// <summary>
/// Transient 服务 - 自定义接口名称
/// </summary>
[YServiceTransient("ICustomTokenGenerator")]
public partial class CustomTransientService
{
    /// <summary>
    /// 生成自定义令牌
    /// </summary>
    /// <param name="type">令牌类型</param>
    /// <param name="length">令牌长度</param>
    /// <returns>生成的令牌</returns>
    public string GenerateCustomToken(string type, int length = 32)
    {
        var token = Guid.NewGuid().ToString("N")[..Math.Min(length, 32)];
        return $"自定义 Transient 令牌[{type}]: {token}";
    }
}

/// <summary>
/// Transient 服务 - 接口名称 + 描述
/// </summary>
[YServiceTransient("IAdvancedMessageProcessor", "高级消息处理器服务")]
public partial class AdvancedTransientService
{
    /// <summary>
    /// 处理高级消息
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="priority">优先级</param>
    /// <param name="encrypted">是否加密</param>
    /// <returns>处理结果</returns>
    public string ProcessAdvancedMessage(string message, int priority = 1, bool encrypted = false)
    {
        var encryptText = encrypted ? "加密" : "明文";
        return $"高级 Transient 消息处理: {message} (优先级: {priority}, {encryptText})";
    }
}

#endregion
