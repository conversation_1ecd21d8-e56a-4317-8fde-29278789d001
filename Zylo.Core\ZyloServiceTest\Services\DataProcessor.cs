
namespace ZyloServiceTest.Services;

/// <summary>
/// 数据处理器 - 测试方法级属性功能
/// </summary>
/// <remarks>
/// 这个类用于测试方法级属性功能，包括：
/// - 细粒度的方法控制
/// - 混合生命周期
/// - 选择性方法包含
/// - 复杂方法签名处理
/// </remarks>
public partial class DataProcessor
{
    /// <summary>
    /// 处理用户数据 - Scoped 生命周期
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="data">用户数据</param>
    /// <returns>处理结果</returns>
    [YServiceScoped]
    public async Task<string> ProcessUserDataAsync(int userId, string data)
    {
        await Task.Delay(50);
        return $"用户 {userId} 的数据已处理: {data}";
    }

    /// <summary>
    /// 获取系统配置 - Singleton 生命周期
    /// </summary>
    /// <param name="configKey">配置键</param>
    /// <returns>配置值</returns>
    [YServiceSingleton]
    public string GetSystemConfig(string configKey)
    {
        return $"Config[{configKey}] = SystemValue_{configKey}";
    }

    /// <summary>
    /// 生成唯一令牌 - Transient 生命周期
    /// </summary>
    /// <param name="prefix">令牌前缀</param>
    /// <returns>生成的令牌</returns>
    [YServiceTransient]
    public string GenerateUniqueToken(string prefix = "TOKEN")
    {
        return $"{prefix}_{Guid.NewGuid():N}";
    }

    /// <summary>
    /// 复杂的数据转换方法 - Scoped 生命周期
    /// </summary>
    /// <typeparam name="TSource">源数据类型</typeparam>
    /// <typeparam name="TTarget">目标数据类型</typeparam>
    /// <param name="source">源数据</param>
    /// <param name="mapper">映射函数</param>
    /// <param name="options">转换选项</param>
    /// <returns>转换后的数据</returns>
    [YServiceScoped]
    public async Task<TTarget> TransformDataAsync<TSource, TTarget>(
        TSource source,
        Func<TSource, TTarget> mapper,
        Dictionary<string, object>? options = null)
        where TSource : class
        where TTarget : class, new()
    {
        await Task.Delay(20);

        Console.WriteLine($"转换数据: {typeof(TSource).Name} -> {typeof(TTarget).Name}");

        if (options != null)
        {
            Console.WriteLine($"转换选项: {string.Join(", ", options.Select(kv => $"{kv.Key}={kv.Value}"))}");
        }

        return mapper(source);
    }

    /// <summary>
    /// 批量处理数据 - Scoped 生命周期
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    /// <param name="items">数据项集合</param>
    /// <param name="processor">处理函数</param>
    /// <param name="batchSize">批次大小</param>
    /// <returns>处理结果</returns>
    [YServiceScoped]
    public async Task<IEnumerable<string>> ProcessBatchAsync<T>(
        IEnumerable<T> items,
        Func<T, string> processor,
        int batchSize = 10) where T : class
    {
        var results = new List<string>();
        var batch = items.Take(batchSize);

        foreach (var item in batch)
        {
            await Task.Delay(5);
            results.Add(processor(item));
        }

        return results;
    }

    /// <summary>
    /// 普通方法，不会包含在接口中
    /// </summary>
    /// <param name="message">消息</param>
    public void LogMessage(string message)
    {
        Console.WriteLine($"[LOG] {DateTime.Now:HH:mm:ss} - {message}");
    }

    /// <summary>
    /// 被忽略的方法
    /// </summary>
    /// <param name="data">数据</param>
    [YServiceIgnore]
    public void IgnoredMethod(string data)
    {
        Console.WriteLine($"这个方法被忽略了: {data}");
    }


    [YServiceScoped]
    /// <summary>
    /// 内部工具方法
    /// </summary>
    /// <param name="input">输入</param>
    /// <returns>处理结果</returns>
    public static string InternalUtility(string input)
    {
        return $"Internal: {input}";
    }

    /// <summary>
    /// 私有方法，自动排除
    /// </summary>
    private void PrivateHelper()
    {
        Console.WriteLine("私有辅助方法");
    }
}