

namespace ZyloServiceTest.Services;

/// <summary>
/// 静态测试服务 - 验证静态类的包装器生成
/// </summary>
/// <remarks>
/// 这个静态类用于测试 YService 是否能为静态类生成包装器，
/// 使其能够通过依赖注入使用
/// </remarks>
[YService(ServiceLifetime.Singleton)]
public static partial class StaticTestService
{
    public class DuplicateFileInfo
    {
        /// <summary>
        /// 文件内容的SHA256哈希值
        /// </summary>
        /// <value>用于唯一标识文件内容的十六进制哈希字符串</value>
        public string Hash { get; set; } = "";

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        /// <value>重复文件组中每个文件的大小，所有文件大小相同</value>
        public long FileSize { get; set; }

        /// <summary>
        /// 重复文件路径列表
        /// </summary>
        /// <value>包含所有具有相同内容的文件路径</value>
        public List<string> FilePaths { get; set; } = new();

        /// <summary>
        /// 计算浪费的磁盘空间
        /// </summary>
        /// <value>除了保留一个文件外，其他重复文件占用的总空间</value>
        /// <remarks>
        /// 计算公式：(重复文件数量 - 1) × 单个文件大小
        /// 这表示如果删除重复文件，可以节省的磁盘空间
        /// </remarks>
        public long TotalWastedSpace => (FilePaths.Count - 1) * FileSize;
    }
    
    /// <summary>
    /// 计算两个整数的和
    /// </summary>
    /// <param name="a">第一个整数</param>
    /// <param name="b">第二个整数</param>
    /// <returns>两个整数的和</returns>
    public static DuplicateFileInfo Addffff(DuplicateFileInfo a2134)
    {
        return  new DuplicateFileInfo();
    }
    
    /// <summary>
    /// 计算两个整数的和
    /// </summary>
    /// <param name="a">第一个整数</param>
    /// <param name="b">第二个整数</param>
    /// <returns>两个整数的和</returns>
    public static int Add(int a, int b)
    {
        return a + b;
    }

    /// <summary>
    /// 计算两个整数的乘积
    /// </summary>
    /// <param name="a">第一个整数</param>
    /// <param name="b">第二个整数</param>
    /// <returns>两个整数的乘积</returns>
    public static int Multiply(int a, int b)
    {
        return a * b;
    }

    /// <summary>
    /// 格式化字符串模板
    /// </summary>
    /// <param name="template">字符串模板</param>
    /// <param name="args">格式化参数</param>
    /// <returns>格式化后的字符串</returns>
    public static string FormatString(string template, params object[] args)
    {
        return string.Format(template, args);
    }

    /// <summary>
    /// 处理泛型集合 - 测试泛型静态方法
    /// </summary>
    /// <typeparam name="T">集合元素类型</typeparam>
    /// <param name="items">输入集合</param>
    /// <returns>处理后的字符串</returns>
    public static string ProcessItems<T>(IEnumerable<T> items) where T : notnull
    {
        return string.Join(", ", items);
    }

    /// <summary>
    /// 获取当前时间戳 - 无参数方法
    /// </summary>
    /// <returns>当前时间戳</returns>
    public static long GetTimestamp()
    {
        return DateTimeOffset.UtcNow.ToUnixTimeSeconds();
    }

    /// <summary>
    /// 私有静态方法 - 应该被排除
    /// </summary>
    /// <param name="value">输入值</param>
    /// <returns>处理后的值</returns>
    private static string InternalProcess(string value)
    {
        return $"Internal: {value}";
    }

    /// <summary>
    /// 被忽略的方法 - 使用 YServiceIgnore 排除
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>处理结果</returns>
    [YServiceIgnore]
    public static string IgnoredMethod(string data)
    {
        return $"Ignored: {data}";
    }
}
