
namespace ZyloServiceTest.Services;

/// <summary>
/// 用户服务 - 测试类级属性和完整功能
/// </summary>
/// <remarks>
/// 这个服务用于测试 YService 的类级属性功能，包括：
/// - 自动接口生成
/// - XML 文档注释保留
/// - 复杂方法签名处理
/// - 方法排除功能
/// </remarks>
[YServiceScoped]
public partial class UserService
{
    /// <summary>
    /// 根据用户ID获取用户信息
    /// </summary>
    /// <param name="userId">用户的唯一标识符</param>
    /// <param name="includeDeleted">是否包含已删除的用户，默认为 false</param>
    /// <returns>
    /// 返回用户信息，如果用户不存在则返回 null
    /// </returns>
    /// <exception cref="ArgumentException">当 userId 小于等于 0 时抛出</exception>
    public async Task<string?> GetUserAsync(int userId, bool includeDeleted = false)
    {
        if (userId <= 0)
            throw new ArgumentException("用户ID必须大于0", nameof(userId));

        await Task.Delay(50); // 模拟异步操作
        
        if (includeDeleted)
            return $"User {userId} (包含已删除)";
        else
            return $"User {userId}";
    }

    /// <summary>
    /// 创建新用户
    /// </summary>
    /// <param name="name">用户名</param>
    /// <param name="email">邮箱地址</param>
    /// <param name="age">年龄，可选参数</param>
    /// <returns>创建的用户信息</returns>
    public async Task<string> CreateUserAsync(string name, string email, int age = 18)
    {
        if (string.IsNullOrEmpty(name))
            throw new ArgumentException("用户名不能为空", nameof(name));
        
        if (string.IsNullOrEmpty(email))
            throw new ArgumentException("邮箱不能为空", nameof(email));

        await Task.Delay(100); // 模拟数据库操作
        
        return $"用户创建成功: {name} ({email}), 年龄: {age}";
    }

    /// <summary>
    /// 处理泛型数据的方法
    /// </summary>
    /// <typeparam name="T">要处理的数据类型</typeparam>
    /// <param name="data">输入数据</param>
    /// <param name="options">处理选项</param>
    /// <returns>处理后的数据</returns>
    public T ProcessData<T>(T data, string options = "default") where T : class
    {
        Console.WriteLine($"处理 {typeof(T).Name} 类型数据，选项: {options}");
        return data;
    }

    /// <summary>
    /// 复杂泛型方法测试
    /// </summary>
    /// <typeparam name="TInput">输入类型</typeparam>
    /// <typeparam name="TOutput">输出类型</typeparam>
    /// <param name="input">输入数据</param>
    /// <param name="converter">转换函数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>转换后的数据</returns>
    public async Task<TOutput> TransformAsync<TInput, TOutput>(
        TInput input, 
        Func<TInput, TOutput> converter,
        CancellationToken cancellationToken = default)
        where TInput : class
        where TOutput : class, new()
    {
        cancellationToken.ThrowIfCancellationRequested();
        
        await Task.Delay(10, cancellationToken);
        
        return converter(input);
    }

    /// <summary>
    /// 内部辅助方法，不会包含在接口中
    /// </summary>
    [YServiceIgnore]
    public void InternalHelper()
    {
        Console.WriteLine("这是内部辅助方法，不会包含在接口中");
    }

    /// <summary>
    /// 私有方法，自动排除
    /// </summary>
    private void PrivateMethod()
    {
        Console.WriteLine("私有方法，自动排除");
    }

    /// <summary>
    /// 受保护的方法，自动排除
    /// </summary>
    protected void ProtectedMethod()
    {
        Console.WriteLine("受保护的方法，自动排除");
    }
}
