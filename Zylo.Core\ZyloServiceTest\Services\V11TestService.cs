

namespace ZyloServiceTest.Services;

/// <summary>
/// v1.1 功能测试服务 - 测试自定义接口命名和服务描述
/// </summary>
/// <remarks>
/// 这个服务用于测试 YService v1.1 的新功能：
/// - 自定义接口名称
/// - 服务描述
/// - 改进的文件命名
/// - 错误诊断系统
/// </remarks>
[YService(
    ServiceLifetime.Scoped,
    InterfaceName = "ICustomUserManager",
    Description = "自定义用户管理服务")]
public partial class V11TestService
{
    /// <summary>
    /// 获取用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    public async Task<string> GetUserInfoAsync(int userId)
    {
        await Task.Delay(10);
        return $"用户信息: {userId}";
    }

    /// <summary>
    /// 更新用户状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateUserStatusAsync(int userId, string status)
    {
        await Task.Delay(10);
        Console.WriteLine($"更新用户 {userId} 状态为: {status}");
        return true;
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteUserAsync(int userId)
    {
        await Task.Delay(10);
        Console.WriteLine($"删除用户: {userId}");
        return true;
    }
}

/// <summary>
/// 传统命名测试服务 - 使用默认接口前缀
/// </summary>
[YService(ServiceLifetime.Singleton, Description = "传统命名的配置服务")]
public partial class ConfigurationService
{
    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <returns>配置值</returns>
    public string GetConfigValue(string key)
    {
        return $"Config[{key}] = DefaultValue";
    }

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    public void SetConfigValue(string key, string value)
    {
        Console.WriteLine($"设置配置: {key} = {value}");
    }
}

/// <summary>
/// 错误诊断测试类 - 故意包含一些问题来测试诊断系统
/// </summary>
/// <remarks>
/// 这个类用于测试 v1.1 的错误诊断功能：
/// - 非 partial 类错误
/// - 静态方法属性错误
/// - 参数过多警告
/// </remarks>
[YService(ServiceLifetime.Transient)]
public partial class DiagnosticTestService
{
    /// <summary>
    /// 正常的方法
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>处理结果</returns>
    public string ProcessData(string data)
    {
        return $"处理: {data}";
    }

    /// <summary>
    /// 参数过多的方法（用于测试警告）
    /// </summary>
    /// <param name="p1">参数1</param>
    /// <param name="p2">参数2</param>
    /// <param name="p3">参数3</param>
    /// <param name="p4">参数4</param>
    /// <param name="p5">参数5</param>
    /// <param name="p6">参数6</param>
    /// <param name="p7">参数7</param>
    /// <param name="p8">参数8</param>
    /// <param name="p9">参数9</param>
    /// <param name="p10">参数10</param>
    /// <returns>处理结果</returns>
    public string MethodWithManyParameters(
        string p1, string p2, string p3, string p4, string p5,
        string p6, string p7, string p8, string p9, string p10)
    {
        return $"处理了 10 个参数: {p1}, {p2}, {p3}, {p4}, {p5}, {p6}, {p7}, {p8}, {p9}, {p10}";
    }

    /// <summary>
    /// 复杂泛型约束的方法（用于测试警告）
    /// </summary>
    /// <typeparam name="T1">类型1</typeparam>
    /// <typeparam name="T2">类型2</typeparam>
    /// <typeparam name="T3">类型3</typeparam>
    /// <param name="item1">项目1</param>
    /// <param name="item2">项目2</param>
    /// <param name="item3">项目3</param>
    /// <returns>处理结果</returns>
    public string ComplexGenericMethod<T1, T2, T3>(T1 item1, T2 item2, T3 item3)
        where T1 : class, IComparable<T1>, IEquatable<T1>
        where T2 : struct, IComparable<T2>
        where T3 : class, IDisposable, IFormattable, new()
    {
        return $"处理复杂泛型: {typeof(T1).Name}, {typeof(T2).Name}, {typeof(T3).Name}";
    }
}
