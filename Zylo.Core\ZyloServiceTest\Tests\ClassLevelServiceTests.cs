using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 类级属性服务测试
/// </summary>
public static class ClassLevelServiceTests
{
    public static async Task RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n📋 测试类级属性服务 (UserService)");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🔧 获取服务实例
            var userService = services.GetRequiredService<IUserService>();
            Console.WriteLine("✅ UserService 服务解析成功");

            // 🧪 测试基础异步方法
            await TestBasicAsyncMethods(userService);

            // 🧪 测试复杂方法签名
            await TestComplexMethodSignatures(userService);

            // 🧪 测试泛型方法
            TestGenericMethods(userService);

            // 🧪 测试错误处理
            await TestErrorHandling(userService);

            Console.WriteLine("✅ 类级属性服务测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 类级属性服务测试失败: {ex.Message}");
        }
    }

    private static async Task TestBasicAsyncMethods(IUserService userService)
    {
        Console.WriteLine("\n🧪 测试基础异步方法:");

        // 测试 GetUserAsync
        var user1 = await userService.GetUserAsync(123);
        Console.WriteLine($"  GetUserAsync(123): {user1}");

        var user2 = await userService.GetUserAsync(456, true);
        Console.WriteLine($"  GetUserAsync(456, true): {user2}");

        // 测试 CreateUserAsync
        var createResult1 = await userService.CreateUserAsync("张三", "<EMAIL>");
        Console.WriteLine($"  CreateUserAsync(默认年龄): {createResult1}");

        var createResult2 = await userService.CreateUserAsync("李四", "<EMAIL>", 25);
        Console.WriteLine($"  CreateUserAsync(指定年龄): {createResult2}");
    }

    private static async Task TestComplexMethodSignatures(IUserService userService)
    {
        Console.WriteLine("\n🔧 测试复杂方法签名:");

        // 测试 TransformAsync
        var input = "测试输入数据";
        var result = await userService.TransformAsync<string, TestData>(
            input,
            s => new TestData { Value = s, Timestamp = DateTime.Now });

        Console.WriteLine($"  TransformAsync: {result.Value} (时间: {result.Timestamp:HH:mm:ss})");

        // 测试带取消令牌的方法
        using var cts = new CancellationTokenSource();
        var result2 = await userService.TransformAsync<string, TestData>(
            "另一个测试",
            s => new TestData { Value = $"转换: {s}", Timestamp = DateTime.Now },
            cts.Token);

        Console.WriteLine($"  TransformAsync(带取消令牌): {result2.Value}");
    }

    private static void TestGenericMethods(IUserService userService)
    {
        Console.WriteLine("\n🔧 测试泛型方法:");

        // 测试 ProcessData
        var testData = new TestData { Value = "泛型测试数据", Timestamp = DateTime.Now };
        var processedData = userService.ProcessData(testData, "自定义选项");
        Console.WriteLine($"  ProcessData: {processedData.Value}");

        // 测试默认参数
        var processedData2 = userService.ProcessData(testData);
        Console.WriteLine($"  ProcessData(默认选项): {processedData2.Value}");
    }

    private static async Task TestErrorHandling(IUserService userService)
    {
        Console.WriteLine("\n🔧 测试错误处理:");

        try
        {
            await userService.GetUserAsync(-1);
            Console.WriteLine("❌ 应该抛出异常但没有抛出");
        }
        catch (ArgumentException ex)
        {
            Console.WriteLine($"  ✅ 正确捕获异常: {ex.Message}");
        }

        try
        {
            await userService.CreateUserAsync("", "<EMAIL>");
            Console.WriteLine("❌ 应该抛出异常但没有抛出");
        }
        catch (ArgumentException ex)
        {
            Console.WriteLine($"  ✅ 正确捕获异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试数据类
    /// </summary>
    public class TestData
    {
        public string Value { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
