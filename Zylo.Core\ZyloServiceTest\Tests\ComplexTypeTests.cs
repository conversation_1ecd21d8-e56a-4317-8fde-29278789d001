using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 复杂类型测试
/// </summary>
public static class ComplexTypeTests
{
    public static async Task RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试复杂类型支持");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🔧 获取服务实例
            var complexService = services.GetRequiredService<IComplexTypeTestService>();
            Console.WriteLine("✅ ComplexTypeTestService 服务解析成功");

            // 🧪 测试泛型方法
            await TestGenericMethods(complexService);

            // 🧪 测试委托参数
            TestDelegateMethods(complexService);

            // 🧪 测试复杂类型参数
            TestComplexTypeParameters(complexService);

            // 🧪 测试异步方法
            await TestAsyncMethods(complexService);

            // 🧪 测试接口约束
            TestInterfaceConstraints(complexService);

            Console.WriteLine("✅ 复杂类型测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 复杂类型测试失败: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
    }

    private static async Task TestGenericMethods(IComplexTypeTestService service)
    {
        Console.WriteLine("\n🔧 测试泛型方法:");

        // 测试基础泛型方法
        var testData = new TestComplexData
        {
            Name = "测试数据",
            Value = 100,
            Timestamp = DateTime.Now
        };

        var processedData = service.ProcessGeneric(testData);
        Console.WriteLine($"  ProcessGeneric: {processedData.Name}");

        // 测试多泛型参数方法
        var transformed = service.Transform<TestComplexData, TestComplexData>(
            testData,
            data => new TestComplexData
            {
                Name = $"转换: {data.Name}",
                Value = data.Value * 2,
                Timestamp = DateTime.Now
            });
        Console.WriteLine($"  Transform: {transformed.Name} - {transformed.Value}");

        // 测试复杂泛型约束
        var numbers = new[] { 1, 5, 3, 9, 2 };
        var max = service.FindMax(numbers);
        Console.WriteLine($"  FindMax: {max}");
    }

    private static void TestDelegateMethods(IComplexTypeTestService service)
    {
        Console.WriteLine("\n🔧 测试委托参数:");

        // 测试 Action 委托
        service.ExecuteAction(msg => Console.WriteLine($"    Action执行: {msg}"), "测试消息");

        // 测试 Func 委托
        var funcResult = service.ProcessWithFunc<int, string>(x => $"数字: {x}", 42);
        Console.WriteLine($"  ProcessWithFunc: {funcResult}");

        // 测试 Predicate 委托
        var items = new[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };
        var evenNumbers = service.FilterItems(items, x => x % 2 == 0);
        Console.WriteLine($"  FilterItems (偶数): {string.Join(", ", evenNumbers)}");

        // 测试自定义委托
        CustomProcessor processor = (data, timestamp) => $"[{timestamp:HH:mm:ss}] 处理: {data}";
        var customResult = service.ProcessWithCustomDelegate(processor, "自定义数据");
        Console.WriteLine($"  CustomDelegate: {customResult}");
    }

    private static void TestComplexTypeParameters(IComplexTypeTestService service)
    {
        Console.WriteLine("\n🔧 测试复杂类型参数:");

        // 测试字典参数
        var dictionary = new Dictionary<string, object>
        {
            ["name"] = "张三",
            ["age"] = 25,
            ["active"] = true
        };
        var dictValue = service.GetFromDictionary(dictionary, "name");
        Console.WriteLine($"  Dictionary: {dictValue}");

        // 测试元组
        var inputTuple = ("李四", 30);
        var outputTuple = service.ProcessTuple(inputTuple);
        Console.WriteLine($"  Tuple: {outputTuple.Name}, {outputTuple.Age}, {outputTuple.IsActive}");

        // 测试可空类型
        var nullableResult1 = service.ProcessNullable("有值");
        var nullableResult2 = service.ProcessNullable(null);
        Console.WriteLine($"  Nullable: '{nullableResult1}', '{nullableResult2}'");

        // 测试集合组合
        var array = new[] { "A", "B" };
        var list = new List<string> { "C", "D" };
        var set = new HashSet<string> { "E", "F", "A" }; // A重复
        var combined = service.CombineCollections(array, list, set);
        Console.WriteLine($"  Collections: {string.Join(", ", combined)}");

        // 测试嵌套泛型
        var nestedData = new Dictionary<string, List<int>>
        {
            ["group1"] = new List<int> { 1, 2, 3 },
            ["group2"] = new List<int> { 4, 5, 6 }
        };
        var processedNested = service.ProcessNestedGeneric(nestedData);
        Console.WriteLine($"  NestedGeneric: {string.Join(", ", processedNested.Keys)}");
    }

    private static async Task TestAsyncMethods(IComplexTypeTestService service)
    {
        Console.WriteLine("\n🔧 测试异步方法:");

        // 测试异步泛型方法
        var asyncResult = await service.ProcessAsync<string>(
            async data =>
            {
                await Task.Delay(10);
                return $"异步处理: {data}";
            },
            "测试数据");
        Console.WriteLine($"  ProcessAsync: {asyncResult}");

        // 测试异步委托
        await service.ExecuteAsync(
            async msg =>
            {
                await Task.Delay(10);
                Console.WriteLine($"    异步Action: {msg}");
            },
            "异步消息");
    }

    private static void TestInterfaceConstraints(IComplexTypeTestService service)
    {
        Console.WriteLine("\n🔧 测试接口约束:");

        // 测试接口参数
        var numbers = new[] { 3, 1, 4, 1, 5, 9, 2, 6 };
        var sorted = service.SortItems(numbers);
        Console.WriteLine($"  SortItems: {string.Join(", ", sorted)}");

        // 测试自定义比较器
        var customSorted = service.SortItems(numbers, Comparer<int>.Create((x, y) => y.CompareTo(x)));
        Console.WriteLine($"  SortItems (降序): {string.Join(", ", customSorted)}");

        // 测试多接口约束
        var multiResult = service.ProcessMultiInterface(42);
        Console.WriteLine($"  MultiInterface: {multiResult}");
    }
}
