using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 便捷属性参数测试
/// </summary>
public static class ConvenienceAttributeTests
{
    public static async Task RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试便捷属性参数支持");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🧪 测试 YServiceScoped 参数
            await TestYServiceScopedParameters(services);

            // 🧪 测试 YServiceSingleton 参数
            TestYServiceSingletonParameters(services);

            // 🧪 测试 YServiceTransient 参数
            TestYServiceTransientParameters(services);

            // 🧪 验证接口命名
            VerifyInterfaceNaming();

            Console.WriteLine("✅ 便捷属性参数测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 便捷属性参数测试失败: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
    }

    private static async Task TestYServiceScopedParameters(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试 YServiceScoped 参数:");

        // 测试默认 Scoped 服务
        var defaultScoped = services.GetRequiredService<IDefaultScopedService>();
        Console.WriteLine("✅ 默认 Scoped 服务解析成功");
        var defaultResult = defaultScoped.ProcessDefault("测试数据");
        Console.WriteLine($"  ProcessDefault: {defaultResult}");

        // 测试自定义接口名称的 Scoped 服务
        var customScoped = services.GetRequiredService<ICustomScopedProcessor>();
        Console.WriteLine("✅ 自定义接口名称 ICustomScopedProcessor 解析成功");
        var customResult = customScoped.ProcessCustom("自定义数据");
        Console.WriteLine($"  ProcessCustom: {customResult}");

        // 测试带描述的 Scoped 服务
        var advancedScoped = services.GetRequiredService<IAdvancedScopedManager>();
        Console.WriteLine("✅ 高级 Scoped 服务 IAdvancedScopedManager 解析成功");
        var advancedResult = advancedScoped.ProcessAdvanced("高级数据", "高性能");
        Console.WriteLine($"  ProcessAdvanced: {advancedResult}");

        // 验证接口类型
        Console.WriteLine($"  默认接口: {typeof(IDefaultScopedService).Name}");
        Console.WriteLine($"  自定义接口: {typeof(ICustomScopedProcessor).Name}");
        Console.WriteLine($"  高级接口: {typeof(IAdvancedScopedManager).Name}");
    }

    private static void TestYServiceSingletonParameters(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试 YServiceSingleton 参数:");

        // 测试默认 Singleton 服务
        var defaultSingleton = services.GetRequiredService<IDefaultSingletonService>();
        Console.WriteLine("✅ 默认 Singleton 服务解析成功");
        var defaultConfig = defaultSingleton.GetGlobalConfig("database_url");
        Console.WriteLine($"  GetGlobalConfig: {defaultConfig}");

        // 测试自定义接口名称的 Singleton 服务
        var customSingleton = services.GetRequiredService<ICustomCacheManager>();
        Console.WriteLine("✅ 自定义接口名称 ICustomCacheManager 解析成功");
        var cacheResult = customSingleton.CacheData("user_123", "张三");
        Console.WriteLine($"  CacheData: {cacheResult}");

        // 测试带描述的 Singleton 服务
        var advancedSingleton = services.GetRequiredService<IGlobalStateController>();
        Console.WriteLine("✅ 高级 Singleton 服务 IGlobalStateController 解析成功");
        var stateResult = advancedSingleton.ManageGlobalState("ACTIVE", true);
        Console.WriteLine($"  ManageGlobalState: {stateResult}");

        // 验证接口类型
        Console.WriteLine($"  默认接口: {typeof(IDefaultSingletonService).Name}");
        Console.WriteLine($"  自定义接口: {typeof(ICustomCacheManager).Name}");
        Console.WriteLine($"  高级接口: {typeof(IGlobalStateController).Name}");
    }

    private static void TestYServiceTransientParameters(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试 YServiceTransient 参数:");

        // 测试默认 Transient 服务
        var defaultTransient = services.GetRequiredService<IDefaultTransientService>();
        Console.WriteLine("✅ 默认 Transient 服务解析成功");
        var defaultData = defaultTransient.GenerateTemporaryData("TEST");
        Console.WriteLine($"  GenerateTemporaryData: {defaultData}");

        // 测试自定义接口名称的 Transient 服务
        var customTransient = services.GetRequiredService<ICustomTokenGenerator>();
        Console.WriteLine("✅ 自定义接口名称 ICustomTokenGenerator 解析成功");
        var customToken = customTransient.GenerateCustomToken("ACCESS", 16);
        Console.WriteLine($"  GenerateCustomToken: {customToken}");

        // 测试带描述的 Transient 服务
        var advancedTransient = services.GetRequiredService<IAdvancedMessageProcessor>();
        Console.WriteLine("✅ 高级 Transient 服务 IAdvancedMessageProcessor 解析成功");
        var messageResult = advancedTransient.ProcessAdvancedMessage("重要消息", 5, true);
        Console.WriteLine($"  ProcessAdvancedMessage: {messageResult}");

        // 验证接口类型
        Console.WriteLine($"  默认接口: {typeof(IDefaultTransientService).Name}");
        Console.WriteLine($"  自定义接口: {typeof(ICustomTokenGenerator).Name}");
        Console.WriteLine($"  高级接口: {typeof(IAdvancedMessageProcessor).Name}");
    }

    private static void VerifyInterfaceNaming()
    {
        Console.WriteLine("\n🔧 验证接口命名规则:");

        // 验证默认命名（I + 类名）
        var defaultInterfaces = new[]
        {
            typeof(IDefaultScopedService),
            typeof(IDefaultSingletonService),
            typeof(IDefaultTransientService)
        };

        Console.WriteLine("  默认命名规则 (I + 类名):");
        foreach (var interfaceType in defaultInterfaces)
        {
            Console.WriteLine($"    ✅ {interfaceType.Name}");
        }

        // 验证自定义命名
        var customInterfaces = new[]
        {
            typeof(ICustomScopedProcessor),
            typeof(ICustomCacheManager),
            typeof(ICustomTokenGenerator)
        };

        Console.WriteLine("  自定义命名规则:");
        foreach (var interfaceType in customInterfaces)
        {
            Console.WriteLine($"    ✅ {interfaceType.Name}");
        }

        // 验证高级命名（带描述）
        var advancedInterfaces = new[]
        {
            typeof(IAdvancedScopedManager),
            typeof(IGlobalStateController),
            typeof(IAdvancedMessageProcessor)
        };

        Console.WriteLine("  高级命名规则 (带描述):");
        foreach (var interfaceType in advancedInterfaces)
        {
            Console.WriteLine($"    ✅ {interfaceType.Name}");
        }

        Console.WriteLine("✅ 所有接口命名规则验证通过");
    }

    /// <summary>
    /// 测试便捷属性的生命周期验证
    /// </summary>
    public static void TestLifetimeVerification(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 验证便捷属性生命周期:");

        // 验证 Scoped 生命周期
        using (var scope1 = services.CreateScope())
        using (var scope2 = services.CreateScope())
        {
            var scoped1_1 = scope1.ServiceProvider.GetRequiredService<IDefaultScopedService>();
            var scoped1_2 = scope1.ServiceProvider.GetRequiredService<IDefaultScopedService>();
            var scoped2_1 = scope2.ServiceProvider.GetRequiredService<IDefaultScopedService>();

            Console.WriteLine($"  Scoped 同作用域相同实例: {ReferenceEquals(scoped1_1, scoped1_2)}");
            Console.WriteLine($"  Scoped 不同作用域不同实例: {!ReferenceEquals(scoped1_1, scoped2_1)}");
        }

        // 验证 Singleton 生命周期
        var singleton1 = services.GetRequiredService<IDefaultSingletonService>();
        var singleton2 = services.GetRequiredService<IDefaultSingletonService>();
        Console.WriteLine($"  Singleton 全局相同实例: {ReferenceEquals(singleton1, singleton2)}");

        // 验证 Transient 生命周期
        var transient1 = services.GetRequiredService<IDefaultTransientService>();
        var transient2 = services.GetRequiredService<IDefaultTransientService>();
        Console.WriteLine($"  Transient 每次不同实例: {!ReferenceEquals(transient1, transient2)}");

        Console.WriteLine("✅ 生命周期验证完成");
    }
}
