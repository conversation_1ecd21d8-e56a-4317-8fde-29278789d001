using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 文档注释测试
/// </summary>
public static class DocumentationTests
{
    public static void RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n📝 测试文档注释保留");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🧪 验证接口文档注释
            VerifyInterfaceDocumentation();

            // 🧪 验证方法文档注释
            VerifyMethodDocumentation();

            // 🧪 验证参数文档注释
            VerifyParameterDocumentation();

            Console.WriteLine("✅ 文档注释测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 文档注释测试失败: {ex.Message}");
        }
    }

    private static void VerifyInterfaceDocumentation()
    {
        Console.WriteLine("\n🔧 验证接口文档注释:");

        // 检查生成的接口是否存在
        var interfaces = new[]
        {
            typeof(IUserService),
            typeof(IDataProcessor)
        };

        foreach (var interfaceType in interfaces)
        {
            Console.WriteLine($"  {interfaceType.Name}: ✅ 接口已生成");

            // 验证接口有方法
            var methods = interfaceType.GetMethods();
            Console.WriteLine($"    方法数量: {methods.Length}");
        }
    }

    private static void VerifyMethodDocumentation()
    {
        Console.WriteLine("\n🔧 验证方法文档注释:");

        // 检查 IUserService 的方法
        var userServiceMethods = typeof(IUserService).GetMethods();
        var expectedUserMethods = new[]
        {
            "GetUserAsync",
            "CreateUserAsync",
            "ProcessData",
            "TransformAsync"
        };

        foreach (var expectedMethod in expectedUserMethods)
        {
            var method = userServiceMethods.FirstOrDefault(m => m.Name == expectedMethod);
            if (method != null)
            {
                Console.WriteLine($"  IUserService.{expectedMethod}: ✅ 存在");
                VerifyMethodSignature(method);
            }
            else
            {
                Console.WriteLine($"  IUserService.{expectedMethod}: ❌ 缺失");
            }
        }

        // 检查 IDataProcessor 的方法
        var dataProcessorMethods = typeof(IDataProcessor).GetMethods();
        var expectedDataMethods = new[]
        {
            "ProcessUserDataAsync",
            "GetSystemConfig",
            "GenerateUniqueToken",
            "TransformDataAsync",
            "ProcessBatchAsync"
        };

        foreach (var expectedMethod in expectedDataMethods)
        {
            var method = dataProcessorMethods.FirstOrDefault(m => m.Name == expectedMethod);
            if (method != null)
            {
                Console.WriteLine($"  IDataProcessor.{expectedMethod}: ✅ 存在");
            }
            else
            {
                Console.WriteLine($"  IDataProcessor.{expectedMethod}: ❌ 缺失");
            }
        }
    }

    private static void VerifyParameterDocumentation()
    {
        Console.WriteLine("\n🔧 验证参数文档注释:");

        // 检查复杂方法的参数
        var getUserMethod = typeof(IUserService).GetMethod("GetUserAsync");
        if (getUserMethod != null)
        {
            var parameters = getUserMethod.GetParameters();
            Console.WriteLine($"  GetUserAsync 参数数量: {parameters.Length}");

            foreach (var param in parameters)
            {
                Console.WriteLine($"    {param.Name}: {param.ParameterType.Name}");
                if (param.HasDefaultValue)
                {
                    Console.WriteLine($"      默认值: {param.DefaultValue}");
                }
            }
        }

        // 检查泛型方法
        var transformMethods = typeof(IUserService).GetMethods()
            .Where(m => m.Name == "TransformAsync" && m.IsGenericMethodDefinition);

        foreach (var method in transformMethods)
        {
            Console.WriteLine($"  TransformAsync 泛型参数数量: {method.GetGenericArguments().Length}");

            var constraints = method.GetGenericArguments()
                .Select(t => t.GetGenericParameterConstraints())
                .ToArray();

            Console.WriteLine($"    泛型约束数量: {constraints.Sum(c => c.Length)}");
        }
    }

    private static void VerifyMethodSignature(MethodInfo method)
    {
        // 验证方法签名的完整性
        var returnType = method.ReturnType;
        var parameters = method.GetParameters();
        var isGeneric = method.IsGenericMethodDefinition;

        Console.WriteLine($"    返回类型: {GetFriendlyTypeName(returnType)}");
        Console.WriteLine($"    参数数量: {parameters.Length}");
        Console.WriteLine($"    是否泛型: {isGeneric}");

        if (isGeneric)
        {
            var genericArgs = method.GetGenericArguments();
            Console.WriteLine($"    泛型参数数量: {genericArgs.Length}");
        }
    }

    private static string GetFriendlyTypeName(Type type)
    {
        if (type.IsGenericType)
        {
            var genericTypeName = type.Name.Split('`')[0];
            var genericArgs = type.GetGenericArguments();
            var argNames = string.Join(", ", genericArgs.Select(GetFriendlyTypeName));
            return $"{genericTypeName}<{argNames}>";
        }

        return type.Name switch
        {
            "String" => "string",
            "Int32" => "int",
            "Boolean" => "bool",
            "Double" => "double",
            "Decimal" => "decimal",
            _ => type.Name
        };
    }
}
