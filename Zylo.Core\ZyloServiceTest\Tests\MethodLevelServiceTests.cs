using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 方法级属性服务测试
/// </summary>
public static class MethodLevelServiceTests
{
    public static async Task RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试方法级属性服务 (DataProcessor)");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🔧 获取服务实例
            var dataProcessor = services.GetRequiredService<IDataProcessor>();
            Console.WriteLine("✅ DataProcessor 服务解析成功");

            // 🧪 测试不同生命周期的方法
            await TestDifferentLifetimeMethods(dataProcessor);

            // 🧪 测试复杂泛型方法
            await TestComplexGenericMethods(dataProcessor);

            // 🧪 测试批量处理
            await TestBatchProcessing(dataProcessor);

            // 🧪 验证方法选择性包含
            VerifySelectiveMethodInclusion(dataProcessor);

            Console.WriteLine("✅ 方法级属性服务测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 方法级属性服务测试失败: {ex.Message}");
        }
    }

    private static async Task TestDifferentLifetimeMethods(IDataProcessor dataProcessor)
    {
        Console.WriteLine("\n🔧 测试不同生命周期的方法:");

        // 测试 Scoped 方法
        var processResult = await dataProcessor.ProcessUserDataAsync(123, "用户数据测试");
        Console.WriteLine($"  ProcessUserDataAsync (Scoped): {processResult}");

        // 测试 Singleton 方法
        var configResult = dataProcessor.GetSystemConfig("database_url");
        Console.WriteLine($"  GetSystemConfig (Singleton): {configResult}");

        // 测试 Transient 方法
        var token1 = dataProcessor.GenerateUniqueToken();
        var token2 = dataProcessor.GenerateUniqueToken("CUSTOM");
        Console.WriteLine($"  GenerateUniqueToken (Transient): {token1}");
        Console.WriteLine($"  GenerateUniqueToken (Transient, 自定义前缀): {token2}");

        // 🚀 v1.2新增：测试静态方法
        var utilityResult = dataProcessor.InternalUtility("测试静态方法");
        Console.WriteLine($"  InternalUtility (静态方法): {utilityResult}");
    }

    private static async Task TestComplexGenericMethods(IDataProcessor dataProcessor)
    {
        Console.WriteLine("\n🔧 测试复杂泛型方法:");

        // 测试 TransformDataAsync
        var sourceData = new SourceData { Id = 1, Name = "测试数据", Value = 100.5 };
        var options = new Dictionary<string, object>
        {
            ["precision"] = 2,
            ["format"] = "currency"
        };

        var transformResult = await dataProcessor.TransformDataAsync<SourceData, TargetData>(
            sourceData,
            src => new TargetData
            {
                Identifier = src.Id.ToString(),
                DisplayName = $"转换: {src.Name}",
                Amount = src.Value
            },
            options);

        Console.WriteLine($"  TransformDataAsync: {transformResult.DisplayName} (金额: {transformResult.Amount})");

        // 测试不带选项的转换
        var simpleResult = await dataProcessor.TransformDataAsync<SourceData, TargetData>(
            sourceData,
            src => new TargetData
            {
                Identifier = src.Id.ToString(),
                DisplayName = src.Name,
                Amount = src.Value
            });

        Console.WriteLine($"  TransformDataAsync (无选项): {simpleResult.DisplayName}");
    }

    private static async Task TestBatchProcessing(IDataProcessor dataProcessor)
    {
        Console.WriteLine("\n🔧 测试批量处理:");

        var items = new[] { "项目1", "项目2", "项目3", "项目4", "项目5" };

        var batchResults = await dataProcessor.ProcessBatchAsync(
            items,
            item => $"已处理: {item}",
            3); // 批次大小为3

        Console.WriteLine($"  ProcessBatchAsync: {string.Join(", ", batchResults)}");

        // 测试字符串批量处理
        var strings = new[] { "项目A", "项目B", "项目C", "项目D", "项目E" };
        var stringResults = await dataProcessor.ProcessBatchAsync(
            strings,
            str => $"字符串{str}",
            5);

        Console.WriteLine($"  ProcessBatchAsync (字符串): {string.Join(", ", stringResults)}");
    }

    private static void VerifySelectiveMethodInclusion(IDataProcessor dataProcessor)
    {
        Console.WriteLine("\n🔧 验证方法选择性包含:");

        // 验证接口中只包含标记了属性的方法
        var interfaceType = typeof(IDataProcessor);
        var methods = interfaceType.GetMethods();

        Console.WriteLine($"  接口中的方法数量: {methods.Length}");

        var expectedMethods = new[]
        {
            "ProcessUserDataAsync",
            "GetSystemConfig",
            "GenerateUniqueToken",
            "TransformDataAsync",
            "ProcessBatchAsync",
            "InternalUtility" // v1.2新增：静态方法也包含在接口中
        };

        foreach (var expectedMethod in expectedMethods)
        {
            var hasMethod = methods.Any(m => m.Name == expectedMethod);
            Console.WriteLine($"  {expectedMethod}: {(hasMethod ? "✅ 存在" : "❌ 缺失")}");
        }

        // 验证普通方法不在接口中
        var excludedMethods = new[] { "LogMessage", "IgnoredMethod" }; // v1.2更新：InternalUtility现在包含在接口中
        foreach (var excludedMethod in excludedMethods)
        {
            var hasMethod = methods.Any(m => m.Name == excludedMethod);
            Console.WriteLine($"  {excludedMethod}: {(hasMethod ? "❌ 不应存在" : "✅ 正确排除")}");
        }
    }

    /// <summary>
    /// 源数据类
    /// </summary>
    public class SourceData
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
    }

    /// <summary>
    /// 目标数据类
    /// </summary>
    public class TargetData
    {
        public string Identifier { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public double Amount { get; set; }
    }
}
