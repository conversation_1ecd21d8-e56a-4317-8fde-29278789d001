using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// 静态类服务测试
/// </summary>
public static class StaticServiceTests
{
    /// <summary>
    /// 运行静态类服务测试
    /// </summary>
    /// <param name="services">服务提供者</param>
    public static void RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n🏗️ 测试静态类包装器 (StaticTestService)");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🔧 获取静态类包装器服务
            var staticService = services.GetRequiredService<IStaticTestService>();
            
           
            Console.WriteLine("✅ StaticTestService 包装器解析成功");

            // 🧪 测试基础数学运算
            TestBasicMath(staticService);

            // 🧪 测试字符串格式化
            TestStringFormatting(staticService);

            // 🧪 测试泛型方法
            TestGenericMethods(staticService);

            // 🧪 测试无参数方法
            TestParameterlessMethods(staticService);

            Console.WriteLine("✅ 静态类包装器测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 静态类包装器测试失败: {ex.Message}");
        }
    }

    private static void TestBasicMath(IStaticTestService staticService)
    {
        Console.WriteLine("\n🧮 测试基础数学运算:");

        // 测试加法
        var sum = staticService.Add(10, 20);
        Console.WriteLine($"  Add(10, 20): {sum}");

        // 测试乘法
        var product = staticService.Multiply(5, 6);
        Console.WriteLine($"  Multiply(5, 6): {product}");
    }

    private static void TestStringFormatting(IStaticTestService staticService)
    {
        Console.WriteLine("\n📝 测试字符串格式化:");

        // 测试字符串格式化
        var formatted = staticService.FormatString("Hello {0}, you have {1} messages", new object[] { "Alice", 5 });
        Console.WriteLine($"  FormatString: {formatted}");
    }

    private static void TestGenericMethods(IStaticTestService staticService)
    {
        Console.WriteLine("\n🔧 测试泛型方法:");

        // 测试泛型方法 - 整数集合
        var intItems = staticService.ProcessItems(new[] { 1, 2, 3, 4, 5 });
        Console.WriteLine($"  ProcessItems<int>: {intItems}");

        // 测试泛型方法 - 字符串集合
        var stringItems = staticService.ProcessItems(new[] { "apple", "banana", "cherry" });
        Console.WriteLine($"  ProcessItems<string>: {stringItems}");
    }

    private static void TestParameterlessMethods(IStaticTestService staticService)
    {
        Console.WriteLine("\n⏰ 测试无参数方法:");

        // 测试无参数方法
        var timestamp = staticService.GetTimestamp();
        Console.WriteLine($"  GetTimestamp(): {timestamp}");
    }

    /// <summary>
    /// 验证静态包装器的生成
    /// </summary>
    /// <param name="services">服务提供者</param>
    public static void VerifyWrapperGeneration(IServiceProvider services)
    {
        Console.WriteLine("\n🔍 验证静态包装器生成:");

        try
        {
            // 验证接口是否存在
            var interfaceType = typeof(IStaticTestService);
            Console.WriteLine($"  ✅ 接口 {interfaceType.Name} 存在");

            // 验证包装器类是否注册
            var service = services.GetService<IStaticTestService>();
            if (service != null)
            {
                Console.WriteLine($"  ✅ 包装器服务注册成功，类型: {service.GetType().Name}");

                // 验证包装器是否正确调用静态方法
                var testResult = service.Add(1, 1);
                if (testResult == 2)
                {
                    Console.WriteLine("  ✅ 包装器正确调用静态方法");
                }
                else
                {
                    Console.WriteLine("  ❌ 包装器调用静态方法失败");
                }
            }
            else
            {
                Console.WriteLine("  ❌ 包装器服务未注册");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 验证失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 测试静态包装器的生命周期
    /// </summary>
    /// <param name="services">服务提供者</param>
    public static void TestWrapperLifetime(IServiceProvider services)
    {
        Console.WriteLine("\n🔄 测试静态包装器生命周期:");

        try
        {
            // 获取两次服务实例
            var service1 = services.GetRequiredService<IStaticTestService>();
            var service2 = services.GetRequiredService<IStaticTestService>();

            // 验证是否为同一实例（Singleton）
            if (ReferenceEquals(service1, service2))
            {
                Console.WriteLine("  ✅ 静态包装器使用 Singleton 生命周期（同一实例）");
            }
            else
            {
                Console.WriteLine("  ❌ 静态包装器生命周期不正确（不同实例）");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"  ❌ 生命周期测试失败: {ex.Message}");
        }
    }
}
