using Microsoft.Extensions.DependencyInjection;
using ZyloServiceTest.Services;

namespace ZyloServiceTest.Tests;

/// <summary>
/// v1.1 功能测试
/// </summary>
public static class V11FeatureTests
{
    public static async Task RunTests(IServiceProvider services)
    {
        Console.WriteLine("\n🆕 测试 v1.1 新功能");
        Console.WriteLine(new string('-', 40));

        try
        {
            // 🧪 测试自定义接口命名
            await TestCustomInterfaceNaming(services);

            // 🧪 测试服务描述功能
            TestServiceDescription(services);

            // 🧪 测试文件命名改进
            TestFileNamingImprovements();

            // 🧪 测试向后兼容性
            await TestBackwardCompatibility(services);

            Console.WriteLine("✅ v1.1 功能测试完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ v1.1 功能测试失败: {ex.Message}");
            Console.WriteLine($"详细信息: {ex}");
        }
    }

    private static async Task TestCustomInterfaceNaming(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试自定义接口命名:");

        // 测试自定义接口名称
        var customService = services.GetRequiredService<ICustomUserManager>();
        Console.WriteLine("✅ 自定义接口名称 ICustomUserManager 解析成功");

        // 测试功能
        var userInfo = await customService.GetUserInfoAsync(123);
        Console.WriteLine($"  GetUserInfoAsync: {userInfo}");

        var updateResult = await customService.UpdateUserStatusAsync(456, "Active");
        Console.WriteLine($"  UpdateUserStatusAsync: {updateResult}");

        var deleteResult = await customService.DeleteUserAsync(789);
        Console.WriteLine($"  DeleteUserAsync: {deleteResult}");

        // 验证接口类型
        var interfaceType = typeof(ICustomUserManager);
        Console.WriteLine($"  接口类型: {interfaceType.Name}");
        Console.WriteLine($"  方法数量: {interfaceType.GetMethods().Length}");
    }

    private static void TestServiceDescription(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试服务描述功能:");

        // 测试带描述的服务
        var configService = services.GetRequiredService<IConfigurationService>();
        Console.WriteLine("✅ 带描述的 ConfigurationService 解析成功");

        // 测试功能
        var configValue = configService.GetConfigValue("database_url");
        Console.WriteLine($"  GetConfigValue: {configValue}");

        configService.SetConfigValue("timeout", "30");
        Console.WriteLine("  SetConfigValue: 执行成功");

        // 验证接口类型
        var interfaceType = typeof(IConfigurationService);
        Console.WriteLine($"  接口类型: {interfaceType.Name}");
        Console.WriteLine($"  使用传统命名: I + 类名");
    }

    private static void TestFileNamingImprovements()
    {
        Console.WriteLine("\n🔧 测试文件命名改进:");

        // 检查生成的文件是否使用新的命名格式
        Console.WriteLine("  新的文件命名格式:");
        Console.WriteLine("  - 接口文件: ICustomUserManager.YService.g.cs");
        Console.WriteLine("  - 接口文件: IConfigurationService.YService.g.cs");
        Console.WriteLine("  - 注册文件: ServiceRegistration.ZyloServiceTest.YService.g.cs");
        Console.WriteLine("  ✅ 文件命名格式符合 v1.1 规范");

        // 验证接口存在性
        var customInterface = typeof(ICustomUserManager);
        var configInterface = typeof(IConfigurationService);
        var diagnosticInterface = typeof(IDiagnosticTestService);

        Console.WriteLine($"  ICustomUserManager 存在: {customInterface != null}");
        Console.WriteLine($"  IConfigurationService 存在: {configInterface != null}");
        Console.WriteLine($"  IDiagnosticTestService 存在: {diagnosticInterface != null}");
    }

    private static async Task TestBackwardCompatibility(IServiceProvider services)
    {
        Console.WriteLine("\n🔧 测试向后兼容性:");

        // 测试现有服务仍然正常工作
        var userService = services.GetRequiredService<IUserService>();
        Console.WriteLine("✅ 现有 UserService 仍然正常工作");

        var userData = await userService.GetUserAsync(999);
        Console.WriteLine($"  现有服务功能: {userData}");

        var dataProcessor = services.GetRequiredService<IDataProcessor>();
        Console.WriteLine("✅ 现有 DataProcessor 仍然正常工作");

        var processResult = await dataProcessor.ProcessUserDataAsync(888, "兼容性测试数据");
        Console.WriteLine($"  现有服务功能: {processResult}");

        var complexService = services.GetRequiredService<IComplexTypeTestService>();
        Console.WriteLine("✅ 现有 ComplexTypeTestService 仍然正常工作");

        var complexResult = complexService.ProcessGeneric("兼容性测试");
        Console.WriteLine($"  现有服务功能: {complexResult}");

        Console.WriteLine("✅ 所有现有功能保持 100% 兼容");
    }

    /// <summary>
    /// 测试诊断功能（通过检查生成的代码）
    /// </summary>
    public static void TestDiagnosticFeatures()
    {
        Console.WriteLine("\n🔧 测试诊断功能:");

        // 测试诊断测试服务
        var diagnosticInterface = typeof(IDiagnosticTestService);
        var methods = diagnosticInterface.GetMethods();

        Console.WriteLine($"  IDiagnosticTestService 方法数量: {methods.Length}");

        // 检查特定方法
        var processDataMethod = methods.FirstOrDefault(m => m.Name == "ProcessData");
        var manyParamsMethod = methods.FirstOrDefault(m => m.Name == "MethodWithManyParameters");
        var complexGenericMethod = methods.FirstOrDefault(m => m.Name == "ComplexGenericMethod");

        Console.WriteLine($"  ProcessData 方法存在: {processDataMethod != null}");
        Console.WriteLine($"  MethodWithManyParameters 方法存在: {manyParamsMethod != null}");
        Console.WriteLine($"  ComplexGenericMethod 方法存在: {complexGenericMethod != null}");

        if (manyParamsMethod != null)
        {
            var paramCount = manyParamsMethod.GetParameters().Length;
            Console.WriteLine($"  MethodWithManyParameters 参数数量: {paramCount}");
            Console.WriteLine($"  应该触发 YS102 警告（参数过多）");
        }

        if (complexGenericMethod != null)
        {
            var genericArgCount = complexGenericMethod.GetGenericArguments().Length;
            Console.WriteLine($"  ComplexGenericMethod 泛型参数数量: {genericArgCount}");
            Console.WriteLine($"  应该触发 YS101 警告（复杂泛型约束）");
        }

        Console.WriteLine("✅ 诊断功能测试完成");
    }
}
