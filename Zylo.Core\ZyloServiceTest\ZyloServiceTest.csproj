﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>13</LangVersion>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <!-- 启用生成器调试 -->
        <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
        <CompilerGeneratedFilesOutputPath>$(BaseIntermediateOutputPath)Generated</CompilerGeneratedFilesOutputPath>
        <IsPackable>false</IsPackable>

    </PropertyGroup>

    <ItemGroup>
        <!-- 🔥 测试 Zylo.Service 包 -->
        <ProjectReference Include="..\Zylo.Toolkit\Zylo.Toolkit.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="true" />


        <!-- 依赖注入支持 -->
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0"/>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="README.md">
            <Pack>true</Pack>
            <PackagePath>\</PackagePath>
        </None>
        <None Update="README.md">
          <Pack>true</Pack>
          <PackagePath>\</PackagePath>
        </None>
    </ItemGroup>

</Project>